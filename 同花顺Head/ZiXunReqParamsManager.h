//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@interface ZiXunReqParamsManager : NSObject
{
}

+ (id)getZiXunTitle:(unsigned long long)arg1 market:(id)arg2;
+ (unsigned long long)getHXZiXunTypeWithMarket:(id)arg1 stockCode:(id)arg2;
+ (id)sharedInstance;
- (id)constructHuShenOptionZiXunRequestParam;
- (id)constructGeGuXinWenZiXunRequestParam;
- (id)constructBlockZiXunRequestParam;
- (id)constructGuoNeiQiHuoZiXunRequestParam;
- (id)constructGuZhiQiHuoZiXunRequestParam;
- (id)constructMeiGuZiXunRequestParam;
- (id)constructHuShenIndexZiXunRequestParam;
- (id)constructUSIndexZiXunRequestParam;
- (id)constructHKIndexZiXunRequestParam;
- (id)constructHuShenJiJinZiXunRequestParam;
- (id)constructWaiHuiZiXunRequestParam;
- (id)constructNormalZiXunRequestParam;
- (id)constructDefaultZiXunRequestParam;
- (id)getZiXunBaseURLFromZiXunTree:(unsigned long long)arg1 market:(id)arg2;
- (unsigned long long)getNewsRequestTypeByZiXunType:(unsigned long long)arg1 stockCode:(id)arg2 market:(id)arg3;
- (id)getZiXunURL:(id)arg1 market:(id)arg2;
- (id)getZiXunRequestParamsByType:(unsigned long long)arg1;
- (id)getZiXunRequestParamsWithMarket:(id)arg1 stockCode:(id)arg2;

@end

