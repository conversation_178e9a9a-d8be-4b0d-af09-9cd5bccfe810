id __cdecl +[servercfgManager shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100C57E9C;
  block[3] = (__int64)&unk_1012E3900;
  block[4] = (__int64)a1;
  if ( qword_1016D3A08 != -1 )
    dispatch_once(&qword_1016D3A08, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3A00);
}

//----- (0000000100C57E9C) ----------------------------------------------------
void __fastcall sub_100C57E9C(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3A00;
  qword_1016D3A00 = v2;
}

//----- (0000000100C57ED4) ----------------------------------------------------
void sub_100C57ED4()
{
  ;
}

//----- (0000000100C57EDA) ----------------------------------------------------
servercfgManager *__cdecl -[servercfgManager init](servercfgManager *self, SEL a2)
{
  servercfgManager *v2; // rax
  servercfgManager *v3; // rbx
  NSMutableArray *v5; // rax
  NSMutableArray *arrBrokerList; // rdi
  AddQsModel *v8; // rax
  AddQsModel *addQsmodel; // rdi
  JYHTTPRequestManage *v11; // rax
  JYHTTPRequestManage *httpRequest; // rdi
  JYHTTPRequestManage *v14; // rax
  JYHTTPRequestManage *aAddQsHttpRequest; // rdi

  v17.receiver = self;
  v17.super_class = (Class)&OBJC_CLASS___servercfgManager;
  v2 = objc_msgSendSuper2(&v17, "init");
  v3 = v2;
  if ( v2 )
  {
    if ( !v2->_arrBrokerList )
    {
      v4 = objc_alloc(&OBJC_CLASS___NSMutableArray);
      v5 = (NSMutableArray *)_objc_msgSend(v4, "init");
      arrBrokerList = v3->_arrBrokerList;
      v3->_arrBrokerList = v5;
    }
    if ( !v3->_addQsmodel )
    {
      v7 = objc_alloc((Class)&OBJC_CLASS___AddQsModel);
      v8 = (AddQsModel *)_objc_msgSend(v7, "init");
      addQsmodel = v3->_addQsmodel;
      v3->_addQsmodel = v8;
    }
    if ( !v3->_httpRequest )
    {
      v10 = objc_alloc((Class)&OBJC_CLASS___JYHTTPRequestManage);
      v11 = (JYHTTPRequestManage *)_objc_msgSend(v10, "initWithDelegate:baseURL:", v3, CFSTR("http://wt.10jqka.com.cn"));
      httpRequest = v3->_httpRequest;
      v3->_httpRequest = v11;
    }
    if ( !v3->_aAddQsHttpRequest )
    {
      v13 = objc_alloc((Class)&OBJC_CLASS___JYHTTPRequestManage);
      v14 = (JYHTTPRequestManage *)_objc_msgSend(
                                     v13,
                                     "initWithDelegate:baseURL:",
                                     v3,
                                     CFSTR("http://xiadan.10jqka.com.cn"));
      aAddQsHttpRequest = v3->_aAddQsHttpRequest;
      v3->_aAddQsHttpRequest = v14;
    }
    -[servercfgManager reInitServerInfo](v3, "reInitServerInfo");
  }
  return v3;
}

//----- (0000000100C58037) ----------------------------------------------------
void __cdecl -[servercfgManager reInitServerInfo](servercfgManager *self, SEL a2)
{
  _QWORD v33[10]; // [rsp+0h] [rbp-110h] BYREF
  servercfgManager *v37; // [rsp+78h] [rbp-98h]
  SEL v38; // [rsp+80h] [rbp-90h]
  SEL v40; // [rsp+90h] [rbp-80h]
  volatile signed __int32 *v44; // [rsp+B8h] [rbp-58h] BYREF

  v37 = self;
  _objc_msgSend(self->_arrBrokerList, "removeAllObjects");
  sub_100D39A70((__int64)v33);
  sub_100D39D50((__int64)v33, 0x1000000, -16777216);
  sub_100F7A470((__int64)v33, "func_name", "get_server_type");
  v2 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getPublicModule");
  sub_100D30280((__int64)v4, (__int64)v33, 0);
  sub_100F7A7F0((volatile signed __int32 **)&__src, (__int64)v33, "server_typename");
  sub_100F7A7F0((volatile signed __int32 **)&v47, (__int64)v33, "server_typeid");
  v42 = 0LL;
  v43 = 0LL;
  sub_100D35390((char *)__src, 44LL, (__int64)&v42, 1);
  v34 = 0LL;
  v35 = 0LL;
  sub_100D35390((char *)v47, 44LL, (__int64)&v34, 1);
  v5 = v42;
  v6 = (*((_QWORD *)&v42 + 1) - (_QWORD)v42) >> 3;
  if ( (int)v6 > 0 )
  {
    v7 = "CString2NSstring:";
    v8 = "isEqualToString:";
    v38 = "initWithName:TypeData:TypeValue:";
    v40 = "addObject:";
    v36 = 8LL * (unsigned int)v6 - 8;
    v9 = 0LL;
    v41 = "CString2NSstring:";
    v39 = "isEqualToString:";
    while ( 1 )
    {
      v10 = sub_100F9F630(v9 + v5);
      v11 = _objc_msgSend(&OBJC_CLASS___tools, v7, v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = v12;
      if ( v12 && !(unsigned __int8)_objc_msgSend(v12, v8, &charsToLeaveEscaped) )
      {
        sub_100D39D50((__int64)v33, 0x1000000, -16777216);
        sub_100F7A470((__int64)v33, "func_name", "get_server_typedata");
        v15 = (char *)sub_100F9F630(v14 + v42);
        sub_100F7A470((__int64)v33, "server_typename", v15);
        v16 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
        v48 = v13;
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = _objc_msgSend(v17, "getPublicModule");
        sub_100D30280((__int64)v18, (__int64)v33, 0);
        v20 = sub_100F9F630(v19 + v42);
        v21 = _objc_msgSend(&OBJC_CLASS___tools, v7, v20);
        v45 = objc_retainAutoreleasedReturnValue(v21);
        sub_100F7A7F0(&v44, (__int64)v33, "server_type_data");
        v22 = sub_100F9F630((__int64)&v44);
        v23 = _objc_msgSend(&OBJC_CLASS___tools, v7, v22);
        v24 = objc_retainAutoreleasedReturnValue(v23);
        v25 = v45;
        if ( v24 )
        {
          v49 = v24;
          v26 = objc_alloc((Class)&OBJC_CLASS___serverListDataModel);
          v28 = sub_100F9F630(v27 + v34);
          v29 = _objc_msgSend(&OBJC_CLASS___tools, v7, v28);
          v30 = objc_retainAutoreleasedReturnValue(v29);
          v31 = _objc_msgSend(v26, v38, v25, v49, v30);
          _objc_msgSend(v37->_arrBrokerList, v40, v31);
          v8 = v39;
          v7 = v41;
          v24 = v49;
        }
        sub_100C56008(&v44);
        v13 = v48;
      }
      if ( v36 == v32 )
        break;
      v5 = v42;
      v9 = v32 + 8;
    }
  }
  sub_100C560F8((__int64 *)&v34);
  sub_100C560F8((__int64 *)&v42);
  sub_100C56008(&v47);
  sub_100C56008(&__src);
  sub_100D39C90(v33);
}

//----- (0000000100C58517) ----------------------------------------------------
void __cdecl -[servercfgManager requestBrokerWithUserid:succeedBlock:failureBlock:](
        servercfgManager *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{

  objc_retain(a3);
  v7 = objc_retain(a4);
  v9 = objc_retain(a5);
  if ( v8
    && _objc_msgSend(v8, "length")
    && !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("NUL")) )
  {
    -[servercfgManager setASucceedBlock:](self, "setASucceedBlock:", v7);
    -[servercfgManager setAFailureBlock:](self, "setAFailureBlock:", v9);
    v12 = objc_alloc(&OBJC_CLASS___NSDictionary);
    v14 = _objc_msgSend(v12, "initWithObjectsAndKeys:", v13, CFSTR("UserID"), 0LL);
    -[servercfgManager cdCLhUJgIFaNrFPY:sendDic:](self, "cdCLhUJgIFaNrFPY:sendDic:", v15, v14);
  }
}

//----- (0000000100C5867D) ----------------------------------------------------
char __cdecl -[servercfgManager IsMeiguBroke:](servercfgManager *self, SEL a2, id a3)
{
  bool v6; // bl

  v3 = _objc_msgSend(a3, "stryybFunc");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = v4;
  v6 = v4
    && _objc_msgSend(v4, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL"))
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v9, "isKindOfClass:", v8))
    && (unsigned __int64)_objc_msgSend(v5, "length") >= 4
    && (unsigned __int16)_objc_msgSend(v5, "characterAtIndex:", 3LL) == 49;
  return v6;
}

//----- (0000000100C5877D) ----------------------------------------------------
id __cdecl -[servercfgManager GetMeguiBrokerNameListArray](servercfgManager *self, SEL a2)
{
  SEL v15; // [rsp+50h] [rbp-D0h]
  SEL v16; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  -[servercfgManager reInitServerInfo](self, "reInitServerInfo");
  v2 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v17 = _objc_msgSend(v2, "init");
  v10 = 0LL;
  v11 = 0LL;
  v12 = 0LL;
  v13 = 0LL;
  v14 = self;
  obj = objc_retain(self->_arrBrokerList);
  v3 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v10, v19, 16LL);
  if ( v3 )
  {
    v4 = *(_QWORD *)v11;
    do
    {
      v15 = "strBrokerName";
      v16 = "addObject:";
      if ( !v3 )
        v3 = 1LL;
      for ( i = 0LL; i != v3; ++i )
      {
        if ( *(_QWORD *)v11 != v4 )
          objc_enumerationMutation(obj);
        v6 = *(void **)(*((_QWORD *)&v10 + 1) + 8 * i);
        if ( v6 && (unsigned __int8)_objc_msgSend(v14, "IsMeiguBroke:", *(_QWORD *)(*((_QWORD *)&v10 + 1) + 8 * i)) )
        {
          v7 = _objc_msgSend(v6, v15);
          v8 = objc_retainAutoreleasedReturnValue(v7);
          _objc_msgSend(v17, v16, v8);
        }
      }
      v3 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v10, v19, 16LL);
    }
    while ( v3 );
  }
  return objc_autoreleaseReturnValue(v17);
}

//----- (0000000100C589A0) ----------------------------------------------------
id __cdecl -[servercfgManager getBrokerNameListArray](servercfgManager *self, SEL a2)
{
  NSMutableArray *v2; // r13
  NSMutableArray *v10; // rbx
  SEL v20; // [rsp+48h] [rbp-108h]
  SEL v21; // [rsp+50h] [rbp-100h]
  SEL v22; // [rsp+58h] [rbp-F8h]
  SEL v23; // [rsp+60h] [rbp-F0h]
  SEL v24; // [rsp+68h] [rbp-E8h]
  SEL v25; // [rsp+70h] [rbp-E0h]
  SEL v26; // [rsp+78h] [rbp-D8h]

  -[servercfgManager reInitServerInfo](self, "reInitServerInfo");
  v28 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "new");
  v15 = 0LL;
  v16 = 0LL;
  v17 = 0LL;
  v18 = 0LL;
  v29 = self;
  v2 = objc_retain(self->_arrBrokerList);
  v3 = (__int64)_objc_msgSend(v2, "countByEnumeratingWithState:objects:count:", &v15, v31, 16LL);
  v4 = v29;
  if ( v3 )
  {
    v19 = *(_QWORD *)v16;
    do
    {
      v20 = "needShieldTrade";
      v23 = "IsMeiguBroke:";
      v25 = "strBrokerName";
      v26 = "addObject:";
      v21 = "strQsid";
      v24 = "isEqualToString:";
      v22 = "strConnectMode";
      if ( !v3 )
        v3 = 1LL;
      for ( i = 0LL; i != v3; ++i )
      {
        if ( *(_QWORD *)v16 != v19 )
          objc_enumerationMutation(v2);
        v6 = *(void **)(*((_QWORD *)&v15 + 1) + 8 * i);
        if ( v6 )
        {
          if ( (unsigned __int8)_objc_msgSend(v4, v20) )
          {
            if ( (unsigned __int8)_objc_msgSend(v4, v23, v6) )
              continue;
LABEL_11:
            v7 = _objc_msgSend(v6, v25);
            v8 = objc_retainAutoreleasedReturnValue(v7);
            _objc_msgSend(v28, v26, v8);
LABEL_15:
            v4 = v29;
            continue;
          }
          v9 = _objc_msgSend(v6, v21);
          v10 = v2;
          v11 = objc_retainAutoreleasedReturnValue(v9);
          if ( (unsigned __int8)_objc_msgSend(v11, v24, CFSTR("356")) || (unsigned __int8)_objc_msgSend(v29, v23, v6) )
          {
            v2 = v10;
            goto LABEL_15;
          }
          v12 = _objc_msgSend(v6, v22);
          v27 = objc_retainAutoreleasedReturnValue(v12);
          v30 = (unsigned __int8)_objc_msgSend(v27, v24, CFSTR("4849666"));
          v2 = v10;
          v4 = v29;
          if ( !v30 )
            goto LABEL_11;
        }
      }
      v13 = _objc_msgSend(v2, "countByEnumeratingWithState:objects:count:", &v15, v31, 16LL);
      v3 = (__int64)v13;
    }
    while ( v13 );
  }
  return objc_autoreleaseReturnValue(v28);
}

//----- (0000000100C58D46) ----------------------------------------------------
char __cdecl -[servercfgManager needShieldTrade](servercfgManager *self, SEL a2)
{
  char result; // al

  v2 = +[JYLuChangHuiManager shareInstance](&OBJC_CLASS___JYLuChangHuiManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)_objc_msgSend(v3, "congNaLiLai");
  v5 = +[JYLuChangHuiManager shareInstance](&OBJC_CLASS___JYLuChangHuiManager, "shareInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "JYBuNengKan");
  v7 = +[JYLuChangHuiManager shareInstance](&OBJC_CLASS___JYLuChangHuiManager, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (unsigned __int8)_objc_msgSend(v8, "isJYList");
  if ( !v4 )
    return 0;
  result = 1;
  if ( !v10 )
    return v9 != 0;
  return result;
}

//----- (0000000100C58E35) ----------------------------------------------------
id __cdecl -[servercfgManager getServerListByBroker:](servercfgManager *self, SEL a2, id a3)
{
  _QWORD v9[3]; // [rsp+0h] [rbp-40h] BYREF

  v3 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", a3);
  sub_100C57C86((__int64)v10);
  v4 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "getPublicModule");
  v9[1] = sub_100C57D0C;
  v9[2] = 0LL;
  v9[0] = v10;
  sub_100D37CE0((__int64)v6, v3, (__int64)v9);
  v7 = objc_retain(v10[0]);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C58F1E) ----------------------------------------------------
id __cdecl -[servercfgManager dumpServersForBroker:](servercfgManager *self, SEL a2, id a3)
{
  _QWORD v9[3]; // [rsp+0h] [rbp-40h] BYREF

  v3 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", a3);
  sub_100C57C86((__int64)v10);
  v4 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "getPublicModule");
  v9[1] = sub_100C57D7A;
  v9[2] = 0LL;
  v9[0] = v10;
  sub_100D37CE0((__int64)v6, v3, (__int64)v9);
  v7 = v10[1];
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C58FF7) ----------------------------------------------------
id __cdecl -[servercfgManager getBrokerIdByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v5; // rbx
  bool v17; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
      {
        v8 = *(void **)(v6 + 32);
        if ( v8 )
        {
          if ( _objc_msgSend(v8, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(*(id *)(v9 + 32));
            v10 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v4;
              while ( 2 )
              {
                v24 = "strBrokerName";
                if ( !v10 )
                  v10 = 1LL;
                for ( i = 0LL; i != v10; ++i )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  v12 = *(void **)(*((_QWORD *)&v19 + 1) + 8 * i);
                  if ( v12 )
                  {
                    v13 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8 * i), v24);
                    v14 = objc_retainAutoreleasedReturnValue(v13);
                    v15 = (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", v4);
                    v17 = v15 == 0;
                    v4 = v25;
                    if ( !v17 )
                    {
                      v18 = _objc_msgSend(v12, "strConnectMode");
                      v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v18);
                      goto LABEL_5;
                    }
                  }
                }
                v10 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v5 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C592AF) ----------------------------------------------------
id __cdecl -[servercfgManager getYybidByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  NSMutableArray *v9; // rdi
  id (**v12)(id, SEL, ...); // r13
  bool v19; // zf
  id (**v20)(id, SEL, ...); // r12
  SEL v28; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v9 = self->_arrBrokerList;
            if ( v9 )
            {
              if ( _objc_msgSend(v9, "count") )
              {
                v25 = 0LL;
                v24 = 0LL;
                v23 = 0LL;
                v22 = 0LL;
                obj = objc_retain(self->_arrBrokerList);
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v31, 16LL);
                if ( v10 )
                {
                  v27 = *(_QWORD *)v23;
                  v12 = &_objc_msgSend;
                  v29 = v11;
                  while ( 2 )
                  {
                    v28 = "strBrokerName";
                    for ( i = 0LL; i != v10; i = (char *)i + 1 )
                    {
                      if ( *(_QWORD *)v23 != v27 )
                        objc_enumerationMutation(obj);
                      v14 = *(id *)(*((_QWORD *)&v22 + 1) + 8LL * (_QWORD)i);
                      if ( v14 )
                      {
                        v26 = *(id *)(*((_QWORD *)&v22 + 1) + 8LL * (_QWORD)i);
                        v15 = ((id (*)(id, SEL, ...))v12)(v14, v28);
                        v16 = objc_retainAutoreleasedReturnValue(v15);
                        v18 = (unsigned __int8)((id (*)(id, SEL, ...))v12)(v16, "isEqualToString:", v17);
                        v19 = v18 == 0;
                        v12 = v20;
                        if ( !v19 )
                        {
                          v21 = _objc_msgSend(v26, "strYybid");
                          v6 = (__CFString *)objc_retainAutoreleasedReturnValue(v21);
                          goto LABEL_5;
                        }
                      }
                    }
                    v10 = ((id (*)(id, SEL, ...))v12)(
                            obj,
                            "countByEnumeratingWithState:objects:count:",
                            &v22,
                            v31,
                            16LL);
                    if ( v10 )
                      continue;
                    break;
                  }
                }
                v6 = &charsToLeaveEscaped;
              }
            }
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5958F) ----------------------------------------------------
id __cdecl -[servercfgManager getQsidByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  NSMutableArray *v9; // rdi
  id (**v12)(id, SEL, ...); // r13
  bool v19; // zf
  id (**v20)(id, SEL, ...); // r12
  SEL v28; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v9 = self->_arrBrokerList;
            if ( v9 )
            {
              if ( _objc_msgSend(v9, "count") )
              {
                v25 = 0LL;
                v24 = 0LL;
                v23 = 0LL;
                v22 = 0LL;
                obj = objc_retain(self->_arrBrokerList);
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v31, 16LL);
                if ( v10 )
                {
                  v27 = *(_QWORD *)v23;
                  v12 = &_objc_msgSend;
                  v29 = v11;
                  while ( 2 )
                  {
                    v28 = "strBrokerName";
                    for ( i = 0LL; i != v10; i = (char *)i + 1 )
                    {
                      if ( *(_QWORD *)v23 != v27 )
                        objc_enumerationMutation(obj);
                      v14 = *(id *)(*((_QWORD *)&v22 + 1) + 8LL * (_QWORD)i);
                      if ( v14 )
                      {
                        v26 = *(id *)(*((_QWORD *)&v22 + 1) + 8LL * (_QWORD)i);
                        v15 = ((id (*)(id, SEL, ...))v12)(v14, v28);
                        v16 = objc_retainAutoreleasedReturnValue(v15);
                        v18 = (unsigned __int8)((id (*)(id, SEL, ...))v12)(v16, "isEqualToString:", v17);
                        v19 = v18 == 0;
                        v12 = v20;
                        if ( !v19 )
                        {
                          v21 = _objc_msgSend(v26, "strQsid");
                          v6 = (__CFString *)objc_retainAutoreleasedReturnValue(v21);
                          goto LABEL_5;
                        }
                      }
                    }
                    v10 = ((id (*)(id, SEL, ...))v12)(
                            obj,
                            "countByEnumeratingWithState:objects:count:",
                            &v22,
                            v31,
                            16LL);
                    if ( v10 )
                      continue;
                    break;
                  }
                }
                v6 = &charsToLeaveEscaped;
              }
            }
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5986F) ----------------------------------------------------
id __cdecl -[servercfgManager getYybridByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v5; // rbx
  bool v17; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
      {
        v8 = *(void **)(v6 + 32);
        if ( v8 )
        {
          if ( _objc_msgSend(v8, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(*(id *)(v9 + 32));
            v10 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v4;
              while ( 2 )
              {
                v24 = "strBrokerName";
                if ( !v10 )
                  v10 = 1LL;
                for ( i = 0LL; i != v10; ++i )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  v12 = *(void **)(*((_QWORD *)&v19 + 1) + 8 * i);
                  if ( v12 )
                  {
                    v13 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8 * i), v24);
                    v14 = objc_retainAutoreleasedReturnValue(v13);
                    v15 = (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", v4);
                    v17 = v15 == 0;
                    v4 = v25;
                    if ( !v17 )
                    {
                      v18 = _objc_msgSend(v12, "strYybrid");
                      v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v18);
                      goto LABEL_5;
                    }
                  }
                }
                v10 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v5 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C59B29) ----------------------------------------------------
id __cdecl -[servercfgManager getQsidAndYybridByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  NSString *v22; // rax
  SEL v29; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v27 = 0LL;
            v26 = 0LL;
            v25 = 0LL;
            v24 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v32, 16LL);
            if ( v10 )
            {
              v28 = *(_QWORD *)v25;
              v30 = v5;
              while ( 2 )
              {
                v29 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v25 != v28 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v24 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v24 + 1) + 8LL * (_QWORD)i), v29);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v30;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strQsid");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v20 = _objc_msgSend(v19, "strYybrid");
                      v21 = objc_retainAutoreleasedReturnValue(v20);
                      v22 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@-%@"), v18, v21);
                      v6 = objc_retainAutoreleasedReturnValue(v22);
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v24, v32, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C59E51) ----------------------------------------------------
id __cdecl -[servercfgManager getAuthTypeByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v5;
              while ( 2 )
              {
                v24 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i), v24);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v25;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strAuthType");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v6 = (__CFString *)_objc_msgSend(v18, "copy");
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5A12C) ----------------------------------------------------
id __cdecl -[servercfgManager getAccountTypeByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v5;
              while ( 2 )
              {
                v24 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i), v24);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v25;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strAccountType");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v6 = (__CFString *)_objc_msgSend(v18, "copy");
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5A407) ----------------------------------------------------
id __cdecl -[servercfgManager getMacVersionByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v5;
              while ( 2 )
              {
                v24 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i), v24);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v25;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strMacVersion");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v6 = (__CFString *)_objc_msgSend(v18, "copy");
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5A6E2) ----------------------------------------------------
id __cdecl -[servercfgManager getServerDataModelByName:](servercfgManager *self, SEL a2, id a3)
{
  NSMutableArray *arrBrokerList; // rdi
  SEL v15; // r12
  SEL v23; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4
    && _objc_msgSend(v4, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, v6, CFSTR("NUL"))
    && (arrBrokerList = self->_arrBrokerList) != 0LL
    && _objc_msgSend(arrBrokerList, "count") )
  {
    v24 = v5;
    v20 = 0LL;
    v19 = 0LL;
    v18 = 0LL;
    v17 = 0LL;
    obj = objc_retain(self->_arrBrokerList);
    v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v26, 16LL);
    if ( v10 )
    {
      v22 = *(_QWORD *)v18;
      while ( 2 )
      {
        v23 = "strBrokerName";
        for ( i = 0LL; i != v10; i = (char *)i + 1 )
        {
          if ( *(_QWORD *)v18 != v22 )
            objc_enumerationMutation(obj);
          v12 = *(void **)(*((_QWORD *)&v17 + 1) + 8LL * (_QWORD)i);
          if ( v12 )
          {
            v21 = *(id *)(*((_QWORD *)&v17 + 1) + 8LL * (_QWORD)i);
            v13 = _objc_msgSend(v12, v23);
            v14 = objc_retainAutoreleasedReturnValue(v13);
            _objc_msgSend(v14, v15, v24);
            if ( v16 )
            {
              v7 = objc_retain(v21);
              goto LABEL_22;
            }
          }
        }
        v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v26, 16LL);
        if ( v10 )
          continue;
        break;
      }
    }
    v7 = 0LL;
LABEL_22:
    v5 = v24;
  }
  else
  {
    v7 = 0LL;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C5A985) ----------------------------------------------------
id __cdecl -[servercfgManager getMacKernelByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v5;
              while ( 2 )
              {
                v24 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i), v24);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v25;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strMacKernel");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v6 = (__CFString *)_objc_msgSend(v18, "copy");
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5AC60) ----------------------------------------------------
id __cdecl -[servercfgManager getMacLoginVersionByName:](servercfgManager *self, SEL a2, id a3)
{
  __CFString *v6; // rbx
  NSMutableArray *arrBrokerList; // rdi
  bool v16; // zf
  SEL v24; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = &charsToLeaveEscaped;
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
      {
        arrBrokerList = self->_arrBrokerList;
        if ( arrBrokerList )
        {
          if ( _objc_msgSend(arrBrokerList, "count") )
          {
            v22 = 0LL;
            v21 = 0LL;
            v20 = 0LL;
            v19 = 0LL;
            obj = objc_retain(self->_arrBrokerList);
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v9, v27, 16LL);
            if ( v10 )
            {
              v23 = *(_QWORD *)v20;
              v25 = v5;
              while ( 2 )
              {
                v24 = "strBrokerName";
                for ( i = 0LL; i != v10; i = (char *)i + 1 )
                {
                  if ( *(_QWORD *)v20 != v23 )
                    objc_enumerationMutation(obj);
                  if ( *(_QWORD *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i) )
                  {
                    v12 = _objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8LL * (_QWORD)i), v24);
                    v13 = objc_retainAutoreleasedReturnValue(v12);
                    v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", v5);
                    v16 = v14 == 0;
                    v5 = v25;
                    if ( !v16 )
                    {
                      v17 = _objc_msgSend(v15, "strMacLoginVersion");
                      v18 = objc_retainAutoreleasedReturnValue(v17);
                      v6 = (__CFString *)_objc_msgSend(v18, "copy");
                      goto LABEL_5;
                    }
                  }
                }
                v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v27, 16LL);
                if ( v10 )
                  continue;
                break;
              }
            }
            v6 = &charsToLeaveEscaped;
          }
        }
      }
    }
  }
LABEL_5:
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100C5AF3B) ----------------------------------------------------
void __cdecl -[servercfgManager RemoveBrokerName:](servercfgManager *self, SEL a2, id a3)
{
  NSNumber *v11; // rax
  NSNumber *v12; // r15

  v17 = objc_retain(a3);
  _objc_msgSend(v3, "setAddBrokerName:andValue:", v17, 0LL);
  v4 = +[servercfgManager shareInstance](&OBJC_CLASS___servercfgManager, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "getYybridByName:", v17);
  v18 = objc_retainAutoreleasedReturnValue(v6);
  v7 = +[TradeAndQuotaMutual shareInstance](&OBJC_CLASS___TradeAndQuotaMutual, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "aQuotaUserID");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = objc_alloc(&OBJC_CLASS___NSDictionary);
  v14 = _objc_msgSend(
          v13,
          "initWithObjectsAndKeys:",
          v18,
          CFSTR("yybrid"),
          v10,
          CFSTR("Userid"),
          v12,
          CFSTR("requestCount"),
          0LL);
  _objc_msgSend(v15, "SendRequestDeleteBroker:yybrid:sendDic:", v10, v18, v14);
}

//----- (0000000100C5B121) ----------------------------------------------------
void __cdecl -[servercfgManager setAddBrokerName:andValue:](servercfgManager *self, SEL a2, id a3, id a4)
{
  _QWORD v13[14]; // [rsp+0h] [rbp-70h] BYREF

  v5 = objc_retain(a3);
  objc_retain(a4);
  sub_100D39A70((__int64)v13);
  sub_100D39D50((__int64)v13, 0x1000000, -16777216);
  sub_100F7A470((__int64)v13, "func_name", "update_servertype");
  if ( v5 )
  {
    v7 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v5);
    sub_100F7A470((__int64)v13, "server_typename", v7);
  }
  if ( v6 )
  {
    v8 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v6);
    sub_100F7A470((__int64)v13, "server_typevalue", v8);
  }
  v9 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "getPublicModule");
  sub_100D30280((__int64)v11, (__int64)v13, 0);
  sub_100D39C90(v13);
}

//----- (0000000100C5B281) ----------------------------------------------------
void __cdecl -[servercfgManager setAddServerStationListName:andValue:andBrokerName:](
        servercfgManager *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  _QWORD v17[16]; // [rsp+0h] [rbp-80h] BYREF

  v6 = objc_retain(a3);
  v7 = objc_retain(a4);
  v9 = objc_retain(v8);
  sub_100D39A70((__int64)v17);
  sub_100D39D50((__int64)v17, 0x1000000, -16777216);
  sub_100F7A470((__int64)v17, "func_name", "update_server");
  if ( v9 )
  {
    v10 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v9);
    sub_100F7A470((__int64)v17, "server_typename", v10);
  }
  if ( v6 )
  {
    v11 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v6);
    sub_100F7A470((__int64)v17, "server_name", v11);
  }
  if ( v7 )
  {
    v12 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v7);
    sub_100F7A470((__int64)v17, "server_string", v12);
  }
  v13 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(v14, "getPublicModule");
  sub_100D30280((__int64)v15, (__int64)v17, 0);
  sub_100D39C90(v17);
}

//----- (0000000100C5B42B) ----------------------------------------------------
id __cdecl -[servercfgManager getNewConnectMode](servercfgManager *self, SEL a2)
{
  int v8; // r12d
  NSString *v9; // rax
  NSString *v10; // rax
  servercfgManager *v21; // [rsp+70h] [rbp-C0h]
  id obj; // [rsp+78h] [rbp-B8h]

  -[servercfgManager reInitServerInfo](self, "reInitServerInfo");
  v15 = 0LL;
  v14 = 0LL;
  v13 = 0LL;
  v12 = 0LL;
  v21 = self;
  obj = objc_retain(self->_arrBrokerList);
  v2 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v12, v23, 16LL);
  if ( v2 )
  {
    v16 = *(_QWORD *)v13;
    do
    {
      v17 = "strConnectMode";
      v18 = "integerValue";
      v19 = "getIndexByConnectMode:";
      v3 = 0LL;
      v20 = v2;
      do
      {
        if ( *(_QWORD *)v13 != v16 )
          objc_enumerationMutation(obj);
        v4 = *(void **)(*((_QWORD *)&v12 + 1) + 8LL * (_QWORD)v3);
        if ( v4 )
        {
          v5 = _objc_msgSend(v4, v17);
          v6 = objc_retainAutoreleasedReturnValue(v5);
          v7 = _objc_msgSend(v6, v18);
          _objc_msgSend(v21, v19, v7);
          v2 = v20;
        }
        v3 = (char *)v3 + 1;
      }
      while ( v2 != v3 );
      v2 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v12, v23, 16LL);
    }
    while ( v2 );
  }
  v9 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("%ld"),
         (unsigned int)((v8 << 16) + 0x10000) | 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100C5B673) ----------------------------------------------------
void __cdecl -[servercfgManager getMoniServerInfoWithSucceedBlock:](servercfgManager *self, SEL a2, id a3)
{
  servercfgManager *v3; // r14
  NSMutableArray *arrBrokerList; // rdi
  bool v12; // zf
  _QWORD v30[4]; // [rsp+48h] [rbp-138h] BYREF
  id to; // [rsp+70h] [rbp-110h] BYREF
  SEL v34; // [rsp+80h] [rbp-100h]
  SEL v35; // [rsp+88h] [rbp-F8h]
  SEL v36; // [rsp+90h] [rbp-F0h]
  SEL v37; // [rsp+98h] [rbp-E8h]
  SEL v38; // [rsp+A0h] [rbp-E0h]
  servercfgManager *v39; // [rsp+A8h] [rbp-D8h]
  id location; // [rsp+B0h] [rbp-D0h] BYREF
  id obj; // [rsp+C8h] [rbp-B8h]

  v3 = self;
  v42 = objc_retain(a3);
  arrBrokerList = self->_arrBrokerList;
  if ( !arrBrokerList || !_objc_msgSend(arrBrokerList, "count") )
    goto LABEL_16;
  v29 = 0LL;
  v28 = 0LL;
  v27 = 0LL;
  v26 = 0LL;
  obj = objc_retain(v3->_arrBrokerList);
  v5 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v26, v44, 16LL);
  if ( !v5 )
  {
LABEL_16:
    v21 = +[TradeAndQuotaMutual shareInstance](&OBJC_CLASS___TradeAndQuotaMutual, "shareInstance");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v23 = _objc_msgSend(v22, "aQuotaUserID");
    objc_retainAutoreleasedReturnValue(v23);
    if ( v24
      && _objc_msgSend(v24, "length")
      && !(unsigned __int8)_objc_msgSend(v24, "isEqualToString:", CFSTR("NULL"))
      && !(unsigned __int8)_objc_msgSend(v24, "isEqualToString:", CFSTR("NUL")) )
    {
      objc_initWeak(&location, v3);
      v30[0] = _NSConcreteStackBlock;
      v30[1] = 3254779904LL;
      v30[2] = sub_100C5BB17;
      v30[3] = &unk_1012EA488;
      objc_copyWeak(&to, &location);
      v31 = objc_retain(v42);
      -[servercfgManager requestBrokerWithUserid:succeedBlock:failureBlock:](
        v3,
        "requestBrokerWithUserid:succeedBlock:failureBlock:",
        v25,
        v30,
        0LL);
      objc_destroyWeak(&to);
      objc_destroyWeak(&location);
    }
    goto LABEL_22;
  }
  v39 = v3;
  v33 = *(_QWORD *)v27;
  v6 = 1;
  do
  {
    v34 = "strQsid";
    v35 = "isEqualToString:";
    v36 = "strBrokerName";
    v37 = "strConnectMode";
    v38 = "strYybid";
    v7 = 0LL;
    v41 = v5;
    do
    {
      if ( *(_QWORD *)v27 != v33 )
        objc_enumerationMutation(obj);
      if ( *(_QWORD *)(*((_QWORD *)&v26 + 1) + 8LL * (_QWORD)v7) )
      {
        v8 = _objc_msgSend(*(id *)(*((_QWORD *)&v26 + 1) + 8LL * (_QWORD)v7), v34);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        v10 = (unsigned __int8)_objc_msgSend(v9, v35, CFSTR("356"));
        v12 = v10 == 0;
        v5 = v41;
        if ( !v12 )
        {
          v13 = _objc_msgSend(v11, v36);
          v14 = objc_retainAutoreleasedReturnValue(v13);
          v16 = _objc_msgSend(v15, v37);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v19 = _objc_msgSend(v18, v38);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          (*((void (__fastcall **)(id, id, id, id))v42 + 2))(v42, v14, v17, v20);
          v6 = 0;
          v5 = v41;
        }
      }
      v7 = (char *)v7 + 1;
    }
    while ( v5 != v7 );
    v5 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v26, v44, 16LL);
  }
  while ( v5 );
  v3 = v39;
  if ( v6 )
    goto LABEL_16;
LABEL_22:
}

//----- (0000000100C5BB17) ----------------------------------------------------
__int64 __fastcall sub_100C5BB17(__int64 a1)
{
  id WeakRetained; // rbx
  id (**v6)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  SEL v32; // [rsp+60h] [rbp-F0h]
  SEL v33; // [rsp+68h] [rbp-E8h]
  SEL v34; // [rsp+70h] [rbp-E0h]
  SEL v35; // [rsp+78h] [rbp-D8h]
  SEL v36; // [rsp+80h] [rbp-D0h]
  id obj; // [rsp+98h] [rbp-B8h]

  v37 = a1;
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "reInitServerInfo");
  v28 = 0LL;
  v27 = 0LL;
  v26 = 0LL;
  v25 = 0LL;
  v2 = objc_loadWeakRetained((id *)(a1 + 40));
  v3 = _objc_msgSend(v2, "arrBrokerList");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  obj = v4;
  v5 = _objc_msgSend(v4, "countByEnumeratingWithState:objects:count:", &v25, v40, 16LL);
  if ( v5 )
  {
    v30 = *(_QWORD *)v26;
    v6 = &_objc_msgSend;
    do
    {
      v32 = "strQsid";
      v33 = "isEqualToString:";
      v34 = "strBrokerName";
      v35 = "strConnectMode";
      v36 = "strYybid";
      v7 = 0LL;
      v29 = v5;
      do
      {
        if ( *(_QWORD *)v26 != v30 )
          objc_enumerationMutation(obj);
        v8 = *(void **)(*((_QWORD *)&v25 + 1) + 8LL * (_QWORD)v7);
        if ( v8 )
        {
          v9 = ((id (*)(id, SEL, ...))v6)(*(id *)(*((_QWORD *)&v25 + 1) + 8LL * (_QWORD)v7), v32);
          v10 = objc_retainAutoreleasedReturnValue(v9);
          v12 = (unsigned __int8)v11(v10, v33, CFSTR("356"));
          v6 = &_objc_msgSend;
          if ( v12 )
          {
            v13 = *(_QWORD *)(v37 + 32);
            v14 = _objc_msgSend(v8, v34);
            v15 = objc_retainAutoreleasedReturnValue(v14);
            v17 = v16(v8, v35);
            v31 = v13;
            v38 = v15;
            v18 = objc_retainAutoreleasedReturnValue(v17);
            v20 = v19(v8, v36);
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v22 = v38;
            (*(void (__fastcall **)(__int64, id, id, id))(v31 + 16))(v31, v38, v18, v21);
            v6 = &_objc_msgSend;
          }
        }
        v7 = (char *)v7 + 1;
      }
      while ( v29 != v7 );
      v5 = ((id (*)(id, SEL, ...))v6)(obj, "countByEnumeratingWithState:objects:count:", &v25, v40, 16LL);
    }
    while ( v5 );
  }
  return __stack_chk_guard;
}

//----- (0000000100C5BE50) ----------------------------------------------------
void __fastcall sub_100C5BE50(__int64 a1, __int64 a2)
{
  _Block_object_assign((void *)(a1 + 32), *(const void **)(a2 + 32), 7);
  objc_copyWeak((id *)(a1 + 40), (id *)(a2 + 40));
}

//----- (0000000100C5BE88) ----------------------------------------------------
signed __int64 __cdecl -[servercfgManager getIndexByConnectMode:](servercfgManager *self, SEL a2, signed __int64 a3)
{
  return WORD1(a3);
}

//----- (0000000100C5BE94) ----------------------------------------------------
void __cdecl -[servercfgManager deleteBrokerDeleteWith:](servercfgManager *self, SEL a2, id a3)
{
  id (*v25)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12
  __CFString *v28; // r15
  id (*v33)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v42)(id, SEL, ...); // rbx
  unsigned __int8 (__fastcall *v50)(id, SEL, id); // r12
  servercfgManager *v52; // r14
  SEL v63; // [rsp+A8h] [rbp-228h]
  SEL v64; // [rsp+B0h] [rbp-220h]
  SEL v65; // [rsp+B8h] [rbp-218h]
  SEL v66; // [rsp+C0h] [rbp-210h]
  SEL v71; // [rsp+E8h] [rbp-1E8h]
  SEL v75; // [rsp+108h] [rbp-1C8h]
  SEL v76; // [rsp+110h] [rbp-1C0h]
  SEL v77; // [rsp+118h] [rbp-1B8h]
  SEL v78; // [rsp+120h] [rbp-1B0h]
  SEL v79; // [rsp+128h] [rbp-1A8h]
  SEL v83; // [rsp+148h] [rbp-188h]
  SEL v85; // [rsp+158h] [rbp-178h]
  servercfgManager *v87; // [rsp+168h] [rbp-168h]
  id obj; // [rsp+170h] [rbp-160h]

  v86 = objc_retain(a3);
  -[servercfgManager reInitServerInfo](self, "reInitServerInfo");
  v56 = 0LL;
  v55 = 0LL;
  v54 = 0LL;
  v53 = 0LL;
  obj = objc_retain(self->_arrBrokerList);
  v3 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v53, v95, 16LL);
  if ( v3 )
  {
    v73 = *(_QWORD *)v54;
    v87 = self;
    do
    {
      v71 = "strConnectMode";
      v85 = "isEqualToString:";
      v75 = "strQsid";
      v76 = "strYybrid";
      v77 = "strVersion";
      v78 = "strMacVersion";
      v83 = "stringWithFormat:";
      v79 = "strBrokerName";
      v81 = "brokerName";
      v82 = "count";
      v80 = "setAddBrokerName:andValue:";
      v4 = 0LL;
      v72 = v3;
      do
      {
        if ( *(_QWORD *)v54 != v73 )
          objc_enumerationMutation(obj);
        v70 = v4;
        v5 = _objc_msgSend(*(id *)(*((_QWORD *)&v53 + 1) + 8 * v4), v71);
        v6 = objc_retainAutoreleasedReturnValue(v5);
        v7 = (unsigned __int8)_objc_msgSend(v6, v85, CFSTR("4849666"));
        if ( !v7 )
        {
          v9 = _objc_msgSend(v8, v75);
          v93 = objc_retainAutoreleasedReturnValue(v9);
          v11 = _objc_msgSend(v10, v76);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          v14 = _objc_msgSend(v13, v77);
          v91 = v12;
          v15 = objc_retainAutoreleasedReturnValue(v14);
          v17 = _objc_msgSend(v16, v78);
          v18 = objc_retainAutoreleasedReturnValue(v17);
          v19 = v93;
          v20 = v15;
          v21 = v91;
          v22 = _objc_msgSend(&OBJC_CLASS___NSString, v83, CFSTR("%@%@%@%@"), v93, v91, v20, v18);
          v90 = objc_retainAutoreleasedReturnValue(v22);
          v24 = _objc_msgSend(v23, v79);
          v92 = objc_retainAutoreleasedReturnValue(v24);
          v57 = 0LL;
          v58 = 0LL;
          v59 = 0LL;
          v60 = 0LL;
          v89 = objc_retain(v86);
          v26 = v25(v89, "countByEnumeratingWithState:objects:count:", &v57, v94, 16LL);
          v28 = &charsToLeaveEscaped;
          v93 = &charsToLeaveEscaped;
          if ( v26 )
          {
            v67 = *(_QWORD *)v58;
            v91 = &charsToLeaveEscaped;
            v29 = 0LL;
LABEL_9:
            v63 = "qsid";
            v64 = "yybrid";
            v65 = "wt_version";
            v66 = "mac_version";
            v69 = v26;
            v74 = (__int64)v26 + v29;
            v30 = 0LL;
            while ( 1 )
            {
              if ( *(_QWORD *)v58 != v67 )
                objc_enumerationMutation(v89);
              v31 = *(void **)(*((_QWORD *)&v57 + 1) + 8 * v30);
              v32 = v27(v31, v63);
              v93 = objc_retainAutoreleasedReturnValue(v32);
              v34 = v33(v31, v64);
              v84 = objc_retainAutoreleasedReturnValue(v34);
              v36 = v35(v31, v65);
              v37 = objc_retainAutoreleasedReturnValue(v36);
              v68 = v31;
              v39 = v38(v31, v66);
              v62 = v30;
              v40 = objc_retainAutoreleasedReturnValue(v39);
              v42 = v41;
              v43 = v84;
              v44 = v41(&OBJC_CLASS___NSString, v83, CFSTR("%@%@%@%@"), v93, v84, v37, v40);
              v61 = objc_retainAutoreleasedReturnValue(v44);
              v46 = v37;
              v28 = (__CFString *)v61;
              v47 = (__int64)v42(v90, v85, v28);
              v27 = v42;
              if ( v47 )
                break;
              v30 = v62 + 1;
              v91 = v28;
              if ( v69 == (id)(v62 + 1) )
              {
                v26 = v27(v89, "countByEnumeratingWithState:objects:count:", &v57, v94, 16LL);
                v91 = v28;
                v29 = v74;
                if ( v26 )
                  goto LABEL_9;
                v93 = &charsToLeaveEscaped;
                if ( !v29 )
                {
                  v93 = &charsToLeaveEscaped;
                  goto LABEL_22;
                }
                goto LABEL_19;
              }
            }
            v93 = &charsToLeaveEscaped;
            v49 = v42(v68, v81);
            v93 = objc_retainAutoreleasedReturnValue(v49);
            if ( !v50(v92, v85, v93) )
              goto LABEL_21;
            v29 = 999999LL;
LABEL_19:
            v51 = v48(v89, v82);
            v52 = v87;
            if ( v29 != v51 )
              goto LABEL_24;
          }
          else
          {
LABEL_21:
LABEL_22:
            v52 = v87;
          }
          ((void (__fastcall *)(servercfgManager *, const char *, id, _QWORD))v48)(v52, v80, v92, 0LL);
LABEL_24:
        }
        v4 = v70 + 1;
      }
      while ( (id)(v70 + 1) != v72 );
      v3 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v53, v95, 16LL);
    }
    while ( v3 );
  }
}

//----- (0000000100C5C733) ----------------------------------------------------
char __cdecl -[servercfgManager isBrokerAddWithQsid:yybrid:](servercfgManager *self, SEL a2, id a3, id a4)
{
  NSString *v6; // rax
  __CFString *v8; // rax
  NSString *v15; // rax
  __CFString *v16; // r15
  SEL v25; // [rsp+40h] [rbp-100h]
  SEL v26; // [rsp+48h] [rbp-F8h]
  SEL v27; // [rsp+50h] [rbp-F0h]
  id obj; // [rsp+80h] [rbp-C0h]

  v4 = objc_retain(a3);
  v30 = v4;
  v31 = objc_retain(v5);
  -[servercfgManager reInitServerInfo](self, "reInitServerInfo");
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v4, v31);
  v32 = objc_retainAutoreleasedReturnValue(v6);
  v21 = 0LL;
  v22 = 0LL;
  v23 = 0LL;
  v24 = 0LL;
  obj = objc_retain(self->_arrBrokerList);
  v7 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v35, 16LL);
  if ( v7 )
  {
    v29 = *(_QWORD *)v22;
    v8 = &charsToLeaveEscaped;
LABEL_3:
    v25 = "strQsid";
    v26 = "strYybrid";
    v27 = "isEqualToString:";
    v9 = 0LL;
    v28 = v7;
    while ( 1 )
    {
      v34 = v8;
      if ( *(_QWORD *)v22 != v29 )
        objc_enumerationMutation(obj);
      v10 = *(void **)(*((_QWORD *)&v21 + 1) + 8LL * (_QWORD)v9);
      v11 = _objc_msgSend(v10, v25);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = _objc_msgSend(v10, v26);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v12, v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v17 = (unsigned __int8)_objc_msgSend(v32, v27, v16);
      v4 = v30;
      if ( v17 )
        break;
      v9 = (id)(v18 + 1);
      v8 = v16;
      if ( v28 == v9 )
      {
        v7 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v35, 16LL);
        v8 = v16;
        if ( v7 )
          goto LABEL_3;
        break;
      }
    }
  }
  else
  {
    v16 = &charsToLeaveEscaped;
  }
  return v19;
}

//----- (0000000100C5CA99) ----------------------------------------------------
char __cdecl -[servercfgManager isNeedAddMoniBrokerWithBrokerArray:](servercfgManager *self, SEL a2, id a3)
{
  bool v4; // zf
  int v6; // eax
  id (**v7)(id, SEL, ...); // rbx
  id (**v14)(id, SEL, ...); // r13
  id (**v17)(id, SEL, ...); // r14
  int v19; // eax
  SEL v26; // [rsp+40h] [rbp-F0h]
  SEL v29; // [rsp+58h] [rbp-D8h]
  id obj; // [rsp+70h] [rbp-C0h]
  int v33; // [rsp+7Ch] [rbp-B4h]

  v3 = objc_retain(a3);
  v31 = v3;
  if ( v3 && (v4 = _objc_msgSend(v3, "count") == 0LL, v3 = v31, v4) )
  {
    v20 = 1;
  }
  else
  {
    v25 = 0LL;
    v24 = 0LL;
    v23 = 0LL;
    v22 = 0LL;
    obj = objc_retain(v3);
    v5 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v34, 16LL);
    if ( v5 )
    {
      v28 = *(_QWORD *)v23;
      v6 = v28;
      LOBYTE(v6) = 1;
      v33 = v6;
      v7 = &_objc_msgSend;
      do
      {
        v26 = "qsid";
        v8 = "isEqualToString:";
        v29 = "yybrid";
        v27 = v5;
        v9 = 0LL;
        v30 = "isEqualToString:";
        do
        {
          if ( *(_QWORD *)v23 != v28 )
            objc_enumerationMutation(obj);
          v10 = *(id *)(*((_QWORD *)&v22 + 1) + 8LL * (_QWORD)v9);
          v11 = ((id (*)(id, SEL, ...))v7)(v10, v26);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          if ( (unsigned __int8)((id (*)(id, SEL, ...))v7)(v12, v8, CFSTR("356")) )
          {
            v13 = ((id (*)(id, SEL, ...))v7)(v10, v29);
            v14 = v7;
            v15 = objc_retainAutoreleasedReturnValue(v13);
            v16 = v8;
            v17 = v14;
            LOBYTE(v14) = ((__int64 (__fastcall *)(id, const char *, __CFString *))v14)(v15, v16, CFSTR("48"));
            v19 = (unsigned __int8)v33;
            if ( (_BYTE)v14 )
              v19 = 0;
            v33 = v19;
            v7 = v17;
            v8 = v30;
          }
          else
          {
          }
          v9 = (id)(v18 + 1);
        }
        while ( v27 != v9 );
        v5 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v7)(
                   obj,
                   "countByEnumeratingWithState:objects:count:",
                   &v22,
                   v34,
                   16LL);
      }
      while ( v5 );
    }
    else
    {
      v33 = 1;
    }
    v3 = v31;
    v20 = v33;
  }
  return v20;
}

//----- (0000000100C5CD52) ----------------------------------------------------
void __cdecl -[servercfgManager cdCLhUJgIFaNrFPY:sendDic:](servercfgManager *self, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = (unsigned __int8)-[servercfgManager _needRequestUserBrokers](self, "_needRequestUserBrokers");
  if ( v5
    && v7
    && _objc_msgSend(v5, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, v8, CFSTR("NUL")) )
  {
    v9 = objc_alloc(&OBJC_CLASS___NSDictionary);
    v10 = _objc_msgSend(
            v9,
            "initWithObjectsAndKeys:",
            CFSTR("wt_query_yyb_user"),
            CFSTR("reqtype"),
            v5,
            CFSTR("userid"),
            CFSTR("mac"),
            CFSTR("app"),
            0LL);
    -[JYHTTPRequestManage xmlRequestWithMethod:path:parameters:sendDic:](
      self->_httpRequest,
      "xmlRequestWithMethod:path:parameters:sendDic:",
      2LL,
      CFSTR("/wt"),
      v10,
      v6);
  }
}

//----- (0000000100C5CECC) ----------------------------------------------------
void __cdecl -[servercfgManager SendRequestDeleteBroker:yybrid:sendDic:](
        servercfgManager *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{

  objc_retain(a3);
  v7 = objc_retain(a4);
  v8 = objc_retain(a5);
  if ( !(unsigned __int8)_objc_msgSend(v9, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v11 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
    if ( v12 )
    {
      if ( !v11
        && _objc_msgSend(v12, "length")
        && !(unsigned __int8)_objc_msgSend(v13, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v14, "isEqualToString:", CFSTR("NUL")) )
      {
        v15 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v17 = _objc_msgSend(
                v15,
                "initWithObjectsAndKeys:",
                v7,
                CFSTR("yybrid"),
                CFSTR("mac"),
                CFSTR("source"),
                v16,
                CFSTR("userid"),
                0LL);
        -[JYHTTPRequestManage jsonRequestWithMethod:path:parameters:sendDic:](
          self->_aAddQsHttpRequest,
          "jsonRequestWithMethod:path:parameters:sendDic:",
          2LL,
          CFSTR("/ao/broker/del/"),
          v17,
          v8);
      }
    }
  }
}

//----- (0000000100C5D06C) ----------------------------------------------------
void __cdecl -[servercfgManager dAXFjjQwJEkyDPiX:sendDic:](servercfgManager *self, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( v5
    && _objc_msgSend(v5, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, v7, CFSTR("NUL")) )
  {
    v8 = objc_alloc(&OBJC_CLASS___NSDictionary);
    v9 = _objc_msgSend(
           v8,
           "initWithObjectsAndKeys:",
           CFSTR("1"),
           CFSTR("mode"),
           CFSTR("356"),
           CFSTR("qsid"),
           CFSTR("mac"),
           CFSTR("source"),
           CFSTR("110010011"),
           CFSTR("type"),
           v5,
           CFSTR("userid"),
           CFSTR("e068.00.00"),
           CFSTR("version"),
           0LL);
    -[JYHTTPRequestManage jsonRequestWithMethod:path:parameters:sendDic:](
      self->_aAddQsHttpRequest,
      "jsonRequestWithMethod:path:parameters:sendDic:",
      2LL,
      CFSTR("/ao/broker/add/"),
      v9,
      v6);
  }
}

//----- (0000000100C5D213) ----------------------------------------------------
void __cdecl -[servercfgManager xmlOrJsonRequestSuccess:sourceDic:andSendDic:](
        servercfgManager *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  id (__cdecl *v8)(id); // r12
  id (__cdecl *v9)(id); // r12
  __CFString *v35; // rbx
  __CFString *v37; // rax
  SEL v73; // r12
  NSString *v94; // rax
  dispatch_time_t v98; // r15
  dispatch_block_t v101; // r12
  unsigned __int64 v108; // rbx
  NSNumber *v122; // rax
  NSString *v134; // rax
  dispatch_time_t v139; // r12
  _QWORD v144[5]; // [rsp+30h] [rbp-2A0h] BYREF
  id (__fastcall *v149)(__int64); // [rsp+78h] [rbp-258h]
  SEL v162; // [rsp+130h] [rbp-1A0h]
  id obj; // [rsp+138h] [rbp-198h]
  SEL v167; // [rsp+158h] [rbp-178h]
  SEL v170; // [rsp+170h] [rbp-160h]
  SEL v171; // [rsp+178h] [rbp-158h]
  SEL v173; // [rsp+188h] [rbp-148h]
  SEL v174; // [rsp+190h] [rbp-140h]

  v175 = self;
  v7 = objc_retain(a3);
  v164 = v8(a4);
  v169 = v9(a5);
  v166 = v7;
  v10 = _objc_msgSend(v7, "URL");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(v11, "absoluteString");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "rangeOfString:", CFSTR("wt_query_yyb_user"));
  if ( v14 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    if ( v169 && (v172 = &charsToLeaveEscaped, _objc_msgSend(v169, "count")) )
    {
      v172 = &charsToLeaveEscaped;
      v36 = _objc_msgSend(v169, "objectForKey:", CFSTR("UserID"));
      v37 = (__CFString *)objc_retainAutoreleasedReturnValue(v36);
    }
    else
    {
      v37 = &charsToLeaveEscaped;
    }
    v172 = v37;
    _objc_msgSend(*((id *)v175 + 3), "setBrokerItemDataSource:", v164);
    v38 = _objc_msgSend(*((id *)v175 + 3), "brokerArray");
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = (unsigned __int8)_objc_msgSend(v175, "isNeedAddMoniBrokerWithBrokerArray:", v39);
    if ( v172
      && v40
      && _objc_msgSend(v172, "length")
      && !(unsigned __int8)_objc_msgSend(v172, "isEqualToString:", CFSTR("NULL"))
      && !(unsigned __int8)_objc_msgSend(v172, "isEqualToString:", CFSTR("NUL")) )
    {
      v127 = _objc_msgSend(v169, "objectForKey:", CFSTR("RequestCount"));
      v128 = objc_retainAutoreleasedReturnValue(v127);
      v130 = v128;
      if ( !v128
        || !_objc_msgSend(v128, v129)
        || (unsigned __int8)_objc_msgSend(v130, "isEqualToString:", CFSTR("NULL"))
        || (unsigned __int8)_objc_msgSend(v130, "isEqualToString:", CFSTR("NUL")) )
      {
        v131 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v132 = _objc_msgSend(
                 v131,
                 "initWithObjectsAndKeys:",
                 v172,
                 CFSTR("UserID"),
                 CFSTR("1"),
                 CFSTR("RequestCount"),
                 0LL);
        _objc_msgSend(v175, "dAXFjjQwJEkyDPiX:sendDic:", v172, v132);
        goto LABEL_42;
      }
      v133 = (__int64)_objc_msgSend(v130, "integerValue");
      if ( v133 < 4 )
      {
        v134 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%lu"), v133 + 1);
        v135 = objc_retainAutoreleasedReturnValue(v134);
        v136 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v137 = _objc_msgSend(v136, "initWithObjectsAndKeys:", v172, CFSTR("UserID"), v135, CFSTR("RequestCount"), 0LL);
        dispatch_time(0LL, 100000000LL);
        v173 = v135;
        v144[0] = _NSConcreteStackBlock;
        v144[1] = 3254779904LL;
        v144[2] = sub_100C5E67D;
        v144[3] = &unk_1012EA4B8;
        v144[4] = v175;
        v145 = objc_retain(v172);
        v146 = v137;
        v138 = objc_retain(v137);
        dispatch_after(v139, &_dispatch_main_q, v144);
        v140(v145);
        v141(v138);
        v142((id)v173);
        v143(v130);
        goto LABEL_42;
      }
    }
    v41 = _objc_msgSend(*((id *)v175 + 3), "brokerArray");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    _objc_msgSend(v175, "deleteBrokerDeleteWith:", v42);
    v155 = 0LL;
    v154 = 0LL;
    v153 = 0LL;
    v152 = 0LL;
    v43 = _objc_msgSend(*((id *)v175 + 3), "brokerArray");
    obj = objc_retainAutoreleasedReturnValue(v43);
    v44 = (const char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v152, v177, 16LL);
    if ( v44 )
    {
      v165 = *(id *)v153;
      v161 = 0LL;
      do
      {
        v174 = "qsid";
        v171 = "yybrid";
        v170 = "isBrokerAddWithQsid:yybrid:";
        v167 = "initWithObjectsAndKeys:";
        v162 = "xmlRequestWithMethod:path:parameters:sendDic:";
        v45 = 0LL;
        v160 = v44;
        do
        {
          if ( *(id *)v153 != v165 )
            objc_enumerationMutation(obj);
          v46 = _objc_msgSend(*(id *)(*((_QWORD *)&v152 + 1) + 8LL * (_QWORD)v45), v174);
          v47 = objc_retainAutoreleasedReturnValue(v46);
          v49 = _objc_msgSend(v48, v171);
          v173 = v45;
          v50 = objc_retainAutoreleasedReturnValue(v49);
          v51 = (unsigned __int8)_objc_msgSend(v175, v170, v47, v50);
          if ( v51 )
          {
            v52 = objc_alloc(&OBJC_CLASS___NSDictionary);
            v54 = _objc_msgSend(v52, v167, v53, CFSTR("BrokerItem"), 0LL);
            v55 = objc_alloc(&OBJC_CLASS___NSDictionary);
            v57 = _objc_msgSend(v56, v174);
            v168 = v54;
            v58 = objc_retainAutoreleasedReturnValue(v57);
            v60 = _objc_msgSend(v59, v171);
            v61 = objc_retainAutoreleasedReturnValue(v60);
            _objc_msgSend(
              v55,
              v167,
              CFSTR("wt_query_detail"),
              CFSTR("reqtype"),
              v58,
              CFSTR("qsid"),
              v61,
              CFSTR("yybrid"),
              CFSTR("mac"),
              CFSTR("app"),
              0LL);
            v62 = v168;
            _objc_msgSend(*((id *)v175 + 1), v162, 2LL, CFSTR("/wt"), v63, v168);
            ++v161;
            v44 = v160;
          }
          v45 = v173 + 1;
        }
        while ( v44 != v173 + 1 );
        v44 = (const char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v152, v177, 16LL);
      }
      while ( v44 );
      if ( v161 )
        goto LABEL_42;
    }
    else
    {
    }
    v86 = _objc_msgSend(v175, "aSucceedBlock");
    v87 = objc_retainAutoreleasedReturnValue(v86);
    if ( v87 )
    {
      v88 = _objc_msgSend(v175, "aSucceedBlock");
      v89 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v88);
      v89[2](v89);
    }
LABEL_42:
    v90 = v172;
LABEL_43:
    goto LABEL_66;
  }
  v16 = _objc_msgSend(v166, "URL");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "absoluteString");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "rangeOfString:", CFSTR("wt_query_detail"));
  if ( v20 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v65 = _objc_msgSend(v169, "objectForKey:", CFSTR("BrokerItem"));
    v66 = objc_retainAutoreleasedReturnValue(v65);
    v67 = _objc_msgSend(v66, "brokerName");
    v68 = objc_retainAutoreleasedReturnValue(v67);
    v69 = _objc_msgSend(v66, "getBrokerValue");
    v162 = (SEL)objc_retainAutoreleasedReturnValue(v69);
    _objc_msgSend(v175, "setAddBrokerName:andValue:", v68, v162);
    _objc_msgSend(*((id *)v175 + 3), "setServerItemDataSource:", v164);
    v159 = 0LL;
    v158 = 0LL;
    v157 = 0LL;
    v156 = 0LL;
    v70 = _objc_msgSend(*((id *)v175 + 3), "serverArray");
    v71 = v175;
    v165 = objc_retainAutoreleasedReturnValue(v70);
    v72 = (const char *)_objc_msgSend(v165, "countByEnumeratingWithState:objects:count:", &v156, v176, 16LL);
    if ( v72 )
    {
      v168 = *(id *)v157;
      v170 = (SEL)v66;
      do
      {
        v173 = "wt_name";
        v174 = "getIpAndPortValue";
        v171 = "setAddServerStationListName:andValue:andBrokerName:";
        v73 = 0LL;
        v167 = v72;
        do
        {
          if ( *(id *)v157 != v168 )
            objc_enumerationMutation(v165);
          v74 = *(void **)(*((_QWORD *)&v156 + 1) + 8LL * (_QWORD)v73);
          v75 = _objc_msgSend(v74, v173);
          v76 = objc_retainAutoreleasedReturnValue(v75);
          v77 = _objc_msgSend(v74, v174);
          v78 = v71;
          v79 = objc_retainAutoreleasedReturnValue(v77);
          _objc_msgSend(v78, v171, v76, v79, v68);
          v73 = (SEL)(v80 + 1);
          v71 = v175;
          v66 = (void *)v170;
        }
        while ( v167 != v73 );
        v72 = (const char *)_objc_msgSend(v165, "countByEnumeratingWithState:objects:count:", &v156, v176, 16LL);
      }
      while ( v72 );
    }
    v81 = _objc_msgSend(v71, "aSucceedBlock");
    v82 = objc_retainAutoreleasedReturnValue(v81);
    if ( v82 )
    {
      v83 = _objc_msgSend(v175, "aSucceedBlock");
      v84 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v83);
      v84[2](v84);
    }
    v85 = (char *)v66;
    goto LABEL_65;
  }
  v22 = _objc_msgSend(v166, "URL");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v24 = _objc_msgSend(v23, "absoluteString");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "rangeOfString:", CFSTR("http://xiadan.10jqka.com.cn"));
  if ( v26 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v28 = _objc_msgSend(v166, "URL");
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v30 = _objc_msgSend(v29, "absoluteString");
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v32 = _objc_msgSend(v31, "rangeOfString:", CFSTR("/ao/broker/del/"));
    if ( v32 == (id)0x7FFFFFFFFFFFFFFFLL )
    {
      if ( v169 && _objc_msgSend(v169, "count") )
      {
        v34 = _objc_msgSend(v169, "objectForKey:", CFSTR("UserID"));
        v35 = (__CFString *)objc_retainAutoreleasedReturnValue(v34);
        if ( !v35 )
        {
          v35 = 0LL;
LABEL_51:
          v90 = v35;
          goto LABEL_43;
        }
      }
      else
      {
        v35 = &charsToLeaveEscaped;
      }
      if ( _objc_msgSend(v35, "length")
        && !(unsigned __int8)_objc_msgSend(v35, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v35, "isEqualToString:", CFSTR("NUL")) )
      {
        v96 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v97 = _objc_msgSend(
                v96,
                "initWithObjectsAndKeys:",
                v35,
                CFSTR("UserID"),
                CFSTR("1"),
                CFSTR("RequestCount"),
                0LL);
        v98 = dispatch_time(0LL, 100000000LL);
        block = _NSConcreteStackBlock;
        v148 = 3254779904LL;
        v149 = sub_100C5E6F6;
        v150 = &unk_1012EA4B8;
        v151 = v175;
        v35 = objc_retain(v35);
        *(_QWORD *)(v99 + 40) = v35;
        *(_QWORD *)(v99 + 48) = v97;
        v100 = objc_retain(v97);
        dispatch_after(v98, &_dispatch_main_q, v101);
      }
      goto LABEL_51;
    }
    v91 = _objc_msgSend(v164, "objectForKey:", CFSTR("errorcode"));
    v92 = (char *)objc_retainAutoreleasedReturnValue(v91);
    v93 = v92;
    if ( v92 )
    {
      v94 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), v92);
      v95 = objc_retainAutoreleasedReturnValue(v94);
    }
    else
    {
      v95 = 0LL;
    }
    v173 = v95;
    v104 = _objc_msgSend(v166, "URL");
    v105 = objc_retainAutoreleasedReturnValue(v104);
    v106 = _objc_msgSend(v105, "absoluteString");
    v107 = objc_retainAutoreleasedReturnValue(v106);
    v108 = (unsigned __int64)_objc_msgSend(v107, "rangeOfString:", CFSTR("?"));
    v109 = (char *)_objc_msgSend(v107, "length");
    v110 = _objc_msgSend(v107, "substringWithRange:", v108 + 1, &v109[~v108]);
    v111 = objc_retainAutoreleasedReturnValue(v110);
    v112 = +[tools splitString:withSeparator:subSeparator:](
             &OBJC_CLASS___tools,
             "splitString:withSeparator:subSeparator:",
             v111,
             CFSTR("&"),
             CFSTR("="));
    objc_retainAutoreleasedReturnValue(v112);
    if ( !(unsigned __int8)_objc_msgSend((id)v173, "isEqualToString:", CFSTR("0")) && _objc_msgSend(v169, "count") )
    {
      v174 = v93;
      v114 = _objc_msgSend(v169, "objectForKey:", CFSTR("requestCount"));
      v115 = objc_retainAutoreleasedReturnValue(v114);
      v116 = _objc_msgSend(v115, "copy");
      v117 = (__int64)_objc_msgSend(v116, "integerValue");
      if ( v117 > 2 )
      {
        v126 = (char *)v116;
        v93 = (char *)v174;
      }
      else
      {
        v170 = v118;
        v119 = _objc_msgSend(v118, "objectForKey:", CFSTR("userid"));
        v167 = (SEL)objc_retainAutoreleasedReturnValue(v119);
        v121 = _objc_msgSend(v120, "objectForKey:", CFSTR("yybrid"));
        v168 = objc_retainAutoreleasedReturnValue(v121);
        v122 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v117 + 1);
        v171 = objc_retainAutoreleasedReturnValue(v122);
        v123 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v124 = _objc_msgSend(
                 v123,
                 "initWithObjectsAndKeys:",
                 v168,
                 CFSTR("yybrid"),
                 v167,
                 CFSTR("Userid"),
                 v171,
                 CFSTR("requestCount"),
                 0LL);
        v93 = (char *)v174;
        _objc_msgSend(v175, "SendRequestDeleteBroker:yybrid:sendDic:", v167, v168, v124);
        v126 = (char *)v171;
      }
    }
    v85 = (char *)v173;
LABEL_65:
  }
LABEL_66:
}

//----- (0000000100C5E67D) ----------------------------------------------------
id __fastcall sub_100C5E67D(__int64 a1)
{
  return _objc_msgSend(*(id *)(a1 + 32), "cdCLhUJgIFaNrFPY:sendDic:", *(_QWORD *)(a1 + 40), *(_QWORD *)(a1 + 48));
}

//----- (0000000100C5E69E) ----------------------------------------------------
id __fastcall sub_100C5E69E(__int64 a1, id *a2)
{
  objc_retain(a2[4]);
  objc_retain(a2[5]);
  return objc_retain(a2[6]);
}

//----- (0000000100C5E6CA) ----------------------------------------------------
void __fastcall sub_100C5E6CA(id *a1)
{
}

//----- (0000000100C5E6F6) ----------------------------------------------------
id __fastcall sub_100C5E6F6(__int64 a1)
{
  return _objc_msgSend(*(id *)(a1 + 32), "cdCLhUJgIFaNrFPY:sendDic:", *(_QWORD *)(a1 + 40), *(_QWORD *)(a1 + 48));
}

//----- (0000000100C5E717) ----------------------------------------------------
void __cdecl -[servercfgManager requestFailure:Msg:andSendDic:](servercfgManager *self, SEL a2, id a3, id a4, id a5)
{
  __CFString *v30; // r13
  NSNumber *v38; // r14
  SEL v42; // r12
  NSNumber *v44; // rax
  NSNumber *v45; // r13

  v6 = objc_retain(a3);
  v52 = objc_retain(v7);
  v54 = objc_retain(a5);
  v53 = v6;
  v8 = _objc_msgSend(v6, "URL");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "absoluteString");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(v11, "rangeOfString:", CFSTR("wt_query_yyb_user"));
  v13(v9);
  if ( v12 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v14 = -[servercfgManager aFailureBlock](self, "aFailureBlock");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    if ( v15 )
    {
      v16 = -[servercfgManager aFailureBlock](self, "aFailureBlock");
      v17 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v16);
      v17[2](v17, v52);
    }
  }
  v18 = _objc_msgSend(v53, "URL");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "absoluteString");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v21, "rangeOfString:", CFSTR("http://xiadan.10jqka.com.cn"));
  v24 = v54;
  if ( v22 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v25 = _objc_msgSend(v53, v23);
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27 = _objc_msgSend(v26, "absoluteString");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29 = _objc_msgSend(v28, "rangeOfString:", CFSTR("/ao/broker/del/"));
    if ( v29 == (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v30 = &charsToLeaveEscaped;
      if ( v54 && _objc_msgSend(v54, "count") )
      {
        v31 = _objc_msgSend(v54, "objectForKey:", CFSTR("UserID"));
        v30 = (__CFString *)objc_retainAutoreleasedReturnValue(v31);
      }
      v32 = _objc_msgSend(v54, "objectForKey:", CFSTR("RequestCount"));
      v33 = objc_retainAutoreleasedReturnValue(v32);
      if ( v30
        && _objc_msgSend(v30, "length")
        && !(unsigned __int8)_objc_msgSend(v30, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v30, "isEqualToString:", CFSTR("NUL"))
        && (unsigned __int8)_objc_msgSend(v33, "isEqualToString:", CFSTR("1")) )
      {
        v34 = objc_alloc(&OBJC_CLASS___NSDictionary);
        v35 = _objc_msgSend(
                v34,
                "initWithObjectsAndKeys:",
                v30,
                CFSTR("UserID"),
                CFSTR("2"),
                CFSTR("RequestCount"),
                0LL);
        -[servercfgManager dAXFjjQwJEkyDPiX:sendDic:](self, "dAXFjjQwJEkyDPiX:sendDic:", v30, v35);
      }
      v24 = v54;
    }
    else
    {
      v24 = v54;
      if ( _objc_msgSend(v54, "count") )
      {
        v36 = _objc_msgSend(v54, "objectForKey:", CFSTR("requestCount"));
        v37 = objc_retainAutoreleasedReturnValue(v36);
        v38 = (NSNumber *)_objc_msgSend(v37, "copy");
        v39 = (__int64)_objc_msgSend(v38, "integerValue");
        if ( v39 > 2 )
        {
          v45 = v38;
        }
        else
        {
          v41 = _objc_msgSend(v54, v40, CFSTR("Userid"));
          v51 = objc_retainAutoreleasedReturnValue(v41);
          v43 = _objc_msgSend(v54, v42, CFSTR("yybrid"));
          objc_retainAutoreleasedReturnValue(v43);
          v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v39 + 1);
          v45 = objc_retainAutoreleasedReturnValue(v44);
          v46 = objc_alloc(&OBJC_CLASS___NSDictionary);
          v48 = _objc_msgSend(
                  v46,
                  "initWithObjectsAndKeys:",
                  v47,
                  CFSTR("yybrid"),
                  v51,
                  CFSTR("Userid"),
                  v45,
                  CFSTR("requestCount"),
                  0LL);
          -[servercfgManager SendRequestDeleteBroker:yybrid:sendDic:](
            self,
            "SendRequestDeleteBroker:yybrid:sendDic:",
            v51,
            v49,
            v48);
        }
        v24 = v54;
      }
    }
  }
}

//----- (0000000100C5ECEE) ----------------------------------------------------
char __cdecl -[servercfgManager _needRequestUserBrokers](servercfgManager *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSString *v6; // rax
  NSString *v7; // r15

  v2 = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, 1uLL, 1);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "firstObject");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@/JYConfig.plist"), v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = v9;
  if ( v9 && (unsigned __int8)_objc_msgSend(v9, "fileExistsAtPath:isDirectory:", v7) )
  {
    v11 = objc_alloc(&OBJC_CLASS___NSDictionary);
    v12 = _objc_msgSend(v11, "initWithContentsOfFile:", v7);
    if ( !v12 )
      goto LABEL_6;
    v19 = v5;
    v18 = v12;
    v13 = _objc_msgSend(v12, "objectForKey:", CFSTR("DoNotUpdateServerList"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "integerValue");
    v5 = v19;
    v12 = v18;
    if ( v15 )
    else
LABEL_6:
  }
  return v16;
}

//----- (0000000100C5EEDF) ----------------------------------------------------
NSMutableArray *__cdecl -[servercfgManager arrBrokerList](servercfgManager *self, SEL a2)
{
  return self->_arrBrokerList;
}

//----- (0000000100C5EEE9) ----------------------------------------------------
void __cdecl -[servercfgManager setArrBrokerList:](servercfgManager *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_arrBrokerList, a3);
}

//----- (0000000100C5EEFA) ----------------------------------------------------
id __cdecl -[servercfgManager aSucceedBlock](servercfgManager *self, SEL a2)
{
  return objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100C5EF0B) ----------------------------------------------------
void __cdecl -[servercfgManager setASucceedBlock:](servercfgManager *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100C5EF1A) ----------------------------------------------------
id __cdecl -[servercfgManager aFailureBlock](servercfgManager *self, SEL a2)
{
  return objc_getProperty(self, a2, 48LL, 0);
}

//----- (0000000100C5EF2B) ----------------------------------------------------
void __cdecl -[servercfgManager setAFailureBlock:](servercfgManager *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 48LL);
}

//----- (0000000100C5EF3A) ----------------------------------------------------
void __cdecl -[servercfgManager .cxx_destruct](servercfgManager *self, SEL a2)
{
  objc_storeStrong(&self->_aFailureBlock, 0LL);
  objc_storeStrong(&self->_aSucceedBlock, 0LL);
  objc_storeStrong((id *)&self->_arrBrokerList, 0LL);
  objc_storeStrong((id *)&self->_addQsmodel, 0LL);
  objc_storeStrong((id *)&self->_aAddQsHttpRequest, 0LL);
  objc_storeStrong((id *)&self->_httpRequest, 0LL);
}

//----- (0000000100C5EF93) ----------------------------------------------------
int __fastcall sub_100C5EF93(char *a1, char a2, __int64 a3, char a4)
{

  v5 = a1;
  v9 = a3;
  v10 = a4;
  for ( i = a1; ; v5 = i )
  {
    do
      v7 = *i++;
    while ( v7 && v7 != a2 );
    if ( v7 != a2 )
      break;
    *(i - 1) = 0;
    sub_100C56258((__int64)&v9, v5);
  }
  return sub_100C56258((__int64)&v9, v5);
}

