//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

#import "NSTableViewDataSource-Protocol.h"
#import "NSTableViewDelegate-Protocol.h"
#import "ZiXunDispatchDelegate-Protocol.h"

@class HXBaseView, HXButton, MajorEventRequestModule, NSArray, NSScrollView, NSString, NSTableView, NSTextField, NSTimer, ZiXunRequestModule;

@interface ZiXunTableViewController : NSViewController <ZiXunDispatchDelegate, NSTableViewDataSource, NSTableViewDelegate>
{
    BOOL _isNeedNewZiXunWindow;
    BOOL _isSecondViewHidden;
    BOOL _isMajorEventShow;
    BOOL _isSpecialViewStateSetted;
    HXBaseView *_firstView;
    HXBaseView *_secondView;
    NSScrollView *_firstScrollView;
    NSScrollView *_secondScrollView;
    NSTableView *_firstTable;
    NSTableView *_secondTable;
    NSTextField *_firstNoNewsTextField;
    NSTextField *_secondNoNewsTextField;
    HXBaseView *_secondTableTitleView;
    NSTextField *_secondTableTitleTF;
    HXButton *_secondTableMoreBtn;
    HXBaseView *_majorEventTitleView;
    NSTextField *_majorEventTitleTF;
    HXButton *_majorEventMoreBtn;
    HXBaseView *_majorEventContentView;
    NSScrollView *_majorEventScrollView;
    NSTableView *_majorEventTable;
    NSTextField *_majorEventNoDataTF;
    NSString *_stockCode;
    NSString *_market;
    CDUnknownBlockType _ziXunOpenURLBlock;
    NSArray *_firstTableZiXunDatas;
    NSArray *_secondTableZiXunDatas;
    ZiXunRequestModule *_firstReqModule;
    ZiXunRequestModule *_secondReqModule;
    MajorEventRequestModule *_majorEvnetReqModule;
    NSString *_ziXunUrl;
    NSTimer *_requestTimer;
    NSArray *_reqParamArr;
    NSArray *_majorEventArr;
    HXBaseView *_secondDocumentView;
}


@property(retain, nonatomic) HXBaseView *secondDocumentView; // @synthesize secondDocumentView=_secondDocumentView;
@property(nonatomic) BOOL isSpecialViewStateSetted; // @synthesize isSpecialViewStateSetted=_isSpecialViewStateSetted;
@property(nonatomic) BOOL isMajorEventShow; // @synthesize isMajorEventShow=_isMajorEventShow;
@property(retain, nonatomic) NSArray *majorEventArr; // @synthesize majorEventArr=_majorEventArr;
@property(retain, nonatomic) NSArray *reqParamArr; // @synthesize reqParamArr=_reqParamArr;
@property(retain, nonatomic) NSTimer *requestTimer; // @synthesize requestTimer=_requestTimer;
@property(nonatomic) BOOL isSecondViewHidden; // @synthesize isSecondViewHidden=_isSecondViewHidden;
@property(copy, nonatomic) NSString *ziXunUrl; // @synthesize ziXunUrl=_ziXunUrl;
@property(retain, nonatomic) MajorEventRequestModule *majorEvnetReqModule; // @synthesize majorEvnetReqModule=_majorEvnetReqModule;
@property(retain, nonatomic) ZiXunRequestModule *secondReqModule; // @synthesize secondReqModule=_secondReqModule;
@property(retain, nonatomic) ZiXunRequestModule *firstReqModule; // @synthesize firstReqModule=_firstReqModule;
@property(retain, nonatomic) NSArray *secondTableZiXunDatas; // @synthesize secondTableZiXunDatas=_secondTableZiXunDatas;
@property(retain, nonatomic) NSArray *firstTableZiXunDatas; // @synthesize firstTableZiXunDatas=_firstTableZiXunDatas;
@property(copy, nonatomic) CDUnknownBlockType ziXunOpenURLBlock; // @synthesize ziXunOpenURLBlock=_ziXunOpenURLBlock;
@property(copy, nonatomic) NSString *market; // @synthesize market=_market;
@property(copy, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
@property(nonatomic) BOOL isNeedNewZiXunWindow; // @synthesize isNeedNewZiXunWindow=_isNeedNewZiXunWindow;
@property(retain) NSTextField *majorEventNoDataTF; // @synthesize majorEventNoDataTF=_majorEventNoDataTF;
@property __weak NSTableView *majorEventTable; // @synthesize majorEventTable=_majorEventTable;
@property __weak NSScrollView *majorEventScrollView; // @synthesize majorEventScrollView=_majorEventScrollView;
@property(retain) HXBaseView *majorEventContentView; // @synthesize majorEventContentView=_majorEventContentView;
@property __weak HXButton *majorEventMoreBtn; // @synthesize majorEventMoreBtn=_majorEventMoreBtn;
@property __weak NSTextField *majorEventTitleTF; // @synthesize majorEventTitleTF=_majorEventTitleTF;
@property(retain) HXBaseView *majorEventTitleView; // @synthesize majorEventTitleView=_majorEventTitleView;
@property __weak HXButton *secondTableMoreBtn; // @synthesize secondTableMoreBtn=_secondTableMoreBtn;
@property __weak NSTextField *secondTableTitleTF; // @synthesize secondTableTitleTF=_secondTableTitleTF;
@property(retain) HXBaseView *secondTableTitleView; // @synthesize secondTableTitleView=_secondTableTitleView;
@property(retain) NSTextField *secondNoNewsTextField; // @synthesize secondNoNewsTextField=_secondNoNewsTextField;
@property(retain) NSTextField *firstNoNewsTextField; // @synthesize firstNoNewsTextField=_firstNoNewsTextField;
@property __weak NSTableView *secondTable; // @synthesize secondTable=_secondTable;
@property __weak NSTableView *firstTable; // @synthesize firstTable=_firstTable;
@property __weak NSScrollView *secondScrollView; // @synthesize secondScrollView=_secondScrollView;
@property __weak NSScrollView *firstScrollView; // @synthesize firstScrollView=_firstScrollView;
@property __weak HXBaseView *secondView; // @synthesize secondView=_secondView;
@property __weak HXBaseView *firstView; // @synthesize firstView=_firstView;
- (id)sortByTime:(id)arg1;
- (id)getZiXunURL:(id)arg1;
- (id)getZiXunBaseURLFromZiXunTree:(unsigned long long)arg1;
- (id)getZiXunTitle:(unsigned long long)arg1;
- (id)filterInvalidZiXunData:(id)arg1;
- (void)sendMajorEventMaiDian;
- (void)relayoutForMajorEventViewIfNeeded;
- (void)showMajorEventRelativeView;
- (void)removeMajorEventRelativeView;
- (void)setMajorEventViewState;
- (void)setNoDataTFStateForMajroEventTable;
- (void)setNoDataTFStateForSecondTable;
- (void)setNoDataTFStateForFirstTable;
- (void)frameDidChanged:(id)arg1;
- (void)openZiXunDetailWindow:(id)arg1;
- (void)actionWhenTableFocusDidChange:(id)arg1;
- (void)postNotificationForChangingTableFocus:(id)arg1;
- (void)invalidateNotificationObserver;
- (void)registerNotificationObserver;
- (void)requestZiXunForSecondTable:(id)arg1;
- (void)requestZiXunForFirstTable:(id)arg1;
- (void)reloadDataForTable;
- (void)clearCacheDataForMajorEventTable;
- (void)clearCacheDataForSecondTable;
- (void)clearCacheDataForFirstTable;
- (void)clearCacheData;
- (void)setUpTheme;
- (void)tableView:(id)arg1 willDisplayCell:(id)arg2 forTableColumn:(id)arg3 row:(long long)arg4;
- (id)tableView:(id)arg1 objectValueForTableColumn:(id)arg2 row:(long long)arg3;
- (long long)numberOfRowsInTableView:(id)arg1;
- (void)majorEventMoreBtnClicked:(id)arg1;
- (void)gongGaoMoreBtnClicked:(id)arg1;
- (void)actionForTableClicked:(id)arg1;
- (void)failToReceiveZiXunData:(unsigned long long)arg1;
- (void)receiveZiXunData:(id)arg1 tableFlag:(unsigned long long)arg2;
- (void)requestMajorEventDataIfNeeded;
- (void)timerToRequestZiXun;
- (void)hideSecondViewForGuoWaiZhiShu;
- (void)showSecondView;
- (void)hideSecondView;
- (void)requestForZiXun:(id)arg1;
- (void)setTableAction;
- (void)setDefaultProperties;
- (void)invalidateRequestTimer;
- (void)fireRequestTimer;
- (void)viewDidDisappearHandle;
- (void)viewDidAppearHandle;
- (void)viewDidDisappear;
- (void)viewDidAppear;
- (void)dealloc;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

