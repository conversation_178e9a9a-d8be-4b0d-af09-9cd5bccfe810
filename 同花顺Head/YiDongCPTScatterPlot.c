CALayer *__cdecl -[YiDongCPTScatterPlot animationLayer](YiDongCPTScatterPlot *self, SEL a2)
{
  CALayer *animationLayer; // rdi
  CALayer *v5; // rax
  CALayer *v6; // rdi

  animationLayer = self->_animationLayer;
  if ( !animationLayer )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___CALayer, "layer");
    v5 = (CALayer *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->_animationLayer;
    self->_animationLayer = v5;
    animationLayer = self->_animationLayer;
  }
  return (CALayer *)objc_retainAutoreleaseReturnValue(animationLayer);
}

//----- (000000010071C242) ----------------------------------------------------
char __cdecl -[YiDongCPTScatterPlot pointingDeviceUpEvent:atPoint:](
        YiDongCPTScatterPlot *self,
        SEL a2,
        id a3,
        CGPoint a4)
{

  v5 = objc_retain(a3);
  v6 = _objc_msgSend(self, "graph");
  objc_retainAutoreleasedReturnValue(v6);
  v7 = _objc_msgSend(self, "plotArea");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = v8;
  if ( !v10 )
    goto LABEL_22;
  if ( !v8 )
    goto LABEL_22;
  if ( (unsigned __int8)_objc_msgSend(self, "isHidden") )
    goto LABEL_22;
  v11 = _objc_msgSend(self, "plotSpace");
  v28 = v9;
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "isDragging");
  v29 = v13;
  v14 = v12;
  v9 = v28;
  if ( v15 )
    goto LABEL_22;
  v27 = v5;
  v16 = _objc_msgSend(self, "delegate");
  v30 = objc_retainAutoreleasedReturnValue(v16);
  if ( !(unsigned __int8)_objc_msgSend(v30, "respondsToSelector:", "scatterPlot:plotSymbolTouchUpAtRecordIndex:")
    && !(unsigned __int8)_objc_msgSend(
                           v30,
                           "respondsToSelector:",
                           "scatterPlot:plotSymbolTouchUpAtRecordIndex:withEvent:")
    && !(unsigned __int8)_objc_msgSend(v30, "respondsToSelector:", "scatterPlot:plotSymbolWasSelectedAtRecordIndex:")
    && !(unsigned __int8)_objc_msgSend(
                           v30,
                           "respondsToSelector:",
                           "scatterPlot:plotSymbolWasSelectedAtRecordIndex:withEvent:") )
  {
    goto LABEL_21;
  }
  _objc_msgSend(v29, "convertPoint:toLayer:", v28, a4.x, a4.y);
  if ( !_objc_msgSend(self, "cachedDataCount") )
    goto LABEL_21;
  v17 = 0LL;
  while ( 1 )
  {
    v18 = _objc_msgSend(self, "plotSymbolForRecordIndex:", v17);
    objc_retainAutoreleasedReturnValue(v18);
    v19 = _objc_msgSend(&OBJC_CLASS___YiDongCPTPlotSymbol, "class");
    if ( (unsigned __int8)_objc_msgSend(v20, "isKindOfClass:", v19) )
      break;
LABEL_14:
    if ( (unsigned __int64)_objc_msgSend(self, "cachedDataCount") <= ++v17 )
      goto LABEL_21;
  }
  v22 = objc_retain(v21);
  if ( !(unsigned __int8)_objc_msgSend(v22, "isSymbolRectContainsPoint:", a4.x, a4.y) )
  {
    v9 = v28;
    goto LABEL_14;
  }
  if ( (unsigned __int8)_objc_msgSend(v30, "respondsToSelector:", "scatterPlot:plotSymbolWasSelectedAtRecordIndex:") )
    _objc_msgSend(v30, v23, self, v17);
  if ( (unsigned __int8)_objc_msgSend(
                          v30,
                          "respondsToSelector:",
                          "scatterPlot:plotSymbolWasSelectedAtRecordIndex:withEvent:") )
    _objc_msgSend(v30, v24, self, v17, v27);
  v9 = v28;
LABEL_21:
  v5 = v27;
LABEL_22:
  return 0;
}

//----- (000000010071C548) ----------------------------------------------------
void __cdecl -[YiDongCPTScatterPlot setAnimationLayer:](YiDongCPTScatterPlot *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_animationLayer, a3);
}

//----- (000000010071C55C) ----------------------------------------------------
void __cdecl -[YiDongCPTScatterPlot .cxx_destruct](YiDongCPTScatterPlot *self, SEL a2)
{
  objc_storeStrong((id *)&self->_animationLayer, 0LL);
}

