ZuoShiZhuanRangTableViewController *__cdecl -[ZuoShiZhuanRangTableViewController initWithNibName:bundle:](
        ZuoShiZhuanRangTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ZuoShiZhuanRangTableViewController *v4; // rax
  ZuoShiZhuanRangTableViewController *v5; // rbx

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangTableViewController;
  v4 = -[HXBaseTableViewController initWithNibName:bundle:](&v7, "initWithNibName:bundle:", a3, a4);
  v5 = v4;
  if ( v4 )
  {
    -[QuoteBaseTableViewController setBlockID:](v4, "setBlockID:", 55823LL);
    -[QuoteBaseTableViewController setTableID:](v5, "setTableID:", 198915LL);
    -[QuoteBaseTableViewController setTableInfo:](v5, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang"));
  }
  return v5;
}

//----- (0000000100768D3A) ----------------------------------------------------
void __cdecl -[ZuoShiZhuanRangTableViewController viewDidLoad](ZuoShiZhuanRangTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangTableViewController;
  -[XinSanBanBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 55823LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 198915LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang"));
}

