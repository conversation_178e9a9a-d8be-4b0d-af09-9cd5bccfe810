void __cdecl -[commumodule dealloc](commumodule *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___commumodule;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (0000000100C29E8B) ----------------------------------------------------
commumodule *__cdecl -[commumodule initWithUserModel:](commumodule *self, SEL a2, id a3)
{
  commumodule *v4; // rax
  commumodule *v5; // r15
  SEL v7; // r12
  SEL v11; // r12
  SEL v15; // r12
  SEL v22; // r12
  SEL v25; // r12
  _QWORD *v26; // rax
  _QWORD *v29; // rax
  _QWORD *v30; // r14
  _QWORD *v31; // rax
  _QWORD *v33; // rbx
  _QWORD *v34; // r12
  _QWORD *v35; // rax
  _QWORD *v37; // rbx
  _QWORD *v38; // rax
  _QWORD v44[4]; // [rsp+28h] [rbp-B8h] BYREF
  _QWORD v46[4]; // [rsp+50h] [rbp-90h] BYREF
  commumodule *v47; // [rsp+70h] [rbp-70h]
  _QWORD v48[4]; // [rsp+78h] [rbp-68h] BYREF

  v3 = objc_retain(a3);
  +[tools AddTradeLog:logText:](&OBJC_CLASS___tools, "AddTradeLog:logText:", 2LL, CFSTR("initWithUserModel Success!"));
  v50.receiver = self;
  v50.super_class = (Class)&OBJC_CLASS___commumodule;
  v4 = objc_msgSendSuper2(&v50, "init");
  v5 = v4;
  if ( v4 )
  {
    if ( !*((_QWORD *)v4 + 31) )
    {
      v6 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
      v8 = _objc_msgSend(v6, v7);
      v9 = (void *)*((_QWORD *)v5 + 31);
      *((_QWORD *)v5 + 31) = v8;
    }
    if ( !*((_QWORD *)v5 + 32) )
    {
      v10 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
      v12 = _objc_msgSend(v10, v11);
      v13 = (void *)*((_QWORD *)v5 + 32);
      *((_QWORD *)v5 + 32) = v12;
    }
    if ( !*((_QWORD *)v5 + 33) )
    {
      v14 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
      v16 = _objc_msgSend(v14, v15);
      v17 = (void *)*((_QWORD *)v5 + 33);
      *((_QWORD *)v5 + 33) = v16;
    }
    -[commumodule setAUserModel:](v5, "setAUserModel:", v3);
    -[commumodule initModule](v5, "initModule");
    v18 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(
      v19,
      "addObserver:selector:name:object:",
      v5,
      "networkNotReachable",
      CFSTR("JYNotification_Network_NotReachable"),
      0LL);
    v20 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    _objc_msgSend(v21, v22, v5, "networkReachableVia", CFSTR("JYNotification_Network_ReachableVia"), 0LL);
    v23 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    _objc_msgSend(v24, v25, v5, "handleSIGPIPE", CFSTR("SignalSIGPIPENotification"), 0LL);
    -[commumodule reloadSetIsMoniUser](v5, "reloadSetIsMoniUser");
    -[commumodule reloadSetIsMeiguUser](v5, "reloadSetIsMeiguUser");
    -[commumodule reloadSetIsZyyUser](v5, "reloadSetIsZyyUser");
    objc_storeStrong((id *)v5 + 18, v5);
    v46[0] = _NSConcreteStackBlock;
    v46[1] = 3254779904LL;
    v46[2] = sub_100C2A336;
    v46[3] = &unk_1012EA228;
    v47 = objc_retain(v5);
    v26 = objc_retainBlock(v46);
    v27 = (void *)*((_QWORD *)v5 + 13);
    *((_QWORD *)v5 + 13) = v26;
    v48[0] = _NSConcreteStackBlock;
    v48[1] = 3254779904LL;
    v48[2] = sub_100C2A467;
    v48[3] = &unk_1012EA258;
    v29 = objc_retain(v28);
    v51 = v3;
    v30 = v29;
    v49 = v29;
    v31 = objc_retainBlock(v48);
    v32 = (void *)v30[14];
    v30[14] = v31;
    v33 = objc_retain(v30);
    v34[4] = v33;
    v35 = objc_retainBlock(v34);
    v36 = (void *)v33[15];
    v33[15] = v35;
    v44[0] = _NSConcreteStackBlock;
    v44[1] = 3254779904LL;
    v44[2] = sub_100C2A8C8;
    v44[3] = &unk_1012EA228;
    v37 = objc_retain(v33);
    v45 = v37;
    v38 = objc_retainBlock(v44);
    v39 = (void *)v37[16];
    v37[16] = v38;
    v40 = (void *)v37[17];
    v37[17] = &stru_1012EA2D8;
    *((_BYTE *)v37 + 196) = 0;
    v3 = v51;
  }
  *((_BYTE *)v5 + 197) = 0;
  v42 = (void *)*((_QWORD *)v5 + 34);
  *((_QWORD *)v5 + 34) = 0LL;
  *((_DWORD *)v5 + 48) = 0;
  return v5;
}

//----- (0000000100C2A336) ----------------------------------------------------
void __fastcall sub_100C2A336(__int64 a1, __int64 a2)
{

  _objc_msgSend(*(id *)(a1 + 32), "setDwSessionId:", *(unsigned int *)(a2 + 104));
  v2 = _objc_msgSend(*(id *)(a1 + 32), "defRspHandleDelegate");
  if ( objc_retainAutoreleasedReturnValue(v2) )
  {
    v3 = _objc_msgSend(*(id *)(a1 + 32), "defRspHandleDelegate");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = (unsigned __int8)_objc_msgSend(v4, "respondsToSelector:", "OnTradeRsponceNotify:");
    if ( v5 )
    {
      v7 = _objc_msgSend(*(id *)(a1 + 32), "defRspHandleDelegate");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      _objc_msgSend(v8, "OnTradeRsponceNotify:", a2);
    }
  }
}

//----- (0000000100C2A467) ----------------------------------------------------
void __fastcall sub_100C2A467(__int64 a1, __int64 a2, __int64 a3)
{

  v4 = _objc_msgSend(*(id *)(a1 + 32), "getRspDelegateByContextId:", a2);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5;
  if ( v5 && (unsigned __int8)_objc_msgSend(v5, "respondsToSelector:", "OnTradeRsponceNotify:") )
  {
    v7 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
    if ( objc_retainAutoreleasedReturnValue(v7) )
    {
      v14 = a3;
      v8 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      if ( (unsigned __int8)_objc_msgSend(v9, "bActiveUser") )
      {
        _objc_msgSend(v6, "OnTradeRsponceNotify:", v14);
      }
      else
      {
        v11 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        v15 = (unsigned __int8)_objc_msgSend(v12, "GetType");
        if ( (v15 & 8) != 0 )
          _objc_msgSend(v6, "OnTradeRsponceNotify:", v14);
      }
    }
    else
    {
    }
  }
}

//----- (0000000100C2A5F5) ----------------------------------------------------
__int64 __fastcall sub_100C2A5F5(__int64 a1, int a2, __int64 a3)
{
  bool v17; // zf
  SEL v23; // [rsp+40h] [rbp-100h]
  SEL v24; // [rsp+48h] [rbp-F8h]
  SEL v29; // [rsp+70h] [rbp-D0h]
  SEL v30; // [rsp+78h] [rbp-C8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v27 = a3;
  v3 = a1;
  v4 = _objc_msgSend(*(id *)(a1 + 32), "GetReceiveDelegateArray:", a2);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v22 = 0LL;
    v21 = 0LL;
    v20 = 0LL;
    v19 = 0LL;
    v28 = v5;
    obj = objc_retain(v5);
    v6 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v33, 16LL);
    if ( v6 )
    {
      v7 = *(_QWORD *)v20;
      v26 = *(_QWORD *)v20;
      do
      {
        v29 = "OnTradeRsponceNotify:";
        v23 = "respondsToSelector:";
        v30 = "aUserModel";
        v24 = "bActiveUser";
        if ( !v6 )
          v6 = 1LL;
        v8 = 0LL;
        v25 = v6;
        do
        {
          if ( *(_QWORD *)v20 != v7 )
            objc_enumerationMutation(obj);
          v9 = *(void **)(*((_QWORD *)&v19 + 1) + 8 * v8);
          if ( v9 && (unsigned __int8)_objc_msgSend(*(id *)(*((_QWORD *)&v19 + 1) + 8 * v8), v23, v29) )
          {
            v10 = _objc_msgSend(*(id *)(v3 + 32), v30);
            v11 = objc_retainAutoreleasedReturnValue(v10);
            if ( v11 )
            {
              v31 = v11;
              v12 = v3;
              v13 = _objc_msgSend(*(id *)(v3 + 32), v30);
              v14 = objc_retainAutoreleasedReturnValue(v13);
              v15(v14, v24);
              v17 = v16 == 0;
              v3 = v12;
              v6 = v25;
              v7 = v26;
              if ( !v17 )
                _objc_msgSend(v9, v29, v27);
            }
            else
            {
            }
          }
          ++v8;
        }
        while ( v6 != v8 );
        v6 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v33, 16LL);
      }
      while ( v6 );
    }
    v5 = v28;
  }
  return __stack_chk_guard;
}

//----- (0000000100C2A8C8) ----------------------------------------------------
void __fastcall sub_100C2A8C8(__int64 a1, __int64 a2)
{
  NSString *v8; // rax
  NSString *v9; // rax

  v2 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
  if ( !objc_retainAutoreleasedReturnValue(v2) )
  {
    return;
  }
  v20 = a2;
  v3 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)_objc_msgSend(v4, "bActiveUser");
  if ( !v5 )
    return;
  v7 = v20;
  sub_100D6DC60(&v19, v20);
  if ( *(_WORD *)(v19 + 74) != 1538 )
    goto LABEL_10;
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), *(unsigned int *)(v19 + 64));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(*(id *)(a1 + 32), "getRspDelegateByContextId:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  if ( !v11
    || (v12 = _objc_msgSend(&OBJC_CLASS___JYZyyManager, "class"),
        !(unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12))
    || !(unsigned __int8)_objc_msgSend(v11, "respondsToSelector:", "OnTradeRsponceNotify:") )
  {
    v7 = v20;
LABEL_10:
    v15 = _objc_msgSend(*(id *)(a1 + 32), "aUserModel");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17 = _objc_msgSend(v16, "getCacheDataObject");
    objc_retainAutoreleasedReturnValue(v17);
    _objc_msgSend(v18, "saveSourceData:", v7);
    goto LABEL_11;
  }
  _objc_msgSend(v11, "OnTradeRsponceNotify:", v20);
LABEL_11:
  sub_100D12680(&v19);
}

//----- (0000000100C2AB23) ----------------------------------------------------
void __cdecl sub_100C2AB23(id a1, void *a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "postNotificationName:object:", CFSTR("TradeSessionConnectSuccess"), 0LL);
}

//----- (0000000100C2AB84) ----------------------------------------------------
void __cdecl -[commumodule initModule](commumodule *self, SEL a2)
{
  _BYTE *v13; // r14
  _QWORD v15[4]; // [rsp+0h] [rbp-20h] BYREF

  v15[0] = v2;
  v4 = *((_QWORD *)self + 23);
  if ( v4
    || (v5 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance"),
        v6 = objc_retainAutoreleasedReturnValue(v5),
        *((_QWORD *)self + 23) = _objc_msgSend(v6, "getOneModule"),
        (v4 = *((_QWORD *)self + 23)) != 0) )
  {
    sub_100D37DB0(v4);
    sub_100D37DD0(v15, *((_QWORD *)self + 23));
    v7 = sub_100F9F630((__int64)v15);
    v8 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = (void *)*((_QWORD *)self + 29);
    *((_QWORD *)self + 29) = v9;
    v11 = +[tools getTradeProductAndVersion](&OBJC_CLASS___tools, "getTradeProductAndVersion");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = (_BYTE *)sub_100F9F630((__int64)v15);
    v14 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v12);
    sub_100D3FBE0(v13, "Version", (__int64)v14);
    sub_100C56008(v15);
  }
}

//----- (0000000100C2ACE6) ----------------------------------------------------
void __cdecl -[commumodule networkNotReachable](commumodule *self, SEL a2)
{
  NSString *v4; // rax
  NSString *v5; // r14
  NSString *v6; // rax
  NSString *v7; // r13
  id WeakRetained; // [rsp+0h] [rbp-30h]

  WeakRetained = objc_loadWeakRetained((id *)self + 28);
  v2 = _objc_msgSend(WeakRetained, "getUserLogInfo");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@网络断开"), v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
         "-[commumodule networkNotReachable]",
         586LL,
         v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v7);
  -[commumodule setIsLostSocketConnection:](self, "setIsLostSocketConnection:", 1LL);
}

//----- (0000000100C2AE17) ----------------------------------------------------
void __cdecl -[commumodule networkReachableVia](commumodule *self, SEL a2)
{
  NSString *v4; // rax
  NSString *v5; // r14
  NSString *v6; // rax
  NSString *v7; // r13
  id WeakRetained; // [rsp+0h] [rbp-30h]

  WeakRetained = objc_loadWeakRetained((id *)self + 28);
  v2 = _objc_msgSend(WeakRetained, "getUserLogInfo");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@网络已连接"), v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
         "-[commumodule networkReachableVia]",
         592LL,
         v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v7);
  -[commumodule setIsLostSocketConnection:](self, "setIsLostSocketConnection:", 0LL);
}

//----- (0000000100C2AF45) ----------------------------------------------------
void __cdecl -[commumodule handleSIGPIPE](commumodule *self, SEL a2)
{
  -[commumodule reconnectSession](self, "reconnectSession");
}

//----- (0000000100C2AF57) ----------------------------------------------------
void __cdecl -[commumodule setIsLostSocketConnection:](commumodule *self, SEL a2, char a3)
{
  UserModel *v3; // rax
  UserModel *v4; // r14

  if ( *((_BYTE *)self + 196) != a3 )
    *((_BYTE *)self + 196) = a3;
  if ( a3 && *((_BYTE *)self + 155) )
  {
    v3 = -[commumodule aUserModel](self, "aUserModel");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = (unsigned __int8)-[UserModel bActiveUser](v4, "bActiveUser");
    if ( v5 )
      -[commumodule reconnectSession](self, "reconnectSession");
    else
      *((_BYTE *)self + 158) = 1;
  }
}

//----- (0000000100C2AFFC) ----------------------------------------------------
id __cdecl -[commumodule getHddinfo](commumodule *self, SEL a2)
{
  __CFString *v2; // r14
  int v3; // r15d
  uint32_t v4; // eax
  NSString *v5; // rax
  __CFString *v6; // r13

  v2 = &charsToLeaveEscaped;
  v3 = 32;
  do
  {
    v4 = arc4random();
    v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%d"), v4 % 0xA);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v8 = _objc_msgSend(v7, "stringByAppendingString:", v6);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v2 = v6;
    --v3;
  }
  while ( v3 );
  v11 = +[tools getMd5String:](&OBJC_CLASS___tools, "getMd5String:", v9);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  return objc_autoreleaseReturnValue(v12);
}

//----- (0000000100C2B122) ----------------------------------------------------
void __cdecl -[commumodule DJAwpxdGFxRzelWL:](commumodule *self, SEL a2, id a3)
{
  _BYTE *v22; // r14
  _BYTE *v24; // r14
  _BYTE *v25; // r15
  _BYTE *v30; // r14
  _BYTE *v31; // r15
  _BYTE *v36; // r14
  _BYTE *v37; // r15
  _BYTE *v47; // r14
  _BYTE *v48; // rbx
  _BYTE *v50; // r14
  _BYTE *v51; // r15
  NSString *v58; // rax
  SEL v59; // r12
  NSString *v65; // rax
  NSString *v66; // rbx
  _BYTE *v78; // r14
  _BYTE *v79; // rbx
  _BYTE *v81; // r14
  _BYTE *v82; // rbx
  _BYTE *v84; // r14
  _BYTE *v85; // r13
  SEL v89; // r12
  NSString *v104; // rax
  NSString *v105; // r13
  _BYTE *v107; // r14
  _BYTE *v108; // r15
  _BYTE *v120; // r14
  _BYTE *v121; // r15
  NSString *v145; // rax
  NSString *v146; // rbx
  _BYTE *v147; // r14
  _BYTE *v148; // rbx
  _BYTE *v180; // r14
  _BYTE *v181; // r15
  SEL v182; // r12
  NSString *v196; // rax
  NSString *v197; // rax
  NSString *v198; // rbx
  _BYTE *v200; // r14
  SEL v216; // r12
  _BYTE *v222; // r14
  unsigned int v226; // eax
  NSString *v232; // rax
  id WeakRetained; // r13
  NSString *v238; // rax
  NSString *v239; // rax
  NSString *v240; // rax
  NSString *v241; // r15
  _BYTE *v247; // r14
  _BYTE *v248; // r13
  SEL v252; // r12
  NSString *v258; // rax
  _BYTE *v259; // r14
  _BYTE *v263; // r12
  _BYTE *v268; // r14
  _BYTE *v272; // r12
  _BYTE *v276; // r14
  _BYTE *v277; // r13
  SEL v281; // r12
  NSString *v285; // r14
  _QWORD v287[10]; // [rsp+28h] [rbp-F8h] BYREF
  SEL v299; // [rsp+D0h] [rbp-50h]
  SEL v301; // [rsp+E0h] [rbp-40h]

  v302 = objc_retain(a3);
  if ( !v302 )
    goto LABEL_71;
  v303 = self;
  if ( !*((_QWORD *)self + 23) )
    _objc_msgSend(v303, "initModule");
  objc_storeStrong((id *)v303 + 34, a3);
  _objc_msgSend(v303, "setTradeLoginParam:", v302);
  v4 = _objc_msgSend(v302, "strMacVersion");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( !v5 )
  {
    goto LABEL_9;
  }
  v6 = _objc_msgSend(v302, "strMacVersion");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = (__int64 *)v303;
  if ( !_objc_msgSend(v7, "length") )
  {
    goto LABEL_12;
  }
  v10 = _objc_msgSend(v302, "strMacVersion");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  if ( (unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("NULL")) )
  {
LABEL_9:
    v9 = (__int64 *)v303;
LABEL_12:
    sub_100D37DD0(v287, v9[23]);
    v16 = sub_100F9F630((__int64)v287);
    v17 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v16);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = (void *)v9[29];
    v9[29] = (__int64)v18;
    v20 = +[tools getTradeProductAndVersion](&OBJC_CLASS___tools, "getTradeProductAndVersion");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = (_BYTE *)sub_100F9F630((__int64)v287);
    v301 = "NSString2CString:";
    v23 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v21);
    sub_100D3FBE0(v22, "Version", (__int64)v23);
    sub_100C56008(v287);
    goto LABEL_13;
  }
  v13 = _objc_msgSend(v302, "strMacVersion");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  LOBYTE(v301) = (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", CFSTR("NUL"));
  v9 = (__int64 *)v303;
  if ( (_BYTE)v301 )
    goto LABEL_12;
  v259 = _objc_msgSend(v303, "getSessionDomain");
  v301 = "NSString2CString:";
  +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", CFSTR("Version"));
  v260 = _objc_msgSend(v302, "strMacVersion");
  v261 = objc_retainAutoreleasedReturnValue(v260);
  v262 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v261);
  sub_100D3FBE0(v259, v263, (__int64)v262);
  v9 = (__int64 *)v303;
LABEL_13:
  v299 = "getSessionDomain";
  v24 = _objc_msgSend(v9, "getSessionDomain");
  v25 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("UserName"));
  v26 = _objc_msgSend(v302, "strUserid");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v29 = _objc_msgSend(v28, v301, v27);
  sub_100D3FBE0(v24, v25, (__int64)v29);
  v30 = _objc_msgSend(v303, v299);
  v31 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("Account"));
  v32 = _objc_msgSend(v302, "strAccount");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v35 = _objc_msgSend(v34, v301, v33);
  sub_100D3FBE0(v30, v31, (__int64)v35);
  v36 = _objc_msgSend(v303, v299);
  v37 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("AccountType"));
  v38 = _objc_msgSend(v302, "strAccType");
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v41 = _objc_msgSend(v40, v301, v39);
  sub_100D3FBE0(v36, v37, (__int64)v41);
  v42 = _objc_msgSend(v302, "strTradePwd");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  v294 = 0LL;
  if ( (unsigned __int64)_objc_msgSend(v43, "length") < 0x10 )
  {
    v294 = 0LL;
  }
  else
  {
    v294 = 0LL;
    v44 = _objc_msgSend(v43, "substringToIndex:", 15LL);
    v45 = objc_retainAutoreleasedReturnValue(v44);
    v294 = 0LL;
    v46 = _objc_msgSend(v302, "strTradePwd");
    v294 = objc_retainAutoreleasedReturnValue(v46);
    v43 = v45;
  }
  v47 = _objc_msgSend(v303, v299);
  v48 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginTradePwd"));
  v49 = _objc_msgSend(&OBJC_CLASS___tools, v301, v43);
  sub_100D3FBE0(v47, v48, (__int64)v49);
  v50 = _objc_msgSend(v303, v299);
  v51 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginCommPwd"));
  v52 = _objc_msgSend(v302, "strCommPwd");
  v53 = objc_retainAutoreleasedReturnValue(v52);
  v291 = v43;
  v55 = _objc_msgSend(v54, v301, v53);
  sub_100D3FBE0(v50, v51, (__int64)v55);
  v56 = _objc_msgSend(v302, "strCrypt");
  v57 = objc_retainAutoreleasedReturnValue(v56);
  v58 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("crypt_name=%@\n"), v57);
  v300 = objc_retainAutoreleasedReturnValue(v58);
  v60 = _objc_msgSend(v302, v59);
  v61 = objc_retainAutoreleasedReturnValue(v60);
  LOBYTE(v50) = (unsigned __int8)_objc_msgSend(v61, "isEqualToString:", CFSTR("AC"));
  if ( (_BYTE)v50 )
  {
    v63 = _objc_msgSend(v302, v62);
    v64 = objc_retainAutoreleasedReturnValue(v63);
    v65 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("crypt_name=%@\nauthcode=1\n"), v64);
LABEL_18:
    v66 = objc_retainAutoreleasedReturnValue(v65);
    v300 = v66;
    goto LABEL_19;
  }
  v74 = _objc_msgSend(v302, v62);
  v75 = objc_retainAutoreleasedReturnValue(v74);
  v76 = (unsigned __int8)_objc_msgSend(v75, "isEqualToString:", CFSTR("HEXIN"));
  if ( v76 )
  {
    if ( (unsigned __int8)_objc_msgSend(v302, "bHTMode") )
    {
      v78 = _objc_msgSend(v303, v299);
      v79 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginCommPwd"));
      v80 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("888888"));
      sub_100D3FBE0(v78, v79, (__int64)v80);
      v81 = _objc_msgSend(v303, v299);
      v82 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginDynOps"));
      v83 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("1"));
      sub_100D3FBE0(v81, v82, (__int64)v83);
      v84 = _objc_msgSend(v303, v299);
      v85 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginDynPwd"));
      v86 = _objc_msgSend(v302, "strCommPwd");
      v87 = objc_retainAutoreleasedReturnValue(v86);
      v88 = _objc_msgSend(&OBJC_CLASS___tools, v301, v87);
      sub_100D3FBE0(v84, v85, (__int64)v88);
      v90 = _objc_msgSend(v302, v89);
      v64 = objc_retainAutoreleasedReturnValue(v90);
      v65 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("crypt_name=%@\nclient_protocol=1\n"), v64);
      goto LABEL_18;
    }
  }
  else
  {
    v243 = _objc_msgSend(v302, v77);
    v244 = objc_retainAutoreleasedReturnValue(v243);
    v245 = (unsigned __int8)_objc_msgSend(v244, "isEqualToString:", CFSTR("DN"));
    if ( v245 )
    {
      v247 = _objc_msgSend(v303, v299);
      v248 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginDynPwd"));
      v249 = _objc_msgSend(v302, "strCommPwd");
      v250 = objc_retainAutoreleasedReturnValue(v249);
      v251 = _objc_msgSend(&OBJC_CLASS___tools, v301, v250);
      sub_100D3FBE0(v247, v248, (__int64)v251);
      v253 = _objc_msgSend(v302, v252);
      objc_retainAutoreleasedReturnValue(v253);
      v254 = _objc_msgSend(v302, "strCommPwd");
      v255 = objc_retainAutoreleasedReturnValue(v254);
      v256 = v300;
      v258 = _objc_msgSend(
               &OBJC_CLASS___NSString,
               "stringWithFormat:",
               CFSTR("crypt_name=%@\nclient_protocol=2\ndynpwd=%@\n"),
               v257,
               v255);
    }
    else
    {
      v273 = _objc_msgSend(v302, v246);
      v274 = objc_retainAutoreleasedReturnValue(v273);
      v275 = (unsigned __int8)_objc_msgSend(v274, "isEqualToString:", CFSTR("DN2"));
      if ( !v275 )
        goto LABEL_19;
      v276 = _objc_msgSend(v303, v299);
      v277 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginDynPwd"));
      v278 = _objc_msgSend(v302, "strCommPwd");
      v279 = objc_retainAutoreleasedReturnValue(v278);
      v280 = _objc_msgSend(&OBJC_CLASS___tools, v301, v279);
      sub_100D3FBE0(v276, v277, (__int64)v280);
      v282 = _objc_msgSend(v302, v281);
      objc_retainAutoreleasedReturnValue(v282);
      v283 = _objc_msgSend(v302, "strCommPwd");
      v255 = objc_retainAutoreleasedReturnValue(v283);
      v256 = v300;
      v258 = _objc_msgSend(
               &OBJC_CLASS___NSString,
               "stringWithFormat:",
               CFSTR("crypt_name=%@\nclient_protocol=3\ndynpwd=%@\n"),
               v284,
               v255);
    }
    v285 = objc_retainAutoreleasedReturnValue(v258);
    v300 = v285;
  }
LABEL_19:
  v67 = _objc_msgSend(v302, "strMacLoginVersion");
  v68 = objc_retainAutoreleasedReturnValue(v67);
  if ( !v68 )
  {
    goto LABEL_28;
  }
  v69 = _objc_msgSend(v302, "strMacLoginVersion");
  v70 = objc_retainAutoreleasedReturnValue(v69);
  if ( !_objc_msgSend(v70, "length") )
    goto LABEL_27;
  v72 = _objc_msgSend(v302, "strMacLoginVersion");
  v73 = objc_retainAutoreleasedReturnValue(v72);
  if ( (unsigned __int8)_objc_msgSend(v73, "isEqualToString:", CFSTR("NULL")) )
  {
LABEL_27:
    goto LABEL_28;
  }
  v160 = _objc_msgSend(v302, "strMacLoginVersion");
  v161 = objc_retainAutoreleasedReturnValue(v160);
  LOBYTE(v296) = (unsigned __int8)_objc_msgSend(v161, "isEqualToString:", CFSTR("NUL"));
  if ( !(_BYTE)v296 )
  {
    v163 = _objc_msgSend(v302, "strMacLoginVersion");
    v164 = objc_retainAutoreleasedReturnValue(v163);
    v165 = v300;
    v167 = _objc_msgSend(v166, "stringWithFormat:", CFSTR("verinfo=%@\n%@"), v164, v300);
    v168 = objc_retainAutoreleasedReturnValue(v167);
    v300 = v168;
  }
LABEL_28:
  v91 = _objc_msgSend(v302, "strKernelver");
  v92 = objc_retainAutoreleasedReturnValue(v91);
  if ( !v92 )
  {
    goto LABEL_34;
  }
  v93 = _objc_msgSend(v302, "strKernelver");
  v94 = objc_retainAutoreleasedReturnValue(v93);
  if ( !_objc_msgSend(v94, "length") )
    goto LABEL_33;
  v96 = _objc_msgSend(v302, "strKernelver");
  v97 = objc_retainAutoreleasedReturnValue(v96);
  if ( (unsigned __int8)_objc_msgSend(v97, "isEqualToString:", CFSTR("NULL")) )
  {
LABEL_33:
    goto LABEL_34;
  }
  v169 = _objc_msgSend(v302, "strKernelver");
  v170 = objc_retainAutoreleasedReturnValue(v169);
  LOBYTE(v296) = (unsigned __int8)_objc_msgSend(v170, "isEqualToString:", CFSTR("NUL"));
  if ( !(_BYTE)v296 )
  {
    v172 = _objc_msgSend(v302, "strKernelver");
    v173 = objc_retainAutoreleasedReturnValue(v172);
    v174 = v300;
    v176 = _objc_msgSend(v175, "stringWithFormat:", CFSTR("kernelver=%@\n%@"), v173, v300);
    v177 = objc_retainAutoreleasedReturnValue(v176);
    v300 = v177;
  }
LABEL_34:
  v98 = +[HardwareInfoManager shareInstance](&OBJC_CLASS___HardwareInfoManager, "shareInstance");
  v296 = objc_retainAutoreleasedReturnValue(v98);
  v99 = _objc_msgSend(v296, "publicIP");
  objc_retainAutoreleasedReturnValue(v99);
  v100 = _objc_msgSend(v296, "cpuSerial");
  v101 = objc_retainAutoreleasedReturnValue(v100);
  v102 = v300;
  v104 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("publicip=%@\ncpuid=%@\nclientname=HexinStock\n%@"),
           v103,
           v101,
           v300);
  v105 = objc_retainAutoreleasedReturnValue(v104);
  v107 = _objc_msgSend(v303, v299);
  v108 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginSecond"));
  v109 = _objc_msgSend(&OBJC_CLASS___tools, v301, v105);
  sub_100D3FBE0(v107, v108, (__int64)v109);
  v110 = _objc_msgSend(v302, "strDefCommPWD");
  v295 = v105;
  v111 = objc_retainAutoreleasedReturnValue(v110);
  if ( !v111 )
  {
    goto LABEL_41;
  }
  v113 = _objc_msgSend(v302, v112);
  v114 = objc_retainAutoreleasedReturnValue(v113);
  if ( !_objc_msgSend(v114, "length") )
  {
    v119 = v114;
    goto LABEL_40;
  }
  v116 = _objc_msgSend(v302, v115);
  v117 = objc_retainAutoreleasedReturnValue(v116);
  if ( (unsigned __int8)_objc_msgSend(v117, "isEqualToString:", CFSTR("NULL")) )
  {
    v119 = v114;
LABEL_40:
    goto LABEL_41;
  }
  v178 = _objc_msgSend(v302, v118);
  v179 = objc_retainAutoreleasedReturnValue(v178);
  LOBYTE(v300) = (unsigned __int8)_objc_msgSend(v179, "isEqualToString:", CFSTR("NUL"));
  if ( !(_BYTE)v300 )
  {
    sub_100D37DD0(v287, *((_QWORD *)v303 + 23));
    v180 = (_BYTE *)sub_100F9F630((__int64)v287);
    v181 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("DefCommPwd"));
    v183 = _objc_msgSend(v302, v182);
    v184 = objc_retainAutoreleasedReturnValue(v183);
    v185 = _objc_msgSend(&OBJC_CLASS___tools, v301, v184);
    sub_100D3FBE0(v180, v181, (__int64)v185);
    sub_100C56008(v287);
  }
LABEL_41:
  v120 = _objc_msgSend(v303, v299);
  v121 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("LoginSecond"));
  v123 = _objc_msgSend(&OBJC_CLASS___tools, v301, v122);
  sub_100D3FBE0(v120, v121, (__int64)v123);
  v124 = v296;
  v125 = _objc_msgSend(v296, "localIP");
  objc_retainAutoreleasedReturnValue(v125);
  v126 = _objc_msgSend(v124, "pcName");
  v127 = objc_retainAutoreleasedReturnValue(v126);
  v128 = _objc_msgSend(v124, "cpuSerial");
  v300 = &OBJC_CLASS___NSString;
  v292 = objc_retainAutoreleasedReturnValue(v128);
  v129 = _objc_msgSend(v124, "publicIP");
  v130 = objc_retainAutoreleasedReturnValue(v129);
  v131 = v302;
  v298 = v130;
  v132 = _objc_msgSend(v302, "strUserid");
  v293 = objc_retainAutoreleasedReturnValue(v132);
  v133 = _objc_msgSend(v131, "strThsName");
  v288 = objc_retainAutoreleasedReturnValue(v133);
  v134 = _objc_msgSend(v131, "strUserid");
  v297 = v127;
  v136 = v135;
  objc_retainAutoreleasedReturnValue(v134);
  v137 = _objc_msgSend(v131, "strThsPwd");
  v138 = objc_retainAutoreleasedReturnValue(v137);
  v290 = v139;
  v140 = v293;
  v141 = v136;
  v289 = v136;
  v142 = v292;
  v143 = _objc_msgSend(
           v300,
           "stringWithFormat:",
           CFSTR("os=mac\nlocalip=%@\ncomputername=%@\ncpuid=%@\npublicip=%@\ndlzh=%@\nths_name=%@\nths_userid=%@\nths_pwd=%@\n"),
           v141,
           v297,
           v292,
           v298,
           v293,
           v288,
           v139,
           v138);
  v300 = objc_retainAutoreleasedReturnValue(v143);
  if ( v294 )
  {
    v145 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("gdmm=%@\n%@"), v294, v300);
    v146 = objc_retainAutoreleasedReturnValue(v145);
    v300 = v146;
  }
  v147 = _objc_msgSend(v303, v299);
  v148 = _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("AddParams"));
  v149 = _objc_msgSend(&OBJC_CLASS___tools, v301, v300);
  sub_100D3FBE0(v147, v148, (__int64)v149);
  v150 = _objc_msgSend(v302, "strYybid");
  v151 = objc_retainAutoreleasedReturnValue(v150);
  if ( !v151 )
  {
    v158 = 0LL;
LABEL_48:
LABEL_60:
    v189 = v299;
    goto LABEL_61;
  }
  v152 = _objc_msgSend(v302, "strYybid");
  v153 = objc_retainAutoreleasedReturnValue(v152);
  if ( !_objc_msgSend(v153, "length") )
  {
    v159 = v153;
LABEL_59:
    goto LABEL_60;
  }
  v154 = _objc_msgSend(v302, "strYybid");
  v155 = objc_retainAutoreleasedReturnValue(v154);
  if ( (unsigned __int8)_objc_msgSend(v155, "isEqualToString:", CFSTR("NULL")) )
  {
    v157 = v156;
LABEL_58:
    v159 = v153;
    goto LABEL_59;
  }
  v186 = _objc_msgSend(v302, "strYybid");
  v187 = objc_retainAutoreleasedReturnValue(v186);
  if ( (unsigned __int8)_objc_msgSend(v187, "isEqualToString:", CFSTR("NUL")) )
  {
    v157 = v188;
    goto LABEL_58;
  }
  v298 = &OBJC_CLASS___tools;
  v264 = _objc_msgSend(v302, "strYybid");
  v265 = objc_retainAutoreleasedReturnValue(v264);
  v266 = v298;
  v298 = v265;
  LOBYTE(v297) = (unsigned __int8)_objc_msgSend(v266, "isPureNumber:", v265);
  v189 = v299;
  if ( (_BYTE)v297 )
  {
    v268 = _objc_msgSend(v303, v299);
    _objc_msgSend(&OBJC_CLASS___tools, v301, CFSTR("DeptId"));
    v269 = _objc_msgSend(v302, "strYybid");
    v270 = objc_retainAutoreleasedReturnValue(v269);
    v271 = _objc_msgSend(&OBJC_CLASS___tools, v301, v270);
    sub_100D3FBE0(v268, v272, (__int64)v271);
    v158 = v270;
    goto LABEL_48;
  }
LABEL_61:
  v190 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v191 = objc_retainAutoreleasedReturnValue(v190);
  v192 = _objc_msgSend(v191, "GetBundle");
  v193 = objc_retainAutoreleasedReturnValue(v192);
  v194 = _objc_msgSend(v193, "pathForResource:ofType:", CFSTR("qsfeature"), CFSTR("txt"));
  v195 = objc_retainAutoreleasedReturnValue(v194);
  v292 = v195;
  if ( v195 && _objc_msgSend(v195, "length") )
  {
    v196 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithContentsOfFile:encoding:error:", v195, 1LL, 0LL);
    v197 = objc_retainAutoreleasedReturnValue(v196);
    v198 = v197;
    if ( v197 && _objc_msgSend(v197, "length") )
    {
      v200 = _objc_msgSend(v199, v189);
      v201 = _objc_msgSend(&OBJC_CLASS___tools, v301, v198);
      sub_100D3FBE0(v200, "QSFeatureCode", (__int64)v201);
    }
  }
  v202 = _objc_msgSend(v296, "harddiskSerialNum");
  v203 = objc_retainAutoreleasedReturnValue(v202);
  v204 = _objc_msgSend(v203, "length");
  if ( v204 )
  {
    v205 = _objc_msgSend(v296, "harddiskSerialNum");
    v206 = objc_retainAutoreleasedReturnValue(v205);
    v207 = +[tools getMd5String:](&OBJC_CLASS___tools, "getMd5String:", v206);
    v298 = objc_retainAutoreleasedReturnValue(v207);
  }
  else
  {
    v298 = &charsToLeaveEscaped;
  }
  v208 = _objc_msgSend(v296, "platformSerialNum");
  v297 = &OBJC_CLASS___NSString;
  v293 = objc_retainAutoreleasedReturnValue(v208);
  v209 = +[tools getMd5String:](&OBJC_CLASS___tools, "getMd5String:", v293);
  v210 = objc_retainAutoreleasedReturnValue(v209);
  v211 = v296;
  v212 = _objc_msgSend(v296, "macAddr");
  v213 = objc_retainAutoreleasedReturnValue(v212);
  v214 = _objc_msgSend(v211, "harddiskSerialNum");
  v215 = objc_retainAutoreleasedReturnValue(v214);
  v217 = _objc_msgSend(v211, v216);
  v218 = objc_retainAutoreleasedReturnValue(v217);
  v219 = _objc_msgSend(
           v297,
           "stringWithFormat:",
           CFSTR("hardwarecode=BIOS:%@,HDD:%@,MAC:%@,PI:%@,VOL:%@,VOLNAME:%@\nhddinfo=%@\nmacinfo=%@\n"),
           v210,
           v298,
           v213,
           &charsToLeaveEscaped,
           &charsToLeaveEscaped,
           &charsToLeaveEscaped,
           v215,
           v218);
  v297 = objc_retainAutoreleasedReturnValue(v219);
  v221 = (char *)v303;
  v222 = _objc_msgSend(v303, v299);
  v223 = _objc_msgSend(&OBJC_CLASS___tools, v301, v297);
  sub_100D3FBE0(v222, "HardwareInfo", (__int64)v223);
  sub_100D3A5E0((__int64)(v221 + 8), *((_QWORD *)v221 + 23), 0x40000u);
  *((_QWORD *)v221 + 5) = sub_100C2810C;
  *((_QWORD *)v221 + 6) = 0LL;
  *((_QWORD *)v221 + 4) = v221 + 104;
  sub_100D3A5E0((__int64)(v221 + 56), *((_QWORD *)v221 + 23), 0x100000u);
  *((_QWORD *)v221 + 11) = sub_100C2810C;
  *((_QWORD *)v221 + 12) = 0LL;
  *((_QWORD *)v221 + 10) = v221 + 104;
  sub_100D39A70((__int64)v287);
  sub_100D39D50((__int64)v287, 0x1000000, -16777216);
  sub_100F7A470((__int64)v287, "func_name", "connect");
  v224 = _objc_msgSend(*((id *)v221 + 30), "strUniqueQsid");
  v225 = objc_retainAutoreleasedReturnValue(v224);
  v226 = (unsigned int)_objc_msgSend(v225, "intValue");
  sub_100F7AC40((__int64)v287, "connect_connectmode", v226);
  v227 = _objc_msgSend(v302, "strLinkMode");
  v228 = objc_retainAutoreleasedReturnValue(v227);
  v229 = (char *)_objc_msgSend(&OBJC_CLASS___tools, v301, v228);
  sub_100F7A470((__int64)v287, "connect_linkmode", v229);
  v221[157] = 0;
  *(_WORD *)(v221 + 155) = 0;
  ++*((_DWORD *)v221 + 48);
  LODWORD(v225) = (unsigned int)_objc_msgSend(v221, "vuPEXDRmhuGmWuqM:", v287);
  v230 = _objc_msgSend(v302, "strUserid");
  v231 = objc_retainAutoreleasedReturnValue(v230);
  v232 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("tradeLogin userID:%@ RET:%u"),
           v231,
           (unsigned int)v225);
  v233 = objc_retainAutoreleasedReturnValue(v232);
  v301 = v233;
  +[tools AddTradeLog:logText:](&OBJC_CLASS___tools, "AddTradeLog:logText:", 1LL, v233);
  WeakRetained = objc_loadWeakRetained((id *)v221 + 28);
  v236 = _objc_msgSend(WeakRetained, "getUserLogInfo");
  v237 = objc_retainAutoreleasedReturnValue(v236);
  v238 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v237, v301);
  v239 = objc_retainAutoreleasedReturnValue(v238);
  v240 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
           "-[commumodule DJAwpxdGFxRzelWL:]",
           820LL,
           v239);
  v241 = objc_retainAutoreleasedReturnValue(v240);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v241);
  sub_100D39C90(v287);
LABEL_71:
}

//----- (0000000100C2D0E2) ----------------------------------------------------
void __cdecl -[commumodule tradeLogOut:](commumodule *self, SEL a2, id a3)
{
  _QWORD *v4; // rax
  unsigned int v6; // eax
  _QWORD *v8; // rax
  aNoeZQVOSoPboiub *v14; // rax
  aNoeZQVOSoPboiub *v18; // rax
  unsigned int v22; // eax
  _QWORD v23[10]; // [rsp+8h] [rbp-88h] BYREF

  v3 = objc_retain(a3);
  v4 = sub_100F84050(0x60u);
  sub_100D6D5C0(v4);
  if ( !v5 )
  {
LABEL_6:
    sub_100D39A70((__int64)v23);
    sub_100D39D50((__int64)v23, 0x1000000, -16777216);
    sub_100F7A470((__int64)v23, "func_name", "disconnect");
    sub_100F7A470((__int64)v23, "disconnect_closemode", "no_notify");
    v22 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
    sub_100F7AC40((__int64)v23, "connect_commid", v22);
    -[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v23);
    -[commumodule clearCommumodule](self, "clearCommumodule");
    sub_100D39C90(v23);
    goto LABEL_7;
  }
  sub_100D39D50(v5, 0x2000000, -16777216);
  if ( v3 )
  {
    v6 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
    v25 = v3;
    *(_DWORD *)(v7 + 88) = v6;
    v8 = sub_100D6D920(v7);
    v10 = (__int64)v8;
    if ( v8 )
    {
      sub_100D7B720((__int64)v8, "req_type", 0x40u);
      v11 = _objc_msgSend(v25, "strZjzh");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v12);
      sub_100D7B630(v10, "zjzh", v13);
      v14 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
      v24 = objc_retainAutoreleasedReturnValue(v14);
      v15 = _objc_msgSend(v24, "strAccount");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v17 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v16);
      sub_100D7B630(v10, "dlzh", v17);
      v18 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
      v24 = objc_retainAutoreleasedReturnValue(v18);
      v19 = _objc_msgSend(v24, "strUniqueQsid");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v20);
      sub_100D7B630(v10, "unique_id", v21);
    }
    v3 = v25;
    -[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v9);
    goto LABEL_6;
  }
LABEL_7:
}

//----- (0000000100C2D402) ----------------------------------------------------
void __cdecl -[commumodule reloadSetIsMoniUser](commumodule *self, SEL a2)
{
  UserModel *v2; // rax
  UserModel *v3; // rbx
  UserModel *v6; // rax
  UserModel *v7; // rbx

  v2 = -[commumodule aUserModel](self, "aUserModel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[UserModel isMoniUser](v3, "isMoniUser");
  if ( v5 != v4 )
  {
    v6 = -[commumodule aUserModel](self, "aUserModel");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    *((_BYTE *)self + 152) = (unsigned __int8)-[UserModel isMoniUser](v7, "isMoniUser");
  }
}

//----- (0000000100C2D4BC) ----------------------------------------------------
void __cdecl -[commumodule reloadSetIsMeiguUser](commumodule *self, SEL a2)
{
  UserModel *v2; // rax
  UserModel *v3; // rbx
  UserModel *v6; // rax
  UserModel *v7; // rbx

  v2 = -[commumodule aUserModel](self, "aUserModel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[UserModel isMeiguUser](v3, "isMeiguUser");
  if ( v5 != v4 )
  {
    v6 = -[commumodule aUserModel](self, "aUserModel");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    *((_BYTE *)self + 153) = (unsigned __int8)-[UserModel isMeiguUser](v7, "isMeiguUser");
  }
}

//----- (0000000100C2D576) ----------------------------------------------------
void __cdecl -[commumodule reloadSetIsZyyUser](commumodule *self, SEL a2)
{
  UserModel *v2; // rax
  UserModel *v3; // rbx
  UserModel *v6; // rax
  UserModel *v7; // rbx

  v2 = -[commumodule aUserModel](self, "aUserModel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[UserModel isZyyUser](v3, "isZyyUser");
  if ( v5 != v4 )
  {
    v6 = -[commumodule aUserModel](self, "aUserModel");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    *((_BYTE *)self + 154) = (unsigned __int8)-[UserModel isZyyUser](v7, "isZyyUser");
  }
}

//----- (0000000100C2D630) ----------------------------------------------------
void __cdecl -[commumodule clearCommumodule](commumodule *self, SEL a2)
{
  NSMutableDictionary *v2; // rax
  NSMutableDictionary *v3; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12

  v2 = -[commumodule dicContext2Insid](self, "dicContext2Insid");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "removeAllObjects");
  v6 = v5(self, "dicInsID2ReqDelegate");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "removeAllObjects");
  v10 = v9(self, "dicNeedPush");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12(v11, "removeAllObjects");
  v13(self, "setDwSessionId:", 0LL);
  v15 = v14(self, "tradeLoginParam");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17(v16, "reseTradeLoginModel");
  -[commumodule setAUserModel:](self, "setAUserModel:", 0LL);
  sub_100D3A430((__int64)self + 8);
  sub_100D3A430((__int64)self + 56);
  *((_QWORD *)self + 23) = 0LL;
  *((_BYTE *)self + 197) = 0;
}

//----- (0000000100C2D75E) ----------------------------------------------------
void *__cdecl -[commumodule getModule](commumodule *self, SEL a2)
{
  return (void *)*((_QWORD *)self + 23);
}

//----- (0000000100C2D76B) ----------------------------------------------------
char *__cdecl -[commumodule getSessionDomain](commumodule *self, SEL a2)
{
  SEL v6; // r12

  v11[0] = v2;
  if ( !*((_QWORD *)self + 30) )
    return 0LL;
  v3 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", *((_QWORD *)self + 29));
  v4 = _objc_msgSend(*((id *)self + 30), "strBroker");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = (const char *)_objc_msgSend(&OBJC_CLASS___tools, v6, v5);
  sub_100F9EDE0(v11);
  sub_100FA0D10(v11, "%s_%s", v3, v7);
  v8 = (char *)sub_100F9F630((__int64)v11);
  sub_100C56008(v11);
  return v8;
}

//----- (0000000100C2D85A) ----------------------------------------------------
id __cdecl -[commumodule getSessionDomainNs](commumodule *self, SEL a2)
{

  if ( !*((_QWORD *)self + 30) )
    return objc_autoreleaseReturnValue(0LL);
  v2 = -[commumodule getSessionDomain](self, "getSessionDomain");
  v3 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v2);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100C2D8AB) ----------------------------------------------------
int __cdecl -[commumodule getSessionStatus](commumodule *self, SEL a2)
{
  unsigned int v2; // eax
  int v4; // ebx
  _QWORD v6[10]; // [rsp+8h] [rbp-68h] BYREF
  volatile signed __int32 *v7[3]; // [rsp+58h] [rbp-18h] BYREF

  sub_100D39A70((__int64)v6);
  sub_100D39D50((__int64)v6, 0x1000000, -16777216);
  sub_100F7A470((__int64)v6, "func_name", "get_session_status");
  v2 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
  sub_100F7AC40((__int64)v6, "sessionid", v2);
  -[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v6);
  sub_100F7A7F0(v7, (__int64)v6, "session_status");
  v3 = (const char *)sub_100F9F630((__int64)v7);
  v4 = atoi(v3);
  sub_100C56008(v7);
  sub_100D39C90(v6);
  return v4;
}

//----- (0000000100C2D98C) ----------------------------------------------------
id __cdecl -[commumodule getSessionIpAndPort](commumodule *self, SEL a2)
{
  unsigned int v2; // eax
  _QWORD v7[10]; // [rsp+8h] [rbp-88h] BYREF
  volatile signed __int32 *v8; // [rsp+58h] [rbp-38h] BYREF
  volatile signed __int32 *v9; // [rsp+60h] [rbp-30h] BYREF
  volatile signed __int32 *v10; // [rsp+68h] [rbp-28h] BYREF
  volatile signed __int32 *v11; // [rsp+70h] [rbp-20h] BYREF
  volatile signed __int32 *v12[3]; // [rsp+78h] [rbp-18h] BYREF

  sub_100D39A70((__int64)v7);
  sub_100D39D50((__int64)v7, 0x1000000, -16777216);
  sub_100F7A470((__int64)v7, "func_name", "get_session_info");
  v2 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
  sub_100F7AC40((__int64)v7, "sessionid", v2);
  -[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v7);
  sub_100F7A7F0(v12, (__int64)v7, "session_ip");
  sub_100F7A7F0(&v8, (__int64)v7, "server_typename");
  sub_100F7A7F0(&v9, (__int64)v7, "session_name");
  sub_100F7A7F0(&v10, (__int64)v7, "session_retname");
  sub_100F7A7F0(&v11, (__int64)v7, "server_typeid");
  v3 = sub_100F9F630((__int64)v12);
  v4 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  sub_100C56008(&v11);
  sub_100C56008(&v10);
  sub_100C56008(&v9);
  sub_100C56008(&v8);
  sub_100C56008(v12);
  sub_100D39C90(v7);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C2DB60) ----------------------------------------------------
id __cdecl -[commumodule getLoginAccount](commumodule *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx
  aNoeZQVOSoPboiub *v4; // rax
  aNoeZQVOSoPboiub *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
    return objc_autoreleaseReturnValue(&charsToLeaveEscaped);
  v4 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[aNoeZQVOSoPboiub strAccount](v5, "strAccount");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C2DBFF) ----------------------------------------------------
id __cdecl -[commumodule getLoginBroker](commumodule *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx
  aNoeZQVOSoPboiub *v4; // rax
  aNoeZQVOSoPboiub *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
    return objc_autoreleaseReturnValue(&charsToLeaveEscaped);
  v4 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[aNoeZQVOSoPboiub strBroker](v5, "strBroker");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C2DC9E) ----------------------------------------------------
id __cdecl -[commumodule getUniqueQsid](commumodule *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx
  aNoeZQVOSoPboiub *v4; // rax
  aNoeZQVOSoPboiub *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
    return objc_autoreleaseReturnValue(&charsToLeaveEscaped);
  v4 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[aNoeZQVOSoPboiub strUniqueQsid](v5, "strUniqueQsid");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C2DD3D) ----------------------------------------------------
id __cdecl -[commumodule getUserid](commumodule *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx
  aNoeZQVOSoPboiub *v4; // rax
  aNoeZQVOSoPboiub *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
    return objc_autoreleaseReturnValue(&charsToLeaveEscaped);
  v4 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[aNoeZQVOSoPboiub strUserid](v5, "strUserid");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C2DDDC) ----------------------------------------------------
id __cdecl -[commumodule getTradePassword](commumodule *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx
  aNoeZQVOSoPboiub *v4; // rax
  aNoeZQVOSoPboiub *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
    return objc_autoreleaseReturnValue(&charsToLeaveEscaped);
  v4 = -[commumodule tradeLoginParam](self, "tradeLoginParam");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[aNoeZQVOSoPboiub strTradePwd](v5, "strTradePwd");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C2DE7B) ----------------------------------------------------
char __cdecl -[commumodule isDisconnected](commumodule *self, SEL a2)
{
  NSString *v4; // rax
  NSString *v5; // rax
  NSString *v6; // rax
  NSString *v7; // r13
  NSNumber *v10; // rax
  NSNumber *v11; // rax
  NSNumber *v12; // r14
  NSString *v14; // rax
  NSString *v15; // r15
  NSString *v16; // rax
  NSNumber *v18; // [rsp+10h] [rbp-40h]
  id WeakRetained; // [rsp+20h] [rbp-30h]

  if ( (unsigned __int8)-[commumodule isLostSocketConnection](self, "isLostSocketConnection") )
  {
    WeakRetained = objc_loadWeakRetained((id *)self + 28);
    v2 = _objc_msgSend(WeakRetained, "getUserLogInfo");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@网络异常"), v3);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
           "-[commumodule isDisconnected]",
           1009LL,
           v5);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v7);
  }
  else
  {
    if ( !*((_BYTE *)self + 158) && *((_BYTE *)self + 155) )
      return 0;
    v21 = objc_loadWeakRetained((id *)self + 28);
    v9 = _objc_msgSend(v21, "getUserLogInfo");
    v19 = objc_retainAutoreleasedReturnValue(v9);
    v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", (unsigned int)*((char *)self + 158));
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", (unsigned int)*((char *)self + 155));
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@-%@--%@"), v19, v13, v12);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
            "-[commumodule isDisconnected]",
            1019LL,
            v15);
    v7 = objc_retainAutoreleasedReturnValue(v16);
    +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v7);
  }
  return 1;
}

//----- (0000000100C2E13B) ----------------------------------------------------
void __cdecl -[commumodule Disconnect](commumodule *self, SEL a2)
{
  NSString *v2; // rax
  id WeakRetained; // rax
  NSString *v6; // rax
  NSString *v7; // r13
  NSString *v8; // rax
  NSString *v9; // r14
  unsigned int v12; // eax

  v2 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("trade Disconnect. "));
  v14 = objc_retainAutoreleasedReturnValue(v2);
  WeakRetained = objc_loadWeakRetained((id *)self + 28);
  v4 = _objc_msgSend(WeakRetained, "getUserLogInfo");
  v13[10] = self;
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@trade Disconnect"), v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
         "-[commumodule Disconnect]",
         1027LL,
         v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v9);
  v11 = v14;
  +[tools AddTradeLog:logText:](&OBJC_CLASS___tools, "AddTradeLog:logText:", 1LL, v14);
  sub_100D39A70((__int64)v13);
  sub_100D39D50((__int64)v13, 0x1000000, -16777216);
  sub_100F7A470((__int64)v13, "func_name", "disconnect");
  sub_100F7A470((__int64)v13, "disconnect_closemode", "no_notify");
  if ( (unsigned int)-[commumodule dwSessionId](self, "dwSessionId") )
  {
    v12 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
    sub_100F7AC40((__int64)v13, "sessionid", v12);
  }
  -[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v13);
  if ( !(unsigned int)-[commumodule dwSessionId](self, "dwSessionId") )
    -[commumodule clearCommumodule](self, "clearCommumodule");
  sub_100D39C90(v13);
}

//----- (0000000100C2E3A4) ----------------------------------------------------
int __cdecl -[commumodule reconnectSession](commumodule *self, SEL a2)
{
  id WeakRetained; // rax
  NSString *v5; // rax
  NSString *v6; // r13
  NSString *v7; // rax
  NSString *v8; // r15
  int v10; // ebx
  int v11; // eax
  unsigned int v12; // eax
  unsigned int v14; // r14d
  NSString *v16; // rax
  NSString *v17; // r13
  NSString *v18; // rax
  NSString *v19; // r15
  SEL v20; // r12
  _QWORD v22[10]; // [rsp+0h] [rbp-90h] BYREF
  volatile signed __int32 *v23; // [rsp+50h] [rbp-40h] BYREF
  id *location; // [rsp+60h] [rbp-30h]

  location = (id *)((char *)self + 224);
  WeakRetained = objc_loadWeakRetained((id *)self + 28);
  v3 = _objc_msgSend(WeakRetained, "getUserInfo");
  v24 = self;
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@-重新连接请求"), v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
         "-[commumodule reconnectSession]",
         1049LL,
         v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v8);
  v10 = 0;
  if ( CFAbsoluteTimeGetCurrent() - *((double *)self + 35) >= 0.1 )
  {
    v11 = (unsigned int)_objc_msgSend(v24, "getSessionStatus");
    if ( v11 >= 0 && v11 != 2 )
    {
      sub_100D39A70((__int64)v22);
      sub_100D39D50((__int64)v22, 0x1000000, -16777216);
      sub_100F7A470((__int64)v22, "func_name", "reconnect_session");
      v12 = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
      sub_100F7AC40((__int64)v22, "sessionid", v12);
      if ( (int)-[commumodule vuPEXDRmhuGmWuqM:](self, "vuPEXDRmhuGmWuqM:", v22) >= 0 )
        *((CFAbsoluteTime *)self + 35) = CFAbsoluteTimeGetCurrent();
      sub_100F7A7F0(&v23, (__int64)v22, "retcode");
      v13 = (const char *)sub_100F9F630((__int64)&v23);
      v14 = atoi(v13);
      v10 = 0;
      if ( v14 + 3 >= 2 )
      {
        v24 = objc_loadWeakRetained(location);
        v15 = _objc_msgSend(v24, "getUserInfo");
        location = (id *)objc_retainAutoreleasedReturnValue(v15);
        v16 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@-%d"), location, v14);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                "-[commumodule reconnectSession]",
                1083LL,
                v17);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        _objc_msgSend(&OBJC_CLASS___tools, v20, v19);
        v10 = v14;
      }
      sub_100C56008(&v23);
      sub_100D39C90(v22);
    }
  }
  return v10;
}

//----- (0000000100C2E707) ----------------------------------------------------
void __cdecl -[commumodule kQvYehFExlWUHfgW:](commumodule *self, SEL a2, id a3)
{
  _DWORD *v5; // rbx
  unsigned int v6; // ebx
  NSString *v11; // rax
  NSString *v12; // rbx
  NSString *v16; // rax
  NSString *v17; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    v5 = _objc_msgSend(v3, "getTradeRequestPacket");
    sub_100D39D50((__int64)v5, 0x2000000, -16777216);
    sub_100D39DA0((__int64)v5, 1, 1);
    if ( !(unsigned __int8)-[commumodule isDisconnected](self, "isDisconnected") )
    {
      v5[22] = (unsigned int)-[commumodule dwSessionId](self, "dwSessionId");
      v6 = sub_100D3A690((__int64)self + 56, (__int64)v5);
      if ( v6 != -1 )
      {
        v7 = (void *)*((_QWORD *)self + 32);
        if ( v7 )
        {
          v8 = _objc_msgSend(v4, "nsInsid");
          v10 = _objc_msgSend(v9, "stringWithFormat:", CFSTR("%ld"), v8);
          objc_retainAutoreleasedReturnValue(v10);
          v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%u"), v6);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          _objc_msgSend(v7, "setObject:forKey:", v13, v12);
        }
        if ( *((_QWORD *)self + 31) )
        {
          v15 = _objc_msgSend(v4, "nsInsid");
          v16 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = _objc_msgSend(v4, "delegate");
          v19 = objc_retainAutoreleasedReturnValue(v18);
          _objc_msgSend(v20, "setObject:forKeyedSubscript:", v19, v17);
          v21(v17);
        }
        if ( (unsigned __int8)_objc_msgSend(v4, "bNeedPush") )
        {
          v22 = _objc_msgSend(v4, "getReqTypeString");
          v23 = objc_retainAutoreleasedReturnValue(v22);
          v24 = _objc_msgSend(v4, "nsInsid");
          -[commumodule registerPushType:InstanceId:](self, "registerPushType:InstanceId:", v23, v24);
        }
      }
    }
  }
}

//----- (0000000100C2E9A2) ----------------------------------------------------
void __cdecl -[commumodule registerPushType:InstanceId:](commumodule *self, SEL a2, id a3, signed __int64 a4)
{
  NSString *v7; // rax
  NSString *v8; // r14

  v5 = objc_retain(a3);
  if ( *(_QWORD *)(v6 + 264) )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), a4);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = _objc_msgSend(*(id *)(v9 + 264), "objectForKey:", v5);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    if ( v11 )
    {
      v12 = v11;
      if ( !(unsigned __int8)_objc_msgSend(v11, "containsObject:", v8) )
        _objc_msgSend(v12, "addObject:", v8);
    }
    else
    {
      v14 = objc_alloc(&OBJC_CLASS___NSMutableArray);
      v12 = _objc_msgSend(v14, "init");
      _objc_msgSend(v12, "addObject:", v8);
    }
    _objc_msgSend(*(id *)(v13 + 264), "setObject:forKey:", v12, v5);
    v15(v8);
  }
}

//----- (0000000100C2EAF2) ----------------------------------------------------
id __cdecl -[commumodule GetReceiveDelegateArray:](commumodule *self, SEL a2, signed __int64 a3)
{
  NSString *v3; // rax
  NSString *v4; // r13
  bool v8; // zf
  SEL v26; // [rsp+48h] [rbp-D8h]
  id obj; // [rsp+60h] [rbp-C0h]

  if ( !*((_QWORD *)self + 33) )
    goto LABEL_17;
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(*(id *)(v5 + 264), "objectForKey:", v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( !v7 || (v27 = v7, v8 = _objc_msgSend(v7, "count") == 0LL, v7 = v27, v8) )
  {
LABEL_17:
    v30 = 0LL;
    return objc_autoreleaseReturnValue(v30);
  }
  v28 = v4;
  v9 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v30 = _objc_msgSend(v9, "init");
  v25 = 0LL;
  v24 = 0LL;
  v23 = 0LL;
  v22 = 0LL;
  obj = objc_retain(v27);
  v11 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v31, 16LL);
  if ( v11 )
  {
    v12 = *(_QWORD *)v23;
    do
    {
      v26 = "addObject:";
      if ( !v11 )
        v11 = 1LL;
      for ( i = 0LL; i != v11; ++i )
      {
        if ( *(_QWORD *)v23 != v12 )
          objc_enumerationMutation(obj);
        v14 = *(void **)(v10 + 248);
        if ( v14 )
        {
          v15 = _objc_msgSend(v14, "objectForKey:", *(_QWORD *)(*((_QWORD *)&v22 + 1) + 8 * i));
          v17 = v16;
          v18 = objc_retainAutoreleasedReturnValue(v15);
          _objc_msgSend(v30, v26, v18);
          v10 = v17;
        }
      }
      v11 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v31, 16LL);
    }
    while ( v11 );
  }
  v20 = obj;
  return objc_autoreleaseReturnValue(v30);
}

//----- (0000000100C2EDD4) ----------------------------------------------------
id __cdecl -[commumodule getRspDelegateByContextId:](commumodule *self, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  v5 = v4;
  v6 = (void *)*((_QWORD *)self + 32);
  if ( v6 )
  {
    v7 = _objc_msgSend(v6, "objectForKey:", v4);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = v8;
    v10 = 0LL;
    if ( v8 )
    {
      v11 = (void *)*((_QWORD *)self + 31);
      if ( v11 )
      {
        v12 = _objc_msgSend(v11, "objectForKey:", v8);
        v10 = objc_retainAutoreleasedReturnValue(v12);
      }
    }
  }
  else
  {
    v9 = 0LL;
    v10 = 0LL;
  }
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100C2EE96) ----------------------------------------------------
char __cdecl -[commumodule LOWNqsWstlJNohYU](commumodule *self, SEL a2)
{
  SEL v6; // r12
  aNoeZQVOSoPboiub *v8; // rax
  aNoeZQVOSoPboiub *v9; // r14

  if ( *((_BYTE *)self + 197) )
    return 0;
  v3 = -[commumodule getSessionDomain](self, "getSessionDomain");
  v4 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", CFSTR("T"));
  sub_100D3FBE0(v3, "LoginExtend", (__int64)v4);
  v5 = -[commumodule getSessionDomain](self, "getSessionDomain");
  v7 = _objc_msgSend(&OBJC_CLASS___tools, v6, CFSTR("htbh=HXKH"));
  sub_100D3FBE0(v5, "OtherParams", (__int64)v7);
  v8 = -[commumodule loginModel](self, "loginModel");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[aNoeZQVOSoPboiub setBIsAutoReg:](v9, "setBIsAutoReg:", 1LL);
  -[commumodule DJAwpxdGFxRzelWL:](self, "DJAwpxdGFxRzelWL:", v9);
  *((_BYTE *)self + 197) = 1;
  return 1;
}

//----- (0000000100C2EFA2) ----------------------------------------------------
unsigned int __cdecl -[commumodule vuPEXDRmhuGmWuqM:](commumodule *self, SEL a2, void *a3)
{
  if ( *((_DWORD *)self + 18) )
    return sub_100D3A690((__int64)self + 56, (__int64)a3);
  else
    return -1;
}

//----- (0000000100C2EFC0) ----------------------------------------------------
char __cdecl -[commumodule isKilledByServer](commumodule *self, SEL a2)
{
  return *((_BYTE *)self + 159);
}

//----- (0000000100C2EFCD) ----------------------------------------------------
TradeResponceDispatch *__cdecl -[commumodule defRspHandleDelegate](commumodule *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)self + 26);
  return (TradeResponceDispatch *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100C2EFE6) ----------------------------------------------------
void __cdecl -[commumodule setDefRspHandleDelegate:](commumodule *self, SEL a2, id a3)
{
  objc_storeWeak((id *)self + 26, a3);
}

//----- (0000000100C2EFFA) ----------------------------------------------------
char __cdecl -[commumodule isLostSocketConnection](commumodule *self, SEL a2)
{
  return *((_BYTE *)self + 196);
}

//----- (0000000100C2F007) ----------------------------------------------------
id __cdecl -[commumodule LoginStatusFun](commumodule *self, SEL a2)
{
  return objc_getProperty(self, a2, 216LL, 0);
}

//----- (0000000100C2F018) ----------------------------------------------------
void __cdecl -[commumodule setLoginStatusFun:](commumodule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 216LL);
}

//----- (0000000100C2F027) ----------------------------------------------------
char __cdecl -[commumodule autoReg](commumodule *self, SEL a2)
{
  return *((_BYTE *)self + 197);
}

//----- (0000000100C2F034) ----------------------------------------------------
void __cdecl -[commumodule setAutoReg:](commumodule *self, SEL a2, char a3)
{
  *((_BYTE *)self + 197) = a3;
}

//----- (0000000100C2F040) ----------------------------------------------------
UserModel *__cdecl -[commumodule aUserModel](commumodule *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)self + 28);
  return (UserModel *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100C2F059) ----------------------------------------------------
void __cdecl -[commumodule setAUserModel:](commumodule *self, SEL a2, id a3)
{
  objc_storeWeak((id *)self + 28, a3);
}

//----- (0000000100C2F06D) ----------------------------------------------------
NSString *__cdecl -[commumodule strDomain](commumodule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 232LL, 0);
}

//----- (0000000100C2F07E) ----------------------------------------------------
void __cdecl -[commumodule setStrDomain:](commumodule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 232LL);
}

//----- (0000000100C2F08D) ----------------------------------------------------
aNoeZQVOSoPboiub *__cdecl -[commumodule tradeLoginParam](commumodule *self, SEL a2)
{
  return (aNoeZQVOSoPboiub *)*((_QWORD *)self + 30);
}

//----- (0000000100C2F09A) ----------------------------------------------------
void __cdecl -[commumodule setTradeLoginParam:](commumodule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)self + 30, a3);
}

//----- (0000000100C2F0AE) ----------------------------------------------------
NSMutableDictionary *__cdecl -[commumodule dicInsID2ReqDelegate](commumodule *self, SEL a2)
{
  return (NSMutableDictionary *)*((_QWORD *)self + 31);
}

//----- (0000000100C2F0BB) ----------------------------------------------------
void __cdecl -[commumodule setDicInsID2ReqDelegate:](commumodule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)self + 31, a3);
}

//----- (0000000100C2F0CF) ----------------------------------------------------
NSMutableDictionary *__cdecl -[commumodule dicContext2Insid](commumodule *self, SEL a2)
{
  return (NSMutableDictionary *)*((_QWORD *)self + 32);
}

//----- (0000000100C2F0DC) ----------------------------------------------------
void __cdecl -[commumodule setDicContext2Insid:](commumodule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)self + 32, a3);
}

//----- (0000000100C2F0F0) ----------------------------------------------------
NSMutableDictionary *__cdecl -[commumodule dicNeedPush](commumodule *self, SEL a2)
{
  return (NSMutableDictionary *)*((_QWORD *)self + 33);
}

//----- (0000000100C2F0FD) ----------------------------------------------------
void __cdecl -[commumodule setDicNeedPush:](commumodule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)self + 33, a3);
}

//----- (0000000100C2F111) ----------------------------------------------------
unsigned int __cdecl -[commumodule dwSessionId](commumodule *self, SEL a2)
{
  return *((_DWORD *)self + 50);
}

//----- (0000000100C2F11D) ----------------------------------------------------
void __cdecl -[commumodule setDwSessionId:](commumodule *self, SEL a2, unsigned int a3)
{
  *((_DWORD *)self + 50) = a3;
}

//----- (0000000100C2F129) ----------------------------------------------------
aNoeZQVOSoPboiub *__cdecl -[commumodule loginModel](commumodule *self, SEL a2)
{
  return (aNoeZQVOSoPboiub *)objc_getProperty(self, a2, 272LL, 0);
}

//----- (0000000100C2F13A) ----------------------------------------------------
void __cdecl -[commumodule setLoginModel:](commumodule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 272LL);
}

//----- (0000000100C2F149) ----------------------------------------------------
double __cdecl -[commumodule lastReconnect](commumodule *self, SEL a2)
{
  return *((double *)self + 35);
}

//----- (0000000100C2F157) ----------------------------------------------------
void __cdecl -[commumodule setLastReconnect:](commumodule *self, SEL a2, double a3)
{
  *((double *)self + 35) = a3;
}

//----- (0000000100C2F165) ----------------------------------------------------
void __cdecl -[commumodule .cxx_destruct](commumodule *self, SEL a2)
{
  objc_storeStrong((id *)self + 34, 0LL);
  objc_storeStrong((id *)self + 33, 0LL);
  objc_storeStrong((id *)self + 32, 0LL);
  objc_storeStrong((id *)self + 31, 0LL);
  objc_storeStrong((id *)self + 30, 0LL);
  objc_storeStrong((id *)self + 29, 0LL);
  objc_destroyWeak((id *)self + 28);
  objc_storeStrong((id *)self + 27, 0LL);
  objc_destroyWeak((id *)self + 26);
  sub_100D3A460((__int64)self + 56);
  sub_100D3A460((__int64)self + 8);
}

//----- (0000000100C2F23C) ----------------------------------------------------
id __cdecl -[commumodule .cxx_construct](commumodule *self, SEL a2)
{
  sub_100D3A380((_QWORD *)self + 1);
  sub_100D3A380((_QWORD *)self + 7);
  *((_DWORD *)self + 40) = 0;
  *(_OWORD *)((char *)self + 104) = 0LL;
  *(_OWORD *)((char *)self + 120) = 0LL;
  *(_OWORD *)((char *)self + 136) = 0LL;
  *((_BYTE *)self + 164) = 1;
  *(_WORD *)((char *)self + 155) = 0;
  *((_BYTE *)self + 157) = 0;
  *((_BYTE *)self + 159) = 0;
  return self;
}

