TradeStallsShiDangQuoteItem *__cdecl -[TradeStallsShiDangQuoteItem init](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  TradeStallsShiDangQuoteItem *v2; // rax
  TradeStallsShiDangQuoteItem *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___TradeStallsShiDangQuoteItem;
  v2 = -[QuoteBaseItem init](&v5, "init");
  v3 = v2;
  if ( v2 )
    -[TradeStallsShiDangQuoteItem initializeAGuTradeStallsValues](v2, "initializeAGuTradeStallsValues");
  return v3;
}

//----- (00000001007F8FDF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem dealloc](TradeStallsShiDangQuoteItem *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___TradeStallsShiDangQuoteItem;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (00000001007F9054) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem initializeAGuTradeStallsValues](TradeStallsShiDangQuoteItem *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___TradeStallsShiDangQuoteItem;
  -[QuoteBaseItem initializeBaseValues](&v2, "initializeBaseValues");
  -[TradeStallsShiDangQuoteItem setWeiBi:](self, "setWeiBi:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setWeiBiBaiFeiBi:](self, "setWeiBiBaiFeiBi:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyOne:](self, "setBuyOne:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyTwo:](self, "setBuyTwo:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyThree:](self, "setBuyThree:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyFour:](self, "setBuyFour:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyFive:](self, "setBuyFive:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyOneVol:](self, "setBuyOneVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyTwoVol:](self, "setBuyTwoVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyThreeVol:](self, "setBuyThreeVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyFourVol:](self, "setBuyFourVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyFiveVol:](self, "setBuyFiveVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuySix:](self, "setBuySix:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuySeven:](self, "setBuySeven:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyEight:](self, "setBuyEight:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyNine:](self, "setBuyNine:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyTen:](self, "setBuyTen:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuySixVol:](self, "setBuySixVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuySevenVol:](self, "setBuySevenVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyEightVol:](self, "setBuyEightVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyNineVol:](self, "setBuyNineVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setBuyTenVol:](self, "setBuyTenVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleOne:](self, "setSaleOne:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleTwo:](self, "setSaleTwo:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleThree:](self, "setSaleThree:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleFour:](self, "setSaleFour:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleFive:](self, "setSaleFive:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleOneVol:](self, "setSaleOneVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleTwoVol:](self, "setSaleTwoVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleThreeVol:](self, "setSaleThreeVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleFourVol:](self, "setSaleFourVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleFiveVol:](self, "setSaleFiveVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleSix:](self, "setSaleSix:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleSeven:](self, "setSaleSeven:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleEight:](self, "setSaleEight:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleNine:](self, "setSaleNine:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleTen:](self, "setSaleTen:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleSixVol:](self, "setSaleSixVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleSevenVol:](self, "setSaleSevenVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleEightVol:](self, "setSaleEightVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleNineVol:](self, "setSaleNineVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setSaleTenVol:](self, "setSaleTenVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setAllBuyVol:](self, "setAllBuyVol:", 4294967295.0);
  -[TradeStallsShiDangQuoteItem setAllSaleVol:](self, "setAllSaleVol:", 4294967295.0);
}

//----- (00000001007F942C) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setCanDisPlayMoney:](TradeStallsShiDangQuoteItem *self, SEL a2, char a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  self->_canDisPlayMoney = a3;
  if ( a3 )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    self->_disPlayMoney = (unsigned __int8)v5(v4, "boolForKey:", CFSTR("TradeDisplayMoney"));
    v7 = v6(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v8, "addObserver:selector:name:object:", self, "switchNumberOrMoneyState:", CFSTR("TradeDisplayMoney"), 0LL);
  }
  else
  {
    self->_disPlayMoney = 0;
    v10 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "removeObserver:", self);
  }
}

//----- (00000001007F9525) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem switchNumberOrMoneyState:](TradeStallsShiDangQuoteItem *self, SEL a2, id a3)
{

  v3 = _objc_msgSend(a3, "userInfo");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "allKeys");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "firstObject");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  self->_disPlayMoney = (unsigned __int8)_objc_msgSend(v8, "boolValue");
  reloadDataBlock = (void (**)(void))self->_reloadDataBlock;
  if ( reloadDataBlock )
    reloadDataBlock[2]();
}

//----- (00000001007F95EB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setConstantProperties:](TradeStallsShiDangQuoteItem *self, SEL a2, id a3)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___TradeStallsShiDangQuoteItem;
  -[QuoteBaseItem setConstantProperties:](&v3, "setConstantProperties:", a3);
}

//----- (00000001007F961A) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem resetAGuValues:](TradeStallsShiDangQuoteItem *self, SEL a2, id a3)
{
  NSNumber *v5; // rax
  NSNumber *v6; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // r14
  NSNumber *v16; // rdi
  NSNumber *v20; // rax
  NSNumber *v21; // r13
  SEL v25; // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  NSNumber *v44; // rax
  NSNumber *v45; // rax
  NSNumber *v51; // rax
  NSNumber *v52; // r13
  NSNumber *v56; // rax
  NSNumber *v57; // r13
  SEL v61; // r12
  id (*v62)(id, SEL, ...); // r12
  id (*v65)(id, SEL, ...); // r12
  id (*v70)(id, SEL, ...); // r12
  id (*v73)(id, SEL, ...); // r12
  id (*v78)(id, SEL, ...); // r12
  id (*v81)(id, SEL, ...); // r12
  id (*v86)(id, SEL, ...); // r12
  id (*v89)(id, SEL, ...); // r12
  id (*v94)(id, SEL, ...); // r12
  id (*v97)(id, SEL, ...); // r12
  id (*v102)(id, SEL, ...); // r12
  id (*v105)(id, SEL, ...); // r12
  id (*v110)(id, SEL, ...); // r12
  id (*v114)(id, SEL, ...); // r12
  id (*v119)(id, SEL, ...); // r12
  id (*v123)(id, SEL, ...); // r12
  id (*v128)(id, SEL, ...); // r12
  id (*v131)(id, SEL, ...); // r12
  id (*v136)(id, SEL, ...); // r12
  NSNumber *v144; // rax
  NSNumber *v145; // r14
  NSNumber *v150; // rax
  NSNumber *v151; // r13
  id (**v339)(id, SEL, ...); // r12
  id (**v340)(id, SEL, ...); // rbx
  SEL v346; // r12
  bool v348; // zf
  SEL v355; // r12
  id (**v364)(id, SEL, ...); // rbx
  NSNumber *v365; // rax
  NSNumber *v366; // rbx
  id (*v367)(id, SEL, ...); // r12
  id (*v370)(id, SEL, ...); // r12
  id (*v372)(id, SEL, ...); // r12
  int v415; // [rsp+15Ch] [rbp-34h]

  v4 = objc_retain(a3);
  v374.receiver = self;
  v374.super_class = (Class)&OBJC_CLASS___TradeStallsShiDangQuoteItem;
  -[QuoteBaseItem resetBaseValues:](&v374, "resetBaseValues:", v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 24LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(v7, "objectForKeyedSubscript:", v6);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  buyOne = self->_buyOne;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v9, buyOne);
  self->_buyOne = buyOne;
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 20LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = _objc_msgSend(v13, "objectForKeyedSubscript:", v12);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = v12;
  v18 = v17;
  v19 = self->_buyOne;
  v375 = v15;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v15, v19);
  self->_buyOne = v19;
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 26LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v18, "objectForKeyedSubscript:", v21);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  buyTwo = self->_buyTwo;
  v376 = v23;
  _objc_msgSend(self, v25, v23, buyTwo);
  self->_buyTwo = buyTwo;
  v27 = v26(&OBJC_CLASS___NSNumber, "numberWithInt:", 28LL);
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v30 = v29(v18, "objectForKeyedSubscript:", v28);
  v31 = objc_retainAutoreleasedReturnValue(v30);
  buyThree = self->_buyThree;
  v377 = v31;
  v33(self, "valueWithNumber:oldValue:", v31, buyThree);
  self->_buyThree = buyThree;
  v35 = v34(&OBJC_CLASS___NSNumber, "numberWithInt:", 150LL);
  v37 = v36;
  v38 = objc_retainAutoreleasedReturnValue(v35);
  v39 = (void *)v37(v18, "objectForKeyedSubscript:", v38);
  v40 = objc_retainAutoreleasedReturnValue(v39);
  buyFour = self->_buyFour;
  v378 = v40;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v40, buyFour);
  *(double *)((char *)&self->super.super.isa + v43) = buyFour;
  v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 154LL);
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = _objc_msgSend(v18, "objectForKeyedSubscript:", v45);
  v47 = objc_retainAutoreleasedReturnValue(v46);
  buyFive = self->_buyFive;
  v379 = v47;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v47, buyFive);
  *(double *)((char *)&self->super.super.isa + v50) = buyFive;
  v51 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 102LL);
  v52 = objc_retainAutoreleasedReturnValue(v51);
  v53 = _objc_msgSend(v18, "objectForKeyedSubscript:", v52);
  v54 = objc_retainAutoreleasedReturnValue(v53);
  buySix = self->_buySix;
  v414 = v54;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v54, buySix);
  self->_buySix = buySix;
  v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 106LL);
  v57 = objc_retainAutoreleasedReturnValue(v56);
  v58 = _objc_msgSend(v18, "objectForKeyedSubscript:", v57);
  v59 = objc_retainAutoreleasedReturnValue(v58);
  buySeven = self->_buySeven;
  v380 = v59;
  _objc_msgSend(self, v61, v59, buySeven);
  self->_buySeven = buySeven;
  v63 = v62(&OBJC_CLASS___NSNumber, "numberWithInt:", 110LL);
  v64 = objc_retainAutoreleasedReturnValue(v63);
  v66 = v65(v18, "objectForKeyedSubscript:", v64);
  v67 = objc_retainAutoreleasedReturnValue(v66);
  buyEight = self->_buyEight;
  v381 = v67;
  v69(self, "valueWithNumber:oldValue:", v67, buyEight);
  self->_buyEight = buyEight;
  v71 = v70(&OBJC_CLASS___NSNumber, "numberWithInt:", 114LL);
  v72 = objc_retainAutoreleasedReturnValue(v71);
  v74 = v73(v18, "objectForKeyedSubscript:", v72);
  v75 = objc_retainAutoreleasedReturnValue(v74);
  buyNine = self->_buyNine;
  v382 = v75;
  v77(self, "valueWithNumber:oldValue:", v75, buyNine);
  self->_buyNine = buyNine;
  v79 = v78(&OBJC_CLASS___NSNumber, "numberWithInt:", 118LL);
  v80 = objc_retainAutoreleasedReturnValue(v79);
  v82 = v81(v18, "objectForKeyedSubscript:", v80);
  v83 = objc_retainAutoreleasedReturnValue(v82);
  buyTen = self->_buyTen;
  v383 = v83;
  v85(self, "valueWithNumber:oldValue:", v83, buyTen);
  self->_buyTen = buyTen;
  v87 = v86(&OBJC_CLASS___NSNumber, "numberWithInt:", 25LL);
  v88 = objc_retainAutoreleasedReturnValue(v87);
  v90 = v89(v18, "objectForKeyedSubscript:", v88);
  v91 = objc_retainAutoreleasedReturnValue(v90);
  buyOneVol = self->_buyOneVol;
  v384 = v91;
  v93(self, "valueWithNumber:oldValue:", v91, buyOneVol);
  self->_buyOneVol = buyOneVol;
  v95 = v94(&OBJC_CLASS___NSNumber, "numberWithInt:", 27LL);
  v96 = objc_retainAutoreleasedReturnValue(v95);
  v416 = v18;
  v98 = v97(v18, "objectForKeyedSubscript:", v96);
  v99 = objc_retainAutoreleasedReturnValue(v98);
  buyTwoVol = self->_buyTwoVol;
  v385 = v99;
  v101(self, "valueWithNumber:oldValue:", v99, buyTwoVol);
  self->_buyTwoVol = buyTwoVol;
  v103 = v102(&OBJC_CLASS___NSNumber, "numberWithInt:", 29LL);
  v104 = objc_retainAutoreleasedReturnValue(v103);
  v106 = v105(v18, "objectForKeyedSubscript:", v104);
  v107 = objc_retainAutoreleasedReturnValue(v106);
  buyThreeVol = self->_buyThreeVol;
  v386 = v107;
  v109(self, "valueWithNumber:oldValue:", v107, buyThreeVol);
  self->_buyThreeVol = buyThreeVol;
  v111 = v110(&OBJC_CLASS___NSNumber, "numberWithInt:", 151LL);
  v112 = objc_retainAutoreleasedReturnValue(v111);
  v113 = v416;
  v115 = v114(v416, "objectForKeyedSubscript:", v112);
  v116 = objc_retainAutoreleasedReturnValue(v115);
  buyFourVol = self->_buyFourVol;
  v387 = v116;
  v118(self, "valueWithNumber:oldValue:", v116, buyFourVol);
  self->_buyFourVol = buyFourVol;
  v120 = v119(&OBJC_CLASS___NSNumber, "numberWithInt:", 155LL);
  v121 = objc_retainAutoreleasedReturnValue(v120);
  v122 = v113;
  v124 = v123(v113, "objectForKeyedSubscript:", v121);
  v125 = objc_retainAutoreleasedReturnValue(v124);
  buyFiveVol = self->_buyFiveVol;
  v388 = v125;
  v127(self, "valueWithNumber:oldValue:", v125, buyFiveVol);
  self->_buyFiveVol = buyFiveVol;
  v129 = v128(&OBJC_CLASS___NSNumber, "numberWithInt:", 103LL);
  v130 = objc_retainAutoreleasedReturnValue(v129);
  v132 = v131(v122, "objectForKeyedSubscript:", v130);
  v133 = objc_retainAutoreleasedReturnValue(v132);
  buySixVol = self->_buySixVol;
  v389 = v133;
  v135(self, "valueWithNumber:oldValue:", v133, buySixVol);
  self->_buySixVol = buySixVol;
  v137 = v136(&OBJC_CLASS___NSNumber, "numberWithInt:", 107LL);
  v139 = v138;
  v140 = objc_retainAutoreleasedReturnValue(v137);
  v141 = (void *)v139(v122, "objectForKeyedSubscript:", v140);
  v142 = objc_retainAutoreleasedReturnValue(v141);
  buySevenVol = self->_buySevenVol;
  v390 = v142;
  -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](self, "valueWithNumber:oldValue:", v142, buySevenVol);
  self->_buySevenVol = buySevenVol;
  v144 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 111LL);
  v145 = objc_retainAutoreleasedReturnValue(v144);
  v147 = _objc_msgSend(v146, "objectForKeyedSubscript:", v145);
  v148 = objc_retainAutoreleasedReturnValue(v147);
  buyEightVol = self->_buyEightVol;
  v391 = v148;
  self->_buyEightVol = ((double (__fastcall *)(TradeStallsShiDangQuoteItem *, const char *, id, double))_objc_msgSend)(
                         self,
                         "valueWithNumber:oldValue:",
                         v148,
                         buyEightVol);
  v150 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 115LL);
  v151 = objc_retainAutoreleasedReturnValue(v150);
  v153 = v152;
  v154 = _objc_msgSend(v152, "objectForKeyedSubscript:", v151);
  v155 = objc_retainAutoreleasedReturnValue(v154);
  buyNineVol = self->_buyNineVol;
  v392 = v155;
  self->_buyNineVol = v157(self, "valueWithNumber:oldValue:", v155, buyNineVol);
  v159 = (void *)v158(&OBJC_CLASS___NSNumber, "numberWithInt:", 119LL);
  v160 = objc_retainAutoreleasedReturnValue(v159);
  v162 = (void *)v161(v153, "objectForKeyedSubscript:", v160);
  v163 = objc_retainAutoreleasedReturnValue(v162);
  buyTenVol = self->_buyTenVol;
  v393 = v163;
  self->_buyTenVol = v165(self, "valueWithNumber:oldValue:", v163, buyTenVol);
  v167 = (void *)v166(&OBJC_CLASS___NSNumber, "numberWithInt:", 30LL);
  v168 = objc_retainAutoreleasedReturnValue(v167);
  v170 = (void *)v169(v153, "objectForKeyedSubscript:", v168);
  v171 = objc_retainAutoreleasedReturnValue(v170);
  self->_saleOne = v172(self, "valueWithNumber:oldValue:", v171, self->_saleOne);
  v174 = (void *)v173(&OBJC_CLASS___NSNumber, "numberWithInt:", 21LL);
  v175 = objc_retainAutoreleasedReturnValue(v174);
  v177 = (void *)v176(v153, "objectForKeyedSubscript:", v175);
  v178 = objc_retainAutoreleasedReturnValue(v177);
  saleOne = self->_saleOne;
  v394 = v178;
  self->_saleOne = v180(self, "valueWithNumber:oldValue:", v178, saleOne);
  v182 = (void *)v181(&OBJC_CLASS___NSNumber, "numberWithInt:", 32LL);
  v183 = objc_retainAutoreleasedReturnValue(v182);
  v184 = v416;
  v186 = (void *)v185(v416, "objectForKeyedSubscript:", v183);
  v187 = objc_retainAutoreleasedReturnValue(v186);
  saleTwo = self->_saleTwo;
  v395 = v187;
  self->_saleTwo = v189(self, "valueWithNumber:oldValue:", v187, saleTwo);
  v191 = (void *)v190(&OBJC_CLASS___NSNumber, "numberWithInt:", 34LL);
  v192 = objc_retainAutoreleasedReturnValue(v191);
  v194 = (void *)v193(v184, "objectForKeyedSubscript:", v192);
  v195 = objc_retainAutoreleasedReturnValue(v194);
  saleThree = self->_saleThree;
  v396 = v195;
  self->_saleThree = v197(self, "valueWithNumber:oldValue:", v195, saleThree);
  v199 = (void *)v198(&OBJC_CLASS___NSNumber, "numberWithInt:", 152LL);
  v200 = objc_retainAutoreleasedReturnValue(v199);
  v202 = (void *)v201(v184, "objectForKeyedSubscript:", v200);
  v203 = objc_retainAutoreleasedReturnValue(v202);
  saleFour = self->_saleFour;
  v397 = v203;
  self->_saleFour = v205(self, "valueWithNumber:oldValue:", v203, saleFour);
  v207 = (void *)v206(&OBJC_CLASS___NSNumber, "numberWithInt:", 156LL);
  v208 = objc_retainAutoreleasedReturnValue(v207);
  v210 = (void *)v209(v184, "objectForKeyedSubscript:", v208);
  v211 = objc_retainAutoreleasedReturnValue(v210);
  saleFive = self->_saleFive;
  v398 = v211;
  self->_saleFive = v213(self, "valueWithNumber:oldValue:", v211, saleFive);
  v215 = (void *)v214(&OBJC_CLASS___NSNumber, "numberWithInt:", 104LL);
  v216 = objc_retainAutoreleasedReturnValue(v215);
  v218 = (void *)v217(v184, "objectForKeyedSubscript:", v216);
  v219 = objc_retainAutoreleasedReturnValue(v218);
  saleSix = self->_saleSix;
  v399 = v219;
  self->_saleSix = v221(self, "valueWithNumber:oldValue:", v219, saleSix);
  v223 = (void *)v222(&OBJC_CLASS___NSNumber, "numberWithInt:", 108LL);
  v224 = objc_retainAutoreleasedReturnValue(v223);
  v225 = (__int64)v184;
  v227 = (void *)v226(v184, "objectForKeyedSubscript:", v224);
  v228 = objc_retainAutoreleasedReturnValue(v227);
  saleSeven = self->_saleSeven;
  v400 = v228;
  self->_saleSeven = v230(self, "valueWithNumber:oldValue:", v228, saleSeven);
  v232 = (void *)v231(&OBJC_CLASS___NSNumber, "numberWithInt:", 112LL);
  v233 = objc_retainAutoreleasedReturnValue(v232);
  v234 = v225;
  v236 = (void *)v235(v225, "objectForKeyedSubscript:", v233);
  v237 = objc_retainAutoreleasedReturnValue(v236);
  saleEight = self->_saleEight;
  v401 = v237;
  self->_saleEight = v239(self, "valueWithNumber:oldValue:", v237, saleEight);
  v241 = (void *)v240(&OBJC_CLASS___NSNumber, "numberWithInt:", 116LL);
  v242 = objc_retainAutoreleasedReturnValue(v241);
  v244 = (void *)v243(v234, "objectForKeyedSubscript:", v242);
  v245 = objc_retainAutoreleasedReturnValue(v244);
  saleNine = self->_saleNine;
  v402 = v245;
  self->_saleNine = v247(self, "valueWithNumber:oldValue:", v245, saleNine);
  v249 = (void *)v248(&OBJC_CLASS___NSNumber, "numberWithInt:", 120LL);
  v250 = objc_retainAutoreleasedReturnValue(v249);
  v251 = v234;
  v253 = (void *)v252(v234, "objectForKeyedSubscript:", v250);
  v254 = objc_retainAutoreleasedReturnValue(v253);
  saleTen = self->_saleTen;
  v403 = v254;
  self->_saleTen = v256(self, "valueWithNumber:oldValue:", v254, saleTen);
  v258 = (void *)v257(&OBJC_CLASS___NSNumber, "numberWithInt:", 31LL);
  v259 = objc_retainAutoreleasedReturnValue(v258);
  v261 = (void *)v260(v251, "objectForKeyedSubscript:", v259);
  v262 = objc_retainAutoreleasedReturnValue(v261);
  saleOneVol = self->_saleOneVol;
  v404 = v262;
  self->_saleOneVol = v264(self, "valueWithNumber:oldValue:", v262, saleOneVol);
  v266 = (void *)v265(&OBJC_CLASS___NSNumber, "numberWithInt:", 33LL);
  v267 = objc_retainAutoreleasedReturnValue(v266);
  v268 = v251;
  v270 = (void *)v269(v251, "objectForKeyedSubscript:", v267);
  v271 = objc_retainAutoreleasedReturnValue(v270);
  saleTwoVol = self->_saleTwoVol;
  v405 = v271;
  self->_saleTwoVol = v273(self, "valueWithNumber:oldValue:", v271, saleTwoVol);
  v275 = (void *)v274(&OBJC_CLASS___NSNumber, "numberWithInt:", 35LL);
  v276 = objc_retainAutoreleasedReturnValue(v275);
  v278 = (void *)v277(v268, "objectForKeyedSubscript:", v276);
  v279 = objc_retainAutoreleasedReturnValue(v278);
  saleThreeVol = self->_saleThreeVol;
  v406 = v279;
  self->_saleThreeVol = v281(self, "valueWithNumber:oldValue:", v279, saleThreeVol);
  v283 = (void *)v282(&OBJC_CLASS___NSNumber, "numberWithInt:", 153LL);
  v284 = objc_retainAutoreleasedReturnValue(v283);
  v286 = (void *)v285(v268, "objectForKeyedSubscript:", v284);
  v287 = objc_retainAutoreleasedReturnValue(v286);
  saleFourVol = self->_saleFourVol;
  v407 = v287;
  self->_saleFourVol = v289(self, "valueWithNumber:oldValue:", v287, saleFourVol);
  v291 = (void *)v290(&OBJC_CLASS___NSNumber, "numberWithInt:", 157LL);
  v292 = objc_retainAutoreleasedReturnValue(v291);
  v294 = (void *)v293(v268, "objectForKeyedSubscript:", v292);
  v295 = objc_retainAutoreleasedReturnValue(v294);
  saleFiveVol = self->_saleFiveVol;
  v408 = v295;
  self->_saleFiveVol = v297(self, "valueWithNumber:oldValue:", v295, saleFiveVol);
  v299 = (void *)v298(&OBJC_CLASS___NSNumber, "numberWithInt:", 105LL);
  v300 = objc_retainAutoreleasedReturnValue(v299);
  v302 = (void *)v301(v268, "objectForKeyedSubscript:", v300);
  v303 = objc_retainAutoreleasedReturnValue(v302);
  saleSixVol = self->_saleSixVol;
  v409 = v303;
  self->_saleSixVol = v305(self, "valueWithNumber:oldValue:", v303, saleSixVol);
  v307 = (void *)v306(&OBJC_CLASS___NSNumber, "numberWithInt:", 109LL);
  v308 = objc_retainAutoreleasedReturnValue(v307);
  v310 = (void *)v309(v268, "objectForKeyedSubscript:", v308);
  v311 = objc_retainAutoreleasedReturnValue(v310);
  saleSevenVol = self->_saleSevenVol;
  v410 = v311;
  self->_saleSevenVol = v313(self, "valueWithNumber:oldValue:", v311, saleSevenVol);
  v315 = (void *)v314(&OBJC_CLASS___NSNumber, "numberWithInt:", 113LL);
  v316 = objc_retainAutoreleasedReturnValue(v315);
  v318 = (void *)v317(v268, "objectForKeyedSubscript:", v316);
  v319 = objc_retainAutoreleasedReturnValue(v318);
  saleEightVol = self->_saleEightVol;
  v411 = v319;
  self->_saleEightVol = v321(self, "valueWithNumber:oldValue:", v319, saleEightVol);
  v323 = (void *)v322(&OBJC_CLASS___NSNumber, "numberWithInt:", 117LL);
  v324 = objc_retainAutoreleasedReturnValue(v323);
  v326 = (void *)v325(v268, "objectForKeyedSubscript:", v324);
  v327 = objc_retainAutoreleasedReturnValue(v326);
  saleNineVol = self->_saleNineVol;
  v412 = v327;
  self->_saleNineVol = v329(self, "valueWithNumber:oldValue:", v327, saleNineVol);
  v331 = (void *)v330(&OBJC_CLASS___NSNumber, "numberWithInt:", 121LL);
  v332 = objc_retainAutoreleasedReturnValue(v331);
  v334 = (void *)v333(v268, "objectForKeyedSubscript:", v332);
  v335 = objc_retainAutoreleasedReturnValue(v334);
  saleTenVol = self->_saleTenVol;
  v413 = v335;
  v338 = v337(self, "valueWithNumber:oldValue:", v335, saleTenVol);
  self->_saleTenVol = v338;
  v340 = v339;
  if ( ((unsigned __int8 (__fastcall *)(TradeStallsShiDangQuoteItem *, const char *))v339)(self, "isCallAuctionTime") )
  {
    v341 = "numberWithDouble:";
    v342 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, double))v340)(
                     &OBJC_CLASS___NSNumber,
                     "numberWithDouble:",
                     self->_buyTwo);
    v343 = objc_retainAutoreleasedReturnValue(v342);
    if ( ((unsigned __int8 (__fastcall *)(TradeStallsShiDangQuoteItem *, const char *, id))v340)(
           self,
           "isValidValue:",
           v343) )
    {
    }
    else
    {
      v344 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, double))v340)(
                       &OBJC_CLASS___NSNumber,
                       "numberWithDouble:",
                       self->_buyOne);
      v345 = objc_retainAutoreleasedReturnValue(v344);
      v347 = (unsigned __int8)_objc_msgSend(self, v346, v345);
      v340 = &_objc_msgSend;
      v348 = v347 == 0;
      v341 = v349;
      if ( !v348 )
        self->_buyTwo = self->_buyOne;
    }
    v338 = self->_saleTwo;
    v350 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, double))v340)(&OBJC_CLASS___NSNumber, v341, v338);
    v351 = objc_retainAutoreleasedReturnValue(v350);
    if ( ((unsigned __int8 (__fastcall *)(TradeStallsShiDangQuoteItem *, __int64, id))v340)(self, v352, v351) )
    {
    }
    else
    {
      v338 = self->_saleOne;
      v353 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, double))v340)(&OBJC_CLASS___NSNumber, v341, v338);
      v354 = objc_retainAutoreleasedReturnValue(v353);
      v356 = (unsigned __int8)_objc_msgSend(self, v355, v354);
      v340 = &_objc_msgSend;
      if ( v356 )
      {
        v338 = *(double *)((char *)&self->super.super.isa + v357);
        self->_saleTwo = v338;
      }
    }
  }
  v358 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v340)(
                   &OBJC_CLASS___NSNumber,
                   "numberWithInt:",
                   123LL);
  v359 = objc_retainAutoreleasedReturnValue(v358);
  v360 = _objc_msgSend(v416, "objectForKeyedSubscript:", v359);
  v361 = objc_retainAutoreleasedReturnValue(v360);
  v362 = v359;
  v364 = &_objc_msgSend;
  v363 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  LOBYTE(v364) = 1;
  if ( (unsigned __int8)_objc_msgSend(v361, "isKindOfClass:", v363) )
  {
    _objc_msgSend(v361, "doubleValue");
    if ( v338 != 4294967295.0 )
    {
      _objc_msgSend(v361, "doubleValue");
      if ( v338 != 2147483648.0 )
      {
        _objc_msgSend(v361, "doubleValue");
        -[TradeStallsShiDangQuoteItem setAllBuyVol:](self, "setAllBuyVol:");
        LODWORD(v364) = 0;
      }
    }
  }
  v415 = (int)v364;
  v365 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 125LL);
  v366 = objc_retainAutoreleasedReturnValue(v365);
  v368 = v367(v416, "objectForKeyedSubscript:", v366);
  v369 = objc_retainAutoreleasedReturnValue(v368);
  v371 = v370(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)v372(v369, "isKindOfClass:", v371) )
  {
    _objc_msgSend(v369, "doubleValue");
    if ( v338 != 4294967295.0 )
    {
      _objc_msgSend(v369, "doubleValue");
      if ( v338 != 2147483648.0 )
      {
        _objc_msgSend(v369, "doubleValue");
        -[TradeStallsShiDangQuoteItem setAllSaleVol:](self, "setAllSaleVol:");
        goto LABEL_18;
      }
    }
  }
  if ( !(_BYTE)v415 )
LABEL_18:
    -[TradeStallsShiDangQuoteItem weiBiCalculate](self, "weiBiCalculate");
}

//----- (00000001007FADB8) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem valueWithNumber:oldValue:](
        TradeStallsShiDangQuoteItem *self,
        SEL a2,
        id a3,
        double a4)
{

  v10 = a4;
  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    _objc_msgSend(v4, "doubleValue");
    v6 = _mm_cmpeq_sd((__m128d)0x41E0000000000000uLL, *(__m128d *)&a4).f64[0];
    v7 = *(_QWORD *)&v6 & 0x41EFFFFFFFE00000LL | ~*(_QWORD *)&v6 & *(_QWORD *)&a4;
    v8 = _mm_cmpeq_sd(*(__m128d *)&a4, (__m128d)0x41EFFFFFFFE00000uLL).f64[0];
    *(_QWORD *)&v10 = *(_QWORD *)&v8 & *(_QWORD *)&v10 | ~*(_QWORD *)&v8 & v7;
  }
  return v10;
}

//----- (00000001007FAE66) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem weiBiCalculate](TradeStallsShiDangQuoteItem *self, SEL a2)
{

  -[TradeStallsShiDangQuoteItem allBuyVol](self, "allBuyVol");
  if ( v2 != 4294967295.0 )
  {
    -[TradeStallsShiDangQuoteItem allBuyVol](self, "allBuyVol", 0.0);
    if ( 2147483648.0 != 0.0 )
      -[TradeStallsShiDangQuoteItem allBuyVol](self, "allBuyVol", 0.0);
  }
  -[TradeStallsShiDangQuoteItem allSaleVol](self, "allSaleVol");
  if ( 4294967295.0 != 0.0 )
  {
    -[TradeStallsShiDangQuoteItem allSaleVol](self, "allSaleVol");
    if ( 2147483648.0 != 0.0 )
      -[TradeStallsShiDangQuoteItem allSaleVol](self, "allSaleVol", 0.0);
  }
  if ( 0.0 + 0.0 != 0.0 )
  {
    -[TradeStallsShiDangQuoteItem setWeiBi:](self, "setWeiBi:", 0.0 - 0.0);
    -[TradeStallsShiDangQuoteItem setWeiBiBaiFeiBi:](self, "setWeiBiBaiFeiBi:", (0.0 - 0.0) / (0.0 + 0.0));
  }
}

//----- (00000001007FAF75) ----------------------------------------------------
char __cdecl -[TradeStallsShiDangQuoteItem isCallAuctionTime](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  NSDate *v6; // rax
  NSDate *v7; // rbx
  NSString *v14; // rax
  NSString *v15; // r14
  bool v16; // bl
  NSDate *v19; // [rsp+10h] [rbp-40h]
  bool v20; // [rsp+24h] [rbp-2Ch]

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getServerTime");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v18 = v5;
  _objc_msgSend(v5, "doubleValue");
  v6 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = +[HXTools getDateFormatter](&OBJC_CLASS___HXTools, "getDateFormatter");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setDateFormat:", CFSTR("HHmm"));
  v10 = _objc_msgSend(v9, "stringFromDate:", v7);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v20 = (int)_objc_msgSend(v11, "intValue") >= 915 && (int)_objc_msgSend(v12, "intValue") < 931;
  v19 = v7;
  if ( (int)_objc_msgSend(v12, "intValue") < 1457 || (int)_objc_msgSend(v13, "intValue") > 1500 )
  {
    v16 = 0;
  }
  else
  {
    v14 = -[QuoteBaseItem market](self, "market");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = (unsigned __int8)+[HXTools isHuShenMarket:](&OBJC_CLASS___HXTools, "isHuShenMarket:", v15) != 0;
  }
  return v20 || v16;
}

//----- (00000001007FB122) ----------------------------------------------------
char __cdecl -[TradeStallsShiDangQuoteItem isValidValue:](TradeStallsShiDangQuoteItem *self, SEL a2, id a3)
{
  bool v6; // bl

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( !(unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5)
    || (_objc_msgSend(v4, "doubleValue"), v3 == 4294967295.0)
    || (_objc_msgSend(v4, "doubleValue"), v3 == 2147483648.0) )
  {
    v6 = 0;
  }
  else
  {
    _objc_msgSend(v4, "doubleValue");
    v6 = 1;
    if ( v3 > -0.000001 )
    {
      _objc_msgSend(v4, "doubleValue");
      v6 = v3 >= 0.000001;
    }
  }
  return v6;
}

//----- (00000001007FB1E7) ----------------------------------------------------
char __cdecl -[TradeStallsShiDangQuoteItem disPlayMoney](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_disPlayMoney;
}

//----- (00000001007FB1F8) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setDisPlayMoney:](TradeStallsShiDangQuoteItem *self, SEL a2, char a3)
{
  self->_disPlayMoney = a3;
}

//----- (00000001007FB208) ----------------------------------------------------
id __cdecl -[TradeStallsShiDangQuoteItem reloadDataBlock](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 136LL, 0);
}

//----- (00000001007FB21B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setReloadDataBlock:](TradeStallsShiDangQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 136LL);
}

//----- (00000001007FB22C) ----------------------------------------------------
char __cdecl -[TradeStallsShiDangQuoteItem canDisPlayMoney](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_canDisPlayMoney;
}

//----- (00000001007FB23D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyOne](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyOne;
}

//----- (00000001007FB24F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyOne:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyOne = a3;
}

//----- (00000001007FB261) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyTwo](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyTwo;
}

//----- (00000001007FB273) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyTwo:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyTwo = a3;
}

//----- (00000001007FB285) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyThree](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyThree;
}

//----- (00000001007FB297) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyThree:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyThree = a3;
}

//----- (00000001007FB2A9) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyFour](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyFour;
}

//----- (00000001007FB2BB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyFour:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyFour = a3;
}

//----- (00000001007FB2CD) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyFive](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyFive;
}

//----- (00000001007FB2DF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyFive:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyFive = a3;
}

//----- (00000001007FB2F1) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buySix](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buySix;
}

//----- (00000001007FB303) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuySix:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buySix = a3;
}

//----- (00000001007FB315) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buySeven](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buySeven;
}

//----- (00000001007FB327) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuySeven:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buySeven = a3;
}

//----- (00000001007FB339) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyEight](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyEight;
}

//----- (00000001007FB34B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyEight:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyEight = a3;
}

//----- (00000001007FB35D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyNine](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyNine;
}

//----- (00000001007FB36F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyNine:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyNine = a3;
}

//----- (00000001007FB381) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyTen](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyTen;
}

//----- (00000001007FB393) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyTen:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyTen = a3;
}

//----- (00000001007FB3A5) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyOneVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyOneVol;
}

//----- (00000001007FB3B7) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyOneVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyOneVol = a3;
}

//----- (00000001007FB3C9) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyTwoVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyTwoVol;
}

//----- (00000001007FB3DB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyTwoVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyTwoVol = a3;
}

//----- (00000001007FB3ED) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyThreeVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyThreeVol;
}

//----- (00000001007FB3FF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyThreeVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyThreeVol = a3;
}

//----- (00000001007FB411) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyFourVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyFourVol;
}

//----- (00000001007FB423) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyFourVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyFourVol = a3;
}

//----- (00000001007FB435) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyFiveVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyFiveVol;
}

//----- (00000001007FB447) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyFiveVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyFiveVol = a3;
}

//----- (00000001007FB459) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buySixVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buySixVol;
}

//----- (00000001007FB46B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuySixVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buySixVol = a3;
}

//----- (00000001007FB47D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buySevenVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buySevenVol;
}

//----- (00000001007FB48F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuySevenVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buySevenVol = a3;
}

//----- (00000001007FB4A1) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyEightVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyEightVol;
}

//----- (00000001007FB4B3) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyEightVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyEightVol = a3;
}

//----- (00000001007FB4C5) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyNineVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyNineVol;
}

//----- (00000001007FB4D7) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyNineVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyNineVol = a3;
}

//----- (00000001007FB4E9) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem buyTenVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_buyTenVol;
}

//----- (00000001007FB4FB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setBuyTenVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_buyTenVol = a3;
}

//----- (00000001007FB50D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleOne](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleOne;
}

//----- (00000001007FB51F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleOne:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleOne = a3;
}

//----- (00000001007FB531) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleTwo](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleTwo;
}

//----- (00000001007FB543) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleTwo:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleTwo = a3;
}

//----- (00000001007FB555) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleThree](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleThree;
}

//----- (00000001007FB567) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleThree:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleThree = a3;
}

//----- (00000001007FB579) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleFour](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleFour;
}

//----- (00000001007FB58B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleFour:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleFour = a3;
}

//----- (00000001007FB59D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleFive](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleFive;
}

//----- (00000001007FB5AF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleFive:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleFive = a3;
}

//----- (00000001007FB5C1) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleSix](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleSix;
}

//----- (00000001007FB5D3) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleSix:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleSix = a3;
}

//----- (00000001007FB5E5) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleSeven](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleSeven;
}

//----- (00000001007FB5F7) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleSeven:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleSeven = a3;
}

//----- (00000001007FB609) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleEight](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleEight;
}

//----- (00000001007FB61B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleEight:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleEight = a3;
}

//----- (00000001007FB62D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleNine](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleNine;
}

//----- (00000001007FB63F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleNine:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleNine = a3;
}

//----- (00000001007FB651) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleTen](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleTen;
}

//----- (00000001007FB663) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleTen:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleTen = a3;
}

//----- (00000001007FB675) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleOneVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleOneVol;
}

//----- (00000001007FB687) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleOneVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleOneVol = a3;
}

//----- (00000001007FB699) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleTwoVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleTwoVol;
}

//----- (00000001007FB6AB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleTwoVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleTwoVol = a3;
}

//----- (00000001007FB6BD) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleThreeVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleThreeVol;
}

//----- (00000001007FB6CF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleThreeVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleThreeVol = a3;
}

//----- (00000001007FB6E1) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleFourVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleFourVol;
}

//----- (00000001007FB6F3) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleFourVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleFourVol = a3;
}

//----- (00000001007FB705) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleFiveVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleFiveVol;
}

//----- (00000001007FB717) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleFiveVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleFiveVol = a3;
}

//----- (00000001007FB729) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleSixVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleSixVol;
}

//----- (00000001007FB73B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleSixVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleSixVol = a3;
}

//----- (00000001007FB74D) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleSevenVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleSevenVol;
}

//----- (00000001007FB75F) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleSevenVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleSevenVol = a3;
}

//----- (00000001007FB771) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleEightVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleEightVol;
}

//----- (00000001007FB783) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleEightVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleEightVol = a3;
}

//----- (00000001007FB795) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleNineVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleNineVol;
}

//----- (00000001007FB7A7) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleNineVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleNineVol = a3;
}

//----- (00000001007FB7B9) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem saleTenVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_saleTenVol;
}

//----- (00000001007FB7CB) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setSaleTenVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_saleTenVol = a3;
}

//----- (00000001007FB7DD) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem allBuyVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_allBuyVol;
}

//----- (00000001007FB7EF) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setAllBuyVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_allBuyVol = a3;
}

//----- (00000001007FB801) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem allSaleVol](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_allSaleVol;
}

//----- (00000001007FB813) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setAllSaleVol:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_allSaleVol = a3;
}

//----- (00000001007FB825) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem weiBi](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_weiBi;
}

//----- (00000001007FB837) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setWeiBi:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_weiBi = a3;
}

//----- (00000001007FB849) ----------------------------------------------------
double __cdecl -[TradeStallsShiDangQuoteItem weiBiBaiFeiBi](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_weiBiBaiFeiBi;
}

//----- (00000001007FB85B) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setWeiBiBaiFeiBi:](TradeStallsShiDangQuoteItem *self, SEL a2, double a3)
{
  self->_weiBiBaiFeiBi = a3;
}

//----- (00000001007FB86D) ----------------------------------------------------
char __cdecl -[TradeStallsShiDangQuoteItem isHongKongMarke](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  return self->_isHongKongMarke;
}

//----- (00000001007FB87E) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem setIsHongKongMarke:](TradeStallsShiDangQuoteItem *self, SEL a2, char a3)
{
  self->_isHongKongMarke = a3;
}

//----- (00000001007FB88E) ----------------------------------------------------
void __cdecl -[TradeStallsShiDangQuoteItem .cxx_destruct](TradeStallsShiDangQuoteItem *self, SEL a2)
{
  objc_storeStrong(&self->_reloadDataBlock, 0LL);
}

