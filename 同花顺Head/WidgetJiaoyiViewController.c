id __cdecl -[WidgetJiaoyiViewController nibName](WidgetJiaoyiViewController *self, SEL a2)
{
  return CFSTR("WidgetJiaoyiViewController");
}

//----- (0000000100D1502D) ----------------------------------------------------
id __cdecl -[WidgetJiaoyiViewController nibBundle](WidgetJiaoyiViewController *self, SEL a2)
{

  v2 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "GetBundle");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100D15096) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController viewDidLoad](WidgetJiaoyiViewController *self, SEL a2)
{
  HXBaseTradeView *v2; // rax
  HXBaseTradeView *v3; // r14
  signed __int8 v7; // al

  v8.receiver = self;
  v8.super_class = (Class)&OBJC_CLASS___WidgetJiaoyiViewController;
  -[PanKouModularBaseViewController viewDidLoad](&v8, "viewDidLoad");
  -[WidgetJiaoyiViewController initObjects](self, "initObjects");
  -[WidgetJiaoyiViewController loadViewFromNib](self, "loadViewFromNib");
  -[WidgetJiaoyiViewController upThemes](self, "upThemes");
  v2 = -[WidgetJiaoyiViewController aDetailContainerView](self, "aDetailContainerView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[WidgetJiaoyiViewController setJyView:](self, "setJyView:", v3);
  -[WidgetJiaoyiViewController addNotification](self, "addNotification");
  v4 = +[HardwareInfoManager shareInstance](&OBJC_CLASS___HardwareInfoManager, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(v5);
  -[WidgetJiaoyiViewController setViewState](self, "setViewState");
  -[WidgetJiaoyiViewController setShowAll:](self, "setShowAll:", 0LL);
  -[WidgetJiaoyiViewController setIsMairuSelected:](self, "setIsMairuSelected:", 1LL);
  v7 = (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll");
  -[WidgetJiaoyiViewController switchContainerViewState:](self, "switchContainerViewState:", (unsigned int)v7);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    10LL,
    CFSTR("Widget交易_页面统计"),
    0LL,
    1LL);
}

//----- (0000000100D151E7) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController initObjects](WidgetJiaoyiViewController *self, SEL a2)
{
  NSDictionary *v6; // rax
  NSDictionary *v7; // rbx

  v2 = _objc_msgSend(&OBJC_CLASS___NSBundle, "mainBundle");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "pathForResource:ofType:", CFSTR("MarketPageRelation"), CFSTR("plist"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithContentsOfFile:", v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[WidgetJiaoyiViewController setMarketPageRelationDic:](self, "setMarketPageRelationDic:", v7);
}

//----- (0000000100D152C4) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController loadViewFromNib](WidgetJiaoyiViewController *self, SEL a2)
{
  id (*v5)(id, SEL, ...); // r12
  const char *WeakRetained; // rax
  id (*v17)(id, SEL, ...); // r12
  _QWORD v28[4]; // [rsp+28h] [rbp-98h] BYREF
  id to; // [rsp+48h] [rbp-78h] BYREF
  id location[6]; // [rsp+90h] [rbp-30h] BYREF

  if ( !*(_QWORD *)&self->super.super._shouldRefresh )
  {
    v3 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v6 = v5(v4, "GetBundle");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = +[HXBaseTradeView loadFormNibName:bundle:](
           &OBJC_CLASS___WidgetUserListView,
           "loadFormNibName:bundle:",
           CFSTR("WidgetUserListView"),
           v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = *(void **)&self->super.super._shouldRefresh;
    *(_QWORD *)&self->super.super._shouldRefresh = v9;
    WeakRetained = (const char *)objc_loadWeakRetained((id *)&self->super._correlatedCode);
    v13 = (char *)WeakRetained;
    if ( WeakRetained )
      objc_msgSend_stret(v30, WeakRetained, "bounds");
    else
      memset(v30, 0, sizeof(v30));
    _objc_msgSend(*(id *)&self->super.super._shouldRefresh, "setFrame:");
    _objc_msgSend(*(id *)&self->super.super._shouldRefresh, "didLoad");
    v14 = objc_loadWeakRetained((id *)&self->super._correlatedCode);
    _objc_msgSend(v14, "addSubview:", *(_QWORD *)&self->super.super._shouldRefresh);
  }
  if ( !*(_QWORD *)&self->super.super.super._reserved )
  {
    v15 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v18 = v17(v16, "GetBundle");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = +[HXBaseTradeView loadFormNibName:bundle:](
            &OBJC_CLASS___WidgetMaiMaiView,
            "loadFormNibName:bundle:",
            CFSTR("WidgetMaiMaiView"),
            v19);
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = *(void **)&self->super.super.super._reserved;
    *(_QWORD *)&self->super.super.super._reserved = v21;
    v24 = (const char *)objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
    v25 = (char *)v24;
    if ( v24 )
      objc_msgSend_stret(v31, v24, "bounds");
    else
      memset(v31, 0, sizeof(v31));
    _objc_msgSend(*(id *)&self->super.super.super._reserved, "setFrame:");
    _objc_msgSend(*(id *)&self->super.super.super._reserved, "setDelegate:", self);
    _objc_msgSend(*(id *)&self->super.super.super._reserved, "setMaimaiViewStyle:", 3LL);
    v26 = objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
    _objc_msgSend(v26, "addSubview:", *(_QWORD *)&self->super.super.super._reserved);
    objc_initWeak(location, self);
    v27 = *(void **)&self->super.super.super._reserved;
    v28[0] = _NSConcreteStackBlock;
    v28[1] = 3254779904LL;
    v28[2] = sub_100D15627;
    v28[3] = &unk_1012E7E90;
    objc_copyWeak(&to, location);
    _objc_msgSend(v27, "setJumpActionBlock:", v28);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (0000000100D15627) ----------------------------------------------------
__int64 __fastcall sub_100D15627(__int64 a1, void *a2)
{
  NSString *v11; // rax
  __CFString *v12; // rbx
  NSNumber *v13; // rax
  __CFString *v16; // rax
  __CFString *v17; // r15
  NSDictionary *v18; // rax
  NSDictionary *v19; // rax
  NSNumber *v26; // [rsp+10h] [rbp-80h]
  id WeakRetained; // [rsp+18h] [rbp-78h]
  NSString *v30; // [rsp+20h] [rbp-70h]

  v25 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = _objc_msgSend(WeakRetained, "marketPageRelationDic");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v29 = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(v29, "market");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v3, "objectForKeyedSubscript:", v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v29);
  v9(v3);
  v10(WeakRetained);
  v28 = v7;
  v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@QuickTradeNotification"), v7);
  v31[0] = (__int64)CFSTR("tradePrice");
  v12 = &charsToLeaveEscaped;
  v32[0] = (__int64)&charsToLeaveEscaped;
  v31[1] = (__int64)CFSTR("maiMaiType");
  v30 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
  v26 = objc_retain(v13);
  v32[1] = (__int64)v26;
  v31[2] = (__int64)CFSTR("market");
  v14 = objc_loadWeakRetained((id *)(a1 + 32));
  v15 = _objc_msgSend(v14, "market");
  v16 = (__CFString *)objc_retain(v15);
  v17 = v16;
  if ( v16 )
    v12 = v16;
  v32[2] = (__int64)v12;
  v18 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v31, 3LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "mutableCopy");
  _objc_msgSend(v20, "addEntriesFromDictionary:", v25);
  v22 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  _objc_msgSend(v23, "postNotificationName:object:", v30, v20);
  return __stack_chk_guard;
}

//----- (0000000100D1594F) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setViewState](WidgetJiaoyiViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v44)(id, SEL, ...); // r12
  id (*v47)(id, SEL, ...); // r12
  id (*v51)(id, SEL, ...); // r12
  DragTextField *v56; // rax
  DragTextField *v57; // rbx
  DragTextField *v60; // rax
  DragTextField *v61; // rbx
  DragTextField *v64; // rax
  DragTextField *v65; // rbx
  DragTextField *v66; // rax
  DragTextField *v67; // rbx
  DragTextField *v68; // rax
  DragTextField *v69; // r14
  DragTextField *v70; // rax
  DragTextField *v71; // rbx
  SEL v72; // r12
  DragTextField *v74; // rax
  DragTextField *v75; // rbx
  DragTextField *v79; // rax
  DragTextField *v80; // rbx
  DragTextField *v84; // rax
  DragTextField *v85; // rbx
  DragTextField *v88; // rax
  DragTextField *v89; // rbx
  DragTextField *v90; // rax
  DragTextField *v91; // rbx
  HXButton *v92; // rax
  HXButton *v93; // rbx
  HXButton *v94; // rax
  HXButton *v95; // rbx
  HXButton *v96; // rax
  HXButton *v97; // rbx
  HXButton *v98; // rax
  HXButton *v99; // rbx
  HXButton *v101; // rax
  HXButton *v102; // rbx
  _QWORD v105[4]; // [rsp+0h] [rbp-80h] BYREF
  _QWORD v107[4]; // [rsp+28h] [rbp-58h] BYREF
  id to; // [rsp+48h] [rbp-38h] BYREF
  id location[6]; // [rsp+50h] [rbp-30h] BYREF

  v2 = _objc_msgSend(self, "view");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(&OBJC_CLASS___HXDragAndDropBaseView, "class");
  v6 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v5);
  if ( v6 )
  {
    v8 = v7(self, "view");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = v10(self, "className");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v9, "setTargetViewControllerName:", v12);
    v13 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v9, "setBackgroundColor:", v14);
  }
  v15 = v7(self, "view");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = v17(self, "titleContainerView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "setDragImageView:", v16);
  v21 = v20(&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v24 = v23(self, "titleContainerView");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  _objc_msgSend(v25, "setBackgroundColor:", v22);
  v27 = v26(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v30 = v29(self, "titleContainerView");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  _objc_msgSend(v31, "setBorderColor:", v28);
  v33 = v32(self, "switchBtn");
  v34 = objc_retainAutoreleasedReturnValue(v33);
  v36 = v35(v34, "cell");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  _objc_msgSend(v37, "setHighlightsBy:", 1LL);
  v39 = v38(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v40 = objc_retainAutoreleasedReturnValue(v39);
  v42 = v41(self, "switchBtn");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  _objc_msgSend(v43, "setTextColorDefault:", v40);
  v45 = v44(&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v46 = objc_retainAutoreleasedReturnValue(v45);
  v48 = v47(self, "switchBtn");
  v49 = objc_retainAutoreleasedReturnValue(v48);
  _objc_msgSend(v49, "setBackgroundColor:", v46);
  v50(&OBJC_CLASS___DragTextField, "DoNothing");
  objc_initWeak(location, self);
  v52 = v51(self, "mairuTextField");
  v53 = objc_retainAutoreleasedReturnValue(v52);
  _objc_msgSend(v53, "setStringValue:", CFSTR("买入"));
  v54 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v55 = objc_retainAutoreleasedReturnValue(v54);
  v56 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v57 = objc_retainAutoreleasedReturnValue(v56);
  -[JYCustomTextField setBackgroundColor:](v57, "setBackgroundColor:", v55);
  v58 = +[HXThemeManager riseTextColor](&OBJC_CLASS___HXThemeManager, "riseTextColor");
  v59 = objc_retainAutoreleasedReturnValue(v58);
  v60 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v61 = objc_retainAutoreleasedReturnValue(v60);
  -[JYCustomTextField setTextColor:](v61, "setTextColor:", v59);
  v62 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v63 = objc_retainAutoreleasedReturnValue(v62);
  v64 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v65 = objc_retainAutoreleasedReturnValue(v64);
  -[JYCustomTextField setBorderColor:](v65, "setBorderColor:", v63);
  v66 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v67 = objc_retainAutoreleasedReturnValue(v66);
  -[JYCustomTextField setTag:](v67, "setTag:", 101LL);
  v68 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v69 = objc_retainAutoreleasedReturnValue(v68);
  v107[0] = _NSConcreteStackBlock;
  v107[1] = 3254779904LL;
  v107[2] = sub_100D162C5;
  v107[3] = &unk_1012EAE18;
  objc_copyWeak(&to, location);
  -[DragTextField setOnClicked:](v69, "setOnClicked:", v107);
  v70 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v71 = objc_retainAutoreleasedReturnValue(v70);
  _objc_msgSend(v71, v72, CFSTR("卖出"));
  v73 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v73);
  v74 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v75 = objc_retainAutoreleasedReturnValue(v74);
  -[JYCustomTextField setBackgroundColor:](v75, "setBackgroundColor:", v76);
  v78 = +[HXThemeManager markedTextColor](&OBJC_CLASS___HXThemeManager, "markedTextColor");
  objc_retainAutoreleasedReturnValue(v78);
  v79 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v80 = objc_retainAutoreleasedReturnValue(v79);
  -[JYCustomTextField setTextColor:](v80, "setTextColor:", v81);
  v83 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v83);
  v84 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v85 = objc_retainAutoreleasedReturnValue(v84);
  -[JYCustomTextField setBorderColor:](v85, "setBorderColor:", v86);
  v88 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v89 = objc_retainAutoreleasedReturnValue(v88);
  -[JYCustomTextField setTag:](v89, "setTag:", 100LL);
  v90 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v91 = objc_retainAutoreleasedReturnValue(v90);
  v105[0] = _NSConcreteStackBlock;
  v105[1] = 3254779904LL;
  v105[2] = sub_100D1633B;
  v105[3] = &unk_1012EAE18;
  objc_copyWeak(&v106, location);
  -[DragTextField setOnClicked:](v91, "setOnClicked:", v105);
  v92 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v93 = objc_retainAutoreleasedReturnValue(v92);
  -[HXButton setBorderWidth:](v93, "setBorderWidth:", 0.5);
  v94 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v95 = objc_retainAutoreleasedReturnValue(v94);
  -[HXButton setTopBorder:](v95, "setTopBorder:", 1LL);
  v96 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v97 = objc_retainAutoreleasedReturnValue(v96);
  -[HXButton setBottomBorder:](v97, "setBottomBorder:", 1LL);
  v98 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v99 = objc_retainAutoreleasedReturnValue(v98);
  -[HXButton setRightBorder:](v99, "setRightBorder:", 1LL);
  v100 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v100);
  v101 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v102 = objc_retainAutoreleasedReturnValue(v101);
  -[HXButton setBorderColor:](v102, "setBorderColor:", v103);
  objc_destroyWeak(&v106);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100D162C5) ----------------------------------------------------
void __fastcall sub_100D162C5(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "clickBuyOrSellAciton:", v2);
}

//----- (0000000100D1633B) ----------------------------------------------------
void __fastcall sub_100D1633B(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "clickBuyOrSellAciton:", v2);
}

//----- (0000000100D163B1) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController viewWillAppear](WidgetJiaoyiViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___WidgetJiaoyiViewController;
  objc_msgSendSuper2(&v2, "viewWillAppear");
  -[WidgetJiaoyiViewController upThemes](self, "upThemes");
}

//----- (0000000100D163F2) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController viewDidAppear](WidgetJiaoyiViewController *self, SEL a2)
{
  HXBaseTradeView *v2; // rax
  HXBaseTradeView *v3; // rbx

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___WidgetJiaoyiViewController;
  -[HXBaseViewController viewDidAppear](&v6, "viewDidAppear");
  v2 = -[WidgetJiaoyiViewController jyView](self, "jyView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseTradeView didAppear](v3, "didAppear");
  -[WidgetJiaoyiViewController upPageViewSelectedIndexForActiveUser](self, "upPageViewSelectedIndexForActiveUser");
  v4 = +[TradeAndQuotaMutual shareInstance](&OBJC_CLASS___TradeAndQuotaMutual, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setIsInQuotaQuickTradeVisible:", 1LL);
  if ( (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll") )
    -[WidgetJiaoyiViewController setupButtton](self, "setupButtton");
}

//----- (0000000100D164DE) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController viewDidDisappear](WidgetJiaoyiViewController *self, SEL a2)
{
  HXBaseTradeView *v2; // rax
  HXBaseTradeView *v3; // rbx

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___WidgetJiaoyiViewController;
  -[HXBaseViewController viewDidDisappear](&v6, "viewDidDisappear");
  v2 = -[WidgetJiaoyiViewController jyView](self, "jyView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseTradeView didDisappear](v3, "didDisappear");
  -[WidgetJiaoyiViewController upPageViewSelectedIndexForActiveUser](self, "upPageViewSelectedIndexForActiveUser");
  v4 = +[TradeAndQuotaMutual shareInstance](&OBJC_CLASS___TradeAndQuotaMutual, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setIsInQuotaQuickTradeVisible:", 0LL);
}

//----- (0000000100D165A3) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setStockCode:market:buySell:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3,
        id a4,
        int a5)
{

  v6 = objc_retain(a3);
  v7 = objc_retain(a4);
  if ( v6
    && _objc_msgSend(v6, "length")
    && !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v6, v8, CFSTR("NUL"))
    && (unsigned __int8)+[tools isValidCodeLen:](&OBJC_CLASS___tools, "isValidCodeLen:", v6) )
  {
    v9 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "getActiveUserByType:", 1LL);
    objc_retainAutoreleasedReturnValue(v11);
    if ( v12 )
    {
      v13 = *(void **)&self->super.super.super._reserved;
      if ( v13 )
        _objc_msgSend(v13, "setStockCode:market:", v6, v7);
    }
  }
}

//----- (0000000100D16719) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setJiaGeTFValueWithStrPrice:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3)
{

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v5 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "getActiveUserByType:", 1LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( v8 )
    {
      v10 = *(void **)&self->super.super.super._reserved;
      if ( v10 )
        _objc_msgSend(v10, "refreshJiaGeTextFieldWithPrice:", v4);
    }
  }
}

//----- (0000000100D16841) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController expandWidgetJY](WidgetJiaoyiViewController *self, SEL a2)
{
  if ( !(unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll") )
  {
    -[WidgetJiaoyiViewController switchBtnClicked:](self, "switchBtnClicked:", 0LL);
    -[WidgetJiaoyiViewController setupButtton](self, "setupButtton");
  }
}

//----- (0000000100D1688A) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController switchBtnClicked:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  signed __int8 v6; // al
  id (*v8)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  SEL v29; // r12
  DragTextField *v34; // rax
  DragTextField *v35; // rax
  DragTextField *v39; // rax
  DragTextField *v40; // rax
  id (*v44)(id, SEL, ...); // r12
  id (*v49)(id, SEL, ...); // r12
  id (*v51)(id, SEL, ...); // r12
  id (*v53)(id, SEL, ...); // r12

  v3 = (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll", a3);
  v4(self, "setShowAll:", v3 == 0);
  v6 = (unsigned __int8)v5(self, "showAll");
  v7(self, "switchContainerViewState:", (unsigned int)v6);
  if ( (unsigned __int8)v8(self, "showAll") )
  {
    if ( (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") )
    {
      v11 = v10(&OBJC_CLASS___HXThemeManager, "riseTextColor");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = v13(self, "mairuTextField");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v15, "setBorderColor:", v12);
      v16(v12);
      v17 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v20 = v19(self, "mairuTextField");
    }
    else
    {
      v42 = v10(&OBJC_CLASS___HXThemeManager, "markedTextColor");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v45 = v44(self, "maichuTextField");
      v46 = objc_retainAutoreleasedReturnValue(v45);
      _objc_msgSend(v46, "setBorderColor:", v43);
      v47(v43);
      v48 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
      v18 = objc_retainAutoreleasedReturnValue(v48);
      v20 = v49(self, "maichuTextField");
    }
    v21 = objc_retainAutoreleasedReturnValue(v20);
    _objc_msgSend(v21, "setBackgroundColor:", v18);
    v50 = *(void **)&self->super.super.super._reserved;
    v52 = v51(self, "stockCode");
    v38 = objc_retainAutoreleasedReturnValue(v52);
    v54 = v53(self, "market");
    v55 = objc_retainAutoreleasedReturnValue(v54);
    _objc_msgSend(v50, "setStockCode:market:", v38, v55);
  }
  else
  {
    v22 = v9(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v25 = (void *)v24(self, "mairuTextField");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    _objc_msgSend(v26, "setBorderColor:", v23);
    v27 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v30 = _objc_msgSend(self, v29);
    v31 = objc_retainAutoreleasedReturnValue(v30);
    _objc_msgSend(v31, "setBackgroundColor:", v28);
    v32 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    -[JYCustomTextField setBorderColor:](v35, "setBorderColor:", v33);
    v37 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
    v38 = objc_retainAutoreleasedReturnValue(v37);
    v39 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    -[JYCustomTextField setBackgroundColor:](v40, "setBackgroundColor:", v38);
  }
  -[WidgetJiaoyiViewController sendZhanKaiZheDieMaiDian](self, "sendZhanKaiZheDieMaiDian");
}

//----- (0000000100D16C92) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController switchContainerViewState:](WidgetJiaoyiViewController *self, SEL a2, char a3)
{
  __CFString *v25; // rbx
  HXButton *v26; // rax
  HXButton *v27; // r15
  int v41; // [rsp+DCh] [rbp-34h]

  v3 = 0LL;
  v41 = a3;
  v4 = _objc_msgSend(self, "view");
  v5 = (const char *)objc_retainAutoreleasedReturnValue(v4);
  v7 = (char *)v5;
  if ( v5 )
  {
    objc_msgSend_stret(&v36, v5);
    v8 = *((double *)&v37 + 1);
  }
  else
  {
    v37 = 0LL;
    v36 = 0LL;
    v8 = 0.0;
  }
  *(double *)&v42 = v8;
  LOBYTE(v3) = v6;
  v38 = dbl_1010DF5C0[v3];
  v35 = v38 - *(double *)&v42;
  if ( v38 - *(double *)&v42 != 0.0 )
  {
    v9 = _objc_msgSend(self, "view");
    *(double *)&v42 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v9));
    v10 = _objc_msgSend(self, "view");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v39 = v11;
    if ( v11 )
    {
      objc_msgSend_stret(&v36, (SEL)v11, "frame");
      v12 = v36;
    }
    else
    {
      v37 = 0LL;
      v36 = 0LL;
      v12 = 0LL;
    }
    *(_QWORD *)&v40 = v12;
    v13 = _objc_msgSend(self, "view");
    v14 = (const char *)objc_retainAutoreleasedReturnValue(v13);
    v15 = (char *)v14;
    if ( v14 )
    {
      objc_msgSend_stret(v29, v14, "frame");
      v16 = *((_QWORD *)&v29[0] + 1);
    }
    else
    {
      memset(v29, 0, sizeof(v29));
      v16 = 0LL;
    }
    *((_QWORD *)&v40 + 1) = v16;
    v17 = _objc_msgSend(self, "view");
    v18 = (const char *)objc_retainAutoreleasedReturnValue(v17);
    v19 = (char *)v18;
    if ( v18 )
    {
      objc_msgSend_stret(&v30, v18, "frame");
      v20 = v31;
    }
    else
    {
      v31 = 0LL;
      v30 = 0LL;
      v20 = 0LL;
    }
    v32 = v40;
    v33 = v20;
    v34 = v38;
    _objc_msgSend(v42, "setFrame:");
    v21 = -[PanKouModularBaseViewController viewResizeCallBackBlock](self, "viewResizeCallBackBlock");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    if ( v22 )
    {
      v23 = -[PanKouModularBaseViewController viewResizeCallBackBlock](self, "viewResizeCallBackBlock");
      v24 = (void (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v23);
      v24[2](v24, v35);
    }
  }
  v25 = CFSTR("收起");
  if ( !(_BYTE)v41 )
    v25 = CFSTR("展开");
  v26 = -[WidgetJiaoyiViewController switchBtn](self, "switchBtn");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28(v27, "setTitle:", v25);
}

//----- (0000000100D16FE2) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController clickBuyOrSellAciton:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  NSNumber *v7; // rax
  NSNumber *v8; // rbx

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    v5 = objc_retain(v3);
    v6 = (char *)_objc_msgSend(v5, "tag");
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v6 - 100);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "boolValue");
    if ( (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll") )
    {
      v9 = (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected");
      if ( v9 == v10 )
      {
        -[WidgetJiaoyiViewController switchBtnClicked:](self, "switchBtnClicked:", 0LL);
      }
      else
      {
        -[WidgetJiaoyiViewController setIsMairuSelected:](self, "setIsMairuSelected:", (unsigned int)v10);
        -[WidgetJiaoyiViewController setupButtton](self, "setupButtton");
      }
    }
    else
    {
      -[WidgetJiaoyiViewController switchBtnClicked:](self, "switchBtnClicked:", 0LL);
      -[WidgetJiaoyiViewController setIsMairuSelected:](self, "setIsMairuSelected:", (unsigned int)v11);
      -[WidgetJiaoyiViewController setupButtton](self, "setupButtton");
    }
    -[WidgetJiaoyiViewController sendMairuMaichuMaiDian](self, "sendMairuMaichuMaiDian");
  }
}

//----- (0000000100D1715C) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setupButtton](WidgetJiaoyiViewController *self, SEL a2)
{
  DragTextField *v4; // rax
  DragTextField *v5; // rbx
  DragTextField *v8; // rax
  DragTextField *v9; // rbx
  SEL v10; // r12
  DragTextField *v13; // rax
  DragTextField *v14; // rbx
  DragTextField *v17; // rax
  DragTextField *v18; // rbx
  SEL v19; // r12
  signed __int8 v21; // al

  if ( (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") )
    v2 = +[HXThemeManager riseTextColor](&OBJC_CLASS___HXThemeManager, "riseTextColor");
  else
    v2 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[JYCustomTextField setBorderColor:](v5, "setBorderColor:", v3);
  if ( (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") )
    v6 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  else
    v6 = +[HXThemeManager markedTextColor](&OBJC_CLASS___HXThemeManager, "markedTextColor");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, v10, v7);
  if ( (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") )
    v11 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  else
    v11 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = -[WidgetJiaoyiViewController mairuTextField](self, "mairuTextField");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[JYCustomTextField setBackgroundColor:](v14, "setBackgroundColor:", v12);
  if ( (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") )
    v15 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  else
    v15 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17 = -[WidgetJiaoyiViewController maichuTextField](self, "maichuTextField");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v18, v19, v16);
  v20 = *(void **)&self->super.super.super._reserved;
  v21 = (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected");
  _objc_msgSend(v20, "clickMaiMaiChangeBtn:", (unsigned int)v21);
}

//----- (0000000100D173B4) ----------------------------------------------------
char __cdecl -[WidgetJiaoyiViewController isVisible](WidgetJiaoyiViewController *self, SEL a2)
{
  HXBaseTradeView *v2; // rax
  HXBaseTradeView *v3; // rbx

  v2 = -[WidgetJiaoyiViewController jyView](self, "jyView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[HXBaseTradeView isVisible](v3, "isVisible");
  return v4;
}

//----- (0000000100D1740D) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController requestForModularData:market:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  NSArray *v11; // rax
  NSArray *v12; // rax
  _QWORD v16[9]; // [rsp+8h] [rbp-78h] BYREF

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = v7;
  if ( v7 )
  {
    v9 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
    if ( v5 )
    {
      if ( !v9 && !(unsigned __int8)_objc_msgSend(v5, v10, &charsToLeaveEscaped) )
      {
        v16[0] = CFSTR("USHA");
        v16[1] = CFSTR("USHD");
        v16[2] = CFSTR("USHJ");
        v16[3] = CFSTR("USHT");
        v16[4] = CFSTR("USHP");
        v16[5] = CFSTR("USZA");
        v16[6] = CFSTR("USZD");
        v16[7] = CFSTR("USZJ");
        v16[8] = CFSTR("USZP");
        v11 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v16, 9LL);
        v12 = objc_retainAutoreleasedReturnValue(v11);
        v13 = (unsigned __int8)_objc_msgSend(v12, "containsObject:", v8);
        if ( v13 )
        {
          -[HXBaseViewController setStockCode:](self, "setStockCode:", v5);
          -[HXBaseViewController setMarket:](self, "setMarket:", v8);
          if ( *(_QWORD *)&self->super.super.super._reserved )
          {
            if ( (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll") )
              _objc_msgSend(
                *(id *)((char *)&self->super.super.super.super.super.isa + v15),
                "setStockCode:market:",
                v5,
                v8);
          }
        }
      }
    }
  }
}

//----- (0000000100D1760E) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController shouldReloadTradeData:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  HXBaseTradeView *v3; // rax
  HXBaseTradeView *v4; // rbx

  if ( (unsigned __int8)-[WidgetJiaoyiViewController isVisible](self, "isVisible", a3) )
  {
    v3 = -[WidgetJiaoyiViewController jyView](self, "jyView");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    -[HXBaseTradeView reloadTradeData](v4, "reloadTradeData");
  }
}

//----- (0000000100D1767B) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController shouldReloadThemes:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  HXBaseTradeView *v3; // rax
  HXBaseTradeView *v4; // rbx

  v3 = -[WidgetJiaoyiViewController jyView](self, "jyView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseTradeView reloadThemes](v4, "reloadThemes");
}

//----- (0000000100D176CC) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController shouldClearTradeDataWithTradeLogin:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3)
{
  HXBaseTradeView *v3; // rax
  HXBaseTradeView *v4; // rbx

  v3 = -[WidgetJiaoyiViewController jyView](self, "jyView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseTradeView shouldClearTradeDataWithType:](v4, "shouldClearTradeDataWithType:", 0LL);
}

//----- (0000000100D1771F) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController shouldClearTradeDataWithAppClose:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3)
{
  HXBaseTradeView *v3; // rax
  HXBaseTradeView *v4; // rbx

  v3 = -[WidgetJiaoyiViewController jyView](self, "jyView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseTradeView shouldClearTradeDataWithType:](v4, "shouldClearTradeDataWithType:", 1LL);
}

//----- (0000000100D17775) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController requestStockInfoSucceedWithStockCode:andMkt:reqMkt:](
        WidgetJiaoyiViewController *self,
        SEL a2,
        id a3,
        id a4,
        char a5)
{
  HXUIFrameManager *v10; // rax
  HXUIFrameManager *v11; // rax
  NSNumber *v16; // rax
  NSDictionary *v19; // rax
  NSDictionary *v20; // rax
  NSNumber *v23; // [rsp+8h] [rbp-68h]
  NSNumber *v27; // [rsp+38h] [rbp-38h]

  v7 = objc_retain(a3);
  v8 = objc_retain(a4);
  if ( v7 )
  {
    if ( _objc_msgSend(v7, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v7, v9, CFSTR("NUL")) )
      {
        if ( (unsigned __int8)+[tools isValidCodeLen:](&OBJC_CLASS___tools, "isValidCodeLen:", v7) )
        {
          v10 = +[HXUIFrameManager sharedInstance](&OBJC_CLASS___HXUIFrameManager, "sharedInstance");
          v11 = objc_retainAutoreleasedReturnValue(v10);
          v12 = -[HXUIFrameManager currentTabbarIndex](v11, "currentTabbarIndex");
          if ( v12 == (id)1 )
          {
            v14 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
            objc_retainAutoreleasedReturnValue(v14);
            v24[0] = (__int64)CFSTR("StockCode");
            v25 = v7;
            v24[1] = (__int64)CFSTR("Market");
            v15 = +[tools safeStringValue:](&OBJC_CLASS___tools, "safeStringValue:", v8);
            v26 = objc_retain(v15);
            v24[2] = (__int64)CFSTR("ReqMkt");
            v22 = v26;
            v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", (unsigned int)a5);
            v18 = v17;
            v23 = objc_retain(v16);
            v27 = v23;
            v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v25, v24, 3LL);
            v20 = objc_retainAutoreleasedReturnValue(v19);
            _objc_msgSend(v18, "postNotificationName:object:", CFSTR("autoSwitchGeGuFenShiNotification"), v20);
          }
        }
      }
    }
  }
}

//----- (0000000100D17A09) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController upThemes](WidgetJiaoyiViewController *self, SEL a2)
{
  HXDragAndDropBaseView *v2; // rax
  HXDragAndDropBaseView *v3; // rbx
  HXDragAndDropBaseView *v5; // rax
  HXDragAndDropBaseView *v6; // rbx
  id WeakRetained; // rbx

  v2 = -[PanKouModularBaseViewController titleContainerView](self, "titleContainerView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( v3 )
  {
    v4 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    objc_retainAutoreleasedReturnValue(v4);
    v5 = -[PanKouModularBaseViewController titleContainerView](self, "titleContainerView");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[HXBaseView setBackgroundColor:](v6, "setBackgroundColor:", v7);
  }
  WeakRetained = objc_loadWeakRetained((id *)&self->super._correlatedCode);
  if ( WeakRetained )
  {
    v10 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    objc_retainAutoreleasedReturnValue(v10);
    v11 = objc_loadWeakRetained((id *)&self->super._correlatedCode);
    _objc_msgSend(v11, "setJyBackgroundColor:", v12);
  }
  if ( *(_QWORD *)&self->super.super._shouldRefresh )
  {
    v14 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(*(id *)&self->super.super._shouldRefresh, "setJyBackgroundColor:", v15);
  }
  v17 = objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
  if ( v17 )
  {
    v18 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    objc_retainAutoreleasedReturnValue(v18);
    v19 = objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
    _objc_msgSend(v19, "setJyBackgroundColor:", v20);
  }
}

//----- (0000000100D17C15) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController upPageViewSelectedIndexForActiveUser](
        WidgetJiaoyiViewController *self,
        SEL a2)
{

  v2 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getActiveUserByType:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v6 = _objc_msgSend(v5, "getCacheDataObject");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "setNeedRefreshQuotaData:", 0LL);
    v8 = _objc_msgSend(v5, "getJYViewStateModel");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(v9, "nJYInQuotaQuickTradeViewController_PageView_SelectedIndex");
  }
}

//----- (0000000100D17D16) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController addNotification](WidgetJiaoyiViewController *self, SEL a2)
{
  SEL v4; // r12
  SEL v7; // r12
  SEL v10; // r12

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "addObserver:selector:name:object:", self, "shouldReloadTradeData:", CFSTR("wt_refresh_data"), 0LL);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(
    v6,
    "addObserver:selector:name:object:",
    self,
    "shouldReloadThemes:",
    CFSTR("JYNotification_UserReloadThemes"),
    0LL);
  v8 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(
    v9,
    "addObserver:selector:name:object:",
    self,
    "shouldClearTradeDataWithTradeLogin:",
    CFSTR("wtlogin_out"),
    0LL);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(
    v12,
    "addObserver:selector:name:object:",
    self,
    "shouldClearTradeDataWithAppClose:",
    CFSTR("app_will_close"),
    0LL);
}

//----- (0000000100D17E5C) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController dealloc](WidgetJiaoyiViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___WidgetJiaoyiViewController;
  -[PanKouModularBaseViewController dealloc](&v4, "dealloc");
}

//----- (0000000100D17F1B) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController sendZhanKaiZheDieMaiDian](WidgetJiaoyiViewController *self, SEL a2)
{
  bool v2; // zf
  __CFString *v3; // rcx

  v2 = (unsigned __int8)-[WidgetJiaoyiViewController showAll](self, "showAll") == 0;
  v3 = CFSTR("Widget交易_展开");
  if ( v2 )
    v3 = CFSTR("Widget交易_收起");
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    v3,
    0LL,
    1LL);
}

//----- (0000000100D17F6A) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController sendMairuMaichuMaiDian](WidgetJiaoyiViewController *self, SEL a2)
{
  bool v2; // zf
  __CFString *v3; // rcx

  v2 = (unsigned __int8)-[WidgetJiaoyiViewController isMairuSelected](self, "isMairuSelected") == 0;
  v3 = CFSTR("Widget交易_切换买入");
  if ( v2 )
    v3 = CFSTR("Widget交易_切换卖出");
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    v3,
    0LL,
    1LL);
}

//----- (0000000100D17FB9) ----------------------------------------------------
JYInQuotaQuickTradeViewControllerDelegate *__cdecl -[WidgetJiaoyiViewController delegate](
        WidgetJiaoyiViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._market);
  return (JYInQuotaQuickTradeViewControllerDelegate *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D17FD2) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setDelegate:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._market, a3);
}

//----- (0000000100D17FE6) ----------------------------------------------------
HXBaseTradeView *__cdecl -[WidgetJiaoyiViewController jyView](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._contentsObjMArr);
  return (HXBaseTradeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D17FFF) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setJyView:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._contentsObjMArr, a3);
}

//----- (0000000100D18013) ----------------------------------------------------
HXButton *__cdecl -[WidgetJiaoyiViewController switchBtn](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._controllerID);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D1802C) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setSwitchBtn:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._controllerID, a3);
}

//----- (0000000100D18040) ----------------------------------------------------
DragTextField *__cdecl -[WidgetJiaoyiViewController mairuTextField](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._paramsMDic);
  return (DragTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D18059) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setMairuTextField:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._paramsMDic, a3);
}

//----- (0000000100D1806D) ----------------------------------------------------
DragTextField *__cdecl -[WidgetJiaoyiViewController maichuTextField](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._noRecordFrame);
  return (DragTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D18086) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setMaichuTextField:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._noRecordFrame, a3);
}

//----- (0000000100D1809A) ----------------------------------------------------
HXBaseTradeView *__cdecl -[WidgetJiaoyiViewController aDetailContainerView](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._titleContainerView);
  return (HXBaseTradeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D180B3) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setADetailContainerView:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._titleContainerView, a3);
}

//----- (0000000100D180C7) ----------------------------------------------------
HXBaseTradeView *__cdecl -[WidgetJiaoyiViewController aUserInfoRoomView](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._correlatedCode);
  return (HXBaseTradeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D180E0) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setAUserInfoRoomView:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._correlatedCode, a3);
}

//----- (0000000100D180F4) ----------------------------------------------------
HXBaseTradeView *__cdecl -[WidgetJiaoyiViewController aMaimaiRoomView](WidgetJiaoyiViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
  return (HXBaseTradeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100D1810D) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setAMaimaiRoomView:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super._refeshDitailsPageBlock, a3);
}

//----- (0000000100D18121) ----------------------------------------------------
char __cdecl -[WidgetJiaoyiViewController showAll](WidgetJiaoyiViewController *self, SEL a2)
{
  return (char)self->super.super._stockCode;
}

//----- (0000000100D18132) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setShowAll:](WidgetJiaoyiViewController *self, SEL a2, char a3)
{
  LOBYTE(self->super.super._stockCode) = a3;
}

//----- (0000000100D18142) ----------------------------------------------------
char __cdecl -[WidgetJiaoyiViewController isMairuSelected](WidgetJiaoyiViewController *self, SEL a2)
{
  return BYTE1(self->super.super._stockCode);
}

//----- (0000000100D18153) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setIsMairuSelected:](WidgetJiaoyiViewController *self, SEL a2, char a3)
{
  BYTE1(self->super.super._stockCode) = a3;
}

//----- (0000000100D18163) ----------------------------------------------------
NSString *__cdecl -[WidgetJiaoyiViewController notificationNameString](WidgetJiaoyiViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 200LL, 0);
}

//----- (0000000100D18176) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setNotificationNameString:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 200LL);
}

//----- (0000000100D18187) ----------------------------------------------------
NSString *__cdecl -[WidgetJiaoyiViewController currentMarket](WidgetJiaoyiViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 208LL, 0);
}

//----- (0000000100D1819A) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setCurrentMarket:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 208LL);
}

//----- (0000000100D181AB) ----------------------------------------------------
NSDictionary *__cdecl -[WidgetJiaoyiViewController marketPageRelationDic](WidgetJiaoyiViewController *self, SEL a2)
{
  return (NSDictionary *)objc_getProperty(self, a2, 216LL, 0);
}

//----- (0000000100D181BE) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController setMarketPageRelationDic:](WidgetJiaoyiViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 216LL);
}

//----- (0000000100D181CF) ----------------------------------------------------
void __cdecl -[WidgetJiaoyiViewController .cxx_destruct](WidgetJiaoyiViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._modularVisibleType, 0LL);
  objc_storeStrong(&self->super._viewResizeCallBackBlock, 0LL);
  objc_storeStrong(&self->super._changeFrameBtnClickedBlock, 0LL);
  objc_destroyWeak(&self->super._refeshDitailsPageBlock);
  objc_destroyWeak((id *)&self->super._correlatedCode);
  objc_destroyWeak((id *)&self->super._titleContainerView);
  objc_destroyWeak((id *)&self->super._noRecordFrame);
  objc_destroyWeak((id *)&self->super.super._paramsMDic);
  objc_destroyWeak((id *)&self->super.super._controllerID);
  objc_destroyWeak((id *)&self->super.super._contentsObjMArr);
  objc_destroyWeak((id *)&self->super.super._market);
  objc_storeStrong((id *)&self->super.super._shouldRefresh, 0LL);
  objc_storeStrong((id *)&self->super.super.super._reserved, 0LL);
}

