void __cdecl -[XinSanBanIndexComponentStocksViewController viewDidLoad](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // r15
  NSNumber *v7; // rax
  NSNumber *v8; // r14
  NSArray *v9; // rax
  NSArray *v10; // rbx
  _QWORD v19[5]; // [rsp+28h] [rbp-58h] BYREF

  v15.receiver = self;
  v15.super_class = (Class)&OBJC_CLASS___XinSanBanIndexComponentStocksViewController;
  -[BanKuaiGeGuBaseTableViewController viewDidLoad](&v15, "viewDidLoad");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v16 = objc_retainAutoreleasedReturnValue(v2);
  v19[0] = v16;
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
  v17 = objc_retainAutoreleasedReturnValue(v3);
  v19[1] = v17;
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
  v18 = objc_retainAutoreleasedReturnValue(v4);
  v19[2] = v18;
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v19[3] = v6;
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 48LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v19[4] = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v19, 5LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v11, "setDataTypes:", v10);
  _objc_msgSend(v12, "setSelectedRowIndexs:", 0LL);
  _objc_msgSend(v13, "addNotificationForSelf");
  _objc_msgSend(v14, "setViewState");
}

//----- (0000000100589C4D) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController viewDidDisappear](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XinSanBanIndexComponentStocksViewController;
  -[HXBaseTableViewController viewDidDisappear](&v2, "viewDidDisappear");
  -[BanKuaiGeGuBaseTableViewController deleteOrder](self, "deleteOrder");
}

//----- (0000000100589C8E) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController dealloc](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  NSTimer *v2; // rax
  NSTimer *v3; // rbx

  v2 = -[XinSanBanIndexComponentStocksViewController countDownTimerForScroll](self, "countDownTimerForScroll");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "invalidate");
  -[XinSanBanIndexComponentStocksViewController setCountDownTimerForScroll:](self, "setCountDownTimerForScroll:", 0LL);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "removeObserver:", self);
  v6(v5);
  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___XinSanBanIndexComponentStocksViewController;
  -[BanKuaiGeGuBaseTableViewController dealloc](&v7, "dealloc");
}

//----- (0000000100589D42) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController addNotificationForSelf](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  HXBaseTableView *v4; // rax
  HXBaseTableView *v5; // rax
  NSNotificationName v12; // r15
  NSNotificationName v19; // [rsp+0h] [rbp-30h]

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v19 = NSViewBoundsDidChangeNotification;
  v4 = -[HXBaseTableViewController myTable](self, "myTable");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "enclosingScrollView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "contentView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v3, "addObserver:selector:name:object:", self, "tableIsScrolling:", v19, v9);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  objc_retainAutoreleasedReturnValue(v11);
  v12 = NSViewFrameDidChangeNotification;
  v13 = _objc_msgSend(self, "view");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v15, "addObserver:selector:name:object:", self, "viewFrameDidChange:", v12, v14);
  v17 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(
    v18,
    "addObserver:selector:name:object:",
    self,
    "actionForFocusDidChange:",
    CFSTR("FocusDidChangedBetweenViews"),
    0LL);
}

//----- (0000000100589EDF) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setViewState](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // rbx
  HXBaseView *v7; // rax
  HXBaseView *v8; // rbx
  HXBaseView *v9; // rax
  HXBaseView *v10; // rbx
  HXBaseView *v11; // rax
  HXBaseView *v12; // rbx
  HXBaseView *v14; // rax
  HXBaseView *v15; // rbx
  NSTextField *v20; // rax
  NSTextField *v21; // rbx

  v2 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[XinSanBanIndexComponentStocksViewController titleContainerView](self, "titleContainerView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseView setBackgroundColor:](v4, "setBackgroundColor:", v5);
  v7 = -[XinSanBanIndexComponentStocksViewController titleContainerView](self, "titleContainerView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[HXBaseView setTopBorder:](v8, "setTopBorder:", 1LL);
  v9 = -[XinSanBanIndexComponentStocksViewController titleContainerView](self, "titleContainerView");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[HXBaseView setBottomBorder:](v10, "setBottomBorder:", 1LL);
  v11 = -[XinSanBanIndexComponentStocksViewController titleContainerView](self, "titleContainerView");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  -[HXBaseView setBorderWidth:](v12, "setBorderWidth:", 1.0);
  v13 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v13);
  v14 = -[XinSanBanIndexComponentStocksViewController titleContainerView](self, "titleContainerView");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  -[HXBaseView setBorderColor:](v15, "setBorderColor:", v16);
  v18 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = -[XinSanBanIndexComponentStocksViewController titleTF](self, "titleTF");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  _objc_msgSend(v21, "setTextColor:", v19);
}

//----- (000000010058A099) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController tableViewSelectionIsChanging:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  HXBaseScrollView *v3; // rax
  HXBaseScrollView *v4; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___XinSanBanIndexComponentStocksViewController;
  -[BanKuaiGeGuBaseTableViewController tableViewSelectionIsChanging:](&v5, "tableViewSelectionIsChanging:", a3);
  v3 = -[XinSanBanIndexComponentStocksViewController topTenScrollView](self, "topTenScrollView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseScrollView setDisEnableScroll:](v4, "setDisEnableScroll:", 0LL);
}

//----- (000000010058A105) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController tableIsScrolling:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  HXBaseTableView *v3; // rax
  HXBaseTableView *v4; // r14
  bool v12; // zf
  NSTimer *v14; // rax
  NSTimer *v15; // rbx
  id (*v18)(id, SEL, ...); // r12

  v3 = -[HXBaseTableViewController myTable](self, "myTable", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "enclosingScrollView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "contentView");
  v8 = (const char *)objc_retainAutoreleasedReturnValue(v7);
  v9 = (char *)v8;
  if ( v8 )
  {
    objc_msgSend_stret(v22, v8, "visibleRect");
    v10 = *((double *)v22 + 1);
  }
  else
  {
    memset(v22, 0, sizeof(v22));
    v10 = 0.0;
  }
  v23 = v10;
  -[XinSanBanIndexComponentStocksViewController visibleY](self, "visibleY");
  v12 = v10 == v23;
  v13 = v23;
  if ( !v12 )
  {
    v14 = -[XinSanBanIndexComponentStocksViewController countDownTimerForScroll](self, "countDownTimerForScroll", v23);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16(v15, "invalidate");
    v17(self, "setCountDownTimerForScroll:", 0LL);
    v19 = v18(
            &OBJC_CLASS___NSTimer,
            "scheduledTimerWithTimeInterval:target:selector:userInfo:repeats:",
            self,
            "requestForMyTable",
            0LL,
            0LL,
            0.1);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21(self, "setCountDownTimerForScroll:", v20);
    v13 = v23;
  }
  -[XinSanBanIndexComponentStocksViewController setVisibleY:](self, "setVisibleY:", v13);
}

//----- (000000010058A288) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController viewFrameDidChange:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(self, "view");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v4);
  if ( v4 == v6 )
  {
    if ( (unsigned __int8)-[HXBaseTableViewController tableHasData](self, "tableHasData") )
      -[BanKuaiGeGuBaseTableViewController requestForMyTable](self, "requestForMyTable");
  }
}

//----- (000000010058A318) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController actionForFocusDidChange:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  HXBaseTableView *v14; // rax
  HXBaseTableView *v15; // rbx

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "object");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  v7 = (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6);
  if ( v7 )
  {
    v9 = _objc_msgSend(v8, "object");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(self, "className");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", v12);
    if ( !v13 )
    {
      -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", 0LL);
      v14 = -[HXBaseTableViewController myTable](self, "myTable");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v15, "deselectAll:", 0LL);
    }
  }
}

//----- (000000010058A447) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController requestForModularData:market:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  int *v8; // rax

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( _objc_msgSend(v5, "length") && _objc_msgSend(v6, "length") )
  {
    if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("899001")) )
    {
      v8 = (int *)&unk_1010D4A88;
    }
    else
    {
      if ( !(unsigned __int8)_objc_msgSend(v5, v7, CFSTR("899002")) )
        goto LABEL_9;
      v8 = (int *)&unk_1010D4A8C;
    }
    if ( *v8 > 0 )
      -[BanKuaiGeGuBaseTableViewController requestForBanKuaiGeGuWithBlockID:](self, "requestForBanKuaiGeGuWithBlockID:");
  }
LABEL_9:
}

//----- (000000010058A51A) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController disableScroll](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  HXBaseScrollView *v2; // rax
  HXBaseScrollView *v3; // rbx

  v2 = -[XinSanBanIndexComponentStocksViewController topTenScrollView](self, "topTenScrollView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseScrollView setDisEnableScroll:](v3, "setDisEnableScroll:", 1LL);
}

//----- (000000010058A55C) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController requestForTableViewData](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  -[BanKuaiGeGuBaseTableViewController requestForMyTable](self, "requestForMyTable");
}

//----- (000000010058A56E) ----------------------------------------------------
HXBaseView *__cdecl -[XinSanBanIndexComponentStocksViewController titleContainerView](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._selectedRowDidChange);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010058A587) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setTitleContainerView:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._selectedRowDidChange, a3);
}

//----- (000000010058A59B) ----------------------------------------------------
NSTextField *__cdecl -[XinSanBanIndexComponentStocksViewController titleTF](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._visibleY);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010058A5B4) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setTitleTF:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._visibleY, a3);
}

//----- (000000010058A5C8) ----------------------------------------------------
HXBaseScrollView *__cdecl -[XinSanBanIndexComponentStocksViewController topTenScrollView](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._visibleX);
  return (HXBaseScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010058A5E1) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setTopTenScrollView:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._visibleX, a3);
}

//----- (000000010058A5F5) ----------------------------------------------------
double __cdecl -[XinSanBanIndexComponentStocksViewController visibleY](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  return *(double *)&self->super._requestFromZero;
}

//----- (000000010058A607) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setVisibleY:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        double a3)
{
  *(double *)&self->super._requestFromZero = a3;
}

//----- (000000010058A619) ----------------------------------------------------
NSTimer *__cdecl -[XinSanBanIndexComponentStocksViewController countDownTimerForScroll](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  return (NSTimer *)self->super._begin;
}

//----- (000000010058A62A) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController setCountDownTimerForScroll:](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._begin, a3);
}

//----- (000000010058A63E) ----------------------------------------------------
void __cdecl -[XinSanBanIndexComponentStocksViewController .cxx_destruct](
        XinSanBanIndexComponentStocksViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super._begin, 0LL);
  objc_destroyWeak((id *)&self->super._visibleX);
  objc_destroyWeak((id *)&self->super._visibleY);
  objc_destroyWeak((id *)&self->super._selectedRowDidChange);
}

