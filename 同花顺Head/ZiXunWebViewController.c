void __cdecl -[ZiXunWebViewController viewDidLoad](ZiXunWebViewController *self, SEL a2)
{
  WebView *v2; // rax
  WebView *v3; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12

  v22.receiver = self;
  v22.super_class = (Class)&OBJC_CLASS___ZiXunWebViewController;
  -[HXBaseViewController viewDidLoad](&v22, "viewDidLoad");
  v2 = -[ZiXunWebViewController webView](self, "webView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "setPolicyDelegate:", self);
  v6 = v5(self, "webView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "setUIDelegate:", self);
  v10 = v9(self, "webView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12(v11, "setFrameLoadDelegate:", self);
  v14 = v13(self, "initialUrlString");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(self, "loadUrl:", v15);
  v17(self, "setDebugEnabled");
  v19 = v18(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21(v20, "addObserver:selector:name:object:", self, "setDebugEnabled", off_1012E3790, 0LL);
}

//----- (000000010061FBEC) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController dealloc](ZiXunWebViewController *self, SEL a2)
{
  WebView *v2; // rax
  WebView *v3; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12

  v2 = -[ZiXunWebViewController webView](self, "webView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "setPolicyDelegate:", 0LL);
  v6 = v5(self, "webView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "setUIDelegate:", 0LL);
  v10 = v9(self, "webView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12(v11, "setFrameLoadDelegate:", 0LL);
  v13.receiver = self;
  v13.super_class = (Class)&OBJC_CLASS___ZiXunWebViewController;
  -[HXBaseViewController dealloc](&v13, "dealloc");
}

//----- (000000010061FCBA) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController loadUrl:](ZiXunWebViewController *self, SEL a2, id a3)
{
  WebView *v5; // rax
  NSURL *v9; // rax
  NSURL *v10; // r14
  NSURLRequest *v11; // rax
  NSURLRequest *v12; // rbx
  WebView *v13; // [rsp+0h] [rbp-30h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v5 = -[ZiXunWebViewController webView](self, "webView");
    v13 = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(v13, "mainFrame");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v8);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = (NSURLRequest *)_objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v10);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v7, "loadRequest:", v12);
  }
}

//----- (000000010061FDB5) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController setDebugEnabled](ZiXunWebViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  _BOOL8 v6; // rbx
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12

  v2 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (unsigned __int8)v4(v3, "boolForKey:", off_1012E3788);
  v6 = v5 != 0;
  v8 = v7(self, "webView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = v10(v9, "preferences");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = v13(&OBJC_CLASS___NSNumber, "numberWithBool:", v6);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(v12, "setValue:forKey:", v15, CFSTR("developerExtrasEnabled"));
}

//----- (000000010061FE9F) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController use](ZiXunWebViewController *self, SEL a2)
{
  ;
}

//----- (000000010061FEA5) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController download](ZiXunWebViewController *self, SEL a2)
{
  ;
}

//----- (000000010061FEAB) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController ignore](ZiXunWebViewController *self, SEL a2)
{
  ;
}

//----- (000000010061FEB1) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController webView:decidePolicyForNewWindowAction:request:newFrameName:decisionListener:](
        ZiXunWebViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6,
        id a7)
{
  id (*v10)(id, SEL, ...); // r12

  v7 = objc_retain(a5);
  v8 = _objc_msgSend(&OBJC_CLASS___NSWorkspace, "sharedWorkspace");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = v10(v7, "URL");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13(v9, "openURL:", v12);
}

//----- (000000010061FF45) ----------------------------------------------------
id __cdecl -[ZiXunWebViewController webView:createWebViewWithRequest:](
        ZiXunWebViewController *self,
        SEL a2,
        id a3,
        id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v8 = _objc_msgSend(v7, "webView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( v9 == v5 )
  {
    v10 = _objc_msgSend(v5, "mainFrame");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "loadRequest:", v6);
    objc_retain(v5);
  }
  return objc_autoreleaseReturnValue(v13);
}

//----- (0000000100620002) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController webView:didCreateJavaScriptContext:forFrame:](
        ZiXunWebViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  WebView *v5; // rax
  WebView *v6; // r14
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12

  v5 = -[ZiXunWebViewController webView](self, "webView", a3, a4, a5);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "mainFrame");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = v10(v9, "javaScriptContext");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = objc_alloc((Class)&OBJC_CLASS___ThsMacJSModel);
  v15 = v14(v13, "init");
  v16(v15, "setJsContext:", v12);
  v17(v12, "setObject:forKeyedSubscript:", v15, CFSTR("ThsMacJSModel"));
  v18(v12, "setExceptionHandler:", &stru_1012E3940);
}

//----- (00000001006200E2) ----------------------------------------------------
void __cdecl sub_1006200E2(id a1, JSContext *a2, JSValue *a3)
{
  ;
}

//----- (00000001006200E8) ----------------------------------------------------
NSString *__cdecl -[ZiXunWebViewController initialUrlString](ZiXunWebViewController *self, SEL a2)
{
  return (NSString *)&self->super.super._topLevelObjects->super.isa;
}

//----- (00000001006200F9) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController setInitialUrlString:](ZiXunWebViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._topLevelObjects, a3);
}

//----- (000000010062010D) ----------------------------------------------------
WebView *__cdecl -[ZiXunWebViewController webView](ZiXunWebViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (WebView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100620126) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController setWebView:](ZiXunWebViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (000000010062013A) ----------------------------------------------------
void __cdecl -[ZiXunWebViewController .cxx_destruct](ZiXunWebViewController *self, SEL a2)
{
  objc_destroyWeak((id *)&self->super.super._editors);
  objc_storeStrong((id *)&self->super.super._topLevelObjects, 0LL);
}

