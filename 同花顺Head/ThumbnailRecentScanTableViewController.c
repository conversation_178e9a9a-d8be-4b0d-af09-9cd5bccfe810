void __cdecl -[ThumbnailRecentScanTableViewController updateTableVCData:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  signed __int8 v11; // al

  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 )
  {
    v6 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("requesttype"));
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "integerValue");
    -[ThumbnailBaseTableViewController setRequestType:](self, "setRequestType:", v8);
    v9 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("isQuickTrade"));
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = (unsigned __int8)_objc_msgSend(v10, "intValue");
    -[ThumbnailRecentScanTableViewController setIsEnterQuickTrade:](self, "setIsEnterQuickTrade:", (unsigned int)v11);
    v12(v10);
    v13 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("TableID"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "longValue");
    -[ThumbnailBaseTableViewController setTableID:](self, "setTableID:", v15);
    v16(v14);
    v17 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("sortid"));
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = _objc_msgSend(v18, "longValue");
    -[HXBaseTableViewController setSortID:](self, "setSortID:", v19);
    v20(v18);
    v21 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("sortorder"));
    if ( objc_retainAutoreleasedReturnValue(v21) )
    {
      v22 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("sortorder"));
      v23 = objc_retainAutoreleasedReturnValue(v22);
      -[HXBaseTableViewController setSortOrder:](self, "setSortOrder:", v23);
    }
    else
    {
      -[HXBaseTableViewController setSortOrder:](self, "setSortOrder:", CFSTR("D"));
    }
    v25 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("sortbegin"));
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27 = _objc_msgSend(v26, "integerValue");
    -[ThumbnailBaseTableViewController setQuotaBegin:](self, "setQuotaBegin:", v27);
    v28 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("sortcount"));
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v30 = _objc_msgSend(v29, "integerValue");
    -[ThumbnailBaseTableViewController setQuotaCount:](self, "setQuotaCount:", v30);
    v31(v29);
    v32 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("Index"));
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = _objc_msgSend(v33, "integerValue");
    -[ThumbnailBaseTableViewController setQuotaIndex:](self, "setQuotaIndex:", v34);
    v35(v33);
    v36 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("SelectedCode"));
    v37 = objc_retainAutoreleasedReturnValue(v36);
    -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v37);
    v38(v37);
    v39 = _objc_msgSend(v5, "objectForKeyedSubscript:", off_1012DF628);
    v40 = objc_retainAutoreleasedReturnValue(v39);
    if ( v40 )
    {
      v41 = _objc_msgSend(v40, "unsignedLongValue");
      -[ThumbnailRecentScanTableViewController setMainGraphType:](self, "setMainGraphType:", v41);
    }
    else
    {
      -[ThumbnailRecentScanTableViewController setMainGraphType:](self, "setMainGraphType:", -1LL);
    }
    v42 = _objc_msgSend(v5, "objectForKeyedSubscript:", off_1012E7230);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    quotaBegin = (void *)self->super._quotaBegin;
    self->super._quotaBegin = (unsigned __int64)v43;
    LOBYTE(self->super._count) = 1;
    -[ThumbnailBaseTableViewController setIsTableSwitched:](self, "setIsTableSwitched:", 1LL);
    -[ThumbnailBaseTableViewController setIsFoucsOfSuperController:](self, "setIsFoucsOfSuperController:", 1LL);
  }
}

//----- (00000001007D233A) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController requestForMyTable](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  if ( !(unsigned __int8)-[ThumbnailBaseTableViewController invalidRequestWhenViewWillBeRemoved](
                           self,
                           "invalidRequestWhenViewWillBeRemoved") )
    -[ThumbnailRecentScanTableViewController requestForRecentScanQuotationDatas](
      self,
      "requestForRecentScanQuotationDatas");
}

//----- (00000001007D2371) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController requestForRecentScanQuotationDatas](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  _BYTE *v4; // r12
  SelfStock *v5; // rax
  SelfStock *v6; // r14

  -[ThumbnailRecentScanTableViewController deleteOrder](self, "deleteOrder");
  if ( _objc_msgSend(v2, "sortID") && _objc_msgSend(v3, "sortID") != (id)12345670 )
  {
    if ( v4[312] )
    {
      v4[312] = 0;
      v4[313] = 1;
    }
    v21 = _objc_msgSend(v4, "sortID");
    _objc_msgSend(v22, "requestSortedRecentScanStocks:", v21);
  }
  else
  {
    v5 = (SelfStock *)+[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = -[SelfStock getRecentlyScanStockWithMarket](v6, "getRecentlyScanStockWithMarket");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v9, "setRecentScanStocks:", v8);
    v11 = _objc_msgSend(v10, "recentScanStocks");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = _objc_msgSend(v12, "count");
    if ( v13 )
    {
      _objc_msgSend(v14, "setRequestRowRange");
      v16 = _objc_msgSend(v15, "recentScanStocks");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18 = _objc_msgSend(v17, "count");
      _objc_msgSend(v19, "setAllCodesNum:", v18);
      _objc_msgSend(v20, "sendRequestForRecentScanStocksQuotationDatas");
    }
    else
    {
      v23 = _objc_msgSend(v14, "myTable");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      _objc_msgSend(v24, "reloadData");
    }
  }
}

//----- (00000001007D254B) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController requestForSortedRecentScanStocksQuotationDatas](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  if ( -[HXBaseTableViewController sortID](self, "sortID") )
  {
    if ( -[HXBaseTableViewController sortID](self, "sortID") != (id)12345670 )
    {
      -[ThumbnailRecentScanTableViewController deleteOrder](self, "deleteOrder");
      -[ThumbnailRecentScanTableViewController setRequestRowRange](self, "setRequestRowRange");
      -[ThumbnailRecentScanTableViewController sendRequestForRecentScanStocksQuotationDatas](
        self,
        "sendRequestForRecentScanStocksQuotationDatas");
    }
  }
}

//----- (00000001007D25B7) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController requestSortedRecentScanStocks:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  SelfStock *v3; // rax
  SelfStock *v4; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  _QWORD v22[5]; // [rsp+0h] [rbp-A0h] BYREF
  id to[2]; // [rsp+28h] [rbp-78h] BYREF
  id location; // [rsp+48h] [rbp-58h] BYREF
  _QWORD v27[2]; // [rsp+50h] [rbp-50h] BYREF
  _QWORD v28[2]; // [rsp+60h] [rbp-40h] BYREF

  v25 = (void *)a3;
  v3 = +[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(v4, "getRecentlyScanStockWithMarket");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( v7 && _objc_msgSend(v7, "count") )
  {
    v9 = v8(v7, "count");
    v10(self, "setAllCodesNum:", v9);
    v12 = v11(self, "getRequestDataTypes:", v25);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = v13;
    if ( v13 && _objc_msgSend(v13, "count") )
    {
      v27[0] = CFSTR("datatype");
      v28[0] = v14;
      v27[1] = CFSTR("StockCodesAndMarket");
      v28[1] = v7;
      v16 = v15(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v28, v27, 2LL);
      v24 = objc_retainAutoreleasedReturnValue(v16);
      objc_initWeak(&location, self);
      v18 = v17(self, "sortRequestModule");
      objc_retain(v18);
      v22[0] = _NSConcreteStackBlock;
      v22[1] = 3254779904LL;
      v22[2] = sub_1007D27EB;
      v22[3] = &unk_1012DC3C0;
      objc_copyWeak(to, &location);
      to[1] = v25;
      v22[4] = self;
      v20 = v19;
      _objc_msgSend(v19, "request:params:callBack:", 4LL, v24, v22);
      objc_destroyWeak(to);
      objc_destroyWeak(&location);
    }
  }
}

//----- (00000001007D27EB) ----------------------------------------------------
void __fastcall sub_1007D27EB(__int64 a1, void *a2)
{
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id WeakRetained; // [rsp+0h] [rbp-30h]

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", *(_QWORD *)(a1 + 48));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(*(id *)(a1 + 32), "sortOrder");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(&OBJC_CLASS___ThumbnailUtils, "sortCodes:bySortID:bySortOrder:", v2, v4, v7);
  objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(WeakRetained, "getCompleteRecentStockArray:", v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(WeakRetained, "setRecentScanStocks:", v12);
  _objc_msgSend(WeakRetained, "requestForSortedRecentScanStocksQuotationDatas");
}

//----- (00000001007D2901) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController order](ThumbnailRecentScanTableViewController *self, SEL a2)
{
  id *v23; // r12
  _QWORD v24[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    v3 = _objc_msgSend(v2, "view");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = _objc_msgSend(v4, "superview");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    if ( v6 )
    {
      v8 = _objc_msgSend(v7, "stockModel");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v10 = _objc_msgSend(v9, "mainStockTableViewDataArray");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      _objc_msgSend(v12, "setOrderCodeList:", v11);
      v14 = _objc_msgSend(v13, "getRequestDataTypes");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      objc_initWeak(location, v16);
      v18 = _objc_msgSend(v17, "tableRequestModule");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v21 = _objc_msgSend(v20, "orderCodeMArray");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v24[0] = _NSConcreteStackBlock;
      v24[1] = 3254779904LL;
      v24[2] = sub_1007D2ACB;
      v24[3] = &unk_1012DAF08;
      objc_copyWeak(&to, location);
      _objc_msgSend(v19, "subscribe:dataTypes:callBack:", v22, v15, v24);
      objc_destroyWeak(v23);
      objc_destroyWeak(location);
    }
  }
}

//----- (00000001007D2ACB) ----------------------------------------------------
void __fastcall sub_1007D2ACB(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithPushData:", v2);
}

//----- (00000001007D2B25) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController deleteOrder](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  HXTableRequestModule *v2; // rax
  HXTableRequestModule *v3; // r14

  v2 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "orderCodeMArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXTableRequestModule dissubscribe:](v3, "dissubscribe:", v6);
  v8 = _objc_msgSend(v7, "orderCodeMArray");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "removeAllObjects");
}

//----- (00000001007D2BC8) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailRecentScanTableViewController recentScanStocks](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{

  quotaIndex = (void *)self->super._quotaIndex;
  if ( !quotaIndex )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (void *)self->super._quotaIndex;
    self->super._quotaIndex = (unsigned __int64)v5;
    quotaIndex = (void *)self->super._quotaIndex;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(quotaIndex);
}

//----- (00000001007D2C19) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailRecentScanTableViewController sortRequestModule](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  id invokeRowSwitchCallBack; // rdi

  invokeRowSwitchCallBack = self->super._invokeRowSwitchCallBack;
  if ( !invokeRowSwitchCallBack )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super._invokeRowSwitchCallBack;
    self->super._invokeRowSwitchCallBack = v5;
    invokeRowSwitchCallBack = self->super._invokeRowSwitchCallBack;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(invokeRowSwitchCallBack);
}

//----- (00000001007D2C6A) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController tableView:rowViewForRow:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{

  v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRowView_Highlight);
  v5 = _objc_msgSend(v4, "init");
  return objc_autoreleaseReturnValue(v5);
}

//----- (00000001007D2C93) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController tableViewSelectionIsChanging:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v12 = v7;
  v8 = _objc_msgSend(v7, "selectedRowIndexs");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v6, "isEqualToIndexSet:", v9);
  if ( !v10 )
  {
    _objc_msgSend(v12, "setSelectedRowIndexs:", v6);
    v11 = _objc_msgSend(v6, "lastIndex");
    _objc_msgSend(v12, "actionForTalbeViewSeclectionDidChange:", v11);
    _objc_msgSend(v12, "setTableViewSelectionIsChanging:", 1LL);
  }
}

//----- (00000001007D2D7C) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController tableViewSelectionDidChange:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToIndexSet:", v8) )
  {
    -[HXBaseTableViewController tableViewSelectionIsChanging](self, "tableViewSelectionIsChanging");
    if ( v9 )
      goto LABEL_8;
  }
  else
  {
  }
  -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", v6);
  if ( (unsigned __int8)-[HXBaseTableViewController postSelectedRowDidChangedNotify](
                          self,
                          "postSelectedRowDidChangedNotify") )
  {
    v10 = _objc_msgSend(v6, "lastIndex");
    -[ThumbnailRecentScanTableViewController actionForTalbeViewSeclectionDidChange:](
      self,
      "actionForTalbeViewSeclectionDidChange:",
      v10);
  }
  -[HXBaseTableViewController setPostSelectedRowDidChangedNotify:](self, "setPostSelectedRowDidChangedNotify:", 1LL);
LABEL_8:
  -[HXBaseTableViewController setTableViewSelectionIsChanging:](self, "setTableViewSelectionIsChanging:", 0LL);
}

//----- (00000001007D2EA1) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController tableView:viewForTableColumn:row:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXStockModel *v14; // rax
  HXStockModel *v15; // rbx
  NSArray *v16; // rax
  HXStockModel *v17; // rdi
  NSArray *v18; // rbx
  unsigned __int64 v21; // r12
  unsigned __int64 v23; // r12
  NSNumber *v34; // rax
  NSNumber *v35; // rbx
  NSNumber *v37; // rax
  NSNumber *v38; // rbx
  NSNumber *v40; // rax
  NSNumber *v41; // rbx
  NSNumber *v43; // rax
  NSNumber *v44; // rbx
  NSNumber *v49; // rax
  NSNumber *v50; // rbx
  NSNumber *v52; // rax
  NSNumber *v53; // rbx
  NSNumber *v55; // rax
  NSNumber *v56; // rax
  NSNumber *v57; // rax
  NSNumber *v58; // rax
  NSNumber *v59; // r15
  NSArray *v60; // rax
  NSArray *v61; // r14
  NSNumber *v62; // rax
  NSNumber *v63; // rbx
  HXStockModel *v67; // rax
  NSArray *v68; // rax
  NSArray *v69; // r15
  NSNumber *v72; // rax
  NSNumber *v73; // rbx
  TextMarkManager *v78; // rax
  TextMarkManager *v79; // rbx
  NSNumber *v106; // rax
  NSNumber *v107; // rbx
  NSNumber *v109; // rax
  NSNumber *v110; // r14
  NSArray *v147; // [rsp+10h] [rbp-F0h]
  NSNumber *v155; // [rsp+50h] [rbp-B0h]
  ThumbnailRecentScanTableViewController *v158; // [rsp+60h] [rbp-A0h]
  NSNumber *v163; // [rsp+88h] [rbp-78h]
  NSNumber *v165; // [rsp+90h] [rbp-70h]
  HXStockModel *v171; // [rsp+A0h] [rbp-60h]
  _QWORD v174[4]; // [rsp+B0h] [rbp-50h] BYREF

  v7 = objc_retain(a3);
  v8 = _objc_msgSend(a4, "identifier");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v7, "makeViewWithIdentifier:owner:", v9, self);
  objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(&OBJC_CLASS___ThumbnailTableCellView, "class");
  if ( (unsigned __int8)_objc_msgSend(v12, "isKindOfClass:", v11) )
  {
    v157 = v13;
    v14 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = -[HXStockModel mainStockTableViewDataArray](v15, "mainStockTableViewDataArray");
    v17 = v15;
    v18 = objc_retainAutoreleasedReturnValue(v16);
    if ( v18 && _objc_msgSend(v18, "count") )
    {
      v151 = v9;
      if ( (__int64)-[ThumbnailBaseTableViewController begin](self, "begin") <= a5
        && (v158 = self,
            v147 = v18,
            v19 = -[ThumbnailBaseTableViewController begin](self, "begin"),
            v20 = (char *)-[ThumbnailBaseTableViewController count](self, "count") + (_QWORD)v19,
            v18 = v147,
            (unsigned __int64)v20 > v21)
        && (-[ThumbnailBaseTableViewController begin](self, "begin"),
            v22 = (char *)_objc_msgSend(v147, "count"),
            v23 <= (unsigned __int64)(v22 - 1)) )
      {
        v168 = (id)v23;
        v28 = _objc_msgSend(v147, "objectAtIndexedSubscript:", v23);
        v29 = objc_retainAutoreleasedReturnValue(v28);
        v30 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
        v161 = v29;
        v31 = (unsigned __int8)_objc_msgSend(v29, "isKindOfClass:", v30);
        v32 = v157;
        if ( v31 && v157 )
        {
          v33 = -[HXBaseTableViewController sortID](self, "sortID");
          v172 = +[ThumbnailUtils getNewSortIDForNormalTable:](
                   &OBJC_CLASS___ThumbnailUtils,
                   "getNewSortIDForNormalTable:",
                   v33);
          v34 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
          v35 = objc_retainAutoreleasedReturnValue(v34);
          v36 = _objc_msgSend(v29, "objectForKeyedSubscript:", v35);
          v154 = objc_retainAutoreleasedReturnValue(v36);
          v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 66LL);
          v38 = objc_retainAutoreleasedReturnValue(v37);
          v39 = _objc_msgSend(v29, "objectForKeyedSubscript:", v38);
          v149 = objc_retainAutoreleasedReturnValue(v39);
          v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
          v41 = objc_retainAutoreleasedReturnValue(v40);
          v42 = _objc_msgSend(v29, "objectForKeyedSubscript:", v41);
          v148 = objc_retainAutoreleasedReturnValue(v42);
          v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
          v44 = objc_retainAutoreleasedReturnValue(v43);
          v45 = _objc_msgSend(v29, "objectForKeyedSubscript:", v44);
          v46 = objc_retainAutoreleasedReturnValue(v45);
          v47 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v46);
          v152 = objc_retainAutoreleasedReturnValue(v47);
          v153 = v46;
          v48 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v46);
          v162 = objc_retainAutoreleasedReturnValue(v48);
          v49 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
          v50 = objc_retainAutoreleasedReturnValue(v49);
          v51 = _objc_msgSend(v161, "objectForKeyedSubscript:", v50);
          v159 = objc_retainAutoreleasedReturnValue(v51);
          v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v172);
          v53 = objc_retainAutoreleasedReturnValue(v52);
          v54 = _objc_msgSend(v161, "objectForKeyedSubscript:", v53);
          v170 = objc_retainAutoreleasedReturnValue(v54);
          v55 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 264648LL);
          v163 = objc_retainAutoreleasedReturnValue(v55);
          v174[0] = v163;
          v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
          v165 = objc_retainAutoreleasedReturnValue(v56);
          v174[1] = v165;
          v57 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 526792LL);
          v155 = objc_retainAutoreleasedReturnValue(v57);
          v174[2] = v155;
          v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 15LL);
          v59 = objc_retainAutoreleasedReturnValue(v58);
          v174[3] = v59;
          v60 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v174, 4LL);
          v61 = objc_retainAutoreleasedReturnValue(v60);
          v62 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v172);
          v63 = objc_retainAutoreleasedReturnValue(v62);
          v160 = (__int64)_objc_msgSend(v61, "containsObject:", v63);
          if ( v160 )
          {
            v64 = +[ThumbnailUtils getSortItemDataWithSortID:dataDic:](
                    &OBJC_CLASS___ThumbnailUtils,
                    "getSortItemDataWithSortID:dataDic:",
                    v172,
                    v161);
            v65 = objc_retainAutoreleasedReturnValue(v64);
            v66 = v170;
            if ( v65 != v170 && v65 )
            {
              v164 = v65;
              v166 = objc_retain(v65);
              v67 = -[HXBaseTableViewController stockModel](self, "stockModel");
              v171 = objc_retainAutoreleasedReturnValue(v67);
              v68 = -[HXStockModel mainStockTableViewDataArray](v171, "mainStockTableViewDataArray");
              v69 = objc_retainAutoreleasedReturnValue(v68);
              v70 = _objc_msgSend(v69, "objectAtIndexedSubscript:", v168);
              v71 = objc_retainAutoreleasedReturnValue(v70);
              v72 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v172);
              v73 = objc_retainAutoreleasedReturnValue(v72);
              _objc_msgSend(v71, "setObject:forKeyedSubscript:", v166, v73);
              v65 = v164;
              v66 = v166;
            }
            v170 = v66;
          }
          v74 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
          v75 = objc_retainAutoreleasedReturnValue(v74);
          if ( (unsigned __int8)+[SelfStock isSelfStock:](&OBJC_CLASS___SelfStock, "isSelfStock:", v153) )
          {
            v76 = +[HXThemeManager markedTextColor](&OBJC_CLASS___HXThemeManager, "markedTextColor");
            v77 = objc_retainAutoreleasedReturnValue(v76);
            v75 = v77;
          }
          v78 = +[TextMarkManager sharedInstance](&OBJC_CLASS___TextMarkManager, "sharedInstance");
          v79 = objc_retainAutoreleasedReturnValue(v78);
          v80 = -[TextMarkManager getStockCodeColorIdx:](v79, "getStockCodeColorIdx:", v153);
          if ( v80 )
          {
            v82 = +[TextMarkMenuItemView getColorForDotsAtIndex:](
                    &OBJC_CLASS___TextMarkMenuItemView,
                    "getColorForDotsAtIndex:",
                    v80);
            v83 = objc_retainAutoreleasedReturnValue(v82);
            v75 = v83;
          }
          v84 = _objc_msgSend(v81, "stockName");
          v85 = objc_retainAutoreleasedReturnValue(v84);
          v156 = v75;
          _objc_msgSend(v85, "setTextColor:", v75);
          v86 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
          v87 = objc_retainAutoreleasedReturnValue(v86);
          v89 = _objc_msgSend(v88, "stockCode");
          v90 = objc_retainAutoreleasedReturnValue(v89);
          _objc_msgSend(v90, "setTextColor:", v87);
          if ( (unsigned __int8)+[HXTools isQiHuoMarketType:](&OBJC_CLASS___HXTools, "isQiHuoMarketType:", v162)
            || (unsigned __int8)+[HXTools isOptionMarketType:](&OBJC_CLASS___HXTools, "isOptionMarketType:", v162) )
          {
            v91 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                    &OBJC_CLASS___ThumbnailUtils,
                    "getSortItemColor:sortItem:zuoShou:",
                    10LL,
                    v159,
                    v149);
            v92 = objc_retainAutoreleasedReturnValue(v91);
            v94 = _objc_msgSend(v93, "currentPrice");
            v95 = objc_retainAutoreleasedReturnValue(v94);
            _objc_msgSend(v95, "setTextColor:", v92);
            v96 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                    &OBJC_CLASS___ThumbnailUtils,
                    "getSortItemColor:sortItem:zuoShou:",
                    v172,
                    v170,
                    v149);
          }
          else
          {
            v141 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                     &OBJC_CLASS___ThumbnailUtils,
                     "getSortItemColor:sortItem:zuoShou:",
                     10LL,
                     v159,
                     v154);
            v142 = objc_retainAutoreleasedReturnValue(v141);
            v144 = _objc_msgSend(v143, "currentPrice");
            v145 = objc_retainAutoreleasedReturnValue(v144);
            _objc_msgSend(v145, "setTextColor:", v142);
            v96 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                    &OBJC_CLASS___ThumbnailUtils,
                    "getSortItemColor:sortItem:zuoShou:",
                    v172,
                    v170,
                    v154);
          }
          v97 = objc_retainAutoreleasedReturnValue(v96);
          v99 = _objc_msgSend(v98, "priceChange");
          v100 = objc_retainAutoreleasedReturnValue(v99);
          _objc_msgSend(v100, "setTextColor:", v97);
          v101 = +[HXTools getPrecisionTypeWithMarket:Code:](
                   &OBJC_CLASS___HXTools,
                   "getPrecisionTypeWithMarket:Code:",
                   v162,
                   v152);
          +[HXTools getVolumeUintWithMarket:](&OBJC_CLASS___HXTools, "getVolumeUintWithMarket:", v162);
          v102 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
          if ( (unsigned __int8)_objc_msgSend(v170, "isKindOfClass:", v102) )
          {
            v103 = v172;
            v104 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
                     &OBJC_CLASS___ThumbnailUtils,
                     "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
                     v170,
                     v172,
                     (unsigned int)v101,
                     v162);
            v105 = (char *)objc_retainAutoreleasedReturnValue(v104);
          }
          else
          {
            v105 = obj;
            v103 = v172;
          }
          v173 = v101;
          v169 = v105;
          if ( v103 == (id)49 )
          {
            v106 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49LL);
            v107 = objc_retainAutoreleasedReturnValue(v106);
            v108 = _objc_msgSend(v161, "objectForKeyedSubscript:", v107);
            v150 = objc_retainAutoreleasedReturnValue(v108);
            v109 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12LL);
            v110 = objc_retainAutoreleasedReturnValue(v109);
            v111 = _objc_msgSend(v161, "objectForKeyedSubscript:", v110);
            v146 = objc_retainAutoreleasedReturnValue(v111);
            v112 = -[ThumbnailBaseTableViewController getXianLiangTextAndColorBy:volumeClass:market:](
                     v158,
                     "getXianLiangTextAndColorBy:volumeClass:market:",
                     v150,
                     v146,
                     v162);
            v113 = objc_retainAutoreleasedReturnValue(v112);
            v114 = _objc_msgSend(v113, "objectForKeyedSubscript:", CFSTR("TextColor"));
            v115 = objc_retainAutoreleasedReturnValue(v114);
            v117 = _objc_msgSend(v116, "priceChange");
            v118 = objc_retainAutoreleasedReturnValue(v117);
            _objc_msgSend(v118, "setTextColor:", v115);
            v119 = _objc_msgSend(v113, "objectForKeyedSubscript:", CFSTR("VolumeValue"));
            v120 = (char *)objc_retainAutoreleasedReturnValue(v119);
            v169 = v120;
          }
          v121 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v148);
          v122 = objc_retainAutoreleasedReturnValue(v121);
          v124 = _objc_msgSend(v123, "stockName");
          v125 = objc_retainAutoreleasedReturnValue(v124);
          _objc_msgSend(v125, "setStringValue:", v122);
          v126 = +[HXTools getDecodeCodeWithMarket:code:](
                   &OBJC_CLASS___HXTools,
                   "getDecodeCodeWithMarket:code:",
                   v162,
                   v152);
          v127 = objc_retainAutoreleasedReturnValue(v126);
          v129 = _objc_msgSend(v128, "stockCode");
          v130 = objc_retainAutoreleasedReturnValue(v129);
          _objc_msgSend(v130, "setStringValue:", v127);
          v131 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
                   &OBJC_CLASS___ThumbnailUtils,
                   "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
                   v159,
                   10LL,
                   v173,
                   v162);
          v132 = objc_retainAutoreleasedReturnValue(v131);
          v134 = _objc_msgSend(v133, "currentPrice");
          v135 = objc_retainAutoreleasedReturnValue(v134);
          _objc_msgSend(v135, "setStringValue:", v132);
          v136 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v169);
          v137 = objc_retainAutoreleasedReturnValue(v136);
          v139 = _objc_msgSend(v138, "priceChange");
          v140 = objc_retainAutoreleasedReturnValue(v139);
          _objc_msgSend(v140, "setStringValue:", v137);
          v18 = v147;
        }
        objc_retain(v32);
      }
      else
      {
        _objc_msgSend(v157, "clearAllDatas");
        objc_retain(v24);
      }
      v9 = v151;
    }
    else
    {
      objc_retain(v157);
    }
  }
  else
  {
    objc_retain(v13);
  }
  return objc_autoreleaseReturnValue(v26);
}

//----- (00000001007D3CC7) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailRecentScanTableViewController numberOfRowsInTableView:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  return (signed __int64)-[ThumbnailBaseTableViewController allCodesNum](self, "allCodesNum", a3);
}

//----- (00000001007D3CD9) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController doInitialOperation](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  -[ThumbnailBaseTableViewController setIsRequestFromZero:](self, "setIsRequestFromZero:", 1LL);
  LOBYTE(self->super._count) = 0;
  BYTE1(self->super._count) = 0;
}

//----- (00000001007D3D11) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController setRequestRowRange](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  HXBaseTableView *v9; // rax
  HXBaseTableView *v10; // rax
  unsigned __int64 v12; // rbx
  HXBaseTableView *v20; // rax
  HXBaseTableView *v21; // rax
  HXBaseTableView *v27; // rax
  HXBaseTableView *v28; // r13
  unsigned __int64 v30; // rbx
  HXBaseTableView *v32; // rax
  HXBaseTableView *v33; // rax
  HXBaseTableView *v35; // rax
  HXBaseTableView *v36; // rax
  unsigned __int64 v37; // rdx
  id (*v39)(id, SEL, ...); // r12
  __m128d v40; // xmm0
  unsigned __int64 v44; // rdx
  id (*v45)(id, SEL, ...); // r12
  id (*v49)(id, SEL, ...); // r12
  id (*v51)(id, SEL, ...); // r12
  id (*v54)(id, SEL, ...); // r12
  id (*v56)(id, SEL, ...); // r12
  id (*v59)(id, SEL, ...); // r12
  id (*v61)(id, SEL, ...); // r12
  id (*v64)(id, SEL, ...); // r12
  id (*v66)(id, SEL, ...); // r12
  id (*v69)(id, SEL, ...); // r12
  _BYTE *v70; // rbx
  id (*v71)(id, SEL, ...); // r12
  _BYTE *v72; // rax
  id (*v75)(id, SEL, ...); // r12
  id (*v77)(id, SEL, ...); // r12
  id (*v80)(id, SEL, ...); // r12
  id (*v83)(id, SEL, ...); // r12
  id (*v87)(id, SEL, ...); // r12
  SEL v99; // [rsp+90h] [rbp-50h]

  v2 = -[ThumbnailBaseTableViewController getVisibleRange](self, "getVisibleRange");
  -[HXBaseTableViewController setVisibleRange:](self, "setVisibleRange:", v2, v3);
  if ( (unsigned __int8)-[ThumbnailBaseTableViewController isTableSwitched](self, "isTableSwitched") )
  {
    v4 = _objc_msgSend(self, "view");
    v5 = (const char *)objc_retainAutoreleasedReturnValue(v4);
    v6 = (char *)v5;
    if ( v5 )
    {
      objc_msgSend_stret(v91, v5, "bounds");
      v8 = *(double *)(v7 + 24);
    }
    else
    {
      memset(v91, 0, sizeof(v91));
      v8 = 0.0;
    }
    *(double *)&v100 = v8;
    v9 = -[HXBaseTableViewController myTable](self, "myTable");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    _objc_msgSend(v10, "rowHeight");
    v12 = (unsigned int)(int)(*(double *)&v100 / v8 - 9.223372036854776e18) ^ 0x8000000000000000LL;
    if ( *(double *)&v100 / v8 < 9.223372036854776e18 )
      v12 = (unsigned int)(int)(*(double *)&v100 / v8);
    v13 = (id)(2 * v12);
    v14 = -[ThumbnailBaseTableViewController quotaBegin](self, "quotaBegin");
    v99 = "setBegin:";
    -[ThumbnailBaseTableViewController setBegin:](self, "setBegin:", v14);
    if ( v13 < -[ThumbnailBaseTableViewController quotaCount](self, "quotaCount")
      && -[ThumbnailBaseTableViewController quotaCount](self, "quotaCount") )
    {
      v13 = -[ThumbnailBaseTableViewController quotaCount](self, "quotaCount");
    }
    -[ThumbnailBaseTableViewController setCount:](self, "setCount:", v13);
  }
  else
  {
    if ( (unsigned __int8)-[ThumbnailBaseTableViewController isRequestFromZero](self, "isRequestFromZero") )
    {
      v99 = "setBegin:";
      -[ThumbnailBaseTableViewController setBegin:](self, "setBegin:", 0LL);
    }
    else
    {
      v15 = -[HXBaseTableViewController visibleRange](self, "visibleRange");
      v99 = "setBegin:";
      -[ThumbnailBaseTableViewController setBegin:](self, "setBegin:", v15);
    }
    -[HXBaseTableViewController visibleRange](self, "visibleRange");
    -[ThumbnailBaseTableViewController setCount:](self, "setCount:");
    if ( !-[ThumbnailBaseTableViewController count](self, "count") )
    {
      v16 = _objc_msgSend(self, "view");
      v17 = (const char *)objc_retainAutoreleasedReturnValue(v16);
      v18 = (char *)v17;
      if ( v17 )
      {
        objc_msgSend_stret(&v92, v17, "bounds");
        v19 = *((double *)&v93 + 1);
      }
      else
      {
        v93 = 0LL;
        v92 = 0LL;
        v19 = 0.0;
      }
      *(double *)&v100 = v19;
      v20 = -[HXBaseTableViewController myTable](self, "myTable");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      _objc_msgSend(v21, "rowHeight");
      -[ThumbnailBaseTableViewController setCount:](self, "setCount:", (int)(*(double *)&v100 / v19));
    }
    if ( LOBYTE(self->super._count) )
    {
      v23 = _objc_msgSend(self, "view");
      v24 = (const char *)objc_retainAutoreleasedReturnValue(v23);
      v25 = (char *)v24;
      if ( v24 )
      {
        objc_msgSend_stret(&v94, v24, "bounds");
        v26 = *((double *)&v95 + 1);
      }
      else
      {
        v95 = 0LL;
        v94 = 0LL;
        v26 = 0.0;
      }
      *(double *)&v100 = v26;
      v27 = -[HXBaseTableViewController myTable](self, "myTable");
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29(v28, "rowHeight");
      v30 = (unsigned int)(int)(*(double *)&v100 / v26 - 9.223372036854776e18) ^ 0x8000000000000000LL;
      if ( *(double *)&v100 / v26 < 9.223372036854776e18 )
        v30 = (unsigned int)(int)(*(double *)&v100 / v26);
      v31(self, "setCount:", v30);
      LOBYTE(self->super._count) = 0;
    }
    if ( BYTE1(self->super._count) )
    {
      v32 = -[HXBaseTableViewController myTable](self, "myTable");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v100 = v33;
      if ( v33 )
      {
        objc_msgSend_stret(&v96, (SEL)v33, "visibleRect");
        v34 = *((double *)&v97 + 1);
      }
      else
      {
        v97 = 0LL;
        v96 = 0LL;
        v34 = 0.0;
      }
      v98 = v34;
      v35 = -[HXBaseTableViewController myTable](self, "myTable");
      v36 = objc_retainAutoreleasedReturnValue(v35);
      _objc_msgSend(v36, "rowHeight");
      v37 = (unsigned int)(int)(v98 / v34 - 9.223372036854776e18) ^ 0x8000000000000000LL;
      if ( v98 / v34 < 9.223372036854776e18 )
        v37 = (unsigned int)(int)(v98 / v34);
      -[ThumbnailBaseTableViewController setCount:](self, "setCount:", v37);
      BYTE1(self->super._count) = 0;
    }
  }
  *(double *)&v100 = COERCE_DOUBLE("count");
  v101 = (double)(int)-[ThumbnailBaseTableViewController begin](self, "begin");
  v40 = _mm_sub_pd(
          (__m128d)_mm_unpacklo_epi32((__m128i)(unsigned __int64)v39(self, "count"), (__m128i)xmmword_1010CE400),
          (__m128d)xmmword_1010CE410);
  *(_QWORD *)&v102 = *(_OWORD *)&_mm_hadd_pd(v40, v40);
  v41(self, "preload:", &v101);
  v42(self, v99, (unsigned int)(int)v101);
  v44 = (unsigned int)(int)v102;
  if ( v102 >= 9.223372036854776e18 )
    v44 = (unsigned int)(int)(v102 - 9.223372036854776e18) ^ 0x8000000000000000LL;
  v99 = "setCount:";
  v43(self, "setCount:", v44);
  v46 = v45(self, "orderCodeMArray");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v48(v47, "removeAllObjects");
  v50 = v49(self, "begin");
  v52 = v51(self, "recentScanStocks");
  v53 = objc_retainAutoreleasedReturnValue(v52);
  v55 = v54(v53, "count");
  if ( v50 < v55 )
  {
    v57 = v56(self, "begin");
    v58 = (const char *)v100;
    v60 = (char *)v59(self, (SEL)v100) + (_QWORD)v57;
    v62 = v61(self, "recentScanStocks");
    v63 = objc_retainAutoreleasedReturnValue(v62);
    v65 = (char *)v64(v63, v58);
    if ( v60 > v65 )
    {
      v67 = v66(self, "recentScanStocks");
      v68 = objc_retainAutoreleasedReturnValue(v67);
      v70 = v69(v68, (SEL)v100);
      v72 = v71(self, "begin");
      v73(self, v99, v70 - v72);
    }
    v74 = v66(self, "begin");
    v76 = v75(self, (SEL)v100);
    v78 = v77(self, "recentScanStocks");
    v79 = objc_retainAutoreleasedReturnValue(v78);
    v81 = v80(v79, "subarrayWithRange:", v74, v76);
    v82 = objc_retainAutoreleasedReturnValue(v81);
    v84 = v83(self, "orderCodeMArray");
    v85 = objc_retainAutoreleasedReturnValue(v84);
    v86(v85, "removeAllObjects");
    v88 = v87(self, "orderCodeMArray");
    v89 = objc_retainAutoreleasedReturnValue(v88);
    v90(v89, "addObjectsFromArray:", v82);
  }
}

//----- (00000001007D4495) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController reloadDataForMyTable](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  HXBaseTableView *v2; // rax
  HXBaseTableView *v3; // rbx
  NSIndexSet *v14; // rax
  NSIndexSet *v15; // rbx
  NSIndexSet *v18; // rax

  v2 = -[HXBaseTableViewController myTable](self, "myTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseTableView reloadRequestData](v3, "reloadRequestData");
  _objc_msgSend(v4, "setTableHasData:", 1LL);
  v6 = (char *)_objc_msgSend(v5, "getIndexForSelectedCode");
  if ( (unsigned __int8)_objc_msgSend(v7, "isFoucsOfSuperController") )
  {
    if ( v6 == (char *)0x7FFFFFFFFFFFFFFFLL )
    {
      v9 = _objc_msgSend(v8, "selectedCode");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v10, "length");
      if ( v11 )
      {
        _objc_msgSend(v12, "setSelectedRowIndexs:", 0LL);
        goto LABEL_8;
      }
      v17 = _objc_msgSend(v12, "begin");
      v18 = (NSIndexSet *)_objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", v17);
      v15 = objc_retainAutoreleasedReturnValue(v18);
      _objc_msgSend(v19, "setSelectedRowIndexs:", v15);
    }
    else
    {
      v13 = _objc_msgSend(v8, "begin");
      v14 = (NSIndexSet *)_objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", &v6[(_QWORD)v13]);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v16, "setSelectedRowIndexs:", v15);
    }
  }
LABEL_8:
  if ( (unsigned __int8)_objc_msgSend(v8, "isTableSwitched") )
  {
    v21 = _objc_msgSend(v20, "myTable");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v24 = _objc_msgSend(v23, "getScorllPosition");
    _objc_msgSend(v22, "scrollRowToVisible:", v24);
    _objc_msgSend(v25, "setIsTableSwitched:", 0LL);
  }
  v26 = _objc_msgSend(v20, "selectedRowIndexs");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v29 = (__int64)_objc_msgSend(v28, "selectedRowDidChange");
  _objc_msgSend(v30, "codingToSelectedRowIndexes:selectedRowDidChange:", v27, (unsigned int)v29);
  _objc_msgSend(v31, "setSelectedRowDidChange:", 0LL);
  _objc_msgSend(v32, "setIsRequestFromZero:", 0LL);
}

//----- (00000001007D46D3) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController actionForTalbeViewSeclectionDidChange:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v5; // rax
  HXStockModel *v6; // rbx
  NSArray *v7; // rax
  NSArray *v8; // r15
  SEL v10; // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  signed __int8 v35; // al
  NSNumber *v36; // rax
  NSNumber *v37; // rbx
  NSMutableDictionary *v38; // rax
  NSMutableDictionary *v39; // r13
  id (*v41)(id, SEL, ...); // r12
  id (*v43)(id, SEL, ...); // r12
  unsigned __int64 quotaBegin; // rdx
  id (*v48)(id, SEL, ...); // r12
  id (*v54)(id, SEL, ...); // r12
  id (*v57)(id, SEL, ...); // r12
  _QWORD v65[2]; // [rsp+20h] [rbp-50h] BYREF
  _QWORD v66[2]; // [rsp+30h] [rbp-40h] BYREF

  if ( (a3 & 0x7FFFFFFFFFFFFFFFLL) != 0x7FFFFFFFFFFFFFFFLL )
  {
    v5 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = -[HXStockModel mainStockTableViewDataArray](v6, "mainStockTableViewDataArray");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( v8 )
    {
      if ( _objc_msgSend(v8, "count") )
      {
        v9 = (char *)(a3 - (_QWORD)-[ThumbnailBaseTableViewController begin](self, "begin"));
        if ( v9 <= (char *)_objc_msgSend(v8, v10) - 1 )
        {
          -[ThumbnailBaseTableViewController setIsFoucsOfSuperController:](self, "setIsFoucsOfSuperController:", 1LL);
          v12 = v11(v8, "objectAtIndexedSubscript:", v9);
          v13 = objc_retainAutoreleasedReturnValue(v12);
          v15 = v14(&OBJC_CLASS___NSDictionary, "class");
          v61 = v13;
          if ( (unsigned __int8)v16(v13, "isKindOfClass:", v15) )
          {
            v18 = v17(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v21 = v20(v61, "objectForKeyedSubscript:", v19);
            objc_retainAutoreleasedReturnValue(v21);
            v23 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v22);
            v24 = objc_retainAutoreleasedReturnValue(v23);
            v26 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v25);
            v63 = objc_retainAutoreleasedReturnValue(v26);
            v64 = v24;
            v27 = v24;
            v29 = v28;
            v62 = v28;
            if ( _objc_msgSend(v27, "length") && _objc_msgSend(v63, "length") )
            {
              v31 = v30(&OBJC_CLASS___SelfStock, "sharedInstance");
              v32 = v29;
              v33 = objc_retainAutoreleasedReturnValue(v31);
              v34(v33, "addRecentlyScanStock:market:toBegin:", v64, v63, 0LL);
              -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v32);
              v35 = (unsigned __int8)-[ThumbnailRecentScanTableViewController isEnterQuickTrade](
                                       self,
                                       "isEnterQuickTrade");
              v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", (unsigned int)v35);
              v37 = objc_retainAutoreleasedReturnValue(v36);
              v38 = _objc_msgSend(
                      &OBJC_CLASS___NSMutableDictionary,
                      "dictionaryWithObjectsAndKeys:",
                      v64,
                      CFSTR("StockCode"),
                      v63,
                      CFSTR("Market"),
                      v37,
                      CFSTR("isQuickTrade"),
                      0LL);
              v39 = objc_retainAutoreleasedReturnValue(v38);
              v40(v37);
              if ( -[ThumbnailRecentScanTableViewController mainGraphType](self, "mainGraphType") != (id)-1LL )
              {
                v42 = v41(self, "mainGraphType");
                v44 = v43(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v42);
                v45 = objc_retainAutoreleasedReturnValue(v44);
                v46(v39, "setObject:forKey:", v45, off_1012DF628);
              }
              quotaBegin = self->super._quotaBegin;
              if ( quotaBegin )
                _objc_msgSend(v39, "setObject:forKey:", quotaBegin, off_1012E7230);
              v41(self, "setMainGraphType:", -1LL);
              v49 = v48(self, "invokeRowSwitchCallBack");
              v50 = objc_retainAutoreleasedReturnValue(v49);
              if ( v50 )
              {
                v51 = -[ThumbnailBaseTableViewController invokeRowSwitchCallBack](self, "invokeRowSwitchCallBack");
                v52 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v51);
                v52[2](v52, v39);
              }
              v29 = v62;
            }
            if ( _objc_msgSend(v64, "length") )
            {
              v53(self, "setSelectedCode:", v29);
              v65[0] = CFSTR("StockCode");
              v66[0] = v64;
              v65[1] = off_1012E0FA8;
              v66[1] = v29;
              v55 = v54(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v66, v65, 2LL);
              v56 = objc_retainAutoreleasedReturnValue(v55);
              v58 = v57(self, "myTable");
              v59 = objc_retainAutoreleasedReturnValue(v58);
              v60(v59, "setParamsDic:", v56);
              v29 = v62;
            }
          }
        }
      }
    }
  }
}

//----- (00000001007D4BC0) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController getRequestDataTypes](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  id (*v3)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  NSNumber *v33; // [rsp+8h] [rbp-88h]
  _QWORD v38[6]; // [rsp+30h] [rbp-60h] BYREF

  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v33 = objc_retainAutoreleasedReturnValue(v2);
  v38[0] = v33;
  v4 = v3(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
  v34 = objc_retainAutoreleasedReturnValue(v4);
  v38[1] = v34;
  v6 = v5(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v35 = objc_retainAutoreleasedReturnValue(v6);
  v38[2] = v35;
  v8 = v7(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
  v36 = objc_retainAutoreleasedReturnValue(v8);
  v38[3] = v36;
  v10 = v9(&OBJC_CLASS___NSNumber, "numberWithInt:", 12LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v38[4] = v11;
  v13 = v12(&OBJC_CLASS___NSNumber, "numberWithInt:", 66LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v38[5] = v14;
  v16 = v15(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v38, 6LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = v18(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v17);
  v37 = objc_retainAutoreleasedReturnValue(v19);
  v20 = v11;
  v22 = v21;
  if ( v23(self, "sortID") )
  {
    if ( -[HXBaseTableViewController sortID](self, "sortID") != (id)12345670 )
    {
      v24 = v22(self, "sortID");
      v25 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v22)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithLong:",
                      v24);
      v26 = objc_retainAutoreleasedReturnValue(v25);
      ((void (__fastcall *)(id, const char *, id))v22)(v37, "containsObject:", v26);
      if ( !v27 )
      {
        v28 = v22(self, "sortID");
        v29 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v22)(
                        &OBJC_CLASS___NSNumber,
                        "numberWithLong:",
                        v28);
        v30 = objc_retainAutoreleasedReturnValue(v29);
        ((void (__fastcall *)(id, const char *, id))v22)(v37, "addObject:", v30);
      }
    }
  }
  return objc_autoreleaseReturnValue(v37);
}

//----- (00000001007D4E35) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController getRequestDataTypes:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // r14
  NSNumber *v7; // rax
  NSNumber *v8; // r15
  id (*v9)(id, SEL, ...); // r12
  NSArray *v10; // rax
  NSNumber *v11; // rax
  NSArray *v13; // rbx
  _QWORD v16[2]; // [rsp+10h] [rbp-40h] BYREF

  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5;
  if ( a3 == 5 )
  {
    v16[0] = v5;
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v16[1] = v8;
    v10 = (NSArray *)v9(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v16, 2LL);
  }
  else
  {
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", a3);
    v8 = objc_retainAutoreleasedReturnValue(v11);
    *(_QWORD *)(v12 + 8) = v8;
    v10 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v12, 2LL);
  }
  v13 = objc_retainAutoreleasedReturnValue(v10);
  v14(v6);
  return objc_autoreleaseReturnValue(v13);
}

//----- (00000001007D4F63) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController dealWithPushData:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v4; // rcx
  id (**v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (**v10)(id, SEL, ...); // r12
  unsigned __int64 v11; // r15
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  ThumbnailRecentScanTableViewController *v36; // r13
  id (*v37)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v44)(id, SEL, ...); // r12
  id (*v47)(id, SEL, ...); // r12
  id (*v48)(id, SEL, ...); // r12
  id (*v49)(id, SEL, ...); // r12
  unsigned __int64 v53; // r12
  bool v54; // cc
  id (*v57)(id, SEL, ...); // r12
  id (*v60)(id, SEL, ...); // r12
  id (*v62)(id, SEL, ...); // r12
  id (*v66)(id, SEL, ...); // r12
  HXBaseTableView *v71; // rax
  HXBaseTableView *v72; // rbx
  unsigned __int64 v75; // [rsp+88h] [rbp-68h]
  unsigned __int64 v76; // [rsp+90h] [rbp-60h]

  v81 = objc_retain(a3);
  if ( v81 && _objc_msgSend(v81, "count") )
  {
    if ( !_objc_msgSend(v81, "count") )
      goto LABEL_14;
    v76 = 0LL;
    v5 = &_objc_msgSend;
    while ( 1 )
    {
      v6 = ((id (*)(id, SEL, ...))v5)(self, "orderCodeMArray");
      v7 = objc_retainAutoreleasedReturnValue(v6);
      v9 = v8(v7, "count");
      if ( !v9 )
        goto LABEL_13;
      v11 = 0LL;
      while ( 1 )
      {
        v12 = ((id (*)(id, SEL, ...))v10)(v81, "objectAtIndexedSubscript:", v76);
        v80 = objc_retainAutoreleasedReturnValue(v12);
        v14 = v13(self, "orderCodeMArray");
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v75 = v11;
        v17 = v16(v15, "objectAtIndexedSubscript:", v11);
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v20 = v19(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
        v21 = objc_retainAutoreleasedReturnValue(v20);
        v23 = v22(v80, "thsStringForKey:", v21);
        v74 = objc_retainAutoreleasedReturnValue(v23);
        v25 = v24(v18, "thsStringForKey:", CFSTR("Market"));
        v26 = objc_retainAutoreleasedReturnValue(v25);
        v73 = v18;
        v27 = v18;
        v28 = v80;
        v30 = v29(v27, "thsStringForKey:", CFSTR("StockCode"));
        v31 = objc_retainAutoreleasedReturnValue(v30);
        v33 = v32(&OBJC_CLASS___HXTools, "getMarketAndCodeByAppendingMarket:andStockCode:", v26, v31);
        v34 = objc_retainAutoreleasedReturnValue(v33);
        v35 = v26;
        v36 = self;
        if ( (unsigned __int8)v37(v74, "isEqualToString:", v34) )
          break;
LABEL_10:
        v50 = v49(v36, "orderCodeMArray");
        v51 = objc_retainAutoreleasedReturnValue(v50);
        v52(v51, "count");
        v54 = v53 <= v75 + 1;
        v10 = &_objc_msgSend;
        v11 = v75 + 1;
        if ( v54 )
          goto LABEL_13;
      }
      v79 = v34;
      v39 = v38(self, "stockModel");
      v40 = objc_retainAutoreleasedReturnValue(v39);
      v42 = v41(v40, "mainStockTableViewDataArray");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v45 = v44(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v43);
      v46 = objc_retainAutoreleasedReturnValue(v45);
      if ( (unsigned __int64)v47(v46, "count") <= v75 )
        break;
      v55 = v48(v46, "objectAtIndexedSubscript:", v75);
      v77 = (char *)v46;
      v56 = objc_retainAutoreleasedReturnValue(v55);
      v58 = v57(&OBJC_CLASS___NSMutableDictionary, "dictionaryWithDictionary:", v56);
      v59 = objc_retainAutoreleasedReturnValue(v58);
      v61 = v60(self, "updateData:SourceData:", v80, v59);
      v78 = (char *)objc_retainAutoreleasedReturnValue(v61);
      v63 = v62(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v77);
      v64 = objc_retainAutoreleasedReturnValue(v63);
      v65(v64, "replaceObjectAtIndex:withObject:", v75, v78);
      v67 = v66(self, "stockModel");
      v68 = objc_retainAutoreleasedReturnValue(v67);
      v69(v68, "setMainStockTableViewDataArray:", v64);
LABEL_13:
      v70 = ((id (*)(id, SEL, ...))v10)(v81, "count");
      v4 = v76 + 1;
      v76 = v4;
      if ( (unsigned __int64)v70 <= v4 )
      {
LABEL_14:
        v71 = -[HXBaseTableViewController myTable](self, "myTable", v3, v4);
        v72 = objc_retainAutoreleasedReturnValue(v71);
        -[HXBaseTableView reloadPushData:](v72, "reloadPushData:");
        goto LABEL_15;
      }
    }
    v36 = self;
    v28 = v80;
    v34 = v79;
    goto LABEL_10;
  }
LABEL_15:
}

//----- (00000001007D5496) ----------------------------------------------------
void __fastcall sub_1007D5496(__int64 a1)
{
  id (*v3)(id, SEL, ...); // r12

  if ( (unsigned __int8)_objc_msgSend(*(id *)(a1 + 32), "isFoucsOfSuperController") )
  {
    v1 = (char *)_objc_msgSend(*(id *)(a1 + 32), "getIndexForSelectedCode");
    if ( v1 == (char *)0x7FFFFFFFFFFFFFFFLL )
    {
      _objc_msgSend(*(id *)(a1 + 32), "setSelectedRowIndexs:", 0LL);
    }
    else
    {
      v2 = _objc_msgSend(*(id *)(a1 + 32), "begin");
      v4 = v3(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", &v1[(_QWORD)v2]);
      v5 = objc_retainAutoreleasedReturnValue(v4);
      v6(*(id *)(a1 + 32), "setSelectedRowIndexs:", v5);
    }
    v7 = *(void **)(a1 + 32);
    v8 = _objc_msgSend(v7, "selectedRowIndexs");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(v7, "codingToSelectedRowIndexes:selectedRowDidChange:", v9, 0LL);
  }
}

//----- (00000001007D5594) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController updateData:SourceData:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (*v9)(id, SEL, ...); // r12
  unsigned __int64 v10; // r14
  signed int v14; // eax

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = -[ThumbnailRecentScanTableViewController getRequestDataTypes](self, "getRequestDataTypes");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( v9(v8, "count") )
  {
    v19 = v8;
    v10 = 0LL;
    v20 = v5;
    v11 = v8;
    do
    {
      v12 = _objc_msgSend(v11, "objectAtIndexedSubscript:", v10);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = (unsigned int)_objc_msgSend(v13, "intValue");
      v15 = +[ThumbnailUtils setSourceData:withPushData:dataItemID:](
              &OBJC_CLASS___ThumbnailUtils,
              "setSourceData:withPushData:dataItemID:",
              v6,
              v20,
              v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      ++v10;
      v6 = v16;
    }
    while ( (unsigned __int64)_objc_msgSend(v17, "count") > v10 );
    v6 = v16;
    v5 = v20;
    v8 = v19;
  }
  return objc_autorelease(v6);
}

//----- (00000001007D56D9) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController sendRequestForRecentScanStocksQuotationDatas](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // r13
  NSDictionary *v8; // rax
  NSDictionary *v9; // r15
  HXTableRequestModule *v10; // rax
  _QWORD v13[4]; // [rsp+0h] [rbp-80h] BYREF
  id to; // [rsp+20h] [rbp-60h] BYREF
  id location; // [rsp+28h] [rbp-58h] BYREF
  _QWORD v16[4]; // [rsp+30h] [rbp-50h] BYREF

  v2 = -[ThumbnailRecentScanTableViewController getRequestDataTypes](self, "getRequestDataTypes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v16[0] = CFSTR("datatype");
    v16[2] = v4;
    v16[1] = CFSTR("StockCodesAndMarket");
    v5 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    *(_QWORD *)(v7 + 8) = v6;
    v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v7, v16, 2LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    objc_initWeak(&location, self);
    v10 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
    objc_retain(v10);
    v13[0] = _NSConcreteStackBlock;
    v13[1] = 3254779904LL;
    v13[2] = sub_1007D589A;
    v13[3] = &unk_1012DAED8;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v11, "request:params:callBack:", 4LL, v9, v13);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
  }
}

//----- (00000001007D589A) ----------------------------------------------------
void __fastcall sub_1007D589A(__int64 a1, void *a2)
{
  id WeakRetained; // r13
  NSNumber *v5; // rax
  id (*v13)(id, SEL, ...); // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "sortID");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v4);
  objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(WeakRetained, "sortOrder");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = +[ThumbnailUtils sortCodes:bySortID:bySortOrder:](
         &OBJC_CLASS___ThumbnailUtils,
         "sortCodes:bySortID:bySortOrder:",
         v2,
         v8,
         v7);
  v21 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(WeakRetained, "orderCodeMArray");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = v13(&OBJC_CLASS___HXTools, "getCompleteDatasWith:and:", v12, v21);
  v16 = v15;
  objc_retainAutoreleasedReturnValue(v14);
  v17 = (void *)v16(WeakRetained, "stockModel");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  ((void (__fastcall *)(id, const char *, __int64))v16)(v18, "setMainStockTableViewDataArray:", v19);
  v16(WeakRetained, "reloadDataForMyTable");
  v16(WeakRetained, "order");
}

//----- (00000001007D5A28) ----------------------------------------------------
id __cdecl -[ThumbnailRecentScanTableViewController getCompleteRecentStockArray:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  SelfStock *v4; // rax
  SelfStock *v5; // rbx

  v3 = objc_retain(a3);
  v4 = +[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[SelfStock getRecentlyScanStockWithMarket](v5, "getRecentlyScanStockWithMarket");
  objc_retainAutoreleasedReturnValue(v6);
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v8 = +[HXTools getCompleteStockCodesWith:and:](&OBJC_CLASS___HXTools, "getCompleteStockCodesWith:and:", v7, v3);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = v9;
    if ( v9 && _objc_msgSend(v9, "count") )
    {
      v12 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v11);
      v13 = objc_retainAutoreleasedReturnValue(v12);
    }
    else
    {
      v13 = objc_retain(v10);
    }
  }
  else
  {
    v13 = objc_retain(v7);
    v14 = v13;
  }
  objc_autorelease(v13);
  return v13;
}

//----- (00000001007D5B4F) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController setRecentScanStocks:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._quotaIndex, a3);
}

//----- (00000001007D5B63) ----------------------------------------------------
unsigned __int64 __cdecl -[ThumbnailRecentScanTableViewController mainGraphType](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  return *(_QWORD *)&self->super._visibleY;
}

//----- (00000001007D5B74) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController setMainGraphType:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  *(_QWORD *)&self->super._visibleY = a3;
}

//----- (00000001007D5B85) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController setSortRequestModule:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super._invokeRowSwitchCallBack, a3);
}

//----- (00000001007D5B99) ----------------------------------------------------
char __cdecl -[ThumbnailRecentScanTableViewController isEnterQuickTrade](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  return self->super._quotaCount;
}

//----- (00000001007D5BAA) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController setIsEnterQuickTrade:](
        ThumbnailRecentScanTableViewController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super._quotaCount) = a3;
}

//----- (00000001007D5BBA) ----------------------------------------------------
void __cdecl -[ThumbnailRecentScanTableViewController .cxx_destruct](
        ThumbnailRecentScanTableViewController *self,
        SEL a2)
{
  objc_storeStrong(&self->super._invokeRowSwitchCallBack, 0LL);
  objc_storeStrong((id *)&self->super._quotaIndex, 0LL);
  objc_storeStrong((id *)&self->super._quotaBegin, 0LL);
}

//----- (00000001007D5BFC) ----------------------------------------------------
void __fastcall sub_1007D5BFC(_DWORD *a1)
{
  *a1 = 0;
}

//----- (00000001007D5C08) ----------------------------------------------------
void __fastcall sub_1007D5C08(_DWORD *a1)
{
  *a1 = 0;
}

//----- (00000001007D5C14) ----------------------------------------------------
void __fastcall sub_1007D5C14(_DWORD *a1)
{
  *a1 = 0;
}

//----- (00000001007D5C20) ----------------------------------------------------
void *__fastcall sub_1007D5C20(void *a1, const void *a2)
{
  return sub_1005B9CCE(a1, a2, 4LL);
}

//----- (00000001007D5C30) ----------------------------------------------------
void *__fastcall sub_1007D5C30(void *a1, const void *a2)
{
  return sub_1005B9CCE(a1, a2, 4LL);
}

//----- (00000001007D5C40) ----------------------------------------------------
void __fastcall sub_1007D5C40(_DWORD *a1, int a2, double a3)
{
  sub_1007D5C4A(a1, a2, a3);
}

//----- (00000001007D5C4A) ----------------------------------------------------
void __fastcall sub_1007D5C4A(_DWORD *a1, int a2, double a3)
{
  int v3; // eax
  int v5; // ecx
  int v6; // ecx

  *a1 = 0;
  if ( a3 != 0.0 && a2 >= 0 )
  {
    v3 = 0;
    if ( a3 < 0.0 )
    {
      *a1 = 0x8000000;
      v3 = 0x8000000;
      a3 = 0.0 - a3;
    }
    if ( (unsigned int)(a2 - 1) <= 6 )
      a3 = a3 * (double)(int)qword_1010DA1B0[a2];
    for ( i = a3 + 0.5; i > 134217727.0; --a2 )
      i = i / 10.0;
    if ( (unsigned int)(a2 + 7) <= 0xE )
    {
      v5 = -a2;
      if ( -a2 < 1 )
        v5 = a2;
      v6 = v3 | (int)i & 0x7FFFFFF | (v5 << 28);
      *a1 = v6;
      if ( a2 > 0 )
        *a1 = v6 | 0x80000000;
    }
  }
}

//----- (00000001007D5D08) ----------------------------------------------------
void __fastcall sub_1007D5D08(_DWORD *a1, int a2, double a3)
{
  sub_1007D5C4A(a1, a2, a3);
}

//----- (00000001007D5D12) ----------------------------------------------------
bool __fastcall sub_1007D5D12(_DWORD *a1)
{
  return *a1 == -1;
}

//----- (00000001007D5D1E) ----------------------------------------------------
void *__fastcall sub_1007D5D1E(void *a1)
{

  v2 = 0x80000000LL;
  return sub_1005B9CCE(a1, &v2, 4LL);
}

//----- (00000001007D5D42) ----------------------------------------------------
__int64 __fastcall sub_1007D5D42(unsigned int *a1)
{
  return *a1;
}

//----- (00000001007D5D9E) ----------------------------------------------------
void *__fastcall sub_1007D5D9E(void *a1, const void *a2)
{
  sub_1005B9CCE(a1, a2, 4LL);
  return a1;
}

//----- (00000001007D5DBC) ----------------------------------------------------
_DWORD *__fastcall sub_1007D5DBC(_DWORD *a1, int a2)
{
  sub_1007D5C4A(a1, 0, (double)a2);
  return a1;
}

//----- (00000001007D5DDC) ----------------------------------------------------
unsigned int *__fastcall sub_1007D5DDC(unsigned int *a1, int a2, double a3)
{
  sub_1007D5D42(a1);
  sub_1007D5C4A(a1, (*a1 >> 28) & 7, a3 + (double)a2);
  return a1;
}

//----- (00000001007D5E10) ----------------------------------------------------
unsigned int *__fastcall sub_1007D5E10(unsigned int *a1, unsigned int *a2, double a3)
{
  unsigned int v3; // ebx

  v3 = (*a2 >> 28) & 7;
  if ( ((*a1 >> 28) & 7) >= v3 )
    v3 = (*a1 >> 28) & 7;
  sub_1007D5D42(a1);
  sub_1007D5D42(a2);
  sub_1007D5C4A(a1, v3, a3 + a3);
  return a1;
}

//----- (00000001007D5E64) ----------------------------------------------------
_DWORD *__fastcall sub_1007D5E64(_DWORD *a1, unsigned int *a2, unsigned int *a3, double a4)
{
  unsigned int v6; // eax
  unsigned int v7; // esi

  sub_1007D5D42(a3);
  sub_1007D5D42(a2);
  v5 = a4 + a4;
  v6 = (*a2 >> 28) & 7;
  v7 = (*a3 >> 28) & 7;
  if ( v6 >= v7 )
    v7 = v6;
  sub_1007D5C4A(a1, v7, v5);
  return a1;
}

//----- (00000001007D5EBE) ----------------------------------------------------
_DWORD *__fastcall sub_1007D5EBE(_DWORD *a1, unsigned int *a2, unsigned int *a3, double a4)
{
  unsigned int v6; // eax
  unsigned int v7; // esi

  sub_1007D5D42(a3);
  sub_1007D5D42(a2);
  v5 = a4 - a4;
  v6 = (*a2 >> 28) & 7;
  v7 = (*a3 >> 28) & 7;
  if ( v6 >= v7 )
    v7 = v6;
  sub_1007D5C4A(a1, v7, v5);
  return a1;
}

//----- (00000001007D5F18) ----------------------------------------------------
_DWORD *__fastcall sub_1007D5F18(_DWORD *a1, unsigned int *a2, unsigned int *a3, double a4)
{
  unsigned int v6; // eax
  unsigned int v7; // esi

  sub_1007D5D42(a3);
  sub_1007D5D42(a2);
  v5 = a4 * a4;
  v6 = (*a2 >> 28) & 7;
  v7 = (*a3 >> 28) & 7;
  if ( v6 >= v7 )
    v7 = v6;
  sub_1007D5C4A(a1, v7, v5);
  return a1;
}

//----- (00000001007D5F72) ----------------------------------------------------
_DWORD *__fastcall sub_1007D5F72(_DWORD *a1, unsigned int *a2, unsigned int *a3, double a4)
{
  unsigned int v6; // eax
  unsigned int v7; // esi

  sub_1007D5D42(a3);
  sub_1007D5D42(a2);
  v5 = a4 / a4;
  v6 = (*a2 >> 28) & 7;
  v7 = (*a3 >> 28) & 7;
  if ( v6 >= v7 )
    v7 = v6;
  sub_1007D5C4A(a1, v7, v5);
  return a1;
}

