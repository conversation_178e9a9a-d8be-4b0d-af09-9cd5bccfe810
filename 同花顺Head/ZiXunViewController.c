void __cdecl -[ZiXunViewController viewDidLoad](ZiXunViewController *self, SEL a2)
{
  WebView *v2; // rax
  LoadLocalURLManager *v5; // rax
  id WeakRetained; // r14

  v25 = self;
  v23.receiver = self;
  v23.super_class = (Class)&OBJC_CLASS___ZiXunViewController;
  -[HXBaseViewController viewDidLoad](&v23, "viewDidLoad");
  -[ZiXunViewController setWebViewDelegates](self, "setWebViewDelegates");
  -[ZiXunViewController initObjects](self, "initObjects");
  v2 = -[ZiXunViewController webView](self, "webView");
  v24 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v24, "mainFrame");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v26 = objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(v26, "getCurrentURLWithKey:", CFSTR("资讯首页"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = (void *)v8(&OBJC_CLASS___NSURL, "URLWithString:", v7);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = (void *)v11(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v10);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v4, "loadRequest:", v13);
  v15(v10);
  v16(v7);
  v17(v26);
  v18(v4);
  v19(v24);
  v20 = v25;
  WeakRetained = objc_loadWeakRetained((id *)v25 + 9);
  _objc_msgSend(v20, "setSelectedItem:", WeakRetained);
  v22(WeakRetained);
}

//----- (000000010060AB95) ----------------------------------------------------
void __cdecl -[ZiXunViewController viewWillAppear](ZiXunViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunViewController;
  objc_msgSendSuper2(&v2, "viewWillAppear");
  +[UserLogSendingQueueManager updatePagePrefix:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "updatePagePrefix:",
    CFSTR("资讯"));
}

//----- (000000010060ABDF) ----------------------------------------------------
void __cdecl -[ZiXunViewController viewDidAppear](ZiXunViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunViewController;
  -[HXBaseViewController viewDidAppear](&v2, "viewDidAppear");
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    10LL,
    CFSTR("pageshow"),
    0LL,
    1LL);
}

//----- (000000010060AC37) ----------------------------------------------------
NSArray *__cdecl -[ZiXunViewController toolBarItems](ZiXunViewController *self, SEL a2)
{
  NSArray *v6; // rax
  NSArray *v7; // rax
  id WeakRetained; // [rsp+8h] [rbp-68h]
  _QWORD v13[5]; // [rsp+18h] [rbp-58h] BYREF

  v3 = *(void **)&self->super.super._reserved;
  if ( !v3 )
  {
    WeakRetained = objc_loadWeakRetained(&self->super.super._autounbinder);
    v13[0] = WeakRetained;
    v12 = objc_loadWeakRetained((id *)&self->super.super._designNibBundleIdentifier);
    v13[1] = v12;
    v13[2] = objc_loadWeakRetained(&self->super.super.__privateData);
    v4 = objc_loadWeakRetained((id *)&self->super.super._viewIsAppearing);
    v13[3] = v4;
    v5 = objc_loadWeakRetained((id *)&self->super.super._isContentViewController);
    v13[4] = v5;
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v13, 5LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = *(void **)&self->super.super._reserved;
    *(_QWORD *)&self->super.super._reserved = v7;
    v3 = *(void **)&self->super.super._reserved;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010060AD71) ----------------------------------------------------
void __cdecl -[ZiXunViewController initObjects](ZiXunViewController *self, SEL a2)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // rbx
  HXBaseView *v7; // rax
  HXBaseView *v8; // rbx
  SEL v9; // r12
  HXBaseView *v13; // rax
  HXBaseView *v14; // rbx

  v2 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ZiXunViewController topView](self, "topView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseView setBackgroundColor:](v4, "setBackgroundColor:", v5);
  v7 = -[ZiXunViewController topView](self, "topView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[HXBaseView setBottomBorder:](v8, "setBottomBorder:", 1LL);
  v10 = _objc_msgSend(self, v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "setBorderWidth:", 2.0);
  v12 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v12);
  v13 = -[ZiXunViewController topView](self, "topView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[HXBaseView setBorderColor:](v14, "setBorderColor:", v15);
  -[ZiXunViewController setUpBtns](self, "setUpBtns");
}

//----- (000000010060AEAF) ----------------------------------------------------
void __cdecl -[ZiXunViewController setWebViewDelegates](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // r15

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  _objc_msgSend(WeakRetained, "setPolicyDelegate:", self);
  v3 = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  v4(v3, "setUIDelegate:", self);
  v5 = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  v6(v5, "setFrameLoadDelegate:", self);
}

//----- (000000010060AF4B) ----------------------------------------------------
void __cdecl -[ZiXunViewController loadWebViewWithURL:](ZiXunViewController *self, SEL a2, id a3)
{
  NSURL *v5; // rax
  NSURL *v6; // r14
  NSURLRequest *v8; // rax
  NSURLRequest *v9; // rbx
  id WeakRetained; // [rsp+8h] [rbp-38h]

  objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  v3 = _objc_msgSend(WeakRetained, "mainFrame");
  v12 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v6);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v12, "loadRequest:", v9);
}

//----- (000000010060B029) ----------------------------------------------------
void __cdecl -[ZiXunViewController insertJSCModel](ZiXunViewController *self, SEL a2)
{
  WebView *v2; // rax
  WebView *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12

  v2 = -[ZiXunViewController webView](self, "webView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "mainFrame");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "javaScriptContext");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = objc_alloc((Class)&OBJC_CLASS___ThsMacJSModel);
  v12 = v11(v10, "init");
  v13(v12, "setJsContext:", v9);
  v14(v9, "setObject:forKeyedSubscript:", v12, CFSTR("ThsMacJSModel"));
  v15(v9, "setExceptionHandler:", &stru_1012E3848);
}

//----- (000000010060B109) ----------------------------------------------------
void __cdecl sub_10060B109(id a1, JSContext *a2, JSValue *a3)
{
  ;
}

//----- (000000010060B10F) ----------------------------------------------------
void __cdecl -[ZiXunViewController setUpBtns](ZiXunViewController *self, SEL a2)
{
  NSArray *v2; // rax
  unsigned __int64 i; // r12
  SEL v20; // [rsp+48h] [rbp-138h]
  SEL v21; // [rsp+50h] [rbp-130h]
  SEL v22; // [rsp+58h] [rbp-128h]
  SEL v23; // [rsp+60h] [rbp-120h]
  SEL v24; // [rsp+68h] [rbp-118h]
  SEL v25; // [rsp+70h] [rbp-110h]
  SEL v26; // [rsp+78h] [rbp-108h]
  SEL v27; // [rsp+80h] [rbp-100h]
  SEL v28; // [rsp+88h] [rbp-F8h]
  SEL v29; // [rsp+90h] [rbp-F0h]
  SEL v30; // [rsp+98h] [rbp-E8h]
  SEL v31; // [rsp+A0h] [rbp-E0h]
  SEL v32; // [rsp+A8h] [rbp-D8h]
  SEL v33; // [rsp+B0h] [rbp-D0h]
  id obj; // [rsp+C8h] [rbp-B8h]

  v16 = 0LL;
  v17 = 0LL;
  v18 = 0LL;
  v19 = 0LL;
  v2 = -[ZiXunViewController toolBarItems](self, "toolBarItems");
  obj = objc_retainAutoreleasedReturnValue(v2);
  v35 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v16, v37, 16LL);
  if ( v35 )
  {
    v34 = *(_QWORD *)v17;
    do
    {
      v20 = "cell";
      v21 = "setHighlightsBy:";
      v22 = "setShouldTracking:";
      v23 = "setCanBeSelected:";
      v24 = "normalTextColor";
      v25 = "setTextColorDefault:";
      v26 = "secondNavigationBarBgColor";
      v27 = "setBackgroundColor:";
      v28 = "secondNavigationBarSelectedColor";
      v29 = "setBackgroundColorSelected:";
      v30 = "setRightBorder:";
      v31 = "setBorderWidth:";
      v32 = "minorModuleLineColor";
      v33 = "setBorderColor:";
      for ( i = 0LL; i < (unsigned __int64)v35; i = v15 + 1 )
      {
        if ( *(_QWORD *)v17 != v34 )
          objc_enumerationMutation(obj);
        v4 = *(void **)(*((_QWORD *)&v16 + 1) + 8 * i);
        v5 = _objc_msgSend(v4, v20);
        v6 = objc_retainAutoreleasedReturnValue(v5);
        _objc_msgSend(v6, v21, 1LL);
        _objc_msgSend(v4, v22, 1LL);
        _objc_msgSend(v4, v23, 1LL);
        v7 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v24);
        v8 = objc_retainAutoreleasedReturnValue(v7);
        _objc_msgSend(v4, v25, v8);
        v9 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v26);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        _objc_msgSend(v4, v27, v10);
        v11 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v28);
        v12 = objc_retainAutoreleasedReturnValue(v11);
        _objc_msgSend(v4, v29, v12);
        _objc_msgSend(v4, v30, 1LL);
        _objc_msgSend(v4, v31, 1.0);
        v13 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v32);
        v14 = objc_retainAutoreleasedReturnValue(v13);
        _objc_msgSend(v4, v33, v14);
      }
      v35 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v16, v37, 16LL);
    }
    while ( v35 );
  }
}

//----- (000000010060B460) ----------------------------------------------------
void __cdecl -[ZiXunViewController setSelectedItem:](ZiXunViewController *self, SEL a2, id a3)
{
  unsigned __int64 i; // r13
  SEL v15; // [rsp+40h] [rbp-F0h]
  SEL v16; // [rsp+48h] [rbp-E8h]
  SEL v17; // [rsp+50h] [rbp-E0h]
  SEL v18; // [rsp+58h] [rbp-D8h]
  id obj; // [rsp+78h] [rbp-B8h]

  v3 = objc_retain(a3);
  v21 = objc_retain(v3);
  v11 = 0LL;
  v12 = 0LL;
  v13 = 0LL;
  v14 = 0LL;
  obj = objc_retain(*(id *)&self->super.super._reserved);
  v20 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v11, v23, 16LL);
  if ( v20 )
  {
    v19 = *(_QWORD *)v12;
    do
    {
      v15 = "setRightBorder:";
      v16 = "setBorderWidth:";
      v17 = "majorModuleLineColor";
      v18 = "setBorderColor:";
      for ( i = 0LL; i < (unsigned __int64)v20; ++i )
      {
        if ( *(_QWORD *)v12 != v19 )
          objc_enumerationMutation(obj);
        _objc_msgSend(*(id *)(*((_QWORD *)&v11 + 1) + 8 * i), v15, 1LL);
        _objc_msgSend(v5, v16, 2.0);
        v6 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v17);
        v7 = objc_retainAutoreleasedReturnValue(v6);
        _objc_msgSend(v8, v18, v7);
        if ( v9 == v21 )
          _objc_msgSend(v21, "setIsSelected:", 1LL);
        else
          _objc_msgSend(v9, "setIsSelected:", 0LL);
      }
      v20 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v11, v23, 16LL);
    }
    while ( v20 );
  }
  v10 = v21;
}

//----- (000000010060B692) ----------------------------------------------------
void __cdecl -[ZiXunViewController indexBtnClicked:](ZiXunViewController *self, SEL a2, id a3)
{
  WebView *v3; // rax
  LoadLocalURLManager *v6; // rax
  NSURLRequest *v12; // rax
  NSURLRequest *v13; // rbx
  WebView *v16; // [rsp+18h] [rbp-38h]
  LoadLocalURLManager *v17; // [rsp+20h] [rbp-30h]

  v15 = objc_retain(a3);
  v3 = -[ZiXunViewController webView](self, "webView");
  v16 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v16, "mainFrame");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v17 = objc_retainAutoreleasedReturnValue(v6);
  v7 = -[LoadLocalURLManager getCurrentURLWithKey:](v17, "getCurrentURLWithKey:", CFSTR("资讯首页"));
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(v9, "URLWithString:", v8);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v11);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v5, "loadRequest:", v13);
  -[ZiXunViewController setSelectedItem:](self, "setSelectedItem:", v15);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("首页"),
    0LL,
    1LL);
}

//----- (000000010060B815) ----------------------------------------------------
void __cdecl -[ZiXunViewController financeBtnClicked:](ZiXunViewController *self, SEL a2, id a3)
{
  WebView *v3; // rax
  LoadLocalURLManager *v5; // rax
  NSURL *v8; // rax
  NSURL *v9; // r13
  NSURLRequest *v10; // rax
  NSURLRequest *v11; // rbx
  WebView *v15; // [rsp+18h] [rbp-38h]
  LoadLocalURLManager *v16; // [rsp+20h] [rbp-30h]

  v14 = objc_retain(a3);
  v3 = -[ZiXunViewController webView](self, "webView");
  v15 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v15, "mainFrame");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v16 = objc_retainAutoreleasedReturnValue(v5);
  v6 = -[LoadLocalURLManager getCurrentURLWithKey:](v16, "getCurrentURLWithKey:", CFSTR("财经头条"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v12, "loadRequest:", v11);
  -[ZiXunViewController setSelectedItem:](self, "setSelectedItem:", v14);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("财经头条"),
    0LL,
    1LL);
}

//----- (000000010060B998) ----------------------------------------------------
void __cdecl -[ZiXunViewController newsBtnClicked:](ZiXunViewController *self, SEL a2, id a3)
{
  WebView *v3; // rax
  LoadLocalURLManager *v5; // rax
  NSURL *v8; // rax
  NSURL *v9; // r13
  NSURLRequest *v10; // rax
  NSURLRequest *v11; // rbx
  WebView *v15; // [rsp+18h] [rbp-38h]
  LoadLocalURLManager *v16; // [rsp+20h] [rbp-30h]

  v14 = objc_retain(a3);
  v3 = -[ZiXunViewController webView](self, "webView");
  v15 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v15, "mainFrame");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v16 = objc_retainAutoreleasedReturnValue(v5);
  v6 = -[LoadLocalURLManager getCurrentURLWithKey:](v16, "getCurrentURLWithKey:", CFSTR("公司要闻"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v12, "loadRequest:", v11);
  -[ZiXunViewController setSelectedItem:](self, "setSelectedItem:", v14);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("公司要闻"),
    0LL,
    1LL);
}

//----- (000000010060BB1B) ----------------------------------------------------
void __cdecl -[ZiXunViewController _24hourScrollBtnClicked:](ZiXunViewController *self, SEL a2, id a3)
{
  WebView *v3; // rax
  LoadLocalURLManager *v5; // rax
  NSURL *v8; // rax
  NSURL *v9; // r13
  NSURLRequest *v10; // rax
  NSURLRequest *v11; // rbx
  WebView *v15; // [rsp+18h] [rbp-38h]
  LoadLocalURLManager *v16; // [rsp+20h] [rbp-30h]

  v14 = objc_retain(a3);
  v3 = -[ZiXunViewController webView](self, "webView");
  v15 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v15, "mainFrame");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v16 = objc_retainAutoreleasedReturnValue(v5);
  v6 = -[LoadLocalURLManager getCurrentURLWithKey:](v16, "getCurrentURLWithKey:", CFSTR("24小时滚动"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v12, "loadRequest:", v11);
  -[ZiXunViewController setSelectedItem:](self, "setSelectedItem:", v14);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("24小时滚动"),
    0LL,
    1LL);
}

//----- (000000010060BC9E) ----------------------------------------------------
void __cdecl -[ZiXunViewController marketBtnClicked:](ZiXunViewController *self, SEL a2, id a3)
{
  WebView *v3; // rax
  LoadLocalURLManager *v5; // rax
  NSURL *v8; // rax
  NSURL *v9; // r13
  NSURLRequest *v10; // rax
  NSURLRequest *v11; // rbx
  WebView *v15; // [rsp+18h] [rbp-38h]
  LoadLocalURLManager *v16; // [rsp+20h] [rbp-30h]

  v14 = objc_retain(a3);
  v3 = -[ZiXunViewController webView](self, "webView");
  v15 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v15, "mainFrame");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v16 = objc_retainAutoreleasedReturnValue(v5);
  v6 = -[LoadLocalURLManager getCurrentURLWithKey:](v16, "getCurrentURLWithKey:", CFSTR("全球市场"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v12, "loadRequest:", v11);
  -[ZiXunViewController setSelectedItem:](self, "setSelectedItem:", v14);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("全球市场"),
    0LL,
    1LL);
}

//----- (000000010060BE21) ----------------------------------------------------
void __cdecl -[ZiXunViewController use](ZiXunViewController *self, SEL a2)
{
  ;
}

//----- (000000010060BE27) ----------------------------------------------------
void __cdecl -[ZiXunViewController download](ZiXunViewController *self, SEL a2)
{
  ;
}

//----- (000000010060BE2D) ----------------------------------------------------
void __cdecl -[ZiXunViewController ignore](ZiXunViewController *self, SEL a2)
{
  ;
}

//----- (000000010060BE33) ----------------------------------------------------
void __cdecl -[ZiXunViewController webView:decidePolicyForNewWindowAction:request:newFrameName:decisionListener:](
        ZiXunViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6,
        id a7)
{

  v7 = _objc_msgSend(a5, "URL", a3, a4);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(&OBJC_CLASS___NSWorkspace, "sharedWorkspace");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "openURL:", v8);
}

//----- (000000010060BEA7) ----------------------------------------------------
id __cdecl -[ZiXunViewController webView:createWebViewWithRequest:](ZiXunViewController *self, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v8 = _objc_msgSend(v7, "webView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( v9 == v5 )
  {
    v10 = _objc_msgSend(v5, "mainFrame");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "loadRequest:", v6);
    objc_retain(v5);
  }
  return objc_autoreleaseReturnValue(v13);
}

//----- (000000010060BF64) ----------------------------------------------------
id __cdecl -[ZiXunViewController webView:contextMenuItemsForElement:defaultMenuItems:](
        ZiXunViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  id (*v7)(id, SEL, ...); // r12
  unsigned __int64 i; // r13
  NSArray *v14; // rax
  NSArray *v15; // r15
  id obj; // [rsp+68h] [rbp-B8h]

  v5 = objc_retain(a5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v25 = objc_retainAutoreleasedReturnValue(v6);
  v18 = 0LL;
  v19 = 0LL;
  v20 = 0LL;
  v21 = 0LL;
  obj = objc_retain(v5);
  v24 = v7(obj, "countByEnumeratingWithState:objects:count:", &v18, v27, 16LL);
  if ( v24 )
  {
    v22 = *(_QWORD *)v19;
    do
    {
      v9 = v8;
      v10 = "tag";
      v23 = "addObject:";
      for ( i = 0LL; i < (unsigned __int64)v24; ++i )
      {
        if ( *(_QWORD *)v19 != v22 )
          objc_enumerationMutation(obj);
        v12 = *(_QWORD *)(*((_QWORD *)&v18 + 1) + 8 * i);
        if ( ((__int64 (__fastcall *)(__int64, const char *))v9)(v12, v10) != 9
          && ((__int64 (__fastcall *)(__int64, const char *))v9)(v12, v10) != 10 )
        {
          v9(v25, v23, v12);
        }
      }
      v24 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v9)(
                  obj,
                  "countByEnumeratingWithState:objects:count:",
                  &v18,
                  v27,
                  16LL);
    }
    while ( v24 );
  }
  v13 = v25;
  v14 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v25);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  return objc_autoreleaseReturnValue(v15);
}

//----- (000000010060C158) ----------------------------------------------------
void __cdecl -[ZiXunViewController webView:didCreateJavaScriptContext:forFrame:](
        ZiXunViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  -[ZiXunViewController insertJSCModel](self, "insertJSCModel", a3, a4, a5);
}

//----- (000000010060C16A) ----------------------------------------------------
WebView *__cdecl -[ZiXunViewController webView](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  return (WebView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C183) ----------------------------------------------------
void __cdecl -[ZiXunViewController setWebView:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._topLevelObjects, a3);
}

//----- (000000010060C197) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunViewController topView](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C1B0) ----------------------------------------------------
void __cdecl -[ZiXunViewController setTopView:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (000000010060C1C4) ----------------------------------------------------
HXButton *__cdecl -[ZiXunViewController indexBtn](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._autounbinder);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C1DD) ----------------------------------------------------
void __cdecl -[ZiXunViewController setIndexBtn:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super._autounbinder, a3);
}

//----- (000000010060C1F1) ----------------------------------------------------
HXButton *__cdecl -[ZiXunViewController financeBtn](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._designNibBundleIdentifier);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C20A) ----------------------------------------------------
void __cdecl -[ZiXunViewController setFinanceBtn:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._designNibBundleIdentifier, a3);
}

//----- (000000010060C21E) ----------------------------------------------------
HXButton *__cdecl -[ZiXunViewController newsBtn](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super.__privateData);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C237) ----------------------------------------------------
void __cdecl -[ZiXunViewController setNewsBtn:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super.__privateData, a3);
}

//----- (000000010060C24B) ----------------------------------------------------
HXButton *__cdecl -[ZiXunViewController _24hourScrollBtn](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._viewIsAppearing);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C264) ----------------------------------------------------
void __cdecl -[ZiXunViewController set_24hourScrollBtn:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._viewIsAppearing, a3);
}

//----- (000000010060C278) ----------------------------------------------------
HXButton *__cdecl -[ZiXunViewController marketBtn](ZiXunViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._isContentViewController);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010060C291) ----------------------------------------------------
void __cdecl -[ZiXunViewController setMarketBtn:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._isContentViewController, a3);
}

//----- (000000010060C2A5) ----------------------------------------------------
void __cdecl -[ZiXunViewController setToolBarItems:](ZiXunViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 112LL);
}

//----- (000000010060C2B6) ----------------------------------------------------
void __cdecl -[ZiXunViewController .cxx_destruct](ZiXunViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super.super._reserved, 0LL);
  objc_destroyWeak((id *)&self->super.super._isContentViewController);
  objc_destroyWeak((id *)&self->super.super._viewIsAppearing);
  objc_destroyWeak(&self->super.super.__privateData);
  objc_destroyWeak((id *)&self->super.super._designNibBundleIdentifier);
  objc_destroyWeak(&self->super.super._autounbinder);
  objc_destroyWeak((id *)&self->super.super._editors);
  objc_destroyWeak((id *)&self->super.super._topLevelObjects);
}

