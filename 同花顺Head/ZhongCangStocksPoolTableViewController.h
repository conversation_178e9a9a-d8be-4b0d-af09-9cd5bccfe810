//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "QuoteBaseTableViewController.h"

#import "ReceiveDispatchData-Protocol.h"

@class HXBaseView, HXTableRequestModule, NSButton, NSMutableArray, NSString;

@interface ZhongCangStocksPoolTableViewController : QuoteBaseTableViewController <ReceiveDispatchData>
{
    NSString *_tableKey;
    HXBaseView *_noStocksTipView;
    NSButton *_noStocksTipBtn;
    HXBaseView *_instructionTipView;
    NSMutableArray *_completeCodeAndDataArr;
    HXTableRequestModule *_backgroundCodeListSortRequestModule;
    HXTableRequestModule *_backgroundDetailDataRequestModule;
}


@property(retain, nonatomic) HXTableRequestModule *backgroundDetailDataRequestModule; // @synthesize backgroundDetailDataRequestModule=_backgroundDetailDataRequestModule;
@property(retain, nonatomic) HXTableRequestModule *backgroundCodeListSortRequestModule; // @synthesize backgroundCodeListSortRequestModule=_backgroundCodeListSortRequestModule;
@property(retain, nonatomic) NSMutableArray *completeCodeAndDataArr; // @synthesize completeCodeAndDataArr=_completeCodeAndDataArr;
@property(retain, nonatomic) HXBaseView *instructionTipView; // @synthesize instructionTipView=_instructionTipView;
@property __weak NSButton *noStocksTipBtn; // @synthesize noStocksTipBtn=_noStocksTipBtn;
@property __weak HXBaseView *noStocksTipView; // @synthesize noStocksTipView=_noStocksTipView;
- (long long)compareInteger:(long long)arg1 withInteger:(long long)arg2;
- (long long)compareDouble:(double)arg1 withDouble:(double)arg2;
- (long long)frozenCount;
- (id)tableKey;
- (long long)numberOfRowsInFrozenTableView:(id)arg1;
- (id)frozenTableView:(id)arg1 dataCellForTableColumn:(id)arg2 row:(long long)arg3;
- (id)frozenTableView:(id)arg1 menuForHeaderOfTableColumn:(id)arg2;
- (void)closeInstructionBtnClicked:(id)arg1;
- (void)actionForTableViewSelectionDidChange:(long long)arg1;
- (void)myTableIsDoubleClicked:(id)arg1;
- (void)requestBackgroundDetailDataWithCodeList:(id)arg1;
- (void)requestBackgroundCodeListSort;
- (id)getWenCaiDataWithOrderCodeArr;
- (id)calculateItemsWithRequestData:(id)arg1;
- (void)dealWithRequestData:(id)arg1 extension:(id)arg2;
- (void)requestDetailData;
- (id)getOrderCodeArrWhenSortByWenCai;
- (id)getCompleteCodeList;
- (void)requestCodeList;
- (void)resetCompleteCodeListWithDataItems:(id)arg1;
- (void)failToReceiveStuffData:(long long)arg1;
- (void)receiveStuffData:(id)arg1;
- (void)requestCompleteCodeListWithDataItems;
- (void)showOrHideNoStocksTipView;
- (void)setOriginalSortInfoForMyFrozenTable;
- (void)setInstructionTipShowed;
- (BOOL)isInstructionTipShowed;
- (void)frameDidChange:(id)arg1;
- (void)requestForMyTable;
- (void)initViewState;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

