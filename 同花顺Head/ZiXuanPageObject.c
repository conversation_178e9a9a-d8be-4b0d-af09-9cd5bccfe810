ZiXuanPageObject *__cdecl -[ZiXuanPageObject initWithZiXuanGuVC:](ZiXuanPageObject *self, SEL a2, id a3)
{
  ZiXuanPageObject *v4; // r15
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v15.receiver = self;
  v15.super_class = (Class)&OBJC_CLASS___ZiXuanPageObject;
  v4 = objc_msgSendSuper2(&v15, "init");
  if ( v4 )
  {
    v5 = _objc_msgSend(v3, "createTabItemObjects");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7(v4, "setTabItemObjects:", v6);
    v9 = v8(v3, "getPageTabContainer");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = v11(v10, "selectedIndex");
    v13(v4, "setSelectedIndex:", v12);
  }
  return v4;
}

//----- (000000010029641F) ----------------------------------------------------
ZiXuanPageObject *__cdecl -[ZiXuanPageObject initWithCoder:](ZiXuanPageObject *self, SEL a2, id a3)
{
  ZiXuanPageObject *v4; // r15
  id (*v8)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v12.receiver = self;
  v12.super_class = (Class)&OBJC_CLASS___ZiXuanPageObject;
  v4 = objc_msgSendSuper2(&v12, "init");
  if ( v4 )
  {
    v5 = _objc_msgSend(v3, "decodeObjectForKey:", CFSTR("ZiXuanPageObject.tabItemObjects"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7(v4, "setTabItemObjects:", v6);
    v9 = v8(v3, "decodeIntegerForKey:", CFSTR("ZiXuanPageObject.selectedIndex"));
    v10(v4, "setSelectedIndex:", v9);
  }
  return v4;
}

//----- (00000001002964DF) ----------------------------------------------------
void __cdecl -[ZiXuanPageObject encodeWithCoder:](ZiXuanPageObject *self, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx

  objc_retain(a3);
  v3 = -[ZiXuanPageObject tabItemObjects](self, "tabItemObjects");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v5, "encodeObject:forKey:", v4, CFSTR("ZiXuanPageObject.tabItemObjects"));
  v6 = -[ZiXuanPageObject selectedIndex](self, "selectedIndex");
  _objc_msgSend(v7, "encodeInteger:forKey:", v6, CFSTR("ZiXuanPageObject.selectedIndex"));
}

//----- (000000010029657C) ----------------------------------------------------
id __cdecl -[ZiXuanPageObject copy](ZiXuanPageObject *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12

  v2 = objc_alloc((Class)&OBJC_CLASS___ZiXuanPageObject);
  v3 = _objc_msgSend(v2, "init");
  v5 = v4(self, "tabItemObjects");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v3, "setTabItemObjects:", v6);
  v9 = v8(self, "selectedIndex");
  v10(v3, "setSelectedIndex:", v9);
  return v3;
}

//----- (0000000100296607) ----------------------------------------------------
NSArray *__cdecl -[ZiXuanPageObject tabItemObjects](ZiXuanPageObject *self, SEL a2)
{
  return self->_tabItemObjects;
}

//----- (0000000100296611) ----------------------------------------------------
void __cdecl -[ZiXuanPageObject setTabItemObjects:](ZiXuanPageObject *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_tabItemObjects, a3);
}

//----- (0000000100296622) ----------------------------------------------------
signed __int64 __cdecl -[ZiXuanPageObject selectedIndex](ZiXuanPageObject *self, SEL a2)
{
  return self->_selectedIndex;
}

//----- (000000010029662C) ----------------------------------------------------
void __cdecl -[ZiXuanPageObject setSelectedIndex:](ZiXuanPageObject *self, SEL a2, signed __int64 a3)
{
  self->_selectedIndex = a3;
}

//----- (0000000100296636) ----------------------------------------------------
void __cdecl -[ZiXuanPageObject .cxx_destruct](ZiXuanPageObject *self, SEL a2)
{
  objc_storeStrong((id *)&self->_tabItemObjects, 0LL);
}

//----- (0000000100296646) ----------------------------------------------------
__int64 __fastcall sub_100296646(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  __m128i si128; // xmm0
  __m128i v6; // xmm1
  __m128i v7; // xmm1
  __m128i v9; // xmm1
  __m128i v10; // xmm1
  unsigned int v14; // r13d

  *(_DWORD *)(a1 + 832) = a4;
  v4 = 0LL;
  si128 = _mm_load_si128((const __m128i *)&xmmword_1010CF4A0);
  do
  {
    v6 = _mm_shuffle_epi32(_mm_cvtsi32_si128(*(unsigned __int8 *)(a3 + v4)), 0);
    v7 = _mm_packus_epi16(
           (__m128i)_mm_shuffle_ps(
                      (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v6, 7u), _mm_srli_epi32(v6, 6u)),
                      (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v6, 5u), _mm_srli_epi32(v6, 4u)),
                      204),
           (__m128i)_mm_shuffle_ps(
                      (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v6, 3u), _mm_srli_epi32(v6, 2u)),
                      (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v6, 1u), v6),
                      204));
    *(_QWORD *)(a1 + 8 * v4++ + 768) = _mm_and_si128(_mm_packus_epi16(v7, v7), si128).u64[0];
  }
  while ( v4 != 8 );
  for ( i = 0LL; i != 8; ++i )
  {
    v9 = _mm_shuffle_epi32(_mm_cvtsi32_si128(*(unsigned __int8 *)(a2 + i)), 0);
    v10 = _mm_packus_epi16(
            (__m128i)_mm_shuffle_ps(
                       (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v9, 7u), _mm_srli_epi32(v9, 6u)),
                       (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v9, 5u), _mm_srli_epi32(v9, 4u)),
                       204),
            (__m128i)_mm_shuffle_ps(
                       (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v9, 3u), _mm_srli_epi32(v9, 2u)),
                       (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v9, 1u), v9),
                       204));
    v22[i] = _mm_and_si128(_mm_packus_epi16(v10, v10), si128).u64[0];
  }
  for ( j = 0LL; j != 56; ++j )
    v24[j - 1] = *((_BYTE *)v22 + byte_1010CF550[j] - 1);
  v12 = v24;
  v13 = 0LL;
  do
  {
    v21 = a1;
    v14 = 0;
    v20 = v13;
    do
    {
      v15 = v23;
      sub_10078E38E(&v23, v12, 0x1Bu);
      v24[26] = v15;
      v16 = v25;
      sub_10078E38E(&v25, v26, 0x1Bu);
      v26[26] = v16;
      ++v14;
    }
    while ( v14 < byte_1010CF590[v13] );
    v17 = 0LL;
    v18 = v21;
    do
    {
      *(_BYTE *)(v18 + v17) = v24[byte_1010CF5A0[v17] - 2];
      ++v17;
    }
    while ( v17 != 48 );
    ++v13;
    a1 = v18 + 48;
  }
  while ( v20 != 15 );
  sub_10078E37C(&v23, 0, 0x38u);
  sub_10078E37C(v22, 0, 0x40u);
  return __stack_chk_guard;
}

//----- (000000010029688A) ----------------------------------------------------
__int64 __fastcall sub_10029688A(__int64 a1, __int64 a2, __int64 a3, unsigned int a4)
{
  unsigned int v4; // r12d
  unsigned int v5; // ecx
  __m128i si128; // xmm4
  __m128i v11; // xmm0
  __m128i v12; // xmm0
  bool v13; // zf
  _BYTE *v18; // r12
  const void *v26; // r12
  __m128i v31; // xmm2
  __m128i v32; // xmm3
  __m128i v33; // xmm4
  __m128i v36; // xmm0
  __m128i v37; // xmm0
  __m128i v38; // xmm1
  __m128i v39; // xmm0
  _BYTE v50[64]; // [rsp+90h] [rbp-110h] BYREF

  v4 = 1030;
  if ( (a4 & 7) == 0 )
  {
    v5 = a4 >> 3;
    if ( v5 )
    {
      v7 = a1;
      v44 = v5;
      v8 = 0LL;
      si128 = _mm_load_si128((const __m128i *)&xmmword_1010CF4A0);
      do
      {
        for ( i = 0LL; i != 8; ++i )
        {
          v11 = _mm_shuffle_epi32(_mm_cvtsi32_si128(*(unsigned __int8 *)(a3 + i)), 0);
          v12 = _mm_packus_epi16(
                  (__m128i)_mm_shuffle_ps(
                             (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v11, 7u), _mm_srli_epi32(v11, 6u)),
                             (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v11, 5u), _mm_srli_epi32(v11, 4u)),
                             204),
                  (__m128i)_mm_shuffle_ps(
                             (__m128)_mm_unpacklo_epi64(_mm_srli_epi32(v11, 3u), _mm_srli_epi32(v11, 2u)),
                             (__m128)_mm_unpackhi_epi64(_mm_srli_epi32(v11, 1u), v11),
                             204));
          v54[i] = _mm_and_si128(_mm_packus_epi16(v12, v12), si128).u64[0];
        }
        v13 = *(_DWORD *)(v7 + 832) == 0;
        v43 = a3;
        v45 = v8;
        if ( v13 )
        {
          for ( j = 0LL; j != 64; ++j )
            v52[j] = *((_BYTE *)v54 + byte_1010CF4D0[j] - 1);
        }
        else
        {
          for ( k = 0LL; k != 64; ++k )
            v52[k] = v53[byte_1010CF4D0[k] + 31] ^ *(_BYTE *)(v7 + byte_1010CF4D0[k] + 767);
        }
        v16 = a1 + 720;
        v17 = 0LL;
        do
        {
          v42 = v17;
          sub_10078E38E(v51, v53, 0x20u);
          if ( *(_DWORD *)(a1 + 832) )
          {
            for ( m = 0LL; m != 48; ++m )
              v48[m] = v18[byte_1010CF5D0[m] + 31] ^ *(_BYTE *)(v7 + m);
          }
          else
          {
            for ( n = 0LL; n != 48; ++n )
              v48[n] = v18[byte_1010CF5D0[n] + 31] ^ *(_BYTE *)(v16 + n);
          }
          v21 = &v47;
          v22 = 0LL;
          v23 = &v49;
          do
          {
            v24 = byte_1010CF600[v22
                               + ((32LL * (unsigned __int8)*(v23 - 5)) | (16LL * (unsigned __int8)*v23) | (8LL * (unsigned __int8)*(v23 - 4)) | (4LL * (unsigned __int8)*(v23 - 3)) | (2LL * (unsigned __int8)*(v23 - 2)) | (unsigned __int8)*(v23 - 1))];
            *(v21 - 3) = (v24 & 8) != 0;
            *(v21 - 2) = (v24 & 4) != 0;
            *(v21 - 1) = (v24 & 2) != 0;
            *v21 = v24 & 1;
            v23 += 6;
            v22 += 64LL;
            v21 += 4;
          }
          while ( v22 != 512 );
          for ( ii = 0LL; ii != 32; ++ii )
            v53[ii] = v52[ii] ^ v46[byte_1010CF800[ii] - 1];
          sub_10078E38E(v18, v51, 0x20u);
          v17 = v42 + 1;
          v7 += 48LL;
          v16 -= 48LL;
        }
        while ( v42 != 15 );
        sub_10078E38E(v51, v53, 0x20u);
        sub_10078E38E(v53, v26, 0x20u);
        sub_10078E38E(v27, v51, 0x20u);
        v7 = a1;
        if ( *(_DWORD *)(a1 + 832) )
        {
          v29 = 0LL;
          v30 = v45;
          v31 = _mm_load_si128((const __m128i *)&xmmword_1010CF4B0);
          v32 = _mm_load_si128((const __m128i *)&xmmword_1010CF4C0);
          v33 = 0LL;
          do
          {
            v50[v29] = *(_BYTE *)(byte_1010CF510[v29] + v28 - 1);
            ++v29;
          }
          while ( v29 != 64 );
        }
        else
        {
          v34 = 0LL;
          v30 = v45;
          v31 = _mm_load_si128((const __m128i *)&xmmword_1010CF4B0);
          v32 = _mm_load_si128((const __m128i *)&xmmword_1010CF4C0);
          v33 = 0LL;
          do
          {
            v50[v34] = *(_BYTE *)(byte_1010CF510[v34] + v28 - 1) ^ *(_BYTE *)(a1 + v34 + 768);
            ++v34;
          }
          while ( v34 != 64 );
        }
        for ( jj = 0LL; jj != 8; ++jj )
        {
          v36 = _mm_loadl_epi64((const __m128i *)&v50[8 * jj]);
          v37 = _mm_packus_epi16(_mm_and_si128(_mm_mullo_epi16(_mm_unpacklo_epi8(v36, v36), v31), v32), v33);
          v38 = _mm_or_si128(_mm_shuffle_epi32(v37, 85), v37);
          v39 = _mm_or_si128(_mm_srli_epi32(v38, 0x10u), v38);
          *(_BYTE *)(a2 + jj) = _mm_cvtsi128_si32(_mm_or_si128(_mm_srli_epi16(v39, 8u), v39));
        }
        v40 = (__int64 *)v50;
        if ( !*(_DWORD *)(a1 + 832) )
          v40 = v54;
        sub_10078E38E((void *)(a1 + 768), v40, 0x40u);
        v8 = v30 + 1;
        a3 = v43 + 8;
        a2 += 8LL;
        si128 = _mm_load_si128((const __m128i *)&xmmword_1010CF4A0);
      }
      while ( v8 != v44 );
    }
    sub_10078E37C(v54, 0, 0x40u);
    sub_10078E37C(v52, 0, 0x40u);
    sub_10078E37C(v51, 0, 0x20u);
    sub_10078E37C(v50, 0, 0x40u);
    sub_10078E37C(v48, 0, 0x30u);
    sub_10078E37C(v46, 0, 0x20u);
  }
  return v4;
}

//----- (0000000100296D97) ----------------------------------------------------
void *__fastcall sub_100296D97(void *a1)
{
  return sub_10078E37C(a1, 0, 0x344u);
}

