void __cdecl -[comboitemview drawRect:](comboitemview *self, SEL a2, CGRect a3)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___comboitemview;
  objc_msgSendSuper2(&v3, "drawRect:");
}

//----- (0000000100CDD2CE) ----------------------------------------------------
void __cdecl -[comboitemview onDelete:](comboitemview *self, SEL a2, id a3)
{
  CGFloat *p_y; // rbx
  id WeakRetained; // rax

  p_y = &self->super._frame.origin.y;
  WeakRetained = objc_loadWeakRetained((id *)&self->super._frame.origin.y);
  if ( WeakRetained )
  {
    v5 = WeakRetained;
    v6 = objc_loadWeakRetained((id *)p_y);
    v16 = (unsigned __int8)_objc_msgSend(v6, "respondsToSelector:", "deleteComboItem:str:");
    if ( v16 )
    {
      v8 = objc_loadWeakRetained((id *)p_y);
      objc_loadWeakRetained((id *)&self->super._frame);
      v9 = objc_loadWeakRetained(&self->super.super._nextResponder);
      v10 = _objc_msgSend(v9, "cell");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(v11, "title");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      _objc_msgSend(v8, "deleteComboItem:str:", v14, v13);
    }
  }
}

//----- (0000000100CDD3F9) ----------------------------------------------------
void __cdecl +[comboitemview DoNothing](id a1, SEL a2)
{
  ;
}

//----- (0000000100CDD3FF) ----------------------------------------------------
NSTextField *__cdecl -[comboitemview tfShowText](comboitemview *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._nextResponder);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CDD418) ----------------------------------------------------
void __cdecl -[comboitemview setTfShowText:](comboitemview *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super._nextResponder, a3);
}

//----- (0000000100CDD42C) ----------------------------------------------------
NSComboBox *__cdecl -[comboitemview ParentNSComBoBox](comboitemview *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._frame);
  return (NSComboBox *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CDD445) ----------------------------------------------------
void __cdecl -[comboitemview setParentNSComBoBox:](comboitemview *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._frame, a3);
}

//----- (0000000100CDD459) ----------------------------------------------------
comboItemActionDelegate *__cdecl -[comboitemview comboItemDelegate](comboitemview *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._frame.origin.y);
  return (comboItemActionDelegate *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CDD472) ----------------------------------------------------
void __cdecl -[comboitemview setComboItemDelegate:](comboitemview *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._frame.origin.y, a3);
}

//----- (0000000100CDD486) ----------------------------------------------------
void __cdecl -[comboitemview .cxx_destruct](comboitemview *self, SEL a2)
{
  objc_destroyWeak((id *)&self->super._frame.origin.y);
  objc_destroyWeak((id *)&self->super._frame);
  objc_destroyWeak(&self->super.super._nextResponder);
}

