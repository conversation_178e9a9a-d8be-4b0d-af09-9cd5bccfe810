id __cdecl +[ZiXunReqParamsManager sharedInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_10056F96B;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D3048 != -1 )
    dispatch_once(&qword_1016D3048, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3040);
}

//----- (000000010056F96B) ----------------------------------------------------
void __fastcall sub_10056F96B(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3040;
  qword_1016D3040 = v2;
}

//----- (000000010056F99D) ----------------------------------------------------
unsigned __int64 __cdecl +[ZiXunReqParamsManager getHXZiXunTypeWithMarket:stockCode:](id a1, SEL a2, id a3, id a4)
{
  unsigned __int64 v7; // rbx

  v5 = objc_retain(a4);
  v6 = +[HXTools getStockType:stockCode:](&OBJC_CLASS___HXTools, "getStockType:stockCode:", a3, v5);
  v7 = 13LL;
  switch ( (unsigned __int64)v6 )
  {
    case 2uLL:
      v7 = 3LL;
      break;
    case 3uLL:
      v7 = 10LL;
      if ( (unsigned __int64)_objc_msgSend(v5, "length") >= 4 )
      {
        v8 = _objc_msgSend(v5, "substringToIndex:", 4LL);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        v7 = 11LL - ((unsigned __int8)_objc_msgSend(v9, "isEqualToString:", CFSTR("8839")) == 0);
      }
      break;
    case 4uLL:
      v7 = 6LL;
      break;
    case 5uLL:
      v7 = 4LL;
      break;
    case 6uLL:
    case 0xEuLL:
    case 0x18uLL:
      v7 = 12LL;
      break;
    case 7uLL:
    case 0x10uLL:
    case 0x13uLL:
    case 0x14uLL:
    case 0x15uLL:
    case 0x16uLL:
    case 0x17uLL:
      v7 = (unsigned __int64)v6;
      break;
    case 8uLL:
      v7 = 5LL;
      break;
    case 9uLL:
      v7 = 2LL;
      break;
    case 0xAuLL:
      v7 = 9LL;
      break;
    case 0xBuLL:
      break;
    case 0xCuLL:
      v7 = 14LL;
      break;
    case 0xDuLL:
      v7 = 18LL;
      break;
    case 0xFuLL:
    case 0x11uLL:
    case 0x19uLL:
      v7 = 15LL;
      break;
    case 0x12uLL:
      v7 = 17LL;
      break;
    default:
      v7 = 1LL;
      break;
  }
  return v7;
}

//----- (000000010056FB38) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager getZiXunRequestParamsWithMarket:stockCode:](
        ZiXunReqParamsManager *self,
        SEL a2,
        id a3,
        id a4)
{

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length") )
  {
    v11 = +[ZiXunReqParamsManager getHXZiXunTypeWithMarket:stockCode:](
            &OBJC_CLASS___ZiXunReqParamsManager,
            "getHXZiXunTypeWithMarket:stockCode:",
            v10,
            v5);
    v13 = (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", CFSTR("UIFB"));
    v14 = 3LL;
    if ( !v13 )
      v14 = (__int64)v11;
    v15 = -[ZiXunReqParamsManager getZiXunRequestParamsByType:](self, "getZiXunRequestParamsByType:", v14);
    v16 = objc_retainAutoreleasedReturnValue(v15);
  }
  else
  {
    v16 = objc_retain(__NSArray0__);
  }
  v17 = v16;
  return objc_autoreleaseReturnValue(v17);
}

//----- (000000010056FC6D) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager getZiXunRequestParamsByType:](
        ZiXunReqParamsManager *self,
        SEL a2,
        unsigned __int64 a3)
{

  if ( a3 - 2 > 0x16 )
    v3 = &selRef_constructDefaultZiXunRequestParam;
  else
    v3 = off_1012E2C30[a3 - 2];
  v4 = _objc_msgSend(self, *v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (000000010056FCAA) ----------------------------------------------------
id __cdecl +[ZiXunReqParamsManager getZiXunTitle:market:](id a1, SEL a2, unsigned __int64 a3, id a4)
{
  __CFString *v6; // r15
  NSArray *v8; // rax
  NSArray *v9; // rbx
  bool v11; // zf
  _QWORD v14[2]; // [rsp+0h] [rbp-30h] BYREF

  v5 = objc_retain(a4);
  v6 = (__CFString *)obj;
  if ( (__int64)a3 <= 14338 )
  {
    if ( (__int64)a3 > 6145 )
    {
      switch ( a3 )
      {
        case 0x1802uLL:
          v6 = CFSTR("【数据】");
          break;
        case 0x1CC3uLL:
LABEL_23:
          v6 = CFSTR("【解盘】");
          v7 = (unsigned __int8)_objc_msgSend(CFSTR("【解盘】"), "isEqualToString:", obj);
          goto LABEL_34;
        case 0x1CC4uLL:
          goto LABEL_22;
      }
    }
    else
    {
      switch ( a3 )
      {
        case 0x3B5uLL:
          v6 = CFSTR("【行业】");
          v7 = (unsigned __int8)_objc_msgSend(CFSTR("【行业】"), "isEqualToString:", obj);
          goto LABEL_34;
        case 0x81DuLL:
          v6 = CFSTR("【分析】");
          v7 = (unsigned __int8)_objc_msgSend(CFSTR("【分析】"), "isEqualToString:", obj);
          goto LABEL_34;
        case 0x81EuLL:
          goto LABEL_21;
      }
    }
LABEL_33:
    v7 = (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", obj);
    goto LABEL_34;
  }
  if ( (__int64)a3 > 14364 )
  {
    if ( (__int64)a3 > 32769 )
    {
      if ( a3 == 49155 )
      {
        v6 = CFSTR("【全球】");
        v7 = (unsigned __int8)_objc_msgSend(CFSTR("【全球】"), "isEqualToString:", obj);
        goto LABEL_34;
      }
      if ( a3 == 49154 )
      {
        v6 = CFSTR("【财经】");
        v7 = (unsigned __int8)_objc_msgSend(CFSTR("【财经】"), "isEqualToString:", obj);
        goto LABEL_34;
      }
      if ( a3 != 32770 )
        goto LABEL_33;
LABEL_21:
      v6 = CFSTR("【新闻】");
      v7 = (unsigned __int8)_objc_msgSend(CFSTR("【新闻】"), "isEqualToString:", obj);
      goto LABEL_34;
    }
    if ( a3 != 14365 )
    {
      if ( a3 != 14369 )
        goto LABEL_33;
      goto LABEL_21;
    }
LABEL_22:
    v6 = CFSTR("【要闻】");
    v7 = (unsigned __int8)_objc_msgSend(CFSTR("【要闻】"), "isEqualToString:", obj);
    goto LABEL_34;
  }
  switch ( a3 )
  {
    case 0x3803uLL:
      v14[0] = CFSTR("USHJ");
      v14[1] = CFSTR("USZJ");
      v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v14, 2LL);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v10 = (unsigned __int8)_objc_msgSend(v9, "containsObject:", v5);
      v11 = v10 == 0;
      v6 = CFSTR("【个基】");
      if ( v11 )
        v6 = CFSTR("【新闻】");
      goto LABEL_33;
    case 0x3804uLL:
    case 0x380DuLL:
      goto LABEL_21;
    case 0x3805uLL:
      v6 = CFSTR("【公告】");
      v7 = (unsigned __int8)_objc_msgSend(CFSTR("【公告】"), "isEqualToString:", obj);
      break;
    case 0x3806uLL:
    case 0x3807uLL:
      v6 = CFSTR("【研报】");
      v7 = (unsigned __int8)_objc_msgSend(CFSTR("【研报】"), "isEqualToString:", obj);
      break;
    case 0x3813uLL:
      goto LABEL_23;
    case 0x3814uLL:
      goto LABEL_22;
    default:
      goto LABEL_33;
  }
LABEL_34:
  if ( !v7 && _objc_msgSend(v6, "length") == (id)4 )
  {
    v12 = _objc_msgSend(v6, "substringWithRange:", 1LL, 2LL);
    v6 = (__CFString *)objc_retainAutoreleasedReturnValue(v12);
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (000000010056FF50) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager getZiXunURL:market:](ZiXunReqParamsManager *self, SEL a2, id a3, id a4)
{
  __CFString *v9; // r15
  NSString *v18; // rax
  NSString *v32; // rax
  NSString *v35; // rax

  v5 = objc_retain(a3);
  objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  v9 = &charsToLeaveEscaped;
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
  {
    v10 = _objc_msgSend(v8, "length");
    if ( v5 )
    {
      if ( v10 )
      {
        v11 = _objc_msgSend(v5, "ziXunType");
        v13 = -[ZiXunReqParamsManager getZiXunBaseURLFromZiXunTree:market:](
                self,
                "getZiXunBaseURLFromZiXunTree:market:",
                v11,
                v12);
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(v5, "newsRequestType");
        if ( v15 != (id)1 )
        {
          if ( v15 )
          {
            v9 = &charsToLeaveEscaped;
LABEL_16:
            goto LABEL_17;
          }
          v16 = _objc_msgSend(v5, "newsURL");
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = _objc_msgSend(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("%@#view_type=desktop_client&skin=black"),
                  v17);
          v19 = v17;
          v9 = objc_retainAutoreleasedReturnValue(v18);
LABEL_15:
          goto LABEL_16;
        }
        v40 = v14;
        v20 = _objc_msgSend(v5, "time");
        v21 = objc_retainAutoreleasedReturnValue(v20);
        v22 = +[HXTools getYYYYMMDDDate:](&OBJC_CLASS___HXTools, "getYYYYMMDDDate:", v21);
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v37 = v23;
        v24 = _objc_msgSend(v40, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%3"), v23);
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v26 = _objc_msgSend(v5, "newsItemId");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = _objc_msgSend(v25, "stringByReplacingOccurrencesOfString:withString:", CFSTR("{guid}"), v27);
        v38 = objc_retainAutoreleasedReturnValue(v28);
        if ( _objc_msgSend(v5, "ziXunType") == (id)14341 )
        {
          v30 = v38;
          if ( +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", v29) != (id)8 )
          {
            v34 = v38;
            v35 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@#fromapp=IHexin"), v38);
            v9 = objc_retainAutoreleasedReturnValue(v35);
            goto LABEL_14;
          }
          v31 = _objc_msgSend(v5, "stockCode");
          v39 = objc_retainAutoreleasedReturnValue(v31);
          v32 = _objc_msgSend(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("%@#code=%@&view_type=desktop_client"),
                  v38,
                  v39);
        }
        else
        {
          v33 = _objc_msgSend(v5, "stockCode");
          v39 = objc_retainAutoreleasedReturnValue(v33);
          v30 = v38;
          v32 = _objc_msgSend(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("%@#code=%@&view_type=desktop_client"),
                  v38,
                  v39);
        }
        v9 = objc_retainAutoreleasedReturnValue(v32);
        v34 = v39;
LABEL_14:
        v14 = v40;
        v19 = v37;
        goto LABEL_15;
      }
    }
  }
LABEL_17:
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100570260) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunReqParamsManager getNewsRequestTypeByZiXunType:stockCode:market:](
        ZiXunReqParamsManager *self,
        SEL a2,
        unsigned __int64 a3,
        id a4,
        id a5)
{
  SEL v10; // r12
  unsigned __int64 v11; // r15
  unsigned __int64 v32; // [rsp+48h] [rbp-E8h]
  SEL v33; // [rsp+50h] [rbp-E0h]
  SEL v34; // [rsp+58h] [rbp-D8h]
  id obj; // [rsp+70h] [rbp-C0h]

  v32 = a3;
  v36 = self;
  v6 = objc_retain(a4);
  v7 = objc_retain(a5);
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( !(unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v8) )
    goto LABEL_16;
  v9 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( !(unsigned __int8)_objc_msgSend(v6, v10, v9) )
    goto LABEL_16;
  v38 = v7;
  if ( !_objc_msgSend(v7, "length") )
  {
    v7 = v38;
LABEL_16:
    v11 = 1LL;
    goto LABEL_17;
  }
  v7 = v38;
  v11 = 1LL;
  if ( _objc_msgSend(v6, "length") )
  {
    v12 = _objc_msgSend(v36, "getZiXunRequestParamsWithMarket:stockCode:", v38, v6);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v28 = 0LL;
    v29 = 0LL;
    v30 = 0LL;
    v31 = 0LL;
    obj = objc_retain(v13);
    v14 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v28, v39, 16LL);
    if ( v14 )
    {
      v15 = v14;
      v35 = v6;
      v36 = *(id *)v29;
      v16 = "class";
      while ( 2 )
      {
        v34 = "thsNumberForKey:";
        v33 = "unsignedIntegerValue";
        for ( i = 0LL; i != v15; i = (char *)i + 1 )
        {
          if ( *(id *)v29 != v36 )
            objc_enumerationMutation(obj);
          v18 = *(void **)(*((_QWORD *)&v28 + 1) + 8LL * (_QWORD)i);
          v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v16);
          if ( (unsigned __int8)_objc_msgSend(v18, "isKindOfClass:", v19) )
          {
            v20 = _objc_msgSend(v18, v34, CFSTR("ZiXunType"));
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v22 = _objc_msgSend(v21, v33);
            if ( v22 == (id)v32 )
            {
              v25 = _objc_msgSend(v18, v34, CFSTR("RequestType"));
              v26 = objc_retainAutoreleasedReturnValue(v25);
              v11 = (unsigned __int64)_objc_msgSend(v26, v33);
              goto LABEL_19;
            }
          }
        }
        v15 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v28, v39, 16LL);
        if ( v15 )
          continue;
        break;
      }
      v11 = 1LL;
LABEL_19:
      v6 = v35;
    }
    else
    {
      v11 = 1LL;
    }
    v27 = obj;
    v7 = v38;
  }
LABEL_17:
  return v11;
}

//----- (00000001005705AD) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager getZiXunBaseURLFromZiXunTree:market:](
        ZiXunReqParamsManager *self,
        SEL a2,
        unsigned __int64 a3,
        id a4)
{
  __CFString *v8; // rbx
  bool v12; // zf

  objc_retain(a4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  v8 = &charsToLeaveEscaped;
  if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v5) && _objc_msgSend(v7, "length") )
  {
    v9 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
    v11 = objc_retainAutoreleasedReturnValue(v9);
    if ( a3 == 14342 )
    {
      v13 = &selRef_getGeGuYanBaoURL;
    }
    else if ( a3 == 14341 )
    {
      v12 = +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", v10) == (id)8;
      v13 = &selRef_getNormalGongGaoURL;
      if ( v12 )
        v13 = &selRef_getMeiGuGongGaoURL;
    }
    else
    {
      v13 = &selRef_getGeGuZiXunURL;
    }
    v14 = _objc_msgSend(v11, *v13);
    v8 = (__CFString *)objc_retainAutoreleasedReturnValue(v14);
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (00000001005706BE) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructDefaultZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSDictionary *v6; // r14
  NSArray *v8; // rax
  NSArray *v9; // rbx
  NSDictionary *v11; // [rsp+8h] [rbp-58h] BYREF
  _QWORD v12[2]; // [rsp+10h] [rbp-50h] BYREF
  _QWORD v13[2]; // [rsp+20h] [rbp-40h] BYREF

  v12[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL);
  v13[0] = objc_retainAutoreleasedReturnValue(v2);
  v12[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v13[1] = v4;
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v13, v12, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v11 = v6;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v11, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (00000001005707DB) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructNormalZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r14
  NSNumber *v4; // rax
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r15
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSDictionary *v12; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r14
  NSNumber *v15; // rax
  NSNumber *v16; // r13
  NSDictionary *v18; // rax
  NSDictionary *v19; // r15
  NSArray *v20; // rax
  NSArray *v21; // r14
  NSDictionary *v26; // [rsp+8h] [rbp-B8h]
  NSDictionary *v27; // [rsp+10h] [rbp-B0h]
  _QWORD v28[3]; // [rsp+18h] [rbp-A8h] BYREF
  _QWORD v29[4]; // [rsp+30h] [rbp-90h] BYREF
  _QWORD v30[4]; // [rsp+50h] [rbp-70h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v32[2]; // [rsp+80h] [rbp-40h] BYREF

  v31[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v3;
  v31[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v32[1] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v31, 2LL);
  v26 = objc_retainAutoreleasedReturnValue(v5);
  v30[0] = CFSTR("ZiXunType");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v30[2] = v8;
  v30[1] = CFSTR("RequestType");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v11 + 8) = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v11, v30, 2LL);
  v27 = objc_retainAutoreleasedReturnValue(v12);
  v29[0] = CFSTR("ZiXunType");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14342LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v29[2] = v14;
  v29[1] = CFSTR("RequestType");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v17 + 8) = v16;
  v18 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v17, v29, 2LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v28[0] = v26;
  v28[1] = v27;
  v28[2] = v19;
  v20 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v28, 3LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  v23(v27);
  v24(v26);
  return objc_autoreleaseReturnValue(v21);
}

//----- (0000000100570A7A) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructWaiHuiZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r14
  NSNumber *v4; // rax
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r15
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSDictionary *v12; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r14
  NSNumber *v15; // rax
  NSNumber *v16; // r13
  NSDictionary *v18; // rax
  NSDictionary *v19; // r15
  NSArray *v20; // rax
  NSArray *v21; // r14
  NSDictionary *v26; // [rsp+8h] [rbp-B8h]
  NSDictionary *v27; // [rsp+10h] [rbp-B0h]
  _QWORD v28[3]; // [rsp+18h] [rbp-A8h] BYREF
  _QWORD v29[4]; // [rsp+30h] [rbp-90h] BYREF
  _QWORD v30[4]; // [rsp+50h] [rbp-70h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v32[2]; // [rsp+80h] [rbp-40h] BYREF

  v31[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2078LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v3;
  v31[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v32[1] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v31, 2LL);
  v26 = objc_retainAutoreleasedReturnValue(v5);
  v30[0] = CFSTR("ZiXunType");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2077LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v30[2] = v8;
  v30[1] = CFSTR("RequestType");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v11 + 8) = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v11, v30, 2LL);
  v27 = objc_retainAutoreleasedReturnValue(v12);
  v29[0] = CFSTR("ZiXunType");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v29[2] = v14;
  v29[1] = CFSTR("RequestType");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v17 + 8) = v16;
  v18 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v17, v29, 2LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v28[0] = v26;
  v28[1] = v27;
  v28[2] = v19;
  v20 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v28, 3LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  v23(v27);
  v24(v26);
  return objc_autoreleaseReturnValue(v21);
}

//----- (0000000100570D10) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructHuShenJiJinZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r14
  NSNumber *v4; // rax
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r15
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSDictionary *v12; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r14
  NSNumber *v15; // rax
  NSNumber *v16; // r13
  NSDictionary *v18; // rax
  NSDictionary *v19; // r15
  NSArray *v20; // rax
  NSArray *v21; // r14
  NSDictionary *v26; // [rsp+8h] [rbp-B8h]
  NSDictionary *v27; // [rsp+10h] [rbp-B0h]
  _QWORD v28[3]; // [rsp+18h] [rbp-A8h] BYREF
  _QWORD v29[4]; // [rsp+30h] [rbp-90h] BYREF
  _QWORD v30[4]; // [rsp+50h] [rbp-70h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v32[2]; // [rsp+80h] [rbp-40h] BYREF

  v31[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v3;
  v31[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v32[1] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v31, 2LL);
  v26 = objc_retainAutoreleasedReturnValue(v5);
  v30[0] = CFSTR("ZiXunType");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 949LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v30[2] = v8;
  v30[1] = CFSTR("RequestType");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v11 + 8) = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v11, v30, 2LL);
  v27 = objc_retainAutoreleasedReturnValue(v12);
  v29[0] = CFSTR("ZiXunType");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v29[2] = v14;
  v29[1] = CFSTR("RequestType");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v17 + 8) = v16;
  v18 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v17, v29, 2LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v28[0] = v26;
  v28[1] = v27;
  v28[2] = v19;
  v20 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v28, 3LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  v23(v27);
  v24(v26);
  return objc_autoreleaseReturnValue(v21);
}

//----- (0000000100570FAF) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructHKIndexZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r13
  NSArray *v38; // rax
  NSArray *v44; // [rsp+18h] [rbp-108h]
  NSDictionary *v45; // [rsp+20h] [rbp-100h]
  _QWORD v46[5]; // [rsp+28h] [rbp-F8h] BYREF
  _QWORD v47[2]; // [rsp+50h] [rbp-D0h] BYREF
  _QWORD v48[2]; // [rsp+60h] [rbp-C0h] BYREF
  _QWORD v49[2]; // [rsp+70h] [rbp-B0h] BYREF
  _QWORD v50[2]; // [rsp+80h] [rbp-A0h] BYREF
  _QWORD v51[2]; // [rsp+90h] [rbp-90h] BYREF
  _QWORD v52[2]; // [rsp+A0h] [rbp-80h] BYREF
  _QWORD v53[2]; // [rsp+B0h] [rbp-70h] BYREF
  _QWORD v54[2]; // [rsp+C0h] [rbp-60h] BYREF
  _QWORD v55[2]; // [rsp+D0h] [rbp-50h] BYREF
  _QWORD v56[2]; // [rsp+E0h] [rbp-40h] BYREF

  v55[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7363LL);
  v56[0] = objc_retainAutoreleasedReturnValue(v2);
  v55[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v56[1] = v4;
  v5 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v56, v55, 2LL);
  v45 = objc_retainAutoreleasedReturnValue(v5);
  v53[0] = CFSTR("ZiXunType");
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 32770LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v54[0] = v8;
  v53[1] = CFSTR("RequestType");
  v10 = (void *)v9(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v54[1] = v11;
  v13 = (void *)v12(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v54, v53, 2LL);
  v41 = objc_retainAutoreleasedReturnValue(v13);
  v51[0] = CFSTR("ZiXunType");
  v15 = (void *)v14(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v52[0] = v16;
  v51[1] = CFSTR("RequestType");
  v18 = (void *)v17(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v52[1] = v19;
  v21 = (void *)v20(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v52, v51, 2LL);
  v23 = v22;
  v42 = objc_retainAutoreleasedReturnValue(v21);
  v49[0] = CFSTR("ZiXunType");
  v25 = (void *)v24(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL);
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v50[0] = v26;
  v49[1] = CFSTR("RequestType");
  v28 = (void *)v27(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v50[1] = objc_retainAutoreleasedReturnValue(v28);
  v29 = (void *)v23(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v50, v49, 2LL);
  v30 = v23;
  v43 = objc_retainAutoreleasedReturnValue(v29);
  v47[0] = CFSTR("ZiXunType");
  v32 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v23)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  6146LL);
  v48[0] = objc_retainAutoreleasedReturnValue(v32);
  v47[1] = CFSTR("RequestType");
  v33 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v23)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithUnsignedInteger:",
                  0LL);
  v34 = objc_retainAutoreleasedReturnValue(v33);
  v48[1] = v34;
  v35 = (void *)v30(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v48, v47, 2LL);
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v46[0] = v45;
  v46[1] = v41;
  v46[2] = v42;
  v46[3] = v43;
  v46[4] = v36;
  v38 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v46, 5LL);
  v44 = objc_retainAutoreleasedReturnValue(v38);
  return objc_autoreleaseReturnValue(v44);
}

//----- (00000001005713C4) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructUSIndexZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r13
  NSArray *v38; // rax
  NSArray *v44; // [rsp+18h] [rbp-108h]
  NSDictionary *v45; // [rsp+20h] [rbp-100h]
  _QWORD v46[5]; // [rsp+28h] [rbp-F8h] BYREF
  _QWORD v47[2]; // [rsp+50h] [rbp-D0h] BYREF
  _QWORD v48[2]; // [rsp+60h] [rbp-C0h] BYREF
  _QWORD v49[2]; // [rsp+70h] [rbp-B0h] BYREF
  _QWORD v50[2]; // [rsp+80h] [rbp-A0h] BYREF
  _QWORD v51[2]; // [rsp+90h] [rbp-90h] BYREF
  _QWORD v52[2]; // [rsp+A0h] [rbp-80h] BYREF
  _QWORD v53[2]; // [rsp+B0h] [rbp-70h] BYREF
  _QWORD v54[2]; // [rsp+C0h] [rbp-60h] BYREF
  _QWORD v55[2]; // [rsp+D0h] [rbp-50h] BYREF
  _QWORD v56[2]; // [rsp+E0h] [rbp-40h] BYREF

  v55[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14369LL);
  v56[0] = objc_retainAutoreleasedReturnValue(v2);
  v55[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v56[1] = v4;
  v5 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v56, v55, 2LL);
  v45 = objc_retainAutoreleasedReturnValue(v5);
  v53[0] = CFSTR("ZiXunType");
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7364LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v54[0] = v8;
  v53[1] = CFSTR("RequestType");
  v10 = (void *)v9(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v54[1] = v11;
  v13 = (void *)v12(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v54, v53, 2LL);
  v41 = objc_retainAutoreleasedReturnValue(v13);
  v51[0] = CFSTR("ZiXunType");
  v15 = (void *)v14(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v52[0] = v16;
  v51[1] = CFSTR("RequestType");
  v18 = (void *)v17(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v52[1] = v19;
  v21 = (void *)v20(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v52, v51, 2LL);
  v23 = v22;
  v42 = objc_retainAutoreleasedReturnValue(v21);
  v49[0] = CFSTR("ZiXunType");
  v25 = (void *)v24(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL);
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v50[0] = v26;
  v49[1] = CFSTR("RequestType");
  v28 = (void *)v27(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v50[1] = objc_retainAutoreleasedReturnValue(v28);
  v29 = (void *)v23(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v50, v49, 2LL);
  v30 = v23;
  v43 = objc_retainAutoreleasedReturnValue(v29);
  v47[0] = CFSTR("ZiXunType");
  v32 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v23)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  6146LL);
  v48[0] = objc_retainAutoreleasedReturnValue(v32);
  v47[1] = CFSTR("RequestType");
  v33 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v23)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithUnsignedInteger:",
                  0LL);
  v34 = objc_retainAutoreleasedReturnValue(v33);
  v48[1] = v34;
  v35 = (void *)v30(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v48, v47, 2LL);
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v46[0] = v45;
  v46[1] = v41;
  v46[2] = v42;
  v46[3] = v43;
  v46[4] = v36;
  v38 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v46, 5LL);
  v44 = objc_retainAutoreleasedReturnValue(v38);
  return objc_autoreleaseReturnValue(v44);
}

//----- (00000001005717DC) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructHuShenIndexZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v6; // rax
  NSNumber *v7; // r15
  _QWORD v26[2]; // [rsp+10h] [rbp-80h] BYREF
  _QWORD v27[2]; // [rsp+20h] [rbp-70h] BYREF
  _QWORD v28[4]; // [rsp+30h] [rbp-60h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-40h] BYREF

  v28[2] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14355LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v29[0] = v3;
  *(_QWORD *)(v4 + 8) = CFSTR("RequestType");
  v5 = v4;
  v6 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v29[1] = v7;
  v9 = (void *)v8(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, v5, 2LL);
  v11 = v10;
  v25 = objc_retainAutoreleasedReturnValue(v9);
  v27[0] = CFSTR("ZiXunType");
  v13 = (void *)v12(&OBJC_CLASS___NSNumber, "numberWithInt:", 14356LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v28[0] = v14;
  v27[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v28[1] = objc_retainAutoreleasedReturnValue(v16);
  v17 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v11)(
                  &OBJC_CLASS___NSDictionary,
                  "dictionaryWithObjects:forKeys:count:",
                  v28,
                  v27,
                  2LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v26[0] = v25;
  v26[1] = v18;
  v20 = (void *)v11(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v18);
  v23(v25);
  return objc_autoreleaseReturnValue(v21);
}

//----- (00000001005719C0) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructMeiGuZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v6; // rax
  NSNumber *v7; // r15
  _QWORD v26[2]; // [rsp+10h] [rbp-80h] BYREF
  _QWORD v27[2]; // [rsp+20h] [rbp-70h] BYREF
  _QWORD v28[4]; // [rsp+30h] [rbp-60h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-40h] BYREF

  v28[2] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v29[0] = v3;
  *(_QWORD *)(v4 + 8) = CFSTR("RequestType");
  v5 = v4;
  v6 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v29[1] = v7;
  v9 = (void *)v8(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, v5, 2LL);
  v11 = v10;
  v25 = objc_retainAutoreleasedReturnValue(v9);
  v27[0] = CFSTR("ZiXunType");
  v13 = (void *)v12(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v28[0] = v14;
  v27[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v28[1] = objc_retainAutoreleasedReturnValue(v16);
  v17 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v11)(
                  &OBJC_CLASS___NSDictionary,
                  "dictionaryWithObjects:forKeys:count:",
                  v28,
                  v27,
                  2LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v26[0] = v25;
  v26[1] = v18;
  v20 = (void *)v11(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v18);
  v23(v25);
  return objc_autoreleaseReturnValue(v21);
}

//----- (0000000100571BA4) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructGuZhiQiHuoZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSDictionary *v6; // r14
  NSArray *v8; // rax
  NSArray *v9; // rbx
  NSDictionary *v11; // [rsp+8h] [rbp-58h] BYREF
  _QWORD v12[2]; // [rsp+10h] [rbp-50h] BYREF
  _QWORD v13[2]; // [rsp+20h] [rbp-40h] BYREF

  v12[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14349LL);
  v13[0] = objc_retainAutoreleasedReturnValue(v2);
  v12[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v13[1] = v4;
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v13, v12, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v11 = v6;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v11, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100571CC4) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructGuoNeiQiHuoZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSDictionary *v6; // r14
  NSArray *v8; // rax
  NSArray *v9; // rbx
  NSDictionary *v11; // [rsp+8h] [rbp-58h] BYREF
  _QWORD v12[2]; // [rsp+10h] [rbp-50h] BYREF
  _QWORD v13[2]; // [rsp+20h] [rbp-40h] BYREF

  v12[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14340LL);
  v13[0] = objc_retainAutoreleasedReturnValue(v2);
  v12[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v13[1] = v4;
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v13, v12, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v11 = v6;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v11, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100571DE4) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructBlockZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v6; // rax
  NSNumber *v7; // r15
  _QWORD v26[2]; // [rsp+10h] [rbp-80h] BYREF
  _QWORD v27[2]; // [rsp+20h] [rbp-70h] BYREF
  _QWORD v28[4]; // [rsp+30h] [rbp-60h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-40h] BYREF

  v28[2] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v29[0] = v3;
  *(_QWORD *)(v4 + 8) = CFSTR("RequestType");
  v5 = v4;
  v6 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v29[1] = v7;
  v9 = (void *)v8(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, v5, 2LL);
  v11 = v10;
  v25 = objc_retainAutoreleasedReturnValue(v9);
  v27[0] = CFSTR("ZiXunType");
  v13 = (void *)v12(&OBJC_CLASS___NSNumber, "numberWithInt:", 14343LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v28[0] = v14;
  v27[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v28[1] = objc_retainAutoreleasedReturnValue(v16);
  v17 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v11)(
                  &OBJC_CLASS___NSDictionary,
                  "dictionaryWithObjects:forKeys:count:",
                  v28,
                  v27,
                  2LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v26[0] = v25;
  v26[1] = v18;
  v20 = (void *)v11(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v18);
  v23(v25);
  return objc_autoreleaseReturnValue(v21);
}

//----- (0000000100571FC8) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructGeGuXinWenZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rbx
  NSDictionary *v5; // rax
  NSDictionary *v6; // r14
  NSArray *v8; // rax
  NSArray *v9; // rbx
  NSDictionary *v11; // [rsp+8h] [rbp-58h] BYREF
  _QWORD v12[2]; // [rsp+10h] [rbp-50h] BYREF
  _QWORD v13[2]; // [rsp+20h] [rbp-40h] BYREF

  v12[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v13[0] = objc_retainAutoreleasedReturnValue(v2);
  v12[1] = CFSTR("RequestType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v13[1] = v4;
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v13, v12, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v11 = v6;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v11, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (00000001005720E8) ----------------------------------------------------
id __cdecl -[ZiXunReqParamsManager constructHuShenOptionZiXunRequestParam](ZiXunReqParamsManager *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r14
  NSNumber *v4; // rax
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r15
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSDictionary *v12; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r14
  NSNumber *v15; // rax
  NSNumber *v16; // r13
  NSDictionary *v18; // rax
  NSDictionary *v19; // r15
  NSArray *v20; // rax
  NSArray *v21; // r14
  NSDictionary *v26; // [rsp+8h] [rbp-B8h]
  NSDictionary *v27; // [rsp+10h] [rbp-B0h]
  _QWORD v28[3]; // [rsp+18h] [rbp-A8h] BYREF
  _QWORD v29[4]; // [rsp+30h] [rbp-90h] BYREF
  _QWORD v30[4]; // [rsp+50h] [rbp-70h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v32[2]; // [rsp+80h] [rbp-40h] BYREF

  v31[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v3;
  v31[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v32[1] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v31, 2LL);
  v26 = objc_retainAutoreleasedReturnValue(v5);
  v30[0] = CFSTR("ZiXunType");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 949LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v30[2] = v8;
  v30[1] = CFSTR("RequestType");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v11 + 8) = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v11, v30, 2LL);
  v27 = objc_retainAutoreleasedReturnValue(v12);
  v29[0] = CFSTR("ZiXunType");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v29[2] = v14;
  v29[1] = CFSTR("RequestType");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v17 + 8) = v16;
  v18 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v17, v29, 2LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v28[0] = v26;
  v28[1] = v27;
  v28[2] = v19;
  v20 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v28, 3LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  v23(v27);
  v24(v26);
  return objc_autoreleaseReturnValue(v21);
}

