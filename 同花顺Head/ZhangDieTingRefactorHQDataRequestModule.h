//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

#import "ReceiveDispatchData-Protocol.h"

@class NSMutableArray, NSString;

@interface ZhangDieTingRefactorHQDataRequestModule : NSObject <ReceiveDispatchData>
{
    BOOL _isRequestZhuangTaiData;
    CDUnknownBlockType _reqCallBack;
    long long _reqInstance;
    NSMutableArray *_reqInstanceArr;
    NSMutableArray *_compeletDetailDataArr;
}


@property(retain, nonatomic) NSMutableArray *compeletDetailDataArr; // @synthesize compeletDetailDataArr=_compeletDetailDataArr;
@property(retain, nonatomic) NSMutableArray *reqInstanceArr; // @synthesize reqInstanceArr=_reqInstanceArr;
@property(nonatomic) BOOL isRequestZhuangTaiData; // @synthesize isRequestZhuangTaiData=_isRequestZhuangTaiData;
@property(nonatomic) long long reqInstance; // @synthesize reqInstance=_reqInstance;
@property(copy, nonatomic) CDUnknownBlockType reqCallBack; // @synthesize reqCallBack=_reqCallBack;
- (void)failToReceiveStuffData:(long long)arg1;
- (void)receiveStuffData:(id)arg1;
- (void)handlerWithReceiveData:(id)arg1;
- (id)formatStockCodeAndMarket:(id)arg1;
- (id)getSameMarketStockCodeDic:(id)arg1;
- (id)getCodeListStrAndMarketListStr:(id)arg1;
- (void)requestForZhuangTai:(id)arg1 reqCallBack:(CDUnknownBlockType)arg2;
- (void)requestForLeiXing:(id)arg1 reqCallBack:(CDUnknownBlockType)arg2;
- (void)requestForCodeListSort:(id)arg1 reqCallBack:(CDUnknownBlockType)arg2;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

