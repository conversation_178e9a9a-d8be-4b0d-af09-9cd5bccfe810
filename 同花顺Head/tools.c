char __cdecl +[tools isZero:](id a1, SEL a2, double a3)
{
  return a3 >= -0.00001 && a3 <= 0.00001;
}

//----- (0000000100C4D9AE) ----------------------------------------------------
char __cdecl +[tools Nsstring:isEqual2CString:](id a1, SEL a2, id a3, char *a4)
{

  v5 = objc_retain(a3);
  v6 = _objc_msgSend(a1, "CString2NSstring:", a4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = v7;
  if ( !v7 || (v9 = 1, !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", v5)) )
    v9 = 0;
  v10(v5);
  return v9;
}

//----- (0000000100C4DA4D) ----------------------------------------------------
char __cdecl +[tools isValidMeiguCodeLen:](id a1, SEL a2, id a3)
{
  bool v5; // bl

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL"))
    && (unsigned __int64)_objc_msgSend(v4, "length") < 6;
  return v5;
}

//----- (0000000100C4DAF6) ----------------------------------------------------
char __cdecl +[tools isValidCodeLen:](id a1, SEL a2, id a3)
{
  bool v5; // bl

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL"))
    && _objc_msgSend(v4, "length") == (id)6;
  return v5;
}

//----- (0000000100C4DB9F) ----------------------------------------------------
char __cdecl +[tools isPureFloat:](id a1, SEL a2, id a3)
{
  bool v5; // bl
  NSScanner *v7; // rax
  NSScanner *v8; // r14

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___NSScanner, "scannerWithString:", v4);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( (unsigned __int8)_objc_msgSend(v8, "scanDouble:", v9) )
      v5 = (unsigned __int8)_objc_msgSend(v8, "isAtEnd") != 0;
    else
      v5 = 0;
  }
  else
  {
    v5 = 0;
  }
  return v5;
}

//----- (0000000100C4DCA6) ----------------------------------------------------
char __cdecl +[tools isPureNumber:](id a1, SEL a2, id a3)
{
  bool v5; // bl
  NSScanner *v7; // rax
  NSScanner *v8; // r14

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___NSScanner, "scannerWithString:", v4);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( (unsigned __int8)_objc_msgSend(v8, "scanInteger:", v9) )
      v5 = (unsigned __int8)_objc_msgSend(v8, "isAtEnd") != 0;
    else
      v5 = 0;
  }
  else
  {
    v5 = 0;
  }
  return v5;
}

//----- (0000000100C4DDAD) ----------------------------------------------------
char __cdecl +[tools isDebt:codeType:](id a1, SEL a2, id a3, signed __int64 a4)
{
  id obj; // [rsp+88h] [rbp-138h]

  v4 = a4;
  v5 = objc_retain(a3);
  v6 = v5;
  if ( !v5
    || !_objc_msgSend(v5, "length")
    || (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL"))
    || (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NUL")) )
  {
    goto LABEL_5;
  }
  if ( (v4 & 0x10) != 0 )
  {
    v19 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = _objc_msgSend(v20, "GetConfigValue:Name:DefaultString:", "system", "WT_DebtSH_Mask", CFSTR("0*;12*;"));
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v39 = v22;
    v23 = _objc_msgSend(v22, "componentsSeparatedByString:", CFSTR("*;"));
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v31 = 0LL;
    v32 = 0LL;
    v33 = 0LL;
    v34 = 0LL;
    obj = objc_retain(v24);
    v26 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v42, 16LL);
    if ( v26 )
    {
      v27 = *(_QWORD *)v32;
LABEL_24:
      v28 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v32 != v27 )
          objc_enumerationMutation(obj);
        if ( !_objc_msgSend(v25, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v31 + 1) + 8 * v28)) )
          goto LABEL_31;
        if ( v26 == (id)++v28 )
        {
          v26 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v42, 16LL);
          if ( v26 )
            goto LABEL_24;
          goto LABEL_30;
        }
      }
    }
    goto LABEL_30;
  }
  if ( (v4 & 0x20) != 0 )
  {
    v9 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "GetConfigValue:Name:DefaultString:", "system", "WT_DebtSH_Mask", CFSTR("10*;"));
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v39 = v12;
    v13 = _objc_msgSend(v12, "componentsSeparatedByString:", CFSTR("*;"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v35 = 0LL;
    v36 = 0LL;
    v37 = 0LL;
    v38 = 0LL;
    obj = objc_retain(v14);
    v16 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v41, 16LL);
    if ( v16 )
    {
      v17 = *(_QWORD *)v36;
LABEL_13:
      v18 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v36 != v17 )
          objc_enumerationMutation(obj);
        if ( !_objc_msgSend(v15, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v35 + 1) + 8 * v18)) )
          break;
        if ( v16 == (id)++v18 )
        {
          v16 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v41, 16LL);
          if ( v16 )
            goto LABEL_13;
          goto LABEL_30;
        }
      }
LABEL_31:
      v30 = obj;
      v7 = 1;
      goto LABEL_6;
    }
LABEL_30:
    v29 = obj;
  }
LABEL_5:
  v7 = 0;
LABEL_6:
  return v7;
}

//----- (0000000100C4E20C) ----------------------------------------------------
char __cdecl +[tools isHuiGou:codeType:](id a1, SEL a2, id a3, signed __int64 a4)
{
  id obj; // [rsp+88h] [rbp-138h]

  v4 = a4;
  v5 = objc_retain(a3);
  v6 = v5;
  if ( !v5
    || !_objc_msgSend(v5, "length")
    || (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL"))
    || (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NUL")) )
  {
    goto LABEL_5;
  }
  if ( (v4 & 0x10) != 0 )
  {
    v19 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = _objc_msgSend(v20, "GetConfigValue:Name:DefaultString:", "system", "WT_HGSH_Mask", CFSTR("202*;201*;"));
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v39 = v22;
    v23 = _objc_msgSend(v22, "componentsSeparatedByString:", CFSTR("*;"));
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v31 = 0LL;
    v32 = 0LL;
    v33 = 0LL;
    v34 = 0LL;
    obj = objc_retain(v24);
    v26 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v42, 16LL);
    if ( v26 )
    {
      v27 = *(_QWORD *)v32;
LABEL_24:
      v28 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v32 != v27 )
          objc_enumerationMutation(obj);
        if ( !_objc_msgSend(v25, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v31 + 1) + 8 * v28)) )
          goto LABEL_31;
        if ( v26 == (id)++v28 )
        {
          v26 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v42, 16LL);
          if ( v26 )
            goto LABEL_24;
          goto LABEL_30;
        }
      }
    }
    goto LABEL_30;
  }
  if ( (v4 & 0x20) != 0 )
  {
    v9 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "GetConfigValue:Name:DefaultString:", "system", "WT_HGSZ_Mask", CFSTR("13*;"));
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v39 = v12;
    v13 = _objc_msgSend(v12, "componentsSeparatedByString:", CFSTR("*;"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v35 = 0LL;
    v36 = 0LL;
    v37 = 0LL;
    v38 = 0LL;
    obj = objc_retain(v14);
    v16 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v41, 16LL);
    if ( v16 )
    {
      v17 = *(_QWORD *)v36;
LABEL_13:
      v18 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v36 != v17 )
          objc_enumerationMutation(obj);
        if ( !_objc_msgSend(v15, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v35 + 1) + 8 * v18)) )
          break;
        if ( v16 == (id)++v18 )
        {
          v16 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v41, 16LL);
          if ( v16 )
            goto LABEL_13;
          goto LABEL_30;
        }
      }
LABEL_31:
      v30 = obj;
      v7 = 1;
      goto LABEL_6;
    }
LABEL_30:
    v29 = obj;
  }
LABEL_5:
  v7 = 0;
LABEL_6:
  return v7;
}

//----- (0000000100C4E66B) ----------------------------------------------------
char __cdecl +[tools isShowZhang:codeType:](id a1, SEL a2, id a3, signed __int64 a4)
{
  SEL v12; // r12
  SEL v15; // r12
  id obj; // [rsp+48h] [rbp-B8h]

  v5 = a4;
  v6 = objc_retain(a3);
  v7 = v6;
  if ( !v6
    || !_objc_msgSend(v6, "length")
    || (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL"))
    || (unsigned __int8)_objc_msgSend(v7, v8, CFSTR("NUL")) )
  {
    v9 = 0;
    goto LABEL_6;
  }
  if ( (v5 & 0x10) != 0 )
  {
    v11 = _objc_msgSend(v7, "substringToIndex:", 1LL);
    v4 = objc_retainAutoreleasedReturnValue(v11);
    if ( (unsigned __int8)_objc_msgSend(v4, v12, CFSTR("0")) )
    {
      v9 = 1;
      goto LABEL_6;
    }
    if ( (v5 & 0x20) != 0 )
    {
LABEL_14:
      v13 = _objc_msgSend(v7, "substringToIndex:", 2LL);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      _objc_msgSend(v14, v15, CFSTR("10"));
      if ( (v5 & 0x10) != 0 )
      v9 = 1;
      if ( v16 )
        goto LABEL_6;
      goto LABEL_17;
    }
  }
  else if ( (v5 & 0x20) != 0 )
  {
    goto LABEL_14;
  }
LABEL_17:
  v17 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(
          v18,
          "GetConfigValue:Name:DefaultString:",
          "system",
          "WT_ZhangMask",
          CFSTR("11*;12*;13*;10*;181*;201*202*;"));
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v33 = v20;
  v21 = _objc_msgSend(v20, "componentsSeparatedByString:", CFSTR("*;"));
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v29 = 0LL;
  v30 = 0LL;
  v31 = 0LL;
  v32 = 0LL;
  obj = objc_retain(v22);
  v24 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v23, v35, 16LL);
  if ( v24 )
  {
    v25 = *(_QWORD *)v30;
    while ( 2 )
    {
      v26 = "rangeOfString:";
      if ( !v24 )
        v24 = 1LL;
      for ( i = 0LL; i != v24; ++i )
      {
        if ( *(_QWORD *)v30 != v25 )
          objc_enumerationMutation(obj);
        if ( !_objc_msgSend(v7, v26, *(_QWORD *)(*((_QWORD *)&v29 + 1) + 8 * i)) )
        {
          v9 = 1;
          goto LABEL_29;
        }
      }
      v24 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v35, 16LL);
      if ( v24 )
        continue;
      break;
    }
  }
  v9 = 0;
LABEL_29:
LABEL_6:
  return v9;
}

//----- (0000000100C4E9F9) ----------------------------------------------------
char __cdecl +[tools isHTTPURLWithStr:](id a1, SEL a2, id a3)
{
  bool v5; // bl

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v5 = 1;
    if ( _objc_msgSend(v4, "rangeOfString:", CFSTR("http://")) == (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v7 = _objc_msgSend(v4, "rangeOfString:", CFSTR("https://"));
      v5 = v7 != v8;
    }
  }
  else
  {
    v5 = 0;
  }
  return v5;
}

//----- (0000000100C4EAD4) ----------------------------------------------------
int __cdecl +[tools _mwstrcmp_mask:code:token:](id a1, SEL a2, const char *a3, const char *a4, char a5)
{
  int v6; // r14d
  size_t v9; // rax
  size_t v10; // r15
  int v15; // eax
  int v16; // ebx
  char __sep[42]; // [rsp+0h] [rbp-2Ah] BYREF

  *(_WORD *)__sep = HIWORD(v5);
  v6 = 0;
  if ( a3 )
  {
    if ( a4 )
    {
      v9 = strlen(a3);
      if ( v9 )
      {
        v10 = v9;
        v11 = (void *)operator new[](v9 + 1);
        memcpy(v11, a3, v10);
        __sep[0] = v12;
        __sep[1] = 0;
        v13 = strtok((char *)v11, __sep);
        v6 = 0;
        if ( v13 )
        {
          while ( 1 )
          {
            v15 = sub_100C4EB87(v13, (char *)a4);
            if ( !v15 )
              break;
            v16 = v15;
            v13 = strtok(0LL, __sep);
            if ( !v13 )
            {
              v6 = v16;
              break;
            }
          }
        }
        operator delete[](v14);
      }
    }
  }
  return v6;
}

//----- (0000000100C4EB87) ----------------------------------------------------
__int64 __fastcall sub_100C4EB87(char *__s, char *a2)
{
  size_t v4; // rbx
  size_t v5; // rax
  size_t v6; // r12
  size_t v7; // r15
  int v8; // edi
  int v9; // ebx
  int v11; // ebx
  int v12; // ebx
  bool v15; // dl
  bool v16; // cl
  size_t v17; // rsi
  size_t v18; // [rsp+0h] [rbp-40h]
  size_t v19; // [rsp+8h] [rbp-38h]
  size_t v20; // [rsp+10h] [rbp-30h]

  if ( __s == a2 )
    return 0LL;
  result = 0xFFFFFFFFLL;
  if ( __s || !a2 )
  {
    if ( !__s || (result = 1LL, a2) )
    {
      v4 = strlen(__s);
      v5 = strlen(a2);
      v6 = 0LL;
      if ( !v4 )
      {
        v7 = 0LL;
        goto LABEL_35;
      }
      v7 = 0LL;
      if ( !v5 )
        goto LABEL_35;
      v18 = v4 - 1;
      v7 = 0LL;
      v6 = 0LL;
      v19 = v5;
      v20 = v4;
      while ( 1 )
      {
        v8 = __s[v6];
        if ( v8 == 42 )
        {
          if ( !__s[v6 + 1] )
            goto LABEL_33;
          ++v6;
          if ( v7 < v5 )
          {
            while ( 1 )
            {
              v9 = __toupper(a2[v7]);
              if ( v9 == __toupper(__s[v10]) )
                break;
              if ( v19 == ++v7 )
              {
                v7 = v19;
                break;
              }
            }
LABEL_22:
            v4 = v20;
          }
        }
        else if ( a2[v7] == 42 )
        {
          if ( !a2[v7 + 1] )
          {
LABEL_33:
            v15 = 1;
            v16 = 0;
            return !v15 && !v16;
          }
          ++v7;
          if ( v6 < v4 )
          {
            while ( 1 )
            {
              v11 = __toupper(v8);
              if ( v11 == __toupper(a2[v7]) )
                goto LABEL_22;
              if ( v18 == v6 )
              {
                v4 = v20;
                v6 = v20;
                break;
              }
              v8 = __s[v6 + 1];
            }
          }
        }
        else
        {
          v12 = __toupper(v8);
          if ( v12 == __toupper(a2[v7]) )
            goto LABEL_22;
          v13 = __s[v6];
          v4 = v20;
          if ( v13 != 63 )
          {
            v14 = a2[v7];
            if ( v14 != 63 )
              return 2 * (unsigned int)(v13 > v14) - 1;
          }
        }
        ++v6;
        ++v7;
        if ( v6 >= v4 )
        {
          v5 = v19;
LABEL_35:
          v16 = v7 < v5;
          v15 = v6 >= v4;
          if ( v6 < v4 )
            return !v15 && !v16;
          v17 = v5;
          result = 0xFFFFFFFFLL;
          if ( v7 >= v17 )
            return !v15 && !v16;
          return result;
        }
        v5 = v19;
        if ( v7 >= v19 )
          goto LABEL_35;
      }
    }
  }
  return result;
}

//----- (0000000100C4ED48) ----------------------------------------------------
char __cdecl +[tools getCodeTypeByScmc:](id a1, SEL a2, id a3)
{
  SEL v7; // r12
  SEL v11; // r12
  SEL v15; // r12
  SEL v19; // r12
  SEL v23; // r12

  v3 = objc_retain(a3);
  v4 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_SH", CFSTR("上|沪"));
  v39 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___configmanager, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_SH", CFSTR("深"));
  v40 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(&OBJC_CLASS___configmanager, v11);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_SH", CFSTR("B|Ｂ"));
  v41 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(&OBJC_CLASS___configmanager, v15);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_SH", CFSTR("特|三板"));
  v42 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(&OBJC_CLASS___configmanager, v19);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v21, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_SH", CFSTR("期权"));
  v43 = objc_retainAutoreleasedReturnValue(v22);
  v24 = _objc_msgSend(&OBJC_CLASS___configmanager, v23);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "GetConfigValue:Name:DefaultString:", "system", "WT_MASK_HK", CFSTR("港"));
  objc_retainAutoreleasedReturnValue(v26);
  v28 = (unsigned __int8)+[tools String:containKey:withSep:](
                           &OBJC_CLASS___tools,
                           "String:containKey:withSep:",
                           v3,
                           v27,
                           CFSTR("|"));
  v29 = 8 * (v28 == 0) + 8;
  if ( !(unsigned __int8)+[tools String:containKey:withSep:](
                           &OBJC_CLASS___tools,
                           "String:containKey:withSep:",
                           v3,
                           v39,
                           CFSTR("|")) )
    v29 = 0;
  v30 = (unsigned __int8)+[tools String:containKey:withSep:](
                           &OBJC_CLASS___tools,
                           "String:containKey:withSep:",
                           v3,
                           v40,
                           CFSTR("|"));
  v31 = 9;
  if ( !v28 )
    v31 = v29 + 32;
  v32 = v29;
  if ( v30 )
    v32 = v31;
  v33 = (unsigned __int8)+[tools String:containKey:withSep:](
                           &OBJC_CLASS___tools,
                           "String:containKey:withSep:",
                           v3,
                           v41,
                           CFSTR("|"));
  v34 = (unsigned __int8)+[tools String:containKey:withSep:](
                           &OBJC_CLASS___tools,
                           "String:containKey:withSep:",
                           v3,
                           v42,
                           CFSTR("|"));
  v35 = v32 | 2;
  if ( !v33 )
    v35 = v32;
  v36 = 35;
  if ( !v34 )
    v36 = v35;
  if ( (unsigned __int8)+[tools String:containKey:withSep:](
                          &OBJC_CLASS___tools,
                          "String:containKey:withSep:",
                          v3,
                          v43,
                          CFSTR("|")) )
  {
    if ( (v36 & 0x30) == 16 )
    {
      v36 = -24;
    }
    else if ( (v36 & 0x30) == 32 )
    {
      v36 = 56;
    }
  }
  return v36;
}

//----- (0000000100C4F15D) ----------------------------------------------------
double __cdecl +[tools countMeiguCanBuy:](id a1, SEL a2, id a3)
{
  long double v11; // [rsp+0h] [rbp-40h]

  v3 = objc_retain(a3);
  v4 = v3;
  v12 = 0.0;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "count") )
    {
      v5 = _objc_msgSend(v4, "objectForKey:", CFSTR("price"));
      v6 = objc_retainAutoreleasedReturnValue(v5);
      _objc_msgSend(v6, "doubleValue");
      *((_QWORD *)&v11 + 1) = 0LL;
      v8 = _objc_msgSend(v7, "objectForKey:", CFSTR("gml"));
      v9 = objc_retainAutoreleasedReturnValue(v8);
      _objc_msgSend(v9, "doubleValue");
      if ( !(unsigned __int8)+[tools isZero:](&OBJC_CLASS___tools, "isZero:", 0.0) )
      {
        floor(v11);
        v12 = 0.0 / 0.0;
      }
    }
  }
  return v12;
}

//----- (0000000100C4F295) ----------------------------------------------------
double __cdecl +[tools countCanBuy:](id a1, SEL a2, id a3)
{
  SEL v7; // r12
  SEL v10; // r12
  SEL v12; // r12
  int v19; // r14d
  SEL v23; // r12
  SEL v25; // r12
  SEL v32; // r12
  signed __int8 v36; // al
  unsigned int v37; // r12d
  unsigned int v38; // r14d
  unsigned int v41; // r12d
  long double v43; // [rsp+0h] [rbp-A0h]
  long double v44; // [rsp+0h] [rbp-A0h]

  v3 = objc_retain(a3);
  v4 = v3;
  v54 = 0.0;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v4, "objectForKey:", CFSTR("kyje"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "doubleValue");
    v8 = _objc_msgSend(v4, v7, CFSTR("stockcode"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = _objc_msgSend(v4, v10, CFSTR("st_type"));
    v52 = objc_retainAutoreleasedReturnValue(v11);
    v13 = _objc_msgSend(v4, v12, CFSTR("s_typename"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v45 = v14;
    if ( v14 && (_objc_msgSend(v14, "rangeOfString:", CFSTR("回购")), v16) )
    {
      v55 = v9;
      v17 = _objc_msgSend(v4, v15, CFSTR("st_xsdw"));
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = (unsigned int)_objc_msgSend(v18, "integerValue");
      floor(v43);
      v54 = 0.0 / ((double)v19 * 100.0);
      v9 = v55;
    }
    else
    {
      v20 = _objc_msgSend(v4, v15, CFSTR("price"));
      v50 = objc_retainAutoreleasedReturnValue(v20);
      _objc_msgSend(v50, "doubleValue");
      v53 = 0.0;
      v54 = 0.0;
      if ( v50
        && _objc_msgSend(v50, "length")
        && !(unsigned __int8)+[tools isZero:](&OBJC_CLASS___tools, "isZero:", 0.0) )
      {
        v56 = v9;
        v22 = _objc_msgSend(v4, v21, CFSTR("st_mfare"));
        v47 = objc_retainAutoreleasedReturnValue(v22);
        _objc_msgSend(v47, "doubleValue");
        v24 = _objc_msgSend(v4, v23, CFSTR("st_sfare"));
        v48 = objc_retainAutoreleasedReturnValue(v24);
        _objc_msgSend(v48, "doubleValue");
        *((_QWORD *)&v43 + 1) = 0LL;
        v26 = _objc_msgSend(v4, v25, CFSTR("st_afare"));
        v49 = objc_retainAutoreleasedReturnValue(v26);
        _objc_msgSend(v49, "doubleValue");
        v27 = _objc_msgSend(a1, "getStringHex:", v52);
        if ( (unsigned __int8)_objc_msgSend(a1, "isShowZhang:codeType:", v9, v27) )
        {
          v29 = _objc_msgSend(v4, v28, CFSTR("gzlx"));
          v30 = objc_retainAutoreleasedReturnValue(v29);
          _objc_msgSend(v30, "doubleValue");
          v53 = 0.0 + 0.0;
          v9 = v56;
        }
        v31 = 0.0 - 0.0;
        v54 = 0.0;
        if ( 0.0 - 0.0 >= 0.0 )
        {
          floor(v43);
          v33 = _objc_msgSend(v4, v32, CFSTR("st_mrdw"));
          v34 = objc_retainAutoreleasedReturnValue(v33);
          v46 = _objc_msgSend(v34, "integerValue");
          v35 = _objc_msgSend(a1, "getStringHex:", v52);
          LOBYTE(v34) = (unsigned __int8)_objc_msgSend(a1, "isShowZhang:codeType:", v56, v35);
          v36 = (unsigned __int8)_objc_msgSend(a1, "isKeChuangBanCode:", v56);
          v37 = (char)v34;
          v38 = v36;
          _objc_msgSend(a1, "fixAmount:isShowZhang:isKCBType:Mrdw:", v37, (unsigned int)v36, v46, v31 / v53);
          v39 = v31 / v53;
          v51 = (*((double *)&v44 + 1) + 1.0) * v53 + 0.0;
          while ( 1 )
          {
            v54 = v39;
            _objc_msgSend(a1, "countMoney:Amount:BMairu:", v4, 1LL, v39);
            if ( v39 - 0.0 <= 0.0 )
              break;
            v40 = v39 * (double)(int)v46 - fmax(1.0, (v39 - 0.0 - 0.0) / v51);
            floor(v44);
            _objc_msgSend(a1, "fixAmount:isShowZhang:isKCBType:Mrdw:", v41, v38, v46, v40);
            v39 = v40;
          }
          v9 = v56;
        }
      }
    }
  }
  return v54;
}

//----- (0000000100C4F864) ----------------------------------------------------
double __cdecl +[tools countMoney:Amount:BMairu:](id a1, SEL a2, id a3, double a4, char a5)
{
  int v20; // r14d
  __m128d v23; // xmm6
  __m128d v24; // xmm7
  unsigned __int64 v26; // xmm0_8
  NSString *v32; // rax
  NSString *v33; // rbx

  v5 = objc_retain(a3);
  v6 = v5;
  v44 = 0.0;
  if ( v5 && _objc_msgSend(v5, "count") )
  {
    v7 = _objc_msgSend(v6, "objectForKey:", CFSTR("s_typename"));
    v37 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v37, "rangeOfString:", CFSTR("回购"));
    v35 = v8;
    v9 = _objc_msgSend(v6, "objectForKey:", CFSTR("price"));
    v38 = objc_retainAutoreleasedReturnValue(v9);
    _objc_msgSend(v38, "doubleValue");
    v45 = 0.0;
    v44 = 0.0;
    if ( !(unsigned __int8)_objc_msgSend(a1, "isZero:", 0.0) )
    {
      v10 = _objc_msgSend(v6, "objectForKey:", CFSTR("stockcode"));
      v39 = objc_retainAutoreleasedReturnValue(v10);
      v11 = _objc_msgSend(v6, "objectForKey:", CFSTR("st_type"));
      v40 = objc_retainAutoreleasedReturnValue(v11);
      v12 = _objc_msgSend(a1, "getStringHex:", v40);
      if ( (unsigned __int8)_objc_msgSend(a1, "isShowZhang:codeType:", v39, v12) )
      {
        v13 = _objc_msgSend(v6, "objectForKey:", CFSTR("gzlx"));
        v14 = objc_retainAutoreleasedReturnValue(v13);
        _objc_msgSend(v14, "doubleValue");
        v45 = 0.0 + 0.0;
      }
      v15 = _objc_msgSend(v6, "objectForKey:", CFSTR("st_mfare"));
      v42 = objc_retainAutoreleasedReturnValue(v15);
      _objc_msgSend(v42, "doubleValue");
      v16 = _objc_msgSend(v6, "objectForKey:", CFSTR("st_sfare"));
      v41 = objc_retainAutoreleasedReturnValue(v16);
      _objc_msgSend(v41, "doubleValue");
      v17 = _objc_msgSend(v6, "objectForKey:", CFSTR("st_afare"));
      v43 = objc_retainAutoreleasedReturnValue(v17);
      _objc_msgSend(v43, "doubleValue");
      v18 = _objc_msgSend(v6, "objectForKey:", CFSTR("st_mrdw"));
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v20 = (unsigned int)_objc_msgSend(v19, "integerValue");
      if ( v35 )
      {
        v44 = a4 * 100.0 * 0.0;
      }
      else
      {
        v22 = v45 * a4 * (double)v20;
        v23.f64[0] = 0.0 / 10.0 * v22;
        v24.f64[0] = 0.0 * ((double)v20 * a4);
        v25 = _mm_cmplt_sd((__m128d)0LL, v23).f64[0];
        v26 = COERCE_UNSIGNED_INT64(
                fmax(
                  COERCE_DOUBLE(COERCE_UNSIGNED_INT64(0.0 + -1.0) & *(_OWORD *)&_mm_cmplt_sd((__m128d)0LL, v24) & *(_OWORD *)&_mm_cmplt_sd((__m128d)0LL, (__m128d)0LL)),
                  v23.f64[0])) & *(_QWORD *)&v25;
        v27 = ~*(_QWORD *)&v25 & *(_QWORD *)&v23.f64[0];
        if ( v24.f64[0] > 0.0 )
          v28 = fmax(1.0, v24.f64[0]);
        else
          v28 = 0.0 * ((double)v20 * a4);
        *(_QWORD *)&v29 = v27 | v26;
        if ( v21 )
          v30 = v22 + v29 + v28;
        else
          v30 = v22 - v29 - v28;
        v31 = v30 + 0.000001;
        if ( !v21 )
          v31 = v31 + -0.0005;
        v32 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.3f"), v31);
        v33 = objc_retainAutoreleasedReturnValue(v32);
        _objc_msgSend(v33, "doubleValue");
        v44 = v31;
      }
    }
  }
  return v44;
}

//----- (0000000100C4FD2C) ----------------------------------------------------
id __cdecl +[tools getShowValueWithCount:Hand:](id a1, SEL a2, id a3, id a4)
{
  __CFString *v7; // r15
  SEL v10; // r12
  NSString *v13; // rax

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = CFSTR("0");
  if ( v5
    && _objc_msgSend(v5, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, v8, CFSTR("NUL")) )
  {
    v9 = (__int64)_objc_msgSend(v5, "integerValue");
    v11 = (__int64)_objc_msgSend(v6, v10);
    v12 = 1LL;
    if ( v11 > 1 )
      v12 = v11;
    v13 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("%ld"),
            v9 / v12 + ((v11 > 1) & (unsigned __int8)(v9 % v12 >= (__int64)((unsigned __int64)v12 >> 1))));
    v7 = objc_retainAutoreleasedReturnValue(v13);
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C4FE74) ----------------------------------------------------
id __cdecl +[tools CString2NSstring:](id a1, SEL a2, const char *a3)
{
  __CFString *v3; // r13
  _BYTE *v12; // rax

  v3 = &charsToLeaveEscaped;
  if ( a3 && *a3 )
  {
    v5 = +[tools _CString2NSstring:](&OBJC_CLASS___tools, "_CString2NSstring:", a3);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = v6;
    if ( *a3 && !_objc_msgSend(v6, "length") )
    {
      v9 = +[tools _fixCString:](&OBJC_CLASS___tools, "_fixCString:", a3);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = objc_retainAutorelease(v10);
      v12 = _objc_msgSend(v11, "bytes");
      v3 = &charsToLeaveEscaped;
      if ( v12 && *v12 )
      {
        v13 = +[tools _CString2NSstring:](&OBJC_CLASS___tools, "_CString2NSstring:", v12);
        v3 = (__CFString *)objc_retainAutoreleasedReturnValue(v13);
      }
    }
    else
    {
      v3 = objc_retain(v7);
    }
  }
  return objc_autoreleaseReturnValue(v3);
}

//----- (0000000100C4FFA3) ----------------------------------------------------
id __cdecl +[tools _CString2NSstring:](id a1, SEL a2, const char *a3)
{
  unsigned __int64 v4; // rax
  NSString *v5; // rax
  __CFString *v6; // rbx
  NSString *v7; // rax
  __CFString *v8; // rdi
  __CFString *v9; // r14

  v4 = CFStringConvertEncodingToNSStringEncoding(0x632u);
  v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithCString:encoding:", a3, v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( !v6 )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%s"), a3);
    v6 = objc_retainAutoreleasedReturnValue(v7);
  }
  v8 = &charsToLeaveEscaped;
  if ( v6 )
    v8 = v6;
  v9 = objc_retain(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100C50038) ----------------------------------------------------
id __cdecl +[tools _fixCString:](id a1, SEL a2, const char *a3)
{
  _BYTE *v8; // rax
  unsigned __int64 v9; // r12
  unsigned __int64 v10; // rdx
  unsigned __int64 v13; // rdx
  unsigned __int64 v14; // rsi
  NSData *v17; // rax
  NSData *v18; // r14

  if ( !a3 )
    return objc_autoreleaseReturnValue(0LL);
  if ( *a3 )
  {
    strlen(a3);
    v4 = objc_alloc(&OBJC_CLASS___NSMutableData);
    v6 = _objc_msgSend(v4, "initWithCapacity:", v5 + 1);
    v7 = objc_retainAutorelease(v6);
    v8 = _objc_msgSend(v7, "mutableBytes");
    if ( v9 )
    {
      v10 = 0LL;
      v11 = 0LL;
      while ( 1 )
      {
        v12 = a3[v10];
        if ( v12 >= 0 )
          break;
        v14 = v10 + 1;
        if ( v12 != -1 )
        {
          if ( v14 < v9 )
          {
            v15 = a3[v14];
            if ( (unsigned __int8)(v15 - 64) <= 0xBEu )
            {
              v8[v11] = v12;
              v8[v11 + 1] = v15;
              v11 += 2LL;
            }
          }
          v13 = v10 + 2;
          goto LABEL_12;
        }
LABEL_13:
        v10 = v14;
        if ( v14 >= v9 )
          goto LABEL_18;
      }
      v8[v11++] = v12;
      v13 = v10 + 1;
LABEL_12:
      v14 = v13;
      goto LABEL_13;
    }
    v11 = 0LL;
LABEL_18:
    v8[v11] = 0;
    return objc_autoreleaseReturnValue(v7);
  }
  else
  {
    v17 = _objc_msgSend(&OBJC_CLASS___NSData, "dataWithBytes:length:", &unk_1010DEEB0, 1LL);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    return objc_autoreleaseReturnValue(v18);
  }
}

//----- (0000000100C50152) ----------------------------------------------------
char *__cdecl +[tools NSString2CString:](id a1, SEL a2, id a3)
{
  unsigned __int64 v6; // r15

  v3 = objc_retain(a3);
  v4 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
         &OBJC_CLASS___tools,
         "stringIsNilOrEmpty:ifNilReturnDefString:",
         v3,
         &charsToLeaveEscaped);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = CFStringConvertEncodingToNSStringEncoding(0x632u);
  v7 = objc_retainAutorelease(v5);
  v8 = (char *)_objc_msgSend(v7, "cStringUsingEncoding:", v6);
  return v8;
}

//----- (0000000100C501FD) ----------------------------------------------------
signed __int64 __cdecl +[tools getStringHex:](id a1, SEL a2, id a3)
{
  signed __int64 v6; // r13
  unsigned __int16 v7; // ax
  int v9; // ecx
  int v10; // eax

  v3 = _objc_msgSend(a3, "lowercaseString");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( (__int64)_objc_msgSend(v4, "length") > 0 )
  {
    v5 = 0LL;
    v6 = 0LL;
    while ( 1 )
    {
      v7 = (unsigned __int16)_objc_msgSend(v4, "characterAtIndex:", v5);
      v9 = v7 << 24;
      if ( (unsigned int)(v9 - 788529153) <= 0xAFFFFFE )
        break;
      if ( (unsigned int)(v9 - 1610612737) <= 0x6FFFFFE )
      {
        v10 = (char)v7 - 87;
        goto LABEL_7;
      }
LABEL_8:
      ++v5;
      if ( !v8 )
        goto LABEL_11;
    }
    v10 = (char)v7 - 48;
LABEL_7:
    v6 = (unsigned int)(int)((double)(int)v6 + exp2((double)(int)v8 * 4.0) * (double)v10);
    goto LABEL_8;
  }
  v6 = 0LL;
LABEL_11:
  return v6;
}

//----- (0000000100C50302) ----------------------------------------------------
id __cdecl +[tools Base64Encode:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r14

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = _objc_msgSend(v4, "dataUsingEncoding:allowLossyConversion:", 4LL, 1LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = +[GTMBase64 encodeData:](&OBJC_CLASS___GTMBase64, "encodeData:", v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = objc_alloc(&OBJC_CLASS___NSString);
    v5 = (__CFString *)_objc_msgSend(v11, "initWithData:encoding:", v9, 4LL);
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C50444) ----------------------------------------------------
id __cdecl +[tools Base64Decode:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r14

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = _objc_msgSend(v4, "dataUsingEncoding:allowLossyConversion:", 4LL, 1LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = +[GTMBase64 decodeData:](&OBJC_CLASS___GTMBase64, "decodeData:", v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = objc_alloc(&OBJC_CLASS___NSString);
    v5 = (__CFString *)_objc_msgSend(v11, "initWithData:encoding:", v9, 4LL);
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C50586) ----------------------------------------------------
id __cdecl +[tools getMacAddr](id a1, SEL a2)
{
  unsigned __int8 *v2; // rax
  unsigned __int8 *v3; // rbx
  NSString *v5; // rax
  NSString *v6; // r14
  size_t __size; // [rsp+18h] [rbp-48h] BYREF
  int v12[4]; // [rsp+20h] [rbp-40h] BYREF
  int v13; // [rsp+30h] [rbp-30h]

  *(_OWORD *)v12 = xmmword_1010CE770;
  v13 = 3;
  if ( if_nametoindex("en0") )
  {
    a2 = (SEL)6;
    if ( sysctl(v12, 6u, 0LL, &__size, 0LL, 0LL) < 0 )
    {
      v10 = "Error: sysctl, take 1/n";
    }
    else
    {
      v2 = (unsigned __int8 *)malloc(__size);
      if ( v2 )
      {
        v3 = v2;
        a2 = (SEL)6;
        if ( sysctl(v12, 6u, v2, &__size, 0LL, 0LL) >= 0 )
        {
          v4 = v3[117];
          v5 = _objc_msgSend(
                 &OBJC_CLASS___NSString,
                 "stringWithFormat:",
                 CFSTR("%02x-%02x-%02x-%02x-%02x-%02x"),
                 v3[v4 + 120],
                 v3[v4 + 121],
                 v3[v4 + 122],
                 v3[v4 + 123],
                 v3[v4 + 124],
                 v3[v4 + 125]);
          v6 = objc_retainAutoreleasedReturnValue(v5);
          free(v3);
          v7 = _objc_msgSend(v6, "uppercaseString");
          v8 = objc_retainAutoreleasedReturnValue(v7);
          return objc_autoreleaseReturnValue(v8);
        }
        v10 = "Error: sysctl, take 2";
      }
      else
      {
        v10 = "Could not allocate memory. error!/n";
      }
    }
  }
  else
  {
    v10 = "Error: if_nametoindex error/n";
  }
  printf(v10, a2);
  return objc_autoreleaseReturnValue(0LL);
}

//----- (0000000100C50712) ----------------------------------------------------
signed __int64 __cdecl +[tools getCodeDecimal:](id a1, SEL a2, id a3)
{
  signed __int64 v5; // rbx
  id obj; // [rsp+40h] [rbp-C0h]

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = 2LL;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(
           v7,
           "GetConfigValue:Name:DefaultString:",
           "system",
           "WT_3Mask",
           CFSTR("1846*;1847*;500*;201*;202*;15*;16*;5*;120*;03*;203*;111*;900*;5190*;125*;100*;010*;120*;"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "componentsSeparatedByString:", CFSTR("*;"));
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v18 = 0LL;
    v19 = 0LL;
    v20 = 0LL;
    v21 = 0LL;
    obj = objc_retain(v11);
    v13 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v18, v24, 16LL);
    v5 = 2LL;
    if ( v13 )
    {
      v23 = v9;
      v14 = *(_QWORD *)v19;
      while ( 2 )
      {
        if ( !v13 )
          v13 = 1LL;
        for ( i = 0LL; i != v13; ++i )
        {
          if ( *(_QWORD *)v19 != v14 )
            objc_enumerationMutation(obj);
          if ( !_objc_msgSend(v12, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v18 + 1) + 8 * i)) )
          {
            v5 = 3LL;
            v9 = v23;
            goto LABEL_17;
          }
        }
        v13 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v18, v24, 16LL);
        if ( v13 )
          continue;
        break;
      }
      v9 = v23;
      v5 = 2LL;
    }
LABEL_17:
    v16 = obj;
  }
  return v5;
}

//----- (0000000100C509E9) ----------------------------------------------------
id __cdecl +[tools getMd5String:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r15
  _QWORD v11[5]; // [rsp+8h] [rbp-28h] BYREF

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, v6, CFSTR("NUL")) )
  {
    v7 = (const char *)_objc_msgSend(a1, "NSString2CString:", v4);
    sub_100D79630(v11, v7, -1);
    v8 = sub_100F9F630((__int64)v11);
    v9 = _objc_msgSend(a1, "CString2NSstring:", v8);
    v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v9);
    sub_100C56008(v11);
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C50AFC) ----------------------------------------------------
id __cdecl +[tools getMd5WithFilePath:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r14
  NSData *v6; // rax
  NSData *v7; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = _objc_msgSend(&OBJC_CLASS___NSData, "dataWithContentsOfFile:", v4);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v5 = &charsToLeaveEscaped;
    if ( v7 )
    {
      v8 = objc_alloc(&OBJC_CLASS___NSString);
      v9 = _objc_msgSend(v8, "initWithData:encoding:", v7, 4LL);
      v10 = +[tools getMd5String:](&OBJC_CLASS___tools, "getMd5String:", v9);
      v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v10);
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C50C55) ----------------------------------------------------
id __cdecl +[tools getDateWithFormatter:](id a1, SEL a2, id a3)
{
  __CFString *v5; // rbx

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
    v7 = _objc_msgSend(v6, "init");
    _objc_msgSend(v7, "setDateFormat:", v4);
    v8 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v7, "stringFromDate:", v9);
    v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v10);
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C50DAA) ----------------------------------------------------
id __cdecl +[tools getDateStrWithTimestamp:formatter:](id a1, SEL a2, signed __int64 a3, id a4)
{
  __CFString *v7; // r14
  NSDate *v10; // rax
  NSDate *v11; // r15

  v5 = objc_retain(a4);
  v6 = v5;
  v7 = &charsToLeaveEscaped;
  if ( v5 )
  {
    if ( _objc_msgSend(v5, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL")) )
      {
        v9 = (unsigned __int8)_objc_msgSend(v6, v8, CFSTR("NUL"));
        if ( a3 )
        {
          if ( !v9 )
          {
            v10 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:", (double)(int)a3);
            v11 = objc_retainAutoreleasedReturnValue(v10);
            v12 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
            v13 = _objc_msgSend(v12, "init");
            _objc_msgSend(v13, "setDateFormat:", v6);
            v15 = _objc_msgSend(v14, "stringFromDate:", v11);
            v7 = (__CFString *)objc_retainAutoreleasedReturnValue(v15);
          }
        }
      }
    }
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C50F0E) ----------------------------------------------------
id __cdecl +[tools getNativeTimestamp](id a1, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "timeIntervalSince1970");
  return _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%0.f"));
}

//----- (0000000100C50F95) ----------------------------------------------------
id __cdecl +[tools splitString:withSeparator:subSeparator:](id a1, SEL a2, id a3, id a4, id a5)
{
  id (__cdecl *v8)(id); // r12
  id (__cdecl *v9)(id); // r12
  id (**v20)(id, SEL, ...); // r13
  id (**v22)(id, SEL, ...); // r15
  id obj; // [rsp+80h] [rbp-D0h]

  v7 = objc_retain(a3);
  v39 = v8(a4);
  v44 = v9(a5);
  if ( v7 )
  {
    v10 = v39;
    if ( _objc_msgSend(v7, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL")) )
      {
        v13 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NUL"));
        if ( v39 )
        {
          if ( !v13 )
          {
            if ( _objc_msgSend(v39, "length") )
            {
              if ( !(unsigned __int8)_objc_msgSend(v39, "isEqualToString:", CFSTR("NULL")) )
              {
                v14 = (unsigned __int8)_objc_msgSend(v39, "isEqualToString:", CFSTR("NUL"));
                if ( v44 )
                {
                  if ( !v14
                    && _objc_msgSend(v44, "length")
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NULL"))
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NUL")) )
                  {
                    v15 = _objc_msgSend(v7, "componentsSeparatedByString:", v39);
                    v16 = objc_retainAutoreleasedReturnValue(v15);
                    v17 = v16;
                    if ( v16 && _objc_msgSend(v16, "count") )
                    {
                      v42 = v17;
                      v43 = v7;
                      v18 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
                      v38 = _objc_msgSend(v18, "init");
                      v33 = 0LL;
                      v32 = 0LL;
                      v31 = 0LL;
                      v30 = 0LL;
                      obj = objc_retain(v17);
                      v19 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v30, v45, 16LL);
                      if ( v19 )
                      {
                        v34 = *(_QWORD *)v31;
                        v20 = &_objc_msgSend;
                        do
                        {
                          v36 = "objectAtIndex:";
                          v35 = "setObject:forKey:";
                          v21 = 0LL;
                          do
                          {
                            if ( *(_QWORD *)v31 != v34 )
                              objc_enumerationMutation(obj);
                            v22 = v20;
                            v23 = ((id (*)(id, SEL, ...))v20)(
                                    *(id *)(*((_QWORD *)&v30 + 1) + 8LL * (_QWORD)v21),
                                    "componentsSeparatedByString:",
                                    v44);
                            v24 = objc_retainAutoreleasedReturnValue(v23);
                            v25 = v24;
                            if ( v24 && ((__int64 (__fastcall *)(id, const char *))v22)(v24, "count") == 2 )
                            {
                              v26 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v22)(v25, v36, 1LL);
                              v40 = objc_retainAutoreleasedReturnValue(v26);
                              v37 = v25;
                              v27 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v22)(v25, v36, 0LL);
                              v28 = objc_retainAutoreleasedReturnValue(v27);
                              ((void (__fastcall *)(id, const char *, id, id))v22)(v38, v35, v40, v28);
                              v10 = v39;
                              v25 = v37;
                            }
                            v21 = (id)(v29 + 1);
                            v20 = v22;
                          }
                          while ( v19 != v21 );
                          v19 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v22)(
                                      obj,
                                      "countByEnumeratingWithState:objects:count:",
                                      &v30,
                                      v45,
                                      16LL);
                        }
                        while ( v19 );
                      }
                      v17 = v42;
                      v7 = v43;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  else
  {
    v10 = v39;
  }
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100C51492) ----------------------------------------------------
id __cdecl +[tools splitOppositeString:withSeparator:subSeparator:](id a1, SEL a2, id a3, id a4, id a5)
{
  id (__cdecl *v8)(id); // r12
  id (__cdecl *v9)(id); // r12
  id (**v20)(id, SEL, ...); // r13
  id (**v22)(id, SEL, ...); // r15
  id obj; // [rsp+80h] [rbp-D0h]

  v7 = objc_retain(a3);
  v39 = v8(a4);
  v44 = v9(a5);
  if ( v7 )
  {
    v10 = v39;
    if ( _objc_msgSend(v7, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL")) )
      {
        v13 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NUL"));
        if ( v39 )
        {
          if ( !v13 )
          {
            if ( _objc_msgSend(v39, "length") )
            {
              if ( !(unsigned __int8)_objc_msgSend(v39, "isEqualToString:", CFSTR("NULL")) )
              {
                v14 = (unsigned __int8)_objc_msgSend(v39, "isEqualToString:", CFSTR("NUL"));
                if ( v44 )
                {
                  if ( !v14
                    && _objc_msgSend(v44, "length")
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NULL"))
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NUL")) )
                  {
                    v15 = _objc_msgSend(v7, "componentsSeparatedByString:", v39);
                    v16 = objc_retainAutoreleasedReturnValue(v15);
                    v17 = v16;
                    if ( v16 && _objc_msgSend(v16, "count") )
                    {
                      v42 = v17;
                      v43 = v7;
                      v18 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
                      v38 = _objc_msgSend(v18, "init");
                      v33 = 0LL;
                      v32 = 0LL;
                      v31 = 0LL;
                      v30 = 0LL;
                      obj = objc_retain(v17);
                      v19 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v30, v45, 16LL);
                      if ( v19 )
                      {
                        v34 = *(_QWORD *)v31;
                        v20 = &_objc_msgSend;
                        do
                        {
                          v36 = "objectAtIndex:";
                          v35 = "setObject:forKey:";
                          v21 = 0LL;
                          do
                          {
                            if ( *(_QWORD *)v31 != v34 )
                              objc_enumerationMutation(obj);
                            v22 = v20;
                            v23 = ((id (*)(id, SEL, ...))v20)(
                                    *(id *)(*((_QWORD *)&v30 + 1) + 8LL * (_QWORD)v21),
                                    "componentsSeparatedByString:",
                                    v44);
                            v24 = objc_retainAutoreleasedReturnValue(v23);
                            v25 = v24;
                            if ( v24 && ((__int64 (__fastcall *)(id, const char *))v22)(v24, "count") == 2 )
                            {
                              v26 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v22)(v25, v36, 0LL);
                              v40 = objc_retainAutoreleasedReturnValue(v26);
                              v37 = v25;
                              v27 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v22)(v25, v36, 1LL);
                              v28 = objc_retainAutoreleasedReturnValue(v27);
                              ((void (__fastcall *)(id, const char *, id, id))v22)(v38, v35, v40, v28);
                              v10 = v39;
                              v25 = v37;
                            }
                            v21 = (id)(v29 + 1);
                            v20 = v22;
                          }
                          while ( v19 != v21 );
                          v19 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v22)(
                                      obj,
                                      "countByEnumeratingWithState:objects:count:",
                                      &v30,
                                      v45,
                                      16LL);
                        }
                        while ( v19 );
                      }
                      v17 = v42;
                      v7 = v43;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  else
  {
    v10 = v39;
  }
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100C5198F) ----------------------------------------------------
id __cdecl +[tools loadFormNibName:bundle:](id a1, SEL a2, id a3, id a4)
{
  SEL v13; // r12
  id obj; // [rsp+60h] [rbp-C0h]

  objc_retain(a3);
  v24 = objc_retain(a4);
  v5 = objc_alloc(&OBJC_CLASS___NSNib);
  v22 = 0LL;
  v25 = _objc_msgSend(v5, "initWithNibNamed:bundle:", v6, v24);
  _objc_msgSend(v25, "instantiateWithOwner:topLevelObjects:", 0LL);
  v7 = objc_retain(v22);
  v18 = 0LL;
  v19 = 0LL;
  v20 = 0LL;
  v21 = 0LL;
  obj = objc_retain(v7);
  v9 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v18, v28, 16LL);
  if ( v9 )
  {
    v27 = v8;
    v23 = *(_QWORD *)v19;
    while ( 2 )
    {
      for ( i = 0LL; i != v9; i = (char *)i + 1 )
      {
        if ( *(_QWORD *)v19 != v23 )
          objc_enumerationMutation(obj);
        v11 = *(void **)(*((_QWORD *)&v18 + 1) + 8LL * (_QWORD)i);
        v12 = _objc_msgSend(&OBJC_CLASS___NSView, "class");
        if ( (unsigned __int8)_objc_msgSend(v11, v13, v12) )
        {
          v14 = objc_retain(v11);
          goto LABEL_13;
        }
      }
      v9 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v18, v28, 16LL);
      if ( v9 )
        continue;
      break;
    }
  }
  v14 = 0LL;
LABEL_13:
  v15 = obj;
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100C51C32) ----------------------------------------------------
id __cdecl +[tools nsstring2AttributedString:andStrColor:andFont:](id a1, SEL a2, id a3, id a4, id a5)
{

  v18 = v5;
  objc_retain(a3);
  v8 = objc_retain(a4);
  v9 = objc_retain(a5);
  if ( !v10 )
  v11 = objc_alloc(&OBJC_CLASS___NSMutableAttributedString);
  v13 = _objc_msgSend(v11, "initWithString:", v12);
  v15 = _objc_msgSend(v14, "length");
  if ( v8 )
    _objc_msgSend(v13, "addAttribute:value:range:", NSForegroundColorAttributeName, v8, 0LL, v15, v18);
  if ( v9 )
    _objc_msgSend(v13, "addAttribute:value:range:", NSFontAttributeName, v9, 0LL, v15);
  _objc_msgSend(v13, "fixAttributesInRange:", 0LL, v15);
  return objc_autoreleaseReturnValue(v13);
}

//----- (0000000100C51D75) ----------------------------------------------------
id __cdecl +[tools getSaveToDocumentsFileWithLastPath:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r14
  NSArray *v7; // rax
  NSArray *v8; // rax
  NSString *v11; // rax
  __CFString *v12; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
        && !(unsigned __int8)_objc_msgSend(v4, v6, CFSTR("NUL")) )
      {
        v7 = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, 1uLL, 1);
        v8 = objc_retainAutoreleasedReturnValue(v7);
        v9 = _objc_msgSend(v8, "firstObject");
        v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v9);
        if ( _objc_msgSend(v5, "length") )
        {
          v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@/%@/"), v5, v4);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          v5 = v12;
        }
      }
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C51EDA) ----------------------------------------------------
id __cdecl +[tools getDataToDocumentsFilesWithName:lastPath:](id a1, SEL a2, id a3, id a4)
{
  NSData *v9; // r15
  NSData *v16; // rax

  objc_retain(a3);
  v6 = objc_retain(a4);
  if ( v5
    && _objc_msgSend(v5, "length")
    && !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v8, "isEqualToString:", CFSTR("NUL")) )
  {
    v12 = +[tools getSaveToDocumentsFileWithLastPath:](&OBJC_CLASS___tools, "getSaveToDocumentsFileWithLastPath:", v6);
    v17 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v17, "stringByAppendingString:", v13);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(&OBJC_CLASS___NSData, "dataWithContentsOfFile:", v15);
    v9 = objc_retainAutoreleasedReturnValue(v16);
  }
  else
  {
    v9 = 0LL;
  }
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100C52035) ----------------------------------------------------
void __cdecl +[tools saveDataWithName:data:saveLastPath:](id a1, SEL a2, id a3, id a4, id a5)
{
  id (__cdecl *v8)(id); // r12
  id (__cdecl *v10)(id); // r12

  v7 = objc_retain(a3);
  v9 = v8(a4);
  v11 = v10(a5);
  if ( v7 )
  {
    if ( _objc_msgSend(v7, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL")) )
      {
        v13 = (unsigned __int8)_objc_msgSend(v7, v12, CFSTR("NUL"));
        if ( v9 )
        {
          if ( !v13 )
          {
            v14 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
            v15 = objc_retainAutoreleasedReturnValue(v14);
            if ( v15 )
            {
              v16 = +[tools getSaveToDocumentsFileWithLastPath:](
                      &OBJC_CLASS___tools,
                      "getSaveToDocumentsFileWithLastPath:",
                      v11);
              v22 = objc_retainAutoreleasedReturnValue(v16);
              if ( _objc_msgSend(v22, "length") )
              {
                _objc_msgSend(v17, "fileExistsAtPath:isDirectory:", v22);
                _objc_msgSend(v18, "createDirectoryAtPath:withIntermediateDirectories:attributes:error:", v22, 1LL, 0LL);
                v23 = objc_retain(0LL);
                v19 = _objc_msgSend(v22, "stringByAppendingString:", v7);
                v20 = objc_retainAutoreleasedReturnValue(v19);
                _objc_msgSend(v9, "writeToFile:atomically:", v20, 1LL);
                v21(v23);
              }
            }
          }
        }
      }
    }
  }
}

//----- (0000000100C5229C) ----------------------------------------------------
void __cdecl +[tools saveArrayWithName:array:saveLastPath:](id a1, SEL a2, id a3, id a4, id a5)
{

  v6 = objc_retain(a3);
  v7 = objc_retain(a4);
  v9 = objc_retain(v8);
  v10 = v9;
  if ( v9 )
  {
    if ( _objc_msgSend(v9, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("NULL")) )
      {
        v12 = (unsigned __int8)_objc_msgSend(v10, v11, CFSTR("NUL"));
        if ( v7 )
        {
          if ( !v12 )
          {
            v13 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
            v14 = objc_retainAutoreleasedReturnValue(v13);
            if ( v14 )
            {
              v15 = +[tools getSaveToDocumentsFileWithLastPath:](
                      &OBJC_CLASS___tools,
                      "getSaveToDocumentsFileWithLastPath:",
                      v10);
              v21 = objc_retainAutoreleasedReturnValue(v15);
              if ( _objc_msgSend(v21, "length") )
              {
                _objc_msgSend(v16, "fileExistsAtPath:isDirectory:", v21);
                _objc_msgSend(v17, "createDirectoryAtPath:withIntermediateDirectories:attributes:error:", v21, 1LL, 0LL);
                v22 = objc_retain(0LL);
                v18 = _objc_msgSend(v21, "stringByAppendingString:", v6);
                v19 = objc_retainAutoreleasedReturnValue(v18);
                _objc_msgSend(v7, "writeToFile:atomically:", v19, 1LL);
                v20(v22);
              }
            }
          }
        }
      }
    }
  }
}

//----- (0000000100C52503) ----------------------------------------------------
void __cdecl +[tools openURLToSafariWithURLStr:](id a1, SEL a2, id a3)
{
  NSURL *v4; // rax
  NSURL *v5; // rbx

  v3 = objc_retain(a3);
  if ( (unsigned __int8)+[tools isHTTPURLWithStr:](&OBJC_CLASS___tools, "isHTTPURLWithStr:", v3) )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v3);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = _objc_msgSend(&OBJC_CLASS___NSWorkspace, "sharedWorkspace");
    _objc_msgSend(v6, "openURL:", v5);
  }
}

//----- (0000000100C525BF) ----------------------------------------------------
id __cdecl +[tools showAlertToWindowCustomBtnName:title:message:iconImage:isNeedCancenBtn:OkBtnName:CancelBtnName:completionHandler:](
        id a1,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6,
        char a7,
        id a8,
        id a9,
        id a10)
{

  v27 = objc_retain(a3);
  v29 = objc_retain(a4);
  v30 = objc_retain(v12);
  v31 = objc_retain(a6);
  objc_retain(a8);
  v32 = objc_retain(a9);
  v28 = objc_retain(a10);
  v13 = objc_alloc(&OBJC_CLASS___NSAlert);
  v15 = _objc_msgSend(v13, "init");
  if ( v14
    && _objc_msgSend(v14, "length")
    && !(unsigned __int8)_objc_msgSend(v16, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v17, "isEqualToString:", CFSTR("NUL")) )
  {
    v19 = _objc_msgSend(v15, "addButtonWithTitle:", v18);
  }
  else
  {
    v19 = _objc_msgSend(v15, "addButtonWithTitle:", CFSTR("确认"));
  }
  v20 = objc_retainAutoreleasedReturnValue(v19);
  if ( a7 )
  {
    if ( v32
      && _objc_msgSend(v32, "length")
      && !(unsigned __int8)_objc_msgSend(v32, "isEqualToString:", CFSTR("NULL"))
      && !(unsigned __int8)_objc_msgSend(v32, "isEqualToString:", CFSTR("NUL")) )
    {
      v21 = _objc_msgSend(v15, "addButtonWithTitle:", v32);
    }
    else
    {
      v21 = _objc_msgSend(v15, "addButtonWithTitle:", CFSTR("取消"));
    }
    v22 = objc_retainAutoreleasedReturnValue(v21);
  }
  if ( v29
    && _objc_msgSend(v29, "length")
    && !(unsigned __int8)_objc_msgSend(v29, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v29, "isEqualToString:", CFSTR("NUL")) )
  {
    _objc_msgSend(v15, "setMessageText:", v29);
  }
  else
  {
    _objc_msgSend(v15, "setMessageText:", &charsToLeaveEscaped);
  }
  if ( v30
    && _objc_msgSend(v30, "length")
    && !(unsigned __int8)_objc_msgSend(v30, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v30, "isEqualToString:", CFSTR("NUL")) )
  {
    _objc_msgSend(v15, "setInformativeText:", v30);
  }
  else
  {
    _objc_msgSend(v15, "setInformativeText:", &charsToLeaveEscaped);
  }
  if ( v31
    && _objc_msgSend(v31, "length")
    && !(unsigned __int8)_objc_msgSend(v31, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v31, "isEqualToString:", CFSTR("NUL")) )
  {
    v23 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", v31);
    v24 = objc_retainAutoreleasedReturnValue(v23);
    _objc_msgSend(v15, "setIcon:", v24);
  }
  _objc_msgSend(v15, "setAlertStyle:", 0LL);
  _objc_msgSend(v15, "beginSheetModalForWindow:completionHandler:", v27, v28);
  return objc_autoreleaseReturnValue(v15);
}

//----- (0000000100C52A00) ----------------------------------------------------
id __cdecl +[tools showAlertToWindow:title:message:iconImage:isNeedCancenBtn:completionHandler:](
        id a1,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6,
        char a7,
        id a8)
{

  v26 = objc_retain(a3);
  v10 = objc_retain(a4);
  v28 = objc_retain(v11);
  objc_retain(a6);
  v27 = objc_retain(a8);
  v12 = objc_alloc(&OBJC_CLASS___NSAlert);
  v13 = _objc_msgSend(v12, "init");
  v14 = _objc_msgSend(v13, "addButtonWithTitle:", CFSTR("确认"));
  v15 = objc_retainAutoreleasedReturnValue(v14);
  if ( a7 )
  {
    v16 = _objc_msgSend(v13, "addButtonWithTitle:", CFSTR("取消"));
    v17 = objc_retainAutoreleasedReturnValue(v16);
  }
  if ( v10
    && _objc_msgSend(v10, "length")
    && !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("NUL")) )
  {
    _objc_msgSend(v13, "setMessageText:", v10);
  }
  else
  {
    _objc_msgSend(v13, "setMessageText:", &charsToLeaveEscaped);
  }
  if ( v28
    && _objc_msgSend(v28, "length")
    && !(unsigned __int8)_objc_msgSend(v28, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v28, "isEqualToString:", CFSTR("NUL")) )
  {
    _objc_msgSend(v13, "setInformativeText:", v28);
  }
  else
  {
    _objc_msgSend(v13, "setInformativeText:", &charsToLeaveEscaped);
  }
  if ( v18
    && _objc_msgSend(v18, "length")
    && !(unsigned __int8)_objc_msgSend(v19, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", CFSTR("NUL")) )
  {
    v22 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", v21);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    _objc_msgSend(v13, "setIcon:", v23);
  }
  _objc_msgSend(v13, "setAlertStyle:", 0LL);
  _objc_msgSend(v13, "beginSheetModalForWindow:completionHandler:", v26, v27);
  return objc_autoreleaseReturnValue(v13);
}

//----- (0000000100C52D29) ----------------------------------------------------
id __cdecl +[tools getQuotaMarketWithSetType:](id a1, SEL a2, id a3)
{
  __CFString *v5; // rbx
  unsigned int v8; // eax
  NSString *v9; // rax
  NSString *v10; // rax

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = sub_100C52E92();
    v7 = objc_retainAutoreleasedReturnValue(v6);
    if ( _objc_msgSend(v7, "count") )
    {
      v8 = (unsigned int)+[tools getStringHex:](&OBJC_CLASS___tools, "getStringHex:", v4);
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%d"), v8);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v7, "objectForKey:", v10);
      v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v11);
    }
    else
    {
      v5 = &charsToLeaveEscaped;
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C52E92) ----------------------------------------------------
id sub_100C52E92()
{
  if ( qword_1016D39F8 != -1 )
    dispatch_once(&qword_1016D39F8, &stru_1012EA468);
  return objc_retainAutoreleaseReturnValue(qword_1016D39F0);
}

//----- (0000000100C52EC2) ----------------------------------------------------
id __cdecl +[tools roundingForFloatNumber:afterPoint:](id a1, SEL a2, double a3, signed __int64 a4)
{
  NSDecimalNumberHandler *v4; // rax
  NSDecimalNumberHandler *v5; // r14
  NSString *v11; // rax
  NSString *v12; // r13
  int v15; // [rsp+0h] [rbp-40h]
  int v16; // [rsp+8h] [rbp-38h]

  v16 = 0;
  v15 = 0;
  v4 = _objc_msgSend(
         &OBJC_CLASS___NSDecimalNumberHandler,
         "decimalNumberHandlerWithRoundingMode:scale:raiseOnExactness:raiseOnOverflow:raiseOnUnderflow:raiseOnDivideByZero:",
         0LL,
         (unsigned int)(__int16)a4,
         0LL,
         0LL,
         v15,
         v16);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = objc_alloc(&OBJC_CLASS___NSDecimalNumber);
  HIDWORD(v7) = 0;
  *(float *)&v7 = a3;
  v8 = _objc_msgSend(v6, "initWithFloat:", v7);
  v9 = _objc_msgSend(v8, "decimalNumberByRoundingAccordingToBehavior:", v5);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  return objc_autoreleaseReturnValue(v12);
}

//----- (0000000100C52FDE) ----------------------------------------------------
double __cdecl +[tools addForFrontNumber:behindNumber:afterPoint:](
        id a1,
        SEL a2,
        double a3,
        double a4,
        signed __int64 a5)
{
  long double v6; // [rsp-28h] [rbp-28h]
  long double v7; // [rsp-28h] [rbp-28h]

  if ( a5 < 0 )
    return 0.0;
  *(double *)&v6 = a4;
  *((double *)&v6 + 1) = a3;
  pow(v6, (int)a1);
  pow(v7, (int)a1);
  return (double)((int)(v9 * (double)(int)10.0) + (int)(v8 * (double)(int)10.0)) * 10.0;
}

//----- (0000000100C53079) ----------------------------------------------------
double __cdecl +[tools subForFrontNumber:behindNumber:afterPoint:](
        id a1,
        SEL a2,
        double a3,
        double a4,
        signed __int64 a5)
{
  long double v6; // [rsp-28h] [rbp-28h]
  long double v7; // [rsp-28h] [rbp-28h]

  if ( a5 < 0 )
    return 0.0;
  *(double *)&v6 = a4;
  *((double *)&v6 + 1) = a3;
  pow(v6, (int)a1);
  pow(v7, (int)a1);
  return (double)((int)(v9 * (double)(int)10.0) - (int)(v8 * (double)(int)10.0)) * 10.0;
}

//----- (0000000100C53114) ----------------------------------------------------
char __cdecl +[tools isStockNeedCaclZichanWithQuotaMarket:](id a1, SEL a2, id a3)
{
  bool v5; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v5 = 1;
    if ( _objc_msgSend(v4, "rangeOfString:", CFSTR("USHA")) == (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v7 = _objc_msgSend(v4, "rangeOfString:", CFSTR("USZA"));
      if ( v7 == v8 )
      {
        v9 = _objc_msgSend(v4, "rangeOfString:", CFSTR("USHJ"));
        if ( v9 == v10 )
        {
          v11 = _objc_msgSend(v4, "rangeOfString:", CFSTR("USZJ"));
          if ( v11 == v12 )
          {
            v13 = _objc_msgSend(v4, "rangeOfString:", CFSTR("USHT"));
            v5 = v13 != v14;
          }
        }
      }
    }
  }
  else
  {
    v5 = 0;
  }
  return v5;
}

//----- (0000000100C53248) ----------------------------------------------------
id __cdecl +[tools stringIsNilOrEmpty:ifNilReturnDefString:](id a1, SEL a2, id a3, id a4)
{
  __CFString *v5; // r14
  __CFString *v6; // r15
  __CFString *v7; // rbx
  __CFString *v9; // rdi

  v5 = (__CFString *)objc_retain(a3);
  v6 = (__CFString *)objc_retain(a4);
  if ( v5
    && _objc_msgSend(v5, "length")
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
  {
    v9 = v5;
  }
  else
  {
    v7 = &charsToLeaveEscaped;
    if ( v6 )
    {
      if ( _objc_msgSend(v6, "length") )
      {
        if ( !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL")) )
        {
          v7 = &charsToLeaveEscaped;
          if ( !(unsigned __int8)_objc_msgSend(v6, v8, CFSTR("NUL")) )
            v7 = v6;
        }
      }
    }
    v9 = v7;
  }
  objc_retain(v9);
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100C53371) ----------------------------------------------------
id __cdecl +[tools getTradeProductAndVersion](id a1, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  NSString *v11; // rax
  NSString *v12; // rbx

  v2 = +[JYPlistManage shareInstance](&OBJC_CLASS___JYPlistManage, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(
         v3,
         "getConfigValueForSectionName:andKeyName:defaultStr:",
         CFSTR("ProductVersion"),
         CFSTR("product"),
         &charsToLeaveEscaped);
  objc_retainAutoreleasedReturnValue(v5);
  v6 = +[JYPlistManage shareInstance](&OBJC_CLASS___JYPlistManage, "shareInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(
         v7,
         "getConfigValueForSectionName:andKeyName:defaultStr:",
         CFSTR("ProductVersion"),
         CFSTR("version"),
         &charsToLeaveEscaped);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v10, v9);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  return objc_autoreleaseReturnValue(v12);
}

//----- (0000000100C534AE) ----------------------------------------------------
char __cdecl +[tools String:containKey:withSep:](id a1, SEL a2, id a3, id a4, id a5)
{
  bool v10; // r14
  id obj; // [rsp+50h] [rbp-C0h]

  v7 = objc_retain(a3);
  v8 = objc_retain(a4);
  v9 = objc_retain(a5);
  v10 = 0;
  if ( v7 && v8 )
  {
    v26 = v9;
    if ( v9 )
    {
      v11 = _objc_msgSend(v8, "componentsSeparatedByString:", v9);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = v12;
      if ( v12 && _objc_msgSend(v12, "count") )
      {
        v29 = v8;
        v25 = 0LL;
        v24 = 0LL;
        v23 = 0LL;
        v22 = 0LL;
        obj = objc_retain(v13);
        if ( _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v30, 16LL) )
        {
          v14 = *(_QWORD *)v23;
          v27 = v13;
          while ( 2 )
          {
            v15 = 0LL;
            do
            {
              if ( *(_QWORD *)v23 != v14 )
                objc_enumerationMutation(obj);
              _objc_msgSend(v7, "rangeOfString:", *(_QWORD *)(*((_QWORD *)&v22 + 1) + 8 * v15));
              if ( v17 )
              {
                v21 = obj;
                v10 = 1;
                v8 = v29;
                goto LABEL_17;
              }
              ++v15;
            }
            while ( v16 != v15 );
            v18 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v22, v30, 16LL);
            v13 = v27;
            if ( v18 )
              continue;
            break;
          }
        }
        v8 = v29;
      }
    }
    _objc_msgSend(v7, "rangeOfString:", v8);
    v10 = v19 != 0;
LABEL_17:
    v9 = v26;
  }
  return v10;
}

//----- (0000000100C5377E) ----------------------------------------------------
double __cdecl +[tools fixAmount:isShowZhang:isKCBType:Mrdw:](
        id a1,
        SEL a2,
        double result,
        char a4,
        char a5,
        signed __int64 a6)
{
  bool v7; // cl
  long double v9; // [rsp-18h] [rbp-18h]

  if ( a6 >= 2 )
    return result / (double)(int)a6;
  if ( a6 == 1 )
  {
    v6 = 0LL;
    v7 = a5 == 0;
    if ( a4 )
    {
      v8 = 10.0;
    }
    else
    {
      LOBYTE(v6) = v7;
      v8 = dbl_1010DEEA0[v6];
    }
    *((double *)&v9 + 1) = v8;
    floor(v9);
    return result / v8 * v10;
  }
  return result;
}

//----- (0000000100C537D8) ----------------------------------------------------
char __cdecl +[tools isMeiguMarket:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // r15
  _QWORD v8[9]; // [rsp+8h] [rbp-78h] BYREF

  v3 = objc_retain(a3);
  v8[0] = CFSTR("UUSD");
  v8[1] = CFSTR("UNYI");
  v8[2] = CFSTR("UNQT");
  v8[3] = CFSTR("UNYN");
  v8[4] = CFSTR("UNQQ");
  v8[5] = CFSTR("UNQS");
  v8[6] = CFSTR("UUSD");
  v8[7] = CFSTR("UUSA");
  v8[8] = CFSTR("UNYA");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v8, 9LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "containsObject:", v3);
  return v6;
}

//----- (0000000100C538FF) ----------------------------------------------------
id __cdecl +[tools ProcssMeiguPrice:](id a1, SEL a2, id a3)
{
  NSString *v6; // rax
  NSString *v7; // rbx

  v4 = objc_retain(a3);
  _objc_msgSend(v4, "doubleValue");
  _objc_msgSend(v5, "doubleValue");
  if ( v3 >= 1.0 )
    v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f"));
  else
    v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.3f"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C539C2) ----------------------------------------------------
id __cdecl +[tools trimString:left:](id a1, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = v6;
  if ( v6
    && _objc_msgSend(v6, "length")
    && (v8 = (char *)_objc_msgSend(v5, "length"), (__int64)v8 > 0)
    && !_objc_msgSend(v5, "rangeOfString:", v7) )
  {
    v13 = _objc_msgSend(v5, "substringWithRange:", v9, &v8[-v9]);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = +[tools trimString:left:](&OBJC_CLASS___tools, "trimString:left:", v14, v7);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v10 = v16;
    v17 = v14;
    if ( v14 != v16 )
      v17 = v16;
    objc_retain(v17);
  }
  else
  {
    v10 = objc_retain(v5);
  }
  objc_autorelease(v11);
  return v10;
}

//----- (0000000100C53B0F) ----------------------------------------------------
id __cdecl +[tools splitString2:withSeparator:subSeparator:](id a1, SEL a2, id a3, id a4, id a5)
{
  SEL v35; // [rsp+58h] [rbp-108h]
  SEL v36; // [rsp+60h] [rbp-100h]
  SEL v37; // [rsp+68h] [rbp-F8h]
  SEL v38; // [rsp+70h] [rbp-F0h]
  SEL v39; // [rsp+78h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-D8h]

  v6 = objc_retain(a3);
  v7 = objc_retain(a4);
  v44 = objc_retain(v8);
  if ( v6 )
  {
    if ( _objc_msgSend(v6, "length") )
    {
      if ( !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NULL")) )
      {
        v11 = (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("NUL"));
        if ( v7 )
        {
          if ( !v11 )
          {
            if ( _objc_msgSend(v7, "length") )
            {
              if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NULL")) )
              {
                v12 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("NUL"));
                if ( v44 )
                {
                  if ( !v12
                    && _objc_msgSend(v44, "length")
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NULL"))
                    && !(unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("NUL")) )
                  {
                    v13 = _objc_msgSend(v6, "componentsSeparatedByString:", v7);
                    v14 = objc_retainAutoreleasedReturnValue(v13);
                    v15 = v14;
                    if ( v14 && _objc_msgSend(v14, "count") )
                    {
                      v42 = v7;
                      v40 = v15;
                      v16 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
                      _objc_msgSend(v16, "init");
                      v32 = 0LL;
                      v31 = 0LL;
                      v30 = 0LL;
                      v29 = 0LL;
                      obj = objc_retain(v15);
                      v17 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v46, 16LL);
                      if ( v17 )
                      {
                        v34 = *(_QWORD *)v30;
                        do
                        {
                          v39 = "stringByReplacingOccurrencesOfString:withString:";
                          v35 = "rangeOfString:";
                          v36 = "substringWithRange:";
                          v37 = "substringFromIndex:";
                          v38 = "setObject:forKey:";
                          v18 = 0LL;
                          v33 = v17;
                          do
                          {
                            if ( *(_QWORD *)v30 != v34 )
                              objc_enumerationMutation(obj);
                            v19 = objc_retain(*(id *)(*((_QWORD *)&v29 + 1) + 8LL * (_QWORD)v18));
                            if ( !v19 )
                              goto LABEL_42;
                            v20 = v19;
                            if ( _objc_msgSend(v19, "length")
                              && !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", CFSTR("NULL"))
                              && !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", CFSTR("NUL")) )
                            {
                              v21 = _objc_msgSend(v20, v39, CFSTR(" "), &charsToLeaveEscaped);
                              v45 = objc_retainAutoreleasedReturnValue(v21);
                              v20 = v45;
                            }
                            if ( v20 )
                            {
                              if ( _objc_msgSend(v20, "length") )
                              {
                                if ( !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", CFSTR("NULL"))
                                  && !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", CFSTR("NUL")) )
                                {
                                  v22 = _objc_msgSend(v20, v35, v44);
                                  if ( v22 )
                                  {
                                    v45 = v22;
                                    if ( v22 != (id)0x7FFFFFFFFFFFFFFFLL )
                                    {
                                      v23 = (char *)_objc_msgSend(v20, "length");
                                      if ( v45 != v23 - 1 )
                                      {
                                        v24 = _objc_msgSend(v20, v36, 0LL);
                                        v43 = objc_retainAutoreleasedReturnValue(v24);
                                        v25 = _objc_msgSend(v20, v37, (char *)v45 + 1);
                                        v45 = objc_retainAutoreleasedReturnValue(v25);
                                        if ( v43
                                          && _objc_msgSend(v43, "length")
                                          && !(unsigned __int8)_objc_msgSend(v43, "isEqualToString:", CFSTR("NULL"))
                                          && !(unsigned __int8)_objc_msgSend(v43, "isEqualToString:", CFSTR("NUL")) )
                                        {
                                          if ( v45 )
                                          {
                                            if ( _objc_msgSend(v45, "length")
                                              && !(unsigned __int8)_objc_msgSend(v45, "isEqualToString:", CFSTR("NULL"))
                                              && !(unsigned __int8)_objc_msgSend(v45, "isEqualToString:", CFSTR("NUL")) )
                                            {
                                              v27 = _objc_msgSend(v45, v39, CFSTR("\r"), CFSTR("\n"));
                                              v28 = objc_retainAutoreleasedReturnValue(v27);
                                              v45 = v28;
                                            }
                                          }
                                          else
                                          {
                                            v45 = 0LL;
                                          }
                                          _objc_msgSend(v26, v38, v45, v43);
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                            else
                            {
LABEL_42:
                              v20 = 0LL;
                            }
                            v18 = (char *)v18 + 1;
                          }
                          while ( v33 != v18 );
                          v17 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v46, 16LL);
                        }
                        while ( v17 );
                      }
                      v7 = v42;
                      v15 = v40;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100C5426B) ----------------------------------------------------
id __cdecl +[tools splitString3:withSeparator:subSeparator:](id a1, SEL a2, char *a3, char *a4, char *a5)
{
  volatile signed __int32 **v9; // r13
  unsigned __int64 v11; // r14
  int v13; // eax
  int v14; // ebx
  volatile signed __int32 **v25; // r12
  SEL v36; // [rsp+20h] [rbp-80h]
  SEL v37; // [rsp+28h] [rbp-78h]
  SEL v41; // [rsp+58h] [rbp-48h]
  SEL v44; // [rsp+70h] [rbp-30h]

  if ( !a3 )
    goto LABEL_38;
  v5 = 0LL;
  if ( !a4 )
    return objc_autoreleaseReturnValue(v5);
  if ( !*a3 )
    return objc_autoreleaseReturnValue(v5);
  v5 = 0LL;
  if ( !a5 || !*a4 )
    return objc_autoreleaseReturnValue(v5);
  if ( !*a5 )
  {
LABEL_38:
    v5 = 0LL;
    return objc_autoreleaseReturnValue(v5);
  }
  v38 = 0LL;
  v39 = 0LL;
  sub_100C54736(a3, (__int64)a4, (__int64)&v38, 0);
  if ( (int)((*((_QWORD *)&v38 + 1) - (_QWORD)v38) >> 3) <= 0 )
  {
    v5 = 0LL;
  }
  else
  {
    v6 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
    v5 = _objc_msgSend(v6, "init");
    v7 = *((_QWORD *)&v38 + 1);
    v8 = v38;
    if ( (int)((*((_QWORD *)&v38 + 1) - (_QWORD)v38) >> 3) > 0 )
    {
      v40 = v5;
      v36 = "CString2NSstring:";
      v41 = "length";
      v44 = "isEqualToString:";
      v37 = "stringWithFormat:";
      v9 = (volatile signed __int32 **)&v35;
      v10 = 0LL;
      v11 = 0LL;
      do
      {
        if ( (v7 - v8) >> 3 <= v11 )
          std::__throw_out_of_range("vector::_M_range_check");
        sub_100F9EE40(v9, (volatile signed __int32 **)(v10 + v8));
        if ( *(_QWORD *)(v35 - 24) )
        {
          v13 = sub_100FA0A50((__int64)v9, v12);
          v14 = v13;
          if ( v13 >= 0 )
          {
            sub_100F9F8A0(&v42, (_BYTE **)v9, v13);
            sub_100F9F7B0(&v43, (__int64 *)v9, v14 + 1);
            sub_100F9FD90(&v42);
            sub_100F9FD90(&v43);
            sub_100F9FD70(&v42);
            sub_100F9FD70(&v43);
            if ( *(_QWORD *)(v42 - 24) && *(_QWORD *)(v43 - 24) )
            {
              v15 = sub_100F9F630((__int64)&v42);
              v16 = _objc_msgSend(&OBJC_CLASS___tools, v36, v15);
              objc_retainAutoreleasedReturnValue(v16);
              v17 = sub_100F9F630((__int64)&v43);
              v18 = _objc_msgSend(&OBJC_CLASS___tools, v36, v17);
              v19 = objc_retainAutoreleasedReturnValue(v18);
              if ( !v20
                || !_objc_msgSend(v20, v41)
                || (unsigned __int8)_objc_msgSend(v21, v44, CFSTR("NULL"))
                || (unsigned __int8)_objc_msgSend(v22, v44, CFSTR("NUL")) )
              {
                v23 = _objc_msgSend(&OBJC_CLASS___NSString, v37, CFSTR("%s"), v42);
                objc_retainAutoreleasedReturnValue(v23);
                v9 = v25;
              }
              if ( !v19
                || !_objc_msgSend(v19, v41)
                || (unsigned __int8)_objc_msgSend(v19, v44, CFSTR("NULL"))
                || (unsigned __int8)_objc_msgSend(v19, v44, CFSTR("NUL")) )
              {
                v27 = _objc_msgSend(&OBJC_CLASS___NSString, v37, CFSTR("%s"), v43);
                v28 = v19;
                v19 = objc_retainAutoreleasedReturnValue(v27);
              }
              if ( v26 )
              {
                if ( _objc_msgSend(v26, v41) )
                {
                  if ( !(unsigned __int8)_objc_msgSend(v29, v44, CFSTR("NULL")) )
                  {
                    v32 = (unsigned __int8)_objc_msgSend(v30, v44, CFSTR("NUL"));
                    if ( v19 )
                    {
                      if ( !v32
                        && _objc_msgSend(v19, v41)
                        && !(unsigned __int8)_objc_msgSend(v19, v44, CFSTR("NULL"))
                        && !(unsigned __int8)_objc_msgSend(v19, v44, CFSTR("NUL")) )
                      {
                        _objc_msgSend(v40, "setObject:forKey:", v19, v33);
                      }
                    }
                  }
                }
              }
            }
            sub_100C56008(&v43);
            sub_100C56008(&v42);
          }
        }
        sub_100C56008(v9);
        ++v11;
        v7 = *((_QWORD *)&v38 + 1);
        v8 = v38;
        v10 += 8LL;
      }
      while ( (__int64)v11 < (int)((*((_QWORD *)&v38 + 1) - (_QWORD)v38) >> 3) );
      v5 = v40;
    }
  }
  sub_100C560F8((__int64 *)&v38);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C54736) ----------------------------------------------------
int __fastcall sub_100C54736(char *__src, __int64 a2, __int64 a3, char a4)
{
  _BYTE *v7; // r12

  if ( __src )
    v6 = (__int64)&__src[strlen(__src)];
  else
    v6 = -1LL;
  v9 = sub_100D38170(__src, (_BYTE *)v6);
  sub_100C56151(v9, v7, a3, a4);
  return sub_100C56008(&v9);
}

//----- (0000000100C547B9) ----------------------------------------------------
id __cdecl +[tools getBase64DecodeData:](id a1, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%3D"), CFSTR("="));
  objc_retainAutoreleasedReturnValue(v4);
  v6 = +[Base64_FXCP decodeString:](&OBJC_CLASS___Base64_FXCP, "decodeString:", v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C54859) ----------------------------------------------------
id __cdecl +[tools getBase64DecodeDataJHMode:](id a1, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%2B"), CFSTR("+"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%2F"), CFSTR("/"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%3D"), CFSTR("="));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%61"), CFSTR("="));
  v11 = v9;
  v12 = objc_retainAutoreleasedReturnValue(v10);
  v13 = +[Base64_FXCP decodeString:](&OBJC_CLASS___Base64_FXCP, "decodeString:", v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100C5498A) ----------------------------------------------------
id __cdecl +[tools getBase64DecodeString:](id a1, SEL a2, id a3)
{
  unsigned __int64 v5; // rbx
  unsigned __int64 v8; // rbx

  v3 = +[tools getBase64DecodeDataJHMode:](&OBJC_CLASS___tools, "getBase64DecodeDataJHMode:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = CFStringConvertEncodingToNSStringEncoding(0x632u);
  v6 = objc_alloc(&OBJC_CLASS___NSString);
  v7 = _objc_msgSend(v6, "initWithData:encoding:", v4, v5);
  if ( !v7 )
  {
    v8 = CFStringConvertEncodingToNSStringEncoding(0x8000100u);
    v9 = objc_alloc(&OBJC_CLASS___NSString);
    v7 = _objc_msgSend(v9, "initWithData:encoding:", v4, v8);
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C54A4E) ----------------------------------------------------
id __cdecl +[tools getBase64EncodeString:](id a1, SEL a2, id a3)
{
  unsigned __int64 v7; // rbx

  v3 = _objc_msgSend(a3, "dataUsingEncoding:", 4LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[Base64_FXCP encodeData:](&OBJC_CLASS___Base64_FXCP, "encodeData:", v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = CFStringConvertEncodingToNSStringEncoding(0x632u);
  v8 = objc_alloc(&OBJC_CLASS___NSString);
  v9 = _objc_msgSend(v8, "initWithData:encoding:", v6, v7);
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100C54B0D) ----------------------------------------------------
void __cdecl +[tools AddTradeLog:logText:](id a1, SEL a2, signed __int64 a3, id a4)
{
  ThsDebugWindowController *v6; // rax
  ThsDebugWindowController *v7; // rbx

  v5 = objc_retain(a4);
  v6 = +[ThsDebugWindowController sharedInstance](&OBJC_CLASS___ThsDebugWindowController, "sharedInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[ThsDebugWindowController AddTradeLog:logText:](v7, "AddTradeLog:logText:", a3, v5);
}

//----- (0000000100C54B9C) ----------------------------------------------------
char __cdecl +[tools isKeChuangBanCode:](id a1, SEL a2, id a3)
{
  return (unsigned __int8)+[HXTools isKeChuangBanStock:market:](
                            &OBJC_CLASS___HXTools,
                            "isKeChuangBanStock:market:",
                            a3,
                            CFSTR("USHA"));
}

//----- (0000000100C54BC0) ----------------------------------------------------
char __cdecl +[tools isChuangYeBanCode:](id a1, SEL a2, id a3)
{
  return (unsigned __int8)+[HXTools isChuangYeBanStock:market:](
                            &OBJC_CLASS___HXTools,
                            "isChuangYeBanStock:market:",
                            a3,
                            CFSTR("USZA"));
}

//----- (0000000100C54BE4) ----------------------------------------------------
id __cdecl +[tools trans2JiaoyiMarketWih:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    if ( (unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USZA")) )
    {
      v5 = CFSTR("1");
    }
    else if ( (unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USHA")) )
    {
      v5 = CFSTR("2");
    }
    else if ( (unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USZB")) )
    {
      v5 = CFSTR("4");
    }
    else if ( (unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USHB")) )
    {
      v5 = CFSTR("5");
    }
    else if ( (unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USTA")) )
    {
      v5 = CFSTR("6");
    }
    else
    {
      v5 = CFSTR("7");
      if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("USTB")) )
        v5 = &charsToLeaveEscaped;
    }
  }
  return v5;
}

//----- (0000000100C54D5A) ----------------------------------------------------
char __cdecl +[tools getCodeTypeByMarket:](id a1, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = 33;
  if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USZA")) )
  {
    v4 = 17;
    if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USHA")) )
    {
      v4 = 34;
      if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USZB")) )
      {
        v4 = 18;
        if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USHB")) )
        {
          v4 = 39;
          if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USTA")) )
          {
            v4 = 40;
            if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("USTB")) )
              v4 = 0;
          }
        }
      }
    }
  }
  return v4;
}

//----- (0000000100C54E42) ----------------------------------------------------
id __cdecl +[tools getSetTypeWithQuotaMarket:](id a1, SEL a2, id a3)
{
  __CFString *v5; // r14
  __CFString *v11; // r14
  NSString *v19; // rax
  SEL v25; // [rsp+48h] [rbp-D8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = v3;
  v5 = &charsToLeaveEscaped;
  if ( v3
    && _objc_msgSend(v3, "length")
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
    && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
  {
    v6 = sub_100C52E92();
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    v5 = &charsToLeaveEscaped;
    if ( v7 )
    {
      v28 = v7;
      if ( _objc_msgSend(v7, "count") )
      {
        v24 = 0LL;
        v23 = 0LL;
        v22 = 0LL;
        v21 = 0LL;
        v9 = _objc_msgSend(v8, "allKeys");
        obj = objc_retainAutoreleasedReturnValue(v9);
        v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v30, 16LL);
        v11 = &charsToLeaveEscaped;
        if ( v10 )
        {
          v26 = *(_QWORD *)v22;
          while ( 2 )
          {
            v25 = "objectForKey:";
            v12 = 0LL;
            v27 = v10;
            do
            {
              if ( *(_QWORD *)v22 != v26 )
                objc_enumerationMutation(obj);
              v13 = *(void **)(*((_QWORD *)&v21 + 1) + 8LL * (_QWORD)v12);
              v14 = _objc_msgSend(v8, v25, v13);
              v15 = objc_retainAutoreleasedReturnValue(v14);
              v16 = (unsigned __int8)_objc_msgSend(v15, "isEqualToString:", v4);
              if ( v16 )
              {
                v8 = v28;
                v18 = _objc_msgSend(v13, "integerValue");
                v19 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%lx"), v18);
                v11 = objc_retainAutoreleasedReturnValue(v19);
                goto LABEL_17;
              }
              v12 = (id)(v17 + 1);
              v8 = v28;
            }
            while ( v27 != v12 );
            v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v30, 16LL);
            if ( v10 )
              continue;
            break;
          }
          v11 = &charsToLeaveEscaped;
        }
LABEL_17:
        v5 = objc_retain(v11);
      }
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C55172) ----------------------------------------------------
id __cdecl +[tools getMarketShortChineseNameWithMarket:](id a1, SEL a2, id a3)
{
  __CFString *v4; // r14
  __int16 v5; // ax
  __int16 v6; // r12
  unsigned __int16 v7; // ax
  __CFString *v9; // rdi

  v3 = objc_retain(a3);
  v4 = &charsToLeaveEscaped;
  if ( _objc_msgSend(v3, "length") != (id)4
    || (unsigned __int16)_objc_msgSend(v3, "characterAtIndex:", 0LL) != 85
    || (unsigned __int16)_objc_msgSend(v3, "characterAtIndex:", 1LL) != 83 )
  {
    goto LABEL_23;
  }
  _objc_msgSend(v3, "characterAtIndex:", 2LL);
  v5 = (unsigned __int16)_objc_msgSend(v3, "characterAtIndex:", 3LL);
  switch ( v6 )
  {
    case 'H':
      v9 = CFSTR("沪");
      goto LABEL_12;
    case 'Z':
      v9 = CFSTR("深");
LABEL_12:
      if ( v5 <= 73 )
      {
        if ( v5 != 65 )
        {
          if ( v5 == 68 )
          {
            v4 = CFSTR("债");
            break;
          }
LABEL_22:
          v4 = CFSTR("未知");
          break;
        }
      }
      else
      {
        if ( v5 == 74 )
        {
          v4 = CFSTR("基");
          break;
        }
        if ( v5 != 80 && v5 != 84 )
          goto LABEL_22;
      }
      v10 = _objc_msgSend(v9, "stringByAppendingString:", CFSTR("A"));
      v4 = (__CFString *)objc_retainAutoreleasedReturnValue(v10);
      break;
    case 'T':
      v7 = v5 - 65;
      if ( v7 <= 0x13u )
      {
        v8 = 524291LL;
        if ( _bittest64(&v8, v7) )
          v4 = CFSTR("转股");
      }
      break;
  }
LABEL_23:
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100C552E9) ----------------------------------------------------
void __cdecl +[tools writeFileLog:](id a1, SEL a2, id a3)
{
  +[HXLoggerManager trackLog:log:showStack:](&OBJC_CLASS___HXLoggerManager, "trackLog:log:showStack:", 106LL, a3, 0LL);
}

//----- (0000000100C5530E) ----------------------------------------------------
void __cdecl +[tools addLocalLog:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  _QWORD block[4]; // [rsp+8h] [rbp-58h] BYREF

  v3 = objc_retain(a3);
  v4 = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, 1uLL, 1);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "firstObject");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "stringByAppendingPathComponent:", CFSTR("locallog.dat"));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v5);
  block[0] = _NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = sub_100C55450;
  block[3] = &unk_1012EACF8;
  v18 = v3;
  v19 = v9;
  v11 = objc_retain(v9);
  v12 = objc_retain(v3);
  dispatch_async(&_dispatch_main_q, block);
  v13(v19);
  v14(v18);
  v15(v11);
  v16(v12);
}

//----- (0000000100C55450) ----------------------------------------------------
void __fastcall sub_100C55450(__int64 a1)
{
  NSString *v7; // rax
  NSString *v8; // rbx

  v1 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
  v2 = _objc_msgSend(v1, "init");
  _objc_msgSend(v2, "setDateFormat:", CFSTR("yyyy'-'MM'-'dd' 'HH':'mm':'sss'"));
  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v2, "stringFromDate:", v4);
  objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@:%@\n"), v6, *(_QWORD *)(a1 + 32));
  v8 = objc_retainAutoreleasedReturnValue(v7);
  +[tools writeFileAtPath:content:](&OBJC_CLASS___tools, "writeFileAtPath:content:", *(_QWORD *)(a1 + 40), v8);
}

//----- (0000000100C55580) ----------------------------------------------------
id __fastcall sub_100C55580(__int64 a1, __int64 a2)
{
  objc_retain(*(id *)(a2 + 32));
  return objc_retain(*(id *)(a2 + 40));
}

//----- (0000000100C555AA) ----------------------------------------------------
id __cdecl +[tools stackSymboles](id a1, SEL a2)
{
  NSString *v6; // rax
  NSString *v7; // rbx

  v2 = _objc_msgSend(&OBJC_CLASS___NSThread, "callStackSymbols");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(a1, "dictOrArrToJsonString:options:", v3, 0LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("\n<<<===堆栈信息:\n%@\n===>>>\n"), v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C55668) ----------------------------------------------------
id __cdecl +[tools dictOrArrToJsonString:options:](id a1, SEL a2, id a3, unsigned __int64 a4)
{
  __CFString *v7; // rbx
  __CFString *v15; // r13

  v5 = objc_retain(a3);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6) )
    v7 = (__CFString *)objc_retain(v5);
  else
    v7 = &charsToLeaveEscaped;
  if ( (unsigned __int8)_objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "isValidJSONObject:", v5) )
  {
    v8 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "dataWithJSONObject:options:error:", v5, a4);
    objc_retainAutoreleasedReturnValue(v8);
    v9 = objc_retain(0LL);
    v11 = _objc_msgSend(v10, "length");
    if ( v9 || !v11 )
    {
      v15 = v7;
    }
    else
    {
      v13 = objc_alloc(&OBJC_CLASS___NSString);
      v15 = (__CFString *)_objc_msgSend(v13, "initWithData:encoding:", v14, 4LL);
    }
    v7 = v15;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100C557EA) ----------------------------------------------------
void __cdecl +[tools writeFileAtPath:content:](id a1, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = _objc_msgSend(&OBJC_CLASS___NSFileHandle, "fileHandleForUpdatingAtPath:", v5);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "seekToEndOfFile");
  v9 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "fileExistsAtPath:", v5);
  if ( v11 )
  {
    v12 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v12) )
    {
      v13 = _objc_msgSend(v6, "dataUsingEncoding:", 4LL);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      _objc_msgSend(v8, "writeData:", v14);
      _objc_msgSend(v8, "closeFile");
    }
  }
  else
  {
    v15 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v15) )
    {
      v16 = _objc_msgSend(v6, "dataUsingEncoding:", 4LL);
      v17 = objc_retainAutoreleasedReturnValue(v16);
      _objc_msgSend(v17, "writeToFile:atomically:", v5, 1LL);
    }
  }
}

//----- (0000000100C559C7) ----------------------------------------------------
id __cdecl +[tools safeStringValue:](id a1, SEL a2, id a3)
{
  __CFString *v11; // r12
  __CFString *v12; // rdi
  __CFString *v13; // r13
  NSString *v15; // rax
  __CFString *v16; // rbx
  __CFString *v20; // r14

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3)
    || (v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class"), (unsigned __int8)_objc_msgSend(
                                                                                    v7,
                                                                                    "isKindOfClass:",
                                                                                    v6)) )
  {
  }
  else if ( !v5 )
  {
    v13 = &charsToLeaveEscaped;
    goto LABEL_7;
  }
  v8 = _objc_msgSend(&OBJC_CLASS___NSNull, "null");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v12 = &charsToLeaveEscaped;
  if ( !(unsigned __int8)_objc_msgSend(v10, "isEqual:", v9) )
    v12 = v11;
  v13 = objc_retain(v12);
LABEL_7:
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v13, "isKindOfClass:", v14) )
  {
    _objc_msgSend(v13, "doubleValue");
    v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"));
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v13 = v16;
  }
  v17 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v13, "isKindOfClass:", v17) )
  {
    v18 = _objc_msgSend(v13, "stringByReplacingOccurrencesOfString:withString:", CFSTR("\\r\\n"), CFSTR("\r\n"));
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v13 = (__CFString *)objc_retain(v19);
    v20 = v13;
  }
  else
  {
    v20 = &charsToLeaveEscaped;
  }
  return objc_autoreleaseReturnValue(v20);
}

//----- (0000000100C55BDC) ----------------------------------------------------
id __cdecl +[tools getZjzh](id a1, SEL a2)
{
  __CFString *v9; // rax
  __CFString *v10; // rbx
  __CFString *v11; // rdi
  __CFString *v12; // r15

  v2 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getActiveUser");
  objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v6 = _objc_msgSend(v5, "getUserInfo");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "strZjzh");
    v9 = (__CFString *)objc_retainAutoreleasedReturnValue(v8);
    v10 = v9;
    v11 = &charsToLeaveEscaped;
    if ( v9 )
      v11 = v9;
    v12 = objc_retain(v11);
  }
  else
  {
    v12 = &charsToLeaveEscaped;
  }
  return objc_autoreleaseReturnValue(v12);
}

//----- (0000000100C55CE5) ----------------------------------------------------
id __cdecl +[tools getQsid](id a1, SEL a2)
{
  __CFString *v7; // rax
  __CFString *v8; // rbx
  __CFString *v9; // rdi
  __CFString *v10; // r15

  v2 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getActiveUser");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v6 = _objc_msgSend(v5, "getQSID");
    v7 = (__CFString *)objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    v9 = &charsToLeaveEscaped;
    if ( v7 )
      v9 = v7;
    v10 = objc_retain(v9);
  }
  else
  {
    v10 = &charsToLeaveEscaped;
  }
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100C55DB3) ----------------------------------------------------
char __cdecl +[tools isHtmlString:](id a1, SEL a2, id a3)
{
  NSArray *v5; // rax
  NSArray *v6; // rax
  id obj; // [rsp+48h] [rbp-E8h]
  _QWORD v21[6]; // [rsp+D0h] [rbp-60h] BYREF

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "length") )
  {
    v21[0] = CFSTR("<html");
    v21[1] = CFSTR("<br>");
    v21[2] = CFSTR("<a href");
    v21[3] = CFSTR("</a>");
    v21[4] = CFSTR("<p");
    v21[5] = CFSTR("</p>");
    v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v21, 6LL);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    v18 = 0LL;
    obj = objc_retain(v6);
    v7 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v20, 16LL);
    if ( v7 )
    {
      v8 = v4;
      v9 = *(_QWORD *)v16;
LABEL_5:
      v10 = "rangeOfString:";
      v11 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v16 != v9 )
          objc_enumerationMutation(obj);
        if ( _objc_msgSend(v8, v10, *(_QWORD *)(*((_QWORD *)&v15 + 1) + 8 * v11)) != (id)0x7FFFFFFFFFFFFFFFLL )
          break;
        if ( v7 == (id)++v11 )
        {
          v7 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v20, 16LL);
          if ( v7 )
            goto LABEL_5;
          break;
        }
      }
      v4 = v8;
    }
    v12 = obj;
  }
  return v13;
}

//----- (0000000100C56008) ----------------------------------------------------
int __fastcall sub_100C56008(_QWORD *a1)
{
  return sub_100D002E8(*a1 - 24LL);
}

//----- (0000000100C5603A) ----------------------------------------------------
void __cdecl sub_100C5603A(id a1)
{
  NSDictionary *v5; // rax
  NSDictionary *v6; // rax

  v1 = _objc_msgSend(&OBJC_CLASS___NSBundle, "mainBundle");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v3 = _objc_msgSend(v2, "pathForResource:ofType:", CFSTR("MarketNumber"), CFSTR("plist"));
  objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithContentsOfFile:", v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = qword_1016D39F0;
  qword_1016D39F0 = v6;
}

//----- (0000000100C560F8) ----------------------------------------------------
int __fastcall sub_100C560F8(__int64 *a1)
{
  _QWORD *v1; // rbx
  _QWORD *v2; // r15

  v1 = (_QWORD *)*a1;
  v2 = (_QWORD *)a1[1];
  if ( (_QWORD *)*a1 != v2 )
  {
    do
      sub_100C56008(v1++);
    while ( v2 != v1 );
  }
  return sub_100C56134(a1);
}

//----- (0000000100C56134) ----------------------------------------------------
int __fastcall sub_100C56134(__int64 *a1)
{
  int result; // eax

  v1 = *a1;
  if ( v1 )
    return sub_100F6D770(v1, 0);
  return result;
}

//----- (0000000100C56151) ----------------------------------------------------
int __fastcall sub_100C56151(char *a1, _BYTE *a2, __int64 a3, char a4)
{
  _BYTE *v7; // rax

  v4 = a1;
  v11 = a3;
  v12 = a4;
  v5 = -1LL;
  while ( a2[++v5] != 0 )
    ;
  v7 = sub_100C562B5(a1, a2);
  if ( v7 )
  {
    do
    {
      *v7 = 0;
      v8 = &v7[v5];
      sub_100C56258((__int64)&v11, v4);
      v7 = sub_100C562B5(v8, a2);
      v4 = v8;
    }
    while ( v7 );
    v4 = (char *)(v5 + v9);
  }
  return sub_100C56258((__int64)&v11, v4);
}

//----- (0000000100C561E2) ----------------------------------------------------
_DWORD *__fastcall sub_100C561E2(unsigned __int64 a1, unsigned __int64 a2)
{
  unsigned __int64 v2; // rbx
  _DWORD *result; // rax

  if ( a1 > 0x3FFFFFFFFFFFFFF9LL )
    std::__throw_length_error("basic_string::_S_create");
  v2 = 2 * a2;
  if ( 2 * a2 <= a1 )
    v2 = a1;
  if ( a1 <= a2 )
    v2 = a1;
  if ( v2 > a2 && v2 + 57 >= 0x1001 )
  {
    v2 = v2 - (((_WORD)v2 + 57) & 0xFFF) + 4096;
    if ( v2 >= 0x3FFFFFFFFFFFFFF9LL )
      v2 = 0x3FFFFFFFFFFFFFF9LL;
  }
  result = sub_100F6D3E0((int)v2 + 25);
  *((_QWORD *)result + 1) = v2;
  result[4] = 0;
  return result;
}

//----- (0000000100C56258) ----------------------------------------------------
int __fastcall sub_100C56258(__int64 a1, char *a2)
{
  int result; // eax
  volatile signed __int32 *v4[3]; // [rsp+8h] [rbp-18h] BYREF

  if ( *(_BYTE *)(a1 + 8) || a2 && *a2 )
  {
    v2 = *(_QWORD *)a1;
    sub_100F9F0A0(v4, a2);
    sub_100C562F8(v2, v4);
    return sub_100C56008(v4);
  }
  return result;
}

//----- (0000000100C562B5) ----------------------------------------------------
_BYTE *__fastcall sub_100C562B5(_BYTE *a1, _BYTE *a2)
{
  _BYTE *result; // rax
  char i; // di

  result = a1;
  if ( *a2 )
  {
    for ( i = *a1; i; i = *++result )
    {
      v4 = 0LL;
      while ( 1 )
      {
        v5 = a2[v4];
        if ( !v5 || i != v5 )
          break;
        i = result[++v4];
        if ( !i )
        {
          v5 = a2[v4];
          break;
        }
      }
      if ( !v5 )
        return result;
    }
    return 0LL;
  }
  return result;
}

//----- (0000000100C562F8) ----------------------------------------------------
int __fastcall sub_100C562F8(__int64 a1, volatile signed __int32 **a2)
{
  volatile signed __int32 **v3; // rsi
  int result; // eax

  v3 = *(volatile signed __int32 ***)(a1 + 8);
  if ( v3 == *(volatile signed __int32 ***)(a1 + 16) )
    return sub_100D38260((__int64 *)a1, v3);
  result = (unsigned int)sub_100F9EE40(*(volatile signed __int32 ***)(a1 + 8), a2);
  *(_QWORD *)(a1 + 8) += 8LL;
  return result;
}

//----- (0000000100C56338) ----------------------------------------------------
volatile signed __int32 **__fastcall sub_100C56338(volatile signed __int32 **a1, volatile signed __int32 **a2)
{
  volatile signed __int32 *v3; // rbx
  volatile signed __int32 *v4; // rdi

  v3 = *a2;
  if ( *a1 != *a2 )
  {
    v4 = v3 - 6;
    if ( *((int *)v3 - 2) < 0 )
    {
      v3 = (volatile signed __int32 *)sub_100D79A50(v4, (__int64)v6, 0LL);
    }
    else if ( v4 != (volatile signed __int32 *)&std::basic_string<char,std::char_traits<char>,baratol::tlallocator<char>>::_Rep::_S_empty_rep_storage )
    {
      _InterlockedIncrement(v3 - 2);
    }
    sub_100D002E8((__int64)(*a1 - 6));
    *a1 = v3;
  }
  return a1;
}

