void __cdecl -[ZiXunWKWebViewController viewDidLoad](ZiXunWKWebViewController *self, SEL a2)
{
  NSString *v4; // rax
  NSString *v5; // rbx
  WKWebView *v7; // rax
  WKWebView *v8; // r15

  v15.receiver = self;
  v15.super_class = (Class)&OBJC_CLASS___ZiXunWKWebViewController;
  -[HXBaseWKWebViewController viewDidLoad](&v15, "viewDidLoad");
  v2 = +[HXThemeManager themeColorString](&OBJC_CLASS___HXThemeManager, "themeColorString");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("document.cookie='cnc1=%@';"), v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v16 = v5;
  v6 = objc_alloc(&OBJC_CLASS___WKUserScript);
  _objc_msgSend(v6, "initWithSource:injectionTime:forMainFrameOnly:", v5, 0LL, 0LL);
  v7 = (WKWebView *)-[HXBaseWKWebViewController webView](self, "webView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "configuration");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "userContentController");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "addUserScript:", v13);
}

//----- (000000010028C52D) ----------------------------------------------------
void __cdecl -[ZiXunWKWebViewController viewDidChangeEffectiveAppearance](ZiXunWKWebViewController *self, SEL a2)
{
  WKWebView *v2; // rax
  WKWebView *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12

  v2 = -[HXBaseWKWebViewController webView](self, "webView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "configuration");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "userContentController");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v9, "removeAllUserScripts");
  v12 = v11(&OBJC_CLASS___HXThemeManager, "themeColorString");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v15 = v14(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("document.cookie='cnc1=%@';"), v13);
  v34 = objc_retainAutoreleasedReturnValue(v15);
  v16 = objc_alloc(&OBJC_CLASS___WKUserScript);
  v18 = v17(v16, "initWithSource:injectionTime:forMainFrameOnly:", v34, 0LL, 0LL);
  v20 = v19(self, "webView");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v23 = v22(v21, "configuration");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v26 = v25(v24, "userContentController");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28(v27, "addUserScript:", v18);
  v30 = v29(self, "webView");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  v33 = v32(v31, "reload");
  objc_unsafeClaimAutoreleasedReturnValue(v33);
}

