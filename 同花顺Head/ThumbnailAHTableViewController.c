void __cdecl -[ThumbnailAHTableViewController viewDidLoad](ThumbnailAHTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ThumbnailAHTableViewController;
  -[ThumbnailBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[ThumbnailAHTableViewController registerNotificationObserver](self, "registerNotificationObserver");
}

//----- (0000000100022266) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController dealloc](ThumbnailAHTableViewController *self, SEL a2)
{

  -[ThumbnailAHTableViewController invalidateNoficationObserver](self, "invalidateNoficationObserver");
  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ThumbnailAHTableViewController;
  -[ThumbnailBaseTableViewController dealloc](&v2, "dealloc");
}

//----- (00000001000222A4) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController doInitialOperation](ThumbnailAHTableViewController *self, SEL a2)
{
  -[ThumbnailBaseTableViewController setIsRequestFromZero:](self, "setIsRequestFromZero:", 1LL);
  -[ThumbnailAHTableViewController setIsNeedToOrderHK:](self, "setIsNeedToOrderHK:", 0LL);
}

//----- (00000001000222D9) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController updateTableVCData:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  signed int v15; // eax

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    v5 = _objc_msgSend(v3, "objectForKeyedSubscript:", CFSTR("requesttype"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "integerValue");
    _objc_msgSend(v8, "setRequestType:", v7);
    v9 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("TableID"));
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "longValue");
    _objc_msgSend(v12, "setTableID:", v11);
    v13 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("blockid"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned int)_objc_msgSend(v14, "intValue");
    _objc_msgSend(v16, "setBlockID:", v15);
    v17 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("sortid"));
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = _objc_msgSend(v18, "longValue");
    _objc_msgSend(v20, "setSortID:", v19);
    v21 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("sortorder"));
    v23 = objc_retainAutoreleasedReturnValue(v21);
    if ( v23 )
    {
      v24 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("sortorder"));
      v25 = objc_retainAutoreleasedReturnValue(v24);
      _objc_msgSend(v26, "setSortOrder:", v25);
    }
    else
    {
      _objc_msgSend(v22, "setSortOrder:", CFSTR("D"));
    }
    v27 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("SelectedCode"));
    v28 = objc_retainAutoreleasedReturnValue(v27);
    _objc_msgSend(v29, "setSelectedCode:", v28);
    v30 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("sortbegin"));
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v32 = _objc_msgSend(v31, "integerValue");
    _objc_msgSend(v33, "setQuotaBegin:", v32);
    v34 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("sortcount"));
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v36 = _objc_msgSend(v35, "integerValue");
    _objc_msgSend(v37, "setQuotaCount:", v36);
    v38 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("Index"));
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = _objc_msgSend(v39, "integerValue");
    _objc_msgSend(v41, "setQuotaIndex:", v40);
    v42 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("totalnumber"));
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v44 = _objc_msgSend(v43, "integerValue");
    _objc_msgSend(v45, "setAllCodesNum:", v44);
    _objc_msgSend(v46, "setIsTableSwitched:", 1LL);
    _objc_msgSend(v47, "setIsFoucsOfSuperController:", 1LL);
    v49 = (unsigned __int8)_objc_msgSend(v48, "doNeedsToOrderHKDataItems");
    _objc_msgSend(v50, "setIsNeedToOrderHK:", v49 != 0);
  }
}

//----- (000000010002262B) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController requestForMyTable](ThumbnailAHTableViewController *self, SEL a2)
{
  if ( !(unsigned __int8)-[ThumbnailBaseTableViewController invalidRequestWhenViewWillBeRemoved](
                           self,
                           "invalidRequestWhenViewWillBeRemoved") )
  {
    -[ThumbnailAHTableViewController deleteOrder](self, "deleteOrder");
    if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
      -[ThumbnailAHTableViewController requestForAHQuotationDatas](self, "requestForAHQuotationDatas");
  }
}

//----- (0000000100022684) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController requestForAHQuotationDatas](ThumbnailAHTableViewController *self, SEL a2)
{
  -[ThumbnailAHTableViewController sendRequestForAssociateCodes](self, "sendRequestForAssociateCodes");
  -[ThumbnailAHTableViewController sendRequestForExchangeRate](self, "sendRequestForExchangeRate");
}

//----- (00000001000226B2) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController sendRequestForAssociateCodes](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r15
  id (*v4)(id, SEL, ...); // r12

  v2 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("id=101&instance=%ld&zipversion=3&downloadcrc=0&reqid=2000"),
         1110LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "writeTextData:withTimeout:delegate:instance:", v3, self, 1110LL, 10.0);
}

//----- (000000010002274B) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController sendRequestForExchangeRate](ThumbnailAHTableViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSArray *v4; // rax
  NSArray *v5; // r14
  HXSocketCenter *v10; // rax
  HXSocketCenter *v11; // rbx
  NSNumber *v14; // [rsp+10h] [rbp-50h]
  NSNumber *v15; // [rsp+18h] [rbp-48h]
  _QWORD v16[2]; // [rsp+20h] [rbp-40h] BYREF

  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v14 = objc_retainAutoreleasedReturnValue(v2);
  v16[0] = v14;
  v3 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 981LL);
  v15 = objc_retainAutoreleasedReturnValue(v3);
  v16[1] = v15;
  v4 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v16, 2LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "componentsJoinedByString:", CFSTR(","));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(
         v8,
         "stringWithFormat:",
         CFSTR("id=1&instance=%ld&zipversion=3&codelist=%@&market=%@&datatype=%@"),
         1111LL,
         CFSTR("1A0001"),
         CFSTR("USHI"),
         v7);
  objc_retainAutoreleasedReturnValue(v9);
  v10 = (HXSocketCenter *)+[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
    v11,
    "writeTextData:withTimeout:delegate:instance:",
    v12,
    self,
    1111LL,
    10.0);
}

//----- (00000001000228DA) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController sendRequestForHuShenQuote](ThumbnailAHTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // r15
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // r13
  NSNumber *v10; // rax
  NSNumber *v11; // r15
  NSArray *v12; // rax
  NSArray *v13; // rbx
  NSDictionary *v14; // rax
  HXTableRequestModule *v16; // rax
  HXTableRequestModule *v17; // r14
  NSDictionary *v18; // rbx
  id *v19; // r12
  _QWORD v20[4]; // [rsp+0h] [rbp-D0h] BYREF
  id to[2]; // [rsp+20h] [rbp-B0h] BYREF
  NSDictionary *v24; // [rsp+40h] [rbp-90h]
  id location; // [rsp+50h] [rbp-80h] BYREF
  _QWORD v27[5]; // [rsp+58h] [rbp-78h] BYREF
  _QWORD v28[2]; // [rsp+80h] [rbp-50h] BYREF
  _QWORD v29[2]; // [rsp+90h] [rbp-40h] BYREF

  to[1] = self;
  v2 = -[ThumbnailAHTableViewController huShenCodes](self, "huShenCodes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[ThumbnailAHTableViewController getFilterMarketsAndCodes:](self, "getFilterMarketsAndCodes:", v3);
  v25 = objc_retainAutoreleasedReturnValue(v4);
  v28[0] = CFSTR("datatype");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v22 = objc_retainAutoreleasedReturnValue(v5);
  v27[0] = v22;
  v6 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
  v23 = objc_retainAutoreleasedReturnValue(v6);
  v27[1] = v23;
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v27[2] = objc_retainAutoreleasedReturnValue(v7);
  v8 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v27[3] = v9;
  v10 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v27[4] = v11;
  v12 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v27, 5LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v29[0] = v13;
  v28[1] = CFSTR("StockCodesAndMarket");
  v29[1] = v25;
  v14 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, v28, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v14);
  objc_initWeak(&location, self);
  v16 = (HXTableRequestModule *)-[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
  v17 = objc_retain(v16);
  v20[0] = _NSConcreteStackBlock;
  v20[1] = 3254779904LL;
  v20[2] = sub_100022BAD;
  v20[3] = &unk_1012DAED8;
  objc_copyWeak(to, &location);
  v18 = v24;
  -[HXTableRequestModule request:params:callBack:](v17, "request:params:callBack:", 4LL, v24, v20);
  objc_destroyWeak(v19);
  objc_destroyWeak(&location);
}

//----- (0000000100022BAD) ----------------------------------------------------
void __fastcall sub_100022BAD(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // r15
  id *v7; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setMainStockTableViewDataArray:", v2);
  v8 = objc_loadWeakRetained(v7);
  _objc_msgSend(v8, "sendRequestForHongKongQuote");
}

//----- (0000000100022C54) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController sendRequestForHongKongQuote](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rax
  NSNumber *v7; // rax
  NSArray *v15; // rax
  NSArray *v16; // rbx
  HXSocketCenter *v21; // rax
  HXSocketCenter *v22; // rbx
  NSNumber *v26; // [rsp+18h] [rbp-78h]
  _QWORD v30[4]; // [rsp+40h] [rbp-50h] BYREF

  v2 = -[ThumbnailAHTableViewController hkCodes](self, "hkCodes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = +[ThumbnailUtils getStockCodes:](&OBJC_CLASS___ThumbnailUtils, "getStockCodes:", v3);
  v25 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v25, "componentsJoinedByString:", CFSTR(","));
  v27 = objc_retainAutoreleasedReturnValue(v6);
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v26 = objc_retainAutoreleasedReturnValue(v7);
  v30[0] = v26;
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, v8, 10LL);
  v28 = objc_retainAutoreleasedReturnValue(v9);
  v30[1] = v28;
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, v10, 6LL);
  v29 = objc_retainAutoreleasedReturnValue(v11);
  v30[2] = v29;
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, v12, 13LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v30[3] = v14;
  v15 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v30, 4LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17 = _objc_msgSend(v16, "componentsJoinedByString:", CFSTR(","));
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v20 = (void *)v19(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("id=1&instance=%ld&zipversion=3&codelist=%@&market=%@&datatype=%@"),
                  1114LL,
                  v27,
                  CFSTR("UHKM"),
                  v18);
  objc_retainAutoreleasedReturnValue(v20);
  v21 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
    v22,
    "writeTextData:withTimeout:delegate:instance:",
    v23,
    self,
    1114LL,
    10.0);
}

//----- (0000000100022EB3) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController order](ThumbnailAHTableViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    v2 = _objc_msgSend(self, "view");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v5 = v4(v3, "superview");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    if ( v6 )
    {
      v7(self, "getOrderCodeList");
      v8(self, "orderAHHuShen");
      if ( LOBYTE(self->super._count) )
        -[ThumbnailAHTableViewController orderAHHK](self, "orderAHHK");
    }
  }
}

//----- (0000000100022F7D) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController deleteOrder](ThumbnailAHTableViewController *self, SEL a2)
{
  -[ThumbnailAHTableViewController deleteOrderAHHuShen](self, "deleteOrderAHHuShen");
  -[ThumbnailAHTableViewController deleteOrderAHHK](self, "deleteOrderAHHK");
}

//----- (0000000100022FAB) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController receiveStuffData:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  signed int v8; // eax
  id (*v10)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  unsigned int v25; // eax
  id (*v26)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXPCBaseDataModel, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v38 = v3;
    v6 = objc_retain(v3);
    v8 = (unsigned int)v7(v6, "instanceId");
    v9(self, "unregisterInMacDataServiceWithRequstInstanceId:", v8);
    v11 = v10(&OBJC_CLASS___PCTXTDataModel, "class");
    if ( (unsigned __int8)v12(v6, "isKindOfClass:", v11) )
    {
      v14 = objc_retain(v6);
      if ( (unsigned int)_objc_msgSend(v14, "instanceId") == 1110 )
      {
        v16 = v15(v14, "txtData");
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v19 = v18(self, "parserData:", v17);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        if ( (unsigned __int8)v21(self, "filterCodesSuccessFromString:", v20) )
          -[ThumbnailAHTableViewController sendRequestForHuShenQuote](self, "sendRequestForHuShenQuote");
      }
    }
    else
    {
      v22 = v13(&OBJC_CLASS___PCDBFileDataModel, "class");
      if ( !(unsigned __int8)v23(v6, "isKindOfClass:", v22) )
      {
LABEL_13:
        v3 = v38;
        goto LABEL_14;
      }
      v24 = objc_retain(v6);
      v25 = (unsigned int)_objc_msgSend(v24, "instanceId");
      if ( v25 == 1111 )
      {
        v31 = v26(v24, "arrBody");
        v32 = objc_retainAutoreleasedReturnValue(v31);
        v34 = v33(v32, "firstObject");
        v35 = objc_retainAutoreleasedReturnValue(v34);
        v36(self, "saveExchangeRate:", v35);
        v37(v32);
      }
      else if ( v25 == 1114 )
      {
        v27 = v26(v24, "arrBody");
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v29(self, "dealWithReceivedHongKongQuotes:", v28);
        v30(self, "executeAfterGangGuTableViewDataAlready");
      }
    }
    goto LABEL_13;
  }
LABEL_14:
}

//----- (0000000100023203) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController failToReceiveStuffData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  -[ThumbnailAHTableViewController unregisterInMacDataServiceWithRequstInstanceId:](
    self,
    "unregisterInMacDataServiceWithRequstInstanceId:",
    a3);
}

//----- (0000000100023215) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController unregisterInMacDataServiceWithRequstInstanceId:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXSocketCenter *v4; // rax
  HXSocketCenter *v5; // rbx

  v4 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(v5, "removeObjectFromMapTable:forInstance:", self, a3);
}

//----- (000000010002326D) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController dealWithAHHuShenPushData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  HXStockModel *v4; // rax
  HXStockModel *v5; // r14
  NSArray *v6; // rax
  NSArray *v7; // rax
  NSNumber *v33; // rax
  NSNumber *v34; // r15
  NSMutableArray *v39; // rax
  NSMutableArray *v40; // rax
  HXStockModel *v51; // rax
  signed __int64 v54; // rax
  HXStockModel *v62; // rax
  HXStockModel *v63; // rax
  NSArray *v64; // rax
  NSArray *v65; // rbx
  NSMutableArray *v66; // rax
  NSMutableArray *v67; // r15
  signed __int64 v69; // rax
  NSNumber *v90; // rax
  NSNumber *v102; // [rsp+58h] [rbp-58h]

  v3 = objc_retain(a3);
  v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXStockModel mainStockTableViewDataArray](v5, "mainStockTableViewDataArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = (unsigned __int8)_objc_msgSend(v7, "isEqual:", __NSArray0__);
  if ( !v8 )
  {
    v100 = v3;
    if ( _objc_msgSend(v3, "count") )
    {
      v103 = 0LL;
      do
      {
        v13 = _objc_msgSend(v12, "orderCodeMArray");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(v14, "count");
        if ( v15 )
        {
          v110 = 0LL;
          while ( 1 )
          {
            v16 = _objc_msgSend(v3, "objectAtIndexedSubscript:", v103);
            v107 = objc_retainAutoreleasedReturnValue(v16);
            v18 = _objc_msgSend(v17, "orderCodeMArray");
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v20 = _objc_msgSend(v19, "objectAtIndexedSubscript:", v110);
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v23 = _objc_msgSend(v21, "thsStringForKey:", CFSTR("Market"));
            v24 = objc_retainAutoreleasedReturnValue(v23);
            v101 = v21;
            v25 = v21;
            v27 = v26;
            v28 = _objc_msgSend(v25, v26, CFSTR("StockCode"));
            v29 = objc_retainAutoreleasedReturnValue(v28);
            v30 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
                    &OBJC_CLASS___HXTools,
                    "getMarketAndCodeByAppendingMarket:andStockCode:",
                    v24,
                    v29);
            v31 = objc_retainAutoreleasedReturnValue(v30);
            v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v34 = objc_retainAutoreleasedReturnValue(v33);
            v35 = _objc_msgSend(v107, v27, v34);
            v36 = objc_retainAutoreleasedReturnValue(v35);
            v37 = (unsigned __int8)_objc_msgSend(v36, "isEqualToString:", v31);
            if ( v37 )
              break;
            v39 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
            v40 = objc_retainAutoreleasedReturnValue(v39);
            v41 = (char *)_objc_msgSend(v40, "count");
            ++v110;
            v3 = v100;
            if ( v41 <= v110 )
              goto LABEL_13;
          }
          v43 = (char *)-[ThumbnailBaseTableViewController begin](self, "begin") + (_QWORD)v110;
          v45 = _objc_msgSend(v44, "stockModel");
          v46 = objc_retainAutoreleasedReturnValue(v45);
          v47 = _objc_msgSend(v46, "mainStockTableViewDataArray");
          v48 = objc_retainAutoreleasedReturnValue(v47);
          v49 = (char *)_objc_msgSend(v48, "count");
          if ( v43 >= v49 )
          {
          }
          else
          {
            v51 = -[HXBaseTableViewController stockModel](self, "stockModel");
            v105 = objc_retainAutoreleasedReturnValue(v51);
            v52 = _objc_msgSend(v105, "mainStockTableViewDataArray");
            v53 = objc_retainAutoreleasedReturnValue(v52);
            v54 = -[ThumbnailBaseTableViewController begin](self, "begin");
            v55 = _objc_msgSend(v53, "objectAtIndexedSubscript:", &v110[v54]);
            v56 = objc_retainAutoreleasedReturnValue(v55);
            v58 = _objc_msgSend(v57, "dictionaryWithDictionary:", v56);
            objc_retainAutoreleasedReturnValue(v58);
            v60 = -[ThumbnailAHTableViewController updateAGuData:SourceData:](
                    self,
                    "updateAGuData:SourceData:",
                    v107,
                    v59);
            v106 = (char *)objc_retainAutoreleasedReturnValue(v60);
            v62 = -[HXBaseTableViewController stockModel](self, "stockModel");
            v63 = objc_retainAutoreleasedReturnValue(v62);
            v64 = -[HXStockModel mainStockTableViewDataArray](v63, "mainStockTableViewDataArray");
            v65 = objc_retainAutoreleasedReturnValue(v64);
            v66 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v65);
            v67 = objc_retainAutoreleasedReturnValue(v66);
            v69 = -[ThumbnailBaseTableViewController begin](self, "begin");
            _objc_msgSend(v67, "replaceObjectAtIndex:withObject:", &v110[v69], v106);
            v71 = _objc_msgSend(v70, "stockModel");
            v72 = objc_retainAutoreleasedReturnValue(v71);
            _objc_msgSend(v72, "setMainStockTableViewDataArray:", v67);
          }
          v3 = v100;
        }
LABEL_13:
        v73 = (char *)_objc_msgSend(v3, "count");
        ++v103;
        v11 = "orderCodeMArray";
      }
      while ( v73 > v103 );
    }
    v74 = _objc_msgSend(v12, "stockModel", v10, v11);
    v111 = objc_retainAutoreleasedReturnValue(v74);
    v75 = _objc_msgSend(v111, "mainStockTableViewDataArray");
    v76 = objc_retainAutoreleasedReturnValue(v75);
    v78 = _objc_msgSend(v77, "sortID");
    v80 = _objc_msgSend(v79, "preprocessOriginalData:DataItemID:", v76, v78);
    v81 = objc_retainAutoreleasedReturnValue(v80);
    v83 = _objc_msgSend(v82, "stockModel");
    v84 = objc_retainAutoreleasedReturnValue(v83);
    _objc_msgSend(v84, "setMainStockTableViewDataArray:", v81);
    v86 = _objc_msgSend(v85, "stockModel");
    v112 = objc_retainAutoreleasedReturnValue(v86);
    v87 = _objc_msgSend(v112, "mainStockTableViewDataArray");
    v108 = objc_retainAutoreleasedReturnValue(v87);
    v89 = _objc_msgSend(v88, "sortID");
    v90 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v89);
    v102 = objc_retainAutoreleasedReturnValue(v90);
    v92 = _objc_msgSend(v91, "sortOrder");
    v104 = objc_retainAutoreleasedReturnValue(v92);
    v93 = +[ThumbnailUtils sortCodes:bySortID:bySortOrder:](
            &OBJC_CLASS___ThumbnailUtils,
            "sortCodes:bySortID:bySortOrder:",
            v108,
            v102,
            v104);
    v94 = objc_retainAutoreleasedReturnValue(v93);
    v96 = _objc_msgSend(v95, "stockModel");
    v97 = objc_retainAutoreleasedReturnValue(v96);
    _objc_msgSend(v97, "setMainStockTableViewDataArray:", v94);
    _objc_msgSend(v98, "getOrderCodeList");
    _objc_msgSend(v99, "reloadPushDataForMyTable");
    v3 = v100;
  }
}

//----- (00000001000239ED) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController dealWithAHHKPushData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  HXStockModel *v4; // rax
  HXStockModel *v5; // r14
  NSArray *v6; // rax
  NSArray *v7; // rax
  NSNumber *v33; // rax
  NSNumber *v34; // r15
  NSMutableArray *v39; // rax
  NSMutableArray *v40; // rax
  HXStockModel *v51; // rax
  signed __int64 v54; // rax
  HXStockModel *v62; // rax
  HXStockModel *v63; // rax
  NSArray *v64; // rax
  NSArray *v65; // rbx
  NSMutableArray *v66; // rax
  NSMutableArray *v67; // r15
  signed __int64 v69; // rax
  NSNumber *v90; // rax
  NSNumber *v102; // [rsp+58h] [rbp-58h]

  v3 = objc_retain(a3);
  v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXStockModel mainStockTableViewDataArray](v5, "mainStockTableViewDataArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = (unsigned __int8)_objc_msgSend(v7, "isEqual:", __NSArray0__);
  if ( !v8 )
  {
    v100 = v3;
    if ( _objc_msgSend(v3, "count") )
    {
      v103 = 0LL;
      do
      {
        v13 = _objc_msgSend(v12, "orderCodeMArrayAHHK");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(v14, "count");
        if ( v15 )
        {
          v110 = 0LL;
          while ( 1 )
          {
            v16 = _objc_msgSend(v3, "objectAtIndexedSubscript:", v103);
            v107 = objc_retainAutoreleasedReturnValue(v16);
            v18 = _objc_msgSend(v17, "orderCodeMArrayAHHK");
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v20 = _objc_msgSend(v19, "objectAtIndexedSubscript:", v110);
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v23 = _objc_msgSend(v21, "thsStringForKey:", CFSTR("Market"));
            v24 = objc_retainAutoreleasedReturnValue(v23);
            v101 = v21;
            v25 = v21;
            v27 = v26;
            v28 = _objc_msgSend(v25, v26, CFSTR("StockCode"));
            v29 = objc_retainAutoreleasedReturnValue(v28);
            v30 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
                    &OBJC_CLASS___HXTools,
                    "getMarketAndCodeByAppendingMarket:andStockCode:",
                    v24,
                    v29);
            v31 = objc_retainAutoreleasedReturnValue(v30);
            v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v34 = objc_retainAutoreleasedReturnValue(v33);
            v35 = _objc_msgSend(v107, v27, v34);
            v36 = objc_retainAutoreleasedReturnValue(v35);
            v37 = (unsigned __int8)_objc_msgSend(v36, "isEqualToString:", v31);
            if ( v37 )
              break;
            v39 = -[ThumbnailAHTableViewController orderCodeMArrayAHHK](self, "orderCodeMArrayAHHK");
            v40 = objc_retainAutoreleasedReturnValue(v39);
            v41 = (char *)_objc_msgSend(v40, "count");
            ++v110;
            v3 = v100;
            if ( v41 <= v110 )
              goto LABEL_13;
          }
          v43 = (char *)-[ThumbnailBaseTableViewController begin](self, "begin") + (_QWORD)v110;
          v45 = _objc_msgSend(v44, "stockModel");
          v46 = objc_retainAutoreleasedReturnValue(v45);
          v47 = _objc_msgSend(v46, "mainStockTableViewDataArray");
          v48 = objc_retainAutoreleasedReturnValue(v47);
          v49 = (char *)_objc_msgSend(v48, "count");
          if ( v43 >= v49 )
          {
          }
          else
          {
            v51 = -[HXBaseTableViewController stockModel](self, "stockModel");
            v105 = objc_retainAutoreleasedReturnValue(v51);
            v52 = _objc_msgSend(v105, "mainStockTableViewDataArray");
            v53 = objc_retainAutoreleasedReturnValue(v52);
            v54 = -[ThumbnailBaseTableViewController begin](self, "begin");
            v55 = _objc_msgSend(v53, "objectAtIndexedSubscript:", &v110[v54]);
            v56 = objc_retainAutoreleasedReturnValue(v55);
            v58 = _objc_msgSend(v57, "dictionaryWithDictionary:", v56);
            objc_retainAutoreleasedReturnValue(v58);
            v60 = -[ThumbnailAHTableViewController updateGangGuData:SourceData:](
                    self,
                    "updateGangGuData:SourceData:",
                    v107,
                    v59);
            v106 = (char *)objc_retainAutoreleasedReturnValue(v60);
            v62 = -[HXBaseTableViewController stockModel](self, "stockModel");
            v63 = objc_retainAutoreleasedReturnValue(v62);
            v64 = -[HXStockModel mainStockTableViewDataArray](v63, "mainStockTableViewDataArray");
            v65 = objc_retainAutoreleasedReturnValue(v64);
            v66 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v65);
            v67 = objc_retainAutoreleasedReturnValue(v66);
            v69 = -[ThumbnailBaseTableViewController begin](self, "begin");
            _objc_msgSend(v67, "replaceObjectAtIndex:withObject:", &v110[v69], v106);
            v71 = _objc_msgSend(v70, "stockModel");
            v72 = objc_retainAutoreleasedReturnValue(v71);
            _objc_msgSend(v72, "setMainStockTableViewDataArray:", v67);
          }
          v3 = v100;
        }
LABEL_13:
        v73 = (char *)_objc_msgSend(v3, "count");
        ++v103;
        v11 = "orderCodeMArrayAHHK";
      }
      while ( v73 > v103 );
    }
    v74 = _objc_msgSend(v12, "stockModel", v10, v11);
    v111 = objc_retainAutoreleasedReturnValue(v74);
    v75 = _objc_msgSend(v111, "mainStockTableViewDataArray");
    v76 = objc_retainAutoreleasedReturnValue(v75);
    v78 = _objc_msgSend(v77, "sortID");
    v80 = _objc_msgSend(v79, "preprocessOriginalData:DataItemID:", v76, v78);
    v81 = objc_retainAutoreleasedReturnValue(v80);
    v83 = _objc_msgSend(v82, "stockModel");
    v84 = objc_retainAutoreleasedReturnValue(v83);
    _objc_msgSend(v84, "setMainStockTableViewDataArray:", v81);
    v86 = _objc_msgSend(v85, "stockModel");
    v112 = objc_retainAutoreleasedReturnValue(v86);
    v87 = _objc_msgSend(v112, "mainStockTableViewDataArray");
    v108 = objc_retainAutoreleasedReturnValue(v87);
    v89 = _objc_msgSend(v88, "sortID");
    v90 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v89);
    v102 = objc_retainAutoreleasedReturnValue(v90);
    v92 = _objc_msgSend(v91, "sortOrder");
    v104 = objc_retainAutoreleasedReturnValue(v92);
    v93 = +[ThumbnailUtils sortCodes:bySortID:bySortOrder:](
            &OBJC_CLASS___ThumbnailUtils,
            "sortCodes:bySortID:bySortOrder:",
            v108,
            v102,
            v104);
    v94 = objc_retainAutoreleasedReturnValue(v93);
    v96 = _objc_msgSend(v95, "stockModel");
    v97 = objc_retainAutoreleasedReturnValue(v96);
    _objc_msgSend(v97, "setMainStockTableViewDataArray:", v94);
    _objc_msgSend(v98, "getOrderCodeList");
    _objc_msgSend(v99, "reloadPushDataForMyTable");
    v3 = v100;
  }
}

//----- (000000010002416D) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController scrollViewWillStartScroll:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  -[ThumbnailAHTableViewController deleteOrder](self, "deleteOrder", a3);
}

//----- (000000010002417F) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController scrollViewDidEndScroll:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  -[ThumbnailAHTableViewController order](self, "order", a3);
}

//----- (0000000100024191) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController tableView:rowViewForRow:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{

  v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRowView_Highlight);
  v5 = _objc_msgSend(v4, "init");
  return objc_autoreleaseReturnValue(v5);
}

//----- (00000001000241BA) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController tableViewSelectionIsChanging:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v12 = v7;
  v8 = _objc_msgSend(v7, "selectedRowIndexs");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v6, "isEqualToIndexSet:", v9);
  if ( !v10 )
  {
    _objc_msgSend(v12, "setSelectedRowIndexs:", v6);
    v11 = _objc_msgSend(v6, "lastIndex");
    _objc_msgSend(v12, "actionForTalbeViewSeclectionDidChange:", v11);
    _objc_msgSend(v12, "setTableViewSelectionIsChanging:", 1LL);
  }
}

//----- (00000001000242A3) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController tableViewSelectionDidChange:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToIndexSet:", v8) )
  {
    -[HXBaseTableViewController tableViewSelectionIsChanging](self, "tableViewSelectionIsChanging");
    if ( v9 )
      goto LABEL_8;
  }
  else
  {
  }
  -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", v6);
  if ( (unsigned __int8)-[HXBaseTableViewController postSelectedRowDidChangedNotify](
                          self,
                          "postSelectedRowDidChangedNotify") )
  {
    v10 = _objc_msgSend(v6, "lastIndex");
    -[ThumbnailAHTableViewController actionForTalbeViewSeclectionDidChange:](
      self,
      "actionForTalbeViewSeclectionDidChange:",
      v10);
  }
  -[HXBaseTableViewController setPostSelectedRowDidChangedNotify:](self, "setPostSelectedRowDidChangedNotify:", 1LL);
LABEL_8:
  -[HXBaseTableViewController setTableViewSelectionIsChanging:](self, "setTableViewSelectionIsChanging:", 0LL);
}

//----- (00000001000243C8) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailAHTableViewController numberOfRowsInTableView:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  return (signed __int64)-[ThumbnailBaseTableViewController allCodesNum](self, "allCodesNum", a3);
}

//----- (00000001000243DA) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController tableView:viewForTableColumn:row:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXStockModel *v10; // rax
  HXStockModel *v11; // rbx
  NSArray *v12; // rax
  NSNumber *v26; // rax
  NSNumber *v27; // rbx
  NSNumber *v29; // rax
  NSNumber *v30; // rbx
  NSNumber *v32; // rax
  NSNumber *v33; // r14
  NSNumber *v38; // rax
  NSNumber *v39; // rbx
  NSNumber *v41; // rax
  NSNumber *v42; // rbx
  NSNumber *v44; // rax
  NSNumber *v45; // rax
  NSNumber *v46; // rax
  NSNumber *v47; // rax
  NSNumber *v48; // rax
  NSNumber *v49; // r13
  NSArray *v50; // rax
  NSArray *v51; // r14
  NSNumber *v52; // rax
  NSNumber *v53; // rbx
  HXStockModel *v58; // rax
  NSArray *v59; // rax
  NSNumber *v62; // rax
  NSNumber *v63; // rbx
  NSNumber *v64; // rax
  NSNumber *v65; // rbx
  TextMarkManager *v72; // rax
  TextMarkManager *v73; // rbx
  NSNumber *v111; // [rsp+8h] [rbp-E8h]
  NSNumber *v116; // [rsp+38h] [rbp-B8h]
  NSArray *v117; // [rsp+38h] [rbp-B8h]
  NSNumber *v118; // [rsp+40h] [rbp-B0h]
  NSNumber *v128; // [rsp+80h] [rbp-70h]
  HXStockModel *v133; // [rsp+90h] [rbp-60h]
  _QWORD v134[5]; // [rsp+98h] [rbp-58h] BYREF

  objc_retain(a3);
  v6 = _objc_msgSend(a4, "identifier");
  v110 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "makeViewWithIdentifier:owner:", v110, self);
  v120 = objc_retainAutoreleasedReturnValue(v8);
  v10 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = -[HXStockModel mainStockTableViewDataArray](v11, "mainStockTableViewDataArray");
  objc_retainAutoreleasedReturnValue(v12);
  if ( !v13 || !_objc_msgSend(v13, "count") )
  {
    v16 = v120;
    goto LABEL_6;
  }
  if ( (char *)_objc_msgSend(v14, "count") - 1 >= (char *)a5 )
  {
    v19 = _objc_msgSend(v15, "objectAtIndexedSubscript:");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    v16 = v120;
    if ( (unsigned __int8)_objc_msgSend(v20, "isKindOfClass:", v21)
      && (v23 = _objc_msgSend(&OBJC_CLASS___ThumbnailTableCellView, v22),
          (unsigned __int8)_objc_msgSend(v120, "isKindOfClass:", v23)) )
    {
      v125 = objc_retain(v120);
      if ( v125 )
      {
        v24 = v20;
        v25 = -[HXBaseTableViewController sortID](self, "sortID");
        v130 = +[ThumbnailUtils getNewSortIDForNormalTable:](
                 &OBJC_CLASS___ThumbnailUtils,
                 "getNewSortIDForNormalTable:",
                 v25);
        v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = _objc_msgSend(v24, "objectForKeyedSubscript:", v27);
        v127 = objc_retainAutoreleasedReturnValue(v28);
        v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
        v30 = objc_retainAutoreleasedReturnValue(v29);
        v31 = _objc_msgSend(v24, "objectForKeyedSubscript:", v30);
        v112 = objc_retainAutoreleasedReturnValue(v31);
        v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
        v33 = objc_retainAutoreleasedReturnValue(v32);
        v34 = _objc_msgSend(v24, "objectForKeyedSubscript:", v33);
        v35 = objc_retainAutoreleasedReturnValue(v34);
        v36 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v35);
        v114 = objc_retainAutoreleasedReturnValue(v36);
        v113 = v35;
        v37 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v35);
        v121 = objc_retainAutoreleasedReturnValue(v37);
        v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v39 = objc_retainAutoreleasedReturnValue(v38);
        v40 = _objc_msgSend(v24, "objectForKeyedSubscript:", v39);
        v115 = objc_retainAutoreleasedReturnValue(v40);
        v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v130);
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v122 = v24;
        v43 = _objc_msgSend(v24, "objectForKeyedSubscript:", v42);
        v132 = objc_retainAutoreleasedReturnValue(v43);
        v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 264648LL);
        v128 = objc_retainAutoreleasedReturnValue(v44);
        v134[0] = v128;
        v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
        v116 = objc_retainAutoreleasedReturnValue(v45);
        v134[1] = v116;
        v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 526792LL);
        v118 = objc_retainAutoreleasedReturnValue(v46);
        v134[2] = v118;
        v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 15LL);
        v111 = objc_retainAutoreleasedReturnValue(v47);
        v134[3] = v111;
        v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 9810LL);
        v49 = objc_retainAutoreleasedReturnValue(v48);
        v134[4] = v49;
        v50 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v134, 5LL);
        v51 = objc_retainAutoreleasedReturnValue(v50);
        v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v130);
        v53 = objc_retainAutoreleasedReturnValue(v52);
        v54 = (unsigned __int8)_objc_msgSend(v51, "containsObject:", v53);
        if ( v54 )
        {
          v55 = v122;
          v56 = +[ThumbnailUtils getSortItemDataWithSortID:dataDic:](
                  &OBJC_CLASS___ThumbnailUtils,
                  "getSortItemDataWithSortID:dataDic:",
                  v130,
                  v122);
          v57 = objc_retainAutoreleasedReturnValue(v56);
          if ( v57 && v132 != v57 )
          {
            v129 = v57;
            v119 = objc_retain(v57);
            v58 = -[HXBaseTableViewController stockModel](self, "stockModel");
            v133 = objc_retainAutoreleasedReturnValue(v58);
            v59 = -[HXStockModel mainStockTableViewDataArray](v133, "mainStockTableViewDataArray");
            v117 = objc_retainAutoreleasedReturnValue(v59);
            v60 = _objc_msgSend(v117, "objectAtIndexedSubscript:", a5);
            v61 = objc_retainAutoreleasedReturnValue(v60);
            v62 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v130);
            v63 = objc_retainAutoreleasedReturnValue(v62);
            _objc_msgSend(v61, "setObject:forKeyedSubscript:", v119, v63);
            v55 = v122;
            v57 = v129;
            v132 = v119;
          }
        }
        else
        {
          v55 = v122;
        }
        if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)100 )
        {
          v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 60LL);
          v65 = objc_retainAutoreleasedReturnValue(v64);
          v66 = _objc_msgSend(v55, "objectForKeyedSubscript:", v65);
          v67 = objc_retainAutoreleasedReturnValue(v66);
          v127 = v67;
        }
        v68 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
        v69 = objc_retainAutoreleasedReturnValue(v68);
        if ( (unsigned __int8)+[SelfStock isSelfStock:](&OBJC_CLASS___SelfStock, "isSelfStock:", v113) )
        {
          v70 = +[HXThemeManager markedTextColor](&OBJC_CLASS___HXThemeManager, "markedTextColor");
          v71 = objc_retainAutoreleasedReturnValue(v70);
          v69 = v71;
        }
        v72 = (TextMarkManager *)+[TextMarkManager sharedInstance](&OBJC_CLASS___TextMarkManager, "sharedInstance");
        v73 = objc_retainAutoreleasedReturnValue(v72);
        v74 = -[TextMarkManager getStockCodeColorIdx:](v73, "getStockCodeColorIdx:", v113);
        if ( v74 )
        {
          v75 = +[TextMarkMenuItemView getColorForDotsAtIndex:](
                  &OBJC_CLASS___TextMarkMenuItemView,
                  "getColorForDotsAtIndex:",
                  v74);
          v76 = objc_retainAutoreleasedReturnValue(v75);
          v69 = v76;
        }
        v77 = _objc_msgSend(v125, "stockName");
        v78 = objc_retainAutoreleasedReturnValue(v77);
        v126 = v69;
        _objc_msgSend(v78, "setTextColor:", v69);
        v79 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
        v80 = objc_retainAutoreleasedReturnValue(v79);
        v81 = _objc_msgSend(v125, "stockCode");
        v82 = objc_retainAutoreleasedReturnValue(v81);
        _objc_msgSend(v82, "setTextColor:", v80);
        v83 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                &OBJC_CLASS___ThumbnailUtils,
                "getSortItemColor:sortItem:zuoShou:",
                10LL,
                v115,
                v127);
        v84 = objc_retainAutoreleasedReturnValue(v83);
        v85 = _objc_msgSend(v125, "currentPrice");
        v86 = objc_retainAutoreleasedReturnValue(v85);
        _objc_msgSend(v86, "setTextColor:", v84);
        v87 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
                &OBJC_CLASS___ThumbnailUtils,
                "getSortItemColor:sortItem:zuoShou:",
                v130,
                v132,
                v127);
        v88 = objc_retainAutoreleasedReturnValue(v87);
        v89 = _objc_msgSend(v125, "priceChange");
        v90 = objc_retainAutoreleasedReturnValue(v89);
        _objc_msgSend(v90, "setTextColor:", v88);
        v91 = +[HXTools getPrecisionTypeWithMarket:Code:](
                &OBJC_CLASS___HXTools,
                "getPrecisionTypeWithMarket:Code:",
                v121,
                v114);
        v124 = ((double (__fastcall *)(__objc2_class *, const char *, id))_objc_msgSend)(
                 &OBJC_CLASS___HXTools,
                 "getVolumeUintWithMarket:",
                 v121);
        if ( v130 == (id)50 )
        {
          v92 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v132);
        }
        else
        {
          v93 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
          if ( !(unsigned __int8)_objc_msgSend(v132, "isKindOfClass:", v93) )
          {
            v131 = obj;
            v94 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v112);
            goto LABEL_29;
          }
          v92 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
                  &OBJC_CLASS___ThumbnailUtils,
                  "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
                  v132,
                  v130,
                  (unsigned int)v91,
                  v121,
                  v124);
        }
        v131 = (char *)objc_retainAutoreleasedReturnValue(v92);
        v94 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v112);
LABEL_29:
        v95 = objc_retainAutoreleasedReturnValue(v94);
        v96 = _objc_msgSend(v125, "stockName");
        v97 = objc_retainAutoreleasedReturnValue(v96);
        _objc_msgSend(v97, "setStringValue:", v95);
        v98 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v114);
        v99 = objc_retainAutoreleasedReturnValue(v98);
        v100 = _objc_msgSend(v125, "stockCode");
        v101 = objc_retainAutoreleasedReturnValue(v100);
        _objc_msgSend(v101, "setStringValue:", v99);
        v102 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
                 &OBJC_CLASS___ThumbnailUtils,
                 "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
                 v115,
                 10LL,
                 v91,
                 v121,
                 v124);
        v103 = objc_retainAutoreleasedReturnValue(v102);
        v104 = _objc_msgSend(v125, "currentPrice");
        v105 = objc_retainAutoreleasedReturnValue(v104);
        _objc_msgSend(v105, "setStringValue:", v103);
        v106 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v131);
        v107 = objc_retainAutoreleasedReturnValue(v106);
        v108 = _objc_msgSend(v125, "priceChange");
        v109 = objc_retainAutoreleasedReturnValue(v108);
        _objc_msgSend(v109, "setStringValue:", v107);
        v16 = v120;
        v20 = v122;
      }
    }
    else
    {
      objc_retain(v120);
    }
    goto LABEL_7;
  }
  v16 = v120;
  _objc_msgSend(v120, "clearAllDatas");
LABEL_6:
  objc_retain(v16);
LABEL_7:
  return objc_autoreleaseReturnValue(v16);
}

//----- (0000000100024FFF) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailAHTableViewController GangGuTableViewDataArray](
        ThumbnailAHTableViewController *self,
        SEL a2)
{

  quotaIndex = (void *)self->super._quotaIndex;
  if ( !quotaIndex )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (void *)self->super._quotaIndex;
    self->super._quotaIndex = (unsigned __int64)v5;
    quotaIndex = (void *)self->super._quotaIndex;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(quotaIndex);
}

//----- (0000000100025050) ----------------------------------------------------
NSMutableArray *__cdecl -[ThumbnailAHTableViewController orderCodeMArrayAHHK](
        ThumbnailAHTableViewController *self,
        SEL a2)
{

  thumbTableSrcollDirection = (void *)self->super._thumbTableSrcollDirection;
  if ( !thumbTableSrcollDirection )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (void *)self->super._thumbTableSrcollDirection;
    self->super._thumbTableSrcollDirection = (signed __int64)v5;
    thumbTableSrcollDirection = (void *)self->super._thumbTableSrcollDirection;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(thumbTableSrcollDirection);
}

//----- (00000001000250A1) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailAHTableViewController huShenCodes](ThumbnailAHTableViewController *self, SEL a2)
{

  visibleY = self->super._visibleY;
  if ( visibleY == 0.0 )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._visibleY;
    *(_QWORD *)&self->super._visibleY = v5;
    visibleY = self->super._visibleY;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(*(id *)&visibleY);
}

//----- (00000001000250F2) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailAHTableViewController hkCodes](ThumbnailAHTableViewController *self, SEL a2)
{
  id invokeRowSwitchCallBack; // rdi

  invokeRowSwitchCallBack = self->super._invokeRowSwitchCallBack;
  if ( !invokeRowSwitchCallBack )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._invokeRowSwitchCallBack;
    self->super._invokeRowSwitchCallBack = v5;
    invokeRowSwitchCallBack = self->super._invokeRowSwitchCallBack;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(invokeRowSwitchCallBack);
}

//----- (0000000100025143) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailAHTableViewController tableRequestModuleForHK](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  NSTimer *countDownTimerForScroll; // rdi
  NSTimer *v5; // rax
  NSTimer *v6; // rdi

  countDownTimerForScroll = self->super._countDownTimerForScroll;
  if ( !countDownTimerForScroll )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = (NSTimer *)_objc_msgSend(v4, "init");
    v6 = self->super._countDownTimerForScroll;
    self->super._countDownTimerForScroll = v5;
    countDownTimerForScroll = self->super._countDownTimerForScroll;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(countDownTimerForScroll);
}

//----- (0000000100025194) ----------------------------------------------------
char __cdecl -[ThumbnailAHTableViewController doNeedsToOrderHKDataItems](ThumbnailAHTableViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  SEL v3; // r12
  SEL v5; // r12
  SEL v7; // r12
  SEL v9; // r12
  SEL v11; // r12
  SEL v14; // r12
  SEL v17; // r12
  NSArray *v19; // rax
  NSNumber *v22; // rax
  NSNumber *v23; // rbx
  NSNumber *v26; // [rsp+10h] [rbp-A0h]
  NSArray *v31; // [rsp+38h] [rbp-78h]
  _QWORD v32[8]; // [rsp+40h] [rbp-70h] BYREF

  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 9810LL);
  v26 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v26;
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, v3, 10LL);
  v27 = objc_retainAutoreleasedReturnValue(v4);
  v32[1] = v27;
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, v5, 100LL);
  v28 = objc_retainAutoreleasedReturnValue(v6);
  v32[2] = v28;
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, v7, 1991120LL);
  v29 = objc_retainAutoreleasedReturnValue(v8);
  v32[3] = v29;
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, v9, 130LL);
  v30 = objc_retainAutoreleasedReturnValue(v10);
  v32[4] = v30;
  v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, v11, 50LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v32[5] = v13;
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, v14, 55LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v32[6] = v16;
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, v17, 5LL);
  v32[7] = objc_retainAutoreleasedReturnValue(v18);
  v19 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v32, 8LL);
  v31 = objc_retainAutoreleasedReturnValue(v19);
  v21 = -[HXBaseTableViewController sortID](self, "sortID");
  v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v21);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  LOBYTE(v16) = (__int64)_objc_msgSend(v31, "containsObject:", v23);
  return (_BYTE)v16 != 0;
}

//----- (00000001000253D7) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController orderAHHuShen](ThumbnailAHTableViewController *self, SEL a2)
{
  HXTableRequestModule *v2; // rax
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // r13
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSArray *v8; // rax
  NSArray *v9; // r15
  _QWORD v18[4]; // [rsp+0h] [rbp-90h] BYREF
  id to; // [rsp+20h] [rbp-70h] BYREF
  id location; // [rsp+40h] [rbp-50h] BYREF

  objc_initWeak(&location, self);
  v2 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v20 = objc_retainAutoreleasedReturnValue(v5);
  v24[0] = (__int64)v20;
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v21 = objc_retain(v6);
  v24[1] = (__int64)v21;
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
  v22 = objc_retain(v7);
  v24[2] = (__int64)v22;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v24, 3LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v18[0] = _NSConcreteStackBlock;
  v18[1] = 3254779904LL;
  v18[2] = sub_1000255B9;
  v18[3] = &unk_1012DAF08;
  objc_copyWeak(&to, &location);
  _objc_msgSend(v10, "subscribe:dataTypes:callBack:", v4, v9, v18);
  v12 = v11;
  v13(v22);
  v14(v21);
  v15(v20);
  v16(v4);
  v17(v12);
  objc_destroyWeak(&to);
  objc_destroyWeak(&location);
}

//----- (00000001000255B9) ----------------------------------------------------
void __fastcall sub_1000255B9(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithAHHuShenPushData:", v2);
}

//----- (0000000100025613) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController orderAHHK](ThumbnailAHTableViewController *self, SEL a2)
{
  HXTableRequestModule *v2; // rax
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // r13
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSArray *v8; // rax
  NSArray *v9; // r15
  _QWORD v18[4]; // [rsp+0h] [rbp-90h] BYREF
  id to; // [rsp+20h] [rbp-70h] BYREF
  id location; // [rsp+40h] [rbp-50h] BYREF

  objc_initWeak(&location, self);
  v2 = -[ThumbnailAHTableViewController tableRequestModuleForHK](self, "tableRequestModuleForHK");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ThumbnailAHTableViewController orderCodeMArrayAHHK](self, "orderCodeMArrayAHHK");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v20 = objc_retainAutoreleasedReturnValue(v5);
  v24[0] = (__int64)v20;
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v21 = objc_retain(v6);
  v24[1] = (__int64)v21;
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
  v22 = objc_retain(v7);
  v24[2] = (__int64)v22;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v24, 3LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v18[0] = _NSConcreteStackBlock;
  v18[1] = 3254779904LL;
  v18[2] = sub_1000257F5;
  v18[3] = &unk_1012DAF08;
  objc_copyWeak(&to, &location);
  _objc_msgSend(v10, "subscribe:dataTypes:callBack:", v4, v9, v18);
  v12 = v11;
  v13(v22);
  v14(v21);
  v15(v20);
  v16(v4);
  v17(v12);
  objc_destroyWeak(&to);
  objc_destroyWeak(&location);
}

//----- (00000001000257F5) ----------------------------------------------------
void __fastcall sub_1000257F5(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithAHHKPushData:", v2);
}

//----- (000000010002584F) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController deleteOrderAHHuShen](ThumbnailAHTableViewController *self, SEL a2)
{
  HXTableRequestModule *v2; // rax
  HXTableRequestModule *v3; // r14

  v2 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "orderCodeMArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXTableRequestModule dissubscribe:](v3, "dissubscribe:", v6);
  v8 = _objc_msgSend(v7, "orderCodeMArray");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "removeAllObjects");
}

//----- (00000001000258F2) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController deleteOrderAHHK](ThumbnailAHTableViewController *self, SEL a2)
{
  HXTableRequestModule *v2; // rax
  HXTableRequestModule *v3; // r14

  v2 = -[ThumbnailAHTableViewController tableRequestModuleForHK](self, "tableRequestModuleForHK");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "orderCodeMArrayAHHK");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXTableRequestModule dissubscribe:](v3, "dissubscribe:", v6);
  v8 = _objc_msgSend(v7, "orderCodeMArrayAHHK");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "removeAllObjects");
}

//----- (0000000100025995) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController parserData:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  unsigned __int64 v5; // rax

  if ( !a3 )
    return objc_autoreleaseReturnValue(0LL);
  v3 = objc_retain(a3);
  v4 = objc_alloc(&OBJC_CLASS___NSString);
  v5 = CFStringConvertEncodingToNSStringEncoding(0x632u);
  v6 = _objc_msgSend(v4, "initWithData:encoding:", v3, v5);
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001000259FB) ----------------------------------------------------
char __cdecl -[ThumbnailAHTableViewController filterCodesSuccessFromString:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  id (**v7)(id, SEL, ...); // r15
  id (**v18)(id, SEL, ...); // r13
  id (**v31)(id, SEL, ...); // r14
  id (**v38)(id, SEL, ...); // rcx
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v53 = self;
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v57 = objc_retainAutoreleasedReturnValue(v4);
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v55 = objc_retainAutoreleasedReturnValue(v5);
    v56 = v3;
    v6 = v3;
    v7 = &_objc_msgSend;
    v8 = _objc_msgSend(v6, "componentsSeparatedByString:", CFSTR("\n"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v45 = 0LL;
    v46 = 0LL;
    v47 = 0LL;
    v48 = 0LL;
    obj = objc_retain(v9);
    v10 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v45, v59, 16LL);
    if ( v10 )
    {
      v11 = (__int64)v10;
      v49 = *(_QWORD *)v46;
LABEL_4:
      v50 = "count";
      v54 = "objectAtIndexedSubscript:";
      v52 = "addObject:";
      v51 = v11;
      v12 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v46 != v49 )
          objc_enumerationMutation(obj);
        v13 = *(_QWORD *)(*((_QWORD *)&v45 + 1) + 8 * v12);
        if ( !((__int64 (__fastcall *)(__int64, const char *))v7)(v13, "length") )
          break;
        v14 = (void *)((__int64 (__fastcall *)(__int64, const char *, __CFString *))v7)(
                        v13,
                        "componentsSeparatedByString:",
                        CFSTR("="));
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v16 = v15;
        if ( !v15 || ((__int64 (__fastcall *)(id, const char *))v7)(v15, v50) != 2 )
        {
          break;
        }
        v17 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v7)(v16, v54, 0LL);
        v18 = v7;
        v19 = objc_retainAutoreleasedReturnValue(v17);
        ((void (__fastcall *)(id, const char *, id))v18)(v57, v52, v19);
        v20 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(v16, v54, 1LL);
        v21 = objc_retainAutoreleasedReturnValue(v20);
        ((void (__fastcall *)(id, __int64, id))v18)(v55, v22, v21);
        v23 = v21;
        v7 = v18;
        v24(v16);
        if ( v51 == ++v12 )
        {
          v11 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v18)(
                  obj,
                  "countByEnumeratingWithState:objects:count:",
                  &v45,
                  v59,
                  16LL);
          if ( v11 )
            goto LABEL_4;
          break;
        }
      }
    }
    v26 = v53;
    v27 = _objc_msgSend(v53, "huShenCodes");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29(v28);
    if ( !v28 )
    {
      v30 = (void *)((__int64 (__fastcall *)(void *, const char *))v7)(&OBJC_CLASS___NSArray, "array");
      v31 = v7;
      v32 = objc_retainAutoreleasedReturnValue(v30);
      ((void (__fastcall *)(id, const char *, id))v31)(v26, "setHuShenCodes:", v32);
      v33 = v32;
      v7 = v31;
    }
    ((void (__fastcall *)(id, const char *, id))v7)(v26, "setHuShenCodes:", v57);
    v34 = (void *)((__int64 (__fastcall *)(id, const char *))v7)(v26, "hkCodes");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    if ( !v35 )
    {
      v36 = (void *)((__int64 (__fastcall *)(void *, const char *))v7)(&OBJC_CLASS___NSArray, "array");
      v37 = objc_retainAutoreleasedReturnValue(v36);
      v38 = v7;
      v39 = v37;
      ((void (__fastcall *)(id, const char *, id))v38)(v26, "setHkCodes:", v37);
    }
    v3 = v56;
    v40 = v55;
    _objc_msgSend(v26, "setHkCodes:", v55);
    v41(obj);
    v42(v40);
    v43(v57);
    v25 = 1;
  }
  else
  {
    v25 = 0;
  }
  return v25;
}

//----- (0000000100025E21) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController dealWithReceivedHongKongQuotes:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  -[ThumbnailAHTableViewController setGangGuTableViewDataArray:](self, "setGangGuTableViewDataArray:", a3);
}

//----- (0000000100025E33) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController executeAfterGangGuTableViewDataAlready](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx

  v2 = -[ThumbnailAHTableViewController GangGuTableViewDataArray](self, "GangGuTableViewDataArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
  {
    v5(self, "handleMainStockTableViewDatas");
    v6(self, "reloadDataForMyTable");
    v7(self, "order");
  }
}

//----- (0000000100025EB8) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController handleMainStockTableViewDatas](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  NSArray *v2; // rax
  id (*v3)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  NSArray *v32; // [rsp+18h] [rbp-38h]

  v2 = -[ThumbnailAHTableViewController GangGuTableViewDataArray](self, "GangGuTableViewDataArray");
  v32 = objc_retainAutoreleasedReturnValue(v2);
  v4 = v3(self, "stockModel");
  v34 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5(v34, "mainStockTableViewDataArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(self, "mergeArray:into:", v32, v7);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(self, "stockModel");
  v14 = v13;
  v15 = objc_retainAutoreleasedReturnValue(v12);
  v14(v15, "setMainStockTableViewDataArray:", v10);
  ((void (__fastcall *)(ThumbnailAHTableViewController *, const char *))v14)(self, "preprocessData");
  v17 = (void *)((__int64 (__fastcall *)(ThumbnailAHTableViewController *, const char *))v14)(self, "stockModel");
  v18 = (__int64 (__fastcall *)(__objc2_class *, const char *, __int64, id, id))v14;
  v33 = objc_retainAutoreleasedReturnValue(v17);
  v19 = (void *)((__int64 (__fastcall *)(id, const char *))v14)(v33, "mainStockTableViewDataArray");
  v30 = objc_retainAutoreleasedReturnValue(v19);
  v20 = ((__int64 (__fastcall *)(ThumbnailAHTableViewController *, const char *))v14)(self, "sortID");
  v21 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v14)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithLong:",
                  v20);
  v31 = objc_retainAutoreleasedReturnValue(v21);
  v22 = (void *)((__int64 (__fastcall *)(ThumbnailAHTableViewController *, const char *))v14)(self, "sortOrder");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = (void *)v18(&OBJC_CLASS___ThumbnailUtils, "sortCodes:bySortID:bySortOrder:", v24, v31, v23);
  objc_retainAutoreleasedReturnValue(v25);
  v26 = (void *)((__int64 (__fastcall *)(ThumbnailAHTableViewController *, const char *))v18)(self, "stockModel");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  ((void (__fastcall *)(id, const char *, __int64))v18)(v27, "setMainStockTableViewDataArray:", v28);
}

//----- (00000001000260C3) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController reloadDataForMyTable](ThumbnailAHTableViewController *self, SEL a2)
{
  HXStockModel *v2; // rax
  HXStockModel *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  HXBaseTableView *v8; // rax
  HXBaseTableView *v9; // rbx
  HXBaseTableView *v11; // rax
  HXBaseTableView *v12; // rbx
  NSString *v15; // rax
  NSString *v16; // rbx
  NSIndexSet *v20; // rax
  NSIndexSet *v21; // rbx

  v2 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXStockModel mainStockTableViewDataArray](v3, "mainStockTableViewDataArray");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  -[ThumbnailBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v6);
  v7(v3);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  v8 = -[HXBaseTableViewController myTable](self, "myTable");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXBaseTableView reloadRequestData](v9, "reloadRequestData");
  v10(v9);
  if ( (unsigned __int8)-[ThumbnailBaseTableViewController isTableSwitched](self, "isTableSwitched") )
  {
    v11 = -[HXBaseTableViewController myTable](self, "myTable");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = -[ThumbnailBaseTableViewController getScorllPosition](self, "getScorllPosition");
    _objc_msgSend(v12, "scrollRowToVisible:", v13);
    -[ThumbnailBaseTableViewController setIsTableSwitched:](self, "setIsTableSwitched:", 0LL);
  }
  v14 = -[HXBaseTableViewController getIndexForSelectedCode](self, "getIndexForSelectedCode");
  if ( v14 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v20 = _objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", v14);
    v21 = objc_retainAutoreleasedReturnValue(v20);
    -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", v21);
LABEL_8:
    goto LABEL_9;
  }
  v15 = -[HXBaseTableViewController selectedCode](self, "selectedCode");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17 = _objc_msgSend(v16, "length");
  if ( !v17 )
  {
    v22 = (void *)v18(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", 0LL);
    v21 = objc_retainAutoreleasedReturnValue(v22);
    v23(self, "setSelectedRowIndexs:", v21);
    goto LABEL_8;
  }
  -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", 0LL);
LABEL_9:
  v24 = (void *)v19(self, "selectedRowIndexs");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v27 = v26(self, "selectedRowDidChange");
  v28(self, "codingToSelectedRowIndexes:selectedRowDidChange:", v25, (unsigned int)v27);
  v29(self, "setSelectedRowDidChange:", 0LL);
}

//----- (000000010002630C) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController reloadPushDataForMyTable](ThumbnailAHTableViewController *self, SEL a2)
{
  HXBaseTableView *v2; // rax
  HXBaseTableView *v3; // rbx

  v2 = -[HXBaseTableViewController myTable](self, "myTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseTableView reloadPushData:](v3, "reloadPushData:");
}

//----- (000000010002638A) ----------------------------------------------------
void __fastcall sub_10002638A(__int64 a1)
{
  NSIndexSet *v2; // rax
  NSIndexSet *v3; // r14

  v1 = _objc_msgSend(*(id *)(a1 + 32), "getIndexForSelectedCode");
  if ( v1 == (id)0x7FFFFFFFFFFFFFFFLL )
  {
    _objc_msgSend(*(id *)(a1 + 32), "setSelectedRowIndexs:", 0LL);
  }
  else
  {
    v2 = _objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", v1);
    v3 = objc_retainAutoreleasedReturnValue(v2);
    _objc_msgSend(*(id *)(a1 + 32), "setSelectedRowIndexs:", v3);
  }
  v4 = *(void **)(a1 + 32);
  v5 = _objc_msgSend(v4, "selectedRowIndexs");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v4, "codingToSelectedRowIndexes:selectedRowDidChange:", v6, 0LL);
}

//----- (0000000100026454) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController mergeArray:into:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (**v33)(id, SEL, ...); // r12
  int v35; // r14d
  id (*v40)(id, SEL, ...); // r12
  id (*v42)(id, SEL, ...); // r12
  id (*v45)(id, SEL, ...); // r12
  id (*v48)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  SEL v61; // r12
  SEL v70; // r12
  id (*v80)(id, SEL, ...); // r12
  id (*v81)(id, SEL, ...); // r12
  id (*v85)(id, SEL, ...); // r12
  id (*v88)(id, SEL, ...); // r12
  SEL v139; // [rsp+80h] [rbp-230h]
  SEL v146; // [rsp+B8h] [rbp-1F8h]
  SEL v147; // [rsp+C0h] [rbp-1F0h]
  SEL v148; // [rsp+C8h] [rbp-1E8h]
  SEL v149; // [rsp+D0h] [rbp-1E0h]
  SEL v151; // [rsp+E0h] [rbp-1D0h]
  SEL v152; // [rsp+E8h] [rbp-1C8h]
  SEL v153; // [rsp+F0h] [rbp-1C0h]
  SEL v156; // [rsp+108h] [rbp-1A8h]
  SEL v157; // [rsp+110h] [rbp-1A0h]
  int v158; // [rsp+11Ch] [rbp-194h]
  id obj; // [rsp+138h] [rbp-178h]
  SEL v163; // [rsp+140h] [rbp-170h]
  SEL v167; // [rsp+160h] [rbp-150h]
  SEL v170; // [rsp+178h] [rbp-138h]

  v143 = self;
  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = v6;
  v150 = v5;
  if ( !v5 || (v164 = v6, v7 = v6, !_objc_msgSend(v5, "count")) )
  {
    v129 = v7;
LABEL_31:
    v165 = objc_retain(v129);
    goto LABEL_32;
  }
  if ( !v164 || (v7 = v164, !_objc_msgSend(v164, "count")) )
  {
    v129 = v5;
    goto LABEL_31;
  }
  v165 = _objc_msgSend(v164, "mutableCopy");
  v131 = 0LL;
  v132 = 0LL;
  v133 = 0LL;
  v134 = 0LL;
  obj = objc_retain(v5);
  v9 = v8(obj, "countByEnumeratingWithState:objects:count:", &v131, v172, 16LL);
  if ( v9 )
  {
    v11 = v9;
    v144 = *(_QWORD *)v132;
LABEL_7:
    v156 = "class";
    v157 = "isKindOfClass:";
    v167 = "numberWithInt:";
    v170 = "objectForKeyedSubscript:";
    v146 = "hkCodes";
    v147 = "findIndexOfTargetCode:inArray:";
    v148 = "huShenCodes";
    v149 = "getStockCodeInCodes:ofIndex:";
    v163 = "setObject:forKey:";
    v153 = "replaceObjectAtIndex:withObject:";
    v151 = "dictionary";
    v152 = "addObject:";
    v145 = v11;
    v12 = 0LL;
    while ( 1 )
    {
      if ( *(_QWORD *)v132 != v144 )
        objc_enumerationMutation(obj);
      v141 = v12;
      v13 = *(void **)(*((_QWORD *)&v131 + 1) + 8 * v12);
      v14 = v10(&OBJC_CLASS___NSDictionary, v156);
      if ( !(unsigned __int8)v15(v13, v157, v14) )
        break;
      v16 = objc_retain(v13);
      v18 = v17(&OBJC_CLASS___NSNumber, v167, 5LL);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v168 = v16;
      v21 = v20(v16, v170, v19);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23 = _objc_msgSend(v143, v146);
      v24 = objc_retainAutoreleasedReturnValue(v23);
      v142 = v22;
      v25 = _objc_msgSend(&OBJC_CLASS___ThumbnailUtils, v147, v22, v24);
      v27 = _objc_msgSend(v26, v148);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v30 = v29(&OBJC_CLASS___ThumbnailUtils, v149, v28, v25);
      v159 = objc_retainAutoreleasedReturnValue(v30);
      v138 = 0LL;
      v137 = 0LL;
      v136 = 0LL;
      v135 = 0LL;
      v160 = objc_retain(v164);
      v32 = v31(v160, "countByEnumeratingWithState:objects:count:", &v135, v171, 16LL);
      if ( v32 )
      {
        v34 = v32;
        v169 = *(id *)v136;
        v35 = 0;
        while ( 2 )
        {
          v139 = "isEqualToString:";
          v36 = (void *)v35;
          v140 = v34;
          v158 = (_DWORD)v34 + v35;
          for ( i = 0LL; i != v140; i = (char *)i + 1 )
          {
            if ( *(id *)v136 != v169 )
              objc_enumerationMutation(v160);
            v38 = *(void **)(*((_QWORD *)&v135 + 1) + 8LL * (_QWORD)i);
            v39 = ((id (*)(id, SEL, ...))v33)(&OBJC_CLASS___NSDictionary, v156);
            if ( !(unsigned __int8)v40(v38, v157, v39) )
            {
              v35 = (int)v36;
              goto LABEL_24;
            }
            v166 = v36;
            v41 = objc_retain(v38);
            v43 = v42(&OBJC_CLASS___NSNumber, v167, 5LL);
            v44 = objc_retainAutoreleasedReturnValue(v43);
            v46 = v45(v41, v170, v44);
            v47 = objc_retainAutoreleasedReturnValue(v46);
            v155 = v41;
            v49 = v48(v41, "mutableCopy");
            v154 = v47;
            if ( (unsigned __int8)v50(v47, v139, v159) )
            {
              v51 = v167;
              v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 5LL);
              v169 = objc_retainAutoreleasedReturnValue(v52);
              v53 = _objc_msgSend(v168, v170, v169);
              objc_retainAutoreleasedReturnValue(v53);
              v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, v51, 50LL);
              v55 = objc_retainAutoreleasedReturnValue(v54);
              v161 = v49;
              _objc_msgSend(v49, v163, v56, v55);
              v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 10LL);
              v169 = objc_retainAutoreleasedReturnValue(v58);
              v59 = _objc_msgSend(v168, v170, v169);
              v60 = objc_retainAutoreleasedReturnValue(v59);
              v62 = _objc_msgSend(&OBJC_CLASS___NSNumber, v61, 100LL);
              v63 = objc_retainAutoreleasedReturnValue(v62);
              _objc_msgSend(v161, v163, v60, v63);
              v64 = v167;
              v65 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 6LL);
              v169 = objc_retainAutoreleasedReturnValue(v65);
              v66 = _objc_msgSend(v168, v170, v169);
              v67 = objc_retainAutoreleasedReturnValue(v66);
              v68 = _objc_msgSend(&OBJC_CLASS___NSNumber, v64, 60LL);
              v69 = objc_retainAutoreleasedReturnValue(v68);
              _objc_msgSend(v161, v70, v67, v69);
              v71 = v167;
              v72 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 13LL);
              v169 = objc_retainAutoreleasedReturnValue(v72);
              v73 = _objc_msgSend(v168, v170, v169);
              objc_retainAutoreleasedReturnValue(v73);
              v74 = _objc_msgSend(&OBJC_CLASS___NSNumber, v71, 130LL);
              v75 = objc_retainAutoreleasedReturnValue(v74);
              v76 = v161;
              _objc_msgSend(v161, v163, v77, v75);
              LODWORD(v75) = (_DWORD)v166;
              _objc_msgSend(v165, v153, v166, v76);
              v35 = (int)v75;
              goto LABEL_24;
            }
            v36 = (char *)v166 + 1;
            v33 = &_objc_msgSend;
          }
          v34 = _objc_msgSend(v160, "countByEnumeratingWithState:objects:count:", &v135, v171, 16LL);
          v35 = v158;
          if ( v34 )
            continue;
          break;
        }
      }
      else
      {
        v35 = 0;
      }
LABEL_24:
      v79 = v160;
      if ( (char *)v80(v79, "count") - 1 < (char *)v35 )
      {
        v82 = v81(&OBJC_CLASS___NSMutableDictionary, v151);
        v83 = objc_retainAutoreleasedReturnValue(v82);
        v166 = v83;
        v84 = v167;
        v86 = v85(&OBJC_CLASS___NSNumber, v167, 5LL);
        v87 = objc_retainAutoreleasedReturnValue(v86);
        v89 = v88(v168, v170, v87);
        objc_retainAutoreleasedReturnValue(v89);
        v90 = _objc_msgSend(&OBJC_CLASS___NSNumber, v84, 50LL);
        v91 = objc_retainAutoreleasedReturnValue(v90);
        v92 = v83;
        v93 = v163;
        _objc_msgSend(v92, v163, v94, v91);
        v96 = v84;
        v97 = _objc_msgSend(&OBJC_CLASS___NSNumber, v84, 5LL);
        v98 = objc_retainAutoreleasedReturnValue(v97);
        v99 = v166;
        _objc_msgSend(v166, v93, v159, v98);
        v100 = v96;
        v101 = _objc_msgSend(&OBJC_CLASS___NSNumber, v96, 10LL);
        v102 = objc_retainAutoreleasedReturnValue(v101);
        v103 = v168;
        v104 = _objc_msgSend(v168, v170, v102);
        objc_retainAutoreleasedReturnValue(v104);
        v105 = _objc_msgSend(&OBJC_CLASS___NSNumber, v100, 100LL);
        v106 = objc_retainAutoreleasedReturnValue(v105);
        v107 = v99;
        v108 = v163;
        _objc_msgSend(v107, v163, v109, v106);
        v111 = v167;
        v112 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 6LL);
        v113 = objc_retainAutoreleasedReturnValue(v112);
        v114 = _objc_msgSend(v103, v170, v113);
        objc_retainAutoreleasedReturnValue(v114);
        v115 = v111;
        v116 = _objc_msgSend(&OBJC_CLASS___NSNumber, v111, 60LL);
        v117 = objc_retainAutoreleasedReturnValue(v116);
        _objc_msgSend(v166, v108, v118, v117);
        v120 = _objc_msgSend(&OBJC_CLASS___NSNumber, v115, 13LL);
        v121 = objc_retainAutoreleasedReturnValue(v120);
        v122 = _objc_msgSend(v168, v170, v121);
        objc_retainAutoreleasedReturnValue(v122);
        v123 = _objc_msgSend(&OBJC_CLASS___NSNumber, v115, 130LL);
        v124 = objc_retainAutoreleasedReturnValue(v123);
        v125 = v166;
        _objc_msgSend(v166, v108, v126, v124);
        v128(v165, v152, v125);
      }
      v12 = v141 + 1;
      if ( (id)(v141 + 1) == v145 )
      {
        v11 = v10(obj, "countByEnumeratingWithState:objects:count:", &v131, v172, 16LL);
        if ( v11 )
          goto LABEL_7;
        break;
      }
    }
  }
  v7 = v164;
LABEL_32:
  return objc_autoreleaseReturnValue(v165);
}

//----- (0000000100027034) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController preprocessOriginalData:DataItemID:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{
  unsigned __int64 v6; // r12
  NSNumber *v14; // rax
  NSNumber *v19; // rbx
  signed __int64 v22; // [rsp+8h] [rbp-68h]

  v5 = objc_retain(a3);
  if ( _objc_msgSend(v5, "count") )
  {
    v6 = 0LL;
    v22 = a4;
    v23 = v5;
    while ( a4 != 1991120 )
    {
      if ( a4 == 9810 )
      {
        v7 = _objc_msgSend(v5, "objectAtIndexedSubscript:", v6);
        v8 = objc_retainAutoreleasedReturnValue(v7);
        v9 = -[ThumbnailAHTableViewController getPremiumRateOfData:](self, "getPremiumRateOfData:", v8);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        v12 = _objc_msgSend(v5, "objectAtIndexedSubscript:", v11);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 9810LL);
LABEL_7:
        v19 = objc_retainAutoreleasedReturnValue(v14);
        _objc_msgSend(v13, "setObject:forKeyedSubscript:", v10, v19);
        a4 = v22;
        v5 = v23;
      }
      v20 = _objc_msgSend(v5, "count");
      if ( (unsigned __int64)v20 <= v6 )
        return objc_autoreleaseReturnValue(v5);
    }
    v15 = _objc_msgSend(v5, "objectAtIndexedSubscript:", v6);
    v8 = objc_retainAutoreleasedReturnValue(v15);
    v16 = -[ThumbnailAHTableViewController getAHHKZhangFuOfData:](self, "getAHHKZhangFuOfData:", v8);
    v10 = objc_retainAutoreleasedReturnValue(v16);
    v18 = _objc_msgSend(v5, "objectAtIndexedSubscript:", v17);
    v13 = objc_retainAutoreleasedReturnValue(v18);
    v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1991120LL);
    goto LABEL_7;
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (000000010002721E) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController getPremiumRateOfData:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // rbx
  NSNumber *v17; // rdi
  NSNumber *v18; // rbx
  id (*v23)(id, SEL, ...); // r12
  NSNumber *v29; // rax
  NSNumber *v30; // rbx
  NSNumber *v32; // [rsp+20h] [rbp-30h]

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInt:", 0xFFFFFFFFLL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( v4 )
  {
    v32 = v6;
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v4, "objectForKeyedSubscript:", v8);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 100LL);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = _objc_msgSend(v4, "objectForKeyedSubscript:", v12);
    v31 = objc_retainAutoreleasedReturnValue(v13);
    v14(v12);
    v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( (unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v15) )
    {
      _objc_msgSend(v10, "doubleValue");
      if ( v3 != 4294967295.0 )
      {
        _objc_msgSend(v10, "doubleValue");
        if ( v3 != 2147483648.0 )
        {
          v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, v16);
          if ( (unsigned __int8)v23(v31, "isKindOfClass:", v22) )
          {
            _objc_msgSend(v31, "doubleValue");
            v19 = v31;
            if ( v3 == 4294967295.0
              || (_objc_msgSend(v31, v24), v3 == 2147483648.0)
              || (v26 = v25, _objc_msgSend(v31, v25), v3 == 0.0)
              || (-[ThumbnailAHTableViewController getExchangeRate](self, "getExchangeRate"),
                  _objc_msgSend(v31, v26),
                  v28 = v3 / 100.0 * (v3 / 100.0),
                  v28 == 0.0) )
            {
              v17 = v32;
            }
            else
            {
              _objc_msgSend(v27, v26);
              v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", (v28 - v28) / v28 * 100.0);
              v30 = objc_retainAutoreleasedReturnValue(v29);
              v17 = v30;
            }
          }
          else
          {
            v17 = v32;
            v19 = v31;
          }
          goto LABEL_9;
        }
      }
      v17 = v32;
    }
    else
    {
      v17 = v32;
    }
    v19 = v31;
LABEL_9:
    v18 = objc_retain(v17);
    goto LABEL_10;
  }
  v18 = objc_retain(v6);
LABEL_10:
  return objc_autorelease(v18);
}

//----- (0000000100027508) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController getAHHKZhangFuOfData:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12

  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 )
  {
    v6 = -[ThumbnailAHTableViewController getAHHKZhangDieOfData:](self, "getAHHKZhangDieOfData:", v4);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    if ( v7 )
    {
      _objc_msgSend(v7, "doubleValue");
      v10 = v9(&OBJC_CLASS___NSNumber, "numberWithInt:", 60LL);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = v12(v5, "objectForKeyedSubscript:", v11);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v16 = 0LL;
      if ( v3 != 4294967295.0 && v3 != 2147483648.0 )
      {
        v17 = v15(&OBJC_CLASS___NSNumber, "class");
        if ( !(unsigned __int8)v18(v14, "isKindOfClass:", v17)
          || (_objc_msgSend(v14, "doubleValue"), v3 == 4294967295.0)
          || (_objc_msgSend(v14, "doubleValue"), v3 == 2147483648.0)
          || (_objc_msgSend(v14, "doubleValue"), v3 == 0.0) )
        {
          v16 = 0LL;
        }
        else
        {
          v22(v14, "doubleValue");
          v24 = v23(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3 / v3 * 100.0);
          v16 = objc_retainAutoreleasedReturnValue(v24);
        }
      }
      v19 = objc_retain(v16);
      v20(v19);
    }
    else
    {
      v19 = 0LL;
    }
  }
  else
  {
    v19 = 0LL;
  }
  return objc_autoreleaseReturnValue(v19);
}

//----- (00000001000276D3) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController getAHHKZhangDieOfData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  NSNumber *v5; // rax
  NSNumber *v6; // rbx
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSNumber *v18; // rdi
  NSNumber *v20; // rbx
  NSNumber *v26; // rax

  if ( a3 )
  {
    v4 = objc_retain(a3);
    v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 100LL);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v4, "objectForKeyedSubscript:", v6);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 60LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v4, "objectForKeyedSubscript:", v10);
    v28 = objc_retainAutoreleasedReturnValue(v11);
    v12(v4);
    v13(v10);
    v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( !(unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v14)
      || (_objc_msgSend(v15, "doubleValue"), v3 == 4294967295.0)
      || (_objc_msgSend(v16, "doubleValue"), v3 == 2147483648.0)
      || (_objc_msgSend(v17, "doubleValue"), v3 == 0.0) )
    {
      v18 = 0LL;
      v19 = v28;
    }
    else
    {
      v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      v19 = v28;
      if ( !(unsigned __int8)_objc_msgSend(v28, "isKindOfClass:", v23)
        || (_objc_msgSend(v28, "doubleValue"), v3 == 4294967295.0)
        || (_objc_msgSend(v28, "doubleValue"), v3 == 2147483648.0)
        || (_objc_msgSend(v28, "doubleValue"), v3 == 0.0) )
      {
        v18 = 0LL;
      }
      else
      {
        v27 = ((double (__fastcall *)(__int64, const char *))_objc_msgSend)(v24, "doubleValue");
        v25 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v28, "doubleValue");
        v26 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v27 - v25);
        v18 = objc_retainAutoreleasedReturnValue(v26);
      }
    }
    v20 = objc_retain(v18);
  }
  else
  {
    v20 = 0LL;
  }
  return objc_autoreleaseReturnValue(v20);
}

//----- (0000000100027916) ----------------------------------------------------
double __cdecl -[ThumbnailAHTableViewController getExchangeRate](ThumbnailAHTableViewController *self, SEL a2)
{
  return *(double *)&self->super._quotaCount;
}

//----- (0000000100027928) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController saveExchangeRate:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12

  v4 = objc_retain(a3);
  if ( v4 )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    if ( (unsigned __int8)v6(v4, "isKindOfClass:", v5) )
    {
      v8 = v7(&OBJC_CLASS___NSNumber, "numberWithInt:", 981LL);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v11 = v10(v4, "objectForKeyedSubscript:", v9);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = v13(&OBJC_CLASS___NSNumber, "class");
      if ( (unsigned __int8)v15(v12, "isKindOfClass:", v14) )
      {
        _objc_msgSend(v12, "doubleValue");
        if ( v3 != 4294967295.0 )
        {
          _objc_msgSend(v12, v16);
          if ( v3 != 2147483648.0 )
          {
            _objc_msgSend(v12, v17);
            *(double *)&self->super._quotaCount = v3;
          }
        }
      }
    }
  }
}

//----- (0000000100027A5E) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController preprocessData](ThumbnailAHTableViewController *self, SEL a2)
{
  signed __int64 v2; // rax
  HXStockModel *v3; // rax
  NSArray *v4; // rax
  NSArray *v5; // r13
  HXStockModel *v9; // rax
  HXStockModel *v10; // r15
  HXStockModel *v12; // r14
  HXStockModel *v13; // rax
  NSArray *v14; // rax
  HXStockModel *v18; // rax
  HXStockModel *v21; // [rsp+0h] [rbp-30h]

  v2 = -[HXBaseTableViewController sortID](self, "sortID");
  if ( v2 > 264647 )
  {
    if ( v2 == 264648 )
      goto LABEL_9;
    if ( v2 != 1991120 )
    {
      if ( v2 != 526792 )
        return;
      goto LABEL_9;
    }
    goto LABEL_10;
  }
  switch ( v2 )
  {
    case 15LL:
LABEL_9:
      v3 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v21 = objc_retainAutoreleasedReturnValue(v3);
      v4 = -[HXStockModel mainStockTableViewDataArray](v21, "mainStockTableViewDataArray");
      v5 = objc_retainAutoreleasedReturnValue(v4);
      v6 = -[HXBaseTableViewController sortID](self, "sortID");
      v8 = _objc_msgSend(v7, "preprocessOriginalData:dataItemID:", v5, v6);
      objc_retainAutoreleasedReturnValue(v8);
      v9 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      -[HXStockModel setMainStockTableViewDataArray:](v10, "setMainStockTableViewDataArray:", v11);
      v12 = v21;
LABEL_11:
      return;
    case 9810LL:
LABEL_10:
      v13 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v12 = objc_retainAutoreleasedReturnValue(v13);
      v14 = (NSArray *)-[HXStockModel mainStockTableViewDataArray](v12, "mainStockTableViewDataArray");
      v5 = objc_retainAutoreleasedReturnValue(v14);
      v16 = _objc_msgSend(self, v15);
      v17 = -[ThumbnailAHTableViewController preprocessOriginalData:DataItemID:](
              self,
              "preprocessOriginalData:DataItemID:",
              v5,
              v16);
      objc_retainAutoreleasedReturnValue(v17);
      v18 = (HXStockModel *)-[HXBaseTableViewController stockModel](self, "stockModel");
      v10 = objc_retainAutoreleasedReturnValue(v18);
      -[HXStockModel setMainStockTableViewDataArray:](v10, "setMainStockTableViewDataArray:", v19);
      goto LABEL_11;
    case 199112LL:
      goto LABEL_9;
  }
}

//----- (0000000100027C27) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController getOrderCodeList](ThumbnailAHTableViewController *self, SEL a2)
{
  _BYTE *v13; // r12
  unsigned __int64 v17; // r14
  NSNumber *v20; // rax
  NSNumber *v21; // rbx
  NSMutableDictionary *v30; // rax
  id (*v37)(id, SEL, ...); // r12
  id (*v43)(id, SEL, ...); // r12
  id (*v46)(id, SEL, ...); // r12
  NSMutableDictionary *v54; // rax
  NSMutableDictionary *v55; // r13
  _BYTE *v64; // [rsp+90h] [rbp-30h]

  -[ThumbnailBaseTableViewController setRequestRowRange](self, "setRequestRowRange");
  v3 = _objc_msgSend(v2, "orderCodeMArray");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "removeAllObjects");
  v6 = _objc_msgSend(v5, "orderCodeMArrayAHHK");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  _objc_msgSend(v7, "removeAllObjects");
  v9 = _objc_msgSend(v8, "stockModel");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "mainStockTableViewDataArray");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v64 = v13;
  v14 = _objc_msgSend(v13, "getVisibleDataSource:", v12);
  objc_retainAutoreleasedReturnValue(v14);
  if ( _objc_msgSend(v15, "count") )
  {
    v17 = 0LL;
    v62 = v16;
    do
    {
      v18 = _objc_msgSend(v16, "objectAtIndexedSubscript:", v17);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v22 = _objc_msgSend(v19, "objectForKeyedSubscript:", v21);
      objc_retainAutoreleasedReturnValue(v22);
      if ( v23 )
      {
        v24 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v23);
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v59 = v26;
        v27 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v26);
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v29 = v28;
        v61 = v25;
        if ( v25 && v28 )
        {
          v30 = _objc_msgSend(
                  &OBJC_CLASS___NSMutableDictionary,
                  "dictionaryWithObjectsAndKeys:",
                  v25,
                  CFSTR("StockCode"),
                  v28,
                  CFSTR("Market"),
                  0LL);
          objc_retainAutoreleasedReturnValue(v30);
          v31 = _objc_msgSend(v64, "orderCodeMArray");
          v32 = objc_retainAutoreleasedReturnValue(v31);
          _objc_msgSend(v32, "addObject:", v33);
        }
        if ( v64[312] )
        {
          v35 = _objc_msgSend(v64, "orderCodeMArrayAHHK");
          v36 = objc_retainAutoreleasedReturnValue(v35);
          if ( !v36 )
          {
            v38 = v37(&OBJC_CLASS___NSMutableArray, "array");
            v39 = objc_retainAutoreleasedReturnValue(v38);
            v40(v64, "setOrderCodeMArrayAHHK:", v39);
          }
          v41 = v37(v62, "objectAtIndexedSubscript:", v17);
          v42 = objc_retainAutoreleasedReturnValue(v41);
          v44 = v43(&OBJC_CLASS___NSNumber, "numberWithInt:", 50LL);
          v45 = objc_retainAutoreleasedReturnValue(v44);
          v47 = v46(v42, "objectForKeyedSubscript:", v45);
          objc_retainAutoreleasedReturnValue(v47);
          if ( v48 )
          {
            v49 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v48);
            v50 = objc_retainAutoreleasedReturnValue(v49);
            v52 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v51);
            v53 = objc_retainAutoreleasedReturnValue(v52);
            v63 = v50;
            if ( v50 && v53 )
            {
              v60 = v53;
              v54 = _objc_msgSend(
                      &OBJC_CLASS___NSMutableDictionary,
                      "dictionaryWithObjectsAndKeys:",
                      v50,
                      CFSTR("StockCode"),
                      v53,
                      CFSTR("Market"),
                      0LL);
              v55 = objc_retainAutoreleasedReturnValue(v54);
              v56 = _objc_msgSend(v64, "orderCodeMArrayAHHK");
              v57 = objc_retainAutoreleasedReturnValue(v56);
              _objc_msgSend(v57, "addObject:", v55);
              v53 = v60;
            }
          }
        }
        v23 = v59;
      }
      ++v17;
    }
    while ( (unsigned __int64)_objc_msgSend(v62, "count") > v17 );
  }
}

//----- (0000000100028105) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController getVisibleDataSource:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  ThumbnailAHTableViewController *v6; // r15
  unsigned __int64 v12; // r12
  unsigned __int64 v17; // r12

  v22 = objc_retain(a3);
  if ( v22 )
  {
    if ( _objc_msgSend(v22, "count") )
    {
      v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
      objc_retainAutoreleasedReturnValue(v3);
      if ( _objc_msgSend(v22, "count") )
      {
        v20 = v4;
        v5 = "begin";
        v6 = self;
        do
        {
          v7 = _objc_msgSend(v6, v5);
          if ( (__int64)v7 <= v8 )
          {
            v9 = v5;
            v10 = _objc_msgSend(v6, v5);
            v11 = (char *)-[ThumbnailBaseTableViewController count](self, "count") + (_QWORD)v10 - 1;
            v6 = self;
            if ( (unsigned __int64)v11 >= v12 )
            {
              v13 = _objc_msgSend(v22, "objectAtIndexedSubscript:", v12);
              v14 = objc_retainAutoreleasedReturnValue(v13);
              _objc_msgSend(v20, "addObject:", v14);
              v15 = v14;
              v5 = v9;
            }
          }
          v16 = _objc_msgSend(v22, "count");
        }
        while ( (unsigned __int64)v16 > v17 );
      }
    }
  }
  return objc_autoreleaseReturnValue(v18);
}

//----- (000000010002825E) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController updateAGuData:SourceData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSNumber *v7; // rax
  NSNumber *v8; // rbx
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12

  v6 = objc_retain(a3);
  v33 = objc_retain(a4);
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v32 = v6;
  v10 = v9(v6, "objectForKeyedSubscript:", v8);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)v14(v11, "isKindOfClass:", v13) )
  {
    _objc_msgSend(v11, "doubleValue");
    if ( v4 != 4294967295.0 )
    {
      _objc_msgSend(v11, "doubleValue");
      if ( v4 != 2147483648.0 )
      {
        v16 = v15(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18(v33, "setObject:forKeyedSubscript:", v11, v17);
      }
    }
  }
  v19 = v15(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v22 = v21(v32, "objectForKeyedSubscript:", v20);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = v24(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)v26(v23, "isKindOfClass:", v25) )
  {
    _objc_msgSend(v23, "doubleValue");
    if ( v4 != 4294967295.0 )
    {
      _objc_msgSend(v23, "doubleValue");
      if ( v4 != 2147483648.0 )
      {
        v28 = v27(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
        v29 = objc_retainAutoreleasedReturnValue(v28);
        v30(v33, "setObject:forKeyedSubscript:", v23, v29);
      }
    }
  }
  return objc_autoreleaseReturnValue(v33);
}

//----- (000000010002847E) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController updateGangGuData:SourceData:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSNumber *v7; // rax
  NSNumber *v8; // rbx
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12

  v6 = objc_retain(a3);
  v33 = objc_retain(a4);
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v32 = v6;
  v10 = v9(v6, "objectForKeyedSubscript:", v8);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)v14(v11, "isKindOfClass:", v13) )
  {
    _objc_msgSend(v11, "doubleValue");
    if ( v4 != 4294967295.0 )
    {
      _objc_msgSend(v11, "doubleValue");
      if ( v4 != 2147483648.0 )
      {
        v16 = v15(&OBJC_CLASS___NSNumber, "numberWithInt:", 100LL);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18(v33, "setObject:forKeyedSubscript:", v11, v17);
      }
    }
  }
  v19 = v15(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v22 = v21(v32, "objectForKeyedSubscript:", v20);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = v24(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)v26(v23, "isKindOfClass:", v25) )
  {
    _objc_msgSend(v23, "doubleValue");
    if ( v4 != 4294967295.0 )
    {
      _objc_msgSend(v23, "doubleValue");
      if ( v4 != 2147483648.0 )
      {
        v28 = v27(&OBJC_CLASS___NSNumber, "numberWithInt:", 130LL);
        v29 = objc_retainAutoreleasedReturnValue(v28);
        v30(v33, "setObject:forKeyedSubscript:", v23, v29);
      }
    }
  }
  return objc_autoreleaseReturnValue(v33);
}

//----- (000000010002869E) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController registerNotificationObserver](
        ThumbnailAHTableViewController *self,
        SEL a2)
{
  SEL v18; // r12

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v22 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "myTable");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "superview");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "superview");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v23 = v10;
  _objc_msgSend(
    v22,
    "addObserver:selector:name:object:",
    v10,
    "scrollViewWillStartScroll:",
    NSScrollViewWillStartLiveScrollNotification,
    v9);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = _objc_msgSend(v13, "myTable");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(v15, "superview");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = _objc_msgSend(v17, v18);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21(
    v12,
    "addObserver:selector:name:object:",
    v23,
    "scrollViewDidEndScroll:",
    NSScrollViewDidEndLiveScrollNotification,
    v20);
}

//----- (000000010002884A) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController invalidateNoficationObserver](
        ThumbnailAHTableViewController *self,
        SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
}

//----- (000000010002889D) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController actionForTalbeViewSeclectionDidChange:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v4; // rax
  HXStockModel *v5; // rbx
  NSArray *v6; // rax
  NSArray *v7; // r15
  SEL v8; // r12
  NSNumber *v12; // rax
  NSNumber *v13; // rbx
  SelfStock *v21; // rax
  SelfStock *v22; // rbx
  NSDictionary *v25; // rax
  NSDictionary *v26; // r13
  NSDictionary *v33; // rax
  HXBaseTableView *v34; // rax
  HXBaseTableView *v35; // rbx
  _QWORD v42[2]; // [rsp+30h] [rbp-50h] BYREF
  _QWORD v43[2]; // [rsp+40h] [rbp-40h] BYREF

  if ( (a3 & 0x7FFFFFFFFFFFFFFFLL) != 0x7FFFFFFFFFFFFFFFLL )
  {
    v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[HXStockModel mainStockTableViewDataArray](v5, "mainStockTableViewDataArray");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    if ( v7 )
    {
      if ( _objc_msgSend(v7, "count") )
      {
        -[ThumbnailBaseTableViewController setIsFoucsOfSuperController:](self, "setIsFoucsOfSuperController:", 1LL);
        if ( (char *)_objc_msgSend(v7, v8) - 1 >= (char *)a3 )
        {
          v9 = _objc_msgSend(v7, "objectAtIndexedSubscript:", a3);
          v10 = objc_retainAutoreleasedReturnValue(v9);
          v11 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
          v39 = v10;
          if ( (unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v11) )
          {
            v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v13 = objc_retainAutoreleasedReturnValue(v12);
            v14 = _objc_msgSend(v39, "objectForKeyedSubscript:", v13);
            objc_retainAutoreleasedReturnValue(v14);
            v16 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v15);
            objc_retainAutoreleasedReturnValue(v16);
            v40 = v17;
            v18 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v17);
            v41 = objc_retainAutoreleasedReturnValue(v18);
            if ( _objc_msgSend(v19, "length") && _objc_msgSend(v41, "length") )
            {
              v21 = +[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
              v22 = objc_retainAutoreleasedReturnValue(v21);
              -[SelfStock addRecentlyScanStock:market:toBegin:](
                v22,
                "addRecentlyScanStock:market:toBegin:",
                v23,
                v41,
                0LL);
              -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v40);
              v25 = _objc_msgSend(
                      &OBJC_CLASS___NSDictionary,
                      "dictionaryWithObjectsAndKeys:",
                      v24,
                      CFSTR("StockCode"),
                      v41,
                      CFSTR("Market"),
                      0LL);
              v26 = objc_retainAutoreleasedReturnValue(v25);
              v27 = -[ThumbnailBaseTableViewController invokeRowSwitchCallBack](self, "invokeRowSwitchCallBack");
              v28 = objc_retainAutoreleasedReturnValue(v27);
              if ( v28 )
              {
                v29 = -[ThumbnailBaseTableViewController invokeRowSwitchCallBack](self, "invokeRowSwitchCallBack");
                v30 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v29);
                v30[2](v30, v26);
              }
            }
            v31 = _objc_msgSend(v20, "length");
            if ( v40 )
            {
              if ( v31 )
              {
                -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v40);
                v42[0] = CFSTR("StockCode");
                v43[0] = v32;
                v42[1] = off_1012E0FA8;
                v43[1] = v40;
                v33 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v43, v42, 2LL);
                objc_retainAutoreleasedReturnValue(v33);
                v34 = -[HXBaseTableViewController myTable](self, "myTable");
                v35 = objc_retainAutoreleasedReturnValue(v34);
                -[HXBaseTableView setParamsDic:](v35, "setParamsDic:", v36);
              }
            }
          }
        }
      }
    }
  }
}

//----- (0000000100028C75) ----------------------------------------------------
id __cdecl -[ThumbnailAHTableViewController getFilterMarketsAndCodes:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  SEL v25; // [rsp+50h] [rbp-110h]
  SEL v26; // [rsp+58h] [rbp-108h]
  SEL v29; // [rsp+70h] [rbp-F0h]
  SEL v30; // [rsp+78h] [rbp-E8h]
  SEL v31; // [rsp+80h] [rbp-E0h]
  SEL v32; // [rsp+88h] [rbp-D8h]
  SEL v34; // [rsp+98h] [rbp-C8h]
  id obj; // [rsp+A0h] [rbp-C0h]

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v36 = objc_retainAutoreleasedReturnValue(v5);
    v21 = 0LL;
    v22 = 0LL;
    v23 = 0LL;
    v24 = 0LL;
    v33 = v4;
    obj = objc_retain(v4);
    v6 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v37, 16LL);
    if ( v6 )
    {
      v7 = (__int64)v6;
      v27 = *(_QWORD *)v22;
LABEL_5:
      v25 = "class";
      v26 = "isKindOfClass:";
      v29 = "getCodeString:";
      v30 = "getMarketString:";
      v34 = "length";
      v31 = "dictionaryWithObjectsAndKeys:";
      v32 = "addObject:";
      if ( !v7 )
        v7 = 1LL;
      v28 = v7;
      v8 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v22 != v27 )
          objc_enumerationMutation(obj);
        v9 = *(void **)(*((_QWORD *)&v21 + 1) + 8 * v8);
        v10 = _objc_msgSend(&OBJC_CLASS___NSString, v25);
        if ( !(unsigned __int8)_objc_msgSend(v9, v26, v10) )
          break;
        v11 = objc_retain(v9);
        v12 = _objc_msgSend(&OBJC_CLASS___HXTools, v29, v11);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v15 = _objc_msgSend(&OBJC_CLASS___HXTools, v30, v14);
        v16 = objc_retainAutoreleasedReturnValue(v15);
        if ( _objc_msgSend(v13, v34) && _objc_msgSend(v16, v34) )
        {
          v17 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v31, v13, CFSTR("StockCode"), v16, CFSTR("Market"), 0LL);
          v18 = objc_retainAutoreleasedReturnValue(v17);
          _objc_msgSend(v36, v32, v18);
        }
        if ( v28 == ++v8 )
        {
          v7 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v21, v37, 16LL);
          if ( v7 )
            goto LABEL_5;
          break;
        }
      }
    }
    v4 = v33;
  }
  else
  {
    v36 = 0LL;
  }
  return objc_autoreleaseReturnValue(v36);
}

//----- (0000000100028FAC) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailAHTableViewController blockID](ThumbnailAHTableViewController *self, SEL a2)
{
  return self->super._quotaBegin;
}

//----- (0000000100028FBD) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setBlockID:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  self->super._quotaBegin = a3;
}

//----- (0000000100028FCE) ----------------------------------------------------
double __cdecl -[ThumbnailAHTableViewController exchangeRate](ThumbnailAHTableViewController *self, SEL a2)
{
  return *(double *)&self->super._quotaCount;
}

//----- (0000000100028FE0) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setExchangeRate:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        double a3)
{
  *(double *)&self->super._quotaCount = a3;
}

//----- (0000000100028FF2) ----------------------------------------------------
char __cdecl -[ThumbnailAHTableViewController isNeedToOrderHK](ThumbnailAHTableViewController *self, SEL a2)
{
  return self->super._count;
}

//----- (0000000100029003) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setIsNeedToOrderHK:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super._count) = a3;
}

//----- (0000000100029013) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setGangGuTableViewDataArray:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._quotaIndex, a3);
}

//----- (0000000100029027) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setHuShenCodes:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._visibleY, a3);
}

//----- (000000010002903B) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setHkCodes:](ThumbnailAHTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super._invokeRowSwitchCallBack, a3);
}

//----- (000000010002904F) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setOrderCodeMArrayAHHK:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._thumbTableSrcollDirection, a3);
}

//----- (0000000100029063) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController setTableRequestModuleForHK:](
        ThumbnailAHTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._countDownTimerForScroll, a3);
}

//----- (0000000100029077) ----------------------------------------------------
void __cdecl -[ThumbnailAHTableViewController .cxx_destruct](ThumbnailAHTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._countDownTimerForScroll, 0LL);
  objc_storeStrong((id *)&self->super._thumbTableSrcollDirection, 0LL);
  objc_storeStrong(&self->super._invokeRowSwitchCallBack, 0LL);
  objc_storeStrong((id *)&self->super._visibleY, 0LL);
  objc_storeStrong((id *)&self->super._quotaIndex, 0LL);
}

