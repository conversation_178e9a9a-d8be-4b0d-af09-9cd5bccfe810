//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSString;

@interface ZhongCangStockModel : NSObject
{
    NSString *_code;
    NSString *_market;
    double _value;
    long long _companyCount;
    double _rate;
    double _stockCount;
}


@property(nonatomic) double stockCount; // @synthesize stockCount=_stockCount;
@property(nonatomic) double rate; // @synthesize rate=_rate;
@property(nonatomic) long long companyCount; // @synthesize companyCount=_companyCount;
@property(nonatomic) double value; // @synthesize value=_value;
@property(retain, nonatomic) NSString *market; // @synthesize market=_market;
@property(retain, nonatomic) NSString *code; // @synthesize code=_code;

@end

