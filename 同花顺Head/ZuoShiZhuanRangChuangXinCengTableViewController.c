ZuoShiZhuanRangChuangXinCengTableViewController *__cdecl -[ZuoShiZhuanRangChuangXinCengTableViewController initWithNibName:bundle:](
        ZuoShiZhuanRangChuangXinCengTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ZuoShiZhuanRangChuangXinCengTableViewController *v4; // rax
  ZuoShiZhuanRangChuangXinCengTableViewController *v5; // rbx

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangChuangXinCengTableViewController;
  v4 = -[HXBaseTableViewController initWithNibName:bundle:](&v7, "initWithNibName:bundle:", a3, a4);
  v5 = v4;
  if ( v4 )
  {
    -[QuoteBaseTableViewController setBlockID:](v4, "setBlockID:", 52183LL);
    -[QuoteBaseTableViewController setTableID:](v5, "setTableID:", 723203LL);
    -[QuoteBaseTableViewController setTableInfo:](v5, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang_ChuangXinCeng"));
  }
  return v5;
}

//----- (00000001006B244D) ----------------------------------------------------
void __cdecl -[ZuoShiZhuanRangChuangXinCengTableViewController viewDidLoad](
        ZuoShiZhuanRangChuangXinCengTableViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangChuangXinCengTableViewController;
  -[XinSanBanBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 52183LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 723203LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang_ChuangXinCeng"));
}

