void __cdecl -[ZhiBiaoGuViewController viewDidLoad](ZhiBiaoGuViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhiBiaoGuViewController;
  -[BanKuaiReDianBaseViewController viewDidLoad](&v2, "viewDidLoad");
  -[ZhiBiaoGuViewController initAllView](self, "initAllView");
  -[ZhiBiaoGuViewController initActionBlock](self, "initActionBlock");
}

//----- (000000010032CB0C) ----------------------------------------------------
ZhiBiaoGuTableViewController *__cdecl -[ZhiBiaoGuViewController zhiBiaoGuTableVC](
        ZhiBiaoGuViewController *self,
        SEL a2)
{
  id autounbinder; // rdi

  autounbinder = self->super.super._autounbinder;
  if ( !autounbinder )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhiBiaoGuTableViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZhiBiaoGuTableViewController"), 0LL);
    v6 = self->super.super._autounbinder;
    self->super.super._autounbinder = v5;
    autounbinder = self->super.super._autounbinder;
  }
  return (ZhiBiaoGuTableViewController *)objc_retainAutoreleaseReturnValue(autounbinder);
}

//----- (000000010032CB66) ----------------------------------------------------
HangYeGeGuTableController *__cdecl -[ZhiBiaoGuViewController hangYeGeGuTableVC](ZhiBiaoGuViewController *self, SEL a2)
{
  NSString *designNibBundleIdentifier; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  designNibBundleIdentifier = self->super.super._designNibBundleIdentifier;
  if ( !designNibBundleIdentifier )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HangYeGeGuTableController);
    v5 = (NSString *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("HangYeGeGuTableController"), 0LL);
    v6 = self->super.super._designNibBundleIdentifier;
    self->super.super._designNibBundleIdentifier = v5;
    designNibBundleIdentifier = self->super.super._designNibBundleIdentifier;
  }
  return (HangYeGeGuTableController *)objc_retainAutoreleaseReturnValue(designNibBundleIdentifier);
}

//----- (000000010032CBC0) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController setGeGuSeletedRowDidChangedBlock:](ZhiBiaoGuViewController *self, SEL a2, id a3)
{
  id privateData; // rdi
  HangYeGeGuTableController *v7; // rax
  HangYeGeGuTableController *v8; // rbx

  if ( a3 )
  {
    v4 = objc_retain(a3);
    v5 = objc_retainBlock(v4);
    privateData = self->super.super.__privateData;
    self->super.super.__privateData = v5;
    v7 = -[ZhiBiaoGuViewController hangYeGeGuTableVC](self, "hangYeGeGuTableVC");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    -[HangYeGeGuTableController setSeletedRowDidChangedBlock:](v8, "setSeletedRowDidChangedBlock:", v4);
    v9(v4);
    v10(v8);
  }
}

//----- (000000010032CC4A) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController keyDownFromSuper:](ZhiBiaoGuViewController *self, SEL a2, id a3)
{
  NSString *designNibBundleIdentifier; // r15

  v3 = 10LL;
  designNibBundleIdentifier = self->super.super._designNibBundleIdentifier;
  v5 = objc_retain(a3);
  if ( !(unsigned __int8)_objc_msgSend(designNibBundleIdentifier, "isFocus") )
    v3 = 9LL;
  v6((&self->super.super.super.super.isa)[v3], "keyDownFromSuper:", v5);
}

//----- (000000010032CCBA) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController refreshAllModules](ZhiBiaoGuViewController *self, SEL a2)
{
  _objc_msgSend(self->super.super._autounbinder, "refreshAllModules");
  _objc_msgSend(self->super.super._designNibBundleIdentifier, "refreshAllModules");
}

//----- (000000010032CD03) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController initAllView](ZhiBiaoGuViewController *self, SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v3; // r15

  v2 = -[BanKuaiReDianBaseViewController quanBuHangYeView](self, "quanBuHangYeView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "zhiBiaoGuTableVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "view");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v3, "addSubview:", v8);
  v10 = _objc_msgSend(v9, "hangYeGeGuView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = _objc_msgSend(v12, "hangYeGeGuTableVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(v14, "view");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v11, "addSubview:", v16);
  v18 = _objc_msgSend(v17, "zhiBiaoGuTableVC");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "view");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v21, "mas_makeConstraints:");
  objc_unsafeClaimAutoreleasedReturnValue(v22);
  v24 = _objc_msgSend(v23, "hangYeGeGuTableVC");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "view");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28 = _objc_msgSend(v27, "mas_makeConstraints:");
  objc_unsafeClaimAutoreleasedReturnValue(v28);
}

//----- (000000010032CF10) ----------------------------------------------------
void __fastcall sub_10032CF10(__int64 a1, void *a2)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12

  v2 = _objc_msgSend(a2, "top");
  v22 = objc_retainAutoreleasedReturnValue(v2);
  v4 = v3(v22, "right");
  v23 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5(v23, "bottom");
  v24 = objc_retainAutoreleasedReturnValue(v6);
  v8 = v7(v24, "left");
  v25 = objc_retainAutoreleasedReturnValue(v8);
  v10 = v9(v25, "equalTo");
  v11 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(*(id *)(a1 + 32), "quanBuHangYeView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = (void *)v11[2](v11, v14);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = v17(v16, "offset");
  v19 = (__int64 (__fastcall **)(id, double))objc_retainAutoreleasedReturnValue(v18);
  v20 = (void *)v19[2](v19, 0.0);
  objc_unsafeClaimAutoreleasedReturnValue(v20);
}

//----- (000000010032D04C) ----------------------------------------------------
void __fastcall sub_10032D04C(__int64 a1, void *a2)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12

  v2 = _objc_msgSend(a2, "top");
  v22 = objc_retainAutoreleasedReturnValue(v2);
  v4 = v3(v22, "right");
  v23 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5(v23, "bottom");
  v24 = objc_retainAutoreleasedReturnValue(v6);
  v8 = v7(v24, "left");
  v25 = objc_retainAutoreleasedReturnValue(v8);
  v10 = v9(v25, "equalTo");
  v11 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(*(id *)(a1 + 32), "hangYeGeGuView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = (void *)v11[2](v11, v14);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = v17(v16, "offset");
  v19 = (__int64 (__fastcall **)(id, double))objc_retainAutoreleasedReturnValue(v18);
  v20 = (void *)v19[2](v19, 0.0);
  objc_unsafeClaimAutoreleasedReturnValue(v20);
}

//----- (000000010032D188) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController initActionBlock](ZhiBiaoGuViewController *self, SEL a2)
{
  _QWORD v9[4]; // [rsp+8h] [rbp-A8h] BYREF
  _QWORD v11[4]; // [rsp+30h] [rbp-80h] BYREF
  _QWORD v13[4]; // [rsp+58h] [rbp-58h] BYREF
  id to; // [rsp+78h] [rbp-38h] BYREF
  id location[6]; // [rsp+80h] [rbp-30h] BYREF

  objc_initWeak(location, self);
  v13[0] = _NSConcreteStackBlock;
  v13[1] = 3254779904LL;
  v13[2] = sub_10032D333;
  v13[3] = &unk_1012DB1D0;
  objc_copyWeak(&to, location);
  v3 = _objc_msgSend(v2, "hangYeGeGuTableVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setHidenViewBlock:", v13);
  v9[0] = _NSConcreteStackBlock;
  v9[1] = 3254779904LL;
  v9[2] = sub_10032D450;
  v9[3] = &unk_1012DB1D0;
  objc_copyWeak(&v10, location);
  _objc_msgSend(v5, "setSelectedBlock:", v9);
  v11[0] = _NSConcreteStackBlock;
  v11[1] = 3254779904LL;
  v11[2] = sub_10032D4DD;
  v11[3] = &unk_1012DC1F0;
  objc_copyWeak(&v12, location);
  v7 = _objc_msgSend(v6, "zhiBiaoGuTableVC");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "setPostZhiBiaoGuDidChangeBlock:", v11);
  objc_destroyWeak(&v12);
  objc_destroyWeak(&v10);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (000000010032D333) ----------------------------------------------------
void __fastcall sub_10032D333(__int64 a1, char a2, double a3)
{
  id *v3; // r13
  id WeakRetained; // rax

  v3 = (id *)(a1 + 32);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v5 = WeakRetained;
  if ( a2 )
  {
    v6 = _objc_msgSend(WeakRetained, "view");
    v7 = (const char *)objc_retainAutoreleasedReturnValue(v6);
    v8 = (char *)v7;
    if ( v7 )
    {
      objc_msgSend_stret(&v12, v7, "frame");
      v9 = *((double *)&v13 + 1);
    }
    else
    {
      v13 = 0LL;
      v12 = 0LL;
      v9 = 0.0;
    }
    v14 = v9 - 24.0;
  }
  else
  {
    _objc_msgSend(WeakRetained, "topTableHeight");
    v14 = a3;
  }
  v10 = objc_loadWeakRetained(v3);
  _objc_msgSend(v10, "setBottomTableIsHiden:", (unsigned int)a2);
  v11 = objc_loadWeakRetained(v3);
  _objc_msgSend(v11, "changeViewsFrameFromTopViewHeight:", v14);
}

//----- (000000010032D450) ----------------------------------------------------
void __fastcall sub_10032D450(__int64 a1, unsigned int a2)
{
  id WeakRetained; // r15

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "hangYeGeGuTableVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "closeViewBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setIsSelected:", a2);
}

//----- (000000010032D4DD) ----------------------------------------------------
__int64 __fastcall sub_10032D4DD(__int64 a1, void *a2)
{
  SEL v2; // r12
  NSDictionary *v45; // rax
  NSDictionary *v46; // rbx
  id WeakRetained; // rax
  unsigned int v56; // eax
  _QWORD v80[21]; // [rsp+B0h] [rbp-180h] BYREF
  _QWORD v81[21]; // [rsp+158h] [rbp-D8h] BYREF

  v80[0] = CFSTR("USHI1B0300");
  v79 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, v2, 61322LL);
  v59 = objc_retainAutoreleasedReturnValue(v3);
  v81[0] = v59;
  v80[1] = CFSTR("USZI399300");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, v4, 61322LL);
  v60 = objc_retainAutoreleasedReturnValue(v5);
  v81[1] = v60;
  v80[2] = CFSTR("USZI399330");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, v6, 205LL);
  v61 = objc_retainAutoreleasedReturnValue(v7);
  v81[2] = v61;
  v80[3] = CFSTR("USHI1B0007");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, v8, 204LL);
  v62 = objc_retainAutoreleasedReturnValue(v9);
  v81[3] = v62;
  v80[4] = CFSTR("USHI1B0016");
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, v10, 203LL);
  v63 = objc_retainAutoreleasedReturnValue(v11);
  v81[4] = v63;
  v80[5] = CFSTR("USZI399005");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, v12, 52791LL);
  v64 = objc_retainAutoreleasedReturnValue(v13);
  v81[5] = v64;
  v80[6] = CFSTR("USHI1B0043");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, v14, 52858LL);
  v65 = objc_retainAutoreleasedReturnValue(v15);
  v81[6] = v65;
  v80[7] = CFSTR("USHI1B0044");
  v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, v16, 52857LL);
  v66 = objc_retainAutoreleasedReturnValue(v17);
  v81[7] = v66;
  v80[8] = CFSTR("USHI1B0045");
  v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, v18, 52854LL);
  v67 = objc_retainAutoreleasedReturnValue(v19);
  v81[8] = v67;
  v80[9] = CFSTR("USHI1B0046");
  v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, v20, 52856LL);
  v68 = objc_retainAutoreleasedReturnValue(v21);
  v81[9] = v68;
  v80[10] = CFSTR("USHI1B0047");
  v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, v22, 52853LL);
  v69 = objc_retainAutoreleasedReturnValue(v23);
  v81[10] = v69;
  v80[11] = CFSTR("USHI1B0061");
  v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, v24, 52852LL);
  v70 = objc_retainAutoreleasedReturnValue(v25);
  v81[11] = v70;
  v80[12] = CFSTR("USHI1B0006");
  v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, v26, 52851LL);
  v71 = objc_retainAutoreleasedReturnValue(v27);
  v81[12] = v71;
  v80[13] = CFSTR("USHI1B0001");
  v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, v28, 52850LL);
  v72 = objc_retainAutoreleasedReturnValue(v29);
  v81[13] = v72;
  v80[14] = CFSTR("USHI1B0002");
  v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, v30, 52849LL);
  v73 = objc_retainAutoreleasedReturnValue(v31);
  v81[14] = v73;
  v80[15] = CFSTR("USHI1B0004");
  v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, v32, 52848LL);
  v75 = objc_retainAutoreleasedReturnValue(v33);
  v81[15] = v75;
  v80[16] = CFSTR("USHI1B0005");
  v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, v34, 52847LL);
  v76 = objc_retainAutoreleasedReturnValue(v35);
  v81[16] = v76;
  v80[17] = CFSTR("USHI1B0020");
  v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, v36, 52846LL);
  v77 = objc_retainAutoreleasedReturnValue(v37);
  v81[17] = v77;
  v80[18] = CFSTR("USHI1B0015");
  v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, v38, 52845LL);
  v78 = objc_retainAutoreleasedReturnValue(v39);
  v81[18] = v78;
  v80[19] = CFSTR("USHI1B0905");
  v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, v40, 55442LL);
  v42 = objc_retainAutoreleasedReturnValue(v41);
  v81[19] = v42;
  v80[20] = CFSTR("USHI1B0688");
  v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, v43, 52134LL);
  v81[20] = objc_retainAutoreleasedReturnValue(v44);
  v45 = (NSDictionary *)_objc_msgSend(
                          &OBJC_CLASS___NSDictionary,
                          "dictionaryWithObjects:forKeys:count:",
                          v81,
                          v80,
                          21LL);
  v46 = objc_retainAutoreleasedReturnValue(v45);
  v47 = _objc_msgSend(v46, "objectForKeyedSubscript:", v79);
  v74 = objc_retainAutoreleasedReturnValue(v47);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v50 = _objc_msgSend(WeakRetained, "hangYeGeGuTableVC");
  v51 = objc_retainAutoreleasedReturnValue(v50);
  _objc_msgSend(v51, "setLinkCode:", v79);
  v53 = objc_loadWeakRetained((id *)(a1 + 32));
  v54 = _objc_msgSend(v53, "hangYeGeGuTableVC");
  v55 = objc_retainAutoreleasedReturnValue(v54);
  v56 = (__int64)_objc_msgSend(v74, "intValue");
  _objc_msgSend(v55, "requestForHangYeGeGuWithBlockID:", v56);
  return __stack_chk_guard;
}

//----- (000000010032DB2B) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController setZhiBiaoGuTableVC:](ZhiBiaoGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super._autounbinder, a3);
}

//----- (000000010032DB3F) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController setHangYeGeGuTableVC:](ZhiBiaoGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._designNibBundleIdentifier, a3);
}

//----- (000000010032DB53) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuViewController geGuSeletedRowDidChangedBlock](ZhiBiaoGuViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 88LL, 0);
}

//----- (000000010032DB66) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuViewController .cxx_destruct](ZhiBiaoGuViewController *self, SEL a2)
{
  objc_storeStrong(&self->super.super.__privateData, 0LL);
  objc_storeStrong((id *)&self->super.super._designNibBundleIdentifier, 0LL);
  objc_storeStrong(&self->super.super._autounbinder, 0LL);
}

