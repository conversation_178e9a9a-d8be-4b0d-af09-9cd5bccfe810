//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

#import "HXTipManagerDataSource-Protocol.h"

@class HXBaseView, HXButton, NSArray, NSButton, NSMutableArray, NSString;

@interface ZiXunMenuViewController : NSViewController <HXTipManagerDataSource>
{
    BOOL _isGetMeiGuKaiHuInfo;
    HXBaseView *_contentView;
    NSButton *_hideCtrlBtn;
    unsigned long long _zixunType;
    HXButton *_selectedBtn;
    NSArray *_menuBtns;
    NSMutableArray *_tipArray;
    HXButton *_operationBtn;
    NSString *_kaiHuUrlStr;
}


@property(nonatomic) BOOL isGetMeiGuKaiHuInfo; // @synthesize isGetMeiGuKaiHuInfo=_isGetMeiGuKaiHuInfo;
@property(retain, nonatomic) NSString *kaiHuUrlStr; // @synthesize kaiHuUrlStr=_kaiHuUrlStr;
@property __weak HXButton *operationBtn; // @synthesize operationBtn=_operationBtn;
@property(retain, nonatomic) NSMutableArray *tipArray; // @synthesize tipArray=_tipArray;
@property(retain, nonatomic) NSArray *menuBtns; // @synthesize menuBtns=_menuBtns;
@property(retain, nonatomic) HXButton *selectedBtn; // @synthesize selectedBtn=_selectedBtn;
@property(nonatomic) unsigned long long zixunType; // @synthesize zixunType=_zixunType;
@property __weak NSButton *hideCtrlBtn; // @synthesize hideCtrlBtn=_hideCtrlBtn;
@property(retain) HXBaseView *contentView; // @synthesize contentView=_contentView;
- (void)showL2TipWithButtonTitle:(id)arg1;
- (id)tipsToShow;
- (void)displayMeiGuKaiHuBtn:(BOOL)arg1;
- (void)setDefaultSelectedMenuBtn;
- (void)resetHideCtrlBtn:(BOOL)arg1;
- (void)resetSelectedBtn:(id)arg1;
- (void)createMenuBtns:(id)arg1;
- (void)operationBtnAction:(id)arg1;
- (void)setMeiGuKaiHuBtnState;
- (void)setDefaultHideCtrlBtn;
- (void)initContentView;
- (void)viewDidAppear;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

