//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

#import "NSXMLParserDelegate-Protocol.h"
#import "ReceiveDispatchData-Protocol.h"

@class NSString;

@interface ZiXunTreeRequestModule : NSObject <ReceiveDispatchData, NSXMLParserDelegate>
{
    BOOL _hasLocalTreeDatas;
    NSString *_geGuZiXunURL;
    NSString *_gongGaoURL;
    NSString *_geGuYanBaoURL;
    NSString *_shiShiJiePanURL;
}

+ (id)shareInstance;

@property(copy, nonatomic) NSString *shiShiJiePanURL; // @synthesize shiShiJiePanURL=_shiShiJiePanURL;
@property(copy, nonatomic) NSString *geGuYanBaoURL; // @synthesize geGuYanBaoURL=_geGuYanBaoURL;
@property(copy, nonatomic) NSString *gongGaoURL; // @synthesize gongGaoURL=_gongGaoURL;
@property(copy, nonatomic) NSString *geGuZiXunURL; // @synthesize geGuZiXunURL=_geGuZiXunURL;
@property(nonatomic) BOOL hasLocalTreeDatas; // @synthesize hasLocalTreeDatas=_hasLocalTreeDatas;
- (id)getURL:(id)arg1;
- (id)getName:(id)arg1;
- (void)filterZiXunURL:(id)arg1;
- (void)parserZiXunTree:(id)arg1;
- (void)unregisterInMacDataServiceWithRequstInstanceId:(long long)arg1;
- (void)failToReceiveStuffData:(long long)arg1;
- (void)receiveStuffData:(id)arg1;
- (id)getHttpRequestURL:(long long)arg1;
- (id)getShiShiJiePanURL;
- (id)getGeGuYanBaoURL;
- (id)getNormalGongGaoURL;
- (id)getMeiGuGongGaoURL;
- (id)getGeGuZiXunURL;
- (void)requestZiXunTree;
- (id)init;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

