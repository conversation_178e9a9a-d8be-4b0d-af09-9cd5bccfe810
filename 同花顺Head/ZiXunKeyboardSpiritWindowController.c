id __cdecl +[Zi<PERSON>un<PERSON>eyboardSpiritWindowController sharedInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100A7FA89;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D35A0 != -1 )
    dispatch_once(&qword_1016D35A0, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3598);
}

//----- (0000000100A7FA89) ----------------------------------------------------
void __fastcall sub_100A7FA89(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "initWithWindowNibName:", CFSTR("ZiXunKeyboardSpiritWindowController"));
  v3 = qword_1016D3598;
  qword_1016D3598 = v2;
}

//----- (0000000100A7FAC2) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController windowDidLoad](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{

  v24.receiver = self;
  v24.super_class = (Class)&OBJC_CLASS___ZiXunKeyboardSpiritWindowController;
  objc_msgSendSuper2(&v24, "windowDidLoad");
  v3 = _objc_msgSend(v2, "window");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setLevel:", 3LL);
  v5 = _objc_msgSend(&OBJC_CLASS___NSColor, "clearColor");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(v7, "window");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setBackgroundColor:", v6);
  v11 = _objc_msgSend(v10, "window");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v12, "contentView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setWantsLayer:", 1LL);
  v16 = _objc_msgSend(v15, "window");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "contentView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = (void *)v20(v19, "layer");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23(v22, "setCornerRadius:", 5.0);
}

//----- (0000000100A7FC59) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController close](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  NSString *frameAutosaveName; // rdi

  v3 = _objc_msgSend(self, "window");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)_objc_msgSend(v4, "isVisible");
  if ( v5 )
  {
    if ( self->super._frameAutosaveName )
    {
      _objc_msgSend(&OBJC_CLASS___NSEvent, "removeMonitor:");
      frameAutosaveName = self->super._frameAutosaveName;
      self->super._frameAutosaveName = 0LL;
    }
    -[ZiXunKeyboardSpiritWindowController backToMain](self, "backToMain");
    v7.receiver = self;
    v7.super_class = (Class)&OBJC_CLASS___ZiXunKeyboardSpiritWindowController;
    objc_msgSendSuper2(&v7, "close");
  }
}

//----- (0000000100A7FD12) ----------------------------------------------------
HXTabbarController *__cdecl -[ZiXunKeyboardSpiritWindowController tabbarController](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{

  v3 = *(void **)&self->super._wcFlags;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._wcFlags;
    *(_QWORD *)&self->super._wcFlags = v5;
    v7 = _objc_msgSend(self, "window");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = (void *)v9(v8, "contentView");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(*(_QWORD *)&self->super._wcFlags, "setView:", v11);
    v14 = v13;
    v15 = (void *)v13(self, "keyboardVC");
    v23 = objc_retainAutoreleasedReturnValue(v15);
    v16 = (void *)v14(self, "promptVC");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    *(_QWORD *)(v18 + 8) = v17;
    v19 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64, __int64))v14)(
                    &OBJC_CLASS___NSArray,
                    "arrayWithObjects:count:",
                    v18,
                    2LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(*(id *)&self->super._wcFlags, "setViewControllers:", v20);
    v3 = *(Class *)((char *)&self->super.super.super.isa + v21);
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100A7FE8C) ----------------------------------------------------
ZiXunKeyboradSpiritViewController *__cdecl -[ZiXunKeyboardSpiritWindowController keyboardVC](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  NSArray *topLevelObjects; // rdi
  objc_class *v5; // rax
  _QWORD v11[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  topLevelObjects = self->super._topLevelObjects;
  if ( !topLevelObjects )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunKeyboradSpiritViewController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZiXunKeyboradSpiritViewController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    objc_initWeak(location, self);
    v11[0] = _NSConcreteStackBlock;
    v11[1] = 3254779904LL;
    v11[2] = sub_100A7FF89;
    v11[3] = &unk_1012DB1D0;
    objc_copyWeak(&to, location);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v8), "setShowNoResultViewBlock:", v11);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    topLevelObjects = *(NSArray **)((char *)&self->super.super.super.isa + v9);
  }
  return (ZiXunKeyboradSpiritViewController *)objc_retainAutoreleaseReturnValue(topLevelObjects);
}

//----- (0000000100A7FF89) ----------------------------------------------------
void __fastcall sub_100A7FF89(__int64 a1, char a2)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "tabbarController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5(v4, "setSelectedIndex:", a2 != 0);
}

//----- (0000000100A7FFF6) ----------------------------------------------------
HXKeyboardSpiritPromptViewController *__cdecl -[ZiXunKeyboardSpiritWindowController promptVC](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  id owner; // rdi

  owner = self->super._owner;
  if ( !owner )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXKeyboardSpiritPromptViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super._owner;
    self->super._owner = v5;
    _objc_msgSend(self->super._owner, "setAddToGroup:", 0LL);
    owner = self->super._owner;
  }
  return (HXKeyboardSpiritPromptViewController *)objc_retainAutoreleaseReturnValue(owner);
}

//----- (0000000100A80064) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController keyDownThenRequest:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "tabbarController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setSelectedIndex:", 0LL);
  v8 = _objc_msgSend(v7, "keyboardVC");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "keyDownThenRequest:", v3);
}

//----- (0000000100A80103) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController voiceRequest:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "tabbarController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setSelectedIndex:", 0LL);
  v8 = _objc_msgSend(v7, "keyboardVC");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "voiceRequest:", v3);
}

//----- (0000000100A801A2) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController textDidChange](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  HXSearchTextFieldView *v2; // rax
  HXSearchTextFieldView *v3; // r15
  HXSearchTextField *v4; // rax
  HXSearchTextField *v5; // rax
  HXTabbarController *v10; // rax
  HXTabbarController *v11; // rbx
  ZiXunKeyboradSpiritViewController *v12; // rax
  ZiXunKeyboradSpiritViewController *v13; // rbx

  v2 = -[ZiXunKeyboardSpiritWindowController keyboardTextFieldView](self, "keyboardTextFieldView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXSearchTextFieldView textField](v3, "textField");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "stringValue");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "length");
  if ( v8 )
  {
    v10 = -[ZiXunKeyboardSpiritWindowController tabbarController](self, "tabbarController");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    -[HXTabbarController setSelectedIndex:](v11, "setSelectedIndex:", 0LL);
    v12 = -[ZiXunKeyboardSpiritWindowController keyboardVC](self, "keyboardVC");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    -[ZiXunKeyboradSpiritViewController textDidChange](v13, "textDidChange");
    v14(v13);
  }
}

//----- (0000000100A802B7) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController windowDidShow](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  NSString *frameAutosaveName; // rdi
  NSString *v6; // rax
  NSString *v7; // rdi
  _QWORD v8[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  if ( self->super._frameAutosaveName )
  {
    _objc_msgSend(&OBJC_CLASS___NSEvent, "removeMonitor:");
    frameAutosaveName = self->super._frameAutosaveName;
    self->super._frameAutosaveName = 0LL;
  }
  objc_initWeak(location, self);
  v8[0] = _NSConcreteStackBlock;
  v8[1] = 3254779904LL;
  v8[2] = sub_100A803BB;
  v8[3] = &unk_1012DD650;
  objc_copyWeak(&to, location);
  v5 = _objc_msgSend(v4, "addLocalMonitorForEventsMatchingMask:handler:", 33554442LL, v8);
  v6 = (NSString *)objc_retainAutoreleasedReturnValue(v5);
  v7 = self->super._frameAutosaveName;
  self->super._frameAutosaveName = v6;
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100A803BB) ----------------------------------------------------
id __fastcall sub_100A803BB(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "localMouseEvent:", v2);
  return objc_autoreleaseReturnValue(v2);
}

//----- (0000000100A80408) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController localMouseEvent:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  CGFloat v3; // xmm0_8
  CGFloat v4; // xmm1_8
  bool v19; // r15
  CGRect rect; // [rsp+20h] [rbp-C0h] BYREF
  CGRect v36; // [rsp+40h] [rbp-A0h] BYREF
  CGPoint v39; // [rsp+90h] [rbp-50h]
  CGPoint point; // [rsp+A0h] [rbp-40h]

  objc_retain(a3);
  v41 = self;
  v5 = _objc_msgSend(self, "window");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "isVisible");
  if ( v7 )
  {
    v38 = v8;
    _objc_msgSend(v8, "locationInWindow");
    v39.x = v3;
    v39.y = v4;
    v9 = _objc_msgSend(v41, "window");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "contentView");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v12, "convertPoint:toView:", 0LL);
    point.x = v3;
    point.y = v4;
    v14 = _objc_msgSend(v41, "window");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(v15, "contentView");
    v17 = (const char *)objc_retainAutoreleasedReturnValue(v16);
    v18 = (char *)v17;
    if ( v17 )
      objc_msgSend_stret(&rect, v17, "bounds");
    else
      memset(&rect, 0, sizeof(rect));
    v19 = CGRectContainsPoint(rect, point);
    v8 = v38;
    if ( !v19 )
    {
      v21 = v41;
      v22 = _objc_msgSend(v41, "keyboardTextFieldView");
      v23 = objc_retainAutoreleasedReturnValue(v22);
      v24 = _objc_msgSend(v23, "superview");
      objc_retainAutoreleasedReturnValue(v24);
      v25 = _objc_msgSend(v21, "keyboardTextFieldView");
      v26 = (const char *)objc_retainAutoreleasedReturnValue(v25);
      v28 = (char *)v26;
      if ( v26 )
        objc_msgSend_stret(v37, v26, "frame");
      else
        memset(v37, 0, sizeof(v37));
      if ( v27 )
        objc_msgSend_stret(&v36, v27, "convertRect:toView:", 0LL);
      else
        memset(&v36, 0, sizeof(v36));
      if ( !CGRectContainsPoint(v36, v39) )
      {
        v30 = _objc_msgSend(v41, "keyboardTextFieldView");
        v31 = objc_retainAutoreleasedReturnValue(v30);
        v32 = _objc_msgSend(v31, "window");
        v33 = objc_retainAutoreleasedReturnValue(v32);
        _objc_msgSend(v33, "makeFirstResponder:", 0LL);
        _objc_msgSend(v34, "close");
      }
      v8 = v38;
    }
  }
}

//----- (0000000100A80718) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController backToMain](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r12
  NSString *v4; // rax
  NSString *v5; // rbx
  NSString *v7; // rax
  NSString *v8; // rax
  NSString *v9; // rbx
  NSString *v11; // rax
  __CFString *v32; // [rsp+18h] [rbp-58h]
  _QWORD v33[2]; // [rsp+20h] [rbp-50h] BYREF
  _QWORD v34[2]; // [rsp+30h] [rbp-40h] BYREF

  v2 = -[ZiXunKeyboardSpiritWindowController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( v3 )
  {
    v4 = -[ZiXunKeyboardSpiritWindowController stockName](self, "stockName");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    if ( !v5 )
    {
      v32 = CFSTR("名称/代码");
      goto LABEL_7;
    }
    v7 = -[ZiXunKeyboardSpiritWindowController stockCode](self, "stockCode");
    objc_retainAutoreleasedReturnValue(v7);
    v8 = -[ZiXunKeyboardSpiritWindowController stockName](self, "stockName");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = (NSString *)_objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@ %@"), v10, v9);
    v32 = objc_retainAutoreleasedReturnValue(v11);
  }
  else
  {
    v32 = CFSTR("名称/代码");
  }
LABEL_7:
  v12 = objc_alloc(&OBJC_CLASS___NSAttributedString);
  v33[0] = NSFontAttributeName;
  v13 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 12.0);
  v30 = objc_retainAutoreleasedReturnValue(v13);
  v34[0] = v30;
  v33[1] = NSForegroundColorAttributeName;
  v15 = (void *)v14(&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v31 = objc_retainAutoreleasedReturnValue(v15);
  v34[1] = v31;
  v17 = (void *)v16(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v34, v33, 2LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v20 = (void *)v19(v12, "initWithString:attributes:", v32, v18);
  v22 = (void *)v21(self, "keyboardTextFieldView");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = (void *)v24(v23, "textField");
  v27 = v26;
  v28 = objc_retainAutoreleasedReturnValue(v25);
  v27(v28, "setPlaceholderAttributedString:", v20);
}

//----- (0000000100A8097A) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController keyboardSpiritRowClicked](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  -[ZiXunKeyboardSpiritWindowController keyboardSpiritRowDoubleClicked](self, "keyboardSpiritRowDoubleClicked");
}

//----- (0000000100A8098C) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController keyboardSpiritRowDoubleClicked](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunKeyboardSpiritWindowController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedViewController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( (unsigned __int8)v7(v6, "respondsToSelector:", "keyboardSpiritRowDoubleClicked") )
    _objc_msgSend(v6, "performSelector:", "keyboardSpiritRowDoubleClicked");
}

//----- (0000000100A80A13) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController selectPrevious](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunKeyboardSpiritWindowController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedViewController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( (unsigned __int8)v7(v6, "respondsToSelector:", "selectPrevious") )
    _objc_msgSend(v6, "performSelector:", "selectPrevious");
}

//----- (0000000100A80A9A) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController selectNext](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunKeyboardSpiritWindowController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedViewController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( (unsigned __int8)v7(v6, "respondsToSelector:", "selectNext") )
    _objc_msgSend(v6, "performSelector:", "selectNext");
}

//----- (0000000100A80B21) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setKeyboardTextFieldView:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  ZiXunKeyboradSpiritViewController *v5; // rax
  ZiXunKeyboradSpiritViewController *v6; // rbx

  v3 = objc_retain(a3);
  objc_storeWeak(&self->super.super._nextResponder, v3);
  _objc_msgSend(v3, "setDelegate:", self);
  v4 = _objc_msgSend(v3, "textField");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = -[ZiXunKeyboardSpiritWindowController keyboardVC](self, "keyboardVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[ZiXunKeyboradSpiritViewController setKeyboardTextField:](v6, "setKeyboardTextField:", v7);
}

//----- (0000000100A80BD3) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setSearchResultBlock:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  ZiXunKeyboradSpiritViewController *v3; // rax
  ZiXunKeyboradSpiritViewController *v4; // rbx

  objc_retain(a3);
  v3 = -[ZiXunKeyboardSpiritWindowController keyboardVC](self, "keyboardVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[ZiXunKeyboradSpiritViewController setSearchResultBlock:](v4, "setSearchResultBlock:", v5);
}

//----- (0000000100A80C45) ----------------------------------------------------
HXSearchTextFieldView *__cdecl -[ZiXunKeyboardSpiritWindowController keyboardTextFieldView](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._nextResponder);
  return (HXSearchTextFieldView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100A80C5E) ----------------------------------------------------
NSMutableArray *__cdecl -[ZiXunKeyboardSpiritWindowController searchHistoryArray](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2)
{
  return (NSMutableArray *)self->super._window;
}

//----- (0000000100A80C6F) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setSearchHistoryArray:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._window, a3);
}

//----- (0000000100A80C83) ----------------------------------------------------
NSString *__cdecl -[ZiXunKeyboardSpiritWindowController stockCode](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  return self->super._windowNibName;
}

//----- (0000000100A80C94) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setStockCode:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._windowNibName, a3);
}

//----- (0000000100A80CA8) ----------------------------------------------------
NSString *__cdecl -[ZiXunKeyboardSpiritWindowController stockName](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  return (NSString *)&self->super._document->super.isa;
}

//----- (0000000100A80CB9) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setStockName:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._document, a3);
}

//----- (0000000100A80CCD) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setKeyboardVC:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, a3);
}

//----- (0000000100A80CE1) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setPromptVC:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super._owner, a3);
}

//----- (0000000100A80CF5) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setTabbarController:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._wcFlags, a3);
}

//----- (0000000100A80D09) ----------------------------------------------------
id __cdecl -[ZiXunKeyboardSpiritWindowController localEventMonitor](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  return self->super._frameAutosaveName;
}

//----- (0000000100A80D1A) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController setLocalEventMonitor:](
        ZiXunKeyboardSpiritWindowController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._frameAutosaveName, a3);
}

//----- (0000000100A80D2E) ----------------------------------------------------
void __cdecl -[ZiXunKeyboardSpiritWindowController .cxx_destruct](ZiXunKeyboardSpiritWindowController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._frameAutosaveName, 0LL);
  objc_storeStrong((id *)&self->super._wcFlags, 0LL);
  objc_storeStrong(&self->super._owner, 0LL);
  objc_storeStrong((id *)&self->super._topLevelObjects, 0LL);
  objc_storeStrong((id *)&self->super._document, 0LL);
  objc_storeStrong((id *)&self->super._windowNibName, 0LL);
  objc_storeStrong((id *)&self->super._window, 0LL);
  objc_destroyWeak(&self->super.super._nextResponder);
}

