void __cdecl -[XinSanBanStockPageContainerController viewDidLoad](XinSanBanStockPageContainerController *self, SEL a2)
{
  HXPageMainGraphTabContainer *v2; // rax
  HXPageMainGraphTabContainer *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  __CFString *v7; // [rsp+18h] [rbp-28h] BYREF

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___XinSanBanStockPageContainerController;
  -[HXPageBaseViewController viewDidLoad](&v6, "viewDidLoad");
  v2 = -[HXPageBaseViewController mainGraphNavigationVC](self, "mainGraphNavigationVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v7 = CFSTR("诊股");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v7, 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXPageMainGraphTabContainer disable:forTitles:](v3, "disable:forTitles:", 1LL, v5);
}

//----- (0000000100326C69) ----------------------------------------------------
id __cdecl -[XinSanBanStockPageContainerController nibName](XinSanBanStockPageContainerController *self, SEL a2)
{
  return CFSTR("HXPageBaseViewController");
}

//----- (0000000100326C76) ----------------------------------------------------
void __cdecl -[XinSanBanStockPageContainerController updatePageControllerView](
        XinSanBanStockPageContainerController *self,
        SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx

  v2 = -[HXBaseViewController market](self, "market");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "length");
  if ( v4 )
  {
    v5.receiver = self;
    v5.super_class = (Class)&OBJC_CLASS___XinSanBanStockPageContainerController;
    -[HXPageBaseViewController updatePageControllerView](&v5, "updatePageControllerView");
  }
}

//----- (0000000100326CE9) ----------------------------------------------------
void __cdecl -[XinSanBanStockPageContainerController actionForSelfStockUpdate](
        XinSanBanStockPageContainerController *self,
        SEL a2)
{
  ;
}

//----- (0000000100326CEF) ----------------------------------------------------
id __cdecl -[XinSanBanStockPageContainerController splitBottomViewHeightName](
        XinSanBanStockPageContainerController *self,
        SEL a2)
{
  return CFSTR("splitBottomViewHeightXinSanBanStock");
}

//----- (0000000100326CFC) ----------------------------------------------------
id __cdecl -[XinSanBanStockPageContainerController splitRightViewWidthName](
        XinSanBanStockPageContainerController *self,
        SEL a2)
{
  return CFSTR("splitRightViewWidthXinSanBanStock");
}

//----- (0000000100326D09) ----------------------------------------------------
id __cdecl -[XinSanBanStockPageContainerController strGeGuPankouType](
        XinSanBanStockPageContainerController *self,
        SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r15
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id result; // rax

  v2 = -[HXBaseViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "market");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = (unsigned __int8)v7(&OBJC_CLASS___HXTools, "isJingAStock:market:", v3, v6);
  v9(v3);
  result = CFSTR("JingShiStockPanKou");
  if ( !v8 )
    return CFSTR("XinSanBanStockPanKou");
  return result;
}

//----- (0000000100326D99) ----------------------------------------------------
void __cdecl -[XinSanBanStockPageContainerController F1KeyDown](XinSanBanStockPageContainerController *self, SEL a2)
{
  ;
}

//----- (0000000100326DA0) ----------------------------------------------------
void __fastcall sub_100326DA0(__int64 a1, _OWORD *a2)
{

  a2[7] = *(_OWORD *)(a1 + 136);
  a2[6] = *(_OWORD *)(a1 + 120);
  a2[5] = *(_OWORD *)(a1 + 104);
  a2[4] = *(_OWORD *)(a1 + 88);
  v2 = *(_OWORD *)(a1 + 24);
  v3 = *(_OWORD *)(a1 + 40);
  v4 = *(_OWORD *)(a1 + 56);
  a2[3] = *(_OWORD *)(a1 + 72);
  a2[2] = v4;
  a2[1] = v3;
  *a2 = v2;
  v5 = *(_OWORD *)(a1 + 168);
  v6 = *(_OWORD *)(a1 + 184);
  v7 = *(_OWORD *)(a1 + 200);
  a2[8] = *(_OWORD *)(a1 + 152);
  a2[9] = v5;
  a2[10] = v6;
  a2[11] = v7;
  a2[12] = *(_OWORD *)(a1 + 216);
  a2[13] = *(_OWORD *)(a1 + 232);
  a2[14] = *(_OWORD *)(a1 + 248);
  a2[15] = *(_OWORD *)(a1 + 264);
}

//----- (0000000100326E58) ----------------------------------------------------
__int64 __fastcall sub_100326E58(__int64 a1, __int64 a2, char a3)
{
  int v7; // r10d
  int v9; // r9d
  int v10; // r8d
  int v11; // esi
  int v12; // r10d
  int v14; // ecx
  int v17; // ebx
  int v18; // ecx
  __m128i si128; // xmm0
  __m128i v20; // xmm1
  __m128i v22; // xmm8
  __m128i v23; // xmm9
  __m128i v24; // xmm10
  __m128i v25; // xmm11
  __m128i v26; // xmm6
  __m128i v27; // xmm7
  __m128i v28; // xmm2
  __m128i v29; // xmm3
  __m128i v30; // xmm4
  unsigned __int64 v31; // rbx
  unsigned __int64 v33; // rdx
  __m128i v34; // xmm3
  __m128i v35; // xmm5
  __m128i v36; // xmm3

  for ( i = 0LL; i != 56; ++i )
    v38[i + 64] = (*(char *)(a2 + ((unsigned __int64)byte_1010D4C00[i] >> 3)) & word_1010D4B90[byte_1010D4C00[i] & 7]) != 0;
  for ( j = 0LL; j != 16; ++j )
  {
    v5 = (unsigned int)(30 - 2 * j);
    if ( a3 )
      v5 = (unsigned int)(2 * j);
    v6 = (unsigned int)(v5 + 1);
    *(_DWORD *)(a1 + 4 * v6 + 24) = 0;
    *(_DWORD *)(a1 + 4 * v5 + 24) = 0;
    v7 = byte_1010D4C40[j];
    for ( k = 0LL; k != 28; ++k )
    {
      v9 = v7 - 28 + k;
      if ( (unsigned int)(v7 + k) < 0x1C )
        v9 = v7 + k;
      v38[k] = v38[v9 + 64];
    }
    v10 = v7;
    v11 = v7 + 28;
    v12 = v7 + 28;
    for ( m = 0LL; m != 28; ++m )
    {
      v14 = v10 + m;
      if ( (unsigned int)(v12 + m) < 0x38 )
        v14 = v11 + m;
      v38[m + 28] = v38[v14 + 64];
    }
    v15 = 0LL;
    v16 = (char *)&unk_1010D4C50;
    v17 = 0;
    v18 = 0;
    do
    {
      if ( v38[*v16] )
      {
        v18 |= *(_DWORD *)((char *)&unk_1010D4BA0 + v15);
        *(_DWORD *)(a1 + 4 * v5 + 24) = v18;
      }
      if ( v38[v16[24]] )
      {
        v17 |= *(_DWORD *)((char *)&unk_1010D4BA0 + v15);
        *(_DWORD *)(a1 + 4 * v6 + 24) = v17;
      }
      ++v16;
      v15 += 4LL;
    }
    while ( v15 != 96 );
  }
  si128 = _mm_load_si128((const __m128i *)&xmmword_1010D4B10);
  v20 = _mm_load_si128((const __m128i *)&xmmword_1010D4B20);
  v21 = 0LL;
  v22 = _mm_load_si128((const __m128i *)&xmmword_1010D4B30);
  v23 = _mm_load_si128((const __m128i *)&xmmword_1010D4B40);
  v24 = _mm_load_si128((const __m128i *)&xmmword_1010D4B50);
  v25 = _mm_load_si128((const __m128i *)&xmmword_1010D4B60);
  v26 = _mm_load_si128((const __m128i *)&xmmword_1010D4B70);
  v27 = _mm_load_si128((const __m128i *)&xmmword_1010D4B80);
  do
  {
    v28 = _mm_unpacklo_epi64(
            _mm_unpacklo_epi32(
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 8 * v21 + 24)),
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 8 * v21 + 32))),
            _mm_unpacklo_epi32(
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 8 * v21 + 40)),
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 8 * v21 + 48))));
    v29 = _mm_or_si128(si128, v22);
    v30 = _mm_or_si128(v20, v22);
    v31 = _mm_shuffle_epi32(v30, 238).u64[0];
    v32 = v29.i64[0];
    v33 = _mm_shuffle_epi32(v29, 238).u64[0];
    v34 = _mm_unpacklo_epi64(
            _mm_unpacklo_epi32(
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 4 * v30.i64[0] + 24)),
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 4 * v31 + 24))),
            _mm_unpacklo_epi32(
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 4 * v29.i64[0] + 24)),
              _mm_cvtsi32_si128(*(_DWORD *)(a1 + 4 * v33 + 24))));
    v35 = _mm_or_si128(
            _mm_and_si128(_mm_srli_epi32(v34, 6u), v26),
            _mm_or_si128(
              _mm_and_si128(_mm_srli_epi32(v34, 0xAu), v25),
              _mm_or_si128(_mm_and_si128(_mm_slli_epi32(v28, 0xAu), v24), _mm_and_si128(_mm_slli_epi32(v28, 6u), v23))));
    *(_DWORD *)(a1 + 8 * v21 + 24) = _mm_cvtsi128_si32(v35);
    *(_DWORD *)(a1 + 8 * v21 + 32) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v35, 85));
    *(_DWORD *)(a1 + 8 * v21 + 40) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v35, 238));
    *(_DWORD *)(a1 + 8 * v21 + 48) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v35, 255));
    v36 = _mm_or_si128(
            _mm_or_si128(
              _mm_and_si128(v34, v26),
              _mm_or_si128(
                _mm_and_si128(_mm_slli_epi32(v28, 0x10u), v24),
                _mm_and_si128(_mm_slli_epi32(v28, 0xCu), v23))),
            _mm_and_si128(_mm_srli_epi32(v34, 4u), v25));
    *(_DWORD *)(a1 + 4 * v30.i64[0] + 24) = _mm_cvtsi128_si32(v36);
    *(_DWORD *)(a1 + 4 * v31 + 24) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v36, 85));
    *(_DWORD *)(a1 + 4 * v32 + 24) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v36, 238));
    *(_DWORD *)(a1 + 4 * v33 + 24) = _mm_cvtsi128_si32(_mm_shuffle_epi32(v36, 255));
    v21 += 4LL;
    v20 = _mm_add_epi64(v20, v27);
    si128 = _mm_add_epi64(si128, v27);
  }
  while ( v21 != 16 );
  *(_BYTE *)(a1 + 408) = 0;
  return __stack_chk_guard;
}

//----- (000000010032718A) ----------------------------------------------------
__int64 __fastcall sub_10032718A(__int64 a1, __int64 a2, char a3)
{

  v12[0] = v3;
  v12[0] = *(_QWORD *)(a2 + 8);
  sub_100326E58(a1, (__int64)v12, a3 ^ 1);
  *(_OWORD *)(a1 + 264) = *(_OWORD *)(a1 + 136);
  *(_OWORD *)(a1 + 248) = *(_OWORD *)(a1 + 120);
  *(_OWORD *)(a1 + 232) = *(_OWORD *)(a1 + 104);
  *(_OWORD *)(a1 + 216) = *(_OWORD *)(a1 + 88);
  v5 = *(_OWORD *)(a1 + 24);
  v6 = *(_OWORD *)(a1 + 40);
  v7 = *(_OWORD *)(a1 + 56);
  *(_OWORD *)(a1 + 200) = *(_OWORD *)(a1 + 72);
  *(_OWORD *)(a1 + 184) = v7;
  *(_OWORD *)(a1 + 168) = v6;
  *(_OWORD *)(a1 + 152) = v5;
  result = sub_100326E58(a1, a2, a3);
  *(_OWORD *)(a1 + 392) = *(_OWORD *)(a1 + 136);
  *(_OWORD *)(a1 + 376) = *(_OWORD *)(a1 + 120);
  *(_OWORD *)(a1 + 360) = *(_OWORD *)(a1 + 104);
  *(_OWORD *)(a1 + 344) = *(_OWORD *)(a1 + 88);
  v9 = *(_OWORD *)(a1 + 24);
  v10 = *(_OWORD *)(a1 + 40);
  v11 = *(_OWORD *)(a1 + 56);
  *(_OWORD *)(a1 + 328) = *(_OWORD *)(a1 + 72);
  *(_OWORD *)(a1 + 312) = v11;
  *(_OWORD *)(a1 + 296) = v10;
  *(_OWORD *)(a1 + 280) = v9;
  *(_BYTE *)(a1 + 408) = 1;
  return result;
}

//----- (000000010032728C) ----------------------------------------------------
char __fastcall sub_10032728C(__int64 a1, __int64 a2, int a3, char a4)
{
  if ( !a2 )
    return 0;
  if ( a3 == 8 )
  {
    sub_100326E58(a1, a2, a4);
    return 1;
  }
  if ( a3 != 16 )
    return 0;
  sub_10032718A(a1, a2, a4);
  return 1;
}

//----- (00000001003272BA) ----------------------------------------------------
__int64 __fastcall sub_1003272BA(__int64 a1, __int64 a2, unsigned int a3, int *a4)
{
  int v4; // eax
  int v5; // edi
  int v6; // eax
  int v7; // eax
  int v8; // edi

  v4 = *(unsigned __int8 *)(a2 + a3) << 24;
  *a4 = v4;
  v5 = v4 | (*(unsigned __int8 *)(a2 + a3 + 1) << 16);
  *a4 = v5;
  v6 = v5 | (*(unsigned __int8 *)(a2 + a3 + 2) << 8);
  *a4 = v6;
  *a4 = v6 | *(unsigned __int8 *)(a2 + a3 + 3);
  v7 = *(unsigned __int8 *)(a2 + a3 + 4) << 24;
  a4[1] = v7;
  v8 = v7 | (*(unsigned __int8 *)(a2 + a3 + 5) << 16);
  a4[1] = v8;
  result = v8 | (*(unsigned __int8 *)(a2 + a3 + 6) << 8);
  a4[1] = result;
  a4[1] = result | *(unsigned __int8 *)(a2 + a3 + 7);
  return result;
}

//----- (000000010032732A) ----------------------------------------------------
char __fastcall sub_10032732A(__int64 a1, _BYTE *a2, __int64 a3, unsigned int a4)
{
  char result; // al

  *(_BYTE *)(a3 + a4) = a2[3];
  *(_BYTE *)(a3 + a4 + 1) = a2[2];
  *(_BYTE *)(a3 + a4 + 2) = a2[1];
  *(_BYTE *)(a3 + a4 + 3) = *a2;
  *(_BYTE *)(a3 + a4 + 4) = a2[7];
  *(_BYTE *)(a3 + a4 + 5) = a2[6];
  *(_BYTE *)(a3 + a4 + 6) = a2[5];
  result = a2[4];
  *(_BYTE *)(a3 + a4 + 7) = result;
  return result;
}

//----- (0000000100327376) ----------------------------------------------------
__int64 __fastcall sub_100327376(__int64 a1, unsigned int *a2, __int64 a3)
{
  unsigned int v3; // ecx
  int v4; // edi
  int v5; // ecx
  unsigned int v6; // edi
  int v7; // r9d
  unsigned int v8; // ecx
  int v9; // r9d
  int v10; // edi
  int v11; // r9d
  unsigned int v12; // edi
  int v13; // ecx
  int v14; // r9d
  int v15; // ecx
  unsigned int v16; // edi
  int v17; // r9d
  int v18; // edi
  unsigned int v19; // r9d
  unsigned int v21; // r8d
  unsigned int v22; // eax
  int v23; // edi
  unsigned int v24; // ecx
  unsigned __int64 v25; // rt0
  unsigned int v26; // edi
  int v27; // ecx
  unsigned int v28; // edi
  int v29; // eax
  unsigned int v30; // ecx
  int v31; // eax
  int v32; // edi
  int v33; // eax
  unsigned int v34; // edi
  int v35; // ecx

  v3 = a2[1];
  v4 = (v3 ^ (*a2 >> 4)) & 0xF0F0F0F;
  v5 = v4 ^ v3;
  v6 = *a2 ^ (16 * v4);
  v7 = HIWORD(v6) ^ (unsigned __int16)v5;
  v8 = v7 ^ v5;
  v9 = v6 ^ (v7 << 16);
  v10 = (v9 ^ (v8 >> 2)) & 0x33333333;
  v11 = v10 ^ v9;
  v12 = v8 ^ (4 * v10);
  v13 = (v11 ^ (v12 >> 8)) & 0xFF00FF;
  v14 = v13 ^ v11;
  v15 = __ROL4__(v12 ^ (v13 << 8), 1);
  v16 = (v14 ^ v15) & 0xAAAAAAAA;
  v17 = v16 ^ v14;
  v18 = v15 ^ v16;
  v19 = __ROL4__(v17, 1);
  v20 = 0LL;
  do
  {
    v21 = *(_DWORD *)(a3 + v20) ^ __ROL4__(v18, 28);
    v19 ^= dword_1010D4D80[(((unsigned int)v18 ^ *(_DWORD *)(a3 + v20 + 4)) >> 24) & 0x3F] | dword_1010D4F80[(((unsigned int)v18 ^ *(_DWORD *)(a3 + v20 + 4)) >> 16) & 0x3F] | dword_1010D5180[(((unsigned int)v18 ^ *(_DWORD *)(a3 + v20 + 4)) >> 8) & 0x3F] | dword_1010D5380[((unsigned __int8)v18 ^ *(_BYTE *)(a3 + v20 + 4)) & 0x3F] | dword_1010D4C80[HIBYTE(v21) & 0x3F] | dword_1010D4E80[HIWORD(v21) & 0x3F] | dword_1010D5280[v21 & 0x3F] | dword_1010D5080[(v21 >> 8) & 0x3F];
    v22 = *(_DWORD *)(a3 + v20 + 8) ^ __ROL4__(v19, 28);
    v18 ^= dword_1010D4D80[((v19 ^ *(_DWORD *)(a3 + v20 + 12)) >> 24) & 0x3F] | dword_1010D4F80[((v19 ^ *(_DWORD *)(a3 + v20 + 12)) >> 16) & 0x3F] | dword_1010D5180[((v19 ^ *(_DWORD *)(a3 + v20 + 12)) >> 8) & 0x3F] | dword_1010D5380[((unsigned __int8)v19 ^ *(_BYTE *)(a3 + v20 + 12)) & 0x3F] | dword_1010D4C80[HIBYTE(v22) & 0x3F] | dword_1010D4E80[HIWORD(v22) & 0x3F] | dword_1010D5280[v22 & 0x3F] | dword_1010D5080[(v22 >> 8) & 0x3F];
    v20 += 16LL;
  }
  while ( (_DWORD)v20 != 128 );
  v23 = __ROR4__(v18, 1);
  v24 = v23 ^ (v23 ^ v19) & 0xAAAAAAAA;
  LODWORD(v25) = v19 ^ (v23 ^ v19) & 0xAAAAAAAA;
  HIDWORD(v25) = v19;
  v26 = (v24 ^ ((unsigned int)(v25 >> 1) >> 8)) & 0xFF00FF;
  v27 = v26 ^ v24;
  v28 = (v25 >> 1) ^ (v26 << 8);
  v29 = (v27 ^ (v28 >> 2)) & 0x33333333;
  v30 = v29 ^ v27;
  v31 = v28 ^ (4 * v29);
  v32 = HIWORD(v30) ^ (unsigned __int16)v31;
  v33 = v32 ^ v31;
  v34 = v30 ^ (v32 << 16);
  v35 = (v33 ^ (v34 >> 4)) & 0xF0F0F0F;
  result = v35 ^ (unsigned int)v33;
  *a2 = v34 ^ (16 * v35);
  a2[1] = result;
  return result;
}

//----- (000000010032759C) ----------------------------------------------------
__int64 __fastcall sub_10032759C(__int64 a1, __int64 a2, unsigned int a3, __int64 a4, unsigned int a5)
{
  int v6; // eax
  int v8; // eax
  int v10; // [rsp+0h] [rbp-30h] BYREF
  int v11; // [rsp+4h] [rbp-2Ch]

  sub_1003272BA(a1, a2, a3, &v10);
  sub_100327376(a1, (unsigned int *)&v10, a1 + 24);
  sub_100327376(a1, (unsigned int *)&v10, a1 + 152);
  sub_100327376(a1, (unsigned int *)&v10, a1 + 280);
  v6 = v10;
  *(_BYTE *)(v7 + a5) = HIBYTE(v10);
  *(_BYTE *)(v7 + a5 + 1) = BYTE2(v6);
  *(_BYTE *)(v7 + a5 + 2) = BYTE1(v6);
  *(_BYTE *)(v7 + a5 + 3) = v6;
  v8 = v11;
  *(_BYTE *)(v7 + a5 + 4) = HIBYTE(v11);
  *(_BYTE *)(v7 + a5 + 5) = BYTE2(v8);
  *(_BYTE *)(v7 + a5 + 6) = BYTE1(v8);
  *(_BYTE *)(v7 + a5 + 7) = v8;
  return __stack_chk_guard;
}

//----- (000000010032766E) ----------------------------------------------------
void __fastcall sub_10032766E(__int64 a1, __int128 *a2)
{

  *(_OWORD *)(a1 + 136) = a2[7];
  *(_OWORD *)(a1 + 120) = a2[6];
  *(_OWORD *)(a1 + 104) = a2[5];
  *(_OWORD *)(a1 + 88) = a2[4];
  v2 = *a2;
  v3 = a2[1];
  v4 = a2[2];
  *(_OWORD *)(a1 + 72) = a2[3];
  *(_OWORD *)(a1 + 56) = v4;
  *(_OWORD *)(a1 + 40) = v3;
  *(_OWORD *)(a1 + 24) = v2;
  *(_BYTE *)(a1 + 408) = 0;
  v5 = a2[9];
  v6 = a2[10];
  v7 = a2[11];
  *(_OWORD *)(a1 + 152) = a2[8];
  *(_OWORD *)(a1 + 168) = v5;
  *(_OWORD *)(a1 + 184) = v6;
  *(_OWORD *)(a1 + 200) = v7;
  *(_OWORD *)(a1 + 216) = a2[12];
  *(_OWORD *)(a1 + 232) = a2[13];
  *(_OWORD *)(a1 + 248) = a2[14];
  *(_OWORD *)(a1 + 264) = a2[15];
  *(_OWORD *)(a1 + 392) = *(_OWORD *)(a1 + 136);
  *(_OWORD *)(a1 + 376) = *(_OWORD *)(a1 + 120);
  *(_OWORD *)(a1 + 360) = *(_OWORD *)(a1 + 104);
  *(_OWORD *)(a1 + 344) = *(_OWORD *)(a1 + 88);
  v8 = *(_OWORD *)(a1 + 24);
  v9 = *(_OWORD *)(a1 + 40);
  v10 = *(_OWORD *)(a1 + 56);
  *(_OWORD *)(a1 + 328) = *(_OWORD *)(a1 + 72);
  *(_OWORD *)(a1 + 312) = v10;
  *(_OWORD *)(a1 + 296) = v9;
  *(_OWORD *)(a1 + 280) = v8;
  *(_BYTE *)(a1 + 408) = 1;
}

//----- (0000000100327790) ----------------------------------------------------
__int64 __fastcall sub_100327790(__int64 a1, __int64 a2, unsigned int a3, __int64 a4, unsigned int a5)
{

  if ( *(_BYTE *)(a1 + 408) )
    return sub_10032759C(a1, a2, a3, a4, a5);
  return result;
}

//----- (00000001003277A6) ----------------------------------------------------
char __fastcall sub_1003277A6(__int64 a1, const void *a2, unsigned int a3)
{
  char result; // al

  if ( a2 )
  {
    if ( a3 )
    {
      memcpy((void *)(a1 + 8), a2, a3);
      sub_10032728C(a1, (__int64)a2, a3, 1);
      v4 = *(_OWORD *)(a1 + 40);
      v5 = *(_OWORD *)(a1 + 56);
      v6 = *(_OWORD *)(a1 + 72);
      *(_OWORD *)(a1 + 412) = *(_OWORD *)(a1 + 24);
      *(_OWORD *)(a1 + 428) = v4;
      *(_OWORD *)(a1 + 444) = v5;
      *(_OWORD *)(a1 + 460) = v6;
      *(_OWORD *)(a1 + 476) = *(_OWORD *)(a1 + 88);
      *(_OWORD *)(a1 + 492) = *(_OWORD *)(a1 + 104);
      *(_OWORD *)(a1 + 508) = *(_OWORD *)(a1 + 120);
      *(_OWORD *)(a1 + 524) = *(_OWORD *)(a1 + 136);
      *(_OWORD *)(a1 + 540) = *(_OWORD *)(a1 + 152);
      *(_OWORD *)(a1 + 556) = *(_OWORD *)(a1 + 168);
      *(_OWORD *)(a1 + 572) = *(_OWORD *)(a1 + 184);
      *(_OWORD *)(a1 + 588) = *(_OWORD *)(a1 + 200);
      *(_OWORD *)(a1 + 604) = *(_OWORD *)(a1 + 216);
      *(_OWORD *)(a1 + 620) = *(_OWORD *)(a1 + 232);
      *(_OWORD *)(a1 + 636) = *(_OWORD *)(a1 + 248);
      *(_OWORD *)(a1 + 652) = *(_OWORD *)(a1 + 264);
      result = sub_10032728C(a1, (__int64)a2, a3, 0);
      v8 = *(_OWORD *)(a1 + 40);
      v9 = *(_OWORD *)(a1 + 56);
      v10 = *(_OWORD *)(a1 + 72);
      *(_OWORD *)(a1 + 668) = *(_OWORD *)(a1 + 24);
      *(_OWORD *)(a1 + 684) = v8;
      *(_OWORD *)(a1 + 700) = v9;
      *(_OWORD *)(a1 + 716) = v10;
      *(_OWORD *)(a1 + 732) = *(_OWORD *)(a1 + 88);
      *(_OWORD *)(a1 + 748) = *(_OWORD *)(a1 + 104);
      *(_OWORD *)(a1 + 764) = *(_OWORD *)(a1 + 120);
      *(_OWORD *)(a1 + 780) = *(_OWORD *)(a1 + 136);
      *(_OWORD *)(a1 + 796) = *(_OWORD *)(a1 + 152);
      *(_OWORD *)(a1 + 812) = *(_OWORD *)(a1 + 168);
      *(_OWORD *)(a1 + 828) = *(_OWORD *)(a1 + 184);
      *(_OWORD *)(a1 + 844) = *(_OWORD *)(a1 + 200);
      *(_OWORD *)(a1 + 860) = *(_OWORD *)(a1 + 216);
      *(_OWORD *)(a1 + 876) = *(_OWORD *)(a1 + 232);
      *(_OWORD *)(a1 + 892) = *(_OWORD *)(a1 + 248);
      *(_OWORD *)(a1 + 908) = *(_OWORD *)(a1 + 264);
    }
  }
  return result;
}

//----- (000000010032799A) ----------------------------------------------------
char __fastcall sub_10032799A(__int64 a1, const void *a2, unsigned int a3)
{
  *(_QWORD *)a1 = off_1012DF7C8;
  *(_BYTE *)(a1 + 408) = 0;
  return sub_1003277A6(a1, a2, a3);
}

//----- (00000001003279B6) ----------------------------------------------------
char __fastcall sub_1003279B6(__int64 a1, const void *a2, unsigned int a3)
{
  *(_QWORD *)a1 = off_1012DF7C8;
  *(_BYTE *)(a1 + 408) = 0;
  return sub_1003277A6(a1, a2, a3);
}

//----- (00000001003279D2) ----------------------------------------------------
void sub_1003279D2()
{
  ;
}

//----- (00000001003279D8) ----------------------------------------------------
void sub_1003279D8()
{
  ;
}

//----- (00000001003279DE) ----------------------------------------------------
void __fastcall sub_1003279DE(void *a1)
{
  operator delete(a1);
}

//----- (00000001003279E8) ----------------------------------------------------
void __fastcall sub_1003279E8(__int64 a1, __int64 a2, unsigned int a3, char a4)
{
  _OWORD *v5; // rax
  int v19; // r15d
  unsigned int i; // ebx

  if ( a2 )
  {
    v4 = a1;
    if ( a4 )
    {
      v5 = (_OWORD *)(a1 + 412);
      *(_OWORD *)(a1 + 136) = *(_OWORD *)(a1 + 524);
      *(_OWORD *)(a1 + 120) = *(_OWORD *)(a1 + 508);
      *(_OWORD *)(a1 + 104) = *(_OWORD *)(a1 + 492);
      *(_OWORD *)(a1 + 88) = *(_OWORD *)(a1 + 476);
      v6 = *(_OWORD *)(a1 + 412);
      v7 = *(_OWORD *)(a1 + 428);
      v8 = *(_OWORD *)(a1 + 444);
      *(_OWORD *)(a1 + 72) = *(_OWORD *)(a1 + 460);
      *(_OWORD *)(a1 + 56) = v8;
      *(_OWORD *)(a1 + 40) = v7;
      *(_OWORD *)(a1 + 24) = v6;
      v9 = (__int128 *)(a1 + 540);
    }
    else
    {
      v5 = (_OWORD *)(a1 + 668);
      *(_OWORD *)(a1 + 136) = *(_OWORD *)(a1 + 780);
      *(_OWORD *)(a1 + 120) = *(_OWORD *)(a1 + 764);
      *(_OWORD *)(a1 + 104) = *(_OWORD *)(a1 + 748);
      *(_OWORD *)(a1 + 88) = *(_OWORD *)(a1 + 732);
      v10 = *(_OWORD *)(a1 + 668);
      v11 = *(_OWORD *)(a1 + 684);
      v12 = *(_OWORD *)(a1 + 700);
      *(_OWORD *)(a1 + 72) = *(_OWORD *)(a1 + 716);
      *(_OWORD *)(a1 + 56) = v12;
      *(_OWORD *)(a1 + 40) = v11;
      *(_OWORD *)(a1 + 24) = v10;
      v9 = (__int128 *)(a1 + 796);
    }
    *(_OWORD *)(a1 + 264) = v9[7];
    *(_OWORD *)(a1 + 248) = v9[6];
    *(_OWORD *)(a1 + 232) = v9[5];
    *(_OWORD *)(a1 + 216) = v9[4];
    v13 = *v9;
    v14 = v9[1];
    v15 = v9[2];
    *(_OWORD *)(a1 + 200) = v9[3];
    *(_OWORD *)(a1 + 184) = v15;
    *(_OWORD *)(a1 + 168) = v14;
    *(_OWORD *)(a1 + 152) = v13;
    v16 = v5[1];
    v17 = v5[2];
    v18 = v5[3];
    *(_OWORD *)(a1 + 280) = *v5;
    *(_OWORD *)(a1 + 296) = v16;
    *(_OWORD *)(a1 + 312) = v17;
    *(_OWORD *)(a1 + 328) = v18;
    *(_OWORD *)(a1 + 344) = v5[4];
    *(_OWORD *)(a1 + 360) = v5[5];
    *(_OWORD *)(a1 + 376) = v5[6];
    *(_OWORD *)(a1 + 392) = v5[7];
    *(_BYTE *)(a1 + 408) = 1;
    if ( a3 >> 3 )
    {
      v19 = (a3 >> 3) - 1;
      v20 = 1;
      for ( i = 0; ; i += 8 )
      {
        if ( v20 )
          sub_10032759C(v4, a2, i, a2, i);
        if ( !v19 )
          break;
        v20 = *(_BYTE *)(v4 + 408);
        --v19;
      }
    }
  }
}

//----- (0000000100327C42) ----------------------------------------------------
void sub_100327C42()
{
  ;
}

