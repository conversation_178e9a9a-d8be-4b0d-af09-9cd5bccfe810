//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSView.h>

@class NSAttributedString, NSString, NSTextField;

@interface ZiXunListItemView : NSView
{
    BOOL _isSelected;
    id _model;
    NSString *_market;
    CDUnknownBlockType _clickBlock;
    NSTextField *_dateTF;
    NSTextField *_categoryTF;
    NSAttributedString *_titleAttrStr;
}


@property(retain, nonatomic) NSAttributedString *titleAttrStr; // @synthesize titleAttrStr=_titleAttrStr;
@property(retain, nonatomic) NSTextField *categoryTF; // @synthesize categoryTF=_categoryTF;
@property(retain, nonatomic) NSTextField *dateTF; // @synthesize dateTF=_dateTF;
@property(copy, nonatomic) CDUnknownBlockType clickBlock; // @synthesize clickBlock=_clickBlock;
@property(nonatomic) BOOL isSelected; // @synthesize isSelected=_isSelected;
@property(retain, nonatomic) NSString *market; // @synthesize market=_market;
@property(retain, nonatomic) id model; // @synthesize model=_model;
- (void)mouseUp:(id)arg1;
- (void)setTitleAttributedString:(id)arg1 color:(id)arg2;
- (void)setTextField:(id)arg1 string:(id)arg2 color:(id)arg3;
- (id)getTextField;
- (void)setViewState;
- (BOOL)isFlipped;
- (void)drawRect:(struct CGRect)arg1;

@end

