UserModel *__cdecl -[UserModel init](UserModel *self, SEL a2)
{
  UserModel *v2; // rax
  UserModel *v3; // r15
  NSString *sessionId; // rdi
  NSTimer *sessionTimer; // rdi
  NSTimer *reconnectTimer; // rdi
  commumodule *v9; // rax
  commumodule *v10; // rbx
  SEL v13; // r12
  UserInfoModel *v16; // rax
  UserInfoModel *v17; // rbx
  CacheDataObject *v21; // rax
  CacheDataObject *v22; // rbx
  CacheDataObject *v26; // rax
  CacheDataObject *v27; // rbx
  SEL v30; // r12
  CacheDataObject *v33; // rax
  CacheDataObject *v34; // rbx
  SEL v37; // r12
  objc_class *v41; // rax
  JYViewStateModel *v44; // rax
  JYViewStateModel *v45; // rbx
  NSArray *extraMenuItems; // rdi

  v51.receiver = self;
  v51.super_class = (Class)&OBJC_CLASS___UserModel;
  v2 = -[HXBaseObject init](&v51, "init");
  v3 = v2;
  if ( v2 )
  {
    v2->_bActiveUser = 0;
    v2->_isUserLogin = 0;
    v2->_isMoniUser = 0;
    v2->_isDisconnectAlertShowing = 0;
    sessionId = v2->_sessionId;
    v2->_sessionId = 0LL;
    sessionTimer = v3->sessionTimer;
    v3->sessionTimer = 0LL;
    v6(sessionTimer);
    reconnectTimer = v3->reconnectTimer;
    v3->reconnectTimer = 0LL;
    v8(reconnectTimer);
    -[UserModel setHadAlertDisconnect:](v3, "setHadAlertDisconnect:", 0LL);
    -[UserModel setWtclSZPC:](v3, "setWtclSZPC:", &charsToLeaveEscaped);
    -[UserModel setWtclShClose:](v3, "setWtclShClose:", &charsToLeaveEscaped);
    v9 = -[UserModel tradeCommuModel](v3, "tradeCommuModel");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( !v10 )
    {
      v11 = objc_alloc((Class)&OBJC_CLASS___commumodule);
      v12 = _objc_msgSend(v11, "initWithUserModel:", v3);
      -[UserModel setTradeCommuModel:](v3, "setTradeCommuModel:", v12);
      v14 = _objc_msgSend(v3, v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v15, "setDefRspHandleDelegate:", v3);
    }
    v16 = -[UserModel userInfo](v3, "userInfo");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    if ( !v17 )
    {
      v18 = objc_alloc((Class)&OBJC_CLASS___UserInfoModel);
      v19 = _objc_msgSend(v18, "init");
      -[UserModel setUserInfo:](v3, "setUserInfo:", v19);
    }
    v21 = -[UserModel aCacheData](v3, "aCacheData");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    if ( !v22 )
    {
      v23 = objc_alloc((Class)&OBJC_CLASS___CacheDataObject);
      v24 = _objc_msgSend(v23, "initWithUserModel:", v3);
      -[UserModel setACacheData:](v3, "setACacheData:", v24);
    }
    v26 = -[UserModel aCacheKCBData](v3, "aCacheKCBData");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    if ( !v27 )
    {
      v28 = objc_alloc((Class)&OBJC_CLASS___CacheDataObject);
      v29 = _objc_msgSend(v28, "initWithUserModel:", v3);
      -[UserModel setACacheKCBData:](v3, "setACacheKCBData:", v29);
      v31 = _objc_msgSend(v3, v30);
      v32 = objc_retainAutoreleasedReturnValue(v31);
      _objc_msgSend(v32, "setNotAddToZiChan:", 1LL);
    }
    v33 = -[UserModel aCacheCYBData](v3, "aCacheCYBData");
    v34 = objc_retainAutoreleasedReturnValue(v33);
    if ( !v34 )
    {
      v35 = objc_alloc((Class)&OBJC_CLASS___CacheDataObject);
      v36 = _objc_msgSend(v35, "initWithUserModel:", v3);
      -[UserModel setACacheCYBData:](v3, "setACacheCYBData:", v36);
      v38 = _objc_msgSend(v3, v37);
      v39 = objc_retainAutoreleasedReturnValue(v38);
      _objc_msgSend(v39, "setNotAddToZiChan:", 1LL);
    }
    if ( !v3->_httpRequest )
    {
      v40 = objc_alloc((Class)&OBJC_CLASS___JYHTTPRequestManage);
      v41 = (objc_class *)_objc_msgSend(v40, "initWithDelegate:baseURL:", v3, CFSTR("http://wt.10jqka.com.cn"));
      v43 = *(Class *)((char *)&v3->super.super.isa + v42);
      *(Class *)((char *)&v3->super.super.isa + v42) = v41;
    }
    v44 = -[UserModel aViewStateModel](v3, "aViewStateModel");
    v45 = objc_retainAutoreleasedReturnValue(v44);
    if ( !v45 )
    {
      v46 = objc_alloc((Class)&OBJC_CLASS___JYViewStateModel);
      v47 = _objc_msgSend(v46, "init");
      -[UserModel setAViewStateModel:](v3, "setAViewStateModel:", v47);
    }
    extraMenuItems = v3->_extraMenuItems;
    v3->_extraMenuItems = 0LL;
    v3->nType = 0;
    -[UserModel addNotification](v3, "addNotification");
  }
  return v3;
}

//----- (0000000100C1A11E) ----------------------------------------------------
void __cdecl -[UserModel dealloc](UserModel *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___UserModel;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (0000000100C1A1EB) ----------------------------------------------------
void __cdecl -[UserModel addNotification](UserModel *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(
    v3,
    "addObserver:selector:name:object:",
    self,
    "handleSessionConnectSuccess",
    CFSTR("TradeSessionConnectSuccess"),
    0LL);
}

//----- (0000000100C1A263) ----------------------------------------------------
void __cdecl -[UserModel handleSessionConnectSuccess](UserModel *self, SEL a2)
{
  _QWORD block[5]; // [rsp+8h] [rbp-28h] BYREF

  block[0] = _NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = sub_100C1A2AE;
  block[3] = &unk_1012DB528;
  block[4] = self;
  dispatch_async(&_dispatch_main_q, block);
}

//----- (0000000100C1A2AE) ----------------------------------------------------
void __fastcall sub_100C1A2AE(__int64 a1)
{

  if ( *(_BYTE *)(*(_QWORD *)(a1 + 32) + 41LL) )
  {
    v1 = _objc_msgSend(&OBJC_CLASS___NSApplication, "sharedApplication");
    v2 = objc_retainAutoreleasedReturnValue(v1);
    v3 = _objc_msgSend(v2, "mainWindow");
    objc_retainAutoreleasedReturnValue(v3);
    v5 = _objc_msgSend(v4, "attachedSheet");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    if ( v6 )
    {
      v8 = _objc_msgSend(v7, "attachedSheet");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      _objc_msgSend(v10, "endSheet:", v9);
    }
    *(_BYTE *)(*(_QWORD *)(a1 + 32) + 41LL) = 0;
  }
}

//----- (0000000100C1A3E0) ----------------------------------------------------
void __fastcall sub_100C1A3E0(__int64 a1)
{
}

//----- (0000000100C1A3EF) ----------------------------------------------------
void __cdecl -[UserModel DJAwpxdGFxRzelWL:](UserModel *self, SEL a2, id a3)
{
  commumodule *v5; // rax
  commumodule *v6; // rbx
  commumodule *v9; // rax
  commumodule *v10; // rbx
  commumodule *v11; // rax
  commumodule *v12; // rbx

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v4 = -[UserModel prometBlock](self, "prometBlock");
    objc_retainAutoreleasedReturnValue(v4);
    v5 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[commumodule setLoginStatusFun:](v6, "setLoginStatusFun:", v7);
    v9 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    -[commumodule setAUserModel:](v10, "setAUserModel:", self);
    v11 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    -[commumodule DJAwpxdGFxRzelWL:](v12, "DJAwpxdGFxRzelWL:", v3);
  }
}

//----- (0000000100C1A52F) ----------------------------------------------------
void __cdecl -[UserModel tradeLogout](UserModel *self, SEL a2)
{

  -[UserModel resetUserModel](self, "resetUserModel");
  v2 = +[QueryTradeDataCenter shareInstance](&OBJC_CLASS___QueryTradeDataCenter, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "NotifyUserChange");
  _objc_msgSend(self->sessionTimer, "invalidate");
}

//----- (0000000100C1A5B5) ----------------------------------------------------
void __cdecl -[UserModel resetUserModel](UserModel *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  UserInfoModel *v14; // rax
  UserInfoModel *v15; // rbx
  id (*v16)(id, SEL, ...); // r12
  CacheDataObject *v20; // rax
  CacheDataObject *v21; // rbx
  id (*v22)(id, SEL, ...); // r12
  CacheDataObject *v26; // rax
  CacheDataObject *v27; // rbx
  id (*v28)(id, SEL, ...); // r12
  CacheDataObject *v32; // rax
  CacheDataObject *v33; // rbx
  id (*v34)(id, SEL, ...); // r12

  -[UserModel setBActiveUser:](self, "setBActiveUser:", 0LL);
  self->_isUserLogin = 0;
  v2(self, "setWtclSZPC:", &charsToLeaveEscaped);
  v3(self, "setWtclShClose:", &charsToLeaveEscaped);
  v5 = v4(self, "tradeCommuModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( v6 )
  {
    v8 = v7(self, "tradeCommuModel");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = v10(self, "userInfo");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v9, "tradeLogOut:", v12);
  }
  v14 = -[UserModel userInfo](self, "userInfo");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  if ( v15 )
  {
    v17 = v16(self, "userInfo");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "resetUserInfoModel");
  }
  v20 = -[UserModel aCacheData](self, "aCacheData");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  if ( v21 )
  {
    v23 = v22(self, "aCacheData");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25(v24, "clearAllSaveSourceData");
  }
  v26 = -[UserModel aCacheKCBData](self, "aCacheKCBData");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  if ( v27 )
  {
    v29 = v28(self, "aCacheKCBData");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31(v30, "clearAllSaveSourceData");
  }
  v32 = -[UserModel aCacheCYBData](self, "aCacheCYBData");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  if ( v33 )
  {
    v35 = v34(self, "aCacheCYBData");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    v37(v36, "clearAllSaveSourceData");
  }
}

//----- (0000000100C1A832) ----------------------------------------------------
char __cdecl -[UserModel isUserLogin](UserModel *self, SEL a2)
{
  return self->_isUserLogin;
}

//----- (0000000100C1A843) ----------------------------------------------------
char __cdecl -[UserModel isSameUser:](UserModel *self, SEL a2, id a3)
{
  UserInfoModel *v3; // rax
  NSString *v4; // rax
  UserInfoModel *v8; // rax
  NSString *v9; // rax
  UserInfoModel *v13; // rax
  NSString *v14; // rax
  NSString *v15; // r14
  bool v21; // r13
  UserInfoModel *v27; // [rsp+8h] [rbp-78h]
  UserInfoModel *v30; // [rsp+20h] [rbp-60h]
  UserInfoModel *v33; // [rsp+38h] [rbp-48h]
  NSString *v34; // [rsp+40h] [rbp-40h]
  NSString *v35; // [rsp+48h] [rbp-38h]

  objc_retain(a3);
  v3 = -[UserModel userInfo](self, "userInfo");
  v27 = objc_retainAutoreleasedReturnValue(v3);
  v4 = -[UserInfoModel strUniqueQsid](v27, "strUniqueQsid");
  v34 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "userInfo");
  v28 = objc_retainAutoreleasedReturnValue(v6);
  v7 = _objc_msgSend(v28, "strUniqueQsid");
  v29 = objc_retainAutoreleasedReturnValue(v7);
  if ( (unsigned __int8)_objc_msgSend(v34, "isEqualToString:", v29) )
  {
    v8 = -[UserModel userInfo](self, "userInfo");
    v30 = objc_retainAutoreleasedReturnValue(v8);
    v9 = -[UserInfoModel strLoginAccount](v30, "strLoginAccount");
    v35 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "userInfo");
    v31 = objc_retainAutoreleasedReturnValue(v11);
    v12 = _objc_msgSend(v31, "strLoginAccount");
    v32 = objc_retainAutoreleasedReturnValue(v12);
    if ( (unsigned __int8)_objc_msgSend(v35, "isEqualToString:", v32) )
    {
      v13 = -[UserModel userInfo](self, "userInfo");
      v33 = objc_retainAutoreleasedReturnValue(v13);
      v14 = -[UserInfoModel strZjzh](v33, "strZjzh");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v17 = _objc_msgSend(v16, "userInfo");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(v18, "strZjzh");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = (unsigned __int8)_objc_msgSend(v15, "isEqualToString:", v20) != 0;
      v22(v18);
      v23(v15);
      v24(v33);
    }
    else
    {
      v21 = 0;
    }
  }
  else
  {
    v21 = 0;
  }
  return v21;
}

//----- (0000000100C1AB65) ----------------------------------------------------
void __cdecl -[UserModel XIXBiQhWtIEfT_DW:](UserModel *self, SEL a2, id a3)
{
  commumodule *v3; // rax
  commumodule *v4; // rbx
  commumodule *v19; // rax
  commumodule *v21; // r15
  NSString *v27; // rax
  NSString *v28; // rax
  NSString *v29; // rax
  NSString *v30; // rbx
  commumodule *v34; // rax
  commumodule *v35; // rbx
  commumodule *v37; // rdi
  dispatch_time_t v38; // rax
  _QWORD v39[5]; // [rsp+8h] [rbp-88h] BYREF
  _QWORD block[5]; // [rsp+30h] [rbp-60h] BYREF

  objc_retain(a3);
  v3 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)-[commumodule isDisconnected](v4, "isDisconnected");
  if ( v5 )
  {
    v41 = v6;
    v7 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
    v42 = self;
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v8, "getUserLogInfo");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = _objc_msgSend(v11, "stringWithFormat:", CFSTR("通讯断开连接:%@"), v10);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v15 = _objc_msgSend(
            v14,
            "stringWithFormat:",
            CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
            "-[UserModel XIXBiQhWtIEfT_DW:]",
            198LL,
            v13);
    objc_retainAutoreleasedReturnValue(v15);
    +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v16);
    v18 = _objc_msgSend(v41, "getStockCodeString");
    v19 = (commumodule *)objc_retainAutoreleasedReturnValue(v18);
    v21 = v19;
    if ( (!v19
       || !_objc_msgSend(v19, "length")
       || (unsigned __int8)_objc_msgSend(v21, "isEqualToString:", CFSTR("NULL"))
       || (unsigned __int8)_objc_msgSend(v21, "isEqualToString:", CFSTR("NUL")))
      && (v22 = _objc_msgSend(v20, "getReqTypeString"),
          v23 = objc_retainAutoreleasedReturnValue(v22),
          v24 = _objc_msgSend(v23, "integerValue"),
          v24 == (id)1025) )
    {
      v25 = _objc_msgSend(v42, "getUserLogInfo");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      v27 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("断线的时候发送停止推送的请求时，不提示:%@"),
              v26);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
              "-[UserModel XIXBiQhWtIEfT_DW:]",
              202LL,
              v28);
      v30 = objc_retainAutoreleasedReturnValue(v29);
      +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v30);
    }
    else if ( ((unsigned __int8)_objc_msgSend(v42, "GetType") & 8) != 0 )
    {
      v38 = dispatch_time(0LL, 1000000000LL);
      v39[0] = _NSConcreteStackBlock;
      v39[1] = 3254779904LL;
      v39[2] = sub_100C1AFCE;
      v39[3] = &unk_1012DB528;
      v39[4] = v42;
      dispatch_after(v38, &_dispatch_main_q, v39);
    }
    else
    {
      block[0] = _NSConcreteStackBlock;
      block[1] = 3254779904LL;
      block[2] = sub_100C1B011;
      block[3] = &unk_1012DB528;
      block[4] = v42;
      dispatch_async(&_dispatch_main_q, block);
    }
    v37 = v21;
LABEL_16:
    goto LABEL_17;
  }
  if ( v6 )
  {
    if ( self->_sessionId )
    {
      v32 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:");
      _objc_msgSend(v33, "setRequestParamStr:Value:", "session_id", v32);
    }
    v34 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    -[commumodule kQvYehFExlWUHfgW:](v35, "kQvYehFExlWUHfgW:", v36);
    v37 = v35;
    goto LABEL_16;
  }
LABEL_17:
}

//----- (0000000100C1AFCE) ----------------------------------------------------
id __fastcall sub_100C1AFCE(__int64 a1)
{
  _objc_msgSend(*(id *)(a1 + 32), "StartReconnect");
  _objc_msgSend(*(id *)(a1 + 32), "StopCheck");
  return _objc_msgSend(*(id *)(a1 + 32), "setHadAlertDisconnect:", 0LL);
}

//----- (0000000100C1B011) ----------------------------------------------------
id __fastcall sub_100C1B011(__int64 a1)
{
  return _objc_msgSend(*(id *)(a1 + 32), "showDisconnectConfirm");
}

//----- (0000000100C1B027) ----------------------------------------------------
id __cdecl -[UserModel getUserInfo](UserModel *self, SEL a2)
{
  return -[UserModel userInfo](self, "userInfo");
}

//----- (0000000100C1B039) ----------------------------------------------------
id __cdecl -[UserModel getCacheDataObject](UserModel *self, SEL a2)
{
  SEL *v2; // rax
  bool v3; // zf

  if ( -[UserModel pTWTType](self, "pTWTType") == (id)1 )
  {
    v2 = &selRef_aCacheKCBData;
  }
  else
  {
    v3 = -[UserModel pTWTType](self, "pTWTType") == (id)2;
    v2 = &selRef_aCacheData;
    if ( v3 )
      v2 = &selRef_aCacheCYBData;
  }
  v4 = _objc_msgSend(self, *v2);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C1B0A4) ----------------------------------------------------
id __cdecl -[UserModel getJYViewStateModel](UserModel *self, SEL a2)
{
  return -[UserModel aViewStateModel](self, "aViewStateModel");
}

//----- (0000000100C1B0B6) ----------------------------------------------------
void __cdecl -[UserModel setBActiveUser:](UserModel *self, SEL a2, char a3)
{
  if ( self->_bActiveUser != a3 )
  {
    self->_bActiveUser = a3;
    if ( !a3 )
      -[UserModel stopStockTradePush](self, "stopStockTradePush");
  }
}

//----- (0000000100C1B0DD) ----------------------------------------------------
void __cdecl -[UserModel stopStockTradePush](UserModel *self, SEL a2)
{
  CacheDataObject *v2; // rax
  CacheDataObject *v3; // r13
  NSMutableDictionary *v4; // rax
  NSMutableDictionary *v5; // rbx
  CacheDataObject *v7; // rax
  CacheDataObject *v8; // r13
  NSMutableDictionary *v9; // rax
  NSMutableDictionary *v10; // rbx
  CacheDataObject *v13; // rax
  CacheDataObject *v14; // r13
  NSMutableDictionary *v15; // rax
  NSMutableDictionary *v16; // rbx
  SEL v23; // r12
  SEL v25; // r12

  v2 = -[UserModel aCacheData](self, "aCacheData");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[CacheDataObject nowStockInfo](v3, "nowStockInfo");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "removeAllObjects");
  v6(v3);
  v7 = -[UserModel aCacheKCBData](self, "aCacheKCBData");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = -[CacheDataObject nowStockInfo](v8, "nowStockInfo");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "removeAllObjects");
  v11(v10);
  v12(v8);
  v13 = -[UserModel aCacheCYBData](self, "aCacheCYBData");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = -[CacheDataObject nowStockInfo](v14, "nowStockInfo");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v16, "removeAllObjects");
  v17(v16);
  v18(v14);
  v19 = objc_alloc((Class)&OBJC_CLASS___TradeRequest);
  v20 = _objc_msgSend(v19, "init");
  if ( v20 )
  {
    v21 = -[HXBaseObject getInstanceID](self, "getInstanceID");
    _objc_msgSend(v20, "setNsInsid:", v21);
    _objc_msgSend(v20, "setDelegate:", self);
    _objc_msgSend(v20, "setBNeedPush:", 0LL);
    _objc_msgSend(v20, "setRequestParamInteger:Value:", "req_type", 1025LL);
    v22 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", &charsToLeaveEscaped);
    _objc_msgSend(v20, "setRequestParamStr:Value:", "stockcode", v22);
    v24 = _objc_msgSend(&OBJC_CLASS___tools, v23, &charsToLeaveEscaped);
    _objc_msgSend(v20, "setRequestParamStr:Value:", "scdm", v24);
    v26 = _objc_msgSend(&OBJC_CLASS___tools, v25, &charsToLeaveEscaped);
    _objc_msgSend(v20, "setRequestParamStr:Value:", "mkcode", v26);
    _objc_msgSend(v20, "setRequestParamInteger:Value:", "amount", 5LL);
    -[UserModel XIXBiQhWtIEfT_DW:](self, "XIXBiQhWtIEfT_DW:", v20);
  }
}

//----- (0000000100C1B377) ----------------------------------------------------
void __cdecl -[UserModel setNetworkState:](UserModel *self, SEL a2, char a3)
{
  NSNumber *v4; // rax
  NSString *v7; // rax
  NSString *v8; // r14
  NSString *v9; // rax
  NSString *v10; // r15
  commumodule *v12; // rax
  commumodule *v13; // rax

  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = -[UserModel getUserLogInfo](self, "getUserLogInfo");
  v15 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("设置账户网络状态%@-%@"), v6, v15);
  v16 = a3;
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
         "-[UserModel setNetworkState:]",
         291LL,
         v8);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v10);
  v12 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  -[commumodule setIsLostSocketConnection:](v13, "setIsLostSocketConnection:", (unsigned int)v16);
}

//----- (0000000100C1B4EE) ----------------------------------------------------
int __cdecl -[UserModel OnTradeRsponceNotify:](UserModel *self, SEL a2, void *a3)
{
  int v4; // ebx
  int v5; // r12d
  unsigned int v7; // r12d
  NSMutableDictionary *v10; // r13
  UserInfoModel *v24; // rax
  UserInfoModel *v25; // r13
  NSString *v26; // rax
  NSString *v27; // rbx
  NSString *v28; // rax
  __CFString *v29; // r13
  unsigned int v41; // r12d
  NSString *v46; // rax
  dispatch_time_t v49; // rax
  _QWORD block[5]; // [rsp+18h] [rbp-98h] BYREF
  volatile signed __int32 *v60[6]; // [rsp+80h] [rbp-30h] BYREF

  sub_100D6DC60(&v59, (__int64)a3);
  v4 = *(__int16 *)(v59 + 74);
  v5 = *(unsigned __int16 *)(v59 + 74);
  if ( v4 > 3586 )
  {
    if ( v5 != 4096 && v5 != 3587 )
      goto LABEL_21;
  }
  else if ( (_WORD)v4 != 16 && v5 != 1541 )
  {
    goto LABEL_21;
  }
  v6 = sub_100F7A8F0(v59, "errorcode");
  if ( v6 )
  {
    sub_100F7A7F0(v60, v59, "errormsg");
    sub_100F5D0E0("reply_type:%x error_code=%s error_str=%s \r\n", v7, v6, (const char *)v60[0]);
    sub_100C56008(v60);
    sub_100F7A7F0(v60, v59, "errormsg");
    v8 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v60[0]);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    sub_100C56008(v60);
    v10 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "new");
    _objc_msgSend(v10, "setObject:forKey:", v9, CFSTR("Error"));
    if ( (unsigned __int8)-[UserModel isMoniUser](self, "isMoniUser") )
    {
      _objc_msgSend(v10, v11, CFSTR("YES"), CFSTR("IsMoniUser"));
    }
    else if ( (unsigned __int8)-[UserModel isZyyUser](self, "isZyyUser") )
    {
      _objc_msgSend(v10, v30, CFSTR("YES"), CFSTR("isZyyUser"));
    }
    v31 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    _objc_msgSend(v32, "postNotificationName:object:", CFSTR("wtlogin_fail"), v10);
    goto LABEL_21;
  }
  if ( (__int16)v4 <= 3586 )
  {
    if ( (unsigned __int16)v4 != 1541 )
    {
      if ( (unsigned __int16)v4 != 2304 )
        goto LABEL_21;
      sub_100F7A7F0(v60, v59, "l_data");
      v12 = sub_100F9F630((__int64)v60);
      sub_100C56008(v60);
      if ( !v12 )
        goto LABEL_21;
      v13 = +[tools splitString3:withSeparator:subSeparator:](
              &OBJC_CLASS___tools,
              "splitString3:withSeparator:subSeparator:",
              v12,
              "\n",
              "=");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = v14;
      if ( v14 )
      {
        v16 = _objc_msgSend(v14, "objectForKey:", CFSTR("cmd"));
        v17 = objc_retainAutoreleasedReturnValue(v16);
        if ( (unsigned __int8)_objc_msgSend(v17, "isEqualToString:", CFSTR("gp_push_chengjiao")) )
        {
          v58 = v18;
          v19 = _objc_msgSend(v15, "objectForKey:", CFSTR("zqdm"));
          v52 = objc_retainAutoreleasedReturnValue(v19);
          v20 = _objc_msgSend(v15, "objectForKey:", CFSTR("cjsl"));
          v53 = objc_retainAutoreleasedReturnValue(v20);
          v21 = _objc_msgSend(v15, "objectForKey:", CFSTR("cjjg"));
          v54 = objc_retainAutoreleasedReturnValue(v21);
          v22 = _objc_msgSend(v15, "objectForKey:", CFSTR("cjsj"));
          v55 = objc_retainAutoreleasedReturnValue(v22);
          v23 = _objc_msgSend(v15, "objectForKey:", CFSTR("ywmc"));
          v56 = objc_retainAutoreleasedReturnValue(v23);
          v24 = -[UserModel userInfo](self, "userInfo");
          v25 = objc_retainAutoreleasedReturnValue(v24);
          v26 = -[UserInfoModel strGdxm](v25, "strGdxm");
          v27 = objc_retainAutoreleasedReturnValue(v26);
          v57 = v27;
          if ( _objc_msgSend(v27, "length") )
          {
            v28 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("成交回报(帐号:%@)"), v57);
            v29 = objc_retainAutoreleasedReturnValue(v28);
          }
          else
          {
            v29 = CFSTR("成交回报");
          }
          v46 = _objc_msgSend(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("%@ %@ %@ 价格:%@ 数量:%@"),
                  v55,
                  v56,
                  v52,
                  v54,
                  v53);
          objc_retainAutoreleasedReturnValue(v46);
          v47 = +[notificationManager shareInstance](&OBJC_CLASS___notificationManager, "shareInstance");
          _objc_msgSend(v47, "showNotificationWithTitle:Content:HasActionBtn:", v29, v48, 1LL);
          v49 = dispatch_time(0LL, 2000000000LL);
          block[0] = _NSConcreteStackBlock;
          block[1] = 3254779904LL;
          block[2] = sub_100C1BCB6;
          block[3] = &unk_1012DB528;
          block[4] = self;
          dispatch_after(v49, &_dispatch_main_q, block);
          v18 = v58;
        }
      }
      v45 = v15;
LABEL_37:
      goto LABEL_21;
    }
    if ( (self->nType & 8) == 0 )
    {
      -[UserModel updateUserinfo:](self, "updateUserinfo:", a3);
      v34 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      _objc_msgSend(v35, "postNotificationName:object:", CFSTR("wtlogin_success"), self);
      if ( (*((_BYTE *)&self->super.super.isa + v36) & 4) == 0 )
        -[UserModel handleDeptinfo:](self, "handleDeptinfo:", a3);
      goto LABEL_21;
    }
    -[UserModel parseUserQuanXian:](self, "parseUserQuanXian:", a3);
    v44 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v43 = objc_retainAutoreleasedReturnValue(v44);
    _objc_msgSend(v43, "postNotificationName:object:", CFSTR("zyylogin_success"), self);
LABEL_32:
    v45 = v43;
    goto LABEL_37;
  }
  if ( (unsigned __int16)v4 == 3587 )
  {
    LODWORD(v60[0]) = 0;
    v37 = sub_100D71A30(v59, v60);
    if ( SLODWORD(v60[0]) <= 0 )
      goto LABEL_21;
    v38 = v37;
    v39 = +[JYExtHangManager shareInstance](&OBJC_CLASS___JYExtHangManager, "shareInstance");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v42 = _objc_msgSend(v40, "handleExtHangData:length:sessionId:", v38, SLODWORD(v60[0]), v41);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    if ( v43 )
      -[UserModel uploadExHangValidationData:](self, "uploadExHangValidationData:", v43);
    goto LABEL_32;
  }
  if ( (unsigned __int16)v4 == 4096 )
    -[UserModel updateUserinfo:](self, "updateUserinfo:", a3);
LABEL_21:
  sub_100D12680(&v59);
  return 1;
}

//----- (0000000100C1BCB6) ----------------------------------------------------
void __fastcall sub_100C1BCB6(__int64 a1)
{
  NSString *v3; // rax
  NSString *v4; // r15

  v1 = _objc_msgSend(*(id *)(a1 + 32), "getCacheDataObject");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  _objc_msgSend(v2, "updateAssociateDataAfterTrade");
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%d"), 258LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "postNotificationName:object:", CFSTR("refreshdata_after_weituo"), v4);
}

//----- (0000000100C1BD95) ----------------------------------------------------
void __cdecl -[UserModel parseUserQuanXian:](UserModel *self, SEL a2, void *a3)
{
  volatile signed __int32 *v9[4]; // [rsp+10h] [rbp-20h] BYREF

  sub_100D6DC60(&v8, (__int64)a3);
  sub_100F7A7F0(v9, v8, "extend_data");
  v3 = sub_100F9F630((__int64)v9);
  sub_100C56008(v9);
  if ( v3 )
  {
    v4 = +[tools splitString3:withSeparator:subSeparator:](
           &OBJC_CLASS___tools,
           "splitString3:withSeparator:subSeparator:",
           v3,
           "&",
           "=");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = +[JYZyyManager shareInstance](&OBJC_CLASS___JYZyyManager, "shareInstance");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "dealWithXcsQuanXian:", v5);
  }
  sub_100D12680(&v8);
}

//----- (0000000100C1BEA7) ----------------------------------------------------
void __cdecl -[UserModel uploadExHangValidationData:](UserModel *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = objc_alloc((Class)&OBJC_CLASS___TradeRequest);
  v5 = _objc_msgSend(v4, "init");
  v6 = v5;
  if ( v3 && v5 )
  {
    v7 = -[HXBaseObject getInstanceID](self, "getInstanceID");
    _objc_msgSend(v8, "setNsInsid:", v7);
    _objc_msgSend(v9, "setDelegate:", self);
    _objc_msgSend(v10, "setRequestParamInteger:Value:", "req_type", 3587LL);
    v11 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v3);
    _objc_msgSend(v12, "setRequestParamStrEx:Value:", "cr", v11);
    -[UserModel XIXBiQhWtIEfT_DW:](self, "XIXBiQhWtIEfT_DW:", v13);
  }
}

//----- (0000000100C1BFC6) ----------------------------------------------------
void __cdecl -[UserModel updateUserinfo:](UserModel *self, SEL a2, void *a3)
{
  unsigned __int16 v6; // r14
  UserInfoModel *v12; // rax
  commumodule *v13; // rax
  commumodule *v14; // r13
  id *v35; // r13
  unsigned __int64 v57; // rbx
  id i; // rax
  id (*v68)(id, SEL, ...); // r12
  id (*v69)(id, SEL, ...); // r12
  unsigned __int64 v82; // r12
  _QWORD *v139; // r14
  unsigned __int64 v143; // rsi
  int v144; // r14d
  _QWORD *v154; // rbx
  int v157; // r12d
  int v164; // eax
  signed __int8 v173; // al
  int v174; // r14d
  signed __int8 v193; // al
  NSString *v201; // rax
  NSString *v202; // r13
  SEL v203; // r12
  dispatch_time_t v236; // rax
  _QWORD block[5]; // [rsp+8h] [rbp-1B8h] BYREF
  SEL v239; // [rsp+38h] [rbp-188h]
  SEL v240; // [rsp+40h] [rbp-180h]
  SEL v241; // [rsp+48h] [rbp-178h]
  SEL v243; // [rsp+58h] [rbp-168h]
  SEL v244; // [rsp+60h] [rbp-160h]
  SEL v245; // [rsp+68h] [rbp-158h]
  SEL v246; // [rsp+70h] [rbp-150h]
  SEL v247; // [rsp+78h] [rbp-148h]
  SEL v248; // [rsp+80h] [rbp-140h]
  SEL v249; // [rsp+88h] [rbp-138h]
  SEL v250; // [rsp+90h] [rbp-130h]
  SEL v251; // [rsp+98h] [rbp-128h]
  SEL v252; // [rsp+A0h] [rbp-120h]
  SEL v253; // [rsp+A8h] [rbp-118h]
  SEL v254; // [rsp+B0h] [rbp-110h]
  SEL v255; // [rsp+B8h] [rbp-108h]
  SEL v256; // [rsp+C0h] [rbp-100h]
  SEL v257; // [rsp+C8h] [rbp-F8h]
  SEL v258; // [rsp+D0h] [rbp-F0h]
  SEL v259; // [rsp+D8h] [rbp-E8h]
  SEL v260; // [rsp+E0h] [rbp-E0h]
  SEL v261; // [rsp+E8h] [rbp-D8h]
  unsigned int nType; // [rsp+F4h] [rbp-CCh]
  SEL v263; // [rsp+F8h] [rbp-C8h]
  SEL v264; // [rsp+100h] [rbp-C0h]
  SEL v265; // [rsp+108h] [rbp-B8h]
  int v267; // [rsp+11Ch] [rbp-A4h]
  SEL v268; // [rsp+120h] [rbp-A0h]
  SEL v269; // [rsp+128h] [rbp-98h]
  _WORD *v271; // [rsp+138h] [rbp-88h] BYREF
  SEL v272; // [rsp+140h] [rbp-80h]
  SEL v274; // [rsp+150h] [rbp-70h]
  volatile signed __int32 *v280; // [rsp+180h] [rbp-40h] BYREF

  if ( !(unsigned __int8)_objc_msgSend(self->sessionTimer, "isValid") )
    _objc_msgSend(self->sessionTimer, "fire");
  sub_100D6DC60(&v271, (__int64)a3);
  v5 = v271;
  v6 = v271[37];
  v7 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "addUser:", self);
  v282 = v5;
  nType = self->nType;
  if ( (nType & 8) != 0 )
  {
    self->_isUserLogin = 1;
  }
  else
  {
    v10 = _objc_msgSend(&OBJC_CLASS___UserManager, v9);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "setActiveUser:", self);
  }
  v12 = -[UserModel userInfo](self, "userInfo");
  objc_retainAutoreleasedReturnValue(v12);
  v277 = self;
  v13 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = -[commumodule getLoginBroker](v14, "getLoginBroker");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v17, "setStrQsName:", v16);
  v19 = v277;
  v20 = _objc_msgSend(v277, "userInfo");
  objc_retainAutoreleasedReturnValue(v20);
  v21 = _objc_msgSend(v19, "tradeCommuModel");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = _objc_msgSend(v22, "getLoginAccount");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  _objc_msgSend(v25, "setStrLoginAccount:", v24);
  v27 = v277;
  v28 = _objc_msgSend(v277, "userInfo");
  objc_retainAutoreleasedReturnValue(v28);
  v29 = _objc_msgSend(v27, "tradeCommuModel");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v31 = _objc_msgSend(v30, "getUniqueQsid");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  _objc_msgSend(v33, "setStrUniqueQsid:", v32);
  if ( v6 == 1541 )
  {
    v267 = v6;
    sub_100F7A7F0(&v280, (__int64)v271, "zjzh");
    v96 = v277;
    v97 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v280);
    v278 = objc_retainAutoreleasedReturnValue(v97);
    sub_100C56008(&v280);
    v98 = _objc_msgSend(v96, "userInfo");
    v99 = objc_retainAutoreleasedReturnValue(v98);
    if ( !v99 )
      goto LABEL_143;
    v100 = _objc_msgSend(v96, "userInfo");
    v101 = objc_retainAutoreleasedReturnValue(v100);
    v102 = _objc_msgSend(v101, "strGdxm");
    if ( objc_retainAutoreleasedReturnValue(v102) )
    {
      v103 = _objc_msgSend(v277, "userInfo");
      v104 = objc_retainAutoreleasedReturnValue(v103);
      v105 = _objc_msgSend(v104, "strGdxm");
      v106 = objc_retainAutoreleasedReturnValue(v105);
      if ( _objc_msgSend(v106, "length") )
      {
        v107 = _objc_msgSend(v277, "userInfo");
        v275 = v104;
        v279 = v101;
        v108 = objc_retainAutoreleasedReturnValue(v107);
        v109 = _objc_msgSend(v108, "strGdxm");
        v110 = objc_retainAutoreleasedReturnValue(v109);
        if ( !(unsigned __int8)_objc_msgSend(v110, "isEqualToString:", CFSTR("NULL")) )
        {
          v132 = _objc_msgSend(v277, "userInfo");
          v274 = (SEL)objc_retainAutoreleasedReturnValue(v132);
          v276 = v108;
          v133 = _objc_msgSend((id)v274, "strGdxm");
          v134 = objc_retainAutoreleasedReturnValue(v133);
          LOBYTE(v281) = (unsigned __int8)_objc_msgSend(v134, "isEqualToString:", CFSTR("NUL"));
          v113 = v277;
          if ( !(_BYTE)v281 )
          {
LABEL_68:
            v118 = _objc_msgSend(v113, "tradeCommuModel");
            v119 = objc_retainAutoreleasedReturnValue(v118);
            v120 = _objc_msgSend(v119, "getTradePassword");
            v266 = objc_retainAutoreleasedReturnValue(v120);
            v121 = _objc_msgSend(v113, "userInfo");
            v122 = objc_retainAutoreleasedReturnValue(v121);
            v123 = _objc_msgSend(v122, "strJymm");
            if ( objc_retainAutoreleasedReturnValue(v123) )
            {
              v124 = _objc_msgSend(v113, "userInfo");
              v125 = objc_retainAutoreleasedReturnValue(v124);
              v126 = _objc_msgSend(v125, "strJymm");
              v279 = objc_retainAutoreleasedReturnValue(v126);
              if ( _objc_msgSend(v279, "length") )
              {
                v127 = _objc_msgSend(v113, "userInfo");
                v275 = objc_retainAutoreleasedReturnValue(v127);
                v128 = _objc_msgSend(v275, "strJymm");
                v129 = objc_retainAutoreleasedReturnValue(v128);
                if ( (unsigned __int8)_objc_msgSend(v129, "isEqualToString:", CFSTR("NULL")) )
                {
                  v130 = v125;
                }
                else
                {
                  v136 = _objc_msgSend(v277, "userInfo");
                  v276 = objc_retainAutoreleasedReturnValue(v136);
                  v137 = _objc_msgSend(v276, "strJymm");
                  v274 = (SEL)objc_retainAutoreleasedReturnValue(v137);
                  if ( !(unsigned __int8)_objc_msgSend((id)v274, "isEqualToString:", CFSTR("NUL")) )
                  {
                    v219 = _objc_msgSend(v277, "userInfo");
                    v281 = objc_retainAutoreleasedReturnValue(v219);
                    v220 = _objc_msgSend(v281, "strJymm");
                    v221 = objc_retainAutoreleasedReturnValue(v220);
                    LOBYTE(v272) = (unsigned __int8)_objc_msgSend(v221, "isEqualToString:", v266);
                    v113 = v277;
                    v139 = v282;
                    if ( (_BYTE)v272 )
                      goto LABEL_81;
                    goto LABEL_80;
                  }
                  v130 = v125;
                }
                v113 = v277;
              }
              else
              {
              }
            }
            else
            {
            }
            v139 = v282;
LABEL_80:
            v140 = _objc_msgSend(v113, "userInfo");
            v141 = objc_retainAutoreleasedReturnValue(v140);
            _objc_msgSend(v141, "setStrJymm:", v266);
LABEL_81:
            v238 = (v139[11] - v139[10]) >> 3;
            if ( (int)v238 <= 0 )
            {
LABEL_114:
              v199 = _objc_msgSend(v113, "getUserLogInfo");
              v200 = objc_retainAutoreleasedReturnValue(v199);
              v201 = _objc_msgSend(
                       &OBJC_CLASS___NSString,
                       "stringWithFormat:",
                       CFSTR("重置断线重连标志位:%@"),
                       v200);
              v202 = objc_retainAutoreleasedReturnValue(v201);
              v204 = _objc_msgSend(
                       &OBJC_CLASS___NSString,
                       v203,
                       CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                       "-[UserModel updateUserinfo:]",
                       651LL,
                       v202);
              v205 = objc_retainAutoreleasedReturnValue(v204);
              +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v205);
              _objc_msgSend(v277, "setNetworkState:", 0LL);
              _objc_msgSend(v206, "setHadAlertDisconnect:", 0LL);
              _objc_msgSend(v207, "StartCheck");
              v208 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
              v209 = objc_retainAutoreleasedReturnValue(v208);
              _objc_msgSend(v209, "postNotificationName:object:", CFSTR("update_useritem"), v210);
              if ( !*(_BYTE *)(v211 + 40) )
              {
                block[0] = _NSConcreteStackBlock;
                block[1] = 3254779904LL;
                block[2] = sub_100C1E027;
                block[3] = &unk_1012DB528;
                block[4] = v211;
                dispatch_async(&_dispatch_main_q, block);
              }
              *(_BYTE *)(v211 + 40) = 1;
              sub_100F7A7F0(&v280, (__int64)v271, "extend_data");
              if ( sub_100F9F630((__int64)&v280) )
              {
                sub_100FA0470((unsigned __int64 *)&v280, (unsigned __int64)"||", (unsigned __int64)"\r\n");
                v212 = +[tools splitString3:withSeparator:subSeparator:](
                         &OBJC_CLASS___tools,
                         "splitString3:withSeparator:subSeparator:",
                         v280,
                         "&",
                         "=");
                v213 = objc_retainAutoreleasedReturnValue(v212);
                v214 = v213;
                if ( v213 && _objc_msgSend(v213, "count") )
                {
                  _objc_msgSend(v214, "setObject:forKey:", v215, CFSTR("user"));
                  v216 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
                  v217 = objc_retainAutoreleasedReturnValue(v216);
                  _objc_msgSend(v217, "postNotificationName:object:", CFSTR("WtInteractionDataReady"), v214);
                }
              }
              sub_100C56008(&v280);
              v218 = v266;
LABEL_122:
LABEL_143:
              if ( v267 == 1541 )
              {
                nType &= 8u;
                if ( !nType )
                {
                  v236 = dispatch_time(0LL, 1000000000LL);
                  dispatch_after(v236, &_dispatch_main_q, &stru_1012EA208);
                }
              }
              goto LABEL_146;
            }
            v282 = (char *)v282 + 80;
            v240 = "length";
            v142 = "isEqualToString:";
            v239 = "stringWithCString:encoding:";
            v241 = "setStrZjzh:";
            v242 = "findMarket:";
            v260 = "init";
            v261 = "setBdefMarket:";
            v243 = "setStrKeycode_kcb:";
            v244 = "setStrMarketCode:";
            v245 = "setStrMarketName:";
            v246 = "getCodeTypeByScmc:";
            v247 = "setCCodeType:";
            v248 = "setStrHblx:";
            v259 = "setStrDefHblx:";
            v249 = "setStrKeyCode:";
            v250 = "setStrSjwtcl:";
            v251 = "integerValue";
            v252 = "setNCodeLen:";
            v253 = "findAccount:";
            v255 = "setStrAccount:";
            v256 = "boolValue";
            v257 = "setBDefAccount:";
            v254 = "addAccount:";
            v143 = 0LL;
            v258 = "addMarket:";
            v144 = 0;
            v263 = "isEqualToString:";
            while ( 1 )
            {
              v279 = (id)v143;
              v145 = sub_100CB2298(v282, v143, 0xB5Cu, v3);
              v146 = objc_retainAutoreleasedReturnValue(v145);
              v147 = v146;
              if ( !v146
                || !_objc_msgSend(v146, v240)
                || (unsigned __int8)_objc_msgSend(v147, v142, CFSTR("NULL"))
                || (unsigned __int8)_objc_msgSend(v147, v142, CFSTR("NUL")) )
              {
                v275 = v147;
                sub_100F7A7F0(&v280, (__int64)v271, "zjzh");
                v148 = _objc_msgSend(&OBJC_CLASS___NSString, v239, v280, 1LL);
                v147 = objc_retainAutoreleasedReturnValue(v148);
                sub_100C56008(&v280);
              }
              v149 = (unsigned __int8)_objc_msgSend(v147, v142, v278);
              v275 = v147;
              if ( !v149 )
                goto LABEL_110;
              v150 = _objc_msgSend(v113, "userInfo");
              v151 = objc_retainAutoreleasedReturnValue(v150);
              v152 = v147;
              v153 = v151;
              _objc_msgSend(v151, v241, v152);
              v154 = v282;
              v155 = sub_100CB2298(v282, (int)v279, 0x8A4u, v3);
              v156 = v113;
              v281 = objc_retainAutoreleasedReturnValue(v155);
              v158 = sub_100CB2298(v154, v157, 0x877u, v3);
              v159 = (const char *)objc_retainAutoreleasedReturnValue(v158);
              v274 = v159;
              v160 = _objc_msgSend(v156, "userInfo");
              v161 = objc_retainAutoreleasedReturnValue(v160);
              v163 = (void *)v162(v161, v242, v159);
              objc_retainAutoreleasedReturnValue(v163);
              v276 = v165;
              if ( !v165 )
              {
                v166 = objc_alloc((Class)&OBJC_CLASS___MarketModel);
                v167 = _objc_msgSend(v166, v260);
                if ( !v167 )
                {
                  LODWORD(v272) = v144;
                  v195 = 0LL;
                  v113 = v277;
LABEL_108:
                  v196 = _objc_msgSend(v113, "userInfo");
                  v197 = objc_retainAutoreleasedReturnValue(v196);
                  _objc_msgSend(v197, v258, v195);
                  v113 = v198;
                  goto LABEL_109;
                }
                v276 = v167;
              }
              LOBYTE(v164) = 1;
              LODWORD(v272) = v164;
              if ( (_BYTE)v144 )
              {
                v169 = v276;
              }
              else
              {
                v169 = v276;
                if ( (unsigned __int8)_objc_msgSend((id)v274, v263, CFSTR("1"))
                  || (unsigned __int8)_objc_msgSend((id)v274, v263, CFSTR("2")) )
                {
                  _objc_msgSend(v169, v261, 1LL);
                }
                else
                {
                  LODWORD(v272) = 0;
                }
              }
              _objc_msgSend(v169, v243, v281);
              _objc_msgSend(v169, v244, v274);
              v268 = v170;
              v171 = sub_100CB2298(v282, (int)v279, 0x87Bu, v3);
              v273 = objc_retainAutoreleasedReturnValue(v171);
              _objc_msgSend(v169, v245, v273);
              v113 = v277;
              v173 = (unsigned __int8)_objc_msgSend(&OBJC_CLASS___tools, v246, v172);
              _objc_msgSend(v169, v247, (unsigned int)v173);
              v174 = (int)v279;
              v175 = sub_100CB2298(v282, (int)v279, 0x87Cu, v3);
              v270 = objc_retainAutoreleasedReturnValue(v175);
              _objc_msgSend(v169, v248, v270);
              if ( !v174 )
              {
                v176 = _objc_msgSend(v113, "userInfo");
                v177 = objc_retainAutoreleasedReturnValue(v176);
                _objc_msgSend(v177, v259, v270);
                _objc_msgSend(v169, v261, 1LL);
                v113 = v277;
              }
              v178 = sub_100CB2298(v282, v174, 0x87Au, v3);
              v264 = (SEL)objc_retainAutoreleasedReturnValue(v178);
              _objc_msgSend(v169, v249, v264);
              v179 = sub_100CB2298(v282, v174, 0x8A5u, v3);
              v269 = (SEL)objc_retainAutoreleasedReturnValue(v179);
              _objc_msgSend(v169, v250, v269);
              v180 = sub_100CB2298(v282, v174, 0x878u, v3);
              v265 = (SEL)objc_retainAutoreleasedReturnValue(v180);
              v181 = _objc_msgSend((id)v265, v251);
              _objc_msgSend(v169, v252, v181);
              v182 = sub_100CB2298(v282, v174, 0x83Au, v3);
              v183 = objc_retainAutoreleasedReturnValue(v182);
              v184 = _objc_msgSend(v169, v253, v183);
              v185 = objc_retainAutoreleasedReturnValue(v184);
              v186 = v185;
              if ( v185 )
                break;
              v188 = objc_alloc((Class)&OBJC_CLASS___AccountModel);
              v189 = _objc_msgSend(v188, v260);
              if ( v189 )
              {
                v186 = v189;
                v187 = v189;
                goto LABEL_104;
              }
              v187 = 0LL;
              _objc_msgSend(v276, v254, 0LL);
LABEL_105:
              v195 = v276;
              if ( !v268 )
                goto LABEL_108;
LABEL_109:
              v144 = (int)v272;
              v142 = v263;
LABEL_110:
              v143 = (unsigned int)((_DWORD)v279 + 1);
              if ( (_DWORD)v238 == (_DWORD)v143 )
                goto LABEL_114;
            }
            v187 = v185;
LABEL_104:
            _objc_msgSend(v186, v255, v183);
            v191 = sub_100CB2298(v282, (int)v279, 0x880u, v3);
            v192 = objc_retainAutoreleasedReturnValue(v191);
            v193 = (unsigned __int8)_objc_msgSend(v192, v256);
            _objc_msgSend(v194, v257, (unsigned int)v193);
            v113 = v277;
            _objc_msgSend(v276, v254, v187);
            goto LABEL_105;
          }
LABEL_67:
          sub_100F7A7F0(&v280, (__int64)v271, "gdxm");
          v114 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v280);
          v115 = objc_retainAutoreleasedReturnValue(v114);
          sub_100C56008(&v280);
          v116 = _objc_msgSend(v113, "userInfo");
          v117 = objc_retainAutoreleasedReturnValue(v116);
          _objc_msgSend(v117, "setStrGdxm:", v115);
          goto LABEL_68;
        }
        v101 = v279;
      }
      else
      {
      }
    }
    else
    {
    }
    v113 = v277;
    goto LABEL_67;
  }
  v35 = (id *)v277;
  if ( v6 == 4096 )
  {
    v267 = 4096;
    sub_100F7A7F0(&v280, (__int64)v271, "extend_data");
    v36 = sub_100F9F630((__int64)&v280);
    sub_100C56008(&v280);
    if ( !v36 )
      goto LABEL_135;
    v37 = +[tools splitString3:withSeparator:subSeparator:](
            &OBJC_CLASS___tools,
            "splitString3:withSeparator:subSeparator:",
            v36,
            "&",
            "=");
    v38 = objc_retainAutoreleasedReturnValue(v37);
    v39 = v38;
    if ( !v38 )
    {
      goto LABEL_135;
    }
    v279 = v38;
    v40 = _objc_msgSend(v38, "objectForKey:", CFSTR("gdxm"));
    v278 = objc_retainAutoreleasedReturnValue(v40);
    v41 = _objc_msgSend(v39, "objectForKey:", CFSTR("session_id"));
    v275 = objc_retainAutoreleasedReturnValue(v41);
    if ( _objc_msgSend(v275, "length") )
      objc_storeStrong(v35 + 9, v41);
    v43 = _objc_msgSend(&OBJC_CLASS___JYPlistManage, v42);
    v44 = objc_retainAutoreleasedReturnValue(v43);
    v45 = _objc_msgSend(v35, "getQSID");
    v46 = objc_retainAutoreleasedReturnValue(v45);
    v47 = _objc_msgSend(v44, "getFxcpFormatString:", v46);
    v48 = objc_retainAutoreleasedReturnValue(v47);
    v50(v44);
    v282 = v48;
    if ( !v48
      || !_objc_msgSend(v282, "length")
      || (unsigned __int8)_objc_msgSend(v282, "isEqualToString:", CFSTR("NULL"))
      || (unsigned __int8)_objc_msgSend(v282, "isEqualToString:", CFSTR("NUL")) )
    {
      goto LABEL_130;
    }
    v51 = _objc_msgSend(v282, "componentsSeparatedByString:", CFSTR("|"));
    v52 = objc_retainAutoreleasedReturnValue(v51);
    if ( (unsigned __int64)_objc_msgSend(v52, "count") < 2 )
      goto LABEL_129;
    v270 = v53;
    v54 = _objc_msgSend(v53, "objectAtIndexedSubscript:", 1LL);
    v55 = objc_retainAutoreleasedReturnValue(v54);
    v56 = _objc_msgSend(v279, "objectForKey:", v55);
    v281 = objc_retainAutoreleasedReturnValue(v56);
    v272 = "objectAtIndex:";
    v274 = "rangeOfString:";
    v268 = "substringToIndex:";
    v269 = "substringFromIndex:";
    v264 = "substringWithRange:";
    v57 = 2LL;
    v265 = "integerValue";
    for ( i = _objc_msgSend(v58, "count"); ; i = _objc_msgSend(v270, "count") )
    {
      if ( v57 >= (unsigned __int64)i )
      {
        v223 = v281;
        if ( v281 && _objc_msgSend(v281, "length") )
          _objc_msgSend(v35, "setUserData:forKey:", v223, CFSTR("level_name"));
LABEL_129:
LABEL_130:
        v224 = _objc_msgSend(v279, "objectForKey:", CFSTR("xd_func_add"));
        v225 = objc_retainAutoreleasedReturnValue(v224);
        v226 = +[tools getBase64DecodeDataJHMode:](&OBJC_CLASS___tools, "getBase64DecodeDataJHMode:", v225);
        objc_retainAutoreleasedReturnValue(v226);
        v227 = v35[25];
        v35[25] = 0LL;
        v228 = objc_alloc((Class)&OBJC_CLASS___JYXMLParser);
        v229 = _objc_msgSend(v228, "initWithDelegate:", v35);
        _objc_msgSend(v229, "xmlParserWithData:", v230);
        if ( v278 )
        {
          if ( _objc_msgSend(v278, "length")
            && !(unsigned __int8)_objc_msgSend(v278, "isEqualToString:", CFSTR("NULL"))
            && !(unsigned __int8)_objc_msgSend(v278, "isEqualToString:", CFSTR("NUL")) )
          {
LABEL_138:
            if ( _objc_msgSend(v278, "length")
              && !(unsigned __int8)_objc_msgSend(v278, "isEqualToString:", CFSTR("NULL"))
              && !(unsigned __int8)_objc_msgSend(v278, "isEqualToString:", CFSTR("NUL")) )
            {
              v234 = _objc_msgSend(v35, "userInfo");
              v235 = objc_retainAutoreleasedReturnValue(v234);
              _objc_msgSend(v235, "setStrGdxm:", v278);
              v218 = v235;
              goto LABEL_122;
            }
            goto LABEL_143;
          }
LABEL_136:
          sub_100F7A7F0(&v280, (__int64)v271, "gdxm");
          v232 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v280);
          v233 = objc_retainAutoreleasedReturnValue(v232);
          sub_100C56008(&v280);
          if ( v233 )
          {
            v278 = v233;
            goto LABEL_138;
          }
          v278 = 0LL;
          goto LABEL_143;
        }
LABEL_135:
        v278 = 0LL;
        goto LABEL_136;
      }
      v276 = (id)v57;
      v61 = _objc_msgSend(v60, v272, v57);
      v62 = objc_retainAutoreleasedReturnValue(v61);
      v63 = (char *)_objc_msgSend(v62, v274, CFSTR("@"));
      if ( v63 == (char *)0x7FFFFFFFFFFFFFFFLL )
      {
        v63 = (char *)_objc_msgSend(v62, v274, CFSTR("*"));
        if ( v63 == (char *)0x7FFFFFFFFFFFFFFFLL )
          goto LABEL_23;
        if ( v63 )
        {
          v64 = 0;
        }
        else
        {
          if ( _objc_msgSend(v62, "length") == (id)1 )
          {
LABEL_23:
            v64 = 0;
            goto LABEL_25;
          }
          v64 = 0;
          v63 = 0LL;
        }
      }
      else
      {
        v64 = 1;
      }
LABEL_25:
      if ( _objc_msgSend(v62, "length") )
      {
        if ( v64 )
        {
          v66 = v281;
          v67 = _objc_msgSend(v62, v264, 0LL, v63);
          v273 = objc_retainAutoreleasedReturnValue(v67);
          if ( v63 >= (char *)v68(v62, "length") - 1 )
          {
            v73 = _objc_msgSend(v66, "componentsSeparatedByString:", v273);
          }
          else
          {
            v70 = v69(v62, v269, v63 + 1);
            v71 = objc_retainAutoreleasedReturnValue(v70);
            v72(v71, v265);
            v73 = _objc_msgSend(v281, "componentsSeparatedByString:", v273);
          }
          v78 = objc_retainAutoreleasedReturnValue(v73);
          v79 = v78;
          if ( v80 < 0 )
            goto LABEL_56;
          v81 = _objc_msgSend(v78, "count");
          if ( v82 >= (unsigned __int64)v81 )
            goto LABEL_56;
          v83 = _objc_msgSend(v79, v272, v82);
          v84 = objc_retainAutoreleasedReturnValue(v83);
          v85 = v281;
          goto LABEL_55;
        }
        if ( v65 )
        {
          if ( v63 )
          {
            v273 = 0LL;
            v74 = _objc_msgSend(v62, v268, v63);
            v75 = objc_retainAutoreleasedReturnValue(v74);
          }
          else
          {
            v75 = 0LL;
          }
          v273 = v75;
          if ( v63 >= (char *)_objc_msgSend(v62, "length") - 1 )
          {
            v79 = 0LL;
          }
          else
          {
            v90 = _objc_msgSend(v62, v269, v63 + 1);
            v79 = objc_retainAutoreleasedReturnValue(v90);
          }
          if ( v273 )
          {
            v91 = (char *)_objc_msgSend(v89, v274);
            if ( v91 == (char *)0x7FFFFFFFFFFFFFFFLL || v91 >= (char *)_objc_msgSend(v281, "length") - 1 )
            {
              v89 = v281;
            }
            else
            {
              v92 = _objc_msgSend(v281, v269, v91 + 1);
              objc_retainAutoreleasedReturnValue(v92);
            }
          }
          if ( !v79 || (v93 = _objc_msgSend(v89, v274, v79)) == 0LL || v93 == (id)0x7FFFFFFFFFFFFFFFLL )
          {
            v281 = v89;
LABEL_56:
            goto LABEL_57;
          }
          v94 = _objc_msgSend(v89, v268, v93);
          v84 = objc_retainAutoreleasedReturnValue(v94);
          v85 = v95;
LABEL_55:
          v281 = v84;
          goto LABEL_56;
        }
        v76 = _objc_msgSend(v281, v274, v62);
        if ( v76 != (id)0x7FFFFFFFFFFFFFFFLL )
        {
          v86 = _objc_msgSend(v77, v268, v76);
          v87 = objc_retainAutoreleasedReturnValue(v86);
          v281 = v87;
        }
      }
LABEL_57:
      v57 = (unsigned __int64)v276 + 1;
      v35 = (id *)v277;
    }
  }
LABEL_146:
  sub_100D12680((__int64 *)&v271);
}

//----- (0000000100C1E027) ----------------------------------------------------
id __fastcall sub_100C1E027(__int64 a1)
{
  return _objc_msgSend(*(id *)(a1 + 32), "sendUserInfoToServer");
}

//----- (0000000100C1E03D) ----------------------------------------------------
void __cdecl sub_100C1E03D(id a1)
{

  v1 = +[JYZyyManager shareInstance](&OBJC_CLASS___JYZyyManager, "shareInstance");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  _objc_msgSend(v2, "requestXcsQuAccount");
}

//----- (0000000100C1E095) ----------------------------------------------------
void __cdecl -[UserModel handleDeptinfo:](UserModel *self, SEL a2, void *a3)
{
  SEL v8; // r12
  unsigned int v12; // r13d
  int v13; // eax
  unsigned int v23; // eax
  __CFString *v35; // r13
  SEL v37; // r12
  SEL v40; // r12
  unsigned int v51; // eax
  UserModel *v52; // r15
  NSWtYybNotice *v54; // rax
  NSWtYybNotice *yybnoticeWin; // rdi
  _QWORD block[5]; // [rsp+0h] [rbp-A0h] BYREF
  UserModel *v62; // [rsp+40h] [rbp-60h]
  volatile signed __int32 *v64; // [rsp+50h] [rbp-50h] BYREF
  unsigned int v67; // [rsp+6Ch] [rbp-34h]
  int v68; // [rsp+70h] [rbp-30h]

  sub_100D6DC60(&v61, (__int64)a3);
  sub_100F7A7F0(&v64, v61, "yybnotice");
  v3 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v64);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  sub_100C56008(&v64);
  if ( v4 )
  {
    if ( _objc_msgSend(v4, "length") )
    {
      v5 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v7 = _objc_msgSend(v6, "GetConfigValue:Name:DefaultString:", "system", "WT_LastNotiID", &charsToLeaveEscaped);
      v62 = self;
      v65 = objc_retainAutoreleasedReturnValue(v7);
      v9 = _objc_msgSend(&OBJC_CLASS___configmanager, v8);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v10, "GetConfigValue:Name:DefaultInteger:", "system", "WT_TipDlg_ShowAgain", 1LL);
      v12 = 0;
      LOBYTE(v13) = v11 == (id)1;
      v68 = v13;
      _objc_msgSend(v4, "rangeOfString:", CFSTR("\n"));
      v14 = +[tools getMd5String:](&OBJC_CLASS___tools, "getMd5String:", v4);
      v66 = objc_retainAutoreleasedReturnValue(v14);
      v15 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v17 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v66);
      _objc_msgSend(v16, "SetConfigValue:Name:StringValue:", "system", "WT_LastNotiID", v17);
      if ( v18 <= 0 )
      {
        v67 = 0;
        v63 = 0LL;
      }
      else
      {
        v19 = v18 - 1;
        if ( (unsigned __int16)_objc_msgSend(v4, "characterAtIndex:", v18 - 1) == 13 )
          v20 = v19;
        v21 = _objc_msgSend(v4, "substringToIndex:", v20);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        v23 = (unsigned int)_objc_msgSend(v66, "isEqualToString:", v65);
        v24 = v23;
        LOBYTE(v23) = (_BYTE)v23 == 0;
        v67 = v23;
        v25 = _objc_msgSend(v22, "rangeOfString:", CFSTR("#"));
        LOBYTE(v12) = v68;
        v27 = 1LL;
        if ( v24 )
          v27 = v12;
        if ( v25 == (id)0x7FFFFFFFFFFFFFFFLL )
        {
          v68 = v27;
          v28 = 0LL;
        }
        else
        {
          v29 = _objc_msgSend(v22, "substringToIndex:", 1LL, v27);
          v30 = objc_retainAutoreleasedReturnValue(v29);
          v28 = _objc_msgSend(v30, "integerValue");
          v68 = (int)v28;
        }
        v31 = _objc_msgSend(v4, "substringFromIndex:", v26 + 1);
        v63 = v28;
        v32 = objc_retainAutoreleasedReturnValue(v31);
        v33(v22);
        v4 = v32;
      }
      v34 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
      v35 = &charsToLeaveEscaped;
      v36 = objc_retainAutoreleasedReturnValue(v34);
      v60 = _objc_msgSend(v36, v37, "system", "WT_LastNotiDate", 0LL);
      v38 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
      v39 = objc_retainAutoreleasedReturnValue(v38);
      v41 = _objc_msgSend(v39, v40, "system", "WT_TipDlg_ShowToday", 1LL);
      v42 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
      v43 = _objc_msgSend(v42, "init");
      if ( !v43 )
        goto LABEL_16;
      _objc_msgSend(v43, "setDateFormat:", CFSTR("yyyyMMdd"));
      v44 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
      v45 = objc_retainAutoreleasedReturnValue(v44);
      v47 = _objc_msgSend(v46, "stringFromDate:", v45);
      v48 = v45;
      v35 = (__CFString *)objc_retainAutoreleasedReturnValue(v47);
      if ( v41 == (id)1
        || (v56 = _objc_msgSend(v35, "integerValue"), (unsigned __int8)v67 | (unsigned __int8)v63)
        || v56 != v60 )
      {
LABEL_16:
        if ( (_BYTE)v68 )
        {
          v49 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
          v50 = objc_retainAutoreleasedReturnValue(v49);
          v51 = (unsigned int)_objc_msgSend(v35, "intValue");
          _objc_msgSend(v50, "SetConfigValue:Name:IntegerValue:", "system", "WT_LastNotiDate", v51);
          v52 = v62;
          if ( !v62->_yybnoticeWin )
          {
            v53 = objc_alloc((Class)&OBJC_CLASS___NSWtYybNotice);
            v54 = (NSWtYybNotice *)_objc_msgSend(v53, "initWithWindowNibName:", CFSTR("wtyybnotice"));
            v52 = v62;
            yybnoticeWin = v62->_yybnoticeWin;
            v62->_yybnoticeWin = v54;
          }
          block[0] = _NSConcreteStackBlock;
          block[1] = 3254779904LL;
          block[2] = sub_100C1E735;
          block[3] = &unk_1012EACF8;
          block[4] = v52;
          v59 = objc_retain(v4);
          dispatch_async(&_dispatch_main_q, block);
        }
      }
    }
  }
  else
  {
    v4 = 0LL;
  }
  sub_100D12680(&v61);
}

//----- (0000000100C1E735) ----------------------------------------------------
_QWORD *__fastcall sub_100C1E735(__int64 a1)
{
  _QWORD *result; // rax

  result = *(_QWORD **)(a1 + 32);
  v3 = (void *)result[23];
  if ( v3 )
  {
    _objc_msgSend(v3, "setStrShowTip:", *(_QWORD *)(a1 + 40));
    return _objc_msgSend(*(id *)(*(_QWORD *)(a1 + 32) + 184LL), "showWindow:");
  }
  return result;
}

//----- (0000000100C1E79E) ----------------------------------------------------
void __cdecl -[UserModel timerFired](UserModel *self, SEL a2)
{
  commumodule *v2; // rax
  commumodule *v3; // r13
  NSString *v8; // rax
  NSString *v9; // r14
  SEL v10; // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  int v26; // ebx
  id (*v27)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  NSString *v32; // rax
  NSString *v33; // rax
  NSString *v34; // rax
  NSString *v35; // r14
  NSString *v39; // rax
  NSString *v40; // r14
  NSString *v41; // rax
  NSString *v42; // rbx
  SEL v43; // r12

  v2 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[commumodule isKilledByServer](v3, "isKilledByServer");
  if ( v4 )
  {
    v45 = v5;
    v6 = _objc_msgSend(v5, "getUserLogInfo");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("被踢掉不用自动重连:%@"), v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            v10,
            CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
            "-[UserModel timerFired]",
            778LL,
            v9);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v12);
    _objc_msgSend(v45, "stopStockTradePush");
    _objc_msgSend(v45, "StopReconnect");
    _objc_msgSend(v45, "StopCheck");
    v13 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v14, "postNotificationName:object:", CFSTR("WTKilledByServer"), v45);
LABEL_3:
    return;
  }
  v15 = _objc_msgSend(v5, "tradeCommuModel");
  v14 = objc_retainAutoreleasedReturnValue(v15);
  if ( !(unsigned __int8)_objc_msgSend(v14, "isDisconnected") || !*(_BYTE *)(v16 + 82) )
    goto LABEL_3;
  v17 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v46 = v19;
  v20 = (unsigned __int8)_objc_msgSend(v18, "isUserShow:", v19);
  v21(v14);
  if ( v20 )
  {
    _objc_msgSend(v46, "StopReconnect");
    v23 = v22(v46, "tradeCommuModel");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v26 = (unsigned int)v25(v24, "reconnectSession");
    v28 = v27(&OBJC_CLASS___NSNumber, "numberWithInt:", (unsigned int)v26);
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v31 = v30(v46, "getUserLogInfo");
    v44 = objc_retainAutoreleasedReturnValue(v31);
    v32 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("进行自动重连，状态nStatus:%@-%@"),
            v29,
            v44);
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
            "-[UserModel timerFired]",
            791LL,
            v33);
    v35 = objc_retainAutoreleasedReturnValue(v34);
    +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v35);
    if ( v26 < 0 )
    {
      if ( !(unsigned __int8)_objc_msgSend(v46, "hadAlertDisconnect") )
      {
        _objc_msgSend(v46, "setHadAlertDisconnect:", 1LL);
        _objc_msgSend(v46, "showDisconnectConfirm");
        v37 = _objc_msgSend(v46, "getUserLogInfo");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        v39 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("self.hadAlertDisconnect = NO 自动重连失败，进行断线弹窗:%@"),
                v38);
        v40 = objc_retainAutoreleasedReturnValue(v39);
        v41 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                "-[UserModel timerFired]",
                799LL,
                v40);
        v42 = objc_retainAutoreleasedReturnValue(v41);
        _objc_msgSend(&OBJC_CLASS___tools, v43, v42);
      }
    }
    else
    {
      _objc_msgSend(v46, "setHadAlertDisconnect:", 0LL);
    }
  }
}

//----- (0000000100C1EC36) ----------------------------------------------------
void __cdecl -[UserModel timerReconnect](UserModel *self, SEL a2)
{
  commumodule *v2; // rax
  commumodule *v3; // r14
  int v4; // r15d

  if ( self->_bActiveUser )
  {
    v2 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = (unsigned int)-[commumodule reconnectSession](v3, "reconnectSession");
    if ( v4 >= 0 )
    {
      v5(self, "StopReconnect");
      v6(self, "StartCheck");
    }
  }
}

//----- (0000000100C1ECCF) ----------------------------------------------------
void __cdecl -[UserModel showDisconnectConfirm](UserModel *self, SEL a2)
{
  NSString *v9; // rax
  NSString *v10; // r13
  NSString *v11; // rax
  NSString *v12; // rbx
  int v24; // [rsp+0h] [rbp-A0h]
  _QWORD v25[5]; // [rsp+28h] [rbp-78h] BYREF
  id to; // [rsp+50h] [rbp-50h] BYREF
  id location[6]; // [rsp+70h] [rbp-30h] BYREF

  if ( !self->_isDisconnectAlertShowing )
  {
    v2 = _objc_msgSend(&OBJC_CLASS___NSApplication, "sharedApplication");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = _objc_msgSend(v3, "mainWindow");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "isVisible");
    if ( v6 )
    {
      v7 = -[UserModel getUserLogInfo](self, "getUserLogInfo");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("断线重连弹提示框:%@"), v8);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
              "-[UserModel showDisconnectConfirm]",
              828LL,
              v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      +[tools writeFileLog:](&OBJC_CLASS___tools, "writeFileLog:", v12);
      objc_initWeak(location, self);
      self->_isDisconnectAlertShowing = 1;
      v14 = _objc_msgSend(&OBJC_CLASS___NSApplication, "sharedApplication");
      v28 = objc_retainAutoreleasedReturnValue(v14);
      v15 = _objc_msgSend(v28, "mainWindow");
      v27 = &OBJC_CLASS___tools;
      v29 = objc_retainAutoreleasedReturnValue(v15);
      v16 = +[JYThemesManage shareInstance](&OBJC_CLASS___JYThemesManage, "shareInstance");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18 = _objc_msgSend(v17, "alert_Image_NormalPic");
      objc_retainAutoreleasedReturnValue(v18);
      v25[0] = _NSConcreteStackBlock;
      v25[1] = 3254779904LL;
      v25[2] = sub_100C1F013;
      v25[3] = &unk_1012EACC8;
      objc_copyWeak(&to, location);
      v25[4] = self;
      v24 = 0;
      v19 = v29;
      v21 = _objc_msgSend(
              v27,
              "showAlertToWindowCustomBtnName:title:message:iconImage:isNeedCancenBtn:OkBtnName:CancelBtnName:completionHandler:",
              v29,
              CFSTR("连接委托主站失败！可能是以下原因："),
              CFSTR("(1) 计算机没有连接到互联网；\n(2) 通过代理上网，代理设置不正确；\n(3) 防火墙阻挡了通讯；\n(4) 营业部的IP地址或域名设置不正确"),
              v20,
              v24,
              CFSTR("确定"),
              0LL,
              v25);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      objc_destroyWeak(&to);
      objc_destroyWeak(location);
    }
  }
}

//----- (0000000100C1F013) ----------------------------------------------------
__int64 __fastcall sub_100C1F013(__int64 a1, __int64 a2)
{
  id WeakRetained; // rbx

  if ( a2 == 1000 )
  {
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(WeakRetained, "StartReconnect");
    v3 = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(v3, "StopCheck");
    v4 = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(v4, "setHadAlertDisconnect:", 0LL);
  }
  result = *(_QWORD *)(a1 + 32);
  *(_BYTE *)(result + 41) = 0;
  return result;
}

//----- (0000000100C1F0CD) ----------------------------------------------------
void __cdecl -[UserModel sendUserInfoToServer](UserModel *self, SEL a2)
{
  UserInfoModel *v2; // rax
  UserInfoModel *v3; // rax
  NSString *v4; // rax
  NSString *v5; // r15
  UserInfoModel *v8; // rax
  UserInfoModel *v9; // rax
  NSString *v10; // rax
  NSString *v11; // rbx
  commumodule *v14; // rax
  commumodule *v15; // r13
  commumodule *v21; // rax
  commumodule *v22; // rbx
  NSString *v41; // rax
  NSString *v42; // r13
  NSString *v44; // rax
  NSString *v45; // r15
  NSString *v47; // rax
  NSString *v48; // r14

  v2 = -[UserModel userInfo](self, "userInfo");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[UserInfoModel strLoginAccount](v3, "strLoginAccount");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
         &OBJC_CLASS___tools,
         "stringIsNilOrEmpty:ifNilReturnDefString:",
         v5,
         &charsToLeaveEscaped);
  v54 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[UserModel userInfo](self, "userInfo");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = -[UserInfoModel strZjzh](v9, "strZjzh");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
          &OBJC_CLASS___tools,
          "stringIsNilOrEmpty:ifNilReturnDefString:",
          v11,
          &charsToLeaveEscaped);
  v50 = objc_retainAutoreleasedReturnValue(v12);
  v14 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = -[commumodule getUserid](v15, "getUserid");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = _objc_msgSend(v18, "stringIsNilOrEmpty:ifNilReturnDefString:", v17, &charsToLeaveEscaped);
  v51 = objc_retainAutoreleasedReturnValue(v19);
  v20 = +[tools getTradeProductAndVersion](&OBJC_CLASS___tools, "getTradeProductAndVersion");
  v52 = objc_retainAutoreleasedReturnValue(v20);
  v21 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = -[commumodule getSessionIpAndPort](v22, "getSessionIpAndPort");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v25 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
          &OBJC_CLASS___tools,
          "stringIsNilOrEmpty:ifNilReturnDefString:",
          v24,
          &charsToLeaveEscaped);
  v53 = objc_retainAutoreleasedReturnValue(v25);
  v26 = +[servercfgManager shareInstance](&OBJC_CLASS___servercfgManager, "shareInstance");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v29 = _objc_msgSend(v28, "userInfo");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v31 = _objc_msgSend(v30, "strQsName");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  v33 = _objc_msgSend(v27, "getQsidByName:", v32);
  v34 = objc_retainAutoreleasedReturnValue(v33);
  v36 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
          &OBJC_CLASS___tools,
          "stringIsNilOrEmpty:ifNilReturnDefString:",
          v34,
          &charsToLeaveEscaped);
  v55 = objc_retainAutoreleasedReturnValue(v36);
  v37 = +[tools getMacAddr](&OBJC_CLASS___tools, "getMacAddr");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  v39 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
          &OBJC_CLASS___tools,
          "stringIsNilOrEmpty:ifNilReturnDefString:",
          v38,
          &charsToLeaveEscaped);
  v40 = objc_retainAutoreleasedReturnValue(v39);
  v41 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@;"), v40);
  v42 = objc_retainAutoreleasedReturnValue(v41);
  v44 = _objc_msgSend(
          &OBJC_CLASS___NSString,
          "stringWithFormat:",
          CFSTR("userid=%@version=mac_%@qsid=%@dlzh=%@ip=%@mac=%@callway=0loginmode=0zjzh=%@dumpState=0type=mac"),
          v51,
          v52,
          v55,
          v54,
          v53,
          v42,
          v50);
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = +[tools getDateWithFormatter:](&OBJC_CLASS___tools, "getDateWithFormatter:", CFSTR("yyyyMMdd"));
  v47 = _objc_msgSend(
          &OBJC_CLASS___NSString,
          "stringWithFormat:",
          CFSTR("%@%@/?%@=%@&%@=%@&%@=%@&%@=%@"),
          CFSTR("http://xdres.10jqka.com.cn"),
          CFSTR("/update"),
          CFSTR("p"),
          CFSTR("upload_userinfo"),
          CFSTR("v"),
          v46,
          CFSTR("qsid"),
          v55,
          CFSTR("userinfo"),
          v45);
  v48 = objc_retainAutoreleasedReturnValue(v47);
  -[JYHTTPRequestManage imageRequestWithURLStr:sendDic:](
    self->_httpRequest,
    "imageRequestWithURLStr:sendDic:",
    v48,
    0LL);
  -[UserModel sendTradeLoginSuccessMaiDian](self, "sendTradeLoginSuccessMaiDian");
}

//----- (0000000100C1F6C7) ----------------------------------------------------
void __cdecl -[UserModel uploadServerError:](UserModel *self, SEL a2, id a3)
{
  commumodule *v13; // rax
  commumodule *v14; // r15
  commumodule *v21; // rax
  NSString *v31; // rax
  NSString *v32; // rbx
  NSString *v34; // rax
  NSString *v35; // r15
  NSString *v37; // rax
  NSString *v38; // rbx
  NSString *v42; // [rsp+48h] [rbp-38h]
  commumodule *v43; // [rsp+50h] [rbp-30h]

  v3 = objc_retain(a3);
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "length")
      && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NULL"))
      && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", CFSTR("NUL")) )
    {
      v5 = _objc_msgSend(v4, "stringByReplacingOccurrencesOfString:withString:", CFSTR("="), CFSTR(":"));
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v8 = _objc_msgSend(v6, "stringByReplacingOccurrencesOfString:withString:", CFSTR(" "), CFSTR("+"));
      objc_retainAutoreleasedReturnValue(v8);
      v10 = +[HXTools urlencode:](&OBJC_CLASS___HXTools, "urlencode:", v9);
      v12 = v11;
      objc_retainAutoreleasedReturnValue(v10);
      v13 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = -[commumodule getUserid](v14, "getUserid");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v17 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
              &OBJC_CLASS___tools,
              "stringIsNilOrEmpty:ifNilReturnDefString:",
              v16,
              &charsToLeaveEscaped);
      v39 = objc_retainAutoreleasedReturnValue(v17);
      v18 = +[tools getTradeProductAndVersion](&OBJC_CLASS___tools, "getTradeProductAndVersion");
      v40 = objc_retainAutoreleasedReturnValue(v18);
      v19 = +[servercfgManager shareInstance](&OBJC_CLASS___servercfgManager, "shareInstance");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
      v43 = objc_retainAutoreleasedReturnValue(v21);
      v22 = -[commumodule getLoginBroker](v43, "getLoginBroker");
      v23 = objc_retainAutoreleasedReturnValue(v22);
      v24 = _objc_msgSend(v20, "getQsidByName:", v23);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v26 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
              &OBJC_CLASS___tools,
              "stringIsNilOrEmpty:ifNilReturnDefString:",
              v25,
              &charsToLeaveEscaped);
      v44 = objc_retainAutoreleasedReturnValue(v26);
      v27 = +[tools getMacAddr](&OBJC_CLASS___tools, "getMacAddr");
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29 = +[tools stringIsNilOrEmpty:ifNilReturnDefString:](
              &OBJC_CLASS___tools,
              "stringIsNilOrEmpty:ifNilReturnDefString:",
              v28,
              &charsToLeaveEscaped);
      v41 = v28;
      v30 = objc_retainAutoreleasedReturnValue(v29);
      v31 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@;"), v30);
      v32 = objc_retainAutoreleasedReturnValue(v31);
      v42 = v32;
      v34 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("userid=%@version=mac_%@qsid=%@mac=%@callway=0loginmode=0servererror=%@dumpState=0type=mac"),
              v39,
              v40,
              v44,
              v32,
              v33);
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v36 = +[tools getDateWithFormatter:](&OBJC_CLASS___tools, "getDateWithFormatter:", CFSTR("yyyyMMdd"));
      v37 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("%@%@/?%@=%@&%@=%@&%@=%@&%@=%@"),
              CFSTR("http://xdres.10jqka.com.cn"),
              CFSTR("/update"),
              CFSTR("p"),
              CFSTR("upload_userinfo"),
              CFSTR("v"),
              v36,
              CFSTR("qsid"),
              v44,
              CFSTR("userinfo"),
              v35);
      v38 = objc_retainAutoreleasedReturnValue(v37);
      -[JYHTTPRequestManage imageRequestWithURLStr:sendDic:](
        self->_httpRequest,
        "imageRequestWithURLStr:sendDic:",
        v38,
        0LL);
    }
  }
  else
  {
    v4 = 0LL;
  }
}

//----- (0000000100C1FBC0) ----------------------------------------------------
void __cdecl -[UserModel xmlOrJsonRequestSuccess:sourceDic:andSendDic:](UserModel *self, SEL a2, id a3, id a4, id a5)
{
  ;
}

//----- (0000000100C1FBC6) ----------------------------------------------------
void __cdecl -[UserModel imageRequestSuccess:iamgeData:andSendDic:](UserModel *self, SEL a2, id a3, id a4, id a5)
{
  ;
}

//----- (0000000100C1FBCC) ----------------------------------------------------
void __cdecl -[UserModel requestFailure:Msg:andSendDic:](UserModel *self, SEL a2, id a3, id a4, id a5)
{
  ;
}

//----- (0000000100C1FBD2) ----------------------------------------------------
void __cdecl -[UserModel SetType:](UserModel *self, SEL a2, int a3)
{
  self->nType |= a3;
}

//----- (0000000100C1FBE2) ----------------------------------------------------
int __cdecl -[UserModel GetType](UserModel *self, SEL a2)
{
  return self->nType;
}

//----- (0000000100C1FBF2) ----------------------------------------------------
void __cdecl -[UserModel setIsMeiguUser:](UserModel *self, SEL a2, char a3)
{
  commumodule *v3; // rax
  commumodule *v4; // rbx

  if ( self->_isMeiguUser != a3 )
  {
    self->_isMeiguUser = a3;
    v3 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    -[commumodule reloadSetIsMeiguUser](v4, "reloadSetIsMeiguUser");
  }
}

//----- (0000000100C1FC57) ----------------------------------------------------
void __cdecl -[UserModel setIsMoniUser:](UserModel *self, SEL a2, char a3)
{
  commumodule *v3; // rax
  commumodule *v4; // rbx

  if ( self->_isMoniUser != a3 )
  {
    self->_isMoniUser = a3;
    v3 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    -[commumodule reloadSetIsMoniUser](v4, "reloadSetIsMoniUser");
  }
}

//----- (0000000100C1FCBC) ----------------------------------------------------
void __cdecl -[UserModel setIsZyyUser:](UserModel *self, SEL a2, char a3)
{
  commumodule *v3; // rax
  commumodule *v4; // rbx

  if ( self->_isZyyUser != a3 )
  {
    self->_isZyyUser = a3;
    v3 = -[UserModel tradeCommuModel](self, "tradeCommuModel");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    -[commumodule reloadSetIsZyyUser](v4, "reloadSetIsZyyUser");
  }
}

//----- (0000000100C1FD21) ----------------------------------------------------
char __cdecl -[UserModel isDisconnect](UserModel *self, SEL a2)
{
  commumodule *tradeCommuModel; // rdi

  tradeCommuModel = self->_tradeCommuModel;
  if ( tradeCommuModel )
    return (unsigned __int8)-[commumodule isDisconnected](tradeCommuModel, "isDisconnected");
  else
    return 0;
}

//----- (0000000100C1FD47) ----------------------------------------------------
void __cdecl -[UserModel Disconnect](UserModel *self, SEL a2)
{
  commumodule *tradeCommuModel; // rdi

  tradeCommuModel = self->_tradeCommuModel;
  if ( tradeCommuModel )
    -[commumodule Disconnect](tradeCommuModel, "Disconnect");
}

//----- (0000000100C1FD6B) ----------------------------------------------------
void __cdecl -[UserModel StartCheck](UserModel *self, SEL a2)
{
  NSTimer *v3; // rax
  NSTimer *v4; // rax
  NSTimer *sessionTimer; // rdi

  if ( !self->sessionTimer )
  {
    v3 = _objc_msgSend(
           &OBJC_CLASS___NSTimer,
           "timerWithTimeInterval:target:selector:userInfo:repeats:",
           self,
           "timerFired",
           0LL,
           1LL,
           3.0);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    sessionTimer = self->sessionTimer;
    self->sessionTimer = v4;
  }
  v6 = _objc_msgSend(&OBJC_CLASS___NSRunLoop, "mainRunLoop");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "addTimer:forMode:", self->sessionTimer, NSDefaultRunLoopMode);
}

//----- (0000000100C1FE2F) ----------------------------------------------------
void __cdecl -[UserModel StopCheck](UserModel *self, SEL a2)
{
  NSTimer *sessionTimer; // rdi

  _objc_msgSend(self->sessionTimer, "invalidate");
  sessionTimer = self->sessionTimer;
  self->sessionTimer = 0LL;
}

//----- (0000000100C1FE67) ----------------------------------------------------
void __cdecl -[UserModel StartReconnect](UserModel *self, SEL a2)
{
  NSTimer *v3; // rax
  objc_class *v4; // rax

  if ( !self->reconnectTimer )
  {
    v3 = _objc_msgSend(
           &OBJC_CLASS___NSTimer,
           "timerWithTimeInterval:target:selector:userInfo:repeats:",
           self,
           "timerReconnect",
           0LL,
           1LL,
           3.0);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v6 = *(Class *)((char *)&self->super.super.isa + v5);
    *(Class *)((char *)&self->super.super.isa + v5) = v4;
    v7 = _objc_msgSend(&OBJC_CLASS___NSRunLoop, "mainRunLoop");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "addTimer:forMode:", *(Class *)((char *)&self->super.super.isa + v9), NSDefaultRunLoopMode);
  }
}

//----- (0000000100C1FF31) ----------------------------------------------------
void __cdecl -[UserModel StopReconnect](UserModel *self, SEL a2)
{
  NSTimer *reconnectTimer; // rdi
  NSTimer *v4; // rdi

  reconnectTimer = self->reconnectTimer;
  if ( reconnectTimer )
  {
    _objc_msgSend(reconnectTimer, "invalidate");
    v4 = self->reconnectTimer;
    self->reconnectTimer = 0LL;
  }
}

//----- (0000000100C1FF73) ----------------------------------------------------
void __cdecl -[UserModel GetChicang](UserModel *self, SEL a2)
{
  NSDate *v5; // rax
  NSDate *v6; // rbx
  NSDate *RefreshChiCang; // rdi
  CacheDataObject *v9; // rax
  CacheDataObject *v10; // rbx

  v4 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
  v5 = (NSDate *)objc_retainAutoreleasedReturnValue(v4);
  v6 = v5;
  if ( !v7 )
  {
    RefreshChiCang = self->RefreshChiCang;
    self->RefreshChiCang = v5;
    goto LABEL_5;
  }
  _objc_msgSend(v5, "timeIntervalSinceDate:", self->RefreshChiCang);
  if ( v2 >= 30.0 )
  {
LABEL_5:
    v9 = -[UserModel aCacheData](self, "aCacheData");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    -[CacheDataObject requestChicang](v10, "requestChicang");
  }
}

//----- (0000000100C20057) ----------------------------------------------------
void __cdecl -[UserModel xmlParserSuccess:](UserModel *self, SEL a2, id a3)
{
  NSArray *v9; // rax
  NSArray *v10; // rax
  NSArray *extraMenuItems; // rdi

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "objectForKey:", CFSTR("item"));
  objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) && _objc_msgSend(v8, "count") )
  {
    v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v8);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    extraMenuItems = self->_extraMenuItems;
    self->_extraMenuItems = v10;
  }
}

//----- (0000000100C20145) ----------------------------------------------------
void __cdecl -[UserModel xmlParserFailureMsg:](UserModel *self, SEL a2, id a3)
{
  ;
}

//----- (0000000100C2014B) ----------------------------------------------------
id __cdecl -[UserModel getMenuItemByID:](UserModel *self, SEL a2, signed __int64 a3)
{
  NSArray *extraMenuItems; // rdi
  id (**v6)(id, SEL, ...); // r13
  NSString *v7; // rax
  id (**v12)(id, SEL, ...); // r14
  SEL v21; // [rsp+40h] [rbp-E0h]
  SEL v22; // [rsp+48h] [rbp-D8h]
  id obj; // [rsp+68h] [rbp-B8h]

  extraMenuItems = self->_extraMenuItems;
  if ( !extraMenuItems || !_objc_msgSend(extraMenuItems, "count") )
    return objc_autoreleaseReturnValue(0LL);
  v6 = &_objc_msgSend;
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), a3);
  v25 = objc_retainAutoreleasedReturnValue(v7);
  v17 = 0LL;
  v18 = 0LL;
  v19 = 0LL;
  v20 = 0LL;
  obj = objc_retain(self->_extraMenuItems);
  v8 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v27, 16LL);
  if ( v8 )
  {
    v23 = *(_QWORD *)v18;
    while ( 2 )
    {
      v21 = "objectForKey:";
      v22 = "isEqualToString:";
      v9 = 0LL;
      v24 = v8;
      do
      {
        if ( *(_QWORD *)v18 != v23 )
          objc_enumerationMutation(obj);
        v10 = ((id (*)(id, SEL, ...))v6)(*(id *)(*((_QWORD *)&v17 + 1) + 8LL * (_QWORD)v9), v21, CFSTR("id"));
        v11 = objc_retainAutoreleasedReturnValue(v10);
        v12 = v6;
        v13 = (unsigned __int8)((id (*)(id, SEL, ...))v6)(v25, v22, v11);
        if ( v13 )
        {
          v15 = objc_retain(v14);
          goto LABEL_16;
        }
        v9 = (char *)v9 + 1;
        v6 = v12;
      }
      while ( v24 != v9 );
      v8 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v12)(
                 obj,
                 "countByEnumeratingWithState:objects:count:",
                 &v17,
                 v27,
                 16LL);
      if ( v8 )
        continue;
      break;
    }
  }
  v15 = 0LL;
LABEL_16:
  return objc_autoreleaseReturnValue(v15);
}

//----- (0000000100C203BA) ----------------------------------------------------
id __cdecl -[UserModel getQSID](UserModel *self, SEL a2)
{
  __CFString *v10; // rbx

  v2 = -[UserModel getUserInfo](self, "getUserInfo");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( v3 )
  {
    v4 = +[servercfgManager shareInstance](&OBJC_CLASS___servercfgManager, "shareInstance");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = _objc_msgSend(v6, "strQsName");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v5, "getQsidAndYybridByName:", v8);
    v10 = (__CFString *)objc_retainAutoreleasedReturnValue(v9);
  }
  else
  {
    v10 = &charsToLeaveEscaped;
  }
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100C204A8) ----------------------------------------------------
void __cdecl -[UserModel setUserData:forKey:](UserModel *self, SEL a2, id a3, id a4)
{
  id (__cdecl *v7)(id); // r12
  id *p_userData; // rbx
  NSMutableDictionary *userData; // rdi

  v6 = objc_retain(a3);
  v8 = v7(a4);
  v9 = v8;
  if ( v6 && v8 )
  {
    p_userData = (id *)&self->_userData;
    userData = self->_userData;
    if ( !userData )
    {
      v12 = objc_alloc(&OBJC_CLASS___NSMutableDictionary);
      v13 = _objc_msgSend(v12, "initWithCapacity:", 1LL);
      v14 = *p_userData;
      *p_userData = v13;
      userData = (NSMutableDictionary *)*p_userData;
    }
    v15 = _objc_msgSend(userData, "objectForKey:", CFSTR("level_name"));
    v20 = objc_retainAutoreleasedReturnValue(v15);
    _objc_msgSend(*p_userData, "setObject:forKey:", v6, v16);
    if ( (unsigned __int8)_objc_msgSend(v17, "isEqualToString:", CFSTR("level_name"))
      && !(unsigned __int8)_objc_msgSend(v20, "isEqualToString:", v6) )
    {
      v18 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      _objc_msgSend(v19, "postNotificationName:object:", CFSTR("UserRiskLevelChanged"), self);
    }
  }
}

//----- (0000000100C2063C) ----------------------------------------------------
id __cdecl -[UserModel getUserDataWithKey:](UserModel *self, SEL a2, id a3)
{
  NSMutableDictionary *userData; // rdi

  v3 = objc_retain(a3);
  v4 = 0LL;
  if ( v3 )
  {
    userData = self->_userData;
    if ( userData )
    {
      v6 = _objc_msgSend(userData, "objectForKey:", v3);
      v4 = objc_retainAutoreleasedReturnValue(v6);
    }
  }
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100C206B6) ----------------------------------------------------
id __cdecl -[UserModel getUserRiskStr](UserModel *self, SEL a2)
{
  NSMutableDictionary *userData; // rdi
  unsigned __int64 v11; // r14
  NSString *v13; // rbx
  NSString *v19; // rax
  NSString *v20; // rbx
  NSString *v25; // rax
  unsigned __int64 v26; // rax

  userData = self->_userData;
  if ( !userData )
    return objc_autoreleaseReturnValue(0LL);
  v5 = _objc_msgSend(userData, "objectForKey:", CFSTR("gq_flag"));
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "integerValue");
  if ( v7 == (id)1 )
    return objc_autoreleaseReturnValue(CFSTR("未测评"));
  if ( v7 == (id)3 )
    return objc_autoreleaseReturnValue(CFSTR("已过期"));
  if ( v7 == (id)2 )
  {
    v9 = _objc_msgSend(self->_userData, "objectForKey:", CFSTR("exp_date"));
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = (unsigned __int64)_objc_msgSend(v10, "integerValue");
    if ( v11 > 0x133C910 )
    {
      v18 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
      v27 = _objc_msgSend(v18, "init");
      _objc_msgSend(v27, "setDateFormat:", CFSTR("yyyy-MM-dd HH-mm-ss"));
      v19 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("%ld-%0ld-%0ld 09:00:00"),
              v11 / 0x2710,
              (__int64)(v11 % 0x2710) / 100,
              (__int64)(v11 % 0x2710) % 100);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v27, "dateFromString:", v20);
      v28 = objc_retainAutoreleasedReturnValue(v21);
      v22 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
      v23 = objc_retainAutoreleasedReturnValue(v22);
      _objc_msgSend(v23, "timeIntervalSince1970");
      _objc_msgSend(v28, "timeIntervalSince1970");
      v24 = v2 - v2;
      if ( v24 >= 0.0 )
      {
        v26 = (unsigned int)(int)v24;
        if ( v24 >= 9.223372036854776e18 )
          v26 = (unsigned int)(int)(v24 - 9.223372036854776e18) ^ 0x8000000000000000LL;
        v25 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("还有%ld天到期"), v26 / 0x15180);
      }
      else
      {
        v25 = (NSString *)_objc_msgSend(self->_userData, "objectForKey:", CFSTR("level_name"));
      }
      v13 = objc_retainAutoreleasedReturnValue(v25);
    }
    else
    {
      v12 = _objc_msgSend(self->_userData, "objectForKey:", CFSTR("level_name"));
      v13 = (NSString *)objc_retainAutoreleasedReturnValue(v12);
    }
    return objc_autoreleaseReturnValue(v13);
  }
  else
  {
    v16 = _objc_msgSend(self->_userData, "objectForKey:", CFSTR("level_name"));
    v17 = objc_retainAutoreleasedReturnValue(v16);
    return objc_autoreleaseReturnValue(v17);
  }
}

//----- (0000000100C20A26) ----------------------------------------------------
id __cdecl -[UserModel getUserLogInfo](UserModel *self, SEL a2)
{

  v2 = -[UserModel getUserInfo](self, "getUserInfo");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getUserLogInfo");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C20A88) ----------------------------------------------------
void __cdecl -[UserModel sendTradeLoginSuccessMaiDian](UserModel *self, SEL a2)
{
  __CFString *v5; // r15
  NSString *v7; // rax
  NSString *v8; // rbx
  bool v9; // r15
  __CFString *v11; // rcx
  NSDictionary *v12; // rax
  NSDictionary *v13; // rax
  NSDictionary *v14; // rax
  NSDictionary *v15; // r14
  __CFString *v18; // [rsp+10h] [rbp-60h]
  __CFString *v19; // [rsp+18h] [rbp-58h]
  __CFString *v20; // [rsp+20h] [rbp-50h] BYREF
  NSDictionary *v21; // [rsp+28h] [rbp-48h] BYREF
  __CFString *v22; // [rsp+30h] [rbp-40h] BYREF
  NSString *v23; // [rsp+38h] [rbp-38h] BYREF

  if ( (unsigned __int8)-[UserModel isMeiguUser](self, "isMeiguUser")
    || !(unsigned __int8)-[UserModel isMoniUser](self, "isMoniUser") )
  {
    v2 = -[UserModel getUserInfo](self, "getUserInfo");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = _objc_msgSend(v3, "strQsName");
    v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v4);
    if ( !v5
      || !_objc_msgSend(v5, "length")
      || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NULL"))
      || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("NUL")) )
    {
      v5 = CFSTR("未知券商");
    }
    v19 = v5;
    v6 = -[UserModel getQSID](self, "getQSID");
    v17 = objc_retainAutoreleasedReturnValue(v6);
    v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@_%@"), v17, v5);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( (unsigned __int8)-[UserModel isMeiguUser](self, "isMeiguUser") )
    {
      v18 = CFSTR("登录成功_证券公司_美股交易");
      v9 = 0;
    }
    else
    {
      v10 = (unsigned __int8)-[UserModel isMoniUser](self, "isMoniUser");
      v11 = 0LL;
      v9 = v10 != 0;
      if ( !v10 )
        v11 = CFSTR("登录成功_证券公司_A股交易");
      v18 = v11;
    }
    v22 = CFSTR("证券公司");
    v23 = v8;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v23, &v22, 1LL);
    v20 = CFSTR("keyValuePairKey");
    v21 = objc_retainAutoreleasedReturnValue(v12);
    v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v21, &v20, 1LL);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = v14;
    if ( !v9 )
      +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
        &OBJC_CLASS___UserLogSendingQueueManager,
        "sendUserLog:action:params:needWait:",
        11LL,
        v18,
        v14,
        1LL);
  }
}

//----- (0000000100C20D5E) ----------------------------------------------------
char __cdecl -[UserModel isMoniUser](UserModel *self, SEL a2)
{
  return self->_isMoniUser;
}

//----- (0000000100C20D6F) ----------------------------------------------------
char __cdecl -[UserModel isMeiguUser](UserModel *self, SEL a2)
{
  return self->_isMeiguUser;
}

//----- (0000000100C20D80) ----------------------------------------------------
char __cdecl -[UserModel bActiveUser](UserModel *self, SEL a2)
{
  return self->_bActiveUser;
}

//----- (0000000100C20D91) ----------------------------------------------------
char __cdecl -[UserModel isZyyUser](UserModel *self, SEL a2)
{
  return self->_isZyyUser;
}

//----- (0000000100C20DA2) ----------------------------------------------------
char __cdecl -[UserModel hadAlertDisconnect](UserModel *self, SEL a2)
{
  return self->_hadAlertDisconnect;
}

//----- (0000000100C20DB3) ----------------------------------------------------
void __cdecl -[UserModel setHadAlertDisconnect:](UserModel *self, SEL a2, char a3)
{
  self->_hadAlertDisconnect = a3;
}

//----- (0000000100C20DC3) ----------------------------------------------------
char __cdecl -[UserModel hasXSCAuthority](UserModel *self, SEL a2)
{
  return self->_hasXSCAuthority;
}

//----- (0000000100C20DD4) ----------------------------------------------------
void __cdecl -[UserModel setHasXSCAuthority:](UserModel *self, SEL a2, char a3)
{
  self->_hasXSCAuthority = a3;
}

//----- (0000000100C20DE4) ----------------------------------------------------
unsigned __int64 __cdecl -[UserModel pTWTType](UserModel *self, SEL a2)
{
  return self->_pTWTType;
}

//----- (0000000100C20DF5) ----------------------------------------------------
void __cdecl -[UserModel setPTWTType:](UserModel *self, SEL a2, unsigned __int64 a3)
{
  self->_pTWTType = a3;
}

//----- (0000000100C20E06) ----------------------------------------------------
id __cdecl -[UserModel prometBlock](UserModel *self, SEL a2)
{
  return objc_getProperty(self, a2, 96LL, 0);
}

//----- (0000000100C20E19) ----------------------------------------------------
void __cdecl -[UserModel setPrometBlock:](UserModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (0000000100C20E2A) ----------------------------------------------------
NSMutableDictionary *__cdecl -[UserModel userData](UserModel *self, SEL a2)
{
  return self->_userData;
}

//----- (0000000100C20E3B) ----------------------------------------------------
void __cdecl -[UserModel setUserData:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_userData, a3);
}

//----- (0000000100C20E4F) ----------------------------------------------------
NSString *__cdecl -[UserModel wtclShClose](UserModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 112LL, 0);
}

//----- (0000000100C20E62) ----------------------------------------------------
void __cdecl -[UserModel setWtclShClose:](UserModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 112LL);
}

//----- (0000000100C20E73) ----------------------------------------------------
NSString *__cdecl -[UserModel wtclSZPC](UserModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 120LL, 0);
}

//----- (0000000100C20E86) ----------------------------------------------------
void __cdecl -[UserModel setWtclSZPC:](UserModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 120LL);
}

//----- (0000000100C20E97) ----------------------------------------------------
NSString *__cdecl -[UserModel userkey](UserModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 128LL, 0);
}

//----- (0000000100C20EAA) ----------------------------------------------------
void __cdecl -[UserModel setUserkey:](UserModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 128LL);
}

//----- (0000000100C20EBB) ----------------------------------------------------
commumodule *__cdecl -[UserModel tradeCommuModel](UserModel *self, SEL a2)
{
  return self->_tradeCommuModel;
}

//----- (0000000100C20ECC) ----------------------------------------------------
void __cdecl -[UserModel setTradeCommuModel:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_tradeCommuModel, a3);
}

//----- (0000000100C20EE0) ----------------------------------------------------
UserInfoModel *__cdecl -[UserModel userInfo](UserModel *self, SEL a2)
{
  return self->_userInfo;
}

//----- (0000000100C20EF1) ----------------------------------------------------
void __cdecl -[UserModel setUserInfo:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_userInfo, a3);
}

//----- (0000000100C20F05) ----------------------------------------------------
CacheDataObject *__cdecl -[UserModel aCacheData](UserModel *self, SEL a2)
{
  return self->_aCacheData;
}

//----- (0000000100C20F16) ----------------------------------------------------
void __cdecl -[UserModel setACacheData:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_aCacheData, a3);
}

//----- (0000000100C20F2A) ----------------------------------------------------
CacheDataObject *__cdecl -[UserModel aCacheKCBData](UserModel *self, SEL a2)
{
  return self->_aCacheKCBData;
}

//----- (0000000100C20F3B) ----------------------------------------------------
void __cdecl -[UserModel setACacheKCBData:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_aCacheKCBData, a3);
}

//----- (0000000100C20F4F) ----------------------------------------------------
CacheDataObject *__cdecl -[UserModel aCacheCYBData](UserModel *self, SEL a2)
{
  return self->_aCacheCYBData;
}

//----- (0000000100C20F60) ----------------------------------------------------
void __cdecl -[UserModel setACacheCYBData:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_aCacheCYBData, a3);
}

//----- (0000000100C20F74) ----------------------------------------------------
JYViewStateModel *__cdecl -[UserModel aViewStateModel](UserModel *self, SEL a2)
{
  return self->_aViewStateModel;
}

//----- (0000000100C20F85) ----------------------------------------------------
void __cdecl -[UserModel setAViewStateModel:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_aViewStateModel, a3);
}

//----- (0000000100C20F99) ----------------------------------------------------
NSWtYybNotice *__cdecl -[UserModel yybnoticeWin](UserModel *self, SEL a2)
{
  return self->_yybnoticeWin;
}

//----- (0000000100C20FAA) ----------------------------------------------------
void __cdecl -[UserModel setYybnoticeWin:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_yybnoticeWin, a3);
}

//----- (0000000100C20FBE) ----------------------------------------------------
NSDate *__cdecl -[UserModel loginTime](UserModel *self, SEL a2)
{
  return self->_loginTime;
}

//----- (0000000100C20FCF) ----------------------------------------------------
void __cdecl -[UserModel setLoginTime:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_loginTime, a3);
}

//----- (0000000100C20FE3) ----------------------------------------------------
NSArray *__cdecl -[UserModel extraMenuItems](UserModel *self, SEL a2)
{
  return self->_extraMenuItems;
}

//----- (0000000100C20FF4) ----------------------------------------------------
void __cdecl -[UserModel setExtraMenuItems:](UserModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_extraMenuItems, a3);
}

//----- (0000000100C21008) ----------------------------------------------------
void __cdecl -[UserModel .cxx_destruct](UserModel *self, SEL a2)
{
  objc_storeStrong((id *)&self->_extraMenuItems, 0LL);
  objc_storeStrong((id *)&self->_loginTime, 0LL);
  objc_storeStrong((id *)&self->_yybnoticeWin, 0LL);
  objc_storeStrong((id *)&self->_aViewStateModel, 0LL);
  objc_storeStrong((id *)&self->_aCacheCYBData, 0LL);
  objc_storeStrong((id *)&self->_aCacheKCBData, 0LL);
  objc_storeStrong((id *)&self->_aCacheData, 0LL);
  objc_storeStrong((id *)&self->_userInfo, 0LL);
  objc_storeStrong((id *)&self->_tradeCommuModel, 0LL);
  objc_storeStrong((id *)&self->_userkey, 0LL);
  objc_storeStrong((id *)&self->_wtclSZPC, 0LL);
  objc_storeStrong((id *)&self->_wtclShClose, 0LL);
  objc_storeStrong((id *)&self->_userData, 0LL);
  objc_storeStrong(&self->_prometBlock, 0LL);
  objc_storeStrong((id *)&self->_sessionId, 0LL);
  objc_storeStrong((id *)&self->RefreshChiCang, 0LL);
  objc_storeStrong((id *)&self->_httpRequest, 0LL);
  objc_storeStrong((id *)&self->chicangTimer, 0LL);
  objc_storeStrong((id *)&self->reconnectTimer, 0LL);
  objc_storeStrong((id *)&self->sessionTimer, 0LL);
}

//----- (0000000100C2116C) ----------------------------------------------------
__int64 InitFunc_0()
{
  NSNumber *v1; // rax
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rbx
  NSNumber *v10; // rax
  NSNumber *v11; // rax
  NSNumber *v12; // r14
  NSDictionary *v13; // rax
  NSNumber *v16; // [rsp+8h] [rbp-108h]
  NSNumber *v17; // [rsp+10h] [rbp-100h]
  NSNumber *v18; // [rsp+18h] [rbp-F8h]
  NSNumber *v19; // [rsp+20h] [rbp-F0h]
  NSNumber *v20; // [rsp+28h] [rbp-E8h]
  NSNumber *v21; // [rsp+30h] [rbp-E0h]
  NSNumber *v22; // [rsp+38h] [rbp-D8h]

  v0 = objc_autoreleasePoolPush();
  std::ios_base::Init::Init((std::ios_base::Init *)&unk_1016D3998);
  __cxa_atexit((void (__cdecl *)(void *))&std::ios_base::Init::~Init, &unk_1016D3998, (void *)&_mh_execute_header);
  v23[0] = (__int64)CFSTR("1");
  v1 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 33LL);
  v16 = objc_retainAutoreleasedReturnValue(v1);
  v24[0] = (__int64)v16;
  v23[1] = (__int64)CFSTR("2");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 17LL);
  v17 = objc_retain(v2);
  v24[1] = (__int64)v17;
  v23[2] = (__int64)CFSTR("4");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34LL);
  v18 = objc_retain(v3);
  v24[2] = (__int64)v18;
  v23[3] = (__int64)CFSTR("5");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 18LL);
  v19 = objc_retain(v4);
  v24[3] = (__int64)v19;
  v23[4] = (__int64)CFSTR("6");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 39LL);
  v20 = objc_retain(v5);
  v24[4] = (__int64)v20;
  v23[5] = (__int64)CFSTR("7");
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 40LL);
  v21 = objc_retain(v6);
  v24[5] = (__int64)v21;
  v23[6] = (__int64)CFSTR("8");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 73LL);
  v22 = objc_retain(v7);
  v24[6] = (__int64)v22;
  v23[7] = (__int64)CFSTR("9");
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 153LL);
  v9 = objc_retain(v8);
  v24[7] = (__int64)v9;
  v23[8] = (__int64)CFSTR("!");
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 33LL);
  v24[8] = (__int64)objc_retain(v10);
  v23[9] = (__int64)CFSTR("\"");
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 17LL);
  v12 = objc_retain(v11);
  v24[9] = (__int64)v12;
  v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v24, v23, 10LL);
  qword_1016D39A0 = objc_retainAutoreleasedReturnValue(v13);
  objc_autoreleasePoolPop(v0);
  return __stack_chk_guard;
}

