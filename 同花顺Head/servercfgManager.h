//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

#import "JYHTTPRequestManageDelegate-Protocol.h"

@class AddQsModel, JYHTTPRequestManage, NSMutableArray, NSString;

@interface servercfgManager : NSObject <JYHTTPRequestManageDelegate>
{
    JYHTTPRequestManage *_httpRequest;
    JYHTTPRequestManage *_aAddQsHttpRequest;
    AddQsModel *_addQsmodel;
    NSMutableArray *_arrBrokerList;
    CDUnknownBlockType _aSucceedBlock;
    CDUnknownBlockType _aFailureBlock;
}

+ (id)shareInstance;

@property(copy, nonatomic) CDUnknownBlockType aFailureBlock; // @synthesize aFailureBlock=_aFailureBlock;
@property(copy, nonatomic) CDUnknownBlockType aSucceedBlock; // @synthesize aSucceedBlock=_aSucceedBlock;
@property(retain, nonatomic) NSMutableArray *arrBrokerList; // @synthesize arrBrokerList=_arrBrokerList;
- (BOOL)_needRequestUserBrokers;
- (void)requestFailure:(id)arg1 Msg:(id)arg2 andSendDic:(id)arg3;
- (void)xmlOrJsonRequestSuccess:(id)arg1 sourceDic:(id)arg2 andSendDic:(id)arg3;
- (void)dAXFjjQwJEkyDPiX:(id)arg1 sendDic:(id)arg2;
- (void)SendRequestDeleteBroker:(id)arg1 yybrid:(id)arg2 sendDic:(id)arg3;
- (void)cdCLhUJgIFaNrFPY:(id)arg1 sendDic:(id)arg2;
- (BOOL)isNeedAddMoniBrokerWithBrokerArray:(id)arg1;
- (BOOL)isBrokerAddWithQsid:(id)arg1 yybrid:(id)arg2;
- (void)deleteBrokerDeleteWith:(id)arg1;
- (long long)getIndexByConnectMode:(long long)arg1;
- (void)getMoniServerInfoWithSucceedBlock:(CDUnknownBlockType)arg1;
- (id)getNewConnectMode;
- (void)setAddServerStationListName:(id)arg1 andValue:(id)arg2 andBrokerName:(id)arg3;
- (void)setAddBrokerName:(id)arg1 andValue:(id)arg2;
- (void)RemoveBrokerName:(id)arg1;
- (id)getMacLoginVersionByName:(id)arg1;
- (id)getMacKernelByName:(id)arg1;
- (id)getServerDataModelByName:(id)arg1;
- (id)getMacVersionByName:(id)arg1;
- (id)getAccountTypeByName:(id)arg1;
- (id)getAuthTypeByName:(id)arg1;
- (id)getQsidAndYybridByName:(id)arg1;
- (id)getYybridByName:(id)arg1;
- (id)getQsidByName:(id)arg1;
- (id)getYybidByName:(id)arg1;
- (id)getBrokerIdByName:(id)arg1;
- (id)dumpServersForBroker:(id)arg1;
- (id)getServerListByBroker:(id)arg1;
- (BOOL)needShieldTrade;
- (id)getBrokerNameListArray;
- (id)GetMeguiBrokerNameListArray;
- (BOOL)IsMeiguBroke:(id)arg1;
- (void)requestBrokerWithUserid:(id)arg1 succeedBlock:(CDUnknownBlockType)arg2 failureBlock:(CDUnknownBlockType)arg3;
- (void)reInitServerInfo;
- (id)init;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

