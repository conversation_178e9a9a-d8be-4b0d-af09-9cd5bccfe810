TradeStallsFiveHundredDocumentView *__cdecl -[TradeStallsFiveHundredDocumentView initWithCoder:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  TradeStallsFiveHundredDocumentView *v3; // rax
  TradeStallsFiveHundredDocumentView *v4; // rbx
  id unused_was_gState; // rdi
  id (*v8)(id, SEL, ...); // r12
  id frameMatrix; // rdi
  id (*v12)(id, SEL, ...); // r12
  CALayer *v14; // rax
  CALayer *layer; // rdi
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id dragTypes; // rdi
  id (*v23)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  _NSViewAuxiliary *v27; // rax
  _NSViewAuxiliary *viewAuxiliary; // rdi
  id (*v30)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12

  v38.receiver = self;
  v38.super_class = (Class)&OBJC_CLASS___TradeStallsFiveHundredDocumentView;
  v3 = objc_msgSendSuper2(&v38, "initWithCoder:", a3);
  v4 = v3;
  if ( v3 )
  {
    v3->super.super._nextResponder = (id)0x4034000000000000LL;
    v3->super._frame.origin.x = 18.0;
    v3->super._frame.size.width = 0.0;
    v3->super._frame.size.height = 36.0;
    v3->super._bounds.origin.x = 36.0;
    v3->super._bounds.origin.y = 64.0;
    v3->super._bounds.size.width = 100.0;
    v3->super._bounds.size.height = 64.0;
    v3->super._superview = (NSView *)0x4064800000000000LL;
    v3->super._subviews = (NSArray *)0x4042000000000000LL;
    v3->super._frame.origin.y = 200.0;
    v3->super._window = (NSWindow *)0x4000000000000000LL;
    v5 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 13.0);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    unused_was_gState = v4->super._unused_was_gState;
    v4->super._unused_was_gState = v6;
    v9 = v8(&OBJC_CLASS___NSFont, "systemFontOfSize:", 11.0);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    frameMatrix = v4->super._frameMatrix;
    v4->super._frameMatrix = v10;
    v13 = v12(&OBJC_CLASS___NSFont, "systemFontOfSize:", 9.0);
    v14 = (CALayer *)objc_retainAutoreleasedReturnValue(v13);
    layer = v4->super._layer;
    v4->super._layer = v14;
    v17 = v16(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v20 = v19(v18, "mutableCopy");
    dragTypes = v4->super._dragTypes;
    v4->super._dragTypes = v20;
    v22(v4->super._dragTypes, "setAlignment:", 0LL);
    v24 = v23(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v27 = (_NSViewAuxiliary *)v26(v25, "mutableCopy");
    viewAuxiliary = v4->super._viewAuxiliary;
    v4->super._viewAuxiliary = v27;
    v29(v4->super._viewAuxiliary, "setAlignment:", 1LL);
    v31 = v30(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v34 = v33(v32, "mutableCopy");
    v35 = *(_QWORD *)&v4->super._vFlags;
    *(_QWORD *)&v4->super._vFlags = v34;
    v36(*(id *)&v4->super._vFlags, "setAlignment:", 2LL);
  }
  return v4;
}

//----- (0000000100BABD48) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView awakeFromNib](TradeStallsFiveHundredDocumentView *self, SEL a2)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12

  v16.receiver = self;
  v16.super_class = (Class)&OBJC_CLASS___TradeStallsFiveHundredDocumentView;
  objc_msgSendSuper2(&v16, "awakeFromNib");
  v2 = objc_alloc(&OBJC_CLASS___NSClickGestureRecognizer);
  v3 = _objc_msgSend(v2, "initWithTarget:action:", self, "click:");
  v4(self, "addGestureRecognizer:", v3);
  v5 = objc_alloc(&OBJC_CLASS___NSClickGestureRecognizer);
  v7 = v6(v5, "initWithTarget:action:", self, "doubleClick:");
  v8(v7, "setNumberOfClicksRequired:", 2LL);
  v9(self, "addGestureRecognizer:", v7);
  v11 = v10(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13(v12, "addObserver:selector:name:object:", self, "franmeDidChange:", NSViewFrameDidChangeNotification, self);
  v14(v7);
  v15(v3);
}

//----- (0000000100BABE65) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView dealloc](TradeStallsFiveHundredDocumentView *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___TradeStallsFiveHundredDocumentView;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (0000000100BABEDA) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView reload](TradeStallsFiveHundredDocumentView *self, SEL a2)
{
  -[TradeStallsFiveHundredDocumentView franmeDidChange:](self, "franmeDidChange:", 0LL);
  _objc_msgSend(self, "setNeedsDisplay:", 1LL);
}

//----- (0000000100BABF0F) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView windowWillClose](TradeStallsFiveHundredDocumentView *self, SEL a2)
{

  mingXiW = self->mingXiW;
  if ( mingXiW != 0.0 )
  {
    _objc_msgSend(*(id *)&mingXiW, "close");
    v4 = self->mingXiW;
    self->mingXiW = 0.0;
  }
}

//----- (0000000100BABF51) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView click:](TradeStallsFiveHundredDocumentView *self, SEL a2, id a3)
{

  _objc_msgSend(a3, "locationInView:", self);
  self->cloumnW = v3;
  self->nameX = v4;
  *(_OWORD *)&self->nameW = xmmword_1010CE1F0;
  _objc_msgSend(self, "setNeedsDisplay:", 1LL);
}

//----- (0000000100BABFB9) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView doubleClick:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // r13
  id WeakRetained; // rbx
  TradeStallsFiveHundredDocumentView *v10; // r13
  NSArray *v11; // rax
  NSArray *v12; // rbx
  NSDictionary *v28; // rax
  NSDictionary *v29; // r14
  NSString *v34; // rax
  _BOOL8 v35; // rbx
  NSNumber *v39; // rax
  NSNumber *v40; // rbx
  NSDictionary *v41; // rax
  NSDictionary *v42; // r15
  NSArray *v47; // [rsp+20h] [rbp-80h]
  id *location; // [rsp+28h] [rbp-78h]
  id *locationb; // [rsp+28h] [rbp-78h]
  _QWORD v53[2]; // [rsp+38h] [rbp-68h] BYREF
  _QWORD v54[5]; // [rsp+48h] [rbp-58h] BYREF

  v54[0] = CFSTR("USHA");
  v54[1] = CFSTR("USZA");
  v54[2] = CFSTR("USHT");
  v54[3] = CFSTR("USHP");
  v54[4] = CFSTR("USZP");
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v54, 5LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  p_volX = &self->volX;
  WeakRetained = objc_loadWeakRetained((id *)&self->volX);
  v7 = _objc_msgSend(WeakRetained, "market");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v47 = v4;
  if ( (unsigned __int8)_objc_msgSend(v4, "containsObject:", v8) )
  {
    v10 = self;
  }
  else
  {
    v53[0] = CFSTR("USHJ");
    v53[1] = CFSTR("USZJ");
    v11 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v53, 2LL);
    v45 = WeakRetained;
    v12 = objc_retainAutoreleasedReturnValue(v11);
    locationa = &self->volX;
    v13 = objc_loadWeakRetained((id *)p_volX);
    v14 = _objc_msgSend(v13, "market");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v52 = (unsigned __int8)_objc_msgSend(v12, "containsObject:", v15);
    v16 = v15;
    v10 = self;
    v17 = v13;
    p_volX = locationa;
    if ( !v52 )
      return;
  }
  priceW = v10->priceW;
  if ( priceW != 0.0 && *(_QWORD *)&v10->nameW != 0x7FFFFFFFFFFFFFFFLL )
  {
    v10->priceW = 0.0;
    v20 = _objc_msgSend(*(id *)&priceW, "price");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    if ( v21 )
    {
      location = (id *)p_volX;
      v23 = v21;
      v24 = _objc_msgSend(v22, "price");
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v26 = (unsigned __int8)_objc_msgSend(v25, "isEqualToString:", obj);
      if ( v26 )
      {
LABEL_10:
        return;
      }
      v28 = -[TradeStallsFiveHundredDocumentView marketPageRelationDic](self, "marketPageRelationDic");
      v29 = objc_retainAutoreleasedReturnValue(v28);
      v30 = objc_loadWeakRetained(location);
      v31 = _objc_msgSend(v30, "market");
      v32 = objc_retainAutoreleasedReturnValue(v31);
      v33 = _objc_msgSend(v29, "objectForKeyedSubscript:", v32);
      v48 = objc_retainAutoreleasedReturnValue(v33);
      v34 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@QuickTradeNotification"), v48);
      locationb = objc_retainAutoreleasedReturnValue(v34);
      v35 = *(_QWORD *)&self->nameW != 0LL;
      v37 = _objc_msgSend(v36, "price");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", v35);
      v40 = objc_retainAutoreleasedReturnValue(v39);
      v41 = _objc_msgSend(
              &OBJC_CLASS___NSDictionary,
              "dictionaryWithObjectsAndKeys:",
              v38,
              CFSTR("tradePrice"),
              v40,
              CFSTR("isMairu"),
              0LL);
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v43 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v44 = objc_retainAutoreleasedReturnValue(v43);
      _objc_msgSend(v44, "postNotificationName:object:", locationb, v42);
      v21 = v48;
    }
    goto LABEL_10;
  }
}

//----- (0000000100BAC3F5) ----------------------------------------------------
char __cdecl -[TradeStallsFiveHundredDocumentView isFlipped](TradeStallsFiveHundredDocumentView *self, SEL a2)
{
  return 1;
}

//----- (0000000100BAC400) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView franmeDidChange:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  TradeStallsFiveHundredDocumentView *v6; // rbx
  id WeakRetained; // r15
  __m128d v14; // xmm0
  __m128d v15; // xmm0
  __m128d v16; // xmm0
  CGFloat v20; // [rsp+70h] [rbp-A0h]
  CGRect rect1; // [rsp+88h] [rbp-88h] BYREF
  CGRect rect2; // [rsp+A8h] [rbp-68h]
  __m128d v24; // [rsp+D0h] [rbp-40h]

  v3 = objc_retain(a3);
  v4 = *(double *)&v3;
  if ( !v3
    || (v5 = _objc_msgSend(v3, "object"),
        v6 = (TradeStallsFiveHundredDocumentView *)objc_retainAutoreleasedReturnValue(v5),
        v6 == self) )
  {
    v24.f64[0] = v4;
    WeakRetained = objc_loadWeakRetained((id *)&self->volX);
    v8 = _objc_msgSend(WeakRetained, "sellOrderDict");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( _objc_msgSend(v9, "count") )
    {
      v4 = v24.f64[0];
    }
    else
    {
      v25 = objc_loadWeakRetained((id *)&self->volX);
      v11 = _objc_msgSend(v25, "buyOrderDict");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v23 = _objc_msgSend(v12, "count");
      v4 = v24.f64[0];
      if ( !v23 )
        goto LABEL_9;
    }
    -[TradeStallsFiveHundredDocumentView caculateRowAndColumn](self, "caculateRowAndColumn");
    v14 = _mm_sub_pd(
            (__m128d)_mm_unpacklo_epi32(
                       (__m128i)(unsigned __int64)(2LL * *(_QWORD *)&self->headerH),
                       (__m128i)xmmword_1010CE400),
            (__m128d)xmmword_1010CE410);
    v15 = _mm_hadd_pd(v14, v14);
    v15.f64[0] = v15.f64[0] * self->super._frame.origin.x + *(double *)&self->super.super._nextResponder;
    v24 = v15;
    objc_msgSend_stret(&v18, (SEL)self, "frame");
    v25 = v18;
    objc_msgSend_stret(v19, (SEL)self, "frame");
    v16 = _mm_sub_pd(
            (__m128d)_mm_unpacklo_ps((__m128)*(unsigned __int64 *)&self->rowH, (__m128)xmmword_1010CE400),
            (__m128d)xmmword_1010CE410);
    v16.f64[0] = _mm_hadd_pd(v16, v16).f64[0] * self->super._frame.origin.y;
    *(_QWORD *)&rect2.origin.x = v25;
    rect2.origin.y = v20;
    rect2.size.width = v16.f64[0];
    rect2.size.height = v24.f64[0];
    objc_msgSend_stret(&rect1, (SEL)self, "frame");
    if ( !CGRectEqualToRect(rect1, rect2) )
    {
      _objc_msgSend(self, "setFrame:");
      volW = self->volW;
      if ( volW != 0.0 )
        (*(void (**)(void))(*(_QWORD *)&volW + 16LL))();
    }
  }
LABEL_9:
}

//----- (0000000100BAC690) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView caculateRowAndColumn](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2)
{
  id WeakRetained; // r13
  __m128i si128; // xmm1
  __m128d v19; // xmm0
  TradeStallsFiveHundredDocumentView *v20; // rbx
  __m128d v21; // xmm3
  unsigned __int64 v23; // rcx
  long double v25; // [rsp+0h] [rbp-50h] BYREF
  TradeStallsFiveHundredDocumentView *v27; // [rsp+20h] [rbp-30h]

  v2 = _objc_msgSend(self, "enclosingScrollView");
  v3 = (const char *)objc_retainAutoreleasedReturnValue(v2);
  v4 = (char *)v3;
  if ( v3 )
  {
    objc_msgSend_stret(&v25, v3, "frame");
    v5 = *((double *)&v26 + 1);
  }
  else
  {
    v26 = 0LL;
    *(_OWORD *)&v25 = 0LL;
    v5 = 0.0;
  }
  v6 = (v5 - *(double *)&self->super.super._nextResponder) / (self->super._frame.origin.x + self->super._frame.origin.x);
  *(_QWORD *)&v7 = (unsigned int)(int)(v6 - 9.223372036854776e18) ^ 0x8000000000000000LL;
  if ( v6 < 9.223372036854776e18 )
    *(_QWORD *)&v7 = (unsigned int)(int)v6;
  self->headerH = v7;
  if ( *(_QWORD *)&self->headerH <= 7uLL )
    *(_QWORD *)&self->headerH = 8LL;
  WeakRetained = objc_loadWeakRetained((id *)&self->volX);
  v9 = _objc_msgSend(WeakRetained, "sellOrderDict");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "count");
  v27 = self;
  v13 = objc_loadWeakRetained((id *)&self->volX);
  v14 = _objc_msgSend(v13, "buyOrderDict");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(v15, "count");
  if ( v11 < v16 )
    v11 = v16;
  si128 = _mm_load_si128((const __m128i *)&xmmword_1010CE400);
  v19 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)(unsigned __int64)v11, si128), (__m128d)xmmword_1010CE410);
  v20 = v27;
  v21 = _mm_sub_pd(
          (__m128d)_mm_unpacklo_epi32(_mm_loadl_epi64((const __m128i *)&v27->headerH), si128),
          (__m128d)xmmword_1010CE410);
  v22 = _mm_hadd_pd(v19, v19).f64[0] / _mm_hadd_pd(v21, v21).f64[0];
  ceil(v25);
  v23 = (unsigned int)(int)(v22 - 9.223372036854776e18) ^ 0x8000000000000000LL;
  if ( v22 < 9.223372036854776e18 )
    v23 = (unsigned int)(int)v22;
  v24 = 1LL;
  if ( v23 )
    v24 = v23;
  *(_QWORD *)&v20->rowH = v24;
}

//----- (0000000100BAC89D) ----------------------------------------------------
_NSRange __cdecl -[TradeStallsFiveHundredDocumentView columnsInRect:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        CGRect a3)
{
  _NSRange result; // rax
  NSUInteger v5; // rsi

  y = self->super._frame.origin.y;
  result.location = (unsigned int)(int)(a3.origin.x / y);
  result.length = (unsigned int)(int)(a3.size.width / y - 9.223372036854776e18) ^ 0x8000000000000000LL;
  if ( a3.size.width / y < 9.223372036854776e18 )
    result.length = (unsigned int)(int)(a3.size.width / y);
  v5 = *(_QWORD *)&self->rowH - result.length++ - result.location;
  if ( v5 < 2 )
    result.length = *(_QWORD *)&self->rowH - result.location;
  return result;
}

//----- (0000000100BAC918) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView drawRect:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        CGRect a3)
{
  id WeakRetained; // r14
  SEL v10; // r12
  CGContext *v17; // r14
  SEL v23; // r12
  CGFloat v25; // xmm1_8
  __m128d v27; // xmm0
  SEL v29; // r12
  SEL v30; // r12
  __m128d v32; // xmm1
  __m128d v33; // xmm1
  SEL v34; // r12
  id *v39; // r12
  id *v45; // rax
  id *v47; // rcx
  CGFloat v48; // rbx
  unsigned __int64 v51; // rcx
  SEL v53; // r14
  bool v62; // cf
  unsigned __int64 v67; // r14
  unsigned __int64 v70; // rbx
  unsigned __int64 v72; // rbx
  unsigned __int64 v82; // r12
  unsigned __int64 v88; // r12
  CGRect rect; // [rsp+0h] [rbp-1D0h]
  CGFloat v96; // [rsp+30h] [rbp-1A0h]
  CGFloat v102; // [rsp+90h] [rbp-140h]
  CGFloat v104; // [rsp+B0h] [rbp-120h]
  CGRect v105; // [rsp+C0h] [rbp-110h]
  CGRect v106; // [rsp+E0h] [rbp-F0h]
  unsigned __int64 v108; // [rsp+110h] [rbp-C0h]
  id *location; // [rsp+158h] [rbp-78h]
  unsigned __int64 v121; // [rsp+178h] [rbp-58h]
  __m128d y; // [rsp+180h] [rbp-50h]
  SEL v123; // [rsp+198h] [rbp-38h]

  v3 = _objc_msgSend(self, "window");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)_objc_msgSend(v4, "isVisible");
  if ( v5 )
  {
    v123 = (SEL)self;
    WeakRetained = objc_loadWeakRetained((id *)&self->volX);
    v7 = _objc_msgSend(WeakRetained, "sellOrderDict");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    location = (id *)&self->volX;
    if ( _objc_msgSend(v8, "count") )
    {
      v10 = v123;
    }
    else
    {
      *(double *)&v124 = COERCE_DOUBLE(objc_loadWeakRetained((id *)&self->volX));
      v11 = _objc_msgSend(v124, "buyOrderDict");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      *(_QWORD *)&y.f64[0] = _objc_msgSend(v12, "count");
      v10 = v123;
      if ( !*(_QWORD *)&y.f64[0] )
        return;
    }
    v121 = *((_QWORD *)v10 + 19);
    v14 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "currentContext");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = objc_retainAutorelease(v15);
    v17 = (CGContext *)_objc_msgSend(v16, "graphicsPort");
    CGContextSetLineWidth(v17, *(CGFloat *)(v18 + 96));
    v19 = +[HXThemeManager tableHeaderBgColor](&OBJC_CLASS___HXThemeManager, "tableHeaderBgColor");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v20, "setFill");
    v21 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    _objc_msgSend(v22, "setStroke");
    objc_msgSend_stret(v95, v23, "frame");
    v25 = *(double *)(v24 + 8);
    v106.origin = 0LL;
    v106.size.width = v96;
    v106.size.height = v25;
    rect.size = v106.size;
    rect.origin = 0LL;
    CGContextFillRect(v17, rect);
    v27 = (__m128d)*(unsigned __int64 *)(v26 + 96);
    v27.f64[0] = v27.f64[0] * 0.5;
    y = v27;
    objc_msgSend_stret(v97, (SEL)v26, "frame");
    v27.f64[0] = v98;
    v27.f64[1] = *(double *)(v28 + 8);
    v105.origin.x = y.f64[0];
    v105.origin.y = y.f64[0];
    v105.size = (CGSize)_mm_sub_pd(v27, _mm_movedup_pd(y));
    CGContextStrokeRect(v17, v105);
    objc_msgSend_stret(v99, v29, "frame");
    *(double *)&v124 = v100 - y.f64[0];
    CGContextMoveToPoint(v17, 0.0, v100 - y.f64[0]);
    objc_msgSend_stret(v101, v30, "frame");
    CGContextAddLineToPoint(v17, v102, *(CGFloat *)&v124);
    CGContextStrokePath(v17);
    v32 = _mm_sub_pd(
            (__m128d)_mm_unpacklo_ps((__m128)*(unsigned __int64 *)(v31 + 152), (__m128)xmmword_1010CE400),
            (__m128d)xmmword_1010CE410);
    v33 = _mm_hadd_pd(v32, v32);
    v33.f64[0] = v33.f64[0] * *(double *)(v31 + 16) + *(double *)(v31 + 8) + y.f64[0];
    y = v33;
    CGContextMoveToPoint(v17, 0.0, v33.f64[0]);
    objc_msgSend_stret(v103, v34, "frame");
    CGContextAddLineToPoint(v17, v104, y.f64[0]);
    CGContextStrokePath(v17);
    v35 = objc_loadWeakRetained(location);
    v36 = _objc_msgSend(v35, "sellOrderDict");
    v37 = objc_retainAutoreleasedReturnValue(v36);
    v38 = _objc_msgSend(v37, "allValues");
    *(double *)&v124 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v38));
    v40 = objc_loadWeakRetained(v39);
    v41 = _objc_msgSend(v40, "buyOrderDict");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    v43 = _objc_msgSend(v42, "allValues");
    v113 = objc_retainAutoreleasedReturnValue(v43);
    v44 = +[TradeStallsModel model](&OBJC_CLASS___TradeStallsModel, "model");
    v120 = objc_retainAutoreleasedReturnValue(v44);
    v45 = (id *)_objc_msgSend((id)v123, "columnsInRect:");
    v108 = (unsigned __int64)v45 + v46;
    if ( !__CFADD__(v45, v46) )
    {
      v47 = v45;
      v121 *= 2LL;
      v119 = "subarrayWithRange:";
      v116 = "mutableCopy";
      v114 = "array";
      v111 = "insertObject:atIndex:";
      v118 = "copy";
      v115 = "addObject:";
      v109 = "arrayByAddingObjectsFromArray:";
      v110 = "drawCloumn:array:";
      do
      {
        v48 = *(double *)&v47;
        v49 = _objc_msgSend(v124, "count");
        v51 = *(_QWORD *)(v50 + 152);
        y.f64[0] = v48;
        location = (id *)(*(_QWORD *)&v48 + 1LL);
        v52 = (__int64)v49 - (*(_QWORD *)&v48 + 1LL) * v51;
        v53 = (SEL)v50;
        if ( v52 < 0 )
        {
          v55 = v51 + v52;
          if ( v55 <= 0 || v55 > v51 )
          {
            v61 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, v114);
            v58 = objc_retainAutoreleasedReturnValue(v61);
          }
          else
          {
            v56 = _objc_msgSend(v124, v119, 0LL, v55);
            v57 = objc_retainAutoreleasedReturnValue(v56);
            v58 = _objc_msgSend(v57, v116);
            v59 = v57;
            v53 = v123;
          }
          v62 = (unsigned __int64)_objc_msgSend(v58, v60) < *((_QWORD *)v53 + 19);
          v63 = v120;
          v64 = v111;
          if ( v62 )
          {
            do
            {
              _objc_msgSend(v58, v64, v63, 0LL);
              v66 = _objc_msgSend(v58, v65);
            }
            while ( (unsigned __int64)v66 < *((_QWORD *)v123 + 19) );
          }
          v112 = _objc_msgSend(v58, v118);
          v53 = v123;
        }
        else
        {
          v54 = _objc_msgSend(v124, v119, v52);
          v112 = objc_retainAutoreleasedReturnValue(v54);
        }
        v67 = *(_QWORD *)&y.f64[0] * *((_QWORD *)v53 + 19);
        v68 = v113;
        if ( v67 < (__int64)_objc_msgSend(v113, "count")
          && (v70 = v67 + *(_QWORD *)(v69 + 152), v70 <= (__int64)_objc_msgSend(v68, "count")) )
        {
          v90 = _objc_msgSend(v68, v119, v67, *(_QWORD *)(v71 + 152));
          v89 = objc_retainAutoreleasedReturnValue(v90);
        }
        else
        {
          if ( v67 >= (__int64)_objc_msgSend(v68, "count")
            || (v72 = v67 + *((_QWORD *)v123 + 19), v72 <= (__int64)_objc_msgSend(v68, "count")) )
          {
            v83 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, v114);
            v76 = objc_retainAutoreleasedReturnValue(v83);
            v84 = _objc_msgSend(v76, "count");
            v85 = v120;
            v86 = v115;
            if ( (unsigned __int64)v84 < v121 )
            {
              do
              {
                _objc_msgSend(v76, v86, v85);
                v87 = _objc_msgSend(v76, "count");
              }
              while ( (unsigned __int64)v87 < v88 );
            }
          }
          else
          {
            v73 = ((__int64 (__fastcall *)(id))_objc_msgSend)(v68);
            v74 = _objc_msgSend(v68, v119, v67, v73 - v67);
            v75 = objc_retainAutoreleasedReturnValue(v74);
            v76 = _objc_msgSend(v75, v116);
            v78 = _objc_msgSend(v76, v77);
            v79 = v120;
            v80 = v115;
            if ( (unsigned __int64)v78 < v121 )
            {
              do
              {
                _objc_msgSend(v76, v80, v79);
                v81 = _objc_msgSend(v76, "count");
              }
              while ( (unsigned __int64)v81 < v82 );
            }
          }
          _objc_msgSend(v76, v118);
        }
        v107 = v89;
        v91 = v112;
        v92 = _objc_msgSend(v112, v109, v89);
        v93 = objc_retainAutoreleasedReturnValue(v92);
        _objc_msgSend((id)v123, v110, *(_QWORD *)&y.f64[0], v93);
        v47 = location;
      }
      while ( (unsigned __int64)location <= v108 );
    }
  }
}

//----- (0000000100BAD1A8) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView drawCloumn:array:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        unsigned __int64 a3,
        id a4)
{
  __m128d v4; // xmm0
  __m128d v5; // xmm0
  CGContext *v10; // r14
  SEL v15; // r12
  NSDictionary *v22; // rax
  NSDictionary *v23; // rbx
  NSDictionary *v30; // rax
  NSDictionary *v31; // rbx
  id WeakRetained; // rbx
  __CFString *v34; // rdi
  NSDictionary *v41; // rax
  NSDictionary *v42; // rbx
  id (**v49)(id, SEL, ...); // r14
  NSDictionary *v50; // rax
  NSDictionary *v51; // rbx
  __m128d v53; // xmm0
  unsigned __int64 v55; // rbx
  CGFloat v58; // xmm0_8
  CGFloat v59; // xmm1_8
  bool v60; // al
  CGContext *v65; // r15
  unsigned __int64 v70; // r15
  unsigned __int64 v71; // rbx
  CGFloat v73; // xmm1_8
  CGFloat v74; // xmm2_8
  CGFloat v75; // rbx
  CGContext *v81; // r15
  CGFloat v84; // xmm0_8
  CGFloat v85; // xmm1_8
  id (**v92)(id, SEL, ...); // r13
  CGFloat v96; // rbx
  CGFloat v106; // rbx
  __m128d v116; // xmm1
  __m128d v124; // xmm0
  CGFloat v127; // [rsp+38h] [rbp-408h]
  __m128d v128; // [rsp+40h] [rbp-400h]
  CGRect v147; // [rsp+E0h] [rbp-360h]
  CGRect rect; // [rsp+160h] [rbp-2E0h]
  CGRect v161; // [rsp+180h] [rbp-2C0h]
  id *location; // [rsp+1D0h] [rbp-270h]
  unsigned __int64 v170; // [rsp+1E0h] [rbp-260h]
  __m128d v178; // [rsp+220h] [rbp-220h]
  CGPoint *v181; // [rsp+240h] [rbp-200h]
  __m128d v183; // [rsp+250h] [rbp-1F0h]
  __m128d v184; // [rsp+260h] [rbp-1E0h]
  CGFloat x; // [rsp+280h] [rbp-1C0h]
  _QWORD v196[3]; // [rsp+350h] [rbp-F0h] BYREF
  _QWORD v197[3]; // [rsp+368h] [rbp-D8h] BYREF
  _QWORD v198[3]; // [rsp+380h] [rbp-C0h] BYREF
  _QWORD v199[3]; // [rsp+398h] [rbp-A8h] BYREF
  _QWORD v200[3]; // [rsp+3B0h] [rbp-90h] BYREF
  _QWORD v201[3]; // [rsp+3C8h] [rbp-78h] BYREF
  _QWORD v202[3]; // [rsp+3E0h] [rbp-60h] BYREF
  _QWORD v203[3]; // [rsp+3F8h] [rbp-48h] BYREF

  v4 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)a3, (__m128i)xmmword_1010CE400), (__m128d)xmmword_1010CE410);
  v5 = _mm_hadd_pd(v4, v4);
  v172 = objc_retain(a4);
  v5.f64[0] = v5.f64[0] * *(double *)(v6 + 24);
  v183 = v5;
  v184 = (__m128d)*(unsigned __int64 *)(v6 + 8);
  v7 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "currentContext");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = objc_retainAutorelease(v8);
  v10 = (CGContext *)_objc_msgSend(v9, "graphicsPort");
  CGContextSetLineWidth(v10, *(CGFloat *)(v11 + 96));
  v12 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "setStroke");
  x = *(double *)(v14 + 96) * -0.5 + *(double *)(v14 + 24) + v5.f64[0];
  CGContextMoveToPoint(v10, x, 0.0);
  objc_msgSend_stret(v126, v15, "frame");
  CGContextAddLineToPoint(v10, x, v127);
  CGContextStrokePath(v10);
  v17 = *(_QWORD *)(v16 + 40);
  v18 = *(_QWORD *)(v16 + 8);
  v131 = *(double *)(v16 + 32) + v183.f64[0] + 2.0;
  v132 = 0x4008000000000000LL;
  v133 = v17;
  v134 = v18;
  v202[0] = NSFontAttributeName;
  v203[0] = *(_QWORD *)(v16 + 112);
  v202[1] = NSForegroundColorAttributeName;
  v19 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v203[1] = v20;
  v202[2] = NSParagraphStyleAttributeName;
  v203[2] = *(_QWORD *)(v21 + 128);
  v22 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v203, v202, 3LL);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  _objc_msgSend(CFSTR("档位"), "drawInRect:withAttributes:", v23);
  v25 = *(_QWORD *)(v24 + 56);
  v26 = *(_QWORD *)(v24 + 8);
  v135 = *(double *)(v24 + 48) + v183.f64[0] + 2.0;
  v136 = 0x4008000000000000LL;
  v137 = v25;
  v138 = v26;
  v200[0] = NSFontAttributeName;
  v201[0] = *(_QWORD *)(v24 + 112);
  v200[1] = NSForegroundColorAttributeName;
  v27 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v201[1] = v28;
  v200[2] = NSParagraphStyleAttributeName;
  v201[2] = *(_QWORD *)(v29 + 136);
  v30 = (NSDictionary *)_objc_msgSend(
                          &OBJC_CLASS___NSDictionary,
                          "dictionaryWithObjects:forKeys:count:",
                          v201,
                          v200,
                          3LL);
  v31 = objc_retainAutoreleasedReturnValue(v30);
  _objc_msgSend(CFSTR("委托价"), "drawInRect:withAttributes:", v31);
  WeakRetained = objc_loadWeakRetained((id *)(v32 + 208));
  v34 = CFSTR("金额");
  if ( !(unsigned __int8)_objc_msgSend(WeakRetained, "disPlayMoney") )
    v34 = CFSTR("总委托量");
  v177 = objc_retain(v34);
  v36 = *(_QWORD *)(v35 + 72);
  v37 = *(_QWORD *)(v35 + 8);
  v139 = *(double *)(v35 + 64) + v183.f64[0] + 2.0;
  v140 = 0x4008000000000000LL;
  v141 = v36;
  v142 = v37;
  v198[0] = NSFontAttributeName;
  v199[0] = *(_QWORD *)(v35 + 112);
  v198[1] = NSForegroundColorAttributeName;
  v38 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v199[1] = v39;
  v198[2] = NSParagraphStyleAttributeName;
  v199[2] = *(_QWORD *)(v40 + 136);
  v41 = (NSDictionary *)_objc_msgSend(
                          &OBJC_CLASS___NSDictionary,
                          "dictionaryWithObjects:forKeys:count:",
                          v199,
                          v198,
                          3LL);
  v42 = objc_retainAutoreleasedReturnValue(v41);
  _objc_msgSend(v177, "drawInRect:withAttributes:", v42);
  v44 = *(_QWORD *)(v43 + 88);
  v45 = *(_QWORD *)(v43 + 8);
  v143 = *(double *)(v43 + 80) + v183.f64[0] + 2.0;
  v144 = 0x4008000000000000LL;
  v145 = v44;
  v146 = v45;
  v196[0] = NSFontAttributeName;
  v197[0] = *(_QWORD *)(v43 + 112);
  v196[1] = NSForegroundColorAttributeName;
  v46 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v197[1] = v47;
  v196[2] = NSParagraphStyleAttributeName;
  v187 = v48;
  v197[2] = *(_QWORD *)(v48 + 144);
  v49 = &_objc_msgSend;
  v50 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v197, v196, 3LL);
  v51 = objc_retainAutoreleasedReturnValue(v50);
  _objc_msgSend(CFSTR("明细"), "drawInRect:withAttributes:", v51);
  v52 = v172;
  if ( (__int64)_objc_msgSend(v52, "count") )
  {
    v53 = (__m128d)0x4014000000000000uLL;
    v53.f64[0] = v183.f64[0] + 5.0;
    v178 = v53;
    v54 = 16LL;
    v181 = (CGPoint *)(v187 + 168);
    v173 = 152LL;
    v182 = 184LL;
    v174 = "dangWei";
    v175 = "rowMarkedBgColor";
    v176 = "setFill";
    location = (id *)(v187 + 200);
    v169 = "showZhuBiToRect:model:isSell:";
    v162 = "name";
    v179 = 104LL;
    v171 = "normalColor";
    v163 = "price";
    v164 = "priceColor";
    v165 = "volNumber";
    v166 = 120LL;
    v167 = "mingXiColor";
    v55 = 0LL;
    v185 = 16LL;
    do
    {
      v56 = (void *)((__int64 (__fastcall *)(id, const char *, unsigned __int64))v49)(
                      v52,
                      "objectAtIndexedSubscript:",
                      v55);
      v57 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v56));
      v58 = *(double *)(v187 + 24);
      v59 = *(double *)(v187 + v54);
      rect.origin.x = v183.f64[0];
      rect.origin.y = v184.f64[0];
      rect.size.width = v58;
      rect.size.height = v59;
      v60 = CGRectContainsPoint(rect, *v181);
      v170 = v55;
      x = v57;
      if ( v60 )
      {
        v61 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v49)(*(_QWORD *)&v57, v174);
        if ( v61 != (void *)0x7FFFFFFFFFFFFFFFLL )
        {
          v180 = v61;
          v62 = (void *)((__int64 (__fastcall *)(void *, const char *))v49)(
                          &OBJC_CLASS___NSGraphicsContext,
                          "currentContext");
          v63 = objc_retainAutoreleasedReturnValue(v62);
          v64 = objc_retainAutorelease(v63);
          v65 = (CGContext *)((__int64 (__fastcall *)(id, const char *))v49)(v64, "graphicsPort");
          v66 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v49)(&OBJC_CLASS___HXThemeManager, v175);
          v67 = objc_retainAutoreleasedReturnValue(v66);
          ((void (__fastcall *)(id, const char *))v49)(v67, v176);
          v54 = v185;
          CGContextFillRect(v65, rect);
          objc_storeStrong(location, v68);
          v69 = v187;
          v70 = *(_QWORD *)(v187 + v173);
          v71 = v170;
          v72 = v182;
          *(_QWORD *)(v187 + v182) = v170 < v70;
          *(_QWORD *)(v69 + v72 + 8) = v180;
          v73 = *(double *)(v69 + 88);
          v74 = *(double *)(v69 + v54);
          v161.origin.x = *(double *)(v69 + 80) + v178.f64[0];
          v161.origin.y = v184.f64[0];
          v161.size.width = v73;
          v161.size.height = v74;
          if ( CGRectContainsPoint(v161, *v181) )
            ((void (__fastcall *)(__int64, const char *, _QWORD, bool))v49)(v187, v169, *(_QWORD *)&x, v71 < v70);
        }
        *v181 = CGPointZero;
        v75 = x;
      }
      else
      {
        v76 = v55 < *(_QWORD *)(v187 + v173);
        v75 = v57;
        if ( *(_QWORD *)(v187 + v182) == v76 )
        {
          v77 = *(_QWORD *)(v187 + v182 + 8);
          if ( v77 == ((__int64 (__fastcall *)(_QWORD, const char *))v49)(*(_QWORD *)&v75, v174) )
          {
            v78 = (void *)((__int64 (__fastcall *)(void *, const char *))v49)(
                            &OBJC_CLASS___NSGraphicsContext,
                            "currentContext");
            v79 = objc_retainAutoreleasedReturnValue(v78);
            v80 = objc_retainAutorelease(v79);
            v81 = (CGContext *)((__int64 (__fastcall *)(id, const char *))v49)(v80, "graphicsPort");
            v82 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v49)(
                            &OBJC_CLASS___HXThemeManager,
                            v175);
            v83 = objc_retainAutoreleasedReturnValue(v82);
            ((void (__fastcall *)(id, const char *))v49)(v83, v176);
            v54 = v185;
            v84 = *(double *)(v187 + 24);
            v85 = *(double *)(v187 + v185);
            v147.origin.x = v183.f64[0];
            v147.origin.y = v184.f64[0];
            v147.size.width = v84;
            v147.size.height = v85;
            v75 = x;
            CGContextFillRect(v81, v147);
          }
        }
      }
      v86 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v49)(*(_QWORD *)&v75, v162);
      objc_retainAutoreleasedReturnValue(v86);
      v87 = v187;
      v88 = *(_QWORD *)(v187 + 40);
      v89 = *(_QWORD *)(v187 + v54);
      v148 = *(double *)(v187 + 32) + v178.f64[0];
      v149 = v184.f64[0];
      v150 = v88;
      v151 = v89;
      v194[0] = (__int64)NSFontAttributeName;
      v195[0] = *(_QWORD *)(v187 + v179);
      v194[1] = (__int64)NSForegroundColorAttributeName;
      v90 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v49)(*(_QWORD *)&v75, v171);
      v180 = objc_retainAutoreleasedReturnValue(v90);
      v195[1] = (__int64)v180;
      v194[2] = (__int64)NSParagraphStyleAttributeName;
      v195[2] = *(_QWORD *)(v87 + 128);
      v91 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64 *, __int64 *, __int64))v49)(
                      &OBJC_CLASS___NSDictionary,
                      "dictionaryWithObjects:forKeys:count:",
                      v195,
                      v194,
                      3LL);
      v92 = v49;
      v93 = objc_retainAutoreleasedReturnValue(v91);
      ((void (__fastcall *)(__int64, const char *, id))v92)(v94, "drawInRect:withAttributes:", v93);
      v96 = x;
      v97 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v92)(*(_QWORD *)&x, v163);
      objc_retainAutoreleasedReturnValue(v97);
      v98 = *(_QWORD *)(v87 + 56);
      v99 = *(_QWORD *)(v87 + v185);
      v152 = *(double *)(v87 + 48) + v178.f64[0];
      v153 = v184.f64[0];
      v154 = v98;
      v155 = v99;
      v192[0] = (__int64)NSFontAttributeName;
      v193[0] = *(_QWORD *)(v87 + v179);
      v192[1] = (__int64)NSForegroundColorAttributeName;
      v100 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v92)(*(_QWORD *)&v96, v164);
      v101 = objc_retainAutoreleasedReturnValue(v100);
      v193[1] = (__int64)v101;
      v192[2] = (__int64)NSParagraphStyleAttributeName;
      v193[2] = *(_QWORD *)(v87 + 136);
      v102 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64 *, __int64 *, __int64))v92)(
                       &OBJC_CLASS___NSDictionary,
                       "dictionaryWithObjects:forKeys:count:",
                       v193,
                       v192,
                       3LL);
      v103 = objc_retainAutoreleasedReturnValue(v102);
      ((void (__fastcall *)(__int64, const char *, id))v92)(v104, "drawInRect:withAttributes:", v103);
      v106 = x;
      v107 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v92)(*(_QWORD *)&x, v165);
      objc_retainAutoreleasedReturnValue(v107);
      v108 = *(_QWORD *)(v87 + 72);
      v109 = *(_QWORD *)(v87 + v185);
      v156 = *(double *)(v87 + 64) + v178.f64[0];
      v157 = v184.f64[0];
      v158 = v108;
      v159 = v109;
      v190[0] = (__int64)NSFontAttributeName;
      v191[0] = *(_QWORD *)(v87 + v179);
      v190[1] = (__int64)NSForegroundColorAttributeName;
      v110 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v92)(*(_QWORD *)&v106, v171);
      v111 = objc_retainAutoreleasedReturnValue(v110);
      v191[1] = (__int64)v111;
      v190[2] = (__int64)NSParagraphStyleAttributeName;
      v191[2] = *(_QWORD *)(v87 + 136);
      v112 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64 *, __int64 *, __int64))v92)(
                       &OBJC_CLASS___NSDictionary,
                       "dictionaryWithObjects:forKeys:count:",
                       v191,
                       v190,
                       3LL);
      v113 = objc_retainAutoreleasedReturnValue(v112);
      ((void (__fastcall *)(__int64, const char *, id))v92)(v114, "drawInRect:withAttributes:", v113);
      v116.f64[1] = 2.0;
      v116.f64[0] = *(double *)(v87 + 80);
      v117 = *(_QWORD *)(v87 + 88);
      v118 = *(double *)(v87 + v185) + -2.0;
      v128 = _mm_add_pd(v116, _mm_unpacklo_pd(v178, v184));
      v129 = v117;
      v130 = v118;
      v188[0] = (__int64)NSFontAttributeName;
      v189[0] = *(_QWORD *)(v87 + v166);
      v188[1] = (__int64)NSForegroundColorAttributeName;
      v119 = (void *)((__int64 (__fastcall *)(_QWORD, const char *))v92)(*(_QWORD *)&x, v167);
      v120 = objc_retainAutoreleasedReturnValue(v119);
      v189[1] = (__int64)v120;
      v188[2] = (__int64)NSParagraphStyleAttributeName;
      v189[2] = *(_QWORD *)(v87 + 144);
      v121 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64 *, __int64 *, __int64))v92)(
                       &OBJC_CLASS___NSDictionary,
                       "dictionaryWithObjects:forKeys:count:",
                       v189,
                       v188,
                       3LL);
      v122 = objc_retainAutoreleasedReturnValue(v121);
      ((void (__fastcall *)(__CFString *, const char *, id))v92)(CFSTR("●"), "drawInRect:withAttributes:", v122);
      v123 = v120;
      v49 = v92;
      v54 = v185;
      v124.f64[1] = v184.f64[1];
      v124.f64[0] = v184.f64[0] + *(double *)(v87 + v54);
      v184 = v124;
      v55 = v170 + 1;
      v52 = v172;
    }
    while ( v55 < ((__int64 (__fastcall *)(id, const char *))v49)(v172, "count") );
  }
}

//----- (0000000100BAE33E) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView showZhuBiToRect:model:isSell:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        CGRect a3,
        id a4,
        char a5)
{
  objc_class *v8; // rax
  CGFloat MaxX; // xmm0_8
  id (*v22)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  signed __int8 v30; // r15
  id (*v31)(id, SEL, ...); // r12
  id rect; // [rsp+0h] [rbp-130h]
  id WeakRetained; // [rsp+B0h] [rbp-80h]
  TradeStallsFiveHundredDocumentView *v53; // [rsp+D0h] [rbp-60h]

  LODWORD(v59) = a5;
  v6 = objc_retain(a4);
  if ( !*(_QWORD *)&self->mingXiW )
  {
    v7 = objc_alloc((Class)&OBJC_CLASS___TradeStallsZhuBiWindowController);
    v8 = (objc_class *)_objc_msgSend(v7, "initWithWindowNibName:", CFSTR("TradeStallsZhuBiWindowController"));
    v10 = *(Class *)((char *)&self->super.super.super.isa + v9);
    *(Class *)((char *)&self->super.super.super.isa + v9) = v8;
  }
  MaxX = CGRectGetMaxX(a3);
  _objc_msgSend(self, "convertPoint:toView:", 0LL, MaxX, a3.origin.y);
  v48 = MaxX;
  y = a3.origin.y;
  v12 = _objc_msgSend(self, "window");
  v13 = (const char *)objc_retainAutoreleasedReturnValue(v12);
  v14 = (char *)v13;
  if ( v13 )
  {
    objc_msgSend_stret(&v40, v13, "frame");
    v15 = *(double *)&v40;
  }
  else
  {
    v41 = 0LL;
    v40 = 0LL;
    v15 = 0.0;
  }
  v57 = v15;
  v16 = _objc_msgSend(self, "window");
  v17 = (const char *)objc_retainAutoreleasedReturnValue(v16);
  v18 = (char *)v17;
  if ( v17 )
  {
    objc_msgSend_stret(&v42, v17, "frame");
    v19 = *((double *)&v42 + 1);
  }
  else
  {
    v43 = 0LL;
    v42 = 0LL;
    v19 = 0.0;
  }
  v58 = v19;
  v51 = *(Class *)((char *)&self->super.super.super.isa + v20);
  WeakRetained = objc_loadWeakRetained((id *)&self->volX);
  v21 = _objc_msgSend(WeakRetained, "stockCode");
  v56 = objc_retainAutoreleasedReturnValue(v21);
  v50 = objc_loadWeakRetained((id *)&self->volX);
  v23 = v22(v50, "market");
  v52 = objc_retainAutoreleasedReturnValue(v23);
  v25 = v24(v6, "price");
  v54 = objc_retainAutoreleasedReturnValue(v25);
  v46 = v6;
  v27 = v26(v6, "priceColor");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v55 = objc_loadWeakRetained((id *)&self->volX);
  v30 = (unsigned __int8)v29(v55, "disPlayMoney");
  v32 = v31(self, "window");
  v33 = (const char *)objc_retainAutoreleasedReturnValue(v32);
  v53 = self;
  if ( v33 )
  {
    objc_msgSend_stret(&v44, v33, "frame");
  }
  else
  {
    v45 = 0LL;
    v44 = 0LL;
  }
  LODWORD(rect_8) = v30;
  rect = v28;
  v34 = (unsigned int)(char)v59;
  v35 = v52;
  v59 = v28;
  v36 = v54;
  _objc_msgSend(
    v51,
    "requestFoZhuBiData:market:price:isSell:priceColor:disPlayMoney:locationInScreen:fromWindowFrame:",
    v56,
    v52,
    v54,
    v34,
    v48 + v57,
    y + v58,
    rect,
    rect_8,
    v44,
    v45,
    v40,
    v41,
    v42,
    v43);
  _objc_msgSend(*(id *)&v53->mingXiW, "showWindow:");
}

//----- (0000000100BAE67E) ----------------------------------------------------
NSDictionary *__cdecl -[TradeStallsFiveHundredDocumentView marketPageRelationDic](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2)
{
  NSDictionary *v8; // rax
  objc_class *v9; // rax

  mingXiX = self->mingXiX;
  if ( mingXiX == 0.0 )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSBundle, "mainBundle");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = _objc_msgSend(v5, "pathForResource:ofType:", CFSTR("MarketPageRelation"), CFSTR("plist"));
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithContentsOfFile:", v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = *(Class *)((char *)&self->super.super.super.isa + v10);
    *(Class *)((char *)&self->super.super.super.isa + v10) = v9;
    mingXiX = *(double *)((char *)&self->super.super.super.isa + v12);
  }
  return (NSDictionary *)objc_retainAutoreleaseReturnValue(*(id *)&mingXiX);
}

//----- (0000000100BAE743) ----------------------------------------------------
TradeStallsFiveHundredQuoteItem *__cdecl -[TradeStallsFiveHundredDocumentView quoteItem](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->volX);
  return (TradeStallsFiveHundredQuoteItem *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100BAE75C) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView setQuoteItem:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->volX, a3);
}

//----- (0000000100BAE770) ----------------------------------------------------
id __cdecl -[TradeStallsFiveHundredDocumentView frameChangeBlock](TradeStallsFiveHundredDocumentView *self, SEL a2)
{
  return objc_getProperty(self, a2, 216LL, 0);
}

//----- (0000000100BAE783) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView setFrameChangeBlock:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 216LL);
}

//----- (0000000100BAE794) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView setMarketPageRelationDic:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->mingXiX, a3);
}

//----- (0000000100BAE7A8) ----------------------------------------------------
TradeStallsZhuBiWindowController *__cdecl -[TradeStallsFiveHundredDocumentView zhuBiWindowController](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2)
{
  return *(TradeStallsZhuBiWindowController **)&self->mingXiW;
}

//----- (0000000100BAE7B9) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView setZhuBiWindowController:](
        TradeStallsFiveHundredDocumentView *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->mingXiW, a3);
}

//----- (0000000100BAE7CD) ----------------------------------------------------
void __cdecl -[TradeStallsFiveHundredDocumentView .cxx_destruct](TradeStallsFiveHundredDocumentView *self, SEL a2)
{
  objc_storeStrong((id *)&self->mingXiW, 0LL);
  objc_storeStrong((id *)&self->mingXiX, 0LL);
  objc_storeStrong((id *)&self->volW, 0LL);
  objc_destroyWeak((id *)&self->volX);
  objc_storeStrong((id *)&self->priceW, 0LL);
  objc_storeStrong((id *)&self->super._vFlags, 0LL);
  objc_storeStrong((id *)&self->super._viewAuxiliary, 0LL);
  objc_storeStrong(&self->super._dragTypes, 0LL);
  objc_storeStrong((id *)&self->super._layer, 0LL);
  objc_storeStrong(&self->super._frameMatrix, 0LL);
  objc_storeStrong(&self->super._unused_was_gState, 0LL);
}

