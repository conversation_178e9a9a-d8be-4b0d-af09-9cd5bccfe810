id __cdecl +[ZiXunWindowController sharedInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100331488;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D2E48 != -1 )
    dispatch_once(&qword_1016D2E48, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D2E40);
}

//----- (0000000100331488) ----------------------------------------------------
void __fastcall sub_100331488(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "initWithWindowNibName:", CFSTR("ZiXunWindowController"));
  v3 = qword_1016D2E40;
  qword_1016D2E40 = v2;
}

//----- (00000001003314C1) ----------------------------------------------------
void __cdecl -[ZiXunWindowController windowDidLoad](ZiXunWindowController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunWindowController;
  -[HXCustomWindowController windowDidLoad](&v2, "windowDidLoad");
  -[ZiXunWindowController initObjects](self, "initObjects");
  -[ZiXunWindowController initSearchField](self, "initSearchField");
}

//----- (0000000100331517) ----------------------------------------------------
void __cdecl -[ZiXunWindowController initObjects](ZiXunWindowController *self, SEL a2)
{

  v2 = _objc_msgSend(self, "window");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "makeKeyWindow");
  v5 = _objc_msgSend(v4, "window");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setCollectionBehavior:", 512LL);
  v7 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(v9, "window");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "setBackgroundColor:", v8);
  v13 = _objc_msgSend(v12, "window");
  v24 = objc_retainAutoreleasedReturnValue(v13);
  v14 = _objc_msgSend(v24, "contentView");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = _objc_msgSend(v16, "ziXunContianerVC");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v18, "view");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  _objc_msgSend(v15, "addSubview:", v20);
  v22 = _objc_msgSend(v21, "window");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  _objc_msgSend(v23, "setTitleVisibility:", 1LL);
}

//----- (00000001003316B9) ----------------------------------------------------
MajorEventRequestModule *__cdecl -[ZiXunWindowController majorEventReqModule](ZiXunWindowController *self, SEL a2)
{
  NSDocument *document; // rdi
  NSDocument *v5; // rax
  NSDocument *v6; // rdi

  document = self->super.super._document;
  if ( !document )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___MajorEventRequestModule);
    v5 = (NSDocument *)_objc_msgSend(v4, "init");
    v6 = self->super.super._document;
    self->super.super._document = v5;
    document = self->super.super._document;
  }
  return (MajorEventRequestModule *)objc_retainAutoreleaseReturnValue(document);
}

//----- (000000010033170A) ----------------------------------------------------
ZiXunWindowContianerVC *__cdecl -[ZiXunWindowController ziXunContianerVC](ZiXunWindowController *self, SEL a2)
{
  NSArray *topLevelObjects; // rdi

  topLevelObjects = self->super.super._topLevelObjects;
  if ( !topLevelObjects )
  {
    v3 = objc_alloc((Class)&OBJC_CLASS___ZiXunWindowContianerVC);
    v4 = _objc_msgSend(v3, "initWithNibName:bundle:", CFSTR("ZiXunWindowContianerVC"), 0LL);
    v6 = *(void **)(v5 + 40);
    *(_QWORD *)(v5 + 40) = v4;
    v8 = _objc_msgSend(v7, "window");
    v18 = objc_retainAutoreleasedReturnValue(v8);
    v9 = _objc_msgSend(v18, "contentView");
    v10 = (const char *)objc_retainAutoreleasedReturnValue(v9);
    v12 = (char *)v10;
    if ( v10 )
      objc_msgSend_stret(v17, v10, "bounds");
    else
      memset(v17, 0, sizeof(v17));
    v13 = _objc_msgSend(*(id *)(v11 + 40), "view");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v14, "setFrame:");
    topLevelObjects = *(NSArray **)(v15 + 40);
  }
  return (ZiXunWindowContianerVC *)objc_retainAutoreleaseReturnValue(topLevelObjects);
}

//----- (0000000100331827) ----------------------------------------------------
void __cdecl -[ZiXunWindowController showWindowWithMenuTitle:stockCode:market:](
        ZiXunWindowController *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  ZiXunWindowContianerVC *v9; // rax
  ZiXunWindowContianerVC *v10; // r13
  ZiXunWindowContianerVC *v13; // rax
  ZiXunWindowContianerVC *v14; // rax

  objc_retain(a5);
  v7 = objc_retain(a4);
  v16 = objc_retain(a3);
  -[ZiXunWindowController queryStockNameForSearchTextFieldIfNeed:market:](
    self,
    "queryStockNameForSearchTextFieldIfNeed:market:",
    v7,
    v8);
  v9 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[ZiXunWindowContianerVC reloadContentViewWithStockCode:market:](
    v10,
    "reloadContentViewWithStockCode:market:",
    v7,
    v11);
  v13 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[ZiXunWindowContianerVC setSelectedMenuTitle:](v14, "setSelectedMenuTitle:", v16);
  _objc_msgSend(self, "showWindow:", 0LL);
}

//----- (0000000100331922) ----------------------------------------------------
void __cdecl -[ZiXunWindowController showWindowWithModel:stockCode:market:](
        ZiXunWindowController *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  ZiXunWindowContianerVC *v9; // rax
  ZiXunWindowContianerVC *v10; // r13
  ZiXunWindowContianerVC *v13; // rax
  ZiXunWindowContianerVC *v14; // rax

  objc_retain(a5);
  v7 = objc_retain(a4);
  v16 = objc_retain(a3);
  -[ZiXunWindowController queryStockNameForSearchTextFieldIfNeed:market:](
    self,
    "queryStockNameForSearchTextFieldIfNeed:market:",
    v7,
    v8);
  v9 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[ZiXunWindowContianerVC reloadContentViewWithStockCode:market:](
    v10,
    "reloadContentViewWithStockCode:market:",
    v7,
    v11);
  v13 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[ZiXunWindowContianerVC setSelectedItem:](v14, "setSelectedItem:", v16);
  _objc_msgSend(self, "showWindow:", 0LL);
}

//----- (0000000100331A1D) ----------------------------------------------------
void __cdecl -[ZiXunWindowController initSearchField](ZiXunWindowController *self, SEL a2)
{
  ZiXunKeyboardSpiritWindowController *v2; // rax
  ZiXunKeyboardSpiritWindowController *v3; // r14
  id WeakRetained; // rbx
  _QWORD v22[4]; // [rsp+8h] [rbp-B8h] BYREF
  _QWORD v24[4]; // [rsp+30h] [rbp-90h] BYREF
  _QWORD v26[4]; // [rsp+58h] [rbp-68h] BYREF
  id to; // [rsp+78h] [rbp-48h] BYREF
  id location[6]; // [rsp+90h] [rbp-30h] BYREF

  objc_initWeak(location, self);
  +[HXVoiceRecognitionWindowController setHotkeyMenuState](
    &OBJC_CLASS___HXVoiceRecognitionWindowController,
    "setHotkeyMenuState");
  v2 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
         &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
         "sharedInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  -[ZiXunKeyboardSpiritWindowController setKeyboardTextFieldView:](v3, "setKeyboardTextFieldView:", WeakRetained);
  -[ZiXunKeyboardSpiritWindowController backToMain](v3, "backToMain");
  v26[0] = _NSConcreteStackBlock;
  v26[1] = 3254779904LL;
  v26[2] = sub_100331DA7;
  v26[3] = &unk_1012DBD90;
  objc_copyWeak(&to, location);
  -[ZiXunKeyboardSpiritWindowController setSearchResultBlock:](v3, "setSearchResultBlock:", v26);
  v28 = v3;
  v5 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("search_AudioInput_small_black"));
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setSize:", 10.0, 15.5);
  v7 = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  v8 = _objc_msgSend(v7, "rightButton");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setImage:", v6);
  v11 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("search_AudioInput_small_blue"));
  objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setSize:", 10.0, 15.5);
  v13 = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  v14 = _objc_msgSend(v13, "rightButton");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v15, "setAlternateImage:", v16);
  v17 = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  _objc_msgSend(v17, "setTextFieldDidChangeBlock:", &stru_1012DF878);
  v29 = v18;
  v22[0] = _NSConcreteStackBlock;
  v22[1] = 3254779904LL;
  v22[2] = sub_100331EDD;
  v22[3] = &unk_1012DAA10;
  objc_copyWeak(&v23, location);
  v19 = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  _objc_msgSend(v19, "setTextFieldBeginEditing:", v22);
  v24[0] = _NSConcreteStackBlock;
  v24[1] = 3254779904LL;
  v24[2] = sub_100331F10;
  v24[3] = v20;
  objc_copyWeak(&v25, location);
  v21 = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  _objc_msgSend(v21, "setRightButtonActionBlock:", v24);
  objc_destroyWeak(&v25);
  objc_destroyWeak(&v23);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100331DA7) ----------------------------------------------------
__int64 __fastcall sub_100331DA7(__int64 a1, void *a2, void *a3)
{
  id (__cdecl *v4)(id); // r12
  id WeakRetained; // rbx

  v3 = objc_retain(a3);
  v5 = v4(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "searchResultDidChange:market:", v5, v3);
  v7(v5);
  return v8(WeakRetained);
}

//----- (0000000100331E19) ----------------------------------------------------
void __cdecl sub_100331E19(id a1)
{
  ZiXunKeyboardSpiritWindowController *v1; // rax
  ZiXunKeyboardSpiritWindowController *v2; // r15
  ZiXunKeyboardSpiritWindowController *v6; // rax
  ZiXunKeyboardSpiritWindowController *v7; // rbx

  v1 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
         &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
         "sharedInstance");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v3 = _objc_msgSend(v2, "window");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "isVisible");
  if ( !v5 )
    _objc_msgSend(v2, "showWindow:", 0LL);
  v6 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
         &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
         "sharedInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[ZiXunKeyboardSpiritWindowController textDidChange](v7, "textDidChange");
}

//----- (0000000100331EDD) ----------------------------------------------------
void __fastcall sub_100331EDD(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "showKeyBoardSpriritWindow:", 0LL);
}

//----- (0000000100331F10) ----------------------------------------------------
void __fastcall sub_100331F10(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "showVoiceRecognitionWindow");
}

//----- (0000000100331F41) ----------------------------------------------------
void __cdecl -[ZiXunWindowController showVoiceRecognitionWindow](ZiXunWindowController *self, SEL a2)
{
  HXVoiceRecognitionWindowController *v2; // rax
  id WeakRetained; // rbx
  ZiXunKeyboardSpiritWindowController *v7; // rax
  ZiXunKeyboardSpiritWindowController *v8; // rbx
  _QWORD v11[4]; // [rsp+0h] [rbp-50h] BYREF
  id to; // [rsp+20h] [rbp-30h] BYREF
  id location[5]; // [rsp+28h] [rbp-28h] BYREF

  objc_initWeak(location, self);
  v2 = +[HXVoiceRecognitionWindowController sharedInstance](
         &OBJC_CLASS___HXVoiceRecognitionWindowController,
         "sharedInstance");
  objc_retainAutoreleasedReturnValue(v2);
  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  _objc_msgSend(v4, "setSearchTextFieldView:", WeakRetained);
  _objc_msgSend(v5, "setMainWC:", self);
  v11[0] = _NSConcreteStackBlock;
  v11[1] = 3254779904LL;
  v11[2] = sub_10033209E;
  v11[3] = &unk_1012DC1F0;
  objc_copyWeak(&to, location);
  _objc_msgSend(v6, "setFinishRecordingBlock:", v11);
  v7 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
         &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
         "sharedInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[ZiXunKeyboardSpiritWindowController close](v8, "close");
  _objc_msgSend(v9, "showVoiceRecognitionWindow");
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (000000010033209E) ----------------------------------------------------
void __fastcall sub_10033209E(__int64 a1, void *a2)
{
  id WeakRetained; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12

  v2 = objc_retain(a2);
  if ( _objc_msgSend(v2, "length") )
  {
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "showKeyBoardSpriritWindow:", 1LL);
    v5 = v4(&OBJC_CLASS___ZiXunKeyboardSpiritWindowController, "sharedInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7(v6, "voiceRequest:", v2);
    v8 = objc_loadWeakRetained((id *)(a1 + 32));
    v10 = v9(v8, "searchTextFieldView");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "textDidChange");
  }
}

//----- (000000010033218E) ----------------------------------------------------
void __cdecl -[ZiXunWindowController showKeyBoardSpriritWindow:](ZiXunWindowController *self, SEL a2, char a3)
{
  ZiXunKeyboardSpiritWindowController *v3; // rax
  ZiXunKeyboardSpiritWindowController *v4; // r14
  HXVoiceRecognitionWindowController *v8; // rax
  HXVoiceRecognitionWindowController *v9; // rax
  id WeakRetained; // r14
  SEL v34; // r12
  SEL v39; // r12
  __CFString *v58; // rcx
  id *location; // [rsp+108h] [rbp-58h]
  int v75; // [rsp+114h] [rbp-4Ch]

  v75 = a3;
  v76 = self;
  v3 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
         &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
         "sharedInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "window");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "isVisible");
  if ( !v7 )
  {
    v77 = v4;
    v8 = +[HXVoiceRecognitionWindowController sharedInstance](
           &OBJC_CLASS___HXVoiceRecognitionWindowController,
           "sharedInstance");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "window");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = (unsigned __int8)_objc_msgSend(v11, "isVisible");
    if ( v12 )
    {
      _objc_msgSend(v13, "cancelRecording");
      _objc_msgSend(v14, "close");
    }
    v73 = v13;
    WeakRetained = objc_loadWeakRetained((id *)v76 + 3);
    v16 = _objc_msgSend(WeakRetained, "superview");
    objc_retainAutoreleasedReturnValue(v16);
    location = (id *)((char *)v76 + 24);
    v17 = (const char *)objc_loadWeakRetained((id *)v76 + 3);
    v19 = (char *)v17;
    if ( v17 )
    {
      objc_msgSend_stret(v60, v17, "frame");
      v20 = *((double *)v60 + 1);
      v22 = ((double (__fastcall *)(__int64, const char *, _QWORD, double, double))_objc_msgSend)(
              v21,
              "convertPoint:toView:",
              0LL,
              *(double *)v60,
              *((double *)v60 + 1));
    }
    else
    {
      memset(v60, 0, sizeof(v60));
      v20 = 0.0;
      v22 = ((double (__fastcall *)(__int64, const char *, _QWORD, double, double))_objc_msgSend)(
              v18,
              "convertPoint:toView:",
              0LL,
              0.0,
              0.0);
    }
    v79 = v22;
    v78 = v20;
    v24 = _objc_msgSend(v76, "window");
    v25 = (const char *)objc_retainAutoreleasedReturnValue(v24);
    v26 = (char *)v25;
    if ( v25 )
    {
      objc_msgSend_stret(v61, v25, "frame");
      v27 = *(double *)v61;
    }
    else
    {
      memset(v61, 0, sizeof(v61));
      v27 = 0.0;
    }
    v28 = v77;
    v79 = v79 + v27;
    v30 = _objc_msgSend(v29, "window");
    v31 = (const char *)objc_retainAutoreleasedReturnValue(v30);
    v32 = (char *)v31;
    if ( v31 )
    {
      objc_msgSend_stret(v62, v31, "frame");
      v33 = *((double *)v62 + 1);
    }
    else
    {
      memset(v62, 0, sizeof(v62));
      v33 = 0.0;
    }
    v79 = v79 + 1.0;
    v78 = v78 + v33 + -4.0;
    v35 = _objc_msgSend(v28, v34);
    v36 = (const char *)objc_retainAutoreleasedReturnValue(v35);
    v37 = (char *)v36;
    if ( v36 )
    {
      objc_msgSend_stret(&v63, v36, "frame");
      v38 = *((double *)&v64 + 1);
    }
    else
    {
      v64 = 0LL;
      v63 = 0LL;
      v38 = 0.0;
    }
    v78 = v78 - v38;
    v40 = _objc_msgSend(v28, v39);
    v41 = objc_retainAutoreleasedReturnValue(v40);
    v69 = v79;
    v70 = v78;
    v42 = (const char *)objc_loadWeakRetained(location);
    if ( v42 )
    {
      objc_msgSend_stret(&v65, v42, "bounds");
      v43 = *(double *)&v66 + -2.0;
    }
    else
    {
      v66 = 0LL;
      v65 = 0LL;
      v43 = -2.0;
    }
    v71 = v43;
    v44 = _objc_msgSend(v28, "window");
    v45 = (const char *)objc_retainAutoreleasedReturnValue(v44);
    v46 = (char *)v45;
    if ( v45 )
    {
      objc_msgSend_stret(&v67, v45, "frame");
      v47 = *((_QWORD *)&v68 + 1);
    }
    else
    {
      v68 = 0LL;
      v67 = 0LL;
      v47 = 0LL;
    }
    v72 = v47;
    _objc_msgSend(v41, "setFrame:display:animate:", 0LL, 0LL);
    v49 = _objc_msgSend(v76, "window");
    v50 = objc_retainAutoreleasedReturnValue(v49);
    v51 = _objc_msgSend(v77, "window");
    v52 = objc_retainAutoreleasedReturnValue(v51);
    _objc_msgSend(v50, "addChildWindow:ordered:", v52, 1LL);
    _objc_msgSend(v53, "windowDidShow");
    if ( !(_BYTE)v75 )
    {
      v79 = COERCE_DOUBLE(objc_loadWeakRetained(location));
      v54 = _objc_msgSend(*(id *)&v79, "textField");
      v55 = objc_retainAutoreleasedReturnValue(v54);
      v56 = _objc_msgSend(v55, "stringValue");
      v57 = objc_retainAutoreleasedReturnValue(v56);
      _objc_msgSend(v57, "length");
      v58 = CFSTR("全局.键盘搜索唤起_键盘_搜索框");
      if ( !v59 )
        v58 = CFSTR("全局.键盘搜索唤起_按键_搜索框");
      +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
        &OBJC_CLASS___UserLogSendingQueueManager,
        "sendUserLog:action:params:needWait:",
        11LL,
        v58,
        0LL,
        1LL);
    }
    v4 = (ZiXunKeyboardSpiritWindowController *)v77;
  }
}

//----- (00000001003326F4) ----------------------------------------------------
void __cdecl -[ZiXunWindowController showKeyBoardSpiritWindow:](ZiXunWindowController *self, SEL a2, id a3)
{
  id WeakRetained; // r15
  ZiXunKeyboardSpiritWindowController *v8; // rax
  ZiXunKeyboardSpiritWindowController *v9; // r15
  id *v12; // r12

  v3 = objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)(v4 + 24));
  v6 = _objc_msgSend(WeakRetained, "window");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( v7 )
  {
    v8 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
           &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
           "sharedInstance");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v3, "characters");
    v15 = v3;
    v11 = objc_retainAutoreleasedReturnValue(v10);
    -[ZiXunKeyboardSpiritWindowController keyDownThenRequest:](v9, "keyDownThenRequest:", v11);
    v13 = objc_loadWeakRetained(v12);
    _objc_msgSend(v13, "textDidChange");
    v14 = v11;
    v3 = v15;
  }
}

//----- (00000001003327EA) ----------------------------------------------------
void __cdecl -[ZiXunWindowController searchResultDidChange:market:](ZiXunWindowController *self, SEL a2, id a3, id a4)
{
  ZiXunKeyboardSpiritWindowController *v11; // rax
  ZiXunKeyboardSpiritWindowController *v12; // rbx
  ZiXunWindowContianerVC *v13; // rax
  ZiXunWindowContianerVC *v14; // rbx
  ZiXunWindowContianerVC *v16; // rax
  ZiXunWindowContianerVC *v17; // rbx

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
  {
    v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8) )
    {
      if ( _objc_msgSend(v9, "length") && _objc_msgSend(v5, "length") )
      {
        -[ZiXunWindowController queryStockNameForSearchTextFieldIfNeed:market:](
          self,
          "queryStockNameForSearchTextFieldIfNeed:market:",
          v10,
          v5);
        v11 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
                &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
                "sharedInstance");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        -[ZiXunKeyboardSpiritWindowController close](v12, "close");
        v13 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        -[ZiXunWindowContianerVC reloadContentViewWithStockCode:market:](
          v14,
          "reloadContentViewWithStockCode:market:",
          v15,
          v5);
        v16 = -[ZiXunWindowController ziXunContianerVC](self, "ziXunContianerVC");
        v17 = objc_retainAutoreleasedReturnValue(v16);
        -[ZiXunWindowContianerVC resetSelection](v17, "resetSelection");
      }
    }
  }
}

//----- (000000010033296D) ----------------------------------------------------
void __cdecl -[ZiXunWindowController keyDown:](ZiXunWindowController *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  if ( (unsigned __int8)-[ZiXunWindowController shouldShowKeyBoardSpiritWindow:](
                          self,
                          "shouldShowKeyBoardSpiritWindow:",
                          v3) )
    -[ZiXunWindowController showKeyBoardSpiritWindow:](self, "showKeyBoardSpiritWindow:", v3);
}

//----- (00000001003329BA) ----------------------------------------------------
void __cdecl -[ZiXunWindowController queryStockNameForSearchTextFieldIfNeed:market:](
        ZiXunWindowController *self,
        SEL a2,
        id a3,
        id a4)
{
  KLineAccessoryManager *v6; // rax
  KLineAccessoryManager *v7; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // r14
  NSDictionary *v14; // rax
  NSDictionary *v15; // rbx
  DataRequestCenter *v19; // rax
  ZiXunKeyboardSpiritWindowController *v22; // rax
  ZiXunKeyboardSpiritWindowController *v23; // rbx
  _QWORD v26[4]; // [rsp+8h] [rbp-78h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = +[KLineAccessoryManager sharedInstance](&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = -[KLineAccessoryManager getaStockName:withCode:](v7, "getaStockName:withCode:", v5, v8);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  if ( v10 && !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v22 = +[ZiXunKeyboardSpiritWindowController sharedInstance](
            &OBJC_CLASS___ZiXunKeyboardSpiritWindowController,
            "sharedInstance");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    -[ZiXunKeyboardSpiritWindowController setStockCode:](v23, "setStockCode:", v24);
    -[ZiXunKeyboardSpiritWindowController setStockName:](v23, "setStockName:", v10);
    -[ZiXunKeyboardSpiritWindowController backToMain](v23, "backToMain");
  }
  else
  {
    v30 = v10;
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 200LL);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v32 = v13;
    v29 = v5;
    v14 = _objc_msgSend(
            &OBJC_CLASS___NSDictionary,
            "dictionaryWithObjectsAndKeys:",
            v13,
            CFSTR("CodeList"),
            v5,
            CFSTR("Market"),
            CFSTR("5,55"),
            CFSTR("DataTypes"),
            v12,
            CFSTR("DataRequest_Type"),
            0LL);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18 = v17;
    if ( v15 )
      _objc_msgSend(v17, "addObject:", v15);
    v19 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
    objc_retainAutoreleasedReturnValue(v19);
    v31 = v15;
    v26[0] = _NSConcreteStackBlock;
    v26[1] = 3254779904LL;
    v26[2] = sub_100332C5F;
    v26[3] = &unk_1012DF898;
    v27 = objc_retain(v32);
    v5 = v29;
    v28 = objc_retain(v29);
    _objc_msgSend(v20, "request:type:params:callBack:fail:", 2LL, 1LL, v18, v26, &stru_1012DF8C8);
    v10 = v30;
  }
}

//----- (0000000100332C5F) ----------------------------------------------------
void __fastcall sub_100332C5F(__int64 a1, void *a2)
{
  NSNumber *v10; // rax
  NSNumber *v11; // rax
  NSNumber *v19; // rax
  NSNumber *v20; // rax
  unsigned __int8 (__fastcall *v34)(id, const char *, __int64); // r12

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    v54 = v2;
    v4 = objc_retain(v2);
    v5 = _objc_msgSend(v4, "arrBody");
    v60 = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(v60, "lastObject");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v57 = v8;
    v9 = v7;
    v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v9, "objectForKey:", v11);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v13);
    v56 = objc_retainAutoreleasedReturnValue(v14);
    v61 = v4;
    v16 = _objc_msgSend(v4, "arrBody");
    v58 = objc_retainAutoreleasedReturnValue(v16);
    v17 = _objc_msgSend(v58, "lastObject");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = _objc_msgSend(v18, "objectForKey:", v20);
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v23 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v22);
    v24 = objc_retainAutoreleasedReturnValue(v23);
    if ( (unsigned __int8)_objc_msgSend(v56, "isEqualToString:", *(_QWORD *)(v57 + 32))
      && (unsigned __int8)_objc_msgSend(v24, v26, *(_QWORD *)(v57 + 40)) )
    {
      v59 = v24;
      v27 = _objc_msgSend(v61, "arrBody");
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29 = _objc_msgSend(v28, "lastObject");
      v30 = v28;
      v31 = objc_retainAutoreleasedReturnValue(v29);
      v33 = v32(&OBJC_CLASS___NSDictionary, "class");
      if ( v34(v31, "isKindOfClass:", v33) )
      {
        v35 = v57;
        v36 = objc_retain(v31);
        v38 = (void *)v37(&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
        v55 = v31;
        v39 = objc_retainAutoreleasedReturnValue(v38);
        v40(v39, "saveKLineAccessoryInfoDict:withMarket:andCode:", v36, *(_QWORD *)(v35 + 40), *(_QWORD *)(v35 + 32));
        v42 = (void *)v41(&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
        v43 = objc_retainAutoreleasedReturnValue(v42);
        v45 = (void *)v44(v43, "getaStockName:withCode:", *(_QWORD *)(v35 + 40), *(_QWORD *)(v35 + 32));
        v46 = objc_retainAutoreleasedReturnValue(v45);
        v48 = (void *)v47(&OBJC_CLASS___ZiXunKeyboardSpiritWindowController, "sharedInstance");
        v49 = objc_retainAutoreleasedReturnValue(v48);
        v50(v49, "setStockCode:", *(_QWORD *)(v57 + 32));
        v51(v49, "setStockName:", v46);
        v52(v49, "backToMain");
        v53 = v49;
        v31 = v55;
      }
      v24 = v59;
    }
    v2 = v54;
  }
}

//----- (0000000100333016) ----------------------------------------------------
void __cdecl sub_100333016(id a1, unsigned __int64 a2)
{
  ;
}

//----- (000000010033301C) ----------------------------------------------------
char __cdecl -[ZiXunWindowController shouldShowKeyBoardSpiritWindow:](ZiXunWindowController *self, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "characters");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = 1;
  if ( v6(v5, "length") )
  {
    v7 = 0;
    if ( (unsigned __int16)((unsigned __int16)_objc_msgSend(v5, "characterAtIndex:", 0LL) + 2304) >= 0x48u )
    {
      v7 = 1;
      if ( ((unsigned int)_objc_msgSend(v3, "modifierFlags") & 0x100000) != 0 )
      {
        v9 = v8(v3, "characters");
        v10 = objc_retainAutoreleasedReturnValue(v9);
        v11(v10, "isEqualToString:", CFSTR("w"));
        if ( v12 )
          v7 = 0;
      }
    }
  }
  return v7;
}

//----- (0000000100333108) ----------------------------------------------------
NSToolbarItem *__cdecl -[ZiXunWindowController searchToolBarItem](ZiXunWindowController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._window);
  return (NSToolbarItem *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100333121) ----------------------------------------------------
void __cdecl -[ZiXunWindowController setSearchToolBarItem:](ZiXunWindowController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._window, a3);
}

//----- (0000000100333135) ----------------------------------------------------
HXSearchTextFieldView *__cdecl -[ZiXunWindowController searchTextFieldView](ZiXunWindowController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._windowNibName);
  return (HXSearchTextFieldView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010033314E) ----------------------------------------------------
void __cdecl -[ZiXunWindowController setSearchTextFieldView:](ZiXunWindowController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._windowNibName, a3);
}

//----- (0000000100333162) ----------------------------------------------------
void __cdecl -[ZiXunWindowController setMajorEventReqModule:](ZiXunWindowController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._document, a3);
}

//----- (0000000100333176) ----------------------------------------------------
void __cdecl -[ZiXunWindowController setZiXunContianerVC:](ZiXunWindowController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._topLevelObjects, a3);
}

//----- (000000010033318A) ----------------------------------------------------
void __cdecl -[ZiXunWindowController .cxx_destruct](ZiXunWindowController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super.super._topLevelObjects, 0LL);
  objc_storeStrong((id *)&self->super.super._document, 0LL);
  objc_destroyWeak((id *)&self->super.super._windowNibName);
  objc_destroyWeak((id *)&self->super.super._window);
}

