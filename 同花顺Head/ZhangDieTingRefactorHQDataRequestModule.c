NSMutableArray *__cdecl -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  NSMutableArray *reqInstanceArr; // rdi
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // rdi

  reqInstanceArr = self->_reqInstanceArr;
  if ( !reqInstanceArr )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (NSMutableArray *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->_reqInstanceArr;
    self->_reqInstanceArr = v5;
    reqInstanceArr = self->_reqInstanceArr;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(reqInstanceArr);
}

//----- (0000000100488099) ----------------------------------------------------
NSMutableArray *__cdecl -[ZhangDieTingRefactorHQDataRequestModule compeletDetailDataArr](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  NSMutableArray *compeletDetailDataArr; // rdi
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // rdi

  compeletDetailDataArr = self->_compeletDetailDataArr;
  if ( !compeletDetailDataArr )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (NSMutableArray *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->_compeletDetailDataArr;
    self->_compeletDetailDataArr = v5;
    compeletDetailDataArr = self->_compeletDetailDataArr;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(compeletDetailDataArr);
}

//----- (00000001004880E4) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule requestForCodeListSort:reqCallBack:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3,
        id a4)
{
  __CFString *v16; // r14
  __CFString *v22; // r15
  __CFString *v28; // rdi
  SEL v35; // r12
  NSNumber *v37; // rax
  NSNumber *v38; // rax
  NSString *v39; // rax
  NSString *v42; // rax
  NSString *v43; // rbx
  HXSocketCenter *v44; // rax
  HXSocketCenter *v45; // r15
  __CFString *v52; // [rsp+18h] [rbp-78h]
  NSString *v57; // [rsp+40h] [rbp-50h]

  v6 = objc_retain(a3);
  v7 = objc_retain(a4);
  v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  v9 = (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v8);
  if ( v7 && v9 )
  {
    _objc_msgSend(v10, "setIsRequestZhuangTaiData:", 0LL);
    v59 = v7;
    _objc_msgSend(v11, "setReqCallBack:", v7);
    v12 = +[HXTools getTenBillionLongFromTimeStamp](&OBJC_CLASS___HXTools, "getTenBillionLongFromTimeStamp");
    v55 = v13;
    _objc_msgSend(v13, "setReqInstance:", v12);
    v14 = _objc_msgSend(v6, "thsArrayForKey:", CFSTR("StockCodesAndMarket"));
    objc_retainAutoreleasedReturnValue(v14);
    v15 = _objc_msgSend(v6, "thsStringForKey:", CFSTR("sortorder"));
    v16 = (__CFString *)objc_retainAutoreleasedReturnValue(v15);
    v17 = _objc_msgSend(v6, "thsNumberForKey:", CFSTR("sortbegin"));
    v54 = objc_retainAutoreleasedReturnValue(v17);
    v18 = _objc_msgSend(v6, "thsNumberForKey:", CFSTR("sortcount"));
    v60 = objc_retainAutoreleasedReturnValue(v18);
    v49 = v6;
    v19 = _objc_msgSend(v6, "thsNumberForKey:", CFSTR("sortid"));
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v51 = v21;
    v22 = v16;
    if ( !v21 )
    {
      v24 = v54;
      v7 = v59;
      goto LABEL_18;
    }
    v61 = v20;
    if ( _objc_msgSend(v21, "count") )
    {
      v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      v24 = v54;
      if ( (unsigned __int8)_objc_msgSend(v54, "isKindOfClass:", v23) )
      {
        _objc_msgSend(v54, "doubleValue");
        v7 = v59;
        if ( v4 != 4294967295.0 )
        {
          _objc_msgSend(v54, "doubleValue");
          if ( v4 != 2147483648.0 )
          {
            v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
            if ( (unsigned __int8)_objc_msgSend(v60, "isKindOfClass:", v25) )
            {
              _objc_msgSend(v60, "doubleValue");
              if ( v4 != 4294967295.0 )
              {
                _objc_msgSend(v60, "doubleValue");
                if ( v4 != 2147483648.0 )
                {
                  v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
                  if ( !(unsigned __int8)_objc_msgSend(v61, "isKindOfClass:", v26) )
                  {
                    v20 = v61;
                    goto LABEL_18;
                  }
                  _objc_msgSend(v61, "doubleValue");
                  if ( v4 == 4294967295.0 )
                  {
                    v20 = v27;
LABEL_18:
                    v6 = v49;
                    goto LABEL_19;
                  }
                  _objc_msgSend(v27, "doubleValue");
                  if ( v4 != 2147483648.0 )
                  {
                    v28 = CFSTR("D");
                    if ( v22 )
                      v28 = v22;
                    v52 = objc_retain(v28);
                    v29 = _objc_msgSend(v55, "getCodeListStrAndMarketListStr:", v51);
                    v30 = objc_retainAutoreleasedReturnValue(v29);
                    if ( v30 )
                    {
                      v31 = v30;
                      v32 = _objc_msgSend(v30, "thsStringForKey:", CFSTR("CodeListStrKey"));
                      objc_retainAutoreleasedReturnValue(v32);
                      v50 = v31;
                      v33 = _objc_msgSend(v31, "thsStringForKey:", CFSTR("MarketListStrKey"));
                      v56 = objc_retainAutoreleasedReturnValue(v33);
                      v53 = v34;
                      if ( _objc_msgSend(v34, "length") && _objc_msgSend(v56, "length") )
                      {
                        v58 = (char *)_objc_msgSend(v54, "integerValue");
                        v36 = _objc_msgSend(v60, v35);
                        v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", &v58[(_QWORD)v36]);
                        v38 = objc_retainAutoreleasedReturnValue(v37);
                        v39 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("0(%@-%@)"), v54, v38);
                        v57 = objc_retainAutoreleasedReturnValue(v39);
                        v41 = _objc_msgSend(v55, "reqInstance");
                        v42 = _objc_msgSend(
                                &OBJC_CLASS___NSString,
                                "stringWithFormat:",
                                CFSTR("instid=%ld\nmethod=statssort\nperiod=0\ndatetime=%@\nmarket=%@\ncodelist=%@\nsortby=%@\nsortdir=%@\nsortbegin=%@\nsortcount=%@\ndataclass=updownlimit\nrettype=hqfile"),
                                v41,
                                v57,
                                v56,
                                v53,
                                v61,
                                v52,
                                v54,
                                v60);
                        v43 = objc_retainAutoreleasedReturnValue(v42);
                        v44 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
                        v45 = objc_retainAutoreleasedReturnValue(v44);
                        v47 = _objc_msgSend(v46, "reqInstance");
                        -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
                          v45,
                          "writeTextData:withTimeout:delegate:instance:",
                          v43,
                          v55,
                          v47,
                          10.0);
                      }
                      v30 = v50;
                    }
                    v20 = v61;
                    v22 = v52;
                    goto LABEL_18;
                  }
                }
              }
            }
          }
        }
LABEL_17:
        v20 = v61;
        goto LABEL_18;
      }
    }
    else
    {
      v24 = v54;
    }
    v7 = v59;
    goto LABEL_17;
  }
LABEL_19:
}

//----- (000000010048865D) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule requestForLeiXing:reqCallBack:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  bool v21; // zf
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  v10 = (unsigned __int8)v9(v5, "isKindOfClass:", v8);
  if ( v7 && v10 )
  {
    v11(self, "setIsRequestZhuangTaiData:", 0LL);
    v12(self, "setReqCallBack:", v7);
    v14 = v13(&OBJC_CLASS___HXTools, "getTenBillionLongFromTimeStamp");
    v15(self, "setReqInstance:", v14);
    v17 = v16(&OBJC_CLASS___NSNumber, "numberWithInt:", 330325LL);
    v39 = objc_retainAutoreleasedReturnValue(v17);
    v19 = v18(v5, "thsArrayForKey:", CFSTR("StockCodesAndMarket"));
    v20 = objc_retainAutoreleasedReturnValue(v19);
    if ( v20 )
    {
      v40 = v20;
      v21 = _objc_msgSend(v20, "count") == 0LL;
      v20 = v40;
      if ( !v21 )
      {
        v22 = -[ZhangDieTingRefactorHQDataRequestModule getCodeListStrAndMarketListStr:](
                self,
                "getCodeListStrAndMarketListStr:",
                v40);
        v23 = objc_retainAutoreleasedReturnValue(v22);
        if ( v23 )
        {
          v42 = v23;
          v25 = v24(v23, "thsStringForKey:", CFSTR("CodeListStrKey"));
          v41 = objc_retainAutoreleasedReturnValue(v25);
          v27 = v26(v42, "thsStringForKey:", CFSTR("MarketListStrKey"));
          v38 = objc_retainAutoreleasedReturnValue(v27);
          v29 = v28(self, "reqInstance");
          v31 = v30(
                  &OBJC_CLASS___NSString,
                  "stringWithFormat:",
                  CFSTR("instid=%ld\nmethod=statscalc\nperiod=0\ndatetime=0(0-0)\nmarket=%@\ncodelist=%@\ndatatype=%@\ndataclass=updownlimit\nrettype=hqfile"),
                  v29,
                  v38,
                  v41,
                  v39);
          v43 = objc_retainAutoreleasedReturnValue(v31);
          v33 = v32(&OBJC_CLASS___HXSocketCenter, "sharedInstance");
          v34 = objc_retainAutoreleasedReturnValue(v33);
          v36 = v35(self, "reqInstance");
          v37(v34, "writeTextData:withTimeout:delegate:instance:", v43, self, v36, 10.0);
          v23 = v42;
        }
        v20 = v40;
      }
    }
  }
}

//----- (00000001004888D8) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule requestForZhuangTai:reqCallBack:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3,
        id a4)
{
  NSNumber *v13; // rax
  unsigned __int64 v24; // r14
  unsigned __int64 v27; // r12
  SEL v48; // [rsp+58h] [rbp-148h]
  SEL v49; // [rsp+60h] [rbp-140h]
  SEL v50; // [rsp+68h] [rbp-138h]
  SEL v51; // [rsp+70h] [rbp-130h]
  SEL v52; // [rsp+78h] [rbp-128h]
  SEL v53; // [rsp+80h] [rbp-120h]
  SEL v56; // [rsp+98h] [rbp-108h]
  SEL v57; // [rsp+A0h] [rbp-100h]
  id obj; // [rsp+C0h] [rbp-E0h]

  v62 = self;
  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  v8 = (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6);
  if ( v5 && v8 )
  {
    v60 = v5;
    _objc_msgSend(v62, "setIsRequestZhuangTaiData:", 1LL);
    v9 = _objc_msgSend(v62, "reqInstanceArr");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    _objc_msgSend(v10, "removeAllObjects");
    v11 = _objc_msgSend(v62, "compeletDetailDataArr");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v12, "removeAllObjects");
    _objc_msgSend(v62, "setReqCallBack:", v5);
    v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330329LL);
    v59 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "thsArrayForKey:", CFSTR("StockCodesAndMarket"));
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17 = v16;
    if ( v16 && _objc_msgSend(v16, "count") )
    {
      v18 = _objc_msgSend(v62, "getSameMarketStockCodeDic:", v17);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      if ( v19 )
      {
        v55 = v17;
        v54 = v20;
        v58 = v19;
        v21 = _objc_msgSend(v19, "allKeys");
        v22 = 0.0;
        v43 = 0LL;
        v44 = 0LL;
        v45 = 0LL;
        v46 = 0LL;
        obj = objc_retainAutoreleasedReturnValue(v21);
        if ( _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v43, v67, 16LL) )
        {
          v23 = *(_QWORD *)v44;
          do
          {
            v56 = "doubleValue";
            v48 = "componentsJoinedByString:";
            v57 = "stringWithFormat:";
            v49 = "getTenBillionLongFromTimeStamp";
            v50 = "sharedInstance";
            v51 = "writeTextData:withTimeout:delegate:instance:";
            v52 = "numberWithLong:";
            v53 = "addObject:";
            v24 = 0LL;
            do
            {
              if ( *(_QWORD *)v44 != v23 )
                objc_enumerationMutation(obj);
              v25 = *(void **)(*((_QWORD *)&v43 + 1) + 8 * v24);
              v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
              if ( (unsigned __int8)_objc_msgSend(v25, "isKindOfClass:", v26) )
              {
                _objc_msgSend(v25, v56);
                if ( v22 != 4294967295.0 )
                {
                  _objc_msgSend(v25, v56);
                  if ( v22 != 2147483648.0 )
                  {
                    v28 = _objc_msgSend(v58, "thsArrayForKey:", v25);
                    v29 = objc_retainAutoreleasedReturnValue(v28);
                    v30 = v29;
                    if ( v29 )
                    {
                      v63 = v29;
                      v31 = _objc_msgSend(v29, "count");
                      v30 = v63;
                      if ( v31 )
                      {
                        v64 = &OBJC_CLASS___NSString;
                        v32 = _objc_msgSend(v63, v48, CFSTR(","));
                        v65 = objc_retainAutoreleasedReturnValue(v32);
                        v33 = _objc_msgSend(v64, v57, CFSTR("%@(%@)"), v25, v65);
                        v64 = objc_retainAutoreleasedReturnValue(v33);
                        v65 = _objc_msgSend(&OBJC_CLASS___HXTools, v49);
                        v34 = _objc_msgSend(
                                &OBJC_CLASS___NSString,
                                v57,
                                CFSTR("instid=%ld\nmethod=statscalc\nperiod=0\ndatetime=0(0-0)\nmarket=%@\ncodelist=%@\ndatatype=%@\ndataclass=updownlimit\nrettype=hqfile"),
                                v65,
                                v25,
                                v64,
                                v59);
                        v35 = objc_retainAutoreleasedReturnValue(v34);
                        v47 = v35;
                        v36 = _objc_msgSend(&OBJC_CLASS___HXSocketCenter, v50);
                        v66 = objc_retainAutoreleasedReturnValue(v36);
                        v37 = v35;
                        v22 = 10.0;
                        v38 = v62;
                        _objc_msgSend(v66, v51, v37, v62, v65, 10.0);
                        v39 = _objc_msgSend(v38, "reqInstanceArr");
                        v66 = objc_retainAutoreleasedReturnValue(v39);
                        v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, v52, v65);
                        v41 = objc_retainAutoreleasedReturnValue(v40);
                        _objc_msgSend(v66, v53, v41);
                        v30 = v63;
                      }
                    }
                  }
                }
              }
              ++v24;
            }
            while ( v24 < v27 );
          }
          while ( _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v43, v67, 16LL) );
        }
        v5 = v60;
        v17 = v55;
        v19 = v58;
      }
    }
  }
}

//----- (0000000100488EAD) ----------------------------------------------------
id __cdecl -[ZhangDieTingRefactorHQDataRequestModule getCodeListStrAndMarketListStr:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  unsigned __int64 i; // r14
  NSDictionary *v22; // r14
  NSDictionary *v26; // rax
  SEL v35; // [rsp+58h] [rbp-118h]
  SEL v38; // [rsp+70h] [rbp-100h]
  id obj; // [rsp+78h] [rbp-F8h]
  SEL v41; // [rsp+88h] [rbp-E8h]
  _QWORD v44[2]; // [rsp+A0h] [rbp-D0h] BYREF
  _QWORD v45[2]; // [rsp+B0h] [rbp-C0h] BYREF

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v5 = -[ZhangDieTingRefactorHQDataRequestModule getSameMarketStockCodeDic:](self, "getSameMarketStockCodeDic:", v3);
    v42 = objc_retainAutoreleasedReturnValue(v5);
    if ( v42 )
    {
      v36 = v3;
      v6 = _objc_msgSend(v42, "allKeys");
      v29 = 0LL;
      v30 = 0LL;
      v31 = 0LL;
      v32 = 0LL;
      obj = objc_retainAutoreleasedReturnValue(v6);
      v37 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v46, 16LL);
      if ( v37 )
      {
        v33 = *(_QWORD *)v30;
        v43 = &charsToLeaveEscaped;
        do
        {
          v35 = "thsArrayForKey:";
          v41 = "componentsJoinedByString:";
          v38 = "stringWithFormat:";
          for ( i = 0LL; i < (unsigned __int64)v37; ++i )
          {
            if ( *(_QWORD *)v30 != v33 )
              objc_enumerationMutation(obj);
            v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
            if ( (unsigned __int8)_objc_msgSend(v9, "isKindOfClass:", v8) )
            {
              _objc_msgSend(v10, "doubleValue");
              if ( 4294967295.0 != 0.0 )
              {
                _objc_msgSend(v11, "doubleValue");
                if ( 2147483648.0 != 0.0 )
                {
                  v13 = _objc_msgSend(v42, v35, v12);
                  v34 = objc_retainAutoreleasedReturnValue(v13);
                  v40 = &OBJC_CLASS___NSString;
                  v14 = _objc_msgSend(v34, v41, CFSTR(","));
                  v15 = objc_retainAutoreleasedReturnValue(v14);
                  v17 = _objc_msgSend(v40, v38, CFSTR("%@(%@);"), v16, v15);
                  objc_retainAutoreleasedReturnValue(v17);
                  v18 = v43;
                  v20 = _objc_msgSend(&OBJC_CLASS___NSString, v38, CFSTR("%@%@"), v43, v19);
                  v40 = objc_retainAutoreleasedReturnValue(v20);
                  v43 = v40;
                }
              }
            }
          }
          v37 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v46, 16LL);
        }
        while ( v37 );
      }
      else
      {
        v43 = &charsToLeaveEscaped;
        v41 = "componentsJoinedByString:";
      }
      v23 = obj;
      v24 = _objc_msgSend(obj, v41, CFSTR(","));
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v44[0] = CFSTR("MarketListStrKey");
      v45[0] = v25;
      v44[1] = CFSTR("CodeListStrKey");
      v45[1] = v43;
      v26 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v45, v44, 2LL);
      v22 = objc_retainAutoreleasedReturnValue(v26);
      v3 = v36;
    }
    else
    {
      v22 = 0LL;
    }
  }
  else
  {
    v22 = 0LL;
  }
  return objc_autoreleaseReturnValue(v22);
}

//----- (00000001004892CA) ----------------------------------------------------
id __cdecl -[ZhangDieTingRefactorHQDataRequestModule getSameMarketStockCodeDic:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v6; // r14
  unsigned int v16; // ebx
  SEL v34; // [rsp+48h] [rbp-138h]
  SEL v36; // [rsp+58h] [rbp-128h]
  SEL v37; // [rsp+60h] [rbp-120h]
  SEL v38; // [rsp+68h] [rbp-118h]
  SEL v39; // [rsp+70h] [rbp-110h]
  SEL v40; // [rsp+78h] [rbp-108h]
  SEL v41; // [rsp+80h] [rbp-100h]
  SEL v42; // [rsp+88h] [rbp-F8h]
  SEL v44; // [rsp+98h] [rbp-E8h]
  SEL v46; // [rsp+A8h] [rbp-D8h]
  unsigned int v47; // [rsp+B4h] [rbp-CCh]
  id obj; // [rsp+C0h] [rbp-C0h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
    v50 = objc_retainAutoreleasedReturnValue(v5);
    v29 = 0LL;
    v30 = 0LL;
    v31 = 0LL;
    v32 = 0LL;
    v43 = v3;
    obj = objc_retain(v3);
    v45 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v51, 16LL);
    if ( v45 )
    {
      v33 = *(_QWORD *)v30;
      do
      {
        v34 = "thsStringForKey:";
        v44 = "length";
        v36 = "sharedInstance";
        v37 = "convertToIntMarket:";
        v46 = "numberWithInt:";
        v38 = "thsArrayForKey:";
        v39 = "arrayWithArray:";
        v40 = "containsObject:";
        v42 = "addObject:";
        v41 = "setObject:forKey:";
        v6 = 0LL;
        do
        {
          if ( *(_QWORD *)v30 != v33 )
            objc_enumerationMutation(obj);
          v7 = *(void **)(*((_QWORD *)&v29 + 1) + 8 * v6);
          v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
          if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v8) )
          {
            v9 = v34;
            v10 = _objc_msgSend(v7, v34, CFSTR("StockCode"));
            objc_retainAutoreleasedReturnValue(v10);
            v11 = _objc_msgSend(v7, v9, CFSTR("Market"));
            v12 = objc_retainAutoreleasedReturnValue(v11);
            v48 = v13;
            if ( _objc_msgSend(v13, v44) && _objc_msgSend(v12, v44) )
            {
              v14 = _objc_msgSend(&OBJC_CLASS___MarketDataManager, v36);
              v15 = objc_retainAutoreleasedReturnValue(v14);
              v35 = v12;
              v16 = (unsigned int)_objc_msgSend(v15, v37, v12);
              v47 = v16;
              v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, v46, v16);
              v18 = objc_retainAutoreleasedReturnValue(v17);
              v19 = _objc_msgSend(v50, v38, v18);
              v20 = objc_retainAutoreleasedReturnValue(v19);
              v21 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, v39, v20);
              v22 = objc_retainAutoreleasedReturnValue(v21);
              if ( !(unsigned __int8)_objc_msgSend(v22, v40, v48) )
                _objc_msgSend(v22, v42, v48);
              if ( _objc_msgSend(v22, "count") )
              {
                v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, v46, v47);
                v26 = objc_retainAutoreleasedReturnValue(v25);
                _objc_msgSend(v50, v41, v22, v26);
                v27(v26);
              }
              v24(v22);
              v12 = v35;
            }
          }
          ++v6;
        }
        while ( v6 < (unsigned __int64)v45 );
        v45 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v51, 16LL);
      }
      while ( v45 );
    }
    v3 = v43;
  }
  else
  {
    v50 = 0LL;
  }
  return objc_autoreleaseReturnValue(v50);
}

//----- (0000000100489721) ----------------------------------------------------
id __cdecl -[ZhangDieTingRefactorHQDataRequestModule formatStockCodeAndMarket:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v6; // r15
  unsigned int v16; // r14d
  SEL v36; // [rsp+50h] [rbp-150h]
  SEL v40; // [rsp+70h] [rbp-130h]
  SEL v41; // [rsp+78h] [rbp-128h]
  SEL v42; // [rsp+80h] [rbp-120h]
  SEL v43; // [rsp+88h] [rbp-118h]
  SEL v44; // [rsp+90h] [rbp-110h]
  SEL v45; // [rsp+98h] [rbp-108h]
  SEL v46; // [rsp+A0h] [rbp-100h]
  SEL v47; // [rsp+A8h] [rbp-F8h]
  SEL v48; // [rsp+B0h] [rbp-F0h]
  SEL v49; // [rsp+B8h] [rbp-E8h]
  SEL v52; // [rsp+D0h] [rbp-D0h]
  id obj; // [rsp+D8h] [rbp-C8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v55 = objc_retainAutoreleasedReturnValue(v5);
    v31 = 0LL;
    v32 = 0LL;
    v33 = 0LL;
    v34 = 0LL;
    v50 = v3;
    obj = objc_retain(v3);
    v51 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v56, 16LL);
    if ( v51 )
    {
      v35 = *(_QWORD *)v32;
      do
      {
        v52 = "numberWithInt:";
        v36 = "thsStringForKey:";
        v40 = "length";
        v41 = "dataUsingEncoding:";
        v42 = "bytes";
        v43 = "sharedInstance";
        v44 = "convertToStringMarket:";
        v45 = "substringFromIndex:";
        v46 = "stringByAppendingString:";
        v47 = "mutableCopy";
        v48 = "setObject:forKey:";
        v49 = "addObject:";
        v6 = 0LL;
        do
        {
          if ( *(_QWORD *)v32 != v35 )
            objc_enumerationMutation(obj);
          v7 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
          if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v7) )
          {
            v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, v52, 5LL);
            v10 = objc_retainAutoreleasedReturnValue(v9);
            v12 = _objc_msgSend(v11, v36, v10);
            v13 = objc_retainAutoreleasedReturnValue(v12);
            v54 = v13;
            if ( v13 && _objc_msgSend(v54, v40) )
            {
              v14 = _objc_msgSend(v54, v41, 4LL);
              v15 = objc_retainAutoreleasedReturnValue(v14);
              v37 = objc_retainAutorelease(v15);
              v16 = *(unsigned __int8 *)_objc_msgSend(v37, v42);
              v17 = _objc_msgSend(&OBJC_CLASS___MarketDataManager, v43);
              v18 = objc_retainAutoreleasedReturnValue(v17);
              v19 = _objc_msgSend(v18, v44, v16);
              v20 = objc_retainAutoreleasedReturnValue(v19);
              v38 = v20;
              v21 = _objc_msgSend(v54, v45, 1LL);
              v39 = objc_retainAutoreleasedReturnValue(v21);
              v22 = _objc_msgSend(v20, v46, v39);
              v23 = objc_retainAutoreleasedReturnValue(v22);
              _objc_msgSend(v24, v47);
              v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, v52, 5LL);
              v26 = objc_retainAutoreleasedReturnValue(v25);
              _objc_msgSend(v27, v48, v23, v26);
              _objc_msgSend(v55, v49, v28);
            }
          }
          ++v6;
        }
        while ( v6 < (unsigned __int64)v51 );
        v51 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v31, v56, 16LL);
      }
      while ( v51 );
    }
    v3 = v50;
  }
  else
  {
    v55 = 0LL;
  }
  return objc_autoreleaseReturnValue(v55);
}

//----- (0000000100489BAF) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule handlerWithReceiveData:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // rbx
  NSMutableArray *v7; // rax
  NSMutableArray *v8; // rbx
  SEL v9; // r12
  NSMutableArray *v15; // rax
  NSMutableArray *v16; // rax
  NSMutableArray *v19; // rax
  NSMutableArray *v20; // rbx
  NSMutableArray *v22; // rax
  NSMutableArray *v23; // rbx
  HXSocketCenter *v25; // rax

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v5 = -[ZhangDieTingRefactorHQDataRequestModule compeletDetailDataArr](self, "compeletDetailDataArr");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "addObjectsFromArray:", v3);
  }
  v7 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( _objc_msgSend(v8, v9) )
    goto LABEL_7;
  v10 = -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](self, "reqCallBack");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  if ( v12 )
  {
    v13 = -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](self, "reqCallBack");
    v14 = (void (__fastcall **)(id, id, id))objc_retainAutoreleasedReturnValue(v13);
    v15 = -[ZhangDieTingRefactorHQDataRequestModule compeletDetailDataArr](self, "compeletDetailDataArr");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v14[2](v14, v16, __NSDictionary0__);
    v18(v14);
    v19 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v20, "removeAllObjects");
    v21(v20);
    v22 = -[ZhangDieTingRefactorHQDataRequestModule compeletDetailDataArr](self, "compeletDetailDataArr");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    _objc_msgSend(v23, "removeAllObjects");
    v24(v23);
    v25 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
    v8 = objc_retainAutoreleasedReturnValue(v25);
    _objc_msgSend(v8, "removeObjectFromMapTable:", self);
LABEL_7:
  }
}

//----- (0000000100489DA8) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule receiveStuffData:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  NSMutableArray *v10; // rax
  NSNumber *v12; // rax
  NSNumber *v13; // rbx
  NSMutableArray *v18; // rax
  NSMutableArray *v19; // r15
  NSNumber *v21; // rax
  NSNumber *v22; // rbx
  HXSocketCenter *v32; // rax
  HXSocketCenter *v33; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___HQFileModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
  {
    v6 = objc_retain(v5);
    v7 = _objc_msgSend(v6, "dataArr");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = -[ZhangDieTingRefactorHQDataRequestModule formatStockCodeAndMarket:](self, "formatStockCodeAndMarket:", v8);
    v35 = objc_retainAutoreleasedReturnValue(v9);
    if ( (unsigned __int8)-[ZhangDieTingRefactorHQDataRequestModule isRequestZhuangTaiData](
                            self,
                            "isRequestZhuangTaiData") )
    {
      v10 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
      objc_retainAutoreleasedReturnValue(v10);
      v11 = _objc_msgSend(v6, "instanceId");
      v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v11);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v15 = v6;
      v16 = (unsigned __int8)_objc_msgSend(v14, "containsObject:", v13);
      if ( v16 )
      {
        v18 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v20 = _objc_msgSend(v15, "instanceId");
        v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v20);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        _objc_msgSend(v19, "removeObject:", v22);
        -[ZhangDieTingRefactorHQDataRequestModule handlerWithReceiveData:](self, "handlerWithReceiveData:", v35);
      }
    }
    else
    {
      v23 = _objc_msgSend(v6, "instanceId");
      v15 = v6;
      if ( v23 == -[ZhangDieTingRefactorHQDataRequestModule reqInstance](self, "reqInstance") )
      {
        v34 = v6;
        v24 = -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](self, "reqCallBack");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        if ( v25 )
        {
          v27 = _objc_msgSend(self, v26);
          v28 = (void (__fastcall **)(id, id, id))objc_retainAutoreleasedReturnValue(v27);
          v29 = _objc_msgSend(v34, "extendDict");
          v30 = objc_retainAutoreleasedReturnValue(v29);
          v28[2](v28, v35, v30);
          v31(v28);
        }
        v32 = (HXSocketCenter *)+[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
        v33 = objc_retainAutoreleasedReturnValue(v32);
        -[HXSocketCenter removeObjectFromMapTable:](v33, "removeObjectFromMapTable:", self);
        v15 = v34;
      }
    }
  }
}

//----- (000000010048A07C) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule failToReceiveStuffData:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        signed __int64 a3)
{
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // rbx
  id (*v6)(id, SEL, ...); // r12
  NSMutableArray *v11; // rax
  NSMutableArray *v12; // r15
  id (*v13)(id, SEL, ...); // r12
  HXSocketCenter *v22; // rax
  HXSocketCenter *v23; // rbx

  if ( (unsigned __int8)-[ZhangDieTingRefactorHQDataRequestModule isRequestZhuangTaiData](
                          self,
                          "isRequestZhuangTaiData") )
  {
    v4 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = v6(&OBJC_CLASS___NSNumber, "numberWithLong:", a3);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v5, "containsObject:", v8);
    if ( v10 )
    {
      v11 = -[ZhangDieTingRefactorHQDataRequestModule reqInstanceArr](self, "reqInstanceArr");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = v13(&OBJC_CLASS___NSNumber, "numberWithLong:", a3);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16(v12, "removeObject:", v15);
      v17(self, "handlerWithReceiveData:", 0LL);
    }
  }
  else if ( -[ZhangDieTingRefactorHQDataRequestModule reqInstance](self, "reqInstance") == (id)a3 )
  {
    v18 = -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](self, "reqCallBack");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    if ( v19 )
    {
      v20 = -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](self, "reqCallBack");
      v21 = (void (__fastcall **)(id, id, id))objc_retainAutoreleasedReturnValue(v20);
      v21[2](v21, __NSArray0__, __NSDictionary0__);
    }
    v22 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    -[HXSocketCenter removeObjectFromMapTable:](v23, "removeObjectFromMapTable:", self);
  }
}

//----- (000000010048A270) ----------------------------------------------------
id __cdecl -[ZhangDieTingRefactorHQDataRequestModule reqCallBack](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  return objc_getProperty(self, a2, 16LL, 0);
}

//----- (000000010048A281) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule setReqCallBack:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (000000010048A290) ----------------------------------------------------
signed __int64 __cdecl -[ZhangDieTingRefactorHQDataRequestModule reqInstance](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  return self->_reqInstance;
}

//----- (000000010048A29A) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule setReqInstance:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        signed __int64 a3)
{
  self->_reqInstance = a3;
}

//----- (000000010048A2A4) ----------------------------------------------------
char __cdecl -[ZhangDieTingRefactorHQDataRequestModule isRequestZhuangTaiData](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  return self->_isRequestZhuangTaiData;
}

//----- (000000010048A2AE) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule setIsRequestZhuangTaiData:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        char a3)
{
  self->_isRequestZhuangTaiData = a3;
}

//----- (000000010048A2B7) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule setReqInstanceArr:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_reqInstanceArr, a3);
}

//----- (000000010048A2C8) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule setCompeletDetailDataArr:](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_compeletDetailDataArr, a3);
}

//----- (000000010048A2D9) ----------------------------------------------------
void __cdecl -[ZhangDieTingRefactorHQDataRequestModule .cxx_destruct](
        ZhangDieTingRefactorHQDataRequestModule *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->_compeletDetailDataArr, 0LL);
  objc_storeStrong((id *)&self->_reqInstanceArr, 0LL);
  objc_storeStrong(&self->_reqCallBack, 0LL);
}

//----- (000000010048A30C) ----------------------------------------------------
id __fastcall sub_10048A30C(void *a1)
{
  unsigned __int64 v2; // rax
  unsigned __int64 i; // r15
  NSString *v7; // rax
  NSString *v9; // rax
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSDictionary *v12; // rax
  NSDictionary *v13; // rax
  NSString *v16; // [rsp+0h] [rbp-C0h]
  NSString *v17; // [rsp+8h] [rbp-B8h]
  unsigned __int64 v18; // [rsp+10h] [rbp-B0h]
  unsigned __int64 v19; // [rsp+38h] [rbp-88h]
  objc_method_description *v21; // [rsp+48h] [rbp-78h]
  Protocol *p; // [rsp+50h] [rbp-70h]
  unsigned int outCount; // [rsp+5Ch] [rbp-64h] BYREF

  p = objc_retain(a1);
  v1 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v20 = _objc_msgSend(v1, "init");
  v2 = 0LL;
  do
  {
    outCount = 0;
    v19 = v2;
    v18 = v2 >> 1;
    v21 = protocol_copyMethodDescriptionList(p, v2 >> 1, v2 & 1, &outCount);
    if ( outCount )
    {
      p_types = &v21->types;
      for ( i = 0LL; i < outCount; ++i )
      {
        v5 = *(p_types - 1);
        v6 = *p_types;
        v24[0] = (__int64)CFSTR("methodName");
        v7 = NSStringFromSelector(v5);
        v16 = objc_retainAutoreleasedReturnValue(v7);
        v25[0] = (__int64)v16;
        v24[1] = v8;
        v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithUTF8String:", v6);
        v17 = objc_retainAutoreleasedReturnValue(v9);
        v25[1] = (__int64)v17;
        v24[2] = (__int64)CFSTR("isRequired");
        v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", (unsigned int)(char)v18);
        v11 = objc_retainAutoreleasedReturnValue(v10);
        v25[2] = (__int64)v11;
        v12 = (NSDictionary *)_objc_msgSend(
                                &OBJC_CLASS___NSDictionary,
                                "dictionaryWithObjects:forKeys:count:",
                                v25,
                                v24,
                                3LL);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        _objc_msgSend(v20, "addObject:", v13);
        p_types += 2;
      }
    }
    free(v21);
    v2 = v19 + 1;
  }
  while ( v19 != 3 );
  return objc_autoreleaseReturnValue(v20);
}

//----- (000000010048A54D) ----------------------------------------------------
NSString *__fastcall sub_10048A54D(SEL aSelector)
{
  return NSStringFromSelector(aSelector);
}

//----- (000000010048A557) ----------------------------------------------------
id __fastcall sub_10048A557(void *a1)
{
  unsigned __int64 v4; // rbx
  id obj; // [rsp+68h] [rbp-B8h]

  v1 = objc_retain(a1);
  v2 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v16 = _objc_msgSend(v2, "init");
  v9 = 0LL;
  v10 = 0LL;
  v11 = 0LL;
  v12 = 0LL;
  v14 = v1;
  v3 = sub_10048A30C(v1);
  obj = objc_retainAutoreleasedReturnValue(v3);
  v15 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v9, v18, 16LL);
  if ( v15 )
  {
    v13 = *(_QWORD *)v10;
    do
    {
      v4 = 0LL;
      do
      {
        if ( *(_QWORD *)v10 != v13 )
          objc_enumerationMutation(obj);
        v5 = _objc_msgSend(*(id *)(*((_QWORD *)&v9 + 1) + 8 * v4), "objectForKeyedSubscript:", CFSTR("methodName"));
        v6 = objc_retainAutoreleasedReturnValue(v5);
        _objc_msgSend(v16, "addObject:", v6);
        ++v4;
      }
      while ( v4 < (unsigned __int64)v15 );
      v15 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v9, v18, 16LL);
    }
    while ( v15 );
  }
  return objc_autoreleaseReturnValue(v16);
}

//----- (000000010048A727) ----------------------------------------------------
SEL __fastcall sub_10048A727(Protocol *p, SEL aSel)
{
  objc_method_description MethodDescription; // rax

  v2 = 0LL;
  do
  {
    MethodDescription = protocol_getMethodDescription(p, aSel, (unsigned int)v2 >> 1, v2 & 1);
    if ( MethodDescription.name != 0LL && MethodDescription.types != 0LL )
      break;
  }
  while ( v2++ != 3 );
  return MethodDescription.name;
}

