<PERSON><PERSON><PERSON><PERSON>ouShiContainerController *__cdecl -[<PERSON><PERSON><PERSON><PERSON><PERSON>ShiContainerController initWithNibName:bundle:](
        <PERSON><PERSON><PERSON><PERSON><PERSON>ShiContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  <PERSON><PERSON><PERSON>ZouShiContainerController *v4; // rax
  <PERSON>GuZouShiContainerController *v5; // rbx

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
  v4 = -[HXBaseViewController initWithNibName:bundle:](&v7, "initWithNibName:bundle:", a3, a4);
  v5 = v4;
  if ( v4 )
  {
    -[FenshiKlineTrendChartContainerController setHeadTitleMode:](v4, "setHeadTitleMode:", 2LL);
    -[FenshiKlineTrendChartContainerController setTitleName:](v5, "setTitleName:", CFSTR("正股走势图"));
  }
  return v5;
}

//----- (00000001002D5843) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController viewDidLoad](ZhengGuZouShiContainerController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
  -[FenshiKlineTrendChartContainerController viewDidLoad](&v2, "viewDidLoad");
  -[ZhengGuZouShiContainerController setViewStates](self, "setViewStates");
  -[ZhengGuZouShiContainerController addNotifications](self, "addNotifications");
}

//----- (00000001002D5899) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController viewDidDisappear](ZhengGuZouShiContainerController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
  -[HXBaseViewController viewDidDisappear](&v2, "viewDidDisappear");
  -[ZhengGuZouShiContainerController deleteOrderForHangQing](self, "deleteOrderForHangQing");
}

//----- (00000001002D58DA) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController dealloc](ZhengGuZouShiContainerController *self, SEL a2)
{

  -[ZhengGuZouShiContainerController deleteOrderForHangQing](self, "deleteOrderForHangQing");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
  -[PanKouModularBaseViewController dealloc](&v4, "dealloc");
}

//----- (00000001002D5959) ----------------------------------------------------
QuoteBaseItem *__cdecl -[ZhengGuZouShiContainerController quoteItem](ZhengGuZouShiContainerController *self, SEL a2)
{
  NSString *correlatedCode; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  correlatedCode = self->super.super._correlatedCode;
  if ( !correlatedCode )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___QuoteBaseItem);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super.super._correlatedCode;
    self->super.super._correlatedCode = v5;
    correlatedCode = self->super.super._correlatedCode;
  }
  return (QuoteBaseItem *)objc_retainAutoreleaseReturnValue(correlatedCode);
}

//----- (00000001002D59AA) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController requestForModularData:market:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  NSString *v11; // rax
  SEL v12; // r12
  NSString *v13; // rax
  NSString *v14; // rbx
  SEL v15; // r12
  NSString *v17; // rax
  NSString *v18; // rax
  NSString *v19; // rbx
  WoLunNiuXiongAccessoryManager *v24; // rax
  WoLunNiuXiongAccessoryManager *v25; // rbx
  id *v26; // r12
  _QWORD v27[4]; // [rsp+8h] [rbp-78h] BYREF
  id to; // [rsp+28h] [rbp-58h] BYREF
  id location; // [rsp+40h] [rbp-40h] BYREF

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = v7;
  if ( v7 )
  {
    v9 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
    if ( v5 )
    {
      if ( !v9 && !(unsigned __int8)_objc_msgSend(v5, v10, &charsToLeaveEscaped) )
      {
        v11 = -[ZhengGuZouShiContainerController singleStockCode](self, "singleStockCode");
        v31 = objc_retainAutoreleasedReturnValue(v11);
        if ( (unsigned __int8)_objc_msgSend(v5, v12, v31) )
        {
          v13 = -[ZhengGuZouShiContainerController singleMarket](self, "singleMarket");
          v14 = objc_retainAutoreleasedReturnValue(v13);
          v32 = (unsigned __int8)_objc_msgSend(v8, v15, v14);
          v16(v31);
          if ( v32 )
          {
            v17 = -[ZhengGuZouShiContainerController dapanStockCode](self, "dapanStockCode");
            objc_retainAutoreleasedReturnValue(v17);
            v18 = -[ZhengGuZouShiContainerController dapanMarket](self, "dapanMarket");
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v29.receiver = self;
            v29.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
            -[FenshiKlineTrendChartContainerController requestForModularData:market:](
              &v29,
              "requestForModularData:market:",
              v20,
              v19);
            goto LABEL_10;
          }
        }
        else
        {
        }
        objc_initWeak(&location, self);
        -[ZhengGuZouShiContainerController setSingleStockCode:](self, "setSingleStockCode:", v5);
        -[ZhengGuZouShiContainerController setSingleMarket:](self, "setSingleMarket:", v8);
        v22 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
                &OBJC_CLASS___HXTools,
                "getMarketAndCodeByAppendingMarket:andStockCode:",
                v8,
                v5);
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v24 = +[WoLunNiuXiongAccessoryManager sharedInstance](
                &OBJC_CLASS___WoLunNiuXiongAccessoryManager,
                "sharedInstance");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v27[0] = _NSConcreteStackBlock;
        v27[1] = 3254779904LL;
        v27[2] = sub_1002D5C4D;
        v27[3] = &unk_1012DC1F0;
        objc_copyWeak(&to, &location);
        v31 = v23;
        -[WoLunNiuXiongAccessoryManager getRelateHKMarketAndCode:callBack:](
          v25,
          "getRelateHKMarketAndCode:callBack:",
          v23,
          v27);
        objc_destroyWeak(v26);
        objc_destroyWeak(&location);
      }
    }
  }
LABEL_10:
}

//----- (00000001002D5C4D) ----------------------------------------------------
void __fastcall sub_1002D5C4D(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "sendZhengGuRequset:", v2);
}

//----- (00000001002D5CA7) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController sendZhengGuRequset:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  NSString *v8; // rax
  NSString *v9; // rbx
  NSString *v10; // rax
  NSString *v11; // rax

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "getCodeString:", v3);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v3);
  objc_retainAutoreleasedReturnValue(v7);
  v8 = -[ZhengGuZouShiContainerController dapanStockCode](self, "dapanStockCode");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v20 = self;
  if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", v9) )
  {
    v10 = -[ZhengGuZouShiContainerController dapanMarket](self, "dapanMarket");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v21 = v6;
    v13 = (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", v11);
    if ( v13 )
      goto LABEL_7;
  }
  else
  {
  }
  if ( !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", &charsToLeaveEscaped) )
  {
    _objc_msgSend(v20, "setDapanStockCode:", v6);
    _objc_msgSend(v20, "setDapanMarket:", v16);
    _objc_msgSend(v20, "requestForHangQingData:market:", v6, v17);
    v19.receiver = v20;
    v19.super_class = (Class)&OBJC_CLASS___ZhengGuZouShiContainerController;
    -[FenshiKlineTrendChartContainerController requestForModularData:market:](
      &v19,
      "requestForModularData:market:",
      v6,
      v18);
  }
LABEL_7:
}

//----- (00000001002D5E4A) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setSubclassModularMode](ZhengGuZouShiContainerController *self, SEL a2)
{
  -[FenshiKlineTrendChartContainerController setFenshiKlineModularMode:](self, "setFenshiKlineModularMode:", 1LL);
}

//----- (00000001002D5E61) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController requestForHangQingData:market:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  QuoteBaseItem *v10; // rax
  id (*v11)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  QuoteBaseItem *v14; // rax
  QuoteBaseItem *v15; // r14
  id (*v24)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  QuoteBaseItem *v35; // rax
  NSNumber *v38; // rax
  NSNumber *v39; // rax
  DataRequestCenter *v45; // rax
  DataRequestCenter *v46; // rax
  DataRequestCenter *v47; // r15
  id *v48; // r12
  _QWORD v49[4]; // [rsp+28h] [rbp-A8h] BYREF
  _QWORD v51[4]; // [rsp+50h] [rbp-80h] BYREF
  id to; // [rsp+70h] [rbp-60h] BYREF
  id location; // [rsp+90h] [rbp-40h] BYREF

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = v7;
  if ( !v7 )
    goto LABEL_13;
  v9 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
  if ( !v5 || v9 || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", &charsToLeaveEscaped) )
    goto LABEL_13;
  v10 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v57 = objc_retainAutoreleasedReturnValue(v10);
  v12 = v11(v57, "stockCode");
  v58 = objc_retainAutoreleasedReturnValue(v12);
  v53 = v5;
  if ( !(unsigned __int8)v13(v5, "isEqualToString:", v58) )
  {
LABEL_9:
    -[ZhengGuZouShiContainerController deleteOrderForHangQing](self, "deleteOrderForHangQing");
    goto LABEL_10;
  }
  v14 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = (void *)v16(v15, "market");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  LOBYTE(v55) = (unsigned __int8)_objc_msgSend(v8, "isEqualToString:", v18);
  if ( !(_BYTE)v55 )
    goto LABEL_9;
LABEL_10:
  v21 = _objc_msgSend(self, v20);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23(v22, "initializeBaseValues");
  v25 = v24(self, "quoteItem");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v5 = v53;
  v27(v26, "setStockCode:", v53);
  v29 = v28(self, "quoteItem");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v31(v30, "setMarket:", v8);
  objc_initWeak(&location, self);
  v57 = &OBJC_CLASS___NSDictionary;
  v33 = v32(self, "quoteItem");
  v55 = objc_retainAutoreleasedReturnValue(v33);
  v34 = _objc_msgSend(v55, "stockCode");
  v58 = objc_retainAutoreleasedReturnValue(v34);
  v35 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v54 = objc_retainAutoreleasedReturnValue(v35);
  v36 = _objc_msgSend(v54, "market");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 200LL);
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v40 = _objc_msgSend(
          v57,
          "dictionaryWithObjectsAndKeys:",
          v58,
          CFSTR("CodeList"),
          v37,
          CFSTR("Market"),
          CFSTR("5,6,7,8,9,10"),
          CFSTR("DataTypes"),
          v39,
          CFSTR("DataRequest_Type"),
          0LL);
  v57 = objc_retainAutoreleasedReturnValue(v40);
  v42 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v43 = objc_retain(v42);
  v44 = v43;
  if ( v57 )
    _objc_msgSend(v43, "addObject:", v57);
  v45 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
  v46 = objc_retainAutoreleasedReturnValue(v45);
  v58 = v44;
  v47 = v46;
  v51[0] = _NSConcreteStackBlock;
  v51[1] = 3254779904LL;
  v51[2] = sub_1002D6317;
  v51[3] = &unk_1012DAA70;
  objc_copyWeak(&to, &location);
  v49[0] = _NSConcreteStackBlock;
  v49[1] = 3254779904LL;
  v49[2] = sub_1002D63D6;
  v49[3] = &unk_1012DAF38;
  objc_copyWeak(&v50, &location);
  -[DataRequestCenter request:type:params:callBack:fail:](
    v47,
    "request:type:params:callBack:fail:",
    2LL,
    1LL,
    v58,
    v51,
    v49);
  objc_destroyWeak(v48);
  objc_destroyWeak(&to);
  objc_destroyWeak(&location);
LABEL_13:
}

//----- (00000001002D6317) ----------------------------------------------------
void __fastcall sub_1002D6317(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    objc_retain(v2);
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "updateForRequest:", v5);
    v7 = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(v7, "orderForHangQing");
  }
}

//----- (00000001002D63D6) ----------------------------------------------------
void __fastcall sub_1002D63D6(__int64 a1)
{
  id WeakRetained; // r15
  id *v4; // r12
  id *v8; // r12

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = _objc_msgSend(WeakRetained, "quoteItem");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "initializeBaseValues");
  v5 = objc_loadWeakRetained(v4);
  v6 = _objc_msgSend(v5, "zhengGuHangQingView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = objc_loadWeakRetained(v8);
  v10 = _objc_msgSend(v9, "quoteItem");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v7, "updateHangQing:", v11);
}

//----- (00000001002D64BA) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController orderForHangQing](ZhengGuZouShiContainerController *self, SEL a2)
{
  QuoteBaseItem *v2; // rax
  QuoteBaseItem *v3; // r15
  NSString *v4; // rax
  QuoteBaseItem *v5; // rax
  QuoteBaseItem *v6; // r15
  NSString *v7; // rax
  NSString *v8; // rax
  QuoteBaseItem *v9; // rax
  QuoteBaseItem *v10; // r15
  NSString *v11; // rax
  NSString *v12; // r13
  QuoteBaseItem *v13; // rdi
  QuoteBaseItem *v15; // rax
  NSString *v16; // rax
  NSString *v17; // rbx
  SubscribeDataPool *v19; // rax
  id (*v20)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  QuoteBaseItem *v49; // [rsp+28h] [rbp-78h]
  QuoteBaseItem *v51; // [rsp+30h] [rbp-70h]
  NSString *v53; // [rsp+38h] [rbp-68h]
  QuoteBaseItem *v55; // [rsp+40h] [rbp-60h]
  SubscribeDataPool *v56; // [rsp+40h] [rbp-60h]
  _QWORD v57[4]; // [rsp+48h] [rbp-58h] BYREF

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    v2 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = -[QuoteBaseItem stockCode](v3, "stockCode");
    if ( !objc_retainAutoreleasedReturnValue(v4) )
    {
      return;
    }
    v55 = v3;
    v5 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = -[QuoteBaseItem market](v6, "market");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( !v8 )
    {
      v13 = v6;
      goto LABEL_8;
    }
    v53 = v8;
    v51 = v6;
    v9 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = -[QuoteBaseItem stockCode](v10, "stockCode");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    if ( (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", &charsToLeaveEscaped) )
    {
      v13 = v51;
LABEL_8:
      return;
    }
    v15 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v49 = objc_retainAutoreleasedReturnValue(v15);
    v16 = -[QuoteBaseItem market](v49, "market");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v46 = (unsigned __int8)_objc_msgSend(v17, "isEqualToString:", &charsToLeaveEscaped);
    if ( !v46 )
    {
      v19 = +[SubscribeDataPool sharedInstance](&OBJC_CLASS___SubscribeDataPool, "sharedInstance");
      v56 = objc_retainAutoreleasedReturnValue(v19);
      v21 = v20(self, "quoteItem");
      v52 = objc_retainAutoreleasedReturnValue(v21);
      v23 = v22(v52, "stockCode");
      v54 = objc_retainAutoreleasedReturnValue(v23);
      v58 = v54;
      v25 = v24(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v58, 1LL);
      v48 = objc_retainAutoreleasedReturnValue(v25);
      v27 = v26(self, "quoteItem");
      v47 = objc_retainAutoreleasedReturnValue(v27);
      v29 = v28(v47, "market");
      v43 = objc_retainAutoreleasedReturnValue(v29);
      v31 = v30(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v50 = objc_retainAutoreleasedReturnValue(v31);
      v57[0] = v50;
      v33 = v32(&OBJC_CLASS___NSNumber, "numberWithInt:", 8LL);
      v44 = objc_retainAutoreleasedReturnValue(v33);
      v57[1] = v44;
      v35 = v34(&OBJC_CLASS___NSNumber, "numberWithInt:", 9LL);
      v45 = objc_retainAutoreleasedReturnValue(v35);
      v57[2] = v45;
      v37 = v36(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v57[3] = v38;
      v40 = v39(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v57, 4LL);
      v41 = objc_retainAutoreleasedReturnValue(v40);
      -[SubscribeDataPool subscribeCode:market:dataType:delegate:](
        v56,
        "subscribeCode:market:dataType:delegate:",
        v48,
        v43,
        v41,
        self);
    }
  }
}

//----- (00000001002D68EA) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController deleteOrderForHangQing](ZhengGuZouShiContainerController *self, SEL a2)
{
  QuoteBaseItem *v2; // rax
  QuoteBaseItem *v3; // r13
  NSString *v4; // rax
  QuoteBaseItem *v5; // rax
  QuoteBaseItem *v6; // r14
  NSString *v7; // rax
  NSString *v8; // rax
  QuoteBaseItem *v9; // rax
  QuoteBaseItem *v10; // r14
  NSString *v11; // rax
  NSString *v12; // r15
  QuoteBaseItem *v13; // rdi
  QuoteBaseItem *v15; // rax
  NSString *v16; // rax
  NSString *v17; // r14
  SubscribeDataPool *v19; // rax
  QuoteBaseItem *v20; // rax
  NSString *v21; // rax
  NSArray *v22; // rax
  NSArray *v23; // r13
  QuoteBaseItem *v24; // rax
  QuoteBaseItem *v25; // rax
  NSString *v26; // rax
  NSString *v27; // r14
  QuoteBaseItem *v29; // [rsp+8h] [rbp-68h]
  QuoteBaseItem *v30; // [rsp+10h] [rbp-60h]
  NSString *v32; // [rsp+20h] [rbp-50h]
  QuoteBaseItem *v33; // [rsp+28h] [rbp-48h]
  QuoteBaseItem *v34; // [rsp+28h] [rbp-48h]
  NSString *v35; // [rsp+30h] [rbp-40h]
  SubscribeDataPool *v36; // [rsp+30h] [rbp-40h]
  NSString *v37; // [rsp+38h] [rbp-38h] BYREF

  v2 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[QuoteBaseItem stockCode](v3, "stockCode");
  if ( !objc_retainAutoreleasedReturnValue(v4) )
  {
    return;
  }
  v5 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[QuoteBaseItem market](v6, "market");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( !v8 )
  {
    v13 = v6;
    goto LABEL_7;
  }
  v35 = v8;
  v33 = v6;
  v9 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = -[QuoteBaseItem stockCode](v10, "stockCode");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  if ( (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v13 = v33;
LABEL_7:
    return;
  }
  v29 = v10;
  v15 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
  v30 = objc_retainAutoreleasedReturnValue(v15);
  v16 = -[QuoteBaseItem market](v30, "market");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v31 = (unsigned __int8)_objc_msgSend(v17, "isEqualToString:", &charsToLeaveEscaped);
  if ( !v31 )
  {
    v19 = +[SubscribeDataPool sharedInstance](&OBJC_CLASS___SubscribeDataPool, "sharedInstance");
    v36 = objc_retainAutoreleasedReturnValue(v19);
    v20 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v34 = objc_retainAutoreleasedReturnValue(v20);
    v21 = -[QuoteBaseItem stockCode](v34, "stockCode");
    v32 = objc_retainAutoreleasedReturnValue(v21);
    v37 = v32;
    v22 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v37, 1LL);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v24 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = -[QuoteBaseItem market](v25, "market");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    -[SubscribeDataPool disSubscribeCode:market:delegate:](v36, "disSubscribeCode:market:delegate:", v23, v27, self);
  }
}

//----- (00000001002D6C05) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController receiveRealTimeData:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  -[ZhengGuZouShiContainerController updateForPush:](self, "updateForPush:", a3);
}

//----- (00000001002D6C17) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController updateForRequest:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 i; // r14
  ZhengGuZouShiContainerController *v30; // [rsp+80h] [rbp-D0h]
  id obj; // [rsp+98h] [rbp-B8h]

  v30 = self;
  v19 = 0LL;
  v20 = 0LL;
  v21 = 0LL;
  v22 = 0LL;
  v3 = _objc_msgSend(a3, "arrBody");
  obj = objc_retainAutoreleasedReturnValue(v3);
  v31 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v34, 16LL);
  if ( v31 )
  {
    v25 = *(_QWORD *)v20;
    do
    {
      v23 = "class";
      v24 = "isKindOfClass:";
      v32 = "quoteItem";
      v26 = "setConstantProperties:";
      v27 = "resetBaseValues:";
      v28 = "zhengGuHangQingView";
      v29 = "updateHangQing:";
      for ( i = 0LL; i < (unsigned __int64)v31; ++i )
      {
        if ( *(_QWORD *)v20 != v25 )
          objc_enumerationMutation(obj);
        v5 = *(void **)(*((_QWORD *)&v19 + 1) + 8 * i);
        v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v23);
        if ( (unsigned __int8)_objc_msgSend(v5, v24, v6) )
        {
          v8 = _objc_msgSend(v7, v32);
          v9 = objc_retainAutoreleasedReturnValue(v8);
          _objc_msgSend(v9, v26, v5);
          v11 = _objc_msgSend(v10, v32);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          _objc_msgSend(v12, v27, v5);
          v14 = _objc_msgSend(v13, v28);
          v15 = objc_retainAutoreleasedReturnValue(v14);
          v17 = _objc_msgSend(v16, v32);
          v18 = objc_retainAutoreleasedReturnValue(v17);
          _objc_msgSend(v15, v29, v18);
        }
      }
      v31 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v34, 16LL);
    }
    while ( v31 );
  }
}

//----- (00000001002D6EB4) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController updateForPush:](ZhengGuZouShiContainerController *self, SEL a2, id a3)
{
  unsigned __int64 v3; // r14
  SEL v19; // [rsp+40h] [rbp-100h]
  SEL v20; // [rsp+48h] [rbp-F8h]
  SEL v22; // [rsp+58h] [rbp-E8h]
  SEL v23; // [rsp+60h] [rbp-E0h]
  SEL v24; // [rsp+68h] [rbp-D8h]
  SEL v26; // [rsp+78h] [rbp-C8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v25 = self;
  v15 = 0LL;
  v16 = 0LL;
  v17 = 0LL;
  v18 = 0LL;
  obj = objc_retain(a3);
  v27 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v29, 16LL);
  if ( v27 )
  {
    v21 = *(_QWORD *)v16;
    do
    {
      v19 = "class";
      v20 = "isKindOfClass:";
      v26 = "quoteItem";
      v22 = "resetBaseValues:";
      v23 = "zhengGuHangQingView";
      v24 = "updateHangQing:";
      v3 = 0LL;
      do
      {
        if ( *(_QWORD *)v16 != v21 )
          objc_enumerationMutation(obj);
        v4 = *(void **)(*((_QWORD *)&v15 + 1) + 8 * v3);
        v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v19);
        if ( (unsigned __int8)_objc_msgSend(v4, v20, v5) )
        {
          v6 = v25;
          v7 = _objc_msgSend(v25, v26);
          v8 = objc_retainAutoreleasedReturnValue(v7);
          _objc_msgSend(v8, v22, v4);
          v10 = _objc_msgSend(v6, v23);
          objc_retainAutoreleasedReturnValue(v10);
          v11 = _objc_msgSend(v6, v26);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          _objc_msgSend(v13, v24, v12);
        }
        ++v3;
      }
      while ( v3 < (unsigned __int64)v27 );
      v27 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v29, 16LL);
    }
    while ( v27 );
  }
}

//----- (00000001002D70FA) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setViewStates](ZhengGuZouShiContainerController *self, SEL a2)
{
  ZhengGuHangQingView *v3; // rax
  ZhengGuHangQingView *v4; // rbx
  ZhengGuHangQingView *v7; // rax
  ZhengGuHangQingView *v8; // rbx
  ZhengGuHangQingView *v10; // rax
  ZhengGuHangQingView *v11; // rbx
  ZhengGuHangQingView *v14; // rax
  ZhengGuHangQingView *v15; // rbx

  v2 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ZhengGuZouShiContainerController zhengGuHangQingView](self, "zhengGuHangQingView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseView setBackgroundColor:](v4, "setBackgroundColor:", v5);
  v7 = -[ZhengGuZouShiContainerController zhengGuHangQingView](self, "zhengGuHangQingView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[HXBaseView setBottomBorder:](v8, "setBottomBorder:", 1LL);
  v9 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v9);
  v10 = -[ZhengGuZouShiContainerController zhengGuHangQingView](self, "zhengGuHangQingView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[HXBaseView setBorderColor:](v11, "setBorderColor:", v12);
  v14 = -[ZhengGuZouShiContainerController zhengGuHangQingView](self, "zhengGuHangQingView");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  -[HXBaseView setBorderWidth:](v15, "setBorderWidth:", 1.0);
  -[FenshiKlineTrendChartContainerController hiddenFuncBtn](self, "hiddenFuncBtn");
}

//----- (00000001002D7239) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController addNotifications](ZhengGuZouShiContainerController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v3, "addObserver:selector:name:object:", self, "frameDidChange:", v6, v5);
}

//----- (00000001002D72CF) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController frameDidChange:](ZhengGuZouShiContainerController *self, SEL a2, id a3)
{
  ZhengGuHangQingView *v10; // rax
  QuoteBaseItem *v11; // rax
  QuoteBaseItem *v12; // rbx

  v3 = objc_retain(a3);
  v4 = v3;
  if ( !v3
    || (v5 = _objc_msgSend(v3, "object"),
        objc_retainAutoreleasedReturnValue(v5),
        v6 = _objc_msgSend(self, "view"),
        v7 = objc_retainAutoreleasedReturnValue(v6),
        v9 == v7) )
  {
    v10 = -[ZhengGuZouShiContainerController zhengGuHangQingView](self, "zhengGuHangQingView");
    objc_retainAutoreleasedReturnValue(v10);
    v11 = -[ZhengGuZouShiContainerController quoteItem](self, "quoteItem");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v13, "updateHangQing:", v12);
  }
}

//----- (00000001002D73AF) ----------------------------------------------------
ZhengGuHangQingView *__cdecl -[ZhengGuZouShiContainerController zhengGuHangQingView](
        ZhengGuZouShiContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._titleContainerView);
  return (ZhengGuHangQingView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001002D73C8) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setZhengGuHangQingView:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._titleContainerView, a3);
}

//----- (00000001002D73DC) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setQuoteItem:](ZhengGuZouShiContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._correlatedCode, a3);
}

//----- (00000001002D73F0) ----------------------------------------------------
NSString *__cdecl -[ZhengGuZouShiContainerController singleStockCode](ZhengGuZouShiContainerController *self, SEL a2)
{
  return (NSString *)self->super.super._refeshDitailsPageBlock;
}

//----- (00000001002D7401) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setSingleStockCode:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super.super._refeshDitailsPageBlock, a3);
}

//----- (00000001002D7415) ----------------------------------------------------
NSString *__cdecl -[ZhengGuZouShiContainerController singleMarket](ZhengGuZouShiContainerController *self, SEL a2)
{
  return (NSString *)self->super.super._changeFrameBtnClickedBlock;
}

//----- (00000001002D7426) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setSingleMarket:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super.super._changeFrameBtnClickedBlock, a3);
}

//----- (00000001002D743A) ----------------------------------------------------
NSString *__cdecl -[ZhengGuZouShiContainerController dapanStockCode](ZhengGuZouShiContainerController *self, SEL a2)
{
  return (NSString *)self->super.super._viewResizeCallBackBlock;
}

//----- (00000001002D744B) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setDapanStockCode:](
        ZhengGuZouShiContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super.super._viewResizeCallBackBlock, a3);
}

//----- (00000001002D745F) ----------------------------------------------------
NSString *__cdecl -[ZhengGuZouShiContainerController dapanMarket](ZhengGuZouShiContainerController *self, SEL a2)
{
  return (NSString *)self->super.super._modularVisibleType;
}

//----- (00000001002D7470) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController setDapanMarket:](ZhengGuZouShiContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._modularVisibleType, a3);
}

//----- (00000001002D7484) ----------------------------------------------------
void __cdecl -[ZhengGuZouShiContainerController .cxx_destruct](ZhengGuZouShiContainerController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super.super._modularVisibleType, 0LL);
  objc_storeStrong(&self->super.super._viewResizeCallBackBlock, 0LL);
  objc_storeStrong(&self->super.super._changeFrameBtnClickedBlock, 0LL);
  objc_storeStrong(&self->super.super._refeshDitailsPageBlock, 0LL);
  objc_storeStrong((id *)&self->super.super._correlatedCode, 0LL);
  objc_destroyWeak((id *)&self->super.super._titleContainerView);
}

