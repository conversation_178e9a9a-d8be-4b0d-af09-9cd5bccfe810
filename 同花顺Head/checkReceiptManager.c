id __cdecl +[checkReceipt<PERSON>anager shared](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_10046CB50;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D2F20 != -1 )
    dispatch_once(&qword_1016D2F20, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D2F18);
}

//----- (000000010046CB50) ----------------------------------------------------
void __fastcall sub_10046CB50(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D2F18;
  qword_1016D2F18 = v2;
}

//----- (000000010046CB82) ----------------------------------------------------
void __cdecl -[checkReceiptManager requestDidFinish:](checkReceiptManager *self, SEL a2, id a3)
{
  -[checkReceiptManager refreshVerifyInfo](self, "refreshVerifyInfo", a3);
}

//----- (000000010046CB94) ----------------------------------------------------
void __cdecl -[checkReceiptManager request:didFailWithError:](checkReceiptManager *self, SEL a2, id a3, id a4)
{
  id (*v6)(id, SEL, ...); // r12
  __CFString *v8; // rbx
  id (*v9)(id, SEL, ...); // r12

  v4 = _objc_msgSend(a4, "userInfo", a3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(v5, "objectForKey:", NSLocalizedDescriptionKey);
  v8 = (__CFString *)objc_retainAutoreleasedReturnValue(v7);
  if ( !v9(v8, "length") )
  {
    v8 = CFSTR("请稍后重试");
  }
  -[checkReceiptManager showSystemNSAlert:](self, "showSystemNSAlert:", v8);
}

//----- (000000010046CC32) ----------------------------------------------------
void __cdecl -[checkReceiptManager showSystemNSAlert:](checkReceiptManager *self, SEL a2, id a3)
{
  _QWORD block[4]; // [rsp+8h] [rbp-38h] BYREF

  block[0] = _NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = sub_10046CCAE;
  block[3] = &unk_1012DABC0;
  v5 = objc_retain(a3);
  v3 = objc_retain(v5);
  dispatch_async(&_dispatch_main_q, block);
}

//----- (000000010046CCAE) ----------------------------------------------------
void __fastcall sub_10046CCAE(__int64 a1)
{

  v1 = objc_alloc(&OBJC_CLASS___NSAlert);
  v2 = _objc_msgSend(v1, "init");
  v3 = _objc_msgSend(v2, "addButtonWithTitle:", CFSTR("知道了"));
  objc_unsafeClaimAutoreleasedReturnValue(v3);
  _objc_msgSend(v2, "setMessageText:", *(_QWORD *)(a1 + 32));
  _objc_msgSend(v2, "setAlertStyle:", 0LL);
  _objc_msgSend(v2, "runModal");
}

//----- (000000010046CD3A) ----------------------------------------------------
void __cdecl -[checkReceiptManager refreshVerifyInfo](checkReceiptManager *self, SEL a2)
{

  v2 = +[IAPVerifyManager shared](&OBJC_CLASS___IAPVerifyManager, "shared");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "sendReceiptToServer");
}

