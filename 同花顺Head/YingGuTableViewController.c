void __cdecl -[YingGuTableViewController viewDidLoad](YingGuTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___YingGuTableViewController;
  -[MeiGuBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", -1LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("YingGuStock"));
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 69635LL);
}

//----- (0000000100477039) ----------------------------------------------------
void __cdecl -[YingGuTableViewController requestForBackgroundData](YingGuTableViewController *self, SEL a2)
{
  ;
}

//----- (000000010047703F) ----------------------------------------------------
void __cdecl -[YingGuTableViewController requestCodeList](YingGuTableViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx
  NSString *v5; // rax
  NSString *v6; // rbx
  NSString *v7; // rax
  NSString *v8; // rbx
  HXTableManager *v9; // rax
  HXTableManager *v10; // r15
  SEL v14; // r12
  NSNumber *v29; // rax
  NSNumber *v31; // rax
  NSNumber *v33; // rax
  NSString *v34; // rax
  NSString *v35; // r15
  NSNumber *v37; // rax
  NSNumber *v38; // r13
  NSDictionary *v39; // rax
  HXTableRequestModule *v40; // rax
  HXTableRequestModule *v41; // r15
  NSNumber *v43; // rax
  NSNumber *v45; // rax
  NSString *v46; // rax
  NSNumber *v48; // rax
  NSNumber *v49; // rax
  NSNumber *v50; // rax
  NSNumber *v51; // rbx
  NSArray *v52; // rax
  NSArray *v53; // r13
  NSDictionary *v54; // rax
  HXTableRequestModule *v56; // rax
  HXTableRequestModule *v57; // r14
  id *v59; // r12
  _QWORD v60[4]; // [rsp+8h] [rbp-1A8h] BYREF
  _QWORD v62[4]; // [rsp+30h] [rbp-180h] BYREF
  id to; // [rsp+50h] [rbp-160h] BYREF
  id location; // [rsp+68h] [rbp-148h] BYREF
  id from; // [rsp+70h] [rbp-140h] BYREF

  v2 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( v3 )
  {
    objc_initWeak(&location, self);
    v4 = -[QuoteBaseTableViewController begin](self, "begin");
    -[QuoteBaseTableViewController count](self, "count");
    if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670
      || (v5 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier"),
          v6 = objc_retainAutoreleasedReturnValue(v5),
          v6) )
    {
      if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670 )
      {
        v7 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
        v8 = objc_retainAutoreleasedReturnValue(v7);
        if ( v8 )
        {
          v9 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
          v10 = objc_retainAutoreleasedReturnValue(v9);
          v11 = -[MeiGuBaseTableViewController tableKey](self, "tableKey");
          v12 = objc_retainAutoreleasedReturnValue(v11);
          v13 = -[HXTableManager getSelectedSchemesForKey:](v10, "getSelectedSchemesForKey:", v12);
          v70 = objc_retainAutoreleasedReturnValue(v13);
          v15 = _objc_msgSend(self, v14);
          v16 = objc_retainAutoreleasedReturnValue(v15);
          v17 = _objc_msgSend(v70, "entityForIdentifier:", v16);
          objc_retainAutoreleasedReturnValue(v17);
          if ( v18 )
          {
            v19 = _objc_msgSend(v18, "querykey");
            v20 = objc_retainAutoreleasedReturnValue(v19);
            if ( v20 )
            {
              v21 = v20;
              v22 = _objc_msgSend(v18, "timestamp");
              v23 = objc_retainAutoreleasedReturnValue(v22);
              if ( v23 )
              {
                v24 = _objc_msgSend(v18, "querykey");
                v71 = objc_retainAutoreleasedReturnValue(v24);
                v26 = _objc_msgSend(v25, "index");
                v28 = _objc_msgSend(v27, "timestamp");
                v72 = objc_retainAutoreleasedReturnValue(v28);
                v73[0] = (__int64)CFSTR("IWenCaiQueryKey");
                v74[0] = (__int64)v71;
                v73[1] = (__int64)CFSTR("IWenCaiKeyIndex");
                v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v26);
                v68 = objc_retain(v29);
                v74[1] = (__int64)v68;
                v73[2] = (__int64)CFSTR("IWenCaiTimestamp");
                v74[2] = (__int64)v72;
                v73[3] = (__int64)CFSTR("sortbegin");
                v30 = -[QuoteBaseTableViewController begin](self, "begin");
                v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v30);
                v69 = objc_retainAutoreleasedReturnValue(v31);
                v74[3] = (__int64)v69;
                v73[4] = (__int64)CFSTR("sortcount");
                v32 = -[QuoteBaseTableViewController count](self, "count");
                v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v32);
                v64 = objc_retainAutoreleasedReturnValue(v33);
                v74[4] = (__int64)v64;
                v73[5] = (__int64)CFSTR("sortorder");
                v34 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
                v35 = objc_retain(v34);
                v74[5] = (__int64)v35;
                v73[6] = (__int64)CFSTR("blockid");
                v36 = -[QuoteBaseTableViewController blockID](self, "blockID");
                v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v36);
                v38 = objc_retainAutoreleasedReturnValue(v37);
                v74[6] = (__int64)v38;
                v39 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v74, v73, 7LL);
                v65 = objc_retainAutoreleasedReturnValue(v39);
                v40 = -[QuoteBaseTableViewController iWenCaiCodeListRequestModule](self, "iWenCaiCodeListRequestModule");
                v41 = objc_retain(v40);
                v62[0] = _NSConcreteStackBlock;
                v62[1] = 3254779904LL;
                v62[2] = sub_100477A30;
                v62[3] = &unk_1012DAED8;
                objc_copyWeak(&to, &location);
                v42 = v65;
                -[HXTableRequestModule request:params:callBack:](v41, "request:params:callBack:", 15LL, v65, v62);
                objc_destroyWeak(&to);
              }
            }
          }
        }
      }
    }
    else
    {
      v76[0] = (__int64)CFSTR("sortbegin");
      v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v4);
      v70 = objc_retainAutoreleasedReturnValue(v43);
      v77[0] = (__int64)v70;
      v76[1] = (__int64)CFSTR("sortcount");
      v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v44);
      v71 = objc_retain(v45);
      v77[1] = (__int64)v71;
      v76[2] = (__int64)CFSTR("sortorder");
      v46 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
      v72 = objc_retain(v46);
      v77[2] = (__int64)v72;
      v76[3] = (__int64)CFSTR("sortid");
      v47 = -[HXBaseTableViewController sortID](self, "sortID");
      v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v47);
      v68 = objc_retainAutoreleasedReturnValue(v48);
      v77[3] = (__int64)v68;
      v76[4] = (__int64)CFSTR("Market");
      v77[4] = (__int64)CFSTR("UEUA");
      v76[5] = (__int64)CFSTR("datatype");
      v49 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v75[0] = (__int64)objc_retainAutoreleasedReturnValue(v49);
      v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
      v51 = objc_retain(v50);
      v75[1] = (__int64)v51;
      v52 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v75, 2LL);
      v53 = objc_retainAutoreleasedReturnValue(v52);
      v77[5] = (__int64)v53;
      v54 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v77, v76, 6LL);
      v69 = objc_retainAutoreleasedReturnValue(v54);
      objc_initWeak(&from, self);
      v56 = -[QuoteBaseTableViewController basicHQCodeListRequestModule](self, "basicHQCodeListRequestModule");
      v57 = objc_retain(v56);
      v60[0] = _NSConcreteStackBlock;
      v60[1] = 3254779904LL;
      v60[2] = sub_10047789C;
      v60[3] = &unk_1012DAED8;
      objc_copyWeak(&v61, &from);
      v58 = v69;
      -[HXTableRequestModule request:params:callBack:](v57, "request:params:callBack:", 6LL, v69, v60);
      objc_destroyWeak(v59);
      objc_destroyWeak(&from);
    }
    objc_destroyWeak(&location);
  }
}

//----- (000000010047789C) ----------------------------------------------------
void __fastcall sub_10047789C(__int64 a1, void *a2, void *a3)
{
  id *v10; // r12
  NSNumber *v12; // rax
  id *v16; // r12
  id *v17; // r14
  id WeakRetained; // [rsp+10h] [rbp-30h]
  NSNumber *v26; // [rsp+10h] [rbp-30h]

  v4 = objc_retain(a2);
  v5 = objc_retain(a3);
  v6 = _objc_msgSend(v4, "count");
  if ( v5 && v6 )
  {
    WeakRetained = objc_loadWeakRetained((id *)(v7 + 32));
    v8 = _objc_msgSend(WeakRetained, "getCodeAndMarketArrWithResponse:", v4);
    v24 = v5;
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v9);
    v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34056LL);
    v26 = objc_retainAutoreleasedReturnValue(v12);
    v13 = _objc_msgSend(v5, "thsNumberForKey:", v26);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "unsignedIntegerValue");
    v17 = v16;
    v18 = objc_loadWeakRetained(v16);
    _objc_msgSend(v18, "setAllCodesNum:", v15);
    v19 = v18;
    v4 = v20;
    v21 = objc_loadWeakRetained(v17);
    _objc_msgSend(v21, "requestDetailData");
    v22 = v21;
    v5 = v24;
    v23(v22);
  }
}

//----- (0000000100477A30) ----------------------------------------------------
void __fastcall sub_100477A30(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // rbx
  id *v10; // r12
  id *v12; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "convertIWenCaiCodeListToUesfulCodeListResponse:", v2);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v14 = v6;
  v7 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( _objc_msgSend(v8, "count") )
  {
    v9 = _objc_msgSend(v8, "mutableCopy");
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v9);
    v13 = objc_loadWeakRetained(v12);
    _objc_msgSend(v13, "requestDetailData");
  }
}

//----- (0000000100477B4B) ----------------------------------------------------
id __cdecl -[YingGuTableViewController getCodeAndMarketArrWithResponse:](
        YingGuTableViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v4; // r14
  NSNumber *v6; // rax
  NSNumber *v7; // rbx
  NSDictionary *v16; // rax
  NSDictionary *v17; // rbx

  v21 = objc_retain(a3);
  if ( _objc_msgSend(v21, "count") )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v20 = objc_retainAutoreleasedReturnValue(v3);
    if ( _objc_msgSend(v21, "count") )
    {
      v4 = 0LL;
      do
      {
        v5 = _objc_msgSend(v21, "thsDictionaryAtIndex:", v4);
        objc_retainAutoreleasedReturnValue(v5);
        v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
        v7 = objc_retainAutoreleasedReturnValue(v6);
        v9 = _objc_msgSend(v8, "thsStringForKey:", v7);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        if ( _objc_msgSend(v10, "length") )
        {
          v12 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v10);
          objc_retainAutoreleasedReturnValue(v12);
          v13 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v10);
          objc_retainAutoreleasedReturnValue(v13);
          v19 = v14;
          if ( _objc_msgSend(v14, "length") && _objc_msgSend(v15, "length") )
          {
            v22[0] = (__int64)CFSTR("StockCode");
            v23[0] = (__int64)v19;
            v22[1] = (__int64)CFSTR("Market");
            v23[1] = (__int64)v15;
            v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v23, v22, 2LL);
            v17 = objc_retainAutoreleasedReturnValue(v16);
            _objc_msgSend(v20, "addObject:", v17);
          }
        }
        ++v4;
      }
      while ( (unsigned __int64)_objc_msgSend(v21, "count") > v4 );
    }
  }
  else
  {
    v20 = 0LL;
  }
  return objc_autoreleaseReturnValue(v20);
}

