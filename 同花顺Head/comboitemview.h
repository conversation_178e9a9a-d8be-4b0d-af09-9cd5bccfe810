//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSView.h>

@class NSComboBox, NSTextField;
@protocol comboItemActionDelegate;

@interface comboitemview : NSView
{
    NSTextField *_tfShowText;
    NSComboBox *_ParentNSComBoBox;
    id <comboItemActionDelegate> _comboItemDelegate;
}

+ (void)DoNothing;

@property(nonatomic) __weak id <comboItemActionDelegate> comboItemDelegate; // @synthesize comboItemDelegate=_comboItemDelegate;
@property(nonatomic) __weak NSComboBox *ParentNSComBoBox; // @synthesize ParentNSComBoBox=_ParentNSComBoBox;
@property __weak NSTextField *tfShowText; // @synthesize tfShowText=_tfShowText;
- (void)onDelete:(id)arg1;
- (void)drawRect:(struct CGRect)arg1;

@end

