void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController updateTableVCData:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  __CFString *v20; // rax
  __CFString *v21; // rdx
  __CFString *v45; // [rsp+0h] [rbp-30h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v4, "thsStringForKey:", CFSTR("tablekey"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[ThumbnailZhongCangStocksPoolTableViewController setTableKey:](self, "setTableKey:", v6);
    v8 = _objc_msgSend(v7, "thsNumberForKey:", CFSTR("TableID"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "longValue");
    -[ThumbnailBaseTableViewController setTableID:](self, "setTableID:", v10);
    v12 = _objc_msgSend(v11, "thsNumberForKey:", CFSTR("sortid"));
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "longValue");
    -[HXBaseTableViewController setSortID:](self, "setSortID:", v14);
    v16 = _objc_msgSend(v15, "thsStringForKey:", CFSTR("IWenCaiSortIdentifier"));
    v17 = objc_retainAutoreleasedReturnValue(v16);
    -[HXBaseTableViewController setWenCaiSortIdentifier:](self, "setWenCaiSortIdentifier:", v17);
    v19 = _objc_msgSend(v18, "thsStringForKey:", CFSTR("sortorder"));
    v20 = (__CFString *)objc_retainAutoreleasedReturnValue(v19);
    v45 = v20;
    v21 = CFSTR("D");
    if ( v20 )
      v21 = v20;
    -[HXBaseTableViewController setSortOrder:](self, "setSortOrder:", v21);
    v23 = _objc_msgSend(v22, "thsNumberForKey:", CFSTR("sortbegin"));
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25 = _objc_msgSend(v24, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaBegin:](self, "setQuotaBegin:", v25);
    v27 = _objc_msgSend(v26, "thsNumberForKey:", CFSTR("sortcount"));
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29 = _objc_msgSend(v28, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaCount:](self, "setQuotaCount:", v29);
    v31 = _objc_msgSend(v30, "thsNumberForKey:", CFSTR("Index"));
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33 = _objc_msgSend(v32, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaIndex:](self, "setQuotaIndex:", v33);
    v35 = _objc_msgSend(v34, "thsStringForKey:", CFSTR("SelectedCode"));
    v36 = objc_retainAutoreleasedReturnValue(v35);
    -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v36);
    v38 = _objc_msgSend(v37, "thsNumberForKey:", CFSTR("totalnumber"));
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = _objc_msgSend(v39, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v40);
    v42 = _objc_msgSend(v41, "thsArrayForKey:", off_1012E26B0[0]);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v44 = _objc_msgSend(v43, "mutableCopy");
    -[ThumbnailZhongCangStocksPoolTableViewController setCompleteCodeAndDataArr:](
      self,
      "setCompleteCodeAndDataArr:",
      v44);
    -[ThumbnailBaseTableViewController setIsTableSwitched:](self, "setIsTableSwitched:", 1LL);
    -[ThumbnailBaseTableViewController setIsFoucsOfSuperController:](self, "setIsFoucsOfSuperController:", 1LL);
  }
}

//----- (0000000100397066) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController basicHQCodeListRequestModule](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{

  visibleY = self->super.super._visibleY;
  if ( visibleY == 0.0 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super.super._visibleY;
    *(_QWORD *)&self->super.super._visibleY = v5;
    visibleY = self->super.super._visibleY;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(*(id *)&visibleY);
}

//----- (00000001003970B7) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController requestForMyTable](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  if ( !(unsigned __int8)-[ThumbnailBaseTableViewController invalidRequestWhenViewWillBeRemoved](
                           self,
                           "invalidRequestWhenViewWillBeRemoved") )
  {
    -[ThumbnailBaseTableViewController deleteOrder](self, "deleteOrder");
    if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
    {
      -[ThumbnailZhongCangStocksPoolTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
      -[ThumbnailZhongCangStocksPoolTableViewController requestCodeList](self, "requestCodeList");
    }
  }
}

//----- (000000010039712E) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController order](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  HXTableRequestModule *v2; // rax
  HXTableRequestModule *v3; // r14
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // r15
  NSArray *v6; // rax
  NSArray *v7; // r13
  id *v8; // r12
  _QWORD v9[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    objc_initWeak(location, self);
    v2 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[ThumbnailZhongCangStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9[0] = _NSConcreteStackBlock;
    v9[1] = 3254779904LL;
    v9[2] = sub_100397263;
    v9[3] = &unk_1012DAF08;
    objc_copyWeak(&to, location);
    -[HXTableRequestModule subscribe:dataTypes:callBack:](v3, "subscribe:dataTypes:callBack:", v5, v7, v9);
    objc_destroyWeak(v8);
    objc_destroyWeak(location);
  }
}

//----- (0000000100397263) ----------------------------------------------------
void __fastcall sub_100397263(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithPushData:", v2);
}

//----- (00000001003972BD) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setRequestAndOrderParams](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSArray *v8; // rax
  NSArray *v9; // rax

  -[ThumbnailBaseTableViewController setRequestRowRange](self, "setRequestRowRange");
  v2 = -[ThumbnailZhongCangStocksPoolTableViewController getRequestDataTypes](self, "getRequestDataTypes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "thsArrayForKey:", CFSTR("basicDataTypes"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[ThumbnailZhongCangStocksPoolTableViewController setBasicHQDataTypes:](self, "setBasicHQDataTypes:", v5);
  v6 = _objc_msgSend(v3, "thsDictionaryForKey:", CFSTR("iWenCaiDataItemParams"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[ThumbnailZhongCangStocksPoolTableViewController setIWenCaiItemParams:](self, "setIWenCaiItemParams:", v7);
  v8 = -[ThumbnailZhongCangStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = -[ThumbnailBaseTableViewController getOrderDataTypesWithRequestDataTypes:](
          self,
          "getOrderDataTypesWithRequestDataTypes:",
          v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[ThumbnailZhongCangStocksPoolTableViewController setOrderHQDataTypes:](self, "setOrderHQDataTypes:", v11);
}

//----- (00000001003973D6) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController getRequestDataTypes](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // r15
  NSArray *v7; // rax
  NSArray *v8; // r14
  NSMutableArray *v9; // rax
  NSNumber *v16; // rax
  NSNumber *v17; // rbx
  NSNumber *v20; // rax
  NSNumber *v21; // rbx
  HXTableManager *v30; // rax
  HXTableManager *v31; // r15
  bool v49; // zf
  NSArray *v52; // rax
  NSArray *v53; // rbx
  NSArray *v56; // rax
  NSArray *v57; // rbx
  NSNumber *v59; // rax
  NSNumber *v60; // r14
  NSArray *v61; // rax
  NSArray *v62; // rbx
  NSArray *v65; // rax
  NSArray *v66; // r15
  NSDictionary *v71; // r14
  NSDictionary *v72; // rax
  NSNumber *v75; // [rsp+0h] [rbp-B0h]
  NSNumber *v76; // [rsp+8h] [rbp-A8h]
  NSMutableArray *v78; // [rsp+10h] [rbp-A0h]
  _QWORD v81[2]; // [rsp+20h] [rbp-90h] BYREF
  _QWORD v82[2]; // [rsp+30h] [rbp-80h] BYREF
  NSNumber *v84; // [rsp+48h] [rbp-68h] BYREF
  _QWORD v87[4]; // [rsp+60h] [rbp-50h] BYREF

  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v76 = objc_retainAutoreleasedReturnValue(v2);
  v87[0] = v76;
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
  v75 = objc_retainAutoreleasedReturnValue(v3);
  v87[1] = v75;
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
  v87[2] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v87[3] = v6;
  v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v87, 4LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v8);
  v78 = objc_retainAutoreleasedReturnValue(v9);
  if ( (__int64)-[HXBaseTableViewController sortID](self, "sortID") )
  {
    if ( _objc_msgSend(v11, "sortID") != (id)12345670 )
    {
      v12 = _objc_msgSend(v11, "wenCaiSortIdentifier");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = _objc_msgSend(v13, "length");
      if ( !v14 )
      {
        v15 = _objc_msgSend(v11, "sortID");
        v16 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v15);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = (__int64)_objc_msgSend(v78, "containsObject:", v17);
        if ( !v18 )
        {
          v19 = _objc_msgSend(v11, "sortID");
          v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v19);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          _objc_msgSend(v78, "addObject:", v21);
        }
      }
    }
  }
  v22 = _objc_msgSend(v11, "sortID");
  v23 = +[ThumbnailUtils handleSpectialDataItem:dataItemsArr:](
          &OBJC_CLASS___ThumbnailUtils,
          "handleSpectialDataItem:dataItemsArr:",
          v22,
          v78);
  v77 = objc_retainAutoreleasedReturnValue(v23);
  v24 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v79 = objc_retainAutoreleasedReturnValue(v24);
  if ( !(__int64)_objc_msgSend(v25, "sortID") || _objc_msgSend(v26, "sortID") == (id)12345670 )
  {
    v27 = _objc_msgSend(v26, "wenCaiSortIdentifier");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29 = _objc_msgSend(v28, "length");
    if ( v29 )
    {
      v30 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v33 = v32;
      v34 = _objc_msgSend(v32, "tableKey");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v36 = -[HXTableManager getSelectedSchemesForKey:](v31, "getSelectedSchemesForKey:", v35);
      objc_retainAutoreleasedReturnValue(v36);
      v37 = _objc_msgSend(v33, "wenCaiSortIdentifier");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v40 = _objc_msgSend(v39, "entityForIdentifier:", v38);
      v41 = objc_retainAutoreleasedReturnValue(v40);
      v42 = _objc_msgSend(v41, "identifier");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      if ( v43 && (v44 = _objc_msgSend(v41, "querykey"), (v45 = objc_retainAutoreleasedReturnValue(v44)) != 0LL) )
      {
        v46 = v45;
        v47 = _objc_msgSend(v41, "timestamp");
        v80 = v41;
        v48 = objc_retainAutoreleasedReturnValue(v47);
        v49 = v48 == 0LL;
        v41 = v80;
        if ( !v49 )
        {
          v50 = _objc_msgSend(v80, "identifier");
          v51 = objc_retainAutoreleasedReturnValue(v50);
          v86 = v51;
          v52 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v86, 1LL);
          v53 = objc_retainAutoreleasedReturnValue(v52);
          _objc_msgSend(v79, "setObject:forKey:", v53, CFSTR("IWenCaiIdentifier"));
          v54 = _objc_msgSend(v80, "querykey");
          v55 = objc_retainAutoreleasedReturnValue(v54);
          v85 = v55;
          v56 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v85, 1LL);
          v57 = objc_retainAutoreleasedReturnValue(v56);
          _objc_msgSend(v79, "setObject:forKey:", v57, CFSTR("IWenCaiQueryKey"));
          v58 = _objc_msgSend(v80, "index");
          v59 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v58);
          v60 = objc_retainAutoreleasedReturnValue(v59);
          v84 = v60;
          v61 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v84, 1LL);
          v62 = objc_retainAutoreleasedReturnValue(v61);
          _objc_msgSend(v79, "setObject:forKey:", v62, CFSTR("IWenCaiKeyIndex"));
          v63 = _objc_msgSend(v80, "timestamp");
          v64 = objc_retainAutoreleasedReturnValue(v63);
          v83 = v64;
          v65 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v83, 1LL);
          v66 = objc_retainAutoreleasedReturnValue(v65);
          _objc_msgSend(v79, "setObject:forKey:", v66, CFSTR("IWenCaiTimestamp"));
          v67 = v64;
          v41 = v80;
        }
      }
      else
      {
      }
    }
  }
  v69 = _objc_msgSend(v77, "count");
  v71 = 0LL;
  if ( v79 && v69 )
  {
    v81[0] = CFSTR("basicDataTypes");
    v82[0] = v70;
    v81[1] = CFSTR("iWenCaiDataItemParams");
    v82[1] = v79;
    v72 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v82, v81, 2LL);
    v71 = objc_retainAutoreleasedReturnValue(v72);
  }
  return objc_autoreleaseReturnValue(v71);
}

//----- (0000000100397AD3) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController dealWithPushData:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  ThumbnailZhongCangStocksPoolTableViewController *v3; // r13
  HXStockModel *v4; // rax
  HXStockModel *v5; // r15
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  HXStockModel *v11; // rdi
  id (**v12)(id, SEL, ...); // r12
  id (**v13)(id, SEL, ...); // r15
  unsigned __int64 v16; // rcx
  unsigned __int64 v22; // r14
  unsigned __int64 v52; // r15
  bool v53; // cc
  unsigned __int64 v55; // rax
  ThumbnailZhongCangStocksPoolTableViewController *v60; // [rsp+60h] [rbp-50h]
  unsigned __int64 v61; // [rsp+68h] [rbp-48h]

  v3 = self;
  v64 = objc_retain(a3);
  if ( _objc_msgSend(v64, "count") )
  {
    v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = v6(v5, "mainStockTableViewDataArray");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v8);
    v63 = objc_retainAutoreleasedReturnValue(v10);
    v11 = v5;
    v13 = v12;
    if ( !((__int64 (__fastcall *)(id, __int64))v13)(v64, v14) )
      goto LABEL_15;
    v61 = 0LL;
    v60 = v3;
    while ( 1 )
    {
      v17 = (void *)((__int64 (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *))v13)(
                      v3,
                      "orderCodeMArray");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v20 = ((__int64 (__fastcall *)(id, __int64))v13)(v18, v19);
      if ( !v20 )
        goto LABEL_14;
      v22 = 0LL;
      while ( ((__int64 (__fastcall *)(id, const char *))v13)(v63, v21) <= v22 )
      {
LABEL_11:
        ++v22;
        v49 = (void *)((__int64 (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *))v13)(
                        v3,
                        "orderCodeMArray");
        v50 = objc_retainAutoreleasedReturnValue(v49);
        v52 = ((__int64 (__fastcall *)(id, __int64))v13)(v50, v51);
        v53 = v52 <= v22;
        v13 = &_objc_msgSend;
        if ( v53 )
          goto LABEL_14;
      }
      v23 = (void *)((__int64 (__fastcall *)(id, const char *, unsigned __int64))v13)(v64, "thsDictionaryAtIndex:", v61);
      v62 = objc_retainAutoreleasedReturnValue(v23);
      v24 = (void *)((__int64 (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *))v13)(
                      v3,
                      "orderCodeMArray");
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v27 = (void *)((__int64 (__fastcall *)(id, __int64, unsigned __int64))v13)(v25, v26, v22);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v13)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithInt:",
                      5LL);
      v30 = objc_retainAutoreleasedReturnValue(v29);
      v31 = (void *)((__int64 (__fastcall *)(id, const char *, id))v13)(v62, "thsStringForKey:", v30);
      v59 = objc_retainAutoreleasedReturnValue(v31);
      v32 = v28;
      v34 = (void *)((__int64 (__fastcall *)(id, __int64, __CFString *))v13)(v28, v33, CFSTR("Market"));
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v58 = v32;
      v37 = (void *)((__int64 (__fastcall *)(id, __int64, __CFString *))v13)(v32, v36, CFSTR("StockCode"));
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v39 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *, id, id))v13)(
                      &OBJC_CLASS___HXTools,
                      "getMarketAndCodeByAppendingMarket:andStockCode:",
                      v35,
                      v38);
      objc_retainAutoreleasedReturnValue(v39);
      if ( !((unsigned __int8 (__fastcall *)(id, const char *, __int64))v13)(v59, "isEqualToString:", v40) )
        goto LABEL_10;
      v42 = v41;
      v43 = _objc_msgSend(v63, "thsDictionaryAtIndex:", v22);
      v44 = objc_retainAutoreleasedReturnValue(v43);
      _objc_msgSend(v44, "mutableCopy");
      v46 = -[ThumbnailZhongCangStocksPoolTableViewController updateData:SourceData:](
              v60,
              "updateData:SourceData:",
              v62,
              v45);
      v47 = objc_retainAutoreleasedReturnValue(v46);
      if ( !v47 )
        break;
      _objc_msgSend(v63, "replaceObjectAtIndex:withObject:", v22, v47);
      v54 = v42;
      v13 = &_objc_msgSend;
      v3 = v60;
      v21 = "count";
LABEL_14:
      v55 = ((__int64 (__fastcall *)(id, const char *))v13)(v64, v21);
      v16 = v61 + 1;
      v61 = v16;
      if ( v55 <= v16 )
      {
LABEL_15:
        v56 = (void *)((__int64 (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *, __int64, unsigned __int64))v13)(
                        v3,
                        "stockModel",
                        v15,
                        v16);
        v57 = objc_retainAutoreleasedReturnValue(v56);
        ((void (__fastcall *)(id, const char *, id))v13)(v57, "setMainStockTableViewDataArray:", v63);
        ((void (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *, __int64))v13)(
          v3,
          "reloadData:",
          1LL);
        goto LABEL_16;
      }
    }
    v41 = v42;
LABEL_10:
    v3 = v60;
    v13 = &_objc_msgSend;
    goto LABEL_11;
  }
LABEL_16:
}

//----- (0000000100397F2D) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController updateData:SourceData:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ThumbnailZhongCangStocksPoolTableViewController *v5; // rbx
  NSArray *v7; // rax
  NSArray *v8; // r13
  NSArray *v11; // rax
  NSArray *v12; // r14
  NSArray *v19; // rax
  NSArray *v20; // rbx
  unsigned __int64 v22; // r12

  v5 = self;
  objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = -[ThumbnailZhongCangStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "count");
  if ( v9 )
  {
    v25 = v10;
    do
    {
      v24 = v6;
      v11 = -[ThumbnailZhongCangStocksPoolTableViewController orderHQDataTypes](v5, "orderHQDataTypes");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = _objc_msgSend(v12, "thsNumberAtIndex:", v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = _objc_msgSend(v15, "unsignedIntegerValue");
      v17 = +[ThumbnailUtils setSourceData:withPushData:dataItemID:](
              &OBJC_CLASS___ThumbnailUtils,
              "setSourceData:withPushData:dataItemID:",
              v24,
              v25,
              v16);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = -[ThumbnailZhongCangStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v20, "count");
      v6 = v18;
      v5 = self;
    }
    while ( (unsigned __int64)v21 > v22 );
    v6 = v18;
    v10 = v25;
  }
  return objc_autorelease(v6);
}

//----- (00000001003980C5) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController actionForTableViewSelectionDidChange:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v4; // rax
  HXStockModel *v5; // r13
  NSArray *v6; // rax
  NSArray *v7; // r14
  __objc2_class *v27; // r15
  __objc2_class *v31; // rdi
  ThumbnailZhongCangStocksPoolTableViewController *v36; // r15
  __objc2_class *v57; // [rsp+28h] [rbp-78h]
  _QWORD v58[2]; // [rsp+30h] [rbp-70h] BYREF
  _QWORD v59[2]; // [rsp+40h] [rbp-60h] BYREF
  _QWORD v60[2]; // [rsp+50h] [rbp-50h] BYREF
  _QWORD v61[2]; // [rsp+60h] [rbp-40h] BYREF

  v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXStockModel mainStockTableViewDataArray](v5, "mainStockTableViewDataArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "arrayWithArray:", v7);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(v10, "count");
  if ( (unsigned __int64)a3 <= 0x7FFFFFFFFFFFFFFELL && v12 )
  {
    v14 = v13(self, "begin");
    v53 = v10;
    v16 = (void *)v15(v10, "thsDictionaryAtIndex:", a3 - v14);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = (void *)v18(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v22 = (void *)v21(v17, "thsStringForKey:", v20);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v24 = v23;
    v26 = (void *)v25(&OBJC_CLASS___HXTools, "getCodeString:", v23);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v55 = v24;
    v29 = (void *)v28(&OBJC_CLASS___HXTools, "getMarketString:", v24);
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v57 = v27;
    v31 = v27;
    v33 = v32;
    if ( v32(v31, "length") && _objc_msgSend(v30, "length") )
    {
      v34 = (void *)v33(&OBJC_CLASS___SelfStock, "sharedInstance");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      ((void (__fastcall *)(id, const char *, __objc2_class *, id, __int64))v33)(
        v35,
        "addRecentlyScanStock:market:toBegin:",
        (__objc2_class *)v57,
        v30,
        1LL);
      v36 = self;
      v38 = (void *)v37(self, "invokeRowSwitchCallBack");
      v39 = objc_retainAutoreleasedReturnValue(v38);
      v54 = v30;
      if ( v39 )
      {
        v60[0] = CFSTR("StockCode");
        v61[0] = v57;
        v60[1] = CFSTR("Market");
        v61[1] = v30;
        v41 = (void *)v40(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v61, v60, 2LL);
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v44 = (void *)v43(self, "invokeRowSwitchCallBack");
        v45 = objc_retainAutoreleasedReturnValue(v44);
        (*((void (__fastcall **)(id, id))v45 + 2))(v45, v42);
        v36 = self;
      }
      ((void (__fastcall *)(ThumbnailZhongCangStocksPoolTableViewController *, const char *, id))v40)(
        v36,
        "setSelectedCode:",
        v55);
      v58[0] = CFSTR("StockCode");
      v59[0] = v57;
      v58[1] = off_1012E0FA8;
      v59[1] = v55;
      v47 = (void *)v46(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v59, v58, 2LL);
      v48 = objc_retainAutoreleasedReturnValue(v47);
      v50 = (void *)v49(v36, "myTable");
      v51 = objc_retainAutoreleasedReturnValue(v50);
      v52(v51, "setParamsDic:", v48);
      v30 = v54;
    }
    v10 = v53;
  }
}

//----- (000000010039845C) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController requestCodeList](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  _QWORD v19[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670
    || (v3 = _objc_msgSend(v2, "wenCaiSortIdentifier"),
        v4 = objc_retainAutoreleasedReturnValue(v3),
        v5 = _objc_msgSend(v4, "length"),
        v5) )
  {
    if ( _objc_msgSend(v2, "sortID") == (id)12345670 )
    {
      v7 = _objc_msgSend(v6, "wenCaiSortIdentifier");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v9 = _objc_msgSend(v8, "length");
      if ( v9 )
      {
        v11 = _objc_msgSend(v10, "getOrderCodeArrWhenSortByWenCai");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        _objc_msgSend(v13, "setOrderCodeMArray:", v12);
        _objc_msgSend(v14, "requestDetailData");
      }
    }
  }
  else
  {
    v15 = _objc_msgSend(v2, "getCompleteCodeList");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    if ( _objc_msgSend(v16, "length") )
    {
      objc_initWeak(location, v17);
      v19[0] = _NSConcreteStackBlock;
      v19[1] = 3254779904LL;
      v19[2] = sub_100398641;
      v19[3] = &unk_1012DAED8;
      objc_copyWeak(&to, location);
      _objc_msgSend(v18, "requestBasicHQCodeListWithCodeList:callBack:", v16, v19);
      objc_destroyWeak(&to);
      objc_destroyWeak(location);
    }
  }
}

//----- (0000000100398641) ----------------------------------------------------
void __fastcall sub_100398641(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // rbx
  id *v7; // r12
  id *v10; // r12
  id *v12; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "sortID");
  if ( v5 == (id)84 )
  {
    v6 = _objc_msgSend(v2, "mutableCopy");
    v8 = objc_loadWeakRetained(v7);
    _objc_msgSend(v8, "setOrderCodeMArray:", v6);
  }
  else
  {
    v9 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v2);
    v6 = objc_retainAutoreleasedReturnValue(v9);
    v8 = _objc_msgSend(v6, "mutableCopy");
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v8);
  }
  v13 = objc_loadWeakRetained(v12);
  _objc_msgSend(v13, "requestDetailData");
}

//----- (0000000100398773) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController getCompleteCodeList](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // rbx
  SEL v22; // r12
  id (*v43)(id, SEL, ...); // r12
  unsigned __int64 v44; // r12
  __CFString *v46; // rcx
  unsigned __int64 v47; // r14
  SEL v52; // rbx
  id (*v58)(id, SEL, ...); // r12
  __CFString *v60; // r12
  __CFString *v62; // r12
  SEL v69; // [rsp+48h] [rbp-128h]
  SEL v70; // [rsp+50h] [rbp-120h]
  SEL v71; // [rsp+58h] [rbp-118h]
  SEL v72; // [rsp+60h] [rbp-110h]
  SEL v73; // [rsp+68h] [rbp-108h]
  SEL v74; // [rsp+70h] [rbp-100h]
  SEL v75; // [rsp+78h] [rbp-F8h]
  SEL v76; // [rsp+80h] [rbp-F0h]
  SEL v77; // [rsp+88h] [rbp-E8h]
  SEL v80; // [rsp+A0h] [rbp-D0h]
  SEL v83; // [rsp+B8h] [rbp-B8h]

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v79 = objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "count");
  if ( v5 )
  {
    v6 = self;
    v75 = "objectAtIndex:";
    v76 = "class";
    v77 = "isKindOfClass:";
    v80 = "code";
    v69 = "length";
    v83 = "market";
    v70 = "thsArrayForKey:";
    v71 = "arrayWithArray:";
    v72 = "containsObject:";
    v74 = "addObject:";
    v73 = "setObject:forKey:";
    v78 = self;
    do
    {
      v7 = _objc_msgSend(v6, "completeCodeAndDataArr");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v10 = _objc_msgSend(v8, v75, v9);
      objc_retainAutoreleasedReturnValue(v10);
      v12 = v11;
      v13 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, v76);
      v15 = (unsigned __int8)_objc_msgSend(v14, v77, v13);
      v17 = v16;
      if ( v15 )
      {
        v81 = v12;
        v18 = _objc_msgSend(v16, v80);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        if ( _objc_msgSend(v19, v69) )
        {
          v20 = _objc_msgSend(v17, v83);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          _objc_msgSend(v21, v22);
          v82 = v17;
          v12 = (char *)v81;
          if ( v23 )
          {
            v24 = _objc_msgSend(v17, v83);
            v25 = objc_retainAutoreleasedReturnValue(v24);
            v26 = _objc_msgSend(v79, v70, v25);
            v27 = objc_retainAutoreleasedReturnValue(v26);
            v29 = _objc_msgSend(v28, v71, v27);
            objc_retainAutoreleasedReturnValue(v29);
            v30 = _objc_msgSend(v82, v80);
            v31 = objc_retainAutoreleasedReturnValue(v30);
            LOBYTE(v25) = (unsigned __int8)_objc_msgSend(v32, v72, v31);
            if ( !(_BYTE)v25 )
            {
              v34 = _objc_msgSend(v17, v80);
              v35 = objc_retainAutoreleasedReturnValue(v34);
              _objc_msgSend(v36, v74, v35);
            }
            if ( _objc_msgSend(v33, "count") )
            {
              v38 = _objc_msgSend(v17, v83);
              v39 = objc_retainAutoreleasedReturnValue(v38);
              _objc_msgSend(v79, v73, v40, v39);
            }
            v17 = v82;
          }
        }
        else
        {
          v12 = (char *)v81;
        }
      }
      v81 = v12 + 1;
      v6 = v78;
      v41 = _objc_msgSend(v78, "completeCodeAndDataArr");
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v82 = v43(v42, "count");
    }
    while ( (unsigned __int64)v82 > v44 );
  }
  v68 = 0LL;
  v67 = 0LL;
  v66 = 0LL;
  v65 = 0LL;
  v45 = _objc_msgSend(v79, "allKeys");
  v83 = (SEL)objc_retainAutoreleasedReturnValue(v45);
  v78 = _objc_msgSend((id)v83, "countByEnumeratingWithState:objects:count:", &v65, v84, 16LL);
  if ( v78 )
  {
    v80 = *(SEL *)v66;
    v46 = &charsToLeaveEscaped;
    do
    {
      v75 = "thsArrayForKey:";
      v76 = "componentsJoinedByString:";
      v77 = "stringWithFormat:";
      v47 = 0LL;
      do
      {
        v81 = v46;
        if ( *(SEL *)v66 != v80 )
          objc_enumerationMutation((id)v83);
        v48 = *(_QWORD *)(*((_QWORD *)&v65 + 1) + 8 * v47);
        v49 = _objc_msgSend(v79, v75, v48);
        v82 = objc_retainAutoreleasedReturnValue(v49);
        v50 = _objc_msgSend(v82, v76, CFSTR(","));
        v51 = objc_retainAutoreleasedReturnValue(v50);
        v52 = v77;
        v54 = _objc_msgSend(v53, v77, CFSTR("%@(%@,);"), v48, v51);
        v55 = objc_retainAutoreleasedReturnValue(v54);
        v56 = v52;
        v57 = v81;
        v59 = v58(&OBJC_CLASS___NSString, v56, CFSTR("%@%@"), v81, v55);
        objc_retainAutoreleasedReturnValue(v59);
        ++v47;
        v46 = v60;
      }
      while ( v47 < (unsigned __int64)v78 );
      v61 = _objc_msgSend((id)v83, "countByEnumeratingWithState:objects:count:", &v65, v84, 16LL);
      v46 = v62;
      v78 = v61;
    }
    while ( v61 );
  }
  return objc_autoreleaseReturnValue(v63);
}

//----- (0000000100398DE1) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController getOrderCodeArrWhenSortByWenCai](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // r15
  unsigned __int64 v9; // r12
  NSDictionary *v21; // rax
  NSDictionary *v22; // rbx

  v2 = -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "sortedArrayUsingComparator:");
  v29 = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v28 = objc_retainAutoreleasedReturnValue(v5);
  if ( (__int64)-[ThumbnailBaseTableViewController begin](self, "begin") < 0 )
    v6 = 0LL;
  else
    v6 = -[ThumbnailBaseTableViewController begin](self, "begin");
  v7 = v29;
  if ( v6 < (char *)-[ThumbnailBaseTableViewController count](self, "count") + (unsigned __int64)v6 )
  {
    v26 = v6;
    do
    {
      v8 = _objc_msgSend(v7, "count");
      if ( v9 >= (unsigned __int64)v8 )
        break;
      v10 = _objc_msgSend(v7, "objectAtIndex:", v9);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
      if ( (unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12) )
      {
        v13 = _objc_msgSend(v11, "code");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        if ( _objc_msgSend(v14, "length") )
        {
          v15 = _objc_msgSend(v11, "market");
          v16 = objc_retainAutoreleasedReturnValue(v15);
          v17 = _objc_msgSend(v16, "length");
          v7 = v29;
          if ( v17 )
          {
            v30[0] = (__int64)CFSTR("StockCode");
            v18 = _objc_msgSend(v11, "code");
            v27 = objc_retainAutoreleasedReturnValue(v18);
            v31[0] = (__int64)v27;
            v30[1] = (__int64)CFSTR("Market");
            v19 = _objc_msgSend(v11, "market");
            v20 = objc_retainAutoreleasedReturnValue(v19);
            v31[1] = (__int64)v20;
            v21 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v31, v30, 2LL);
            v22 = objc_retainAutoreleasedReturnValue(v21);
            _objc_msgSend(v28, "addObject:", v22);
            v7 = v29;
          }
        }
        else
        {
          v7 = v29;
        }
      }
      v23 = -[ThumbnailBaseTableViewController count](self, "count");
    }
    while ( v24 < &v23[(_QWORD)v26] );
  }
  return objc_autoreleaseReturnValue(v28);
}

//----- (0000000100399148) ----------------------------------------------------
id __fastcall sub_100399148(__int64 a1, void *a2, void *a3, double a4)
{

  objc_retain(a2);
  v5 = objc_retain(a3);
  v6 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
  v35 = v7;
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
  {
    v8 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8) )
    {
      v34 = v5;
      v9 = _objc_msgSend(*(id *)(a1 + 32), "wenCaiSortIdentifier");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", off_1012E26A0[0]);
      v13 = *(void **)(v12 + 32);
      if ( v11 )
      {
        v14 = "rate";
        _objc_msgSend(v35, "rate");
      }
      else
      {
        v17 = _objc_msgSend(*(id *)(v12 + 32), "wenCaiSortIdentifier");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v19 = (unsigned __int8)_objc_msgSend(v18, "isEqualToString:", off_1012E2698[0]);
        v21 = *(void **)(v20 + 32);
        if ( v19 )
        {
          v22 = _objc_msgSend(v35, "companyCount");
          v5 = v34;
          v23 = _objc_msgSend(v34, "companyCount");
          v15 = _objc_msgSend(v21, "compareInteger:withInteger:", v22, v23);
          goto LABEL_9;
        }
        v26 = _objc_msgSend(*(id *)(v20 + 32), "wenCaiSortIdentifier");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = (unsigned __int8)_objc_msgSend(v27, "isEqualToString:", off_1012E26A8[0]);
        v13 = *(void **)(v29 + 32);
        if ( v28 )
        {
          v14 = "stockCount";
          _objc_msgSend(v35, "stockCount");
        }
        else
        {
          v30 = _objc_msgSend(*(id *)(v29 + 32), "wenCaiSortIdentifier");
          v31 = objc_retainAutoreleasedReturnValue(v30);
          v32 = (unsigned __int8)_objc_msgSend(v31, "isEqualToString:", off_1012E2690[0]);
          if ( !v32 )
          {
            v16 = 0LL;
            v5 = v34;
            goto LABEL_10;
          }
          v13 = *(void **)(v33 + 32);
          v14 = "value";
          _objc_msgSend(v35, "value");
        }
      }
      v5 = v34;
      _objc_msgSend(v34, v14);
      v15 = _objc_msgSend(v13, "compareDouble:withDouble:", a4, a4);
LABEL_9:
      v16 = v15;
      goto LABEL_10;
    }
  }
  v16 = 0LL;
LABEL_10:
  return v16;
}

//----- (00000001003993B4) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController requestDetailData](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx
  NSArray *v5; // rax
  NSArray *v6; // rbx
  HXStockModel *v8; // rax
  HXStockModel *v9; // rbx
  NSArray *v10; // rax
  NSArray *v11; // r13
  SEL v12; // r12
  NSDictionary *v15; // rax
  HXTableRequestModule *v16; // rax
  HXTableRequestModule *v17; // r13
  NSArray *v20; // rax
  NSArray *v21; // r15
  NSNumber *v22; // rax
  NSNumber *v23; // rbx
  NSDictionary *v25; // rax
  NSDictionary *v26; // rbx
  HXStockModel *v30; // rax
  HXStockModel *v31; // rbx
  _QWORD v32[4]; // [rsp+8h] [rbp-A8h] BYREF
  id to; // [rsp+28h] [rbp-88h] BYREF
  _QWORD v34[4]; // [rsp+30h] [rbp-80h] BYREF
  id location; // [rsp+58h] [rbp-58h] BYREF

  v2 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
  {
    objc_initWeak(&location, self);
    v5 = -[ThumbnailZhongCangStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "count");
    if ( v7 )
    {
      v8 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      -[HXStockModel setMainRequestDidBack:](v9, "setMainRequestDidBack:", 0LL);
      v37[0] = (__int64)CFSTR("datatype");
      v10 = -[ThumbnailZhongCangStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v38[0] = (__int64)v11;
      v37[1] = (__int64)CFSTR("StockCodesAndMarket");
      v13 = _objc_msgSend(self, v12);
      v14 = objc_retain(v13);
      v38[1] = (__int64)v14;
      v15 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v38, v37, 2LL);
      objc_retainAutoreleasedReturnValue(v15);
      v16 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
      v17 = objc_retain(v16);
      v32[0] = _NSConcreteStackBlock;
      v32[1] = 3254779904LL;
      v32[2] = sub_100399764;
      v32[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      -[HXTableRequestModule request:params:callBack:](v17, "request:params:callBack:", 4LL, v18, v32);
      objc_destroyWeak(&to);
    }
    v20 = -[ThumbnailZhongCangStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 84LL);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    _objc_msgSend(v21, "containsObject:", v23);
    if ( v24 )
    {
      v34[0] = _NSConcreteStackBlock;
      v34[1] = 3254779904LL;
      v34[2] = sub_10039982F;
      v34[3] = &unk_1012DAED8;
      objc_copyWeak(&v35, &location);
      -[ThumbnailBaseTableWithHangYeViewController requestSuoShuHangYeDetail:](self, "requestSuoShuHangYeDetail:", v34);
      objc_destroyWeak(&v35);
    }
    v25 = -[ThumbnailZhongCangStocksPoolTableViewController iWenCaiItemParams](self, "iWenCaiItemParams");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27 = _objc_msgSend(v26, "count");
    if ( v27 )
    {
      v28 = -[ThumbnailZhongCangStocksPoolTableViewController getWenCaiDataWithOrderCodeArr](
              self,
              "getWenCaiDataWithOrderCodeArr");
      v29 = objc_retainAutoreleasedReturnValue(v28);
      v30 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v31 = objc_retainAutoreleasedReturnValue(v30);
      -[HXStockModel setIWenCaiData:](v31, "setIWenCaiData:", v29);
    }
    objc_destroyWeak(&location);
  }
}

//----- (0000000100399764) ----------------------------------------------------
void __fastcall sub_100399764(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // rax

  v9 = objc_retain(a3);
  v10 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setMainRequestDidBack:", 1LL);
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v7, "dealWithRequestData:extension:", v10, v9);
}

//----- (000000010039982F) ----------------------------------------------------
void __fastcall sub_10039982F(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v2 = objc_retain(a2);
  if ( _objc_msgSend(v2, "count") )
  {
    _objc_msgSend(v2, "copy");
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    v4 = _objc_msgSend(WeakRetained, "stockModel");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "setSuoShuHangYeData:", v6);
    v14 = v2;
    v8 = objc_loadWeakRetained((id *)(a1 + 32));
    v9 = _objc_msgSend(v8, "stockModel");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11(v10, "mainRequestDidBack");
    v2 = v14;
    if ( v12 )
    {
      v13 = objc_loadWeakRetained((id *)(a1 + 32));
      _objc_msgSend(v13, "reloadData:", 0LL);
    }
  }
}

//----- (0000000100399962) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController dealWithRequestData:extension:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{

  v4 = objc_retain(a3);
  v6 = _objc_msgSend(v5, "stockModel");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  _objc_msgSend(v7, "setMainStockTableViewDataArray:", v4);
  _objc_msgSend(v8, "reloadData:", 0LL);
  _objc_msgSend(v9, "setTableHasData:", 1LL);
  _objc_msgSend(v10, "setIsRequestFromZero:", 0LL);
  if ( (unsigned __int8)_objc_msgSend(v11, "isTableSwitched") )
  {
    v13 = _objc_msgSend(v12, "myTable");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v16 = _objc_msgSend(v15, "getScorllPosition");
    _objc_msgSend(v14, "scrollRowToVisible:", v16);
    _objc_msgSend(v17, "setIsTableSwitched:", 0LL);
  }
  v18 = _objc_msgSend(v12, "stockModel");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "mainStockTableViewDataArray");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  _objc_msgSend(v22, "setOrderCodeList:", v21);
  _objc_msgSend(v23, "order");
}

//----- (0000000100399AC1) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController getWenCaiDataWithOrderCodeArr](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // rbx
  NSMutableArray *v9; // rax
  NSMutableArray *v10; // r14
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  NSMutableArray *v23; // rax
  NSMutableArray *v24; // rbx
  bool v26; // zf
  NSMutableArray *v27; // rax
  NSMutableArray *v28; // r15
  ThumbnailZhongCangStocksPoolTableViewController *v35; // r12
  ThumbnailZhongCangStocksPoolTableViewController *v36; // rbx
  SEL v41; // r12
  NSMutableArray *v51; // rax
  NSMutableArray *v52; // rbx
  unsigned __int64 v54; // r12
  bool v55; // cc
  NSMutableArray *v56; // rax
  NSMutableArray *v57; // rax
  id (*v64)(id, SEL, ...); // r12
  NSNumber *v74; // rax
  NSNumber *v75; // rbx
  id (*v85)(id, SEL, ...); // r12
  __CFString *v92; // [rsp+70h] [rbp-D0h]
  __CFString *v93; // [rsp+78h] [rbp-C8h]
  __CFString *v94; // [rsp+80h] [rbp-C0h]
  __CFString *v95; // [rsp+88h] [rbp-B8h]
  SEL v97; // [rsp+D0h] [rbp-70h]

  v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v96 = objc_retainAutoreleasedReturnValue(v3);
  v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  if ( !v6 )
    return objc_autoreleaseReturnValue(v96);
  v92 = off_1012E2690[0];
  v95 = off_1012E2698[0];
  v93 = off_1012E26A0[0];
  v94 = off_1012E26A8[0];
  v7 = 0LL;
  do
  {
    v9 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v90 = v7;
    v12 = v11(v10, "thsDictionaryAtIndex:", v7);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    ((void (__cdecl *)(id))v8)(v10);
    v15 = v14(v13, "thsStringForKey:", CFSTR("StockCode"));
    v16 = v8;
    v17 = objc_retainAutoreleasedReturnValue(v15);
    v91 = v13;
    v19 = v18(v13, "thsStringForKey:", CFSTR("Market"));
    v100 = objc_retainAutoreleasedReturnValue(v19);
    v98 = v17;
    v20 = "length";
    v22 = v16;
    if ( !v21(v17, "length") )
      goto LABEL_15;
    v20 = "length";
    if ( !_objc_msgSend(v100, "length") )
      goto LABEL_15;
    v23 = -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v20 = "count";
    v25 = _objc_msgSend(v24, "count");
    v26 = v25 == 0LL;
    if ( v26 )
      goto LABEL_15;
    while ( 1 )
    {
      v27 = -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v30 = _objc_msgSend(v28, "objectAtIndex:", v29);
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v32 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
      v33 = (unsigned __int8)_objc_msgSend(v31, "isKindOfClass:", v32);
      v34 = v31;
      v36 = v35;
      if ( v33 )
      {
        v37 = _objc_msgSend(v31, "code");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        if ( !_objc_msgSend(v38, "length") )
        {
          v36 = self;
          goto LABEL_14;
        }
        v39 = _objc_msgSend(v31, "market");
        v40 = objc_retainAutoreleasedReturnValue(v39);
        _objc_msgSend(v40, v41);
        v42 = v38;
        v36 = self;
        if ( v43 )
        {
          v44 = _objc_msgSend(v34, "code");
          v45 = objc_retainAutoreleasedReturnValue(v44);
          if ( !(unsigned __int8)_objc_msgSend(v98, "isEqualToString:", v45) )
          {
            goto LABEL_14;
          }
          v47 = _objc_msgSend(v34, "market");
          v101 = v34;
          v48 = objc_retainAutoreleasedReturnValue(v47);
          v49 = (unsigned __int8)_objc_msgSend(v100, "isEqualToString:", v48);
          v36 = self;
          if ( v49 )
            break;
        }
      }
LABEL_14:
      v51 = -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](v36, "completeCodeAndDataArr");
      v52 = objc_retainAutoreleasedReturnValue(v51);
      v53 = _objc_msgSend(v52, "count");
      v20 = "completeCodeAndDataArr";
      v55 = (unsigned __int64)v53 <= v54;
      if ( v55 )
        goto LABEL_15;
    }
    v60 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
            &OBJC_CLASS___HXTools,
            "getMarketAndCodeByAppendingMarket:andStockCode:",
            v100,
            v98);
    v61 = (char *)objc_retainAutoreleasedReturnValue(v60);
    v20 = "length";
    if ( _objc_msgSend(v61, "length") )
    {
      v62 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v65 = v64(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v66 = objc_retainAutoreleasedReturnValue(v65);
      v99 = v63;
      v97 = v61;
      v67(v63, "setObject:forKey:", v61, v66);
      v68 = v101;
      v69(v101, "value");
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v101, "value");
        v71 = _objc_msgSend(v70, "numberWithDouble:");
        v72 = objc_retainAutoreleasedReturnValue(v71);
        _objc_msgSend(v99, "setObject:forKey:", v72, v92);
      }
      if ( _objc_msgSend(v101, "companyCount") != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v73 = _objc_msgSend(v101, "companyCount");
        v74 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v73);
        v75 = objc_retainAutoreleasedReturnValue(v74);
        _objc_msgSend(v76, "setObject:forKey:", v75, v95);
        v68 = v101;
      }
      v77 = v68;
      _objc_msgSend(v68, "rate");
      v79 = v78;
      v80 = "stockCount";
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v77, "rate");
        v82 = _objc_msgSend(v81, "numberWithDouble:");
        v83 = objc_retainAutoreleasedReturnValue(v82);
        _objc_msgSend(v79, "setObject:forKey:", v83, v93);
      }
      _objc_msgSend(v77, v80);
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v77, v84);
        v86 = v85(&OBJC_CLASS___NSNumber, "numberWithDouble:");
        v87 = objc_retainAutoreleasedReturnValue(v86);
        v88(v79, "setObject:forKey:", v87, v94);
      }
      v20 = "addObject:";
      _objc_msgSend(v96, "addObject:", v79);
      v61 = (char *)v97;
    }
LABEL_15:
    ((void (__fastcall *)(id, const char *))v22)(v100, v20);
    ((void (__fastcall *)(id))v22)(v98);
    ((void (__fastcall *)(id))v22)(v91);
    v7 = v90 + 1;
    v56 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v57 = objc_retainAutoreleasedReturnValue(v56);
    v8 = v22;
    v58 = _objc_msgSend(v57, "count");
    ((void (__fastcall *)(__int64))v8)(v59);
  }
  while ( (unsigned __int64)v58 > v90 + 1 );
  return objc_autoreleaseReturnValue(v96);
}

//----- (000000010039A2B0) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController tableView:rowViewForRow:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{

  v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRowView_Highlight);
  v5 = _objc_msgSend(v4, "init");
  return objc_autoreleaseReturnValue(v5);
}

//----- (000000010039A2D9) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController tableViewSelectionIsChanging:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v12 = v7;
  v8 = _objc_msgSend(v7, "selectedRowIndexs");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v6, "isEqualToIndexSet:", v9);
  if ( !v10 )
  {
    _objc_msgSend(v12, "setSelectedRowIndexs:", v6);
    v11 = _objc_msgSend(v6, "lastIndex");
    _objc_msgSend(v12, "actionForTableViewSelectionDidChange:", v11);
    _objc_msgSend(v12, "setTableViewSelectionIsChanging:", 1LL);
  }
}

//----- (000000010039A3C2) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController tableViewSelectionDidChange:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToIndexSet:", v8) )
  {
    -[HXBaseTableViewController tableViewSelectionIsChanging](self, "tableViewSelectionIsChanging");
    if ( v9 )
      goto LABEL_8;
  }
  else
  {
  }
  -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", v6);
  if ( (unsigned __int8)-[HXBaseTableViewController postSelectedRowDidChangedNotify](
                          self,
                          "postSelectedRowDidChangedNotify") )
  {
    v10 = _objc_msgSend(v6, "lastIndex");
    -[ThumbnailZhongCangStocksPoolTableViewController actionForTableViewSelectionDidChange:](
      self,
      "actionForTableViewSelectionDidChange:",
      v10);
  }
  -[HXBaseTableViewController setPostSelectedRowDidChangedNotify:](self, "setPostSelectedRowDidChangedNotify:", 1LL);
LABEL_8:
  -[HXBaseTableViewController setTableViewSelectionIsChanging:](self, "setTableViewSelectionIsChanging:", 0LL);
}

//----- (000000010039A4E7) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailZhongCangStocksPoolTableViewController numberOfRowsInTableView:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  return (signed __int64)-[ThumbnailBaseTableViewController allCodesNum](self, "allCodesNum", a3);
}

//----- (000000010039A4F9) ----------------------------------------------------
id __cdecl -[ThumbnailZhongCangStocksPoolTableViewController tableView:viewForTableColumn:row:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  NSNumber *v72; // rax
  NSNumber *v73; // rbx
  bool v84; // zf
  SEL *v85; // rcx
  TextMarkManager *v88; // rax
  TextMarkManager *v89; // rbx
  SEL v112; // r12
  SEL v151; // r12
  SEL v154; // r12
  SEL v157; // r12

  v195 = (id)a5;
  objc_retain(a3);
  v6 = _objc_msgSend(a4, "identifier");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v194 = self;
  v9 = _objc_msgSend(v8, "makeViewWithIdentifier:owner:", v7, self);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = _objc_msgSend(&OBJC_CLASS___ThumbnailTableCellView, "class");
  v193 = v10;
  if ( !(unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v12) )
  {
    v81 = v193;
    goto LABEL_16;
  }
  if ( (__int64)_objc_msgSend(v194, "begin") > (__int64)v195
    || (v14 = v13(v194, "begin"), v14 + v15(v194, "count") <= (unsigned __int64)v195) )
  {
    _objc_msgSend(v193, "clearAllDatas");
    v81 = v193;
LABEL_16:
    objc_retain(v81);
    goto LABEL_17;
  }
  v17 = (char *)v195 - v16(v194, "begin");
  v19 = (void *)v18(v194, "stockModel");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v22 = (void *)v21(v20, "mainStockTableViewDataArray");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = (void *)v24(v23, "mutableCopy");
  v185 = v25;
  v195 = v17;
  v27 = (void *)v26(v25, "thsDictionaryAtIndex:", v17);
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v30 = (id)v29(v28, "mutableCopy");
  if ( v31(v30, "count") )
  {
    v33 = (void *)v32(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v34 = objc_retainAutoreleasedReturnValue(v33);
    v36 = (void *)v35(v30, "thsStringForKey:", v34);
    v37 = objc_retainAutoreleasedReturnValue(v36);
    v39 = (void *)v38(&OBJC_CLASS___HXTools, "getCodeString:", v37);
    v183 = objc_retainAutoreleasedReturnValue(v39);
    v180 = v37;
    v41 = (void *)v40(&OBJC_CLASS___HXTools, "getMarketString:", v37);
    v188 = objc_retainAutoreleasedReturnValue(v41);
    v43 = (void *)v42(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
    v44 = objc_retainAutoreleasedReturnValue(v43);
    v46 = (void *)v45(v30, "thsStringForKey:", v44);
    v181 = objc_retainAutoreleasedReturnValue(v46);
    v48 = (void *)v47(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
    v49 = objc_retainAutoreleasedReturnValue(v48);
    v51 = (void *)v50(v30, "thsNumberForKey:", v49);
    v186 = objc_retainAutoreleasedReturnValue(v51);
    v53 = (void *)v52(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v54 = objc_retainAutoreleasedReturnValue(v53);
    v56 = (void *)v55(v30, "thsNumberForKey:", v54);
    v184 = objc_retainAutoreleasedReturnValue(v56);
    v191 = (id)v57(&OBJC_CLASS___HXTools, "getPrecisionTypeWithMarket:Code:", v188, v183);
    v190 = COERCE_ID(v58(&OBJC_CLASS___HXTools, "getVolumeUintWithMarket:", v188));
    v60 = v59(v194, "sortID");
    v62 = v61(&OBJC_CLASS___ThumbnailUtils, "getNewSortIDForNormalTable:", v60);
    v64 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithLong:", v62);
    v65 = objc_retainAutoreleasedReturnValue(v64);
    v187 = v30;
    v67 = (void *)v66(v30, "thsNumberForKey:", v65);
    v68 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v67));
    v189 = v62;
    if ( v62 == 199112 )
    {
      v69 = +[ThumbnailUtils getSortItemDataWithSortID:dataDic:](
              &OBJC_CLASS___ThumbnailUtils,
              "getSortItemDataWithSortID:dataDic:",
              199112LL,
              v187);
      v70 = objc_retainAutoreleasedReturnValue(v69);
      v71 = v70;
      if ( v70 && (v68 == 0.0 || !(unsigned __int8)_objc_msgSend(v70, "isEqualToNumber:", *(_QWORD *)&v68)) )
      {
        v192 = COERCE_DOUBLE(objc_retain(v71));
        v72 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", 199112LL);
        v73 = objc_retainAutoreleasedReturnValue(v72);
        v68 = v192;
        _objc_msgSend(v187, "setObject:forKey:", *(_QWORD *)&v192, v73);
        _objc_msgSend(v185, "setObject:atIndexedSubscript:", v74, v195);
        v75 = _objc_msgSend(v194, "stockModel");
        v76 = objc_retainAutoreleasedReturnValue(v75);
        v77(v76, "setMainStockTableViewDataArray:", v185);
      }
    }
    if ( _objc_msgSend(v194, "sortID") && _objc_msgSend(v194, "sortID") != (id)12345670 )
    {
      LODWORD(v192) = 0;
    }
    else
    {
      v78 = _objc_msgSend(v194, "wenCaiSortIdentifier");
      v79 = objc_retainAutoreleasedReturnValue(v78);
      v80 = _objc_msgSend(v79, "length");
      LOBYTE(v80) = v80 != 0LL;
      LODWORD(v192) = (_DWORD)v80;
    }
    v84 = (unsigned __int8)+[SelfStock isSelfStock:](&OBJC_CLASS___SelfStock, "isSelfStock:", v180) == 0;
    v85 = &selRef_markedTextColor;
    if ( v84 )
      v85 = &selRef_normalTextColor;
    v86 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v85);
    v87 = objc_retainAutoreleasedReturnValue(v86);
    v88 = +[TextMarkManager sharedInstance](&OBJC_CLASS___TextMarkManager, "sharedInstance");
    v89 = objc_retainAutoreleasedReturnValue(v88);
    v91 = -[TextMarkManager getStockCodeColorIdx:](v89, "getStockCodeColorIdx:", v90);
    v182 = *(id *)&v68;
    if ( v91 )
    {
      v92 = +[TextMarkMenuItemView getColorForDotsAtIndex:](
              &OBJC_CLASS___TextMarkMenuItemView,
              "getColorForDotsAtIndex:",
              v91);
      v93 = objc_retainAutoreleasedReturnValue(v92);
    }
    else
    {
      v93 = v87;
    }
    v94 = _objc_msgSend(v193, "stockName");
    v95 = objc_retainAutoreleasedReturnValue(v94);
    v177 = v93;
    _objc_msgSend(v95, "setTextColor:", v93);
    v96 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v97 = objc_retainAutoreleasedReturnValue(v96);
    v98 = _objc_msgSend(v193, "stockCode");
    v99 = objc_retainAutoreleasedReturnValue(v98);
    _objc_msgSend(v99, "setTextColor:", v97);
    v100(v99);
    v101(v97);
    v102 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
             &OBJC_CLASS___ThumbnailUtils,
             "getSortItemColor:sortItem:zuoShou:",
             10LL,
             v184,
             v186);
    v103 = objc_retainAutoreleasedReturnValue(v102);
    v104 = _objc_msgSend(v193, "currentPrice");
    v105 = objc_retainAutoreleasedReturnValue(v104);
    _objc_msgSend(v105, "setTextColor:", v103);
    v106(v105);
    v107(v103);
    v108 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v181);
    v109 = objc_retainAutoreleasedReturnValue(v108);
    v110 = _objc_msgSend(v193, "stockName");
    v111 = objc_retainAutoreleasedReturnValue(v110);
    _objc_msgSend(v111, "setStringValue:", v109);
    v113 = _objc_msgSend(&OBJC_CLASS___HXTools, v112, v183);
    v114 = objc_retainAutoreleasedReturnValue(v113);
    v115 = _objc_msgSend(v193, "stockCode");
    v116 = objc_retainAutoreleasedReturnValue(v115);
    _objc_msgSend(v116, "setStringValue:", v114);
    v117(v114);
    v118 = v190;
    v119 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
             &OBJC_CLASS___ThumbnailUtils,
             "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
             v184,
             10LL,
             v191,
             v188,
             *(double *)&v190);
    v120 = objc_retainAutoreleasedReturnValue(v119);
    v121 = _objc_msgSend(v193, "currentPrice");
    v122 = objc_retainAutoreleasedReturnValue(v121);
    _objc_msgSend(v122, "setStringValue:", v120);
    v123(v122);
    v124(v120);
    v125 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    objc_retainAutoreleasedReturnValue(v125);
    if ( v182 && !LOBYTE(v192) )
    {
      v126 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
               &OBJC_CLASS___ThumbnailUtils,
               "getSortItemColor:sortItem:zuoShou:",
               v189,
               v182,
               v186);
      objc_retainAutoreleasedReturnValue(v126);
    }
    v128 = _objc_msgSend(v193, "priceChange");
    v129 = objc_retainAutoreleasedReturnValue(v128);
    _objc_msgSend(v129, "setTextColor:", v130);
    if ( LOBYTE(v192) )
    {
      v190 = &OBJC_CLASS___ThumbnailUtils;
      v132 = _objc_msgSend(v194, "stockModel");
      v191 = objc_retainAutoreleasedReturnValue(v132);
      v133 = _objc_msgSend(v191, "iWenCaiData");
      v178 = objc_retainAutoreleasedReturnValue(v133);
      v134 = _objc_msgSend(v194, "tableKey");
      v179 = objc_retainAutoreleasedReturnValue(v134);
      v135 = _objc_msgSend(v194, "wenCaiSortIdentifier");
      v136 = objc_retainAutoreleasedReturnValue(v135);
      v138 = v137;
      v176 = v137;
      v139 = +[ThumbnailUtils getIWenCaiStringValueWithDataSource:index:tableKey:identifier:textColor:](
               &OBJC_CLASS___ThumbnailUtils,
               "getIWenCaiStringValueWithDataSource:index:tableKey:identifier:textColor:",
               v178,
               v195,
               v179,
               v136,
               &v176);
      v140 = (char *)objc_retainAutoreleasedReturnValue(v139);
      v141 = objc_retain(v176);
      v131 = v141;
    }
    else if ( v182 )
    {
      v118 = v190;
      v143 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
               &OBJC_CLASS___ThumbnailUtils,
               "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
               v182,
               v189,
               v191,
               v188,
               *(double *)&v190);
      v140 = (char *)objc_retainAutoreleasedReturnValue(v143);
    }
    else
    {
      v140 = obj;
    }
    v191 = v131;
    if ( v189 == 84 )
    {
      v144 = _objc_msgSend(v194, "getHangYeSortItemDataWithSortID:index:", 84LL, v195);
      v145 = objc_retainAutoreleasedReturnValue(v144);
      v195 = v145;
    }
    else
    {
      v195 = v140;
    }
    v146 = v193;
    v147 = _objc_msgSend(v193, "priceChange");
    v148 = objc_retainAutoreleasedReturnValue(v147);
    _objc_msgSend(v194, "resetHangYeItemDataFont:textField:", v189, v148);
    v149 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v195);
    v150 = objc_retainAutoreleasedReturnValue(v149);
    v152 = _objc_msgSend(v146, v151);
    v153 = objc_retainAutoreleasedReturnValue(v152);
    _objc_msgSend(v153, "setStringValue:", v150);
    v155 = _objc_msgSend(v146, v154);
    v156 = objc_retainAutoreleasedReturnValue(v155);
    _objc_msgSend(v156, "setLineBreakMode:", 0LL);
    v158 = _objc_msgSend(v146, v157);
    v159 = objc_retainAutoreleasedReturnValue(v158);
    _objc_msgSend(v159, "setToolTip:", 0LL);
    if ( LOBYTE(v192) )
    {
      v160 = v193;
      v161 = _objc_msgSend(v193, "priceChange");
      v162 = objc_retainAutoreleasedReturnValue(v161);
      _objc_msgSend(v162, "setLineBreakMode:", 4LL);
      v163 = _objc_msgSend(v160, "priceChange");
      v164 = objc_retainAutoreleasedReturnValue(v163);
      v165 = _objc_msgSend(v164, "font");
      v166 = objc_retainAutoreleasedReturnValue(v165);
      _objc_msgSend(v194, "widthOfString:withFont:", v195, v166);
      v194 = v118;
      v167 = _objc_msgSend(v160, "priceChange");
      v168 = (const char *)objc_retainAutoreleasedReturnValue(v167);
      v169 = (char *)v168;
      if ( v168 )
      {
        objc_msgSend_stret(&v174, v168, "bounds");
        v170 = *(double *)&v175 + -4.0;
      }
      else
      {
        v175 = 0LL;
        v174 = 0LL;
        v170 = -4.0;
      }
      v192 = v170;
      if ( *(double *)&v194 >= v192 )
      {
        v171 = _objc_msgSend(v193, "priceChange");
        v172 = objc_retainAutoreleasedReturnValue(v171);
        v173(v172, "setToolTip:", v195);
      }
    }
    objc_retain(v193);
    v30 = v187;
  }
  else
  {
    _objc_msgSend(v193, "clearAllDatas");
    objc_retain(v193);
  }
LABEL_17:
  v82 = v193;
  return objc_autoreleaseReturnValue(v82);
}

//----- (000000010039B26E) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailZhongCangStocksPoolTableViewController compareDouble:withDouble:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        double a3,
        double a4)
{
  NSString *v4; // rax
  NSString *v5; // rbx
  NSString *v12; // rax
  NSString *v13; // rbx
  signed __int64 v15; // r12
  __m128d v18; // [rsp+10h] [rbp-40h]

  v18.f64[0] = a4;
  v4 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "isEqualToString:", CFSTR("D"));
  if ( v6 )
  {
    v7 = _mm_cmpeq_sd(*(__m128d *)&a3, (__m128d)0x7FEFFFFFFFFFFFFFuLL).f64[0];
    *(_QWORD *)&v8 = ~*(_QWORD *)&v7 & *(_QWORD *)&a3 | *(_QWORD *)&v7 & 0xFFEFFFFFFFFFFFFFLL;
    v9 = _mm_cmpeq_sd((__m128d)0x7FEFFFFFFFFFFFFFuLL, v18).f64[0];
    *(_QWORD *)&v10 = ~*(_QWORD *)&v9 & *(_QWORD *)&v18.f64[0] | *(_QWORD *)&v9 & 0xFFEFFFFFFFFFFFFFLL;
  }
  else
  {
    v10 = a4;
    v8 = a3;
  }
  if ( v8 <= v10 )
  {
    if ( v10 <= v8 )
    {
      v11 = 0;
      v19 = 0LL;
    }
    else
    {
      v19 = -1LL;
      v11 = 0;
    }
  }
  else
  {
    v19 = 1LL;
    v11 = 1;
  }
  v12 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", CFSTR("D"));
  if ( v11 || !v14 )
    return v19 | -(__int64)(v14 != 0);
  else
    return v15;
}

//----- (000000010039B3BD) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailZhongCangStocksPoolTableViewController compareInteger:withInteger:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3,
        signed __int64 a4)
{
  NSString *v5; // rax
  NSString *v6; // rax
  signed __int64 v9; // rbx
  NSString *v10; // rax
  NSString *v11; // r15
  signed __int64 v13; // r12

  v5 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("D"));
  v9 = 0x8000000000000001LL;
  if ( a4 != 0x7FFFFFFFFFFFFFFFLL )
    v9 = a4;
  if ( !v7 )
    v9 = a4;
  v10 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("D"));
  if ( v13 <= v9 && v12 )
    return v13 < v9;
  v15 = 1LL;
  if ( v13 <= v9 )
    v15 = -(__int64)(v13 < v9);
  return v15 | -(__int64)(v12 != 0);
}

//----- (000000010039B4C2) ----------------------------------------------------
NSString *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController tableKey](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSString *)self->super.super._quotaCount;
}

//----- (000000010039B4D3) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setTableKey:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._quotaCount, a3);
}

//----- (000000010039B4E7) ----------------------------------------------------
NSMutableArray *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController completeCodeAndDataArr](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSMutableArray *)self->super.super._quotaIndex;
}

//----- (000000010039B4F8) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setCompleteCodeAndDataArr:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._quotaIndex, a3);
}

//----- (000000010039B50C) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setBasicHQCodeListRequestModule:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._visibleY, a3);
}

//----- (000000010039B520) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController basicHQDataTypes](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSArray *)objc_getProperty(self, a2, 352LL, 0);
}

//----- (000000010039B533) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setBasicHQDataTypes:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 352LL);
}

//----- (000000010039B544) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController orderHQDataTypes](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSArray *)objc_getProperty(self, a2, 360LL, 0);
}

//----- (000000010039B557) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setOrderHQDataTypes:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 360LL);
}

//----- (000000010039B568) ----------------------------------------------------
NSDictionary *__cdecl -[ThumbnailZhongCangStocksPoolTableViewController iWenCaiItemParams](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSDictionary *)objc_getProperty(self, a2, 368LL, 0);
}

//----- (000000010039B57B) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController setIWenCaiItemParams:](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 368LL);
}

//----- (000000010039B58C) ----------------------------------------------------
void __cdecl -[ThumbnailZhongCangStocksPoolTableViewController .cxx_destruct](
        ThumbnailZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super.super._countDownTimerForScroll, 0LL);
  objc_storeStrong((id *)&self->super.super._thumbTableSrcollDirection, 0LL);
  objc_storeStrong(&self->super.super._invokeRowSwitchCallBack, 0LL);
  objc_storeStrong((id *)&self->super.super._visibleY, 0LL);
  objc_storeStrong((id *)&self->super.super._quotaIndex, 0LL);
  objc_storeStrong((id *)&self->super.super._quotaCount, 0LL);
}

