NSString *__cdecl -[ZhongCangStockModel code](ZhongCangStockModel *self, SEL a2)
{
  return self->_code;
}

//----- (00000001001E3D93) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setCode:](ZhongCangStockModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_code, a3);
}

//----- (00000001001E3DA4) ----------------------------------------------------
NSString *__cdecl -[ZhongCangStockModel market](ZhongCangStockModel *self, SEL a2)
{
  return self->_market;
}

//----- (00000001001E3DAE) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setMarket:](ZhongCangStockModel *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_market, a3);
}

//----- (00000001001E3DBF) ----------------------------------------------------
double __cdecl -[ZhongCangStockModel value](ZhongCangStockModel *self, SEL a2)
{
  return self->_value;
}

//----- (00000001001E3DCA) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setValue:](ZhongCangStockModel *self, SEL a2, double a3)
{
  self->_value = a3;
}

//----- (00000001001E3DD5) ----------------------------------------------------
signed __int64 __cdecl -[ZhongCangStockModel companyCount](ZhongCangStockModel *self, SEL a2)
{
  return self->_companyCount;
}

//----- (00000001001E3DDF) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setCompanyCount:](ZhongCangStockModel *self, SEL a2, signed __int64 a3)
{
  self->_companyCount = a3;
}

//----- (00000001001E3DE9) ----------------------------------------------------
double __cdecl -[ZhongCangStockModel rate](ZhongCangStockModel *self, SEL a2)
{
  return self->_rate;
}

//----- (00000001001E3DF4) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setRate:](ZhongCangStockModel *self, SEL a2, double a3)
{
  self->_rate = a3;
}

//----- (00000001001E3DFF) ----------------------------------------------------
double __cdecl -[ZhongCangStockModel stockCount](ZhongCangStockModel *self, SEL a2)
{
  return self->_stockCount;
}

//----- (00000001001E3E0A) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel setStockCount:](ZhongCangStockModel *self, SEL a2, double a3)
{
  self->_stockCount = a3;
}

//----- (00000001001E3E15) ----------------------------------------------------
void __cdecl -[ZhongCangStockModel .cxx_destruct](ZhongCangStockModel *self, SEL a2)
{
  objc_storeStrong((id *)&self->_market, 0LL);
  objc_storeStrong((id *)&self->_code, 0LL);
}

