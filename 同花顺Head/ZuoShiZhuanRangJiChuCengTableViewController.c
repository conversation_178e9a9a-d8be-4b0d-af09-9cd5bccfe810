ZuoShiZhuanRangJiChuCengTableViewController *__cdecl -[ZuoShiZhuanRangJiChuCengTableViewController initWithNibName:bundle:](
        ZuoShiZhuanRangJiChuCengTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ZuoShiZhuanRangJiChuCengTableViewController *v4; // rax
  ZuoShiZhuanRangJiChuCengTableViewController *v5; // rbx

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangJiChuCengTableViewController;
  v4 = -[HXBaseTableViewController initWithNibName:bundle:](&v7, "initWithNibName:bundle:", a3, a4);
  v5 = v4;
  if ( v4 )
  {
    -[QuoteBaseTableViewController setBlockID:](v4, "setBlockID:", 52184LL);
    -[QuoteBaseTableViewController setTableID:](v5, "setTableID:", 788739LL);
    -[QuoteBaseTableViewController setTableInfo:](v5, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang_JiChuCeng"));
  }
  return v5;
}

//----- (00000001006458FF) ----------------------------------------------------
void __cdecl -[ZuoShiZhuanRangJiChuCengTableViewController viewDidLoad](
        ZuoShiZhuanRangJiChuCengTableViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZuoShiZhuanRangJiChuCengTableViewController;
  -[XinSanBanBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 52184LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 788739LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("XSB_ZuoShiZhuanRang_JiChuCeng"));
}

