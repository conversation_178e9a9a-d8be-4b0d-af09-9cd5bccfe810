//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "FrozenQuoteBaseTableViewController.h"

@class NSString;

@interface XinSanBanZhiShuTableViewController : FrozenQuoteBaseTableViewController
{
    NSString *_tableKey;
}


- (id)tableKey;
- (long long)frozenCount;
- (void)setOriginalSortInfoForMyFrozenTable;
- (void)myTableIsDoubleClicked:(id)arg1;
- (id)calculateItemsWithRequestData:(id)arg1;
- (void)dealWithRequestData:(id)arg1 extension:(id)arg2;
- (void)requestForMyTable;
- (void)initObjects;

@end

