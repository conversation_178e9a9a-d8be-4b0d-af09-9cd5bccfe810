//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <CorePlot/CPTScatterPlot.h>

@class CALayer;

@interface YiDongCPTScatterPlot : CPTScatterPlot
{
    CALayer *_animationLayer;
}


@property(retain, nonatomic) CALayer *animationLayer; // @synthesize animationLayer=_animationLayer;
- (BOOL)pointingDeviceUpEvent:(id)arg1 atPoint:(struct CGPoint)arg2;

@end

