//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "BanKuaiGeGuBaseTableViewController.h"

@class HXBaseScrollView, HXBaseView, NSTextField, NSTimer;

@interface XinSanBanIndexComponentStocksViewController : BanKuaiGeGuBaseTableViewController
{
    HXBaseView *_titleContainerView;
    NSTextField *_titleTF;
    HXBaseScrollView *_topTenScrollView;
    double _visibleY;
    NSTimer *_countDownTimerForScroll;
}


@property(retain, nonatomic) NSTimer *countDownTimerForScroll; // @synthesize countDownTimerForScroll=_countDownTimerForScroll;
@property(nonatomic) double visibleY; // @synthesize visibleY=_visibleY;
@property __weak HXBaseScrollView *topTenScrollView; // @synthesize topTenScrollView=_topTenScrollView;
@property __weak NSTextField *titleTF; // @synthesize titleTF=_titleTF;
@property __weak HXBaseView *titleContainerView; // @synthesize titleContainerView=_titleContainerView;
- (void)requestForTableViewData;
- (void)disableScroll;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
- (void)actionForFocusDidChange:(id)arg1;
- (void)viewFrameDidChange:(id)arg1;
- (void)tableIsScrolling:(id)arg1;
- (void)tableViewSelectionIsChanging:(id)arg1;
- (void)setViewState;
- (void)addNotificationForSelf;
- (void)dealloc;
- (void)viewDidDisappear;
- (void)viewDidLoad;

@end

