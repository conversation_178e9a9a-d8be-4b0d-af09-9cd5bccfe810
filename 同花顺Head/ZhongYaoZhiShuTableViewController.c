void __cdecl -[<PERSON><PERSON><PERSON><PERSON><PERSON>hiShuTableViewController initObjects](ZhongYaoZhiShuTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___Z<PERSON><PERSON>aoZhiShuTableViewController;
  -[QuoteBaseTableViewController initObjects](&v2, "initObjects");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 54149LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 65795LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("ZhongYaoZhiShuStock"));
}

//----- (0000000100053639) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableViewController requestForMyTable](ZhongYaoZhiShuTableViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx
  NSNumber *v5; // rax
  NSNumber *v7; // rax
  NSString *v9; // rax
  NSNumber *v12; // rax
  NSNumber *v15; // rax
  NSNumber *v16; // r14
  NSArray *v18; // rax
  NSArray *v19; // rbx
  NSDictionary *v21; // rax
  HXTableRequestModule *v27; // rax
  HXTableRequestModule *v28; // r14
  _QWORD v32[4]; // [rsp+8h] [rbp-E8h] BYREF
  id to; // [rsp+28h] [rbp-C8h] BYREF
  id location[7]; // [rsp+58h] [rbp-98h] BYREF
  _QWORD v40[6]; // [rsp+90h] [rbp-60h] BYREF

  -[QuoteBaseTableViewController deleteOrder](self, "deleteOrder");
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    -[QuoteBaseTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
    v2 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    if ( v3 )
    {
      location[1] = CFSTR("sortbegin");
      v4 = -[QuoteBaseTableViewController begin](self, "begin");
      v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v4);
      v34 = objc_retainAutoreleasedReturnValue(v5);
      v40[0] = v34;
      location[2] = CFSTR("sortcount");
      v6 = -[QuoteBaseTableViewController count](self, "count");
      v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v6);
      v35 = objc_retainAutoreleasedReturnValue(v7);
      *(_QWORD *)(v8 + 8) = v35;
      location[3] = CFSTR("sortorder");
      v9 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
      v36 = objc_retainAutoreleasedReturnValue(v9);
      *(_QWORD *)(v10 + 16) = v36;
      location[4] = CFSTR("sortid");
      v11 = -[HXBaseTableViewController sortID](self, "sortID");
      v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v11);
      v37 = objc_retainAutoreleasedReturnValue(v12);
      *(_QWORD *)(v13 + 24) = v37;
      location[5] = CFSTR("blockid");
      v14 = -[QuoteBaseTableViewController blockID](self, "blockID");
      v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v40[4] = v16;
      *(_QWORD *)(v17 + 40) = CFSTR("datatype");
      v18 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v40[5] = v19;
      v21 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v20, 6LL);
      v38 = objc_retainAutoreleasedReturnValue(v21);
      v22(v16);
      v23(v37);
      v24(v36);
      v25(v35);
      v26(v34);
      objc_initWeak(location, self);
      v27 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
      v28 = objc_retain(v27);
      v32[0] = _NSConcreteStackBlock;
      v32[1] = 3254779904LL;
      v32[2] = sub_100053987;
      v32[3] = &unk_1012DAED8;
      objc_copyWeak(&to, location);
      v29 = v38;
      -[HXTableRequestModule request:params:callBack:](v28, "request:params:callBack:", 5LL, v38, v32);
      v30(v28);
      objc_destroyWeak(&to);
      objc_destroyWeak(location);
      v31(v29);
    }
  }
}

//----- (0000000100053987) ----------------------------------------------------
__int64 __fastcall sub_100053987(__int64 a1, void *a2, void *a3)
{
  id (__cdecl *v4)(id); // r12
  id WeakRetained; // rbx

  v3 = objc_retain(a3);
  v5 = v4(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithRequestData:extension:", v5, v3);
  v7(v5);
  return v8(WeakRetained);
}

//----- (00000001000539F9) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableViewController myTableIsDoubleClicked:](
        ZhongYaoZhiShuTableViewController *self,
        SEL a2,
        id a3)
{
  HXFrozenTableView *v3; // rax
  HXFrozenTableView *v4; // rbx
  _BYTE *v5; // r13
  NSIndexSet *v6; // rax
  NSIndexSet *v7; // rbx
  HXStockModel *v9; // rax
  HXStockModel *v10; // rax
  NSArray *v11; // rax
  NSArray *v12; // rbx
  HXStockModel *v15; // rax
  NSArray *v16; // rax
  NSArray *v17; // r13
  NSNumber *v19; // rax
  NSNumber *v20; // rbx
  NSNumber *v28; // rax
  NSNumber *v30; // rax
  SEL v32; // r12
  SEL v35; // r12
  NSNumber *v38; // rax
  NSNumber *v40; // rax
  SEL v41; // r12
  NSNumber *v43; // rax
  NSString *v44; // rax
  NSNumber *v46; // rax
  NSDictionary *v48; // rax
  NSNumber *v56; // [rsp+20h] [rbp-70h]
  NSNumber *v57; // [rsp+28h] [rbp-68h]
  NSString *v58; // [rsp+30h] [rbp-60h]
  NSNumber *v59; // [rsp+38h] [rbp-58h]
  NSNumber *v60; // [rsp+40h] [rbp-50h]
  NSNumber *v61; // [rsp+48h] [rbp-48h]
  HXStockModel *v63; // [rsp+58h] [rbp-38h]
  NSDictionary *v65; // [rsp+60h] [rbp-30h]

  -[QuoteBaseTableViewController setRequestRowRange](self, "setRequestRowRange", a3);
  v3 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXFrozenTableView selectedRow](v4, "selectedRow");
  v6 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7);
  if ( v7 )
  {
    if ( v5 == (_BYTE *)-1LL )
      return;
    v64 = (char *)(v5 - (_BYTE *)-[QuoteBaseTableViewController begin](self, "begin"));
  }
  else
  {
    v64 = -[QuoteBaseTableViewController begin](self, "begin");
  }
  v9 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = -[HXStockModel mainStockTableViewDataArray](v10, "mainStockTableViewDataArray");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = (char *)_objc_msgSend(v12, "count");
  if ( v64 < v13 )
  {
    v15 = (HXStockModel *)-[HXBaseTableViewController stockModel](self, "stockModel");
    v63 = objc_retainAutoreleasedReturnValue(v15);
    v16 = (NSArray *)-[HXStockModel mainStockTableViewDataArray](v63, "mainStockTableViewDataArray");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18 = _objc_msgSend(v17, "objectAtIndexedSubscript:", v64);
    objc_retainAutoreleasedReturnValue(v18);
    v19 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v22 = _objc_msgSend(v21, "objectForKeyedSubscript:", v20);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    if ( v23 )
    {
      v25 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      _objc_msgSend(v26, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
      v27(v26);
      v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
      v60 = objc_retainAutoreleasedReturnValue(v28);
      v29 = -[QuoteBaseTableViewController tableID](self, "tableID");
      v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v29);
      v61 = objc_retainAutoreleasedReturnValue(v30);
      v31 = -[QuoteBaseTableViewController blockID](self, "blockID");
      v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, v32, v31);
      v54 = objc_retainAutoreleasedReturnValue(v33);
      v34 = -[HXBaseTableViewController sortID](self, "sortID");
      v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, v35, v34);
      v55 = objc_retainAutoreleasedReturnValue(v36);
      v37 = -[QuoteBaseTableViewController begin](self, "begin");
      v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v37);
      v56 = objc_retainAutoreleasedReturnValue(v38);
      v39 = -[QuoteBaseTableViewController count](self, "count");
      v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v39);
      v57 = objc_retainAutoreleasedReturnValue(v40);
      v42 = _objc_msgSend(self, v41);
      v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedLong:", &v64[(_QWORD)v42]);
      v62 = objc_retainAutoreleasedReturnValue(v43);
      v44 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
      v58 = objc_retainAutoreleasedReturnValue(v44);
      v45 = -[QuoteBaseTableViewController allCodesNum](self, "allCodesNum");
      v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v45);
      v59 = objc_retainAutoreleasedReturnValue(v46);
      v48 = _objc_msgSend(
              &OBJC_CLASS___NSDictionary,
              "dictionaryWithObjectsAndKeys:",
              v60,
              CFSTR("requesttype"),
              v61,
              CFSTR("TableID"),
              v54,
              CFSTR("blockid"),
              v55,
              CFSTR("sortid"),
              v56,
              CFSTR("sortbegin"),
              v57,
              CFSTR("sortcount"),
              v47,
              CFSTR("Index"),
              v23,
              CFSTR("SelectedCode"),
              v58,
              CFSTR("sortorder"),
              v59,
              CFSTR("totalnumber"),
              0LL);
      v65 = objc_retainAutoreleasedReturnValue(v48);
      v50 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v51 = objc_retainAutoreleasedReturnValue(v50);
      _objc_msgSend(v51, "postNotificationName:object:", CFSTR("DeliverQuotationTableDataNotification"), v65);
      v52(v51);
      v53(v65);
    }
  }
}

//----- (0000000100053F00) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableViewController dealWithRequestData:extension:](
        ZhongYaoZhiShuTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  SEL v5; // r12
  HXStockModel *v8; // rax
  HXStockModel *v9; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // r15
  signed int v16; // eax

  v19 = objc_retain(a4);
  v6 = _objc_msgSend(self, v5, a3);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXStockModel setMainStockTableViewDataArray:](v9, "setMainStockTableViewDataArray:", v7);
  v10(v7);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34056LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v19, "objectForKeyedSubscript:", v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = (unsigned int)_objc_msgSend(v14, "intValue");
  -[QuoteBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v16);
  v17(v14);
  v18(v12);
  -[QuoteBaseTableViewController reloadData:](self, "reloadData:", 0LL);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  -[QuoteBaseTableViewController setRequestFromZero:](self, "setRequestFromZero:", 0LL);
  -[QuoteBaseTableViewController setOrderCodeList](self, "setOrderCodeList");
  -[QuoteBaseTableViewController order](self, "order");
}

//----- (0000000100054051) ----------------------------------------------------
id __cdecl -[ZhongYaoZhiShuTableViewController calculateItemsWithRequestData:](
        ZhongYaoZhiShuTableViewController *self,
        SEL a2,
        id a3)
{
  NSNumber *v11; // rax
  NSNumber *v12; // rbx
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  NSNumber *v19; // rbx
  NSNumber *v25; // rax
  NSNumber *v26; // rax
  NSNumber *v27; // r12
  NSNumber *v28; // rax
  NSNumber *v29; // r13
  NSNumber *v30; // rax
  NSNumber *v31; // r13

  v4 = objc_retain(a3);
  v35 = _objc_msgSend(v4, "mutableCopy");
  if ( _objc_msgSend(v5, "count") )
  {
    v7 = 0LL;
    while ( 1 )
    {
      v8 = _objc_msgSend(v6, "objectAtIndexedSubscript:", v7);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
      if ( (unsigned __int8)_objc_msgSend(v9, "isKindOfClass:", v10) )
        break;
LABEL_11:
      if ( (unsigned __int64)_objc_msgSend(v24, "count") <= ++v7 )
        goto LABEL_26;
    }
    v38 = _objc_msgSend(v9, "mutableCopy");
    v36 = v9;
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = _objc_msgSend(v36, "objectForKeyedSubscript:", v12);
    objc_retainAutoreleasedReturnValue(v13);
    v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(v36, "objectForKeyedSubscript:", v15);
    v37 = objc_retainAutoreleasedReturnValue(v16);
    v34 = v17;
    if ( v17 && (_objc_msgSend(v17, "doubleValue"), v3 != 4294967295.0) )
    {
      _objc_msgSend(v18, "doubleValue");
      if ( v3 == 2147483648.0 )
        goto LABEL_8;
      _objc_msgSend(v21, "doubleValue");
      if ( !v37 )
        goto LABEL_8;
      v19 = 0LL;
      v20 = 0LL;
      if ( v3 == 0.0 )
        goto LABEL_10;
      _objc_msgSend(v37, "doubleValue");
      if ( v3 == 4294967295.0
        || (_objc_msgSend(v37, "doubleValue"), v3 == 2147483648.0)
        || (_objc_msgSend(v37, "doubleValue"), v3 == 0.0) )
      {
LABEL_8:
        v19 = 0LL;
      }
      else
      {
        _objc_msgSend(v34, "doubleValue");
        _objc_msgSend(v37, "doubleValue");
        _objc_msgSend(v37, "doubleValue");
        v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3 - v3);
        v3 = (v3 - v3) / v3 * 100.0;
        v19 = objc_retainAutoreleasedReturnValue(v25);
        v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3);
        v27 = objc_retainAutoreleasedReturnValue(v26);
        if ( v19 )
        {
          _objc_msgSend(v19, "doubleValue");
          if ( v3 != 4294967295.0 )
          {
            _objc_msgSend(v19, "doubleValue");
            if ( v3 != 2147483648.0 )
            {
              v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 264648LL);
              v29 = objc_retainAutoreleasedReturnValue(v28);
              _objc_msgSend(v38, "setObject:forKeyedSubscript:", v19, v29);
            }
          }
        }
        if ( v27 )
        {
          _objc_msgSend(v27, "doubleValue");
          if ( v3 != 4294967295.0 )
          {
            _objc_msgSend(v20, "doubleValue");
            if ( v3 != 2147483648.0 )
            {
              v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
              v31 = objc_retainAutoreleasedReturnValue(v30);
              _objc_msgSend(v38, "setObject:forKeyedSubscript:", v32, v31);
            }
          }
          goto LABEL_10;
        }
      }
      v20 = 0LL;
    }
    else
    {
      v19 = 0LL;
      v20 = 0LL;
    }
LABEL_10:
    v22 = v20;
    _objc_msgSend(v35, "setObject:atIndexedSubscript:", v38, v7);
    v9 = v36;
    goto LABEL_11;
  }
LABEL_26:
  return objc_autoreleaseReturnValue(v35);
}

//----- (00000001000544C8) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableViewController setOriginalSortInfoForMyFrozenTable](
        ZhongYaoZhiShuTableViewController *self,
        SEL a2)
{
  HXTableManager *v2; // rax
  NSString *v13; // rax
  NSString *v14; // rax
  NSSortDescriptor *v15; // rax
  NSSortDescriptor *v16; // r13
  NSArray *v17; // rax
  NSArray *v18; // r15
  NSSortDescriptor *v28; // [rsp+58h] [rbp-38h] BYREF

  v27 = self;
  v22 = 0LL;
  v23 = &v22;
  v24 = 0x2020000000LL;
  v25 = 0;
  v2 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v27, "tableKey");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "getSelectedSchemesForKey:", v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v26 = v7;
  v9 = _objc_msgSend(v7, "dataItemEntities");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "enumerateObjectsUsingBlock:");
  v11 = 199112LL;
  if ( !*((_BYTE *)v23 + 24) )
    v11 = 5LL;
  _objc_msgSend(v27, "setSortID:", v11);
  _objc_msgSend(v27, "setIsSetSortInfoByCode:", 1LL);
  v12 = _objc_msgSend(v27, "sortID");
  v13 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(&OBJC_CLASS___NSSortDescriptor, "sortDescriptorWithKey:ascending:", v14, 0LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v28 = v16;
  v17 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v28, 1LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v27, "myFrozenTable");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  _objc_msgSend(v20, "setSortDescriptors:", v18);
  _objc_msgSend(v27, "setSortOrder:", CFSTR("D"));
  _objc_msgSend(v27, "setWenCaiSortIdentifier:", 0LL);
  _Block_object_dispose(&v22, 8);
}

//----- (000000010005477D) ----------------------------------------------------
void __fastcall sub_10005477D(__int64 a1, void *a2, __int64 a3, _BYTE *a4)
{

  v5 = _objc_msgSend(a2, "identifier");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "longLongValue");
  if ( v8 == 199112 )
  {
    *(_BYTE *)(*(_QWORD *)(*(_QWORD *)(a1 + 32) + 8LL) + 24LL) = 1;
    *a4 = 1;
  }
}

//----- (00000001000547EB) ----------------------------------------------------
signed __int64 __cdecl -[ZhongYaoZhiShuTableViewController frozenCount](
        ZhongYaoZhiShuTableViewController *self,
        SEL a2)
{
  return 3LL;
}

//----- (00000001000547F6) ----------------------------------------------------
id __cdecl -[ZhongYaoZhiShuTableViewController tableKey](ZhongYaoZhiShuTableViewController *self, SEL a2)
{

  begin = (void *)self->super.super._begin;
  if ( !begin )
  {
    self->super.super._begin = (unsigned __int64)CFSTR("__zhongYaoZhiShuTableKey");
    begin = (void *)self->super.super._begin;
  }
  return objc_retainAutoreleaseReturnValue(begin);
}

//----- (000000010005482E) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableViewController .cxx_destruct](ZhongYaoZhiShuTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super.super._begin, 0LL);
}

