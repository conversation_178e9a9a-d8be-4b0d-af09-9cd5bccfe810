void __cdecl -[ZiXunMenuViewController viewDidLoad](ZiXunMenuViewController *self, SEL a2)
{
  HXTipManager *v2; // rax
  HXTipManager *v3; // rbx

  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZiXunMenuViewController;
  objc_msgSendSuper2(&v4, "viewDidLoad");
  -[ZiXunMenuViewController initContentView](self, "initContentView");
  -[ZiXunMenuViewController setDefaultHideCtrlBtn](self, "setDefaultHideCtrlBtn");
  v2 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXTipManager addDataSource:](v3, "addDataSource:", self);
}

//----- (0000000100996B85) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController viewDidAppear](ZiXunMenuViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunMenuViewController;
  objc_msgSendSuper2(&v2, "viewDidAppear");
  -[ZiXunMenuViewController setMeiGuKaiHuBtnState](self, "setMeiGuKaiHuBtnState");
}

//----- (0000000100996BC6) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController initContentView](ZiXunMenuViewController *self, SEL a2)
{
  HXButton *v10; // rax
  HXButton *v11; // rbx

  _objc_msgSend(self->super._nibName, "setTopBorder:", 1LL);
  _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v2), "setBottomBorder:", 1LL);
  _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v3), "setBorderWidth:", 2.0);
  v4 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v6), "setBorderColor:", v5);
  v7 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v9), "setBackgroundColor:", v8);
  v10 = -[ZiXunMenuViewController operationBtn](self, "operationBtn");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(v11, "mas_makeConstraints:");
  objc_unsafeClaimAutoreleasedReturnValue(v12);
}

//----- (0000000100996D04) ----------------------------------------------------
void __fastcall sub_100996D04(__int64 a1, void *a2)
{

  v38 = objc_retain(a2);
  v2 = _objc_msgSend(v38, "right");
  v37 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v37, "equalTo");
  v4 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(*(id *)(a1 + 32), "contentView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (void *)v4[2](v4, v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "offset");
  v10 = (__int64 (__fastcall **)(id, double))objc_retainAutoreleasedReturnValue(v9);
  v11 = (void *)v10[2](v10, -8.0);
  objc_unsafeClaimAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v38, "top");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(v14, "equalTo");
  v16 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v15);
  v17 = _objc_msgSend(*(id *)(a1 + 32), "contentView");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = (void *)v16[2](v16, v18);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21 = _objc_msgSend(v20, "offset");
  v22 = (__int64 (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v21);
  v23 = (void *)v22[2](v22, 2.0);
  objc_unsafeClaimAutoreleasedReturnValue(v23);
  v25 = _objc_msgSend(v38, "bottom");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v27 = _objc_msgSend(v26, "equalTo");
  objc_retainAutoreleasedReturnValue(v27);
  v28 = _objc_msgSend(*(id *)(a1 + 32), "contentView");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  v31 = (void *)(*(__int64 (__fastcall **)(__int64, id))(v30 + 16))(v30, v29);
  v32 = objc_retainAutoreleasedReturnValue(v31);
  v33 = _objc_msgSend(v32, "offset");
  v34 = (__int64 (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v33);
  v35 = (void *)v34[2](v34, -2.0);
  objc_unsafeClaimAutoreleasedReturnValue(v35);
}

//----- (0000000100996F92) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setDefaultHideCtrlBtn](ZiXunMenuViewController *self, SEL a2)
{
  NSButton *v2; // rax
  NSButton *v7; // rax
  NSButton *v8; // rbx
  NSButton *v9; // rax
  NSButton *v10; // r14

  v2 = -[ZiXunMenuViewController hideCtrlBtn](self, "hideCtrlBtn");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("switchdown"));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v5, "setImage:", v4);
  v7 = -[ZiXunMenuViewController hideCtrlBtn](self, "hideCtrlBtn");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "setToolTip:", CFSTR("收起底部栏"));
  v9 = -[ZiXunMenuViewController hideCtrlBtn](self, "hideCtrlBtn");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "cell");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setHighlightsBy:", 1LL);
}

//----- (0000000100997099) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setMeiGuKaiHuBtnState](ZiXunMenuViewController *self, SEL a2)
{
  HXButton *v7; // rax
  HXButton *v8; // rbx
  HXButton *v9; // rax
  HXButton *v10; // rbx
  _QWORD v13[4]; // [rsp+0h] [rbp-50h] BYREF
  id to; // [rsp+20h] [rbp-30h] BYREF
  id location[5]; // [rsp+28h] [rbp-28h] BYREF

  if ( self->super._representedObject == (id)5 )
  {
    objc_initWeak(location, self);
    v2 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = _objc_msgSend(v3, "GetMeiguUserArr");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    if ( _objc_msgSend(v5, "count") )
    {
      v7 = -[ZiXunMenuViewController operationBtn](self, "operationBtn");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      _objc_msgSend(v8, "setHidden:", 1LL);
    }
    else if ( !(unsigned __int8)-[ZiXunMenuViewController isGetMeiGuKaiHuInfo](self, "isGetMeiGuKaiHuInfo") )
    {
      v11 = +[LuChangHuiManager shareInstance](&OBJC_CLASS___LuChangHuiManager, "shareInstance");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13[0] = _NSConcreteStackBlock;
      v13[1] = 3254779904LL;
      v13[2] = sub_10099727A;
      v13[3] = &unk_1012DB1D0;
      objc_copyWeak(&to, location);
      _objc_msgSend(v12, "requestMeiGuKaiHuState:", v13);
      objc_destroyWeak(&to);
    }
    objc_destroyWeak(location);
  }
  else
  {
    v9 = -[ZiXunMenuViewController operationBtn](self, "operationBtn");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    _objc_msgSend(v10, "setHidden:", 1LL);
  }
}

//----- (000000010099727A) ----------------------------------------------------
void __fastcall sub_10099727A(__int64 a1, unsigned int a2)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "displayMeiGuKaiHuBtn:", a2);
}

//----- (00000001009972B0) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController operationBtnAction:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  NSString *v3; // rax
  NSURL *v5; // r15
  NSURL *v12; // rax
  NSString *v16; // rax
  NSString *v17; // rbx

  v3 = -[ZiXunMenuViewController kaiHuUrlStr](self, "kaiHuUrlStr", a3);
  v5 = objc_retainAutoreleasedReturnValue(v3);`
  if ( v5 )`
  {`
    v6 = _objc_msgSend(v4, "kaiHuUrlStr");`
    v7 = objc_retainAutoreleasedReturnValue(v6);`
    v8 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &chars`ToLeaveEscaped);
    if ( v8 )`
      return;`
    v10 = _objc_msgSend(v9, "kaiHuUrlStr");`
    v11 = objc_retainAutoreleasedReturnValue(v10);`
    v12 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v11);`
    v5 = objc_retainAutoreleasedReturnValue(v12);`
    v13 = _objc_msgSend(&OBJC_CLASS___NSWorkspace, "sharedWorkspace");`
    v14 = objc_retainAutoreleasedReturnValue(v13);`
    _objc_msgSend(v14, "openURL:", v5);`
    v15(v14);`
    v16 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", C`FSTR("资讯栏_运营位"));
    v17 = objc_retainAutoreleasedReturnValue(v16);`
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](`
      &OBJC_CLASS___UserLogSendingQueueManager,`
      "sendUserLog:action:params:needWait:",`
      11LL,`
      v17,`
      0LL,`
      1LL);`
    v18(v17);`
  }`
}`
`
//----- (0000000100997422) -------------------------------------------`---------
void __cdecl -[ZiXunMenuViewController createMenuBtns:](ZiXunMenuViewC`ontroller *self, SEL a2, id a3)
{`
  HXTipManager *v29; // rax`
  HXTipManager *v30; // rbx`
  NSArray *v31; // rax`
  NSArray *v32; // rax`
  _QWORD v38[2]; // [rsp+140h] [rbp-40h] BYREF`
`
  v3 = objc_retain(a3);`
  v4 = v3;`
  if ( v3 && _objc_msgSend(v3, "count") )`
  {`
    _objc_msgSend(self->super.view, "makeObjectsPerformSelector:", "re`moveFromSuperview");
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");`
    v37 = objc_retainAutoreleasedReturnValue(v5);`
    if ( _objc_msgSend(v4, "count") )`
    {`
      v6 = 26LL;`
      v7 = 0LL;`
      v36 = v4;`
      while ( 1 )`
      {
        v8 = _objc_msgSend(v4, "objectAtIndexedSubscript:", v7);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        if ( !_objc_msgSend(v9, "length") )
          break;
        v10 = objc_alloc((Class)&OBJC_CLASS___HXCornerButton);
        v11 = _objc_msgSend(v10, "initWithFrame:");
        _objc_msgSend(v11, "setTitle:", v9);
        v12 = _objc_msgSend(v11, "cell");
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v34 = v9;
        v35 = v14;
        _objc_msgSend(v13, "setHighlightsBy:", 1LL);
        _objc_msgSend(v11, "setShouldTracking:", 1LL);
        _objc_msgSend(v11, "setCanBeSelected:", 1LL);
        v16 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
        v17 = objc_retainAutoreleasedReturnValue(v16);
        _objc_msgSend(v11, "setTextColorDefault:", v17);
        v18(v17);
        v19 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        _objc_msgSend(v11, "setBackgroundColor:", v20);
        v21(v20);
        if ( !v35 )
          _objc_msgSend(v11, "setLeftBorder:", 1LL);
        v22 = +[HXThemeManager secondNavigationBarSelectedColor](
                &OBJC_CLASS___HXThemeManager,
                "secondNavigationBarSelectedColor");
        v23 = objc_retainAutoreleasedReturnValue(v22);
        _objc_msgSend(v11, "setBackgroundColorSelected:", v23);
        _objc_msgSend(v11, "setRightBorder:", 1LL);
        _objc_msgSend(v11, "setTopBorder:", 1LL);
        _objc_msgSend(v11, "setBottomBorder:", 1LL);
        _objc_msgSend(v11, "setBorderWidth:", 2.0);
        v24 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        _objc_msgSend(v11, "setBorderColor:", v25);
        v26(v25);
        _objc_msgSend(v11, "setHidden:", 0LL);
        _objc_msgSend(self->super._nibName, "addSubview:", v11);
        _objc_msgSend(v37, "addObject:", v11);
        v27(v11);
        v28(v34);
        v4 = v36;
        v7 = v35 + 1;
        v6 += 82LL;
        if ( (unsigned __int64)_objc_msgSend(v36, "count") <= v35 + 1 )
          goto LABEL_11;
      }
    }
LABEL_11:
    -[ZiXunMenuViewController setMenuBtns:](self, "setMenuBtns:", v37);
    v29 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v38[0] = CFSTR("挂单撤单");
    v38[1] = CFSTR("大单棱镜");
    v31 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v38, 2LL);
    v32 = objc_retainAutoreleasedReturnValue(v31);
    -[HXTipManager showTipsWithIdArray:](v30, "showTipsWithIdArray:", v32);
  }
}

//----- (00000001009979B3) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController resetSelectedBtn:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  unsigned __int64 i; // r15
  SEL v20; // [rsp+50h] [rbp-D0h]
  id obj; // [rsp+68h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v22 = v3;
    v6 = objc_retain(v3);
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    v18 = 0LL;
    v21 = self;
    v8 = v7(self, "menuBtns");
    obj = objc_retainAutoreleasedReturnValue(v8);
    v10 = v9(obj, "countByEnumeratingWithState:objects:count:", &v15, v24, 16LL);
    if ( v10 )
    {
      v11 = v10;
      v19 = *(_QWORD *)v16;
      do
      {
        v20 = "setSelectedBtn:";
        for ( i = 0LL; i < (unsigned __int64)v11; ++i )
        {
          if ( *(_QWORD *)v16 != v19 )
            objc_enumerationMutation(obj);
          v13 = *(id *)(*((_QWORD *)&v15 + 1) + 8 * i);
          if ( v13 == v6 )
          {
            _objc_msgSend(v6, "setIsSelected:", 1LL);
            v14(v21, v20, v6);
          }
          else
          {
            _objc_msgSend(v13, "setIsSelected:", 0LL);
          }
        }
        v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v24, 16LL);
      }
      while ( v11 );
    }
    v3 = v22;
  }
}

//----- (0000000100997BAC) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController resetHideCtrlBtn:](ZiXunMenuViewController *self, SEL a2, char a3)
{
  bool v3; // zf
  __CFString *v4; // rdx
  __CFString *v5; // r13
  id (*v8)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12

  v3 = a3 == 0;
  v4 = CFSTR("switchdown");
  if ( v3 )
    v4 = CFSTR("switchup");
  v5 = CFSTR("收起底部栏");
  if ( v3 )
    v5 = CFSTR("展开底部栏");
  v6 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(self, "hideCtrlBtn");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11(v10, "setImage:", v7);
  v13 = v12(self, "hideCtrlBtn");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15(v14, "setToolTip:", v5);
}

//----- (0000000100997C82) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setDefaultSelectedMenuBtn](ZiXunMenuViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSArray *v5; // rax
  NSArray *v6; // r14

  v2 = -[ZiXunMenuViewController menuBtns](self, "menuBtns");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "count");
  if ( v4 )
  {
    v5 = -[ZiXunMenuViewController menuBtns](self, "menuBtns");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "objectAtIndexedSubscript:", 0LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "setIsSelected:", 1LL);
    -[ZiXunMenuViewController setSelectedBtn:](self, "setSelectedBtn:", v8);
    v9(v8);
  }
}

//----- (0000000100997D52) ----------------------------------------------------
NSArray *__cdecl -[ZiXunMenuViewController menuBtns](ZiXunMenuViewController *self, SEL a2)
{
  NSView *view; // rdi
  NSView *v5; // rax
  NSView *v6; // rdi

  view = self->super.view;
  if ( !view )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = (NSView *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super.view;
    self->super.view = v5;
    view = self->super.view;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(view);
}

//----- (0000000100997DA3) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setZixunType:](ZiXunMenuViewController *self, SEL a2, unsigned __int64 a3)
{
  if ( self->super._representedObject != (id)a3 )
  {
    self->super._representedObject = (id)a3;
    -[ZiXunMenuViewController setMeiGuKaiHuBtnState](self, "setMeiGuKaiHuBtnState");
  }
}

//----- (0000000100997DC8) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController displayMeiGuKaiHuBtn:](ZiXunMenuViewController *self, SEL a2, char a3)
{
  HXButton *v3; // rax
  HXButton *v4; // rbx

  -[ZiXunMenuViewController setIsGetMeiGuKaiHuInfo:](self, "setIsGetMeiGuKaiHuInfo:", a3);
  v3 = -[ZiXunMenuViewController operationBtn](self, "operationBtn");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setHidden:", 1LL);
}

//----- (0000000100997E1A) ----------------------------------------------------
id __cdecl -[ZiXunMenuViewController tipsToShow](ZiXunMenuViewController *self, SEL a2)
{
  return -[ZiXunMenuViewController tipArray](self, "tipArray");
}

//----- (0000000100997E2C) ----------------------------------------------------
NSMutableArray *__cdecl -[ZiXunMenuViewController tipArray](ZiXunMenuViewController *self, SEL a2)
{
  NSArray *topLevelObjects; // rdi
  NSArray *v5; // rax
  NSArray *v6; // rdi
  SEL v10; // r12
  id *v12; // r12
  _QWORD v14[4]; // [rsp+8h] [rbp-88h] BYREF
  _QWORD v16[4]; // [rsp+30h] [rbp-60h] BYREF
  id to; // [rsp+50h] [rbp-40h] BYREF
  id location[6]; // [rsp+60h] [rbp-30h] BYREF

  topLevelObjects = self->super._topLevelObjects;
  if ( !topLevelObjects )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (NSArray *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._topLevelObjects;
    self->super._topLevelObjects = v5;
    objc_initWeak(location, self);
    v7 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
    v8 = _objc_msgSend(v7, "initWithIdentifier:", CFSTR("挂单撤单"));
    v16[0] = _NSConcreteStackBlock;
    v16[1] = 3254779904LL;
    v16[2] = sub_100998019;
    v16[3] = &unk_1012DAA10;
    objc_copyWeak(&to, location);
    _objc_msgSend(v8, "setShowTipBlock:", v16);
    v9 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
    v11 = _objc_msgSend(v9, v10, CFSTR("大单棱镜"));
    v18 = v8;
    v14[0] = _NSConcreteStackBlock;
    v14[1] = 3254779904LL;
    v14[2] = sub_100998051;
    v14[3] = &unk_1012DAA10;
    objc_copyWeak(&v15, location);
    _objc_msgSend(v11, "setShowTipBlock:", v14);
    _objc_msgSend(self->super._topLevelObjects, "addObject:", v18);
    _objc_msgSend(self->super._topLevelObjects, "addObject:", v11);
    objc_destroyWeak(v12);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    topLevelObjects = self->super._topLevelObjects;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(topLevelObjects);
}

//----- (0000000100998019) ----------------------------------------------------
void __fastcall sub_100998019(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "showL2TipWithButtonTitle:", CFSTR("挂单撤单"));
}

//----- (0000000100998051) ----------------------------------------------------
void __fastcall sub_100998051(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "showL2TipWithButtonTitle:", CFSTR("大单棱镜"));
}

//----- (0000000100998089) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController showL2TipWithButtonTitle:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  NSArray *v3; // rax
  SEL v16; // [rsp+48h] [rbp-D8h]
  SEL v17; // [rsp+50h] [rbp-D0h]
  id obj; // [rsp+68h] [rbp-B8h]

  v19 = objc_retain(a3);
  v12 = 0LL;
  v13 = 0LL;
  v14 = 0LL;
  v15 = 0LL;
  v3 = -[ZiXunMenuViewController menuBtns](self, "menuBtns");
  obj = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v12, v21, 16LL);
  if ( v4 )
  {
    v5 = v4;
    v18 = *(_QWORD *)v13;
    while ( 2 )
    {
      v16 = "title";
      v17 = "isEqualToString:";
      for ( i = 0LL; i != v5; i = (char *)i + 1 )
      {
        if ( *(_QWORD *)v13 != v18 )
          objc_enumerationMutation(obj);
        v7 = *(void **)(*((_QWORD *)&v12 + 1) + 8LL * (_QWORD)i);
        v8 = _objc_msgSend(v7, v16);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        v10(v9, v17, v19);
        if ( v11 )
        {
          _objc_msgSend(v7, "setCornerText:", CFSTR("L2"));
          goto LABEL_13;
        }
      }
      v5 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v12, v21, 16LL);
      if ( v5 )
        continue;
      break;
    }
  }
LABEL_13:
}

//----- (000000010099826A) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunMenuViewController contentView](ZiXunMenuViewController *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 16LL, 1);
}

//----- (0000000100998280) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setContentView:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 16LL);
}

//----- (0000000100998291) ----------------------------------------------------
NSButton *__cdecl -[ZiXunMenuViewController hideCtrlBtn](ZiXunMenuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibBundle);
  return (NSButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001009982AA) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setHideCtrlBtn:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibBundle, a3);
}

//----- (00000001009982BE) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunMenuViewController zixunType](ZiXunMenuViewController *self, SEL a2)
{
  return (unsigned __int64)self->super._representedObject;
}

//----- (00000001009982CF) ----------------------------------------------------
HXButton *__cdecl -[ZiXunMenuViewController selectedBtn](ZiXunMenuViewController *self, SEL a2)
{
  return (HXButton *)self->super._title;
}

//----- (00000001009982E0) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setSelectedBtn:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._title, a3);
}

//----- (00000001009982F4) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setMenuBtns:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.view, a3);
}

//----- (0000000100998308) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setTipArray:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, a3);
}

//----- (000000010099831C) ----------------------------------------------------
HXButton *__cdecl -[ZiXunMenuViewController operationBtn](ZiXunMenuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._editors);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100998335) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setOperationBtn:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._editors, a3);
}

//----- (0000000100998349) ----------------------------------------------------
NSString *__cdecl -[ZiXunMenuViewController kaiHuUrlStr](ZiXunMenuViewController *self, SEL a2)
{
  return (NSString *)self->super._autounbinder;
}

//----- (000000010099835A) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setKaiHuUrlStr:](ZiXunMenuViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super._autounbinder, a3);
}

//----- (000000010099836E) ----------------------------------------------------
char __cdecl -[ZiXunMenuViewController isGetMeiGuKaiHuInfo](ZiXunMenuViewController *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (000000010099837F) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController setIsGetMeiGuKaiHuInfo:](ZiXunMenuViewController *self, SEL a2, char a3)
{
  LOBYTE(self->super.super._nextResponder) = a3;
}

//----- (000000010099838F) ----------------------------------------------------
void __cdecl -[ZiXunMenuViewController .cxx_destruct](ZiXunMenuViewController *self, SEL a2)
{
  objc_storeStrong(&self->super._autounbinder, 0LL);
  objc_destroyWeak((id *)&self->super._editors);
  objc_storeStrong((id *)&self->super._topLevelObjects, 0LL);
  objc_storeStrong((id *)&self->super.view, 0LL);
  objc_storeStrong((id *)&self->super._title, 0LL);
  objc_destroyWeak((id *)&self->super._nibBundle);
  objc_storeStrong((id *)&self->super._nibName, 0LL);
}

