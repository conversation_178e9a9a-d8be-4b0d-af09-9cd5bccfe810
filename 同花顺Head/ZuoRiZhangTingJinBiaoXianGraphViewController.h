//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "GraphBaseViewController.h"

@class SimpleGraphDataParseModule, SimpleGraphDataRequestModule;

@interface ZuoRiZhangTingJinBiaoXianGraphViewController : GraphBaseViewController
{
    SimpleGraphDataRequestModule *_SGRequestShouYi;
    SimpleGraphDataRequestModule *_SGRequestShangZheng;
    SimpleGraphDataParseModule *_SGParserForShangZheng;
}


@property(retain, nonatomic) SimpleGraphDataParseModule *SGParserForShangZheng; // @synthesize SGParserForShangZheng=_SGParserForShangZheng;
@property(retain, nonatomic) SimpleGraphDataRequestModule *SGRequestShangZheng; // @synthesize SGRequestShangZheng=_SGRequestShangZheng;
@property(retain, nonatomic) SimpleGraphDataRequestModule *SGRequestShouYi; // @synthesize SGRequestShouYi=_SGRequestShouYi;
- (void)deleteOrder;
- (void)orderShangZheng:(id)arg1;
- (void)orderShouYi:(id)arg1;
- (void)mouseDown:(id)arg1;
- (void)requestAllModularData;
- (id)createDataForPlot;
- (void)refreshAllModules;
- (void)frameDidChange:(id)arg1;
- (void)dealloc;
- (void)initObjects;
- (void)viewDidAppear;
- (void)viewDidLoad;

@end

