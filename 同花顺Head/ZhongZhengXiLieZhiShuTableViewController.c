ZhongZhengXiLieZhiShuTableViewController *__cdecl -[ZhongZhengXiLieZhiShuTableViewController initWithNibName:bundle:](
        ZhongZhengXiLieZhiShuTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ZhongZhengXiLieZhiShuTableViewController *v4; // rax
  ZhongZhengXiLieZhiShuTableViewController *v5; // rbx

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZhongZhengXiLieZhiShuTableViewController;
  v4 = -[HXBaseTableViewController initWithNibName:bundle:](&v7, "initWithNibName:bundle:", a3, a4);
  v5 = v4;
  if ( v4 )
  {
    -[QuoteBaseTableViewController setBlockID:](v4, "setBlockID:", 52797LL);
    -[QuoteBaseTableViewController setTableID:](v5, "setTableID:", 393475LL);
    -[QuoteBaseTableViewController setTableInfo:](v5, "setTableInfo:", CFSTR("ZhongZhengXiLieZhiShuStock"));
  }
  return v5;
}

//----- (0000000100443754) ----------------------------------------------------
void __cdecl -[ZhongZhengXiLieZhiShuTableViewController viewDidLoad](
        ZhongZhengXiLieZhiShuTableViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhongZhengXiLieZhiShuTableViewController;
  -[HuShenDisableIWenCaiEditTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 52797LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 393475LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("ZhongZhengXiLieZhiShuStock"));
}

//----- (00000001004437C8) ----------------------------------------------------
id __cdecl -[ZhongZhengXiLieZhiShuTableViewController tableKey](ZhongZhengXiLieZhiShuTableViewController *self, SEL a2)
{

  frozenCount = (void *)self->super.super.super._frozenCount;
  if ( !frozenCount )
  {
    self->super.super.super._frozenCount = (signed __int64)CFSTR("__HuShenZhiShuTableKey");
    frozenCount = (void *)self->super.super.super._frozenCount;
  }
  return objc_retainAutoreleaseReturnValue(frozenCount);
}

//----- (0000000100443800) ----------------------------------------------------
void __cdecl -[ZhongZhengXiLieZhiShuTableViewController .cxx_destruct](
        ZhongZhengXiLieZhiShuTableViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super.super.super._frozenCount, 0LL);
}

