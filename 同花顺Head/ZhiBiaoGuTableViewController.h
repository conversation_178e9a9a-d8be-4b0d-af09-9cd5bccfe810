//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "QuoteBaseTableViewController.h"

@class HXTableRequestModule, NSString;

@interface ZhiBiaoGuTableViewController : QuoteBaseTableViewController
{
    NSString *_tableKey;
    CDUnknownBlockType _postZhiBiaoGuDidChangeBlock;
    HXTableRequestModule *_codeListSortRequestModule;
    HXTableRequestModule *_backgroundCodeListSortRequestModule;
    HXTableRequestModule *_backgroundDetailDataRequestModule;
}


@property(retain, nonatomic) HXTableRequestModule *backgroundDetailDataRequestModule; // @synthesize backgroundDetailDataRequestModule=_backgroundDetailDataRequestModule;
@property(retain, nonatomic) HXTableRequestModule *backgroundCodeListSortRequestModule; // @synthesize backgroundCodeListSortRequestModule=_backgroundCodeListSortRequestModule;
@property(retain, nonatomic) HXTableRequestModule *codeListSortRequestModule; // @synthesize codeListSortRequestModule=_codeListSortRequestModule;
@property(copy, nonatomic) CDUnknownBlockType postZhiBiaoGuDidChangeBlock; // @synthesize postZhiBiaoGuDidChangeBlock=_postZhiBiaoGuDidChangeBlock;
- (long long)frozenCount;
- (id)tableKey;
- (id)frozenTableView:(id)arg1 dataCellForTableColumn:(id)arg2 row:(long long)arg3;
- (id)frozenTableView:(id)arg1 menuForHeaderOfTableColumn:(id)arg2;
- (void)actionForTableViewSelectionDidChange:(long long)arg1;
- (void)myTableIsDoubleClicked:(id)arg1;
- (id)calculateItemsWithRequestData:(id)arg1;
- (void)dealWithRequestData:(id)arg1 extension:(id)arg2;
- (void)requestDetailData;
- (id)getCodeAndMarketArrWithResponse:(id)arg1;
- (id)getCodeListStrWithCodeAndMarketArr:(id)arg1;
- (void)requestCodeListSort;
- (void)requestBackgroundDetailDataWithCodeList:(id)arg1;
- (void)requestBackgroundCodeListSort;
- (void)setOriginalSortInfoForMyFrozenTable;
- (void)requestForMyTable;
- (void)initObjects;

@end

