//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "QuoteBaseItem.h"

@interface XinSanBanStockQuoteItem : QuoteBaseItem
{
    double _caiBaoShiJian;
    double _shiYingTTM;
    double _zongGuBen;
    double _liuTongGuBen;
    double _waiPan;
    double _jingLiRun;
    double _meiGuJingZiChan;
    double _zhangTing;
    double _dieTing;
    double _shangNianMeiGuShouYi;
    double _zhangDie2;
    double _piaoMianGuXiLv;
    double _qiXiRi;
    double _zongShiZhi;
    double _zuoShiShangShuLiang;
    double _huanShouLv;
    double _neiPan;
    double _junJia;
    double _shiJingLv;
    double _liuTongShiZhi;
}

@property(nonatomic) double liuTongShiZhi; // @synthesize liuTongShiZhi=_liuTongShiZhi;
@property(nonatomic) double shiJingLv; // @synthesize shiJingLv=_shiJingLv;
@property(nonatomic) double junJia; // @synthesize junJia=_junJia;
@property(nonatomic) double neiPan; // @synthesize neiPan=_neiPan;
@property(nonatomic) double huanShouLv; // @synthesize huanShouLv=_huanShouLv;
@property(nonatomic) double zuoShiShangShuLiang; // @synthesize zuoShiShangShuLiang=_zuoShiShangShuLiang;
@property(nonatomic) double zongShiZhi; // @synthesize zongShiZhi=_zongShiZhi;
@property(nonatomic) double qiXiRi; // @synthesize qiXiRi=_qiXiRi;
@property(nonatomic) double piaoMianGuXiLv; // @synthesize piaoMianGuXiLv=_piaoMianGuXiLv;
@property(nonatomic) double zhangDie2; // @synthesize zhangDie2=_zhangDie2;
@property(nonatomic) double shangNianMeiGuShouYi; // @synthesize shangNianMeiGuShouYi=_shangNianMeiGuShouYi;
@property(nonatomic) double dieTing; // @synthesize dieTing=_dieTing;
@property(nonatomic) double zhangTing; // @synthesize zhangTing=_zhangTing;
@property(nonatomic) double meiGuJingZiChan; // @synthesize meiGuJingZiChan=_meiGuJingZiChan;
@property(nonatomic) double jingLiRun; // @synthesize jingLiRun=_jingLiRun;
@property(nonatomic) double waiPan; // @synthesize waiPan=_waiPan;
@property(nonatomic) double liuTongGuBen; // @synthesize liuTongGuBen=_liuTongGuBen;
@property(nonatomic) double zongGuBen; // @synthesize zongGuBen=_zongGuBen;
@property(nonatomic) double shiYingTTM; // @synthesize shiYingTTM=_shiYingTTM;
@property(nonatomic) double caiBaoShiJian; // @synthesize caiBaoShiJian=_caiBaoShiJian;
- (void)liuTongShiZhiCalculate;
- (void)shiJingLvCalculate;
- (void)junJiaCalculate;
- (void)neiPanCalculate;
- (void)huanShouLvCalculate;
- (void)resetExtraValues:(id)arg1;
- (void)setConstantProperties:(id)arg1;
- (void)initializeExtraValues;
- (id)init;

@end

