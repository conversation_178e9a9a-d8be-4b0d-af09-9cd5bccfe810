//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "BanKuaiReDianBaseViewController.h"

@class HangYeGeGuTableController, ZhiBiaoGuTableViewController;

@interface ZhiBiaoGuViewController : BanKuaiReDianBaseViewController
{
    ZhiBiaoGuTableViewController *_zhiBiaoGuTableVC;
    HangYeGeGuTableController *_hangYeGeGuTableVC;
    CDUnknownBlockType _geGuSeletedRowDidChangedBlock;
}


@property(copy, nonatomic) CDUnknownBlockType geGuSeletedRowDidChangedBlock; // @synthesize geGuSeletedRowDidChangedBlock=_geGuSeletedRowDidChangedBlock;
@property(retain, nonatomic) HangYeGeGuTableController *hangYeGeGuTableVC; // @synthesize hangYeGeGuTableVC=_hangYeGeGuTableVC;
@property(retain, nonatomic) ZhiBiaoGuTableViewController *zhiBiaoGuTableVC; // @synthesize zhiBiaoGuTableVC=_zhiBiaoGuTableVC;
- (void)initActionBlock;
- (void)initAllView;
- (void)refreshAllModules;
- (void)keyDownFromSuper:(id)arg1;
- (void)viewDidLoad;

@end

