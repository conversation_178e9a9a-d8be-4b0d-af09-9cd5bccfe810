void __cdecl -[UserListWndCtroller awakeFromNib](UserListWndCtroller *self, SEL a2)
{
  NSDocument *v4; // rax
  NSDocument *document; // rdi
  NSTableView *v6; // rax
  NSTableView *v7; // rbx

  v8.receiver = self;
  v8.super_class = (Class)&OBJC_CLASS___UserListWndCtroller;
  objc_msgSendSuper2(&v8, "awakeFromNib");
  v3 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v4 = (NSDocument *)_objc_msgSend(v3, "init");
  document = self->super.super._document;
  self->super.super._document = v4;
  BYTE4(self->super.super._topLevelObjects) = 1;
  if ( (unsigned int)sub_101003230(1, 11, 0, 0) )
  {
    v6 = -[UserListWndCtroller tableUser](self, "tableUser");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "setStyle:", 4LL);
  }
}

//----- (0000000100CB6B36) ----------------------------------------------------
void __cdecl -[UserListWndCtroller showWindow:](UserListWndCtroller *self, SEL a2, id a3)
{
  id WeakRetained; // rbx

  v3 = objc_retain(a3);
  v15.receiver = v4;
  v15.super_class = (Class)&OBJC_CLASS___UserListWndCtroller;
  -[HXBaseTradeWindow showWindow:](&v15, "showWindow:", v3);
  v5 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(v6, "GetUserArrayByType:", *(unsigned int *)(v7 + 40));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = *(void **)(v10 + 32);
  *(_QWORD *)(v10 + 32) = v9;
  WeakRetained = objc_loadWeakRetained((id *)(v12 + 88));
  _objc_msgSend(WeakRetained, "reloadData");
  _objc_msgSend(v14, "upThemes");
}

//----- (0000000100CB6C4F) ----------------------------------------------------
void __cdecl -[UserListWndCtroller windowDidLoad](UserListWndCtroller *self, SEL a2)
{
  NSDocument *v6; // rax
  NSDocument *document; // rdi

  v3 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "GetUserArrayByType:", LODWORD(self->super.super._topLevelObjects));
  v6 = (NSDocument *)objc_retainAutoreleasedReturnValue(v5);
  document = self->super.super._document;
  self->super.super._document = v6;
}

//----- (0000000100CB6CDC) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setUserType:](UserListWndCtroller *self, SEL a2, int a3)
{
  LODWORD(self->super.super._topLevelObjects) = a3;
}

//----- (0000000100CB6CEC) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setShowButton:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._jyContentView, a3);
}

//----- (0000000100CB6D00) ----------------------------------------------------
id __cdecl -[UserListWndCtroller tableView:viewForTableColumn:row:](
        UserListWndCtroller *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  _QWORD v28[4]; // [rsp+8h] [rbp-68h] BYREF
  id to; // [rsp+28h] [rbp-48h] BYREF
  id location; // [rsp+38h] [rbp-38h] BYREF

  if ( !_objc_msgSend(self->super.super._document, "count", a3, a4) )
    return objc_autoreleaseReturnValue(0LL);
  v32 = (id)a5;
  v6 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "GetBundle");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v10, "loadFormNibName:bundle:", CFSTR("UserListViewItem"), v9);
  objc_retainAutoreleasedReturnValue(v11);
  objc_initWeak(&location, self);
  if ( v12 )
  {
    v28[0] = _NSConcreteStackBlock;
    v28[1] = 3254779904LL;
    v28[2] = sub_100CB6FC6;
    v28[3] = &unk_1012EAEB8;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v13, "setLogout:", v28);
    v14 = _objc_msgSend(self->super.super._document, "objectAtIndex:", v32);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = v15;
    if ( v15 )
    {
      v17 = _objc_msgSend(v15, "getUserInfo");
      v32 = objc_retainAutoreleasedReturnValue(v17);
      v18 = _objc_msgSend(v32, "strGdxm");
      v30 = objc_retainAutoreleasedReturnValue(v18);
      v19 = _objc_msgSend(v16, "getUserInfo");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v20, "strQsName");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23 = v30;
      _objc_msgSend(v24, "SetData:Name:QSName:", v16, v30, v22);
    }
    objc_destroyWeak(&to);
  }
  v25 = objc_retain(v12);
  objc_destroyWeak(&location);
  return objc_autoreleaseReturnValue(v25);
}

//----- (0000000100CB6FC6) ----------------------------------------------------
void __fastcall sub_100CB6FC6(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "UserLogout:", v2);
}

//----- (0000000100CB703C) ----------------------------------------------------
signed __int64 __cdecl -[UserListWndCtroller numberOfRowsInTableView:](UserListWndCtroller *self, SEL a2, id a3)
{
  return (signed __int64)_objc_msgSend(self->super.super._document, "count", a3);
}

//----- (0000000100CB705A) ----------------------------------------------------
void __cdecl -[UserListWndCtroller UserLogout:](UserListWndCtroller *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "postNotificationName:object:", CFSTR("delete_user_notification"), v3);
    -[UserListWndCtroller close](self, "close");
  }
}

//----- (0000000100CB70FF) ----------------------------------------------------
void __cdecl -[UserListWndCtroller positionWithTable](UserListWndCtroller *self, SEL a2)
{
  __m128d v6; // xmm0
  __m128d v7; // xmm0
  __m128d v17; // xmm1
  SEL v22; // r12
  int v26; // ebx
  __m128d v30; // [rsp+80h] [rbp-A0h]
  __m128d v33; // [rsp+A0h] [rbp-80h]
  __m128d v34; // [rsp+B0h] [rbp-70h] BYREF
  __m128d v37; // [rsp+E0h] [rbp-40h]

  v2 = +[UserManager shareInstance](&OBJC_CLASS___UserManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "GetUserArrayByType:", LODWORD(self->super.super._topLevelObjects));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( (unsigned __int64)_objc_msgSend(v5, "count") > 4 )
  {
    v7 = (__m128d)0x4061800000000000uLL;
  }
  else
  {
    v6 = _mm_sub_pd(
           (__m128d)_mm_unpacklo_epi32(
                      (__m128i)(unsigned __int64)(28LL * (_QWORD)_objc_msgSend(v5, "count")),
                      (__m128i)xmmword_1010CE400),
           (__m128d)xmmword_1010CE410);
    v7 = _mm_hadd_pd(v6, v6);
  }
  v33 = v7;
  v8 = _objc_msgSend(self, "window");
  v9 = (const char *)objc_retainAutoreleasedReturnValue(v8);
  v10 = (char *)v9;
  if ( v9 )
  {
    objc_msgSend_stret(&v28, v9, "frame");
  }
  else
  {
    v29 = 0LL;
    v28 = 0LL;
  }
  if ( *(double *)&v29 != 0.0 && *((double *)&v29 + 1) != 0.0 )
  {
    v11 = (unsigned __int8)_objc_msgSend(*(id *)&self->super.super._wcFlags, "isFlipped");
    v12 = *(void **)&self->super.super._wcFlags;
    if ( v11 )
    {
      if ( v12 )
      {
        objc_msgSend_stret(&v34, *(SEL *)&self->super.super._wcFlags, "bounds");
        v13 = *((_QWORD *)&v35 + 1);
        _objc_msgSend(v12, "convertPoint:toView:", 0LL, 0.0, *((double *)&v35 + 1));
      }
      else
      {
        v35 = 0LL;
        v34 = 0LL;
        v13 = 0LL;
        _objc_msgSend(v12, "convertPoint:toView:", 0LL, 0.0, 0.0);
      }
    }
    else
    {
      v13 = 0LL;
      _objc_msgSend(*(id *)&self->super.super._wcFlags, "convertPoint:toView:", 0LL, 0.0, 0.0);
    }
    *(_QWORD *)&v36 = v13;
    v37.f64[0] = 0.0;
    v14 = _objc_msgSend(*(id *)&self->super.super._wcFlags, "window");
    v15 = (const char *)objc_retain(v14);
    v16 = (char *)v15;
    if ( v15 )
    {
      objc_msgSend_stret(&v34, v15, "convertRectToScreen:");
      v37 = (__m128d)*(unsigned __int64 *)&v34.f64[0];
      v36 = *(unsigned __int64 *)&v34.f64[1];
    }
    else
    {
      v35 = 0LL;
      v34 = 0LL;
      v36 = 0LL;
      v37 = 0LL;
    }
    v17.f64[1] = *((double *)&v36 + 1);
    if ( *(double *)&v36 - v33.f64[0] >= 0.0 )
    {
      v17.f64[0] = *(double *)&v36 - v33.f64[0];
      v37 = _mm_unpacklo_pd(v37, v17);
    }
    else
    {
      v18 = (unsigned __int8)_objc_msgSend(*(id *)&self->super.super._wcFlags, "isFlipped");
      v20 = *(void **)&self->super.super._wcFlags;
      if ( v18 )
      {
        v21 = 0LL;
        _objc_msgSend(*(id *)&self->super.super._wcFlags, v19, 0LL, 0.0, 0.0);
      }
      else if ( v20 )
      {
        objc_msgSend_stret(&v34, *(SEL *)&self->super.super._wcFlags, "bounds");
        v21 = *((_QWORD *)&v35 + 1);
        _objc_msgSend(v20, v22, 0LL, 0.0, *((double *)&v35 + 1));
      }
      else
      {
        v35 = 0LL;
        v34 = 0LL;
        v21 = 0LL;
        _objc_msgSend(v20, v19, 0LL, 0.0, 0.0);
      }
      *(_QWORD *)&v36 = v21;
      v37.f64[0] = 0.0;
      v23 = _objc_msgSend(*(id *)&self->super.super._wcFlags, "window");
      v24 = (const char *)objc_retain(v23);
      v25 = (char *)v24;
      if ( v24 )
      {
        objc_msgSend_stret(&v34, v24, "convertRectToScreen:");
        v37 = v34;
      }
      else
      {
        v35 = 0LL;
        v37 = 0LL;
        v34 = 0LL;
      }
    }
    v26 = (unsigned int)-[UserListWndCtroller nWidth](self, "nWidth");
    v27 = _objc_msgSend(self, "window");
    v30 = v37;
    v31 = (double)v26;
    v32 = v33.f64[0];
    _objc_msgSend(v27, "setFrame:display:", 0LL);
  }
}

//----- (0000000100CB75B2) ----------------------------------------------------
void __cdecl -[UserListWndCtroller runLoopWithUserList](UserListWndCtroller *self, SEL a2)
{
  id WeakRetained; // r14
  id *v30; // r12
  __int16 v33; // r13
  NSRunLoopMode v58; // [rsp+0h] [rbp-90h]
  id *location; // [rsp+8h] [rbp-88h]
  id *v60; // [rsp+40h] [rbp-50h]
  id *v62; // [rsp+60h] [rbp-30h]

  v2 = _objc_msgSend(self, "window");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v60 = (id *)(v4 + 88);
  WeakRetained = objc_loadWeakRetained((id *)(v4 + 88));
  _objc_msgSend(v3, "makeFirstResponder:", WeakRetained);
  v6 = _objc_msgSend(&OBJC_CLASS___NSDate, "distantFuture");
  v61 = objc_retainAutoreleasedReturnValue(v6);
  *(_BYTE *)(v7 + 44) = 0;
  v58 = NSDefaultRunLoopMode;
  location = (id *)(v7 + 96);
  v8 = 0LL;
  v62 = (id *)v7;
  while ( 1 )
  {
    v9 = _objc_msgSend(NSApp, "nextEventMatchingMask:untilDate:inMode:dequeue:", -1LL, v61, v58, 1LL, v58);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( _objc_msgSend(v10, "type") == (id)13 && (unsigned __int16)_objc_msgSend(v10, "subtype") == 2 )
    {
      _objc_msgSend(v11, "close");
      goto LABEL_35;
    }
    if ( _objc_msgSend(v10, "type") == (id)1 || _objc_msgSend(v10, "type") == (id)3 )
      break;
    if ( _objc_msgSend(v10, "type") != (id)10 )
      goto LABEL_10;
    v31 = _objc_msgSend(v10, "characters");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33 = (unsigned __int16)_objc_msgSend(v32, "characterAtIndex:", 0LL);
    if ( v33 <= 9 )
    {
      if ( (unsigned __int16)(v33 + 2304) < 2u )
      {
        v34 = _objc_msgSend(v30, "window");
        v35 = objc_retainAutoreleasedReturnValue(v34);
        _objc_msgSend(v35, "sendEvent:", v10);
        goto LABEL_11;
      }
      if ( v33 == 3 )
        goto LABEL_11;
      goto LABEL_10;
    }
    if ( v33 == 10 || v33 == 13 )
      goto LABEL_11;
    if ( v33 != 27 )
      goto LABEL_10;
    v41 = _objc_msgSend(v10, "window");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    v44 = _objc_msgSend(v43, "window");
    v45 = objc_retainAutoreleasedReturnValue(v44);
    v46(v42);
    if ( v42 == v45 )
    {
      _objc_msgSend(v62, "close");
      goto LABEL_35;
    }
    _objc_msgSend(NSApp, "sendEvent:", v10);
    v30 = v62;
LABEL_11:
    v8 = v10;
    if ( *((_BYTE *)v30 + 44) )
      goto LABEL_35;
  }
  v12 = _objc_msgSend(v10, "window");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = objc_loadWeakRetained(location);
  v15 = _objc_msgSend(v14, "window");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17(v14);
  v18(v13);
  if ( v13 == v16 )
  {
    v47 = objc_loadWeakRetained(v62 + 8);
    v48 = (unsigned __int8)_objc_msgSend(v47, "GetListShow");
    if ( !v48 )
    {
      _objc_msgSend(NSApp, "sendEvent:", v10);
      goto LABEL_35;
    }
    _objc_msgSend(v62, "close");
    v50 = objc_loadWeakRetained(v62 + 8);
    _objc_msgSend(v50, "SetListShow:", 0LL);
    goto LABEL_34;
  }
  v19 = _objc_msgSend(v10, "window");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21 = _objc_msgSend(v62, "window");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23(v20);
  if ( v20 != v22 )
  {
    _objc_msgSend(v62, "close");
    v51 = objc_loadWeakRetained(v62 + 8);
    _objc_msgSend(v51, "SetListShow:", 0LL);
    _objc_msgSend(NSApp, "sendEvent:", v10);
    goto LABEL_35;
  }
  if ( _objc_msgSend(v10, "clickCount") != (id)2
    || (v24 = _objc_msgSend(v10, "window"),
        v25 = objc_retainAutoreleasedReturnValue(v24),
        v27 = _objc_msgSend(v26, "window"),
        v28 = objc_retainAutoreleasedReturnValue(v27),
        v29(v25),
        v25 != v28) )
  {
LABEL_10:
    _objc_msgSend(NSApp, "sendEvent:", v10);
    goto LABEL_11;
  }
  v36 = objc_loadWeakRetained(v60);
  v37 = _objc_msgSend(v36, "numberOfRows");
  if ( (__int64)v37 <= 0 )
  {
    _objc_msgSend(v38, "close");
    v50 = objc_loadWeakRetained((id *)(v52 + 64));
    _objc_msgSend(v50, "SetListShow:", 0LL);
LABEL_34:
    goto LABEL_35;
  }
  v39 = objc_loadWeakRetained(v60);
  v40 = _objc_msgSend(v39, "selectedRow");
  if ( v40 == (id)-1LL )
    goto LABEL_11;
  v53 = _objc_msgSend(v30[4], "objectAtIndex:", v40);
  v54 = objc_retainAutoreleasedReturnValue(v53);
  if ( v54 )
  {
    v55 = (void (__fastcall **)(id, id))v62[10];
    if ( v55 )
      v55[2](v55, v54);
  }
  _objc_msgSend(v62, "close");
  v56 = objc_loadWeakRetained(v62 + 8);
  _objc_msgSend(v56, "SetListShow:", 0LL);
LABEL_35:
}

//----- (0000000100CB7D4F) ----------------------------------------------------
void __cdecl -[UserListWndCtroller tableViewSelectionDidChange:](UserListWndCtroller *self, SEL a2, id a3)
{
  id WeakRetained; // rbx

  v3 = objc_retain(a3);
  if ( self->super._nInstanceID )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->super._jyWindow);
    _objc_msgSend(WeakRetained, "selectedRow");
    v6 = _objc_msgSend(self->super.super._document, "objectAtIndex:", v5);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    if ( v7 )
      (*(void (__fastcall **)(signed __int64, id))(self->super._nInstanceID + 16))(self->super._nInstanceID, v7);
  }
  v9 = objc_loadWeakRetained((id *)&self->super.super._frameAutosaveName);
  _objc_msgSend(v9, "SetListShow:", 0LL);
  -[UserListWndCtroller close](self, "close");
}

//----- (0000000100CB7E62) ----------------------------------------------------
void __cdecl -[UserListWndCtroller tableViewSelectionIsChanging:](UserListWndCtroller *self, SEL a2, id a3)
{
  ;
}

//----- (0000000100CB7E68) ----------------------------------------------------
void __cdecl -[UserListWndCtroller upThemes](UserListWndCtroller *self, SEL a2)
{
  id WeakRetained; // rbx
  id *v5; // r12
  id *v9; // r12

  WeakRetained = objc_loadWeakRetained((id *)&self->super._jyWindow);
  if ( WeakRetained )
  {
    v3 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v6 = objc_loadWeakRetained(v5);
    _objc_msgSend(v6, "setBackgroundColor:", v4);
    v7 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = objc_loadWeakRetained(v9);
    _objc_msgSend(v10, "setGridColor:", v8);
  }
}

//----- (0000000100CB7F72) ----------------------------------------------------
void __cdecl -[UserListWndCtroller close](UserListWndCtroller *self, SEL a2)
{

  BYTE4(self->super.super._topLevelObjects) = 1;
  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___UserListWndCtroller;
  objc_msgSendSuper2(&v2, "close");
}

//----- (0000000100CB7FAC) ----------------------------------------------------
HXBaseTradeView *__cdecl -[UserListWndCtroller MainView](UserListWndCtroller *self, SEL a2)
{
  return *(HXBaseTradeView **)&self->super.super._wcFlags;
}

//----- (0000000100CB7FBD) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setMainView:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._wcFlags, a3);
}

//----- (0000000100CB7FD1) ----------------------------------------------------
JYUserInfoItemView *__cdecl -[UserListWndCtroller ItemView](UserListWndCtroller *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._frameAutosaveName);
  return (JYUserInfoItemView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CB7FEA) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setItemView:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._frameAutosaveName, a3);
}

//----- (0000000100CB7FFE) ----------------------------------------------------
id __cdecl -[UserListWndCtroller LogouFun](UserListWndCtroller *self, SEL a2)
{
  return objc_getProperty(self, a2, 72LL, 0);
}

//----- (0000000100CB8011) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setLogouFun:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 72LL);
}

//----- (0000000100CB8022) ----------------------------------------------------
id __cdecl -[UserListWndCtroller SelectFun](UserListWndCtroller *self, SEL a2)
{
  return objc_getProperty(self, a2, 80LL, 0);
}

//----- (0000000100CB8035) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setSelectFun:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 80LL);
}

//----- (0000000100CB8046) ----------------------------------------------------
int __cdecl -[UserListWndCtroller nWidth](UserListWndCtroller *self, SEL a2)
{
  return (int)self->super.super._owner;
}

//----- (0000000100CB8056) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setNWidth:](UserListWndCtroller *self, SEL a2, int a3)
{
  LODWORD(self->super.super._owner) = a3;
}

//----- (0000000100CB8066) ----------------------------------------------------
NSTableView *__cdecl -[UserListWndCtroller tableUser](UserListWndCtroller *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._jyWindow);
  return (NSTableView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CB807F) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setTableUser:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._jyWindow, a3);
}

//----- (0000000100CB8093) ----------------------------------------------------
WTButton *__cdecl -[UserListWndCtroller button](UserListWndCtroller *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._jyContentView);
  return (WTButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100CB80AC) ----------------------------------------------------
void __cdecl -[UserListWndCtroller setButton:](UserListWndCtroller *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._jyContentView, a3);
}

//----- (0000000100CB80C0) ----------------------------------------------------
void __cdecl -[UserListWndCtroller .cxx_destruct](UserListWndCtroller *self, SEL a2)
{
  objc_destroyWeak((id *)&self->super._jyContentView);
  objc_destroyWeak((id *)&self->super._jyWindow);
  objc_storeStrong((id *)&self->super._nInstanceID, 0LL);
  objc_storeStrong(&self->super.super._moreVars, 0LL);
  objc_destroyWeak((id *)&self->super.super._frameAutosaveName);
  objc_storeStrong((id *)&self->super.super._wcFlags, 0LL);
  objc_storeStrong((id *)&self->super.super._document, 0LL);
}

