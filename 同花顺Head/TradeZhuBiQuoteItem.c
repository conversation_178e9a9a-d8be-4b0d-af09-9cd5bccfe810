TradeZhuBiQuoteItem *__cdecl -[TradeZhuBiQuoteItem init](TradeZhuBiQuoteItem *self, SEL a2)
{
  TradeZhuBiQuoteItem *v2; // rbx
  NSColor *v4; // rax
  NSColor *fallTextColor; // rdi
  id (*v6)(id, SEL, ...); // r12
  NSColor *v8; // rax
  NSColor *riseTextColor; // rdi
  id (*v10)(id, SEL, ...); // r12
  NSColor *v12; // rax
  NSColor *orangeLineColor; // rdi
  id (*v14)(id, SEL, ...); // r12
  NSColor *v16; // rax
  NSColor *normalTextColor; // rdi
  id (*v18)(id, SEL, ...); // r12
  NSMutableDictionary *v20; // rax
  NSMutableDictionary *idModelDict; // rdi
  id (*v22)(id, SEL, ...); // r12
  NSMutableArray *v24; // rax
  NSMutableArray *tempPushArray; // rdi
  id (*v26)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12

  v35.receiver = self;
  v35.super_class = (Class)&OBJC_CLASS___TradeZhuBiQuoteItem;
  v2 = objc_msgSendSuper2(&v35, "init");
  if ( v2 )
  {
    v3 = +[HXThemeManager fallTextColor](&OBJC_CLASS___HXThemeManager, "fallTextColor");
    v4 = (NSColor *)objc_retainAutoreleasedReturnValue(v3);
    fallTextColor = v2->fallTextColor;
    v2->fallTextColor = v4;
    v7 = v6(&OBJC_CLASS___HXThemeManager, "riseTextColor");
    v8 = (NSColor *)objc_retainAutoreleasedReturnValue(v7);
    riseTextColor = v2->riseTextColor;
    v2->riseTextColor = v8;
    v11 = v10(&OBJC_CLASS___HXThemeManager, "orangeLineColor");
    v12 = (NSColor *)objc_retainAutoreleasedReturnValue(v11);
    orangeLineColor = v2->orangeLineColor;
    v2->orangeLineColor = v12;
    v15 = v14(&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v16 = (NSColor *)objc_retainAutoreleasedReturnValue(v15);
    normalTextColor = v2->normalTextColor;
    v2->normalTextColor = v16;
    v19 = v18(&OBJC_CLASS___NSMutableDictionary, "dictionary");
    v20 = (NSMutableDictionary *)objc_retainAutoreleasedReturnValue(v19);
    idModelDict = v2->_idModelDict;
    v2->_idModelDict = v20;
    v23 = v22(&OBJC_CLASS___NSMutableArray, "array");
    v24 = (NSMutableArray *)objc_retainAutoreleasedReturnValue(v23);
    tempPushArray = v2->_tempPushArray;
    v2->_tempPushArray = v24;
    v27 = v26(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v2->_disPlayMoney = (unsigned __int8)v29(v28, "boolForKey:", CFSTR("TradeDisplayMoney"));
    v31 = v30(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33(v32, "addObserver:selector:name:object:", v2, "switchNumberOrMoneyState:", CFSTR("TradeDisplayMoney"), 0LL);
  }
  return v2;
}

//----- (0000000100C03E19) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem dealloc](TradeZhuBiQuoteItem *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___TradeZhuBiQuoteItem;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (0000000100C03E8E) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem switchNumberOrMoneyState:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{

  v3 = _objc_msgSend(a3, "userInfo");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "allKeys");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "firstObject");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (__int64)_objc_msgSend(v8, "boolValue");
  *(_BYTE *)(v10 + 72) = v9;
  _objc_msgSend(v11, "requestForStateData:market:", *((_QWORD *)v11 + 21), *((_QWORD *)v11 + 22));
  v13 = *(_QWORD *)(v12 + 144);
  if ( v13 )
    (*(void (**)(void))(v13 + 16))();
}

//----- (0000000100C03F6B) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem didSelectTime:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  unsigned __int64 type; // r13

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(v4, "objectForKeyedSubscript:", CFSTR("type"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "integerValue");
  type = self->_type;
  if ( v10 == type )
    -[TradeZhuBiQuoteItem selectZhuBi:](self, "selectZhuBi:", v4);
}

//----- (0000000100C04006) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem selectZhuBi:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  NSNumber *v6; // rax
  NSNumber *v7; // rbx
  NSNumber *v9; // rax
  NSNumber *v10; // rbx
  NSNumber *v13; // rax
  NSNumber *v14; // rbx
  id *v24; // r15
  id WeakRetained; // rbx
  id *v26; // r12
  int v38; // eax
  NSString *v39; // rax
  NSString *v40; // rbx
  bool v51; // cf
  id obj; // [rsp+80h] [rbp-50h]

  v5 = objc_retain(a3);
  if ( v5 )
  {
    obj = a3;
    val = self;
    v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v5, "objectForKeyedSubscript:", v7);
    v61 = objc_retainAutoreleasedReturnValue(v8);
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v5, "objectForKeyedSubscript:", v10);
    v63 = objc_retainAutoreleasedReturnValue(v11);
    v12(v10);
    v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49LL);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v5, "objectForKeyedSubscript:", v14);
    v64 = objc_retainAutoreleasedReturnValue(v15);
    v55 = v5;
    v16 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("zongBiShu"));
    v56 = objc_retainAutoreleasedReturnValue(v16);
    v60 = _objc_msgSend(v56, "integerValue");
    v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( !(unsigned __int8)_objc_msgSend(v18, "isKindOfClass:", v17) )
      goto LABEL_5;
    _objc_msgSend(v19, "doubleValue");
    if ( v3 == 4294967295.0 )
      goto LABEL_5;
    _objc_msgSend(v20, "doubleValue");
    if ( v3 == 2147483648.0 )
      goto LABEL_5;
    v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( !(unsigned __int8)_objc_msgSend(v63, "isKindOfClass:", v22) )
      goto LABEL_5;
    _objc_msgSend(v63, "doubleValue");
    if ( v3 == 4294967295.0 )
      goto LABEL_5;
    _objc_msgSend(v63, "doubleValue");
    if ( v3 == 2147483648.0 )
      goto LABEL_5;
    v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( !(unsigned __int8)_objc_msgSend(v64, "isKindOfClass:", v23) )
      goto LABEL_5;
    _objc_msgSend(v64, "doubleValue");
    if ( v3 == 4294967295.0 )
      goto LABEL_5;
    _objc_msgSend(v64, "doubleValue");
    if ( (__int64)v60 <= 0 || v3 == 2147483648.0 )
      goto LABEL_5;
    v24 = (id *)val;
    if ( _objc_msgSend(*((id *)val + 26), "count") )
    {
      WeakRetained = objc_loadWeakRetained((id *)val + 19);
      _objc_msgSend(WeakRetained, "addObjects:", *((_QWORD *)val + 26));
      _objc_msgSend(*((id *)val + 26), "removeAllObjects");
      v24 = (id *)val;
      v27 = objc_loadWeakRetained(v26);
      _objc_msgSend(v27, "rearrangeObjects");
    }
    v28 = objc_loadWeakRetained(v24 + 19);
    v29 = _objc_msgSend(v28, "arrangedObjects");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    if ( !_objc_msgSend(v30, "count") )
    {
LABEL_34:
LABEL_5:
      v5 = v55;
      goto LABEL_6;
    }
    v32 = _objc_msgSend(v30, "firstObject");
    objc_retainAutoreleasedReturnValue(v32);
    v57 = v30;
    v33 = _objc_msgSend(v30, "lastObject");
    v34 = objc_retainAutoreleasedReturnValue(v33);
    v58 = v35;
    _objc_msgSend(v35, "time");
    v67 = v3;
    v59 = v34;
    _objc_msgSend(v34, "time");
    v66 = v3;
    _objc_msgSend(v61, "doubleValue");
    v68 = v3;
    v36 = v24[2];
    v66 = v3 + -8.0;
    v37 = +[HXTools stringNumber:withFormat:](&OBJC_CLASS___HXTools, "stringNumber:withFormat:", v63, v36);
    objc_retainAutoreleasedReturnValue(v37);
    v38 = (unsigned int)_objc_msgSend(v64, "integerValue");
    v39 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"), (double)v38 / *((double *)v24 + 3));
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v41 = _objc_msgSend(v40, "integerValue");
    *((_QWORD *)val + 4) = 0x7FFFFFFFFFFFFFFFLL;
    v43 = v60;
    if ( v68 < v66 )
    {
LABEL_21:
      v62 = v68 + 1.0;
      v67 = v68 + 3.0;
      if ( v68 + 3.0 < v68 + 1.0 )
      {
LABEL_25:
        v50 = v68;
        v51 = v68 < v66;
        *((_QWORD *)val + 4) = 0x7FFFFFFFFFFFFFFFLL;
        if ( v51 )
        {
LABEL_28:
          v53 = v62;
          if ( v67 < v62 )
          {
LABEL_33:
            v30 = v57;
            goto LABEL_34;
          }
          while ( 1 )
          {
            v62 = v53;
            v45 = _objc_msgSend(val, "findeRelatedRangeWithTime:xianjia:bishu:zongShou:wuCha:", v42, v43, v41, 1LL);
            v47 = v54;
            if ( v45 != (id)0x7FFFFFFFFFFFFFFFLL )
              break;
            v53 = v62 + 1.0;
            if ( v67 < v62 + 1.0 )
              goto LABEL_33;
          }
        }
        else
        {
          while ( 1 )
          {
            v68 = v50;
            v45 = _objc_msgSend(val, "findeRelatedRangeWithTime:xianjia:bishu:zongShou:wuCha:", v42, v43, v41, 1LL);
            v47 = v52;
            if ( v45 != (id)0x7FFFFFFFFFFFFFFFLL )
              break;
            v50 = v68 + -1.0;
            if ( v68 + -1.0 < v66 )
              goto LABEL_28;
          }
        }
      }
      else
      {
        v48 = v62;
        while ( 1 )
        {
          *(double *)&obj = v48;
          v45 = _objc_msgSend(val, "findeRelatedRangeWithTime:xianjia:bishu:zongShou:wuCha:", v42, v60, v41, 0LL, v48);
          v47 = v49;
          if ( v45 != (id)0x7FFFFFFFFFFFFFFFLL )
            break;
          v48 = *(double *)&obj + 1.0;
          if ( v67 < *(double *)&obj + 1.0 )
            goto LABEL_25;
        }
      }
    }
    else
    {
      v44 = v68;
      while ( 1 )
      {
        *(double *)&obj = v44;
        v45 = _objc_msgSend(val, "findeRelatedRangeWithTime:xianjia:bishu:zongShou:wuCha:", v42, v60, v41, 0LL);
        v47 = v46;
        if ( v45 != (id)0x7FFFFFFFFFFFFFFFLL )
          break;
        v44 = *(double *)&obj + -1.0;
        if ( *(double *)&obj + -1.0 < v66 )
          goto LABEL_21;
      }
    }
    _objc_msgSend(val, "didFindeRelatedRange:", v45, v47);
    goto LABEL_33;
  }
LABEL_6:
}

//----- (0000000100C04816) ----------------------------------------------------
void __fastcall sub_100C04816(__int64 a1, void *a2)
{
  id WeakRetained; // rax

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "updateZhubiData:", a2);
  v5 = objc_loadWeakRetained((id *)(a1 + 32));
  v6 = objc_retain(v5);
  v7 = _objc_msgSend(v6, "needSelectDict");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v6, "selectZhuBi:", v8);
}

//----- (0000000100C048D3) ----------------------------------------------------
_NSRange __cdecl -[TradeZhuBiQuoteItem findeRelatedRangeWithTime:xianjia:bishu:zongShou:wuCha:](
        TradeZhuBiQuoteItem *self,
        SEL a2,
        double a3,
        id a4,
        signed __int64 a5,
        signed __int64 a6,
        unsigned __int64 a7)
{
  unsigned __int64 v9; // rdx
  unsigned __int64 v11; // r15
  unsigned __int64 v12; // r14
  id (**v13)(id, SEL, ...); // r12
  signed __int64 v15; // rbx
  NSUInteger v16; // r12
  _NSRange result; // rax

  v19 = objc_retain(a4);
  v8 = -[TradeZhuBiQuoteItem findeA1WithTickTime:](self, "findeA1WithTickTime:", a3);
  if ( v8 == (id)v10 || (v11 = v9, v9 == v10) || (v12 = (unsigned __int64)v8, v9 < (unsigned __int64)v8) )
  {
LABEL_9:
    v15 = v10;
  }
  else
  {
    v13 = &_objc_msgSend;
    while ( 1 )
    {
      v14 = ((id (*)(id, SEL, ...))v13)(self, "findeA2WithA1Index:xianjia:wuCha:", v11, v19, a7);
      if ( v14 != (id)0x7FFFFFFFFFFFFFFFLL
        && (__int64)((id (*)(id, SEL, ...))v13)(
                      self,
                      "caculateZongShouWithA2Index:biShu:zongShou:wuCha:",
                      v14,
                      a5,
                      a6,
                      a7) >= 0 )
      {
        break;
      }
      if ( --v11 < v12 )
      {
        v10 = 0x7FFFFFFFFFFFFFFFLL;
        goto LABEL_9;
      }
    }
    v15 = a5;
  }
  result.location = v16;
  result.length = v15;
  return result;
}

//----- (0000000100C049CA) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem didFindeRelatedRange:](TradeZhuBiQuoteItem *self, SEL a2, _NSRange a3)
{

  selectionIndexesBlock = (void (__fastcall **)(id, NSUInteger, NSUInteger))self->_selectionIndexesBlock;
  if ( selectionIndexesBlock )
    selectionIndexesBlock[2](selectionIndexesBlock, a3.location, a3.length);
}

//----- (0000000100C049E6) ----------------------------------------------------
_NSRange __cdecl -[TradeZhuBiQuoteItem findeA1WithTickTime:](TradeZhuBiQuoteItem *self, SEL a2, double a3)
{
  id WeakRetained; // rbx
  _NSRange result; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
  v4 = _objc_msgSend(WeakRetained, "arrangedObjects");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( _objc_msgSend(v5, "count") )
  {
    v6 = (char *)_objc_msgSend(v5, "count") - 1;
    if ( (__int64)v6 >= 0 )
    {
      v11 = v5;
      v7 = 0;
      do
      {
        v8 = _objc_msgSend(v11, "objectAtIndexedSubscript:", v6);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        _objc_msgSend(v9, "time");
        if ( !v7 )
          v7 = 1;
        --v6;
      }
      while ( (__int64)v6 >= 0 );
      v5 = v11;
    }
  }
  result.location = 0x7FFFFFFFFFFFFFFFLL;
  result.length = 0x7FFFFFFFFFFFFFFFLL;
  return result;
}

//----- (0000000100C04B66) ----------------------------------------------------
unsigned __int64 __cdecl -[TradeZhuBiQuoteItem findeA2WithA1Index:xianjia:wuCha:](
        TradeZhuBiQuoteItem *self,
        SEL a2,
        unsigned __int64 a3,
        id a4,
        unsigned __int64 a5)
{
  unsigned __int64 v6; // r15
  id WeakRetained; // r14

  v6 = 0x7FFFFFFFFFFFFFFFLL;
  v18 = objc_retain(a4);
  WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
  v8 = _objc_msgSend(WeakRetained, "arrangedObjects");
  v19 = objc_retainAutoreleasedReturnValue(v8);
  if ( (a3 & 0x8000000000000000LL) == 0LL )
  {
    while ( 1 )
    {
      v9 = _objc_msgSend(v19, "objectAtIndexedSubscript:", a3);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v10, "price");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", v18);
      v15(v10);
      if ( v13 )
        break;
      if ( (__int64)a3-- <= 0 )
      {
        v6 = 0x7FFFFFFFFFFFFFFFLL;
        goto LABEL_6;
      }
    }
    v6 = a3;
  }
LABEL_6:
  return v6;
}

//----- (0000000100C04C8B) ----------------------------------------------------
signed __int64 __cdecl -[TradeZhuBiQuoteItem caculateZongShouWithA2Index:biShu:zongShou:wuCha:](
        TradeZhuBiQuoteItem *self,
        SEL a2,
        unsigned __int64 a3,
        signed __int64 a4,
        signed __int64 a5,
        unsigned __int64 a6)
{
  signed __int64 v6; // r12
  id WeakRetained; // rax
  unsigned __int64 v14; // r12

  v6 = -1LL;
  if ( a3 != 0x7FFFFFFFFFFFFFFFLL && a4 > 0 && self->lastZongShouIndex != a3 )
  {
    self->lastZongShouIndex = a3;
    WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
    v9 = _objc_msgSend(WeakRetained, "arrangedObjects");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( (__int64)(a3 - a4 + 1) < 0 )
    {
      v12 = v10;
    }
    else if ( a3 - a4 + 1 <= a3 )
    {
      v13 = 0LL;
      v14 = a3 - a4 + 1;
      do
      {
        v15 = _objc_msgSend(v10, "objectAtIndexedSubscript:", v14);
        v16 = objc_retainAutoreleasedReturnValue(v15);
        v13 += (__int64)_objc_msgSend(v16, "volValue");
        v12 = v10;
        v14 = v17 + 1;
      }
      while ( v14 <= a3 );
    }
    else
    {
      v12 = v10;
    }
  }
  return v6;
}

//----- (0000000100C04DCA) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem loadMoreData](TradeZhuBiQuoteItem *self, SEL a2)
{
  id WeakRetained; // r15
  id (*v6)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  NSNumber *v12; // rax
  NSNumber *v13; // r15
  _QWORD v16[4]; // [rsp+8h] [rbp-68h] BYREF
  id to[2]; // [rsp+28h] [rbp-48h] BYREF
  id location[6]; // [rsp+40h] [rbp-30h] BYREF

  if ( self->_market && self->_stockCode && !self->_requestingZhuBi )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
    v4 = _objc_msgSend(WeakRetained, "arrangedObjects");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    if ( v6(v5, "count") )
    {
      v8 = v7(v5, "firstObject");
      v18 = objc_retainAutoreleasedReturnValue(v8);
      v9(v18, "time");
      to[1] = v2;
      objc_initWeak(location, self);
      v11 = v10(&OBJC_CLASS___NSNumber, "numberWithInt:", 4294966996LL);
      objc_retainAutoreleasedReturnValue(v11);
      v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v16[0] = _NSConcreteStackBlock;
      v16[1] = 3254779904LL;
      v16[2] = sub_100C04F85;
      v16[3] = &unk_1012DAA70;
      objc_copyWeak(to, location);
      -[TradeZhuBiQuoteItem requestZhuBiWithStart:end:callBack:fail:](
        self,
        "requestZhuBiWithStart:end:callBack:fail:",
        v14,
        v13,
        v16,
        0LL);
      objc_destroyWeak(to);
      objc_destroyWeak(location);
    }
  }
}

//----- (0000000100C04F85) ----------------------------------------------------
void __fastcall sub_100C04F85(__int64 a1, void *a2)
{
  id WeakRetained; // rbx
  SEL v11; // r12

  v2 = objc_retain(a2);
  objc_loadWeakRetained((id *)(a1 + 32));
  v18 = v2;
  v3 = _objc_msgSend(v2, "arrBody");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v5, "updateLoadMoreZhubiData:", v4);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v8 = _objc_msgSend(WeakRetained, "didLoadMoreData");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( v9 )
  {
    v10 = objc_loadWeakRetained((id *)(a1 + 32));
    v12 = _objc_msgSend(v10, v11);
    objc_retainAutoreleasedReturnValue(v12);
    v13 = _objc_msgSend(v18, "arrBody");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "count");
    (*(void (__fastcall **)(__int64, id))(v16 + 16))(v16, v15);
  }
}

//----- (0000000100C050AF) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem requestForStateData:market:](TradeZhuBiQuoteItem *self, SEL a2, id a3, id a4)
{
  id (__cdecl *v6)(id); // r12
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  DataRequestCenter *v14; // rax
  DataRequestCenter *v15; // r15
  NSArray *v17; // rax
  _QWORD v21[4]; // [rsp+30h] [rbp-70h] BYREF
  id to; // [rsp+50h] [rbp-50h] BYREF
  id location; // [rsp+60h] [rbp-40h] BYREF

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = v7;
  if ( v7 )
  {
    v9 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
    if ( v5 )
    {
      if ( !v9 && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", &charsToLeaveEscaped) )
      {
        -[TradeZhuBiQuoteItem deleteOrder](self, "deleteOrder");
        -[TradeZhuBiQuoteItem resetObjectsWithMarker:stockCode:](self, "resetObjectsWithMarker:stockCode:", v8, v5);
        v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 200LL);
        v11 = objc_retainAutoreleasedReturnValue(v10);
        v13 = _objc_msgSend(
                v12,
                "dictionaryWithObjectsAndKeys:",
                v5,
                CFSTR("CodeList"),
                v8,
                CFSTR("Market"),
                CFSTR("6"),
                CFSTR("DataTypes"),
                v11,
                CFSTR("DataRequest_Type"),
                0LL);
        objc_retainAutoreleasedReturnValue(v13);
        objc_initWeak(&location, self);
        v14 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v25 = v16;
        v17 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v25, 1LL);
        v23 = v18;
        objc_retainAutoreleasedReturnValue(v17);
        v21[0] = _NSConcreteStackBlock;
        v21[1] = 3254779904LL;
        v21[2] = sub_100C05330;
        v21[3] = &unk_1012DAA70;
        objc_copyWeak(&to, &location);
        -[DataRequestCenter request:type:params:callBack:fail:](
          v15,
          "request:type:params:callBack:fail:",
          2LL,
          1LL,
          v19,
          v21,
          &stru_1012EA0E8);
        objc_destroyWeak(&to);
        objc_destroyWeak(&location);
      }
    }
  }
}

//----- (0000000100C05330) ----------------------------------------------------
void __fastcall sub_100C05330(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    objc_retain(v2);
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "updateForRequest:", v5);
  }
}

//----- (0000000100C053C6) ----------------------------------------------------
void __cdecl sub_100C053C6(id a1, unsigned __int64 a2)
{
  ;
}

//----- (0000000100C053CC) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem updateForRequest:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  NSNumber *v6; // rax
  NSNumber *v7; // r13
  NSString *stockCode; // rdx
  NSNumber *v15; // rax
  NSNumber *v16; // rax
  NSNumber *v18; // r13
  NSNumber *v24; // rax
  NSNumber *v25; // r14
  _QWORD v26[4]; // [rsp+8h] [rbp-A8h] BYREF
  id to; // [rsp+28h] [rbp-88h] BYREF
  _QWORD v28[4]; // [rsp+30h] [rbp-80h] BYREF
  id location; // [rsp+70h] [rbp-40h] BYREF

  val = self;
  v30 = objc_retain(a3);
  v3 = _objc_msgSend(v30, "arrBody");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "firstObject");
  objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "objectForKeyedSubscript:", v7);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  stockCode = self->_stockCode;
  v31 = v12;
  if ( (unsigned __int8)_objc_msgSend(v12, "isEqualToString:", stockCode) )
  {
    _objc_msgSend(val, "setConstantValues:", v14);
    objc_initWeak(&location, val);
    v15 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v34 = objc_retainAutoreleasedReturnValue(v15);
    if ( !*((_QWORD *)val + 10) )
    {
      v22 = *((_QWORD *)val + 17);
      if ( v22 )
      {
        v23 = (*(__int64 (**)(void))(v22 + 16))();
        if ( v23 > 0 )
        {
          v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", -v23);
          v25 = objc_retainAutoreleasedReturnValue(v24);
          v34 = v25;
        }
      }
    }
    v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v32 = v17;
    v18 = objc_retainAutoreleasedReturnValue(v16);
    v26[0] = _NSConcreteStackBlock;
    v26[1] = 3254779904LL;
    v26[2] = sub_100C056AD;
    v26[3] = &unk_1012DAA70;
    objc_copyWeak(&to, &location);
    v28[0] = _NSConcreteStackBlock;
    v28[1] = v19;
    v28[2] = sub_100C0596C;
    v28[3] = &unk_1012DAF38;
    objc_copyWeak(&v29, &location);
    _objc_msgSend(val, "requestZhuBiWithStart:end:callBack:fail:", v34, v18, v26, v28);
    objc_destroyWeak(&v29);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
  }
}

//----- (0000000100C056AD) ----------------------------------------------------
void __fastcall sub_100C056AD(__int64 a1, void *a2)
{
  id *v2; // r13
  id WeakRetained; // rax

  v44 = objc_retain(a2);
  v2 = (id *)(a1 + 32);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "idModelDict");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "removeAllObjects");
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  v8 = _objc_msgSend(v7, "tempPushArray");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "removeAllObjects");
  v11 = objc_loadWeakRetained((id *)(a1 + 32));
  v12 = _objc_msgSend(v11, "tradeZhuBiArrayController");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "content");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v15, "removeAllObjects");
  v17 = objc_loadWeakRetained((id *)(a1 + 32));
  v18 = _objc_msgSend(v17, "tradeZhuBiArrayController");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "rearrangeObjects");
  v21 = v20;
  v20(v19);
  v22(v17);
  v23 = objc_retain(v44);
  v24 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v24, "updateZhubiData:", v23);
  v25(v24);
  v26 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v26, "order");
  v27(v26);
  v28 = objc_loadWeakRetained((id *)(a1 + 32));
  v29 = _objc_msgSend(v28, "scrollToBottomBlock");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v21(v30);
  v21(v31);
  if ( v30 )
  {
    v32 = objc_loadWeakRetained(v2);
    v33 = _objc_msgSend(v32, "scrollToBottomBlock");
    v34 = (void (__fastcall **)(id, _QWORD))objc_retainAutoreleasedReturnValue(v33);
    v34[2](v34, 0LL);
  }
  v36 = objc_loadWeakRetained(v2);
  v37 = _objc_msgSend(v36, "didFinishFirstRequestBlock");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  if ( v38 )
  {
    v40 = objc_loadWeakRetained(v2);
    v41 = _objc_msgSend(v40, "didFinishFirstRequestBlock");
    v42 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v41);
    v42[2](v42);
  }
}

//----- (0000000100C0596C) ----------------------------------------------------
void __fastcall sub_100C0596C(__int64 a1)
{
  id *v1; // r15
  id WeakRetained; // rax

  v1 = (id *)(a1 + 32);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "tradeZhuBiArrayController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "content");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "removeAllObjects");
  v8 = objc_loadWeakRetained((id *)(a1 + 32));
  v9 = _objc_msgSend(v8, "tradeZhuBiArrayController");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "rearrangeObjects");
  v12 = objc_loadWeakRetained((id *)(a1 + 32));
  v13 = _objc_msgSend(v12, "didFinishFirstRequestBlock");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  if ( v14 )
  {
    v16 = objc_loadWeakRetained(v1);
    v17 = _objc_msgSend(v16, "didFinishFirstRequestBlock");
    v18 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v17);
    v18[2](v18);
  }
  v20 = objc_loadWeakRetained(v1);
  v21 = _objc_msgSend(v20, "reloadBlock");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  if ( v22 )
  {
    v24 = objc_loadWeakRetained(v1);
    v25 = _objc_msgSend(v24, "reloadBlock");
    v26 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v25);
    v26[2](v26);
  }
  v28 = objc_loadWeakRetained(v1);
  _objc_msgSend(v28, "order");
}

//----- (0000000100C05B3F) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem requestZhuBiWithStart:end:callBack:fail:](
        TradeZhuBiQuoteItem *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6)
{
  id (__cdecl *v9)(id); // r12
  id (__cdecl *v11)(id); // r12
  id (__cdecl *v16)(id); // r12
  NSString *market; // rax
  NSString *stockCode; // rsi
  NSTimer *countDowntimer; // rdi
  NSTimer *v21; // rdi
  NSDictionary *v22; // rax
  DataRequestCenter *v23; // rax
  id (__cdecl *v24)(id); // r12
  id (__cdecl *v25)(id); // r12
  _QWORD v27[4]; // [rsp+8h] [rbp-108h] BYREF
  _QWORD v30[4]; // [rsp+38h] [rbp-D8h] BYREF
  id to; // [rsp+60h] [rbp-B0h] BYREF
  id location; // [rsp+80h] [rbp-90h] BYREF
  _QWORD v38[5]; // [rsp+90h] [rbp-80h] BYREF
  _QWORD v39[5]; // [rsp+B8h] [rbp-58h] BYREF

  v37 = a6;
  v8 = objc_retain(a3);
  v10 = v9(a4);
  v12 = v11(a5);
  v13 = v10;
  v15 = v12;
  v17 = v16(v37);
  if ( v8 )
  {
    if ( v13 )
    {
      if ( v15 )
      {
        market = self->_market;
        if ( market )
        {
          stockCode = self->_stockCode;
          if ( stockCode )
          {
            v37 = v17;
            countDowntimer = self->_countDowntimer;
            if ( countDowntimer )
            {
              _objc_msgSend(countDowntimer, "invalidate");
              v21 = self->_countDowntimer;
              self->_countDowntimer = 0LL;
              stockCode = self->_stockCode;
              market = self->_market;
            }
            self->_requestingZhuBi = 1;
            v38[0] = CFSTR("Market");
            v39[0] = market;
            v38[1] = CFSTR("CodeList");
            v39[1] = stockCode;
            v38[2] = CFSTR("DataTypes");
            v39[2] = CFSTR("5,10,12,13");
            v38[3] = CFSTR("Start");
            v33 = v8;
            v39[3] = v8;
            v38[4] = CFSTR("End");
            v39[4] = v13;
            v22 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v39, v38, 5LL);
            v35 = objc_retainAutoreleasedReturnValue(v22);
            objc_initWeak(&location, self);
            v23 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
            v34 = objc_retain(v23);
            v30[0] = _NSConcreteStackBlock;
            v30[1] = 3254779904LL;
            v30[2] = sub_100C05E78;
            v30[3] = &unk_1012DBB08;
            objc_copyWeak(&to, &location);
            v31 = v24(v15);
            v27[0] = _NSConcreteStackBlock;
            v27[1] = 3254779904LL;
            v27[2] = sub_100C06054;
            v27[3] = &unk_1012E0680;
            v28 = v25(v37);
            objc_copyWeak(&v29, &location);
            _objc_msgSend(v34, "request:params:callBack:fail:", 220LL, v35, v30, v27);
            objc_destroyWeak(&v29);
            objc_destroyWeak(&to);
            objc_destroyWeak(&location);
            v8 = v33;
            v17 = v37;
          }
        }
      }
    }
  }
}

//----- (0000000100C05E78) ----------------------------------------------------
void __fastcall sub_100C05E78(__int64 a1, void *a2, __int64 a3)
{
  NSNumber *v9; // rax
  NSNumber *v10; // r14
  id WeakRetained; // r14

  objc_retain(a2);
  v5 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v5) )
  {
    v26 = a3;
    v27 = objc_retain(v7);
    v8 = _objc_msgSend(v27, "dicExt");
    v25 = objc_retainAutoreleasedReturnValue(v8);
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v25, "objectForKeyedSubscript:", v10);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v12);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = v12;
    a1 = v16;
    WeakRetained = objc_loadWeakRetained((id *)(v17 + 40));
    v19 = _objc_msgSend(WeakRetained, "stockCode");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    LOBYTE(v14) = (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", v20);
    if ( !(_BYTE)v14 )
    {
      v23 = v27;
      goto LABEL_8;
    }
    v22 = *(_QWORD *)(a1 + 32);
    if ( v22 )
      (*(void (__fastcall **)(__int64, id, __int64))(v22 + 16))(v22, v27, v26);
  }
  v23 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v23, "setRequestingZhuBi:", 0LL);
LABEL_8:
}

//----- (0000000100C06054) ----------------------------------------------------
void __fastcall sub_100C06054(__int64 a1)
{
  id WeakRetained; // rbx

  v2 = *(_QWORD *)(a1 + 32);
  if ( v2 )
    (*(void (__fastcall **)(__int64))(v2 + 16))(v2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "setRequestingZhuBi:", 0LL);
}

//----- (0000000100C06099) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem order](TradeZhuBiQuoteItem *self, SEL a2)
{
  NSString *stockCode; // rdi
  NSString *v10; // rax
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  NSString *v26; // [rsp+0h] [rbp-30h]

  v3 = -[TradeZhuBiQuoteItem viewDispalyedBlock](self, "viewDispalyedBlock");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( v4 )
  {
    v5 = -[TradeZhuBiQuoteItem viewDispalyedBlock](self, "viewDispalyedBlock");
    v6 = (__int64 (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v5);
    v7 = v6[2](v6);
  }
  else
  {
    v7 = 1;
  }
  stockCode = self->_stockCode;
  if ( stockCode )
  {
    if ( self->_market )
    {
      if ( !(unsigned __int8)_objc_msgSend(stockCode, "isEqualToString:", &charsToLeaveEscaped) )
      {
        v9 = (unsigned __int8)_objc_msgSend(self->_market, "isEqualToString:", &charsToLeaveEscaped);
        if ( v7 )
        {
          if ( !v9 )
          {
            v10 = _objc_msgSend(
                    &OBJC_CLASS___NSString,
                    "stringWithFormat:",
                    CFSTR("id=10201&instance=10201&zipversion=3&action=change&codelist=%@&market=%@&datatype=56,1,13,10,12,74,75"),
                    self->_stockCode,
                    self->_market);
            v26 = objc_retainAutoreleasedReturnValue(v10);
            v12 = v11(&OBJC_CLASS___HXSocketCenter, "sharedInstance");
            v13 = objc_retainAutoreleasedReturnValue(v12);
            v15 = v14(&OBJC_CLASS___NSString, "stringWithFormat:", v26, self->_stockCode, self->_market);
            v16 = objc_retainAutoreleasedReturnValue(v15);
            v17(v13, "writeTextData:withTimeout:delegate:instance:", v16, self, 10201LL, 10.0);
            v19 = v18(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
            v20 = objc_retainAutoreleasedReturnValue(v19);
            v21(v20, "addObserver:selector:name:object:", self, "didSelectTime:", off_1012E7B28, 0LL);
            v23 = v22(&OBJC_CLASS___SubscribeLogManager, "sharedInstance");
            v24 = objc_retainAutoreleasedReturnValue(v23);
            v25(v24, "addLog:stackSkip:type:", v26, 1LL, 103LL);
          }
        }
      }
    }
  }
}

//----- (0000000100C062BC) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem deleteOrder](TradeZhuBiQuoteItem *self, SEL a2)
{
  NSString *stockCode; // rdi
  NSString *v4; // rax
  HXSocketCenter *v5; // rax
  HXSocketCenter *v6; // r13
  SEL v7; // r12
  HXSocketCenter *v11; // rax
  HXSocketCenter *v12; // rax
  NSString *v17; // rdi
  NSString *market; // rdi
  SubscribeLogManager *v19; // rax
  SubscribeLogManager *v20; // r14
  NSString *v21; // [rsp+8h] [rbp-38h]

  stockCode = self->_stockCode;
  if ( stockCode
    && self->_market
    && !(unsigned __int8)_objc_msgSend(stockCode, "isEqualToString:", &charsToLeaveEscaped)
    && !(unsigned __int8)_objc_msgSend(self->_market, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("id=10201&instance=10201&zipversion=3&action=delete&codelist=%@&market=%@&datatype=56,1,13,10,12"),
           self->_stockCode,
           self->_market);
    v21 = objc_retainAutoreleasedReturnValue(v4);
    if ( self->_type != 1 )
    {
      v5 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v8 = _objc_msgSend(
             &OBJC_CLASS___NSString,
             v7,
             CFSTR("id=10201&instance=10201&zipversion=3&action=delete&codelist=%@&market=%@&datatype=56,1,13,10,12"),
             self->_stockCode,
             self->_market);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
        v6,
        "writeTextData:withTimeout:delegate:instance:",
        v9,
        self,
        10201LL,
        10.0);
    }
    v11 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    -[HXSocketCenter removeObjectFromMapTable:](v12, "removeObjectFromMapTable:", self);
    v14 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(v15, "removeObserver:name:object:", self, off_1012E7B28, 0LL);
    v17 = self->_stockCode;
    self->_stockCode = 0LL;
    market = self->_market;
    self->_market = 0LL;
    v19 = +[SubscribeLogManager sharedInstance](&OBJC_CLASS___SubscribeLogManager, "sharedInstance");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    -[SubscribeLogManager addLog:stackSkip:type:](v20, "addLog:stackSkip:type:", v21, 1LL, 103LL);
  }
}

//----- (0000000100C064F9) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem receiveStuffData:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    -[TradeZhuBiQuoteItem updateZhubiData:](self, "updateZhubiData:", v4);
    scrollToBottomBlock = (void (__fastcall **)(id, _QWORD))self->_scrollToBottomBlock;
    if ( scrollToBottomBlock )
    {
      if ( !self->_countDowntimer )
        scrollToBottomBlock[2](scrollToBottomBlock, 0LL);
    }
  }
}

//----- (0000000100C0657C) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem resetObjectsWithMarker:stockCode:](TradeZhuBiQuoteItem *self, SEL a2, id a3, id a4)
{
  NSDateFormatter *v10; // rax
  NSDateFormatter *dataFormatter; // rdi
  NSTimeZone *v12; // rax
  NSTimeZone *v13; // rax
  NSString *v15; // rax
  NSString *v16; // r13
  NSDictionary *needSelectDict; // rdi
  NSTimer *countDowntimer; // rdi

  v22 = objc_retain(a3);
  v8 = objc_retain(v7);
  objc_storeStrong((id *)&self->_market, a3);
  objc_storeStrong((id *)&self->_stockCode, v9);
  self->_zuoShou = 4294967295.0;
  if ( !self->dataFormatter )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSDateFormatter, "new");
    dataFormatter = self->dataFormatter;
    self->dataFormatter = v10;
    v12 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, "timeZoneWithName:", CFSTR("Asia/Shanghai"));
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(self->dataFormatter, "setTimeZone:", v13);
    _objc_msgSend(self->dataFormatter, "setDateFormat:", CFSTR("HH:mm:ss"));
  }
  self->precisionType = +[HXTools getPrecisionTypeWithMarket:Code:](
                          &OBJC_CLASS___HXTools,
                          "getPrecisionTypeWithMarket:Code:",
                          self->_market,
                          0LL);
  v15 = -[TradeZhuBiQuoteItem market](self, "market");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v17, "getVolumeUintWithMarket:", v16);
  self->volumeUint = v4;
  needSelectDict = self->_needSelectDict;
  self->_needSelectDict = 0LL;
  v20 = v19;
  v19(needSelectDict);
  if ( !self->_countDowntimer )
  {
    _objc_msgSend(0LL, "invalidate");
    countDowntimer = self->_countDowntimer;
    self->_countDowntimer = 0LL;
  }
  ((void (__fastcall *)(id))v20)(v8);
  ((void (__fastcall *)(id))v20)(v22);
}

//----- (0000000100C06728) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setConstantValues:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  NSNumber *v4; // rax
  NSNumber *v5; // rbx

  if ( a3 )
  {
    objc_retain(a3);
    v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = _objc_msgSend(v6, "objectForKeyedSubscript:", v5);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v10) )
    {
      _objc_msgSend(v8, "doubleValue");
      if ( v3 != 4294967295.0 )
      {
        _objc_msgSend(v8, "doubleValue");
        if ( v3 != 2147483648.0 )
        {
          _objc_msgSend(v8, "doubleValue");
          self->_zuoShou = v3;
        }
      }
    }
  }
}

//----- (0000000100C06845) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem updateLoadMoreZhubiData:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  unsigned __int64 v6; // r12
  SEL v13; // r14
  TradeZhuBiQuoteItem *v14; // r13
  TradeZhuBiQuoteItem *v33; // rbx
  TradeZhuBiQuoteItem *v39; // rax
  TradeZhuBiQuoteItem *v47; // rbx
  unsigned __int64 precisionType; // rcx
  __CFString *v70; // rdx
  TradeZhuBiQuoteItem *v82; // rax
  id WeakRetained; // rbx
  id (**v119)(id, SEL, ...); // rbx
  SEL i; // r13
  bool v137; // zf
  id *v143; // r13
  SEL v159; // [rsp+98h] [rbp-348h]
  SEL v160; // [rsp+A0h] [rbp-340h]
  SEL v161; // [rsp+A8h] [rbp-338h]
  SEL v162; // [rsp+B0h] [rbp-330h]
  SEL v163; // [rsp+B8h] [rbp-328h]
  SEL v164; // [rsp+C0h] [rbp-320h]
  SEL v165; // [rsp+C8h] [rbp-318h]
  SEL v166; // [rsp+D0h] [rbp-310h]
  SEL v167; // [rsp+D8h] [rbp-308h]
  SEL v168; // [rsp+E0h] [rbp-300h]
  SEL v169; // [rsp+E8h] [rbp-2F8h]
  NSColor **p_fallTextColor; // [rsp+F8h] [rbp-2E8h]
  NSColor **p_riseTextColor; // [rsp+100h] [rbp-2E0h]
  SEL v176; // [rsp+120h] [rbp-2C0h]
  SEL v177; // [rsp+128h] [rbp-2B8h]
  SEL v178; // [rsp+130h] [rbp-2B0h]
  SEL v180; // [rsp+140h] [rbp-2A0h]
  SEL v181; // [rsp+148h] [rbp-298h]
  SEL v182; // [rsp+150h] [rbp-290h]
  SEL v183; // [rsp+158h] [rbp-288h]
  SEL v184; // [rsp+160h] [rbp-280h]
  SEL v185; // [rsp+168h] [rbp-278h]
  SEL v186; // [rsp+170h] [rbp-270h]
  SEL v187; // [rsp+178h] [rbp-268h]
  SEL v188; // [rsp+180h] [rbp-260h]
  SEL v189; // [rsp+188h] [rbp-258h]
  NSColor **v192; // [rsp+1A0h] [rbp-240h]
  SEL v193; // [rsp+1A8h] [rbp-238h]
  SEL v194; // [rsp+1B0h] [rbp-230h]
  id *location; // [rsp+1B8h] [rbp-228h]
  SEL v197; // [rsp+1C8h] [rbp-218h]
  SEL v198; // [rsp+1D0h] [rbp-210h]
  SEL v199; // [rsp+1D8h] [rbp-208h]
  SEL v200; // [rsp+1E0h] [rbp-200h]
  SEL v201; // [rsp+1E8h] [rbp-1F8h]
  id obj; // [rsp+218h] [rbp-1C8h]
  SEL v211; // [rsp+238h] [rbp-1A8h]
  SEL v216; // [rsp+260h] [rbp-180h]
  SEL v218; // [rsp+270h] [rbp-170h]
  SEL v219; // [rsp+278h] [rbp-168h]
  SEL v220; // [rsp+280h] [rbp-160h]
  SEL v222; // [rsp+290h] [rbp-150h]
  TradeZhuBiQuoteItem *v223; // [rsp+298h] [rbp-148h]

  v223 = self;
  v3 = objc_retain(a3);
  if ( !_objc_msgSend(v3, "count") )
    goto LABEL_96;
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v196 = objc_retainAutoreleasedReturnValue(v4);
  v149 = 0LL;
  v150 = 0LL;
  v151 = 0LL;
  v152 = 0LL;
  v170 = v3;
  obj = objc_retain(v3);
  v206 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v149, v227, 16LL);
  if ( !v206 )
  {
    v215 = 0LL;
    v213 = 0LL;
    v212 = 0LL;
    v202 = 0LL;
    v214 = 0LL;
    v203 = 0LL;
    goto LABEL_75;
  }
  v210 = *(id *)v150;
  p_riseTextColor = &v223->riseTextColor;
  p_fallTextColor = &v223->fallTextColor;
  v190 = 0.0;
  v215 = 0LL;
  v213 = 0LL;
  v212 = 0LL;
  v202 = 0LL;
  v214 = 0LL;
  v203 = 0LL;
  v209 = 0LL;
  do
  {
    v219 = "numberWithInt:";
    v5 = "objectForKeyedSubscript:";
    v216 = "class";
    v220 = "isKindOfClass:";
    v222 = "doubleValue";
    v193 = "init";
    v194 = "setSortNumber:";
    location = (id *)"setObject:forKeyedSubscript:";
    v157 = "addObject:";
    v158 = "setTime:";
    v175 = "time";
    v182 = "stringWithFormat:";
    v176 = "setName:";
    v179 = "dateWithTimeIntervalSince1970:";
    v180 = "stringFromDate:";
    v181 = "setTimeString:";
    v177 = "setNameColor:";
    v178 = "setLineHidden:";
    v159 = "stringNumber:withFormat:";
    v160 = "setPrice:";
    v161 = "getJudgedColorOfTargetValue:JudgedValue:";
    v162 = "setPriceColor:";
    v211 = "integerValue";
    v183 = "setVol:";
    v199 = "vol";
    v163 = "setVolValue:";
    v200 = "stringByAppendingString:";
    v164 = "setVolColor:";
    v165 = "tenThousandWithNumber:";
    v197 = "setMoney:";
    v185 = "money";
    v166 = "length";
    v198 = "systemFontOfSize:";
    v167 = "setVolFont:";
    v184 = "thsNumberForKey:";
    v168 = "setBuyNumber:";
    v201 = "volValue";
    v186 = "setBuyIndex:";
    v187 = "setTotalBuyVolValue:";
    v169 = "setSaleNumber:";
    v188 = "setSaleIndex:";
    v189 = "setTotalSaleVolValue:";
    v6 = 0LL;
    v218 = "objectForKeyedSubscript:";
    do
    {
      if ( *(id *)v150 != v210 )
        objc_enumerationMutation(obj);
      v7 = *(void **)(*((_QWORD *)&v149 + 1) + 8 * v6);
      v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 1LL);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v217 = v7;
      v10 = _objc_msgSend(v7, v5, v9);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
      if ( (unsigned __int8)_objc_msgSend(v11, v220, v12) )
      {
        v13 = v222;
        if ( ((double (__fastcall *)(id, SEL))_objc_msgSend)(v11, v222) != 4294967295.0
          && ((double (__fastcall *)(id, SEL))_objc_msgSend)(v11, v13) != 2147483648.0 )
        {
          v14 = v223;
          v15 = _objc_msgSend(v223->_idModelDict, v218, v11);
          v16 = objc_retainAutoreleasedReturnValue(v15);
          if ( !v16 )
          {
            v17 = objc_alloc((Class)&OBJC_CLASS___TradeZhuBiModel);
            v18 = _objc_msgSend(v17, v193);
            _objc_msgSend(v18, v194, v11);
            _objc_msgSend(v14->_idModelDict, (SEL)location, v18, v11);
            v225 = v18;
            _objc_msgSend(v196, v157, v18);
            v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 56LL);
            v20 = objc_retainAutoreleasedReturnValue(v19);
            v21 = _objc_msgSend(v217, v218, v20);
            v22 = objc_retainAutoreleasedReturnValue(v21);
            v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
            v24 = (__int64)_objc_msgSend(v22, v220, v23);
            v208 = v22;
            if ( v24 )
            {
              v25 = v22;
              v26 = v222;
              if ( ((double (__fastcall *)(id, SEL))_objc_msgSend)(v25, v222) != 4294967295.0
                && ((double (__fastcall *)(id, const char *))_objc_msgSend)(v208, v26) != 2147483648.0 )
              {
                _objc_msgSend(v208, v26);
                v27 = v225;
                _objc_msgSend(v225, v158);
                v28 = v27;
                v29 = v27;
                v30 = v175;
                v31 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v29, v175);
                if ( v190 == v31 )
                {
                  v36 = (__int64)v209 + 1;
                  if ( (unsigned __int64)v209 < 2 )
                    v36 = 2LL;
                  v209 = (id)v36;
                  v37 = _objc_msgSend(&OBJC_CLASS___NSString, v182, CFSTR("%ld"));
                  v38 = objc_retainAutoreleasedReturnValue(v37);
                  _objc_msgSend(v28, v176, v38);
                  v39 = v223;
                  if ( !v223->_type )
                  {
                    _objc_msgSend(v28, v175);
                    v40 = _objc_msgSend(&OBJC_CLASS___NSDate, v179);
                    v204 = objc_retainAutoreleasedReturnValue(v40);
                    v41 = _objc_msgSend(v223->dataFormatter, v180, v204);
                    v42 = objc_retainAutoreleasedReturnValue(v41);
                    _objc_msgSend(v28, v181, v42);
                    v39 = v223;
                  }
                  _objc_msgSend(v28, v177, v39->orangeLineColor);
                  _objc_msgSend(v28, v178, 1LL);
                }
                else
                {
                  v31 = ((double (__fastcall *)(void *, const char *))_objc_msgSend)(v28, v30);
                  v190 = v31;
                  _objc_msgSend(v28, v30);
                  v32 = _objc_msgSend(&OBJC_CLASS___NSDate, v179);
                  v209 = objc_retainAutoreleasedReturnValue(v32);
                  v33 = v223;
                  v34 = _objc_msgSend(v223->dataFormatter, v180, v209);
                  v35 = objc_retainAutoreleasedReturnValue(v34);
                  _objc_msgSend(v28, v176, v35);
                  _objc_msgSend(v28, v181, v35);
                  _objc_msgSend(v28, v177, v33->normalTextColor);
                  _objc_msgSend(v28, v178, 0LL);
                  v209 = 0LL;
                }
                v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 10LL);
                v44 = objc_retainAutoreleasedReturnValue(v43);
                v45 = _objc_msgSend(v217, v218, v44);
                v46 = objc_retainAutoreleasedReturnValue(v45);
                _objc_msgSend(v46, v222);
                if ( v31 != 4294967295.0 && v31 != 2147483648.0 )
                {
                  v47 = v223;
                  precisionType = v223->precisionType;
                  *(double *)&v224 = v31;
                  v49 = _objc_msgSend(&OBJC_CLASS___HXTools, v159, v46, precisionType);
                  v50 = objc_retainAutoreleasedReturnValue(v49);
                  _objc_msgSend(v225, v160, v50);
                  zuoShou = v47->_zuoShou;
                  v52 = _objc_msgSend(&OBJC_CLASS___HXTools, v161, zuoShou, *(double *)&v224);
                  v53 = objc_retainAutoreleasedReturnValue(v52);
                  _objc_msgSend(v225, v162, v53);
                  v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 12LL);
                  v55 = objc_retainAutoreleasedReturnValue(v54);
                  v56 = _objc_msgSend(v217, v218, v55);
                  v57 = objc_retainAutoreleasedReturnValue(v56);
                  v205 = v46;
                  v58 = v57;
                  v59 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
                  v60 = (unsigned __int8)_objc_msgSend(v58, v220, v59);
                  v204 = v58;
                  if ( v60 )
                  {
                    _objc_msgSend(v58, v222);
                    if ( zuoShou != 4294967295.0 )
                    {
                      _objc_msgSend(v58, v222);
                      if ( zuoShou != 2147483648.0 )
                      {
                        v61 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 13LL);
                        v62 = objc_retainAutoreleasedReturnValue(v61);
                        v63 = _objc_msgSend(v217, v218, v62);
                        v64 = objc_retainAutoreleasedReturnValue(v63);
                        v65 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
                        if ( !(unsigned __int8)_objc_msgSend(v64, v220, v65) )
                          goto LABEL_30;
                        v66 = v64;
                        v221 = v64;
                        v67 = v222;
                        _objc_msgSend(v66, v222);
                        if ( zuoShou == 4294967295.0 || (_objc_msgSend(v221, v67), zuoShou == 2147483648.0) )
                        {
                          v64 = v221;
                          goto LABEL_30;
                        }
                        v173 = (double)(int)_objc_msgSend(v221, v211);
                        v69 = v173 / v223->volumeUint;
                        v70 = CFSTR("%.0f");
                        if ( v69 < 1.0 )
                          v70 = CFSTR("%.1f");
                        v71 = _objc_msgSend(&OBJC_CLASS___NSString, v182, v70);
                        v72 = objc_retainAutoreleasedReturnValue(v71);
                        v73 = v225;
                        _objc_msgSend(v225, v183, v72);
                        v74 = _objc_msgSend(v73, v199);
                        v75 = objc_retainAutoreleasedReturnValue(v74);
                        v76 = _objc_msgSend(v75, v211);
                        _objc_msgSend(v73, v163, v76);
                        v77 = _objc_msgSend(v204, v211);
                        v174 = v77;
                        if ( v77 == (id)1 )
                        {
                          v78 = _objc_msgSend(v225, v199);
                          v191 = objc_retainAutoreleasedReturnValue(v78);
                          v79 = _objc_msgSend(v191, v200, CFSTR("↓"));
                          v192 = p_fallTextColor;
                          v64 = v221;
                          goto LABEL_43;
                        }
                        v64 = v221;
                        if ( v77 == (id)5 )
                        {
                          v80 = _objc_msgSend(v225, v199);
                          v191 = objc_retainAutoreleasedReturnValue(v80);
                          v79 = _objc_msgSend(v191, v200, CFSTR("↑"));
                          v192 = p_riseTextColor;
LABEL_43:
                          v81 = objc_retainAutoreleasedReturnValue(v79);
                          _objc_msgSend(v225, v183, v81);
                          _objc_msgSend(v225, v164, *v192);
                        }
                        v82 = v223;
                        if ( v223->_disPlayMoney )
                        {
                          v69 = *(double *)&v224 * v173;
                          v83 = _objc_msgSend(&OBJC_CLASS___HXTools, v165, *(double *)&v224 * v173);
                          v84 = objc_retainAutoreleasedReturnValue(v83);
                          _objc_msgSend(v225, v197, v84);
                          if ( v174 == (id)1 )
                          {
                            v85 = _objc_msgSend(v225, v185);
                            *(double *)&v224 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v85));
                            v86 = _objc_msgSend(v224, v200, CFSTR("↓"));
                            goto LABEL_49;
                          }
                          v82 = v223;
                          if ( v174 == (id)5 )
                          {
                            v87 = _objc_msgSend(v225, v185);
                            *(double *)&v224 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v87));
                            v86 = _objc_msgSend(v224, v200, CFSTR("↑"));
LABEL_49:
                            v88 = objc_retainAutoreleasedReturnValue(v86);
                            _objc_msgSend(v225, v197, v88);
                            v82 = v223;
                          }
                        }
                        if ( !v82->_type )
                        {
                          v89 = _objc_msgSend(v225, v199);
                          v90 = objc_retainAutoreleasedReturnValue(v89);
                          *(double *)&v224 = COERCE_DOUBLE(_objc_msgSend(v90, v166));
                          if ( (__int64)v224 < 7 )
                          {
                            if ( v224 == (id)6 )
                            {
                              v69 = 11.0;
                              v91 = _objc_msgSend(&OBJC_CLASS___NSFont, v198, 11.0);
                            }
                            else
                            {
                              v69 = 13.0;
                              v91 = _objc_msgSend(&OBJC_CLASS___NSFont, v198, 13.0);
                            }
                          }
                          else
                          {
                            v69 = 10.0;
                            v91 = _objc_msgSend(&OBJC_CLASS___NSFont, v198, 10.0);
                          }
                          v92 = objc_retainAutoreleasedReturnValue(v91);
                          _objc_msgSend(v225, v167, v92);
                        }
                        v93 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 74LL);
                        v94 = objc_retainAutoreleasedReturnValue(v93);
                        v95 = _objc_msgSend(v217, v184, v94);
                        *(double *)&v224 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v95));
                        v96 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
                        if ( (unsigned __int8)_objc_msgSend(v224, v220, v96) )
                        {
                          v97 = v222;
                          _objc_msgSend(v224, v222);
                          if ( v69 != 4294967295.0 )
                          {
                            _objc_msgSend(v224, v97);
                            if ( v69 != 2147483648.0 )
                            {
                              v98 = v224;
                              _objc_msgSend(v225, v168, v224);
                              v99 = v211;
                              v100 = _objc_msgSend(v98, v211);
                              if ( v203 == v100 )
                              {
                                v101 = v214 + 1;
                                v102 = v225;
                                v103 = _objc_msgSend(v225, v201);
                                v213 = &v213[(_QWORD)v103];
                                _objc_msgSend(v102, v187);
                                v214 = v101;
                                _objc_msgSend(v102, v186, v101);
                              }
                              else
                              {
                                v203 = _objc_msgSend(v98, v99);
                                v104 = v225;
                                v105 = (char *)_objc_msgSend(v225, v201);
                                _objc_msgSend(v104, v186, 0LL);
                                v213 = v105;
                                _objc_msgSend(v104, v187, v105);
                                v214 = 0LL;
                              }
                              v106 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 75LL);
                              v107 = objc_retainAutoreleasedReturnValue(v106);
                              v108 = _objc_msgSend(v217, v184, v107);
                              v109 = objc_retainAutoreleasedReturnValue(v108);
                              v110 = _objc_msgSend(&OBJC_CLASS___NSNumber, v216);
                              if ( (unsigned __int8)_objc_msgSend(v109, v220, v110) )
                              {
                                _objc_msgSend(v109, v222);
                                if ( v69 != 4294967295.0 )
                                {
                                  _objc_msgSend(v109, v222);
                                  if ( v69 != 2147483648.0 )
                                  {
                                    _objc_msgSend(v225, v169, v109);
                                    v111 = _objc_msgSend(v109, v211);
                                    if ( v202 == v111 )
                                    {
                                      v112 = v212 + 1;
                                      v113 = _objc_msgSend(v225, v201);
                                      v215 = &v215[(_QWORD)v113];
                                      _objc_msgSend(v225, v189);
                                      v212 = v112;
                                      _objc_msgSend(v225, v188, v112);
                                    }
                                    else
                                    {
                                      v202 = _objc_msgSend(v109, v211);
                                      v114 = v225;
                                      v215 = (char *)_objc_msgSend(v225, v201);
                                      _objc_msgSend(v114, v188, 0LL);
                                      _objc_msgSend(v114, v189, v215);
                                      v212 = 0LL;
                                    }
                                  }
                                }
                              }
                            }
                          }
                          v64 = v221;
                        }
LABEL_30:
                      }
                    }
                  }
                  v46 = v205;
                }
              }
            }
            v16 = v225;
          }
        }
      }
      v6 = v68 + 1;
      v5 = v218;
    }
    while ( v6 < (unsigned __int64)v206 );
    v115 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v149, v227, 16LL);
    v206 = v115;
  }
  while ( v115 );
LABEL_75:
  location = (id *)&v223->_tradeZhuBiArrayController;
  WeakRetained = objc_loadWeakRetained((id *)&v223->_tradeZhuBiArrayController);
  v117 = _objc_msgSend(WeakRetained, "arrangedObjects");
  v118 = objc_retainAutoreleasedReturnValue(v117);
  v156 = 0LL;
  v155 = 0LL;
  v154 = 0LL;
  v153 = 0LL;
  v210 = objc_retain(v118);
  v119 = &_objc_msgSend;
  v120 = (const char *)_objc_msgSend(v210, "countByEnumeratingWithState:objects:count:", &v153, v226, 16LL);
  if ( v120 )
  {
    v121 = v120;
    v216 = *(SEL *)v154;
    v122 = 1;
    LOBYTE(v119) = 1;
    do
    {
      obj = "buyNumber";
      v220 = "integerValue";
      v222 = "volValue";
      v193 = "setTotalBuyVolValue:";
      v194 = "setBuyIndex:";
      v217 = "saleNumber";
      v208 = "setTotalSaleVolValue:";
      v225 = "setSaleIndex:";
      v219 = v121;
      for ( i = 0LL; i != v219; ++i )
      {
        if ( *(SEL *)v154 != v216 )
          objc_enumerationMutation(v210);
        v124 = *(char **)(*((_QWORD *)&v153 + 1) + 8LL * (_QWORD)i);
        v125 = 0;
        v218 = v124;
        if ( v122 )
        {
          LODWORD(v206) = (_DWORD)v119;
          v126 = _objc_msgSend(v124, (SEL)obj);
          v127 = objc_retainAutoreleasedReturnValue(v126);
          v128 = _objc_msgSend(v127, v220);
          v125 = 0;
          if ( v128 == v203 )
          {
            v130 = v214 + 1;
            v131 = _objc_msgSend(v129, v222);
            v213 = &v213[(_QWORD)v131];
            _objc_msgSend(v132, v193);
            v214 = v130;
            _objc_msgSend(v133, v194, v130);
            v125 = 1;
            v122 = 1;
          }
          else
          {
            v122 = 0;
          }
          LOBYTE(v119) = (_BYTE)v206;
        }
        else
        {
          v122 = 0;
        }
        if ( (_BYTE)v119
          && (v134 = _objc_msgSend((id)v218, (SEL)v217),
              v135 = objc_retainAutoreleasedReturnValue(v134),
              v136 = _objc_msgSend(v135, v220),
              v137 = v136 == v202,
              v122 = v138,
              v137) )
        {
          v119 = (id (**)(id, SEL, ...))(v212 + 1);
          v139 = (char *)v218;
          v140 = _objc_msgSend((id)v218, v222);
          v215 = &v215[(_QWORD)v140];
          v141(v139, (SEL)v208);
          v212 = (__int64)v119;
          v142(v139, (SEL)v225, v119);
          LOBYTE(v119) = 1;
        }
        else
        {
          if ( !v125 )
            goto LABEL_93;
          LODWORD(v119) = 0;
        }
      }
      v121 = (const char *)_objc_msgSend(v210, "countByEnumeratingWithState:objects:count:", &v153, v226, 16LL);
    }
    while ( v121 );
  }
LABEL_93:
  v143 = location;
  v144 = objc_loadWeakRetained(location);
  _objc_msgSend(v144, "addObjects:", v196);
  v145(v144);
  v146 = objc_loadWeakRetained(v143);
  _objc_msgSend(v146, "rearrangeObjects");
  v147(v146);
  reloadBlock = (void (**)(void))v223->_reloadBlock;
  v3 = v170;
  if ( reloadBlock )
    reloadBlock[2]();
LABEL_96:
}

//----- (0000000100C07E34) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem updateZhubiData:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  NSNumber *v7; // rax
  NSNumber *v8; // rbx
  NSString *v10; // rax
  NSString *v12; // rax
  NSString *v13; // rbx
  id WeakRetained; // rbx
  SEL v33; // r12
  SEL v36; // r12
  unsigned __int64 v38; // r14
  NSNumber *v40; // rax
  NSNumber *v41; // rbx
  SEL v42; // r12
  TradeZhuBiQuoteItem *v48; // r13
  TradeZhuBiQuoteItem *v52; // rbx
  NSMutableDictionary *idModelDict; // rdi
  NSMutableArray *tempPushArray; // rdi
  NSNumber *v58; // rax
  NSNumber *v59; // rbx
  NSNumber *v61; // rdi
  TradeZhuBiQuoteItem *v72; // rax
  NSNumber *v76; // rax
  NSNumber *v77; // r13
  unsigned __int64 precisionType; // rcx
  NSNumber *v86; // rax
  NSNumber *v87; // rbx
  NSNumber *v89; // rdi
  NSNumber *v93; // rax
  NSNumber *v94; // rbx
  NSNumber *v96; // rdi
  __CFString *v102; // rdx
  NSColor **v113; // rcx
  NSNumber *v128; // rax
  NSNumber *v129; // rbx
  NSNumber *v131; // rdi
  NSNumber *v141; // rax
  NSNumber *v142; // rbx
  NSNumber *v144; // rdi
  id *p_tradeZhuBiArrayController; // r14
  SEL v169; // [rsp+50h] [rbp-2E0h]
  SEL v170; // [rsp+58h] [rbp-2D8h]
  SEL v171; // [rsp+60h] [rbp-2D0h]
  SEL v172; // [rsp+68h] [rbp-2C8h]
  SEL v173; // [rsp+70h] [rbp-2C0h]
  SEL v174; // [rsp+78h] [rbp-2B8h]
  SEL v175; // [rsp+80h] [rbp-2B0h]
  SEL v176; // [rsp+88h] [rbp-2A8h]
  SEL v177; // [rsp+90h] [rbp-2A0h]
  SEL v178; // [rsp+98h] [rbp-298h]
  SEL v179; // [rsp+A0h] [rbp-290h]
  SEL v180; // [rsp+A8h] [rbp-288h]
  SEL v181; // [rsp+B0h] [rbp-280h]
  SEL v182; // [rsp+B8h] [rbp-278h]
  SEL v183; // [rsp+C0h] [rbp-270h]
  SEL v184; // [rsp+C8h] [rbp-268h]
  SEL v185; // [rsp+D0h] [rbp-260h]
  NSColor **p_fallTextColor; // [rsp+E8h] [rbp-248h]
  NSColor **p_riseTextColor; // [rsp+F0h] [rbp-240h]
  SEL v193; // [rsp+110h] [rbp-220h]
  SEL v194; // [rsp+118h] [rbp-218h]
  SEL v195; // [rsp+120h] [rbp-210h]
  SEL v196; // [rsp+128h] [rbp-208h]
  SEL v197; // [rsp+130h] [rbp-200h]
  SEL v198; // [rsp+138h] [rbp-1F8h]
  SEL v199; // [rsp+140h] [rbp-1F0h]
  SEL v200; // [rsp+148h] [rbp-1E8h]
  SEL v201; // [rsp+150h] [rbp-1E0h]
  SEL v202; // [rsp+158h] [rbp-1D8h]
  SEL v203; // [rsp+160h] [rbp-1D0h]
  SEL v204; // [rsp+168h] [rbp-1C8h]
  SEL v205; // [rsp+170h] [rbp-1C0h]
  SEL v206; // [rsp+178h] [rbp-1B8h]
  NSColor **v212; // [rsp+1A8h] [rbp-188h]
  id obj; // [rsp+1B0h] [rbp-180h]
  SEL v214; // [rsp+1B8h] [rbp-178h]
  SEL v215; // [rsp+1C0h] [rbp-170h]
  SEL v220; // [rsp+1E8h] [rbp-148h]
  SEL v221; // [rsp+1F0h] [rbp-140h]
  SEL v222; // [rsp+1F8h] [rbp-138h]
  unsigned __int64 v228; // [rsp+228h] [rbp-108h]
  SEL v229; // [rsp+230h] [rbp-100h]
  SEL v230; // [rsp+238h] [rbp-F8h]
  SEL v231; // [rsp+240h] [rbp-F0h]
  SEL v235; // [rsp+260h] [rbp-D0h]
  TradeZhuBiQuoteItem *v237; // [rsp+270h] [rbp-C0h]

  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 )
  {
    v6 = _objc_msgSend(v4, "dicExt");
    v232 = objc_retainAutoreleasedReturnValue(v6);
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v232, "thsStringForKey:", v8);
    v207 = objc_retainAutoreleasedReturnValue(v9);
    v10 = -[TradeZhuBiQuoteItem market](self, "market");
    objc_retainAutoreleasedReturnValue(v10);
    v208 = v11;
    v237 = self;
    v12 = -[TradeZhuBiQuoteItem stockCode](self, "stockCode");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v15 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
            &OBJC_CLASS___HXTools,
            "getMarketAndCodeByAppendingMarket:andStockCode:",
            v14,
            v13);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    if ( (unsigned __int8)_objc_msgSend(v207, "isEqualToString:", v16) )
    {
      v186 = v16;
      v19 = _objc_msgSend(v18, "arrBody");
      v209 = objc_retainAutoreleasedReturnValue(v19);
      if ( _objc_msgSend(v209, "count") )
      {
        if ( _objc_msgSend(v237->_tempPushArray, "count") )
        {
          v20 = _objc_msgSend(v237->_tempPushArray, "lastObject");
          v21 = objc_retainAutoreleasedReturnValue(v20);
        }
        else
        {
          WeakRetained = objc_loadWeakRetained((id *)&v237->_tradeZhuBiArrayController);
          v23 = _objc_msgSend(WeakRetained, "arrangedObjects");
          v24 = objc_retainAutoreleasedReturnValue(v23);
          v25 = _objc_msgSend(v24, "lastObject");
          v21 = objc_retainAutoreleasedReturnValue(v25);
        }
        if ( v21 )
        {
          _objc_msgSend(v21, "time");
          v216 = v3;
          v26 = _objc_msgSend(v21, "name");
          v27 = objc_retainAutoreleasedReturnValue(v26);
          _objc_msgSend(v27, "containsString:", CFSTR(":"));
          if ( v28 )
          {
            v228 = 0LL;
          }
          else
          {
            v29 = _objc_msgSend(v21, "name");
            v30 = objc_retainAutoreleasedReturnValue(v29);
            v228 = (unsigned __int64)_objc_msgSend(v30, "integerValue");
          }
          v31 = _objc_msgSend(v21, "buyNumber");
          v32 = objc_retainAutoreleasedReturnValue(v31);
          v217 = _objc_msgSend(v32, v33);
          v223 = (char *)_objc_msgSend(v21, "buyIndex");
          v34 = _objc_msgSend(v21, "saleNumber");
          v35 = objc_retainAutoreleasedReturnValue(v34);
          v218 = _objc_msgSend(v35, v36);
          v226 = (char *)_objc_msgSend(v21, "saleIndex");
          v224 = (char *)_objc_msgSend(v21, "totalBuyVolValue");
          v225 = (char *)_objc_msgSend(v21, "totalSaleVolValue");
        }
        else
        {
          v216 = 0.0;
          v225 = 0LL;
          v224 = 0LL;
          v226 = 0LL;
          v218 = 0LL;
          v223 = 0LL;
          v217 = 0LL;
          v228 = 0LL;
        }
        v187 = v21;
        zuoShou = 0.0;
        v166 = 0LL;
        v165 = 0LL;
        v164 = 0LL;
        v163 = 0LL;
        obj = objc_retain(v209);
        v192 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v163, v239, 16LL);
        if ( v192 )
        {
          v167 = *(_QWORD *)v164;
          p_riseTextColor = &v237->riseTextColor;
          p_fallTextColor = &v237->fallTextColor;
          do
          {
            v229 = "class";
            v230 = "isKindOfClass:";
            v235 = "doubleValue";
            v169 = "init";
            v170 = "setSortNumber:";
            v171 = "setObject:forKeyedSubscript:";
            v172 = "addObject:";
            v173 = "setTime:";
            v174 = "time";
            v199 = "stringWithFormat:";
            v193 = "setName:";
            v196 = "dateWithTimeIntervalSince1970:";
            v197 = "stringFromDate:";
            v198 = "setTimeString:";
            v194 = "setNameColor:";
            v195 = "setLineHidden:";
            v175 = "stringNumber:withFormat:";
            v176 = "setPrice:";
            v177 = "getJudgedColorOfTargetValue:JudgedValue:";
            v178 = "setPriceColor:";
            v231 = "integerValue";
            v200 = "setVol:";
            v220 = "vol";
            v179 = "setVolValue:";
            v221 = "stringByAppendingString:";
            v180 = "setVolColor:";
            v181 = "tenThousandWithNumber:";
            v202 = "money";
            v182 = "length";
            v215 = "systemFontOfSize:";
            v183 = "setVolFont:";
            v201 = "thsNumberForKey:";
            v184 = "setBuyNumber:";
            v222 = "volValue";
            v203 = "setBuyIndex:";
            v204 = "setTotalBuyVolValue:";
            v185 = "setSaleNumber:";
            v205 = "setSaleIndex:";
            v206 = "setTotalSaleVolValue:";
            v38 = 0LL;
            v232 = "objectForKeyedSubscript:";
            v214 = "setMoney:";
            do
            {
              if ( *(_QWORD *)v164 != v167 )
                objc_enumerationMutation(obj);
              v39 = *(void **)(*((_QWORD *)&v163 + 1) + 8 * v38);
              v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
              v41 = objc_retainAutoreleasedReturnValue(v40);
              v234 = v39;
              v43 = _objc_msgSend(v39, v42, v41);
              objc_retainAutoreleasedReturnValue(v43);
              v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
              if ( (unsigned __int8)_objc_msgSend(v45, v230, v44) )
              {
                v47 = v235;
                _objc_msgSend(v46, v235);
                if ( zuoShou != 4294967295.0 )
                {
                  _objc_msgSend(v46, v47);
                  if ( zuoShou != 2147483648.0 )
                  {
                    v48 = v237;
                    v49 = _objc_msgSend(v237->_idModelDict, (SEL)v232, v46);
                    v50 = objc_retainAutoreleasedReturnValue(v49);
                    if ( !v50 )
                    {
                      v51 = objc_alloc((Class)&OBJC_CLASS___TradeZhuBiModel);
                      v52 = v48;
                      v53 = _objc_msgSend(v51, v169);
                      _objc_msgSend(v53, v170, v54);
                      idModelDict = v52->_idModelDict;
                      v227 = v56;
                      _objc_msgSend(idModelDict, v171, v53, v56);
                      tempPushArray = v52->_tempPushArray;
                      v238 = v53;
                      _objc_msgSend(tempPushArray, v172, v53);
                      v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 56LL);
                      v59 = objc_retainAutoreleasedReturnValue(v58);
                      v60 = _objc_msgSend(v234, (SEL)v232, v59);
                      v61 = v59;
                      v62 = objc_retainAutoreleasedReturnValue(v60);
                      v63 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
                      v64 = (unsigned __int8)_objc_msgSend(v62, v230, v63);
                      v168 = v62;
                      if ( v64 )
                      {
                        _objc_msgSend(v62, v235);
                        if ( zuoShou != 4294967295.0 )
                        {
                          _objc_msgSend(v62, v235);
                          if ( zuoShou != 2147483648.0 )
                          {
                            _objc_msgSend(v62, v235);
                            v65 = v238;
                            _objc_msgSend(v238, v173);
                            v66 = v174;
                            _objc_msgSend(v65, v174);
                            if ( v216 == zuoShou )
                            {
                              v70 = v228 + 1;
                              if ( v228 < 2 )
                                v70 = 2LL;
                              v228 = v70;
                              v71 = _objc_msgSend(&OBJC_CLASS___NSString, v199, CFSTR("%ld"));
                              v236 = objc_retainAutoreleasedReturnValue(v71);
                              _objc_msgSend(v65, v193, v236);
                              v72 = v237;
                              if ( !v237->_type )
                              {
                                v236 = &OBJC_CLASS___NSDate;
                                _objc_msgSend(v65, v66);
                                v73 = _objc_msgSend(v236, v196);
                                v236 = objc_retainAutoreleasedReturnValue(v73);
                                v74 = _objc_msgSend(v237->dataFormatter, v197, v236);
                                v75 = objc_retainAutoreleasedReturnValue(v74);
                                _objc_msgSend(v65, v198, v75);
                                v72 = v237;
                              }
                              _objc_msgSend(v65, v194, v72->orangeLineColor);
                              _objc_msgSend(v65, v195, 1LL);
                            }
                            else
                            {
                              _objc_msgSend(v65, v66);
                              v216 = zuoShou;
                              v236 = &OBJC_CLASS___NSDate;
                              _objc_msgSend(v65, v66);
                              v67 = _objc_msgSend(v236, v196);
                              v236 = objc_retainAutoreleasedReturnValue(v67);
                              v68 = _objc_msgSend(v237->dataFormatter, v197, v236);
                              v69 = objc_retainAutoreleasedReturnValue(v68);
                              _objc_msgSend(v65, v193, v69);
                              _objc_msgSend(v65, v198, v69);
                              _objc_msgSend(v65, v194, v237->normalTextColor);
                              _objc_msgSend(v65, v195, 0LL);
                              v228 = 0LL;
                            }
                            v76 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
                            v77 = objc_retainAutoreleasedReturnValue(v76);
                            v78 = _objc_msgSend(v234, (SEL)v232, v77);
                            v79 = objc_retainAutoreleasedReturnValue(v78);
                            v236 = v79;
                            _objc_msgSend(v79, v235);
                            if ( zuoShou != 4294967295.0 && zuoShou != 2147483648.0 )
                            {
                              precisionType = v237->precisionType;
                              *(double *)&v233 = zuoShou;
                              v81 = _objc_msgSend(&OBJC_CLASS___HXTools, v175, v236, precisionType);
                              v82 = objc_retainAutoreleasedReturnValue(v81);
                              v83 = v238;
                              _objc_msgSend(v238, v176, v82);
                              zuoShou = v237->_zuoShou;
                              v84 = _objc_msgSend(&OBJC_CLASS___HXTools, v177, zuoShou, *(double *)&v233);
                              v85 = objc_retainAutoreleasedReturnValue(v84);
                              _objc_msgSend(v83, v178, v85);
                              v86 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12LL);
                              v87 = objc_retainAutoreleasedReturnValue(v86);
                              v88 = _objc_msgSend(v234, (SEL)v232, v87);
                              v89 = v87;
                              v90 = objc_retainAutoreleasedReturnValue(v88);
                              v91 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
                              v92 = (unsigned __int8)_objc_msgSend(v90, v230, v91);
                              v210 = v90;
                              if ( v92 )
                              {
                                _objc_msgSend(v90, v235);
                                if ( zuoShou != 4294967295.0 )
                                {
                                  _objc_msgSend(v90, v235);
                                  if ( zuoShou != 2147483648.0 )
                                  {
                                    v93 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
                                    v94 = objc_retainAutoreleasedReturnValue(v93);
                                    v95 = _objc_msgSend(v234, (SEL)v232, v94);
                                    v96 = v94;
                                    v97 = objc_retainAutoreleasedReturnValue(v95);
                                    v98 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
                                    v99 = (unsigned __int8)_objc_msgSend(v97, v230, v98);
                                    v219 = v97;
                                    if ( v99 )
                                    {
                                      v100 = v97;
                                      v101 = v235;
                                      _objc_msgSend(v100, v235);
                                      if ( zuoShou != 4294967295.0 )
                                      {
                                        _objc_msgSend(v219, v101);
                                        if ( zuoShou != 2147483648.0 )
                                        {
                                          v190 = (double)(int)_objc_msgSend(v219, v231);
                                          zuoShou = v190 / v237->volumeUint;
                                          v102 = CFSTR("%.0f");
                                          if ( zuoShou < 1.0 )
                                            v102 = CFSTR("%.1f");
                                          v103 = _objc_msgSend(&OBJC_CLASS___NSString, v199, v102);
                                          v104 = objc_retainAutoreleasedReturnValue(v103);
                                          v105 = v238;
                                          _objc_msgSend(v238, v200, v104);
                                          v106 = _objc_msgSend(v105, v220);
                                          v107 = objc_retainAutoreleasedReturnValue(v106);
                                          v108 = _objc_msgSend(v107, v231);
                                          _objc_msgSend(v105, v179, v108);
                                          v109 = _objc_msgSend(v210, v231);
                                          v191 = v109;
                                          if ( v109 == (id)1 )
                                          {
                                            v110 = v238;
                                            v111 = _objc_msgSend(v238, v220);
                                            v211 = objc_retainAutoreleasedReturnValue(v111);
                                            v112 = _objc_msgSend(v211, v221, CFSTR("↓"));
                                            v113 = p_fallTextColor;
                                            goto LABEL_50;
                                          }
                                          if ( v109 == (id)5 )
                                          {
                                            v110 = v238;
                                            v114 = _objc_msgSend(v238, v220);
                                            v211 = objc_retainAutoreleasedReturnValue(v114);
                                            v112 = _objc_msgSend(v211, v221, CFSTR("↑"));
                                            v113 = p_riseTextColor;
LABEL_50:
                                            v212 = v113;
                                            v115 = objc_retainAutoreleasedReturnValue(v112);
                                            _objc_msgSend(v110, v200, v115);
                                            _objc_msgSend(v110, v180, *v212);
                                          }
                                          if ( v237->_disPlayMoney )
                                          {
                                            zuoShou = *(double *)&v233 * v190;
                                            v116 = _objc_msgSend(&OBJC_CLASS___HXTools, v181, *(double *)&v233 * v190);
                                            v117 = objc_retainAutoreleasedReturnValue(v116);
                                            v118 = v238;
                                            _objc_msgSend(v238, v214, v117);
                                            if ( v191 == (id)1 )
                                            {
                                              v119 = _objc_msgSend(v118, v202);
                                              *(double *)&v233 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v119));
                                              v120 = _objc_msgSend(v233, v221, CFSTR("↓"));
                                              goto LABEL_56;
                                            }
                                            if ( v191 == (id)5 )
                                            {
                                              v118 = v238;
                                              v121 = _objc_msgSend(v238, v202);
                                              *(double *)&v233 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v121));
                                              v120 = _objc_msgSend(v233, v221, CFSTR("↑"));
LABEL_56:
                                              v122 = objc_retainAutoreleasedReturnValue(v120);
                                              _objc_msgSend(v118, v214, v122);
                                            }
                                          }
                                          if ( !v237->_type )
                                          {
                                            v123 = _objc_msgSend(v238, v220);
                                            v124 = objc_retainAutoreleasedReturnValue(v123);
                                            v125 = (__int64)_objc_msgSend(v124, v182);
                                            if ( v125 < 7 )
                                            {
                                              if ( v125 == 6 )
                                              {
                                                zuoShou = 11.0;
                                                v126 = _objc_msgSend(&OBJC_CLASS___NSFont, v215, 11.0);
                                              }
                                              else
                                              {
                                                zuoShou = 13.0;
                                                v126 = _objc_msgSend(&OBJC_CLASS___NSFont, v215, 13.0);
                                              }
                                            }
                                            else
                                            {
                                              zuoShou = 10.0;
                                              v126 = _objc_msgSend(&OBJC_CLASS___NSFont, v215, 10.0);
                                            }
                                            v127 = objc_retainAutoreleasedReturnValue(v126);
                                            _objc_msgSend(v238, v183, v127);
                                          }
                                          v128 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 74LL);
                                          v129 = objc_retainAutoreleasedReturnValue(v128);
                                          v130 = _objc_msgSend(v234, v201, v129);
                                          v131 = v129;
                                          v132 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v130));
                                          v133 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
                                          v134 = (unsigned __int8)_objc_msgSend(*(id *)&v132, v230, v133);
                                          *(double *)&v233 = v132;
                                          if ( v134 )
                                          {
                                            _objc_msgSend(*(id *)&v132, v235);
                                            if ( zuoShou != 4294967295.0 )
                                            {
                                              _objc_msgSend(*(id *)&v132, v235);
                                              if ( zuoShou != 2147483648.0 )
                                              {
                                                v135 = v238;
                                                v136 = v233;
                                                _objc_msgSend(v238, v184, v233);
                                                v137 = _objc_msgSend(v136, v231);
                                                if ( v217 == v137 )
                                                {
                                                  v138 = v223 + 1;
                                                  v139 = _objc_msgSend(v135, v222);
                                                  v224 = &v224[(_QWORD)v139];
                                                  _objc_msgSend(v135, v204);
                                                  v223 = v138;
                                                  _objc_msgSend(v135, v203, v138);
                                                }
                                                else
                                                {
                                                  v217 = _objc_msgSend(v136, v231);
                                                  v140 = (char *)_objc_msgSend(v135, v222);
                                                  _objc_msgSend(v135, v203, 0LL);
                                                  v224 = v140;
                                                  _objc_msgSend(v135, v204, v140);
                                                  v223 = 0LL;
                                                }
                                                v141 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 75LL);
                                                v142 = objc_retainAutoreleasedReturnValue(v141);
                                                v143 = _objc_msgSend(v234, v201, v142);
                                                v144 = v142;
                                                v145 = objc_retainAutoreleasedReturnValue(v143);
                                                v146 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229);
                                                if ( (unsigned __int8)_objc_msgSend(v145, v230, v146) )
                                                {
                                                  _objc_msgSend(v145, v235);
                                                  if ( zuoShou != 4294967295.0 )
                                                  {
                                                    v234 = v145;
                                                    _objc_msgSend(v145, v235);
                                                    if ( zuoShou == 2147483648.0 )
                                                      goto LABEL_75;
                                                    v147 = v234;
                                                    _objc_msgSend(v238, v185, v234);
                                                    v148 = _objc_msgSend(v147, v231);
                                                    if ( v218 == v148 )
                                                    {
                                                      v149 = v226 + 1;
                                                      v150 = v238;
                                                      v151 = _objc_msgSend(v238, v222);
                                                      v225 = &v225[(_QWORD)v151];
                                                      _objc_msgSend(v150, v206);
                                                      v226 = v149;
                                                      _objc_msgSend(v150, v205, v149);
LABEL_75:
                                                      v145 = v234;
                                                    }
                                                    else
                                                    {
                                                      v218 = _objc_msgSend(v234, v231);
                                                      v152 = v238;
                                                      v153 = (char *)_objc_msgSend(v238, v222);
                                                      _objc_msgSend(v152, v205, 0LL);
                                                      v225 = v153;
                                                      _objc_msgSend(v152, v206, v153);
                                                      v226 = 0LL;
                                                      v145 = v154;
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                      v50 = v238;
                    }
                  }
                }
              }
              ++v38;
            }
            while ( v38 < (unsigned __int64)v192 );
            v155 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v163, v239, 16LL);
            v192 = v155;
          }
          while ( v155 );
        }
        if ( !v237->_countDowntimer || v237->_type == 1 )
        {
          p_tradeZhuBiArrayController = (id *)&v237->_tradeZhuBiArrayController;
          v157 = objc_loadWeakRetained((id *)&v237->_tradeZhuBiArrayController);
          _objc_msgSend(v157, "addObjects:", *(_QWORD *)(v158 + 208));
          _objc_msgSend(*(id *)(v159 + 208), "removeAllObjects");
          v160 = objc_loadWeakRetained(p_tradeZhuBiArrayController);
          _objc_msgSend(v160, "rearrangeObjects");
          v162 = *(_QWORD *)(v161 + 104);
          if ( v162 )
            (*(void (**)(void))(v162 + 16))();
        }
      }
      v16 = v186;
    }
  }
}

//----- (0000000100C09476) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem resetCountDownTimer](TradeZhuBiQuoteItem *self, SEL a2)
{
  NSTimer *v4; // rax
  NSTimer *countDowntimer; // rdi

  _objc_msgSend(self->_countDowntimer, "invalidate");
  v3 = _objc_msgSend(
         &OBJC_CLASS___NSTimer,
         "scheduledTimerWithTimeInterval:target:selector:userInfo:repeats:",
         self,
         "noOperationThenJump",
         0LL,
         0LL,
         10.0);
  v4 = (NSTimer *)objc_retainAutoreleasedReturnValue(v3);
  countDowntimer = self->_countDowntimer;
  self->_countDowntimer = v4;
}

//----- (0000000100C094E1) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem noOperationThenJump](TradeZhuBiQuoteItem *self, SEL a2)
{
  NSTimer *countDowntimer; // rdi
  id (*v4)(id, SEL, ...); // r12
  id WeakRetained; // r15

  _objc_msgSend(self->_countDowntimer, "invalidate");
  countDowntimer = self->_countDowntimer;
  self->_countDowntimer = 0LL;
  if ( v4(self->_tempPushArray, "count") )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
    v6(WeakRetained, "addObjects:", self->_tempPushArray);
    v7(self->_tempPushArray, "removeAllObjects");
    v8 = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
    v9(v8, "rearrangeObjects");
  }
  scrollToBottomBlock = (void (__fastcall **)(id, __int64))self->_scrollToBottomBlock;
  if ( scrollToBottomBlock )
    scrollToBottomBlock[2](scrollToBottomBlock, 1LL);
}

//----- (0000000100C095CB) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem getCurItemMarket](TradeZhuBiQuoteItem *self, SEL a2)
{
  return -[TradeZhuBiQuoteItem market](self, "market");
}

//----- (0000000100C095DD) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem getCurItemStockcode](TradeZhuBiQuoteItem *self, SEL a2)
{
  return -[TradeZhuBiQuoteItem stockCode](self, "stockCode");
}

//----- (0000000100C095EF) ----------------------------------------------------
unsigned __int64 __cdecl -[TradeZhuBiQuoteItem type](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_type;
}

//----- (0000000100C095F9) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setType:](TradeZhuBiQuoteItem *self, SEL a2, unsigned __int64 a3)
{
  self->_type = a3;
}

//----- (0000000100C09603) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem selectionIndexesBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 88LL, 0);
}

//----- (0000000100C09614) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setSelectionIndexesBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (0000000100C09623) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem scrollToBottomBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 96LL, 0);
}

//----- (0000000100C09634) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setScrollToBottomBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (0000000100C09643) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem reloadBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 104LL, 0);
}

//----- (0000000100C09654) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setReloadBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 104LL);
}

//----- (0000000100C09663) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem didLoadMoreData](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 112LL, 0);
}

//----- (0000000100C09674) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setDidLoadMoreData:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 112LL);
}

//----- (0000000100C09683) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem didFinishFirstRequestBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 120LL, 0);
}

//----- (0000000100C09694) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setDidFinishFirstRequestBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 120LL);
}

//----- (0000000100C096A3) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem viewDispalyedBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 128LL, 0);
}

//----- (0000000100C096B4) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setViewDispalyedBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 128LL);
}

//----- (0000000100C096C3) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem viewDispalyedRowsBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 136LL, 0);
}

//----- (0000000100C096D4) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setViewDispalyedRowsBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 136LL);
}

//----- (0000000100C096E3) ----------------------------------------------------
id __cdecl -[TradeZhuBiQuoteItem switchNumberOrMoneyStateBlock](TradeZhuBiQuoteItem *self, SEL a2)
{
  return objc_getProperty(self, a2, 144LL, 0);
}

//----- (0000000100C096F4) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setSwitchNumberOrMoneyStateBlock:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 144LL);
}

//----- (0000000100C09703) ----------------------------------------------------
NSArrayController *__cdecl -[TradeZhuBiQuoteItem tradeZhuBiArrayController](TradeZhuBiQuoteItem *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_tradeZhuBiArrayController);
  return (NSArrayController *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100C0971C) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setTradeZhuBiArrayController:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_tradeZhuBiArrayController, a3);
}

//----- (0000000100C09730) ----------------------------------------------------
char __cdecl -[TradeZhuBiQuoteItem disPlayMoney](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_disPlayMoney;
}

//----- (0000000100C0973A) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setDisPlayMoney:](TradeZhuBiQuoteItem *self, SEL a2, char a3)
{
  self->_disPlayMoney = a3;
}

//----- (0000000100C09743) ----------------------------------------------------
NSMutableDictionary *__cdecl -[TradeZhuBiQuoteItem idModelDict](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_idModelDict;
}

//----- (0000000100C09750) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setIdModelDict:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_idModelDict, a3);
}

//----- (0000000100C09764) ----------------------------------------------------
NSString *__cdecl -[TradeZhuBiQuoteItem stockCode](TradeZhuBiQuoteItem *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 168LL, 0);
}

//----- (0000000100C09775) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setStockCode:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 168LL);
}

//----- (0000000100C09784) ----------------------------------------------------
NSString *__cdecl -[TradeZhuBiQuoteItem market](TradeZhuBiQuoteItem *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 176LL, 0);
}

//----- (0000000100C09795) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setMarket:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 176LL);
}

//----- (0000000100C097A4) ----------------------------------------------------
double __cdecl -[TradeZhuBiQuoteItem zuoShou](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_zuoShou;
}

//----- (0000000100C097B2) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setZuoShou:](TradeZhuBiQuoteItem *self, SEL a2, double a3)
{
  self->_zuoShou = a3;
}

//----- (0000000100C097C0) ----------------------------------------------------
NSDictionary *__cdecl -[TradeZhuBiQuoteItem needSelectDict](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_needSelectDict;
}

//----- (0000000100C097CD) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setNeedSelectDict:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_needSelectDict, a3);
}

//----- (0000000100C097E1) ----------------------------------------------------
NSTimer *__cdecl -[TradeZhuBiQuoteItem countDowntimer](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_countDowntimer;
}

//----- (0000000100C097EE) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setCountDowntimer:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_countDowntimer, a3);
}

//----- (0000000100C09802) ----------------------------------------------------
char __cdecl -[TradeZhuBiQuoteItem isRequestingZhuBi](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_requestingZhuBi;
}

//----- (0000000100C0980C) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setRequestingZhuBi:](TradeZhuBiQuoteItem *self, SEL a2, char a3)
{
  self->_requestingZhuBi = a3;
}

//----- (0000000100C09815) ----------------------------------------------------
NSMutableArray *__cdecl -[TradeZhuBiQuoteItem tempPushArray](TradeZhuBiQuoteItem *self, SEL a2)
{
  return self->_tempPushArray;
}

//----- (0000000100C09822) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem setTempPushArray:](TradeZhuBiQuoteItem *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_tempPushArray, a3);
}

//----- (0000000100C09836) ----------------------------------------------------
void __cdecl -[TradeZhuBiQuoteItem .cxx_destruct](TradeZhuBiQuoteItem *self, SEL a2)
{
  objc_storeStrong((id *)&self->_tempPushArray, 0LL);
  objc_storeStrong((id *)&self->_countDowntimer, 0LL);
  objc_storeStrong((id *)&self->_needSelectDict, 0LL);
  objc_storeStrong((id *)&self->_market, 0LL);
  objc_storeStrong((id *)&self->_stockCode, 0LL);
  objc_storeStrong((id *)&self->_idModelDict, 0LL);
  objc_destroyWeak((id *)&self->_tradeZhuBiArrayController);
  objc_storeStrong(&self->_switchNumberOrMoneyStateBlock, 0LL);
  objc_storeStrong(&self->_viewDispalyedRowsBlock, 0LL);
  objc_storeStrong(&self->_viewDispalyedBlock, 0LL);
  objc_storeStrong(&self->_didFinishFirstRequestBlock, 0LL);
  objc_storeStrong(&self->_didLoadMoreData, 0LL);
  objc_storeStrong(&self->_reloadBlock, 0LL);
  objc_storeStrong(&self->_scrollToBottomBlock, 0LL);
  objc_storeStrong(&self->_selectionIndexesBlock, 0LL);
  objc_storeStrong((id *)&self->normalTextColor, 0LL);
  objc_storeStrong((id *)&self->orangeLineColor, 0LL);
  objc_storeStrong((id *)&self->riseTextColor, 0LL);
  objc_storeStrong((id *)&self->fallTextColor, 0LL);
  objc_storeStrong((id *)&self->dataFormatter, 0LL);
}

