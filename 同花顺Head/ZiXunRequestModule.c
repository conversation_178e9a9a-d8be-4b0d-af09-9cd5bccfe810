ZiXunRequestModule *__cdecl -[ZiXunRequestModule init](ZiXunRequestModule *self, SEL a2)
{

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZiXunRequestModule;
  v2 = objc_msgSendSuper2(&v7, "init");
  if ( v2 )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = (void *)*((_QWORD *)v2 + 5);
    *((_QWORD *)v2 + 5) = v4;
    v2[9] = 0;
    *(_OWORD *)(v2 + 56) = 0LL;
    *((_QWORD *)v2 + 9) = 0LL;
  }
  return (ZiXunRequestModule *)v2;
}

//----- (00000001005AB92A) ----------------------------------------------------
<PERSON><PERSON>ateFormatter *__cdecl -[ZiXunRequestModule dateFormatter](ZiXunRequestModule *self, SEL a2)
{
  NSDateFormatter *dateFormatter; // rdi
  NSDateFormatter *v5; // rax
  NSDateFormatter *v6; // rdi
  NSTimeZone *v7; // rax
  NSTimeZone *v8; // r14
  NSLocale *v10; // rax
  NSLocale *v11; // r14

  dateFormatter = self->_dateFormatter;
  if ( !dateFormatter )
  {
    v4 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
    v5 = (NSDateFormatter *)_objc_msgSend(v4, "init");
    v6 = self->_dateFormatter;
    self->_dateFormatter = v5;
    v7 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, "timeZoneWithName:", CFSTR("Asia/Shanghai"));
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(self->_dateFormatter, "setTimeZone:", v8);
    v9(v8);
    v10 = _objc_msgSend(&OBJC_CLASS___NSLocale, "localeWithLocaleIdentifier:", CFSTR("zh_CN"));
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(self->_dateFormatter, "setLocale:", v11);
    v12(v11);
    _objc_msgSend(self->_dateFormatter, "setDateStyle:", 2LL);
    _objc_msgSend(self->_dateFormatter, "setTimeStyle:", 1LL);
    _objc_msgSend(self->_dateFormatter, "setDateFormat:", CFSTR("yyyy-MM-dd HH:mm:ss"));
    dateFormatter = self->_dateFormatter;
  }
  return (NSDateFormatter *)objc_retainAutoreleaseReturnValue(dateFormatter);
}

//----- (00000001005ABA37) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestForZiXun:](ZiXunRequestModule *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  SEL v21; // [rsp+40h] [rbp-E0h]
  SEL v23; // [rsp+50h] [rbp-D0h]
  id obj; // [rsp+68h] [rbp-B8h]

  v24 = self;
  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    _objc_msgSend(v24, "resetParamsBeforeRequest");
    v6 = v5(v4, "count");
    v7(v24, "setRequestSum:", v6);
    v20 = 0LL;
    v19 = 0LL;
    v18 = 0LL;
    v17 = 0LL;
    v25 = v4;
    obj = objc_retain(v4);
    v9 = v8(obj, "countByEnumeratingWithState:objects:count:", &v17, v27, 16LL);
    if ( v9 )
    {
      v10 = (__int64)v9;
      v22 = *(_QWORD *)v18;
LABEL_5:
      v21 = "class";
      v23 = "requestZiXun:";
      if ( !v10 )
        v10 = 1LL;
      v11 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v18 != v22 )
          objc_enumerationMutation(obj);
        v12 = *(void **)(*((_QWORD *)&v17 + 1) + 8 * v11);
        v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v21);
        if ( !(unsigned __int8)v14(v12, "isKindOfClass:", v13) )
          break;
        v15 = objc_retain(v12);
        v16 = v15;
        if ( v15 && _objc_msgSend(v15, "count") )
          _objc_msgSend(v24, v23, v16);
        if ( v10 == ++v11 )
        {
          v10 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v27, 16LL);
          if ( v10 )
            goto LABEL_5;
          break;
        }
      }
    }
    v4 = v25;
  }
}

//----- (00000001005ABC8E) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestZiXun:](ZiXunRequestModule *self, SEL a2, id a3)
{
  SEL v7; // r12
  bool v10; // zf

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("ZiXunType"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v8 = _objc_msgSend(v4, v7, CFSTR("RequestType"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "longValue") == 0LL;
    v11 = &selRef_requestZiXunBySocket_;
    if ( v10 )
      v11 = &selRef_requestZiXunByHttp_;
    _objc_msgSend(self, *v11, v6);
  }
}

//----- (00000001005ABD69) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestZiXunByHttp:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSURL *v9; // rax
  NSURL *v10; // rbx
  NSURLRequest *v11; // rax
  NSURLRequest *v12; // r15
  _QWORD v17[4]; // [rsp+8h] [rbp-78h] BYREF
  id to[2]; // [rsp+30h] [rbp-50h] BYREF
  id location[6]; // [rsp+50h] [rbp-30h] BYREF

  to[1] = self;
  objc_retain(a3);
  v3 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "longValue");
  v7 = _objc_msgSend(v4, "getHttpRequestURL:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v20 = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v8);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = objc_alloc((Class)&OBJC_CLASS___AFHTTPRequestOperation);
  v21 = v12;
  v14 = _objc_msgSend(v13, "initWithRequest:", v12);
  objc_initWeak(location, self);
  v17[0] = _NSConcreteStackBlock;
  v17[1] = 3254779904LL;
  v17[2] = sub_1005ABF34;
  v17[3] = &unk_1012DC830;
  objc_copyWeak(to, location);
  v18 = objc_retain(v15);
  _objc_msgSend(v14, "setCompletionBlockWithSuccess:failure:", v17, &stru_1012E33E0);
  _objc_msgSend(v14, "start");
  objc_destroyWeak(to);
  objc_destroyWeak(location);
}

//----- (00000001005ABF34) ----------------------------------------------------
void __fastcall sub_1005ABF34(__int64 a1, __int64 a2, void *a3)
{
  id WeakRetained; // rbx
  id (*v9)(id, SEL, ...); // r12

  v3 = *(void **)(a1 + 32);
  v4 = objc_retain(a3);
  v5 = _objc_msgSend(v3, "integerValue");
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v7(WeakRetained, "setCurrentZiXunType:", v5);
  v8 = objc_loadWeakRetained((id *)(a1 + 40));
  v10 = (char *)v9(v8, "backReqSum");
  v11(v8, "setBackReqSum:", v10 + 1);
  v12 = objc_loadWeakRetained((id *)(a1 + 40));
  v13(v12, "parseHttpZiXunItemData:", v4);
}

//----- (00000001005AC001) ----------------------------------------------------
void __cdecl sub_1005AC001(id a1, AFHTTPRequestOperation *a2, NSError *a3)
{
  ;
}

//----- (00000001005AC007) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestZiXunBySocket:](ZiXunRequestModule *self, SEL a2, id a3)
{
  bool v4; // zf

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "longValue") == (id)949;
  v6 = &selRef_requestNormalZiXunBySocket_;
  if ( v4 )
    v6 = &selRef_requestJiJinHangYe_;
  v5(self, *v6, v3);
}

//----- (00000001005AC06D) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestNormalZiXunBySocket:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSString *v5; // rax
  NSString *v6; // r14
  NSString *v7; // rax
  HXSocketCenter *v8; // r15
  SEL v9; // r12
  _BOOL8 v13; // r15
  NSString *v14; // rax
  NSString *v15; // rax
  NSString *v16; // r13
  NSString *v18; // rax
  HXSocketCenter *v20; // rax

  v4 = objc_retain(a3);
  v5 = -[ZiXunRequestModule stockCode](self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( _objc_msgSend(v6, "length") )
  {
    v22 = v4;
    v7 = -[ZiXunRequestModule market](self, "market");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( _objc_msgSend(v8, v9) )
    {
      v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      if ( (unsigned __int8)_objc_msgSend(v22, "isKindOfClass:", v10) )
      {
        _objc_msgSend(v22, "doubleValue");
        if ( v3 != 4294967295.0 )
        {
          _objc_msgSend(v22, v11);
          v12(v6);
          v4 = v22;
          if ( v3 == 2147483648.0 )
            goto LABEL_7;
          v23 = _objc_msgSend(v22, "longValue");
          v21 = _objc_msgSend(v22, "longValue");
          v13 = _objc_msgSend(v22, "longLongValue") == (id)14371;
          v14 = -[ZiXunRequestModule stockCode](self, "stockCode");
          objc_retainAutoreleasedReturnValue(v14);
          v15 = (NSString *)-[ZiXunRequestModule market](self, "market");
          v16 = objc_retainAutoreleasedReturnValue(v15);
          v18 = (NSString *)_objc_msgSend(
                              &OBJC_CLASS___NSString,
                              "stringWithFormat:",
                              CFSTR("id=%ld&instance=%ld&textid=%ld&LastTextTime=0&summary=%ld&code=%@&market=%@&advertorial=1"),
                              1001LL,
                              v23,
                              v21,
                              v13,
                              v17,
                              v16);
          v6 = objc_retainAutoreleasedReturnValue(v18);
          v20 = (HXSocketCenter *)+[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
          v8 = objc_retainAutoreleasedReturnValue(v20);
          -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
            v8,
            "writeTextData:withTimeout:delegate:instance:",
            v6,
            self,
            v23,
            10.0);
        }
      }
    }
    v4 = v22;
  }
LABEL_7:
}

//----- (00000001005AC2B5) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule requestJiJinHangYe:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSString *v4; // rax
  HXSocketCenter *v5; // rax
  HXSocketCenter *v6; // rbx

  v3 = _objc_msgSend(a3, "longValue");
  v4 = _objc_msgSend(
         &OBJC_CLASS___NSString,
         "stringWithFormat:",
         CFSTR("id=1007&instance=%ld&URL=/rss/494/index.xml"),
         v3);
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXSocketCenter writeTextData:withTimeout:delegate:instance:](
    v6,
    "writeTextData:withTimeout:delegate:instance:",
    v7,
    self,
    v3,
    10.0);
}

//----- (00000001005AC362) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule receiveStuffData:](ZiXunRequestModule *self, SEL a2, id a3)
{
  signed int v6; // eax
  SEL v8; // r12
  signed int v11; // eax

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXPCBaseDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = objc_retain(v3);
    v6 = (unsigned int)_objc_msgSend(v5, "instanceId");
    -[ZiXunRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
      self,
      "unregisterInMacDataServiceWithRequstInstanceId:",
      v6);
    v7 = _objc_msgSend(&OBJC_CLASS___PCTXTDataModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, v8, v7) )
    {
      objc_retain(v5);
      v9 = -[ZiXunRequestModule backReqSum](self, "backReqSum");
      -[ZiXunRequestModule setBackReqSum:](self, "setBackReqSum:", v9 + 1);
      v11 = (unsigned int)_objc_msgSend(v10, "instanceId");
      -[ZiXunRequestModule setCurrentZiXunType:](self, "setCurrentZiXunType:", v11);
      -[ZiXunRequestModule parserZiXunItemData:](self, "parserZiXunItemData:", v12);
    }
  }
}

//----- (00000001005AC47C) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule failToReceiveStuffData:](ZiXunRequestModule *self, SEL a2, signed __int64 a3)
{
  ZiXunDispatchDelegate *v9; // rax
  ZiXunDispatchDelegate *v10; // rbx
  ZiXunDispatchDelegate *v13; // rax
  ZiXunDispatchDelegate *v14; // rbx

  -[ZiXunRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
    self,
    "unregisterInMacDataServiceWithRequstInstanceId:");
  -[ZiXunRequestModule setCurrentZiXunType:](self, "setCurrentZiXunType:", a3);
  v4 = -[ZiXunRequestModule failReqSum](self, "failReqSum");
  -[ZiXunRequestModule setFailReqSum:](self, "setFailReqSum:", v4 + 1);
  v5 = -[ZiXunRequestModule failReqSum](self, "failReqSum");
  v6 = -[ZiXunRequestModule requestSum](self, "requestSum");
  -[ZiXunRequestModule setAllZiXunReqFail:](self, "setAllZiXunReqFail:", v5 == v6);
  if ( (unsigned __int8)-[ZiXunRequestModule allZiXunReqFail](self, "allZiXunReqFail") == 1 )
  {
    v7 = -[ZiXunRequestModule currentZiXunType](self, "currentZiXunType");
    v8 = -[ZiXunRequestModule getTableFlagValue:](self, "getTableFlagValue:", v7);
    objc_retainAutoreleasedReturnValue(v8);
    v9 = -[ZiXunRequestModule delegate](self, "delegate");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = (unsigned __int8)_objc_msgSend(v10, "respondsToSelector:", "failToReceiveZiXunData:");
    if ( v11 )
    {
      v13 = -[ZiXunRequestModule delegate](self, "delegate");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v16 = _objc_msgSend(v15, "integerValue");
      _objc_msgSend(v14, "failToReceiveZiXunData:", v16);
    }
  }
  else
  {
    -[ZiXunRequestModule backReqSum](self, "backReqSum");
    v17 = -[ZiXunRequestModule failReqSum](self, "failReqSum");
    if ( &v17[v18] == -[ZiXunRequestModule requestSum](self, "requestSum") )
      -[ZiXunRequestModule setNeedWaiting:](self, "setNeedWaiting:", 0LL);
    -[ZiXunRequestModule postZiXunDataToZiXunTable](self, "postZiXunDataToZiXunTable");
  }
}

//----- (00000001005AC630) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
        ZiXunRequestModule *self,
        SEL a2,
        signed __int64 a3)
{
  HXSocketCenter *v4; // rax
  HXSocketCenter *v5; // rbx

  v4 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(v5, "removeObjectFromMapTable:forInstance:", self, a3);
}

//----- (00000001005AC688) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule parserZiXunItemData:](ZiXunRequestModule *self, SEL a2, id a3)
{
  int v8; // r12d

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    _objc_msgSend(v3, "instanceId");
    v5 = _objc_msgSend(v4, "txtData");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = v6;
    if ( v8 == 949 )
      -[ZiXunRequestModule parseJiJinHangYeXmlWithRegularExpression:](
        self,
        "parseJiJinHangYeXmlWithRegularExpression:",
        v6);
    else
      -[ZiXunRequestModule parseXmlWithRegularExpression:](self, "parseXmlWithRegularExpression:", v6);
    -[ZiXunRequestModule backReqSum](self, "backReqSum");
    v9 = -[ZiXunRequestModule failReqSum](self, "failReqSum");
    if ( &v9[v10] == -[ZiXunRequestModule requestSum](self, "requestSum")
      && !(unsigned __int8)-[ZiXunRequestModule allZiXunReqFail](self, "allZiXunReqFail") )
    {
      -[ZiXunRequestModule setNeedWaiting:](self, "setNeedWaiting:", 0LL);
    }
    -[ZiXunRequestModule postZiXunDataToZiXunTable](self, "postZiXunDataToZiXunTable");
  }
}

//----- (00000001005AC78D) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule parseXmlWithRegularExpression:](ZiXunRequestModule *self, SEL a2, id a3)
{
  unsigned __int64 v4; // r14
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  unsigned __int64 i; // r15
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  SEL v42; // [rsp+58h] [rbp-118h]
  SEL v43; // [rsp+60h] [rbp-110h]
  SEL v44; // [rsp+68h] [rbp-108h]
  id obj; // [rsp+B8h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v53 = self;
    v4 = CFStringConvertEncodingToNSStringEncoding(0x632u);
    v5 = objc_alloc(&OBJC_CLASS___NSString);
    v6 = _objc_msgSend(v5, "initWithData:encoding:", v3, v4);
    v8 = v7(v6, "stringByReplacingOccurrencesOfString:withString:", CFSTR("\x01"), &charsToLeaveEscaped);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( v10(v9, "length") )
    {
      v49 = v3;
      v40 = 0LL;
      v12 = v11(
              &OBJC_CLASS___NSRegularExpression,
              "regularExpressionWithPattern:options:error:",
              CFSTR("<data>.*?</data>"),
              9LL,
              &v40);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v51 = objc_retain(v40);
      v15 = v14(v9, "length");
      v50 = v13;
      v17 = v16(v13, "matchesInString:options:range:", v9, 1LL, 0LL, v15);
      v36 = 0LL;
      v37 = 0LL;
      v38 = 0LL;
      v39 = 0LL;
      obj = objc_retainAutoreleasedReturnValue(v17);
      v52 = v18(obj, "countByEnumeratingWithState:objects:count:", &v36, v55, 16LL);
      v20 = v53;
      if ( v52 )
      {
        v46 = *(_QWORD *)v37;
        v45 = v9;
        do
        {
          v42 = "range";
          v43 = "substringWithRange:";
          v44 = "getNewsItemWithDataStr:";
          v47 = "ziXunItems";
          v48 = "addObject:";
          for ( i = 0LL; i < (unsigned __int64)v52; ++i )
          {
            if ( *(_QWORD *)v37 != v46 )
              objc_enumerationMutation(obj);
            v22 = v19(*(id *)(*((_QWORD *)&v36 + 1) + 8 * i), v42);
            v25 = v24(v9, v43, v22, v23);
            v41 = objc_retainAutoreleasedReturnValue(v25);
            v27 = v26(v20, v44, v41);
            v28 = objc_retainAutoreleasedReturnValue(v27);
            v30 = v29;
            v31 = v28;
            if ( v28 )
            {
              v32 = (void *)v30(v20, v47);
              v33 = objc_retainAutoreleasedReturnValue(v32);
              ((void (__fastcall *)(id, const char *, __int64))v30)(v33, v48, v34);
              v35 = v33;
              v20 = v53;
            }
            v19 = (id (*)(id, SEL, ...))v30;
            v9 = v45;
          }
          v52 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v30)(
                      obj,
                      "countByEnumeratingWithState:objects:count:",
                      &v36,
                      v55,
                      16LL);
        }
        while ( v52 );
      }
      v3 = v49;
    }
  }
}

//----- (00000001005ACAFF) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule parseJiJinHangYeXmlWithRegularExpression:](ZiXunRequestModule *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  unsigned __int64 i; // r15
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  SEL v42; // [rsp+58h] [rbp-118h]
  SEL v43; // [rsp+60h] [rbp-110h]
  SEL v44; // [rsp+68h] [rbp-108h]
  id obj; // [rsp+B8h] [rbp-B8h]

  v3 = -[ZiXunRequestModule transCodingXMLData:](self, "transCodingXMLData:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( v4 )
  {
    v53 = self;
    v5 = objc_alloc(&OBJC_CLASS___NSString);
    v6 = _objc_msgSend(v5, "initWithData:encoding:", v4, 4LL);
    v8 = v7(v6, "stringByReplacingOccurrencesOfString:withString:", CFSTR("\x01"), &charsToLeaveEscaped);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( v10(v9, "length") )
    {
      v49 = v4;
      v40 = 0LL;
      v12 = v11(
              &OBJC_CLASS___NSRegularExpression,
              "regularExpressionWithPattern:options:error:",
              CFSTR("<item>.*?</item>"),
              9LL,
              &v40);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v51 = objc_retain(v40);
      v15 = v14(v9, "length");
      v50 = v13;
      v17 = v16(v13, "matchesInString:options:range:", v9, 1LL, 0LL, v15);
      v36 = 0LL;
      v37 = 0LL;
      v38 = 0LL;
      v39 = 0LL;
      obj = objc_retainAutoreleasedReturnValue(v17);
      v52 = v18(obj, "countByEnumeratingWithState:objects:count:", &v36, v55, 16LL);
      v20 = v53;
      if ( v52 )
      {
        v46 = *(_QWORD *)v37;
        v45 = v9;
        do
        {
          v42 = "range";
          v43 = "substringWithRange:";
          v44 = "getJiJinHangYeNewsItemWithDataStr:";
          v47 = "ziXunItems";
          v48 = "addObject:";
          for ( i = 0LL; i < (unsigned __int64)v52; ++i )
          {
            if ( *(_QWORD *)v37 != v46 )
              objc_enumerationMutation(obj);
            v22 = v19(*(id *)(*((_QWORD *)&v36 + 1) + 8 * i), v42);
            v25 = v24(v9, v43, v22, v23);
            v41 = objc_retainAutoreleasedReturnValue(v25);
            v27 = v26(v20, v44, v41);
            v28 = objc_retainAutoreleasedReturnValue(v27);
            v30 = v29;
            v31 = v28;
            if ( v28 )
            {
              v32 = (void *)v30(v20, v47);
              v33 = objc_retainAutoreleasedReturnValue(v32);
              ((void (__fastcall *)(id, const char *, __int64))v30)(v33, v48, v34);
              v35 = v33;
              v20 = v53;
            }
            v19 = (id (*)(id, SEL, ...))v30;
            v9 = v45;
          }
          v52 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v30)(
                      obj,
                      "countByEnumeratingWithState:objects:count:",
                      &v36,
                      v55,
                      16LL);
        }
        while ( v52 );
      }
      v4 = v49;
    }
  }
}

//----- (00000001005ACE72) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule parseHttpZiXunItemData:](ZiXunRequestModule *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSData, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = objc_alloc(&OBJC_CLASS___NSData);
    v6 = _objc_msgSend(v5, "initWithData:", v3);
    -[ZiXunRequestModule parseHttpXmlWithRegularExpression:](self, "parseHttpXmlWithRegularExpression:", v6);
    -[ZiXunRequestModule backReqSum](self, "backReqSum");
    v7 = -[ZiXunRequestModule failReqSum](self, "failReqSum");
    if ( &v7[v8] == -[ZiXunRequestModule requestSum](self, "requestSum")
      && !(unsigned __int8)-[ZiXunRequestModule allZiXunReqFail](self, "allZiXunReqFail") )
    {
      -[ZiXunRequestModule setNeedWaiting:](self, "setNeedWaiting:", 0LL);
    }
    -[ZiXunRequestModule postZiXunDataToZiXunTable](self, "postZiXunDataToZiXunTable");
  }
}

//----- (00000001005ACF79) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule parseHttpXmlWithRegularExpression:](ZiXunRequestModule *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  unsigned __int64 i; // r15
  id (*v24)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  SEL v42; // [rsp+58h] [rbp-118h]
  SEL v43; // [rsp+60h] [rbp-110h]
  SEL v44; // [rsp+68h] [rbp-108h]
  id obj; // [rsp+B8h] [rbp-B8h]

  v3 = -[ZiXunRequestModule transCodingXMLData:](self, "transCodingXMLData:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( v4 )
  {
    v53 = self;
    v5 = objc_alloc(&OBJC_CLASS___NSString);
    v6 = _objc_msgSend(v5, "initWithData:encoding:", v4, 4LL);
    v8 = v7(v6, "stringByReplacingOccurrencesOfString:withString:", CFSTR("\x01"), &charsToLeaveEscaped);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( v10(v9, "length") )
    {
      v49 = v4;
      v40 = 0LL;
      v12 = v11(
              &OBJC_CLASS___NSRegularExpression,
              "regularExpressionWithPattern:options:error:",
              CFSTR("<item>.*?</item>"),
              9LL,
              &v40);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v51 = objc_retain(v40);
      v15 = v14(v9, "length");
      v50 = v13;
      v17 = v16(v13, "matchesInString:options:range:", v9, 1LL, 0LL, v15);
      v36 = 0LL;
      v37 = 0LL;
      v38 = 0LL;
      v39 = 0LL;
      obj = objc_retainAutoreleasedReturnValue(v17);
      v52 = v18(obj, "countByEnumeratingWithState:objects:count:", &v36, v55, 16LL);
      v20 = v53;
      if ( v52 )
      {
        v46 = *(_QWORD *)v37;
        v45 = v9;
        do
        {
          v42 = "range";
          v43 = "substringWithRange:";
          v44 = "getHttpNewsItemWithDataStr:";
          v47 = "ziXunItems";
          v48 = "addObject:";
          for ( i = 0LL; i < (unsigned __int64)v52; ++i )
          {
            if ( *(_QWORD *)v37 != v46 )
              objc_enumerationMutation(obj);
            v22 = v19(*(id *)(*((_QWORD *)&v36 + 1) + 8 * i), v42);
            v25 = v24(v9, v43, v22, v23);
            v41 = objc_retainAutoreleasedReturnValue(v25);
            v27 = v26(v20, v44, v41);
            v28 = objc_retainAutoreleasedReturnValue(v27);
            v30 = v29;
            v31 = v28;
            if ( v28 )
            {
              v32 = (void *)v30(v20, v47);
              v33 = objc_retainAutoreleasedReturnValue(v32);
              ((void (__fastcall *)(id, const char *, __int64))v30)(v33, v48, v34);
              v35 = v33;
              v20 = v53;
            }
            v19 = (id (*)(id, SEL, ...))v30;
            v9 = v45;
          }
          v52 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v30)(
                      obj,
                      "countByEnumeratingWithState:objects:count:",
                      &v36,
                      v55,
                      16LL);
        }
        while ( v52 );
      }
      v4 = v49;
    }
  }
}

//----- (00000001005AD2EC) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsItemWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  ZiXunRequestModule *v28; // rdi

  v4 = objc_retain(a3);
  if ( _objc_msgSend(v4, "length") )
  {
    v6 = -[ZiXunRequestModule getNewsTimeWithDataStr:](self, "getNewsTimeWithDataStr:", v5);
    v34 = objc_retainAutoreleasedReturnValue(v6);
    v8 = -[ZiXunRequestModule getNewsTitleWithDataStr:](self, "getNewsTitleWithDataStr:", v7);
    v36 = objc_retainAutoreleasedReturnValue(v8);
    v10 = -[ZiXunRequestModule getNewsIdWithDataStr:](self, "getNewsIdWithDataStr:", v9);
    v35 = objc_retainAutoreleasedReturnValue(v10);
    v12 = -[ZiXunRequestModule getNewsStockCodeWithDataStr:](self, "getNewsStockCodeWithDataStr:", v11);
    v32 = objc_retainAutoreleasedReturnValue(v12);
    v14 = -[ZiXunRequestModule getNewsPropertiesWithDataStr:](self, "getNewsPropertiesWithDataStr:", v13);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = -[ZiXunRequestModule getNewsOperationWithDataStr:](self, "getNewsOperationWithDataStr:", v16);
    v31 = objc_retainAutoreleasedReturnValue(v17);
    v33 = v15;
    v18 = -[ZiXunRequestModule getCreateTimeWithPropArr:](self, "getCreateTimeWithPropArr:", v15);
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = v19;
    if ( !_objc_msgSend(v19, "length") )
      v20 = v34;
    v21 = objc_retain(v20);
    v30 = v19;
    if ( _objc_msgSend(v21, "length") )
    {
      if ( _objc_msgSend(v36, "length") )
      {
        v23 = v35;
        if ( _objc_msgSend(v35, "length")
          && _objc_msgSend(v32, "length")
          && (!v33 || !(unsigned __int8)_objc_msgSend(v33, "containsObject:", CFSTR("da=1"))) )
        {
          if ( _objc_msgSend(v31, "integerValue") == (id)1 )
          {
            v24 = 0LL;
            v25 = v36;
            v23 = v35;
          }
          else
          {
            v27 = objc_alloc((Class)&OBJC_CLASS___NewsItem);
            v24 = _objc_msgSend(v27, "init");
            _objc_msgSend(v24, "setTime:", v21);
            _objc_msgSend(v24, "setTitle:", v36);
            v23 = v35;
            _objc_msgSend(v24, "setNewsItemId:", v35);
            _objc_msgSend(v24, "setStockCode:", v32);
            _objc_msgSend(v24, "setIsAd:", 0LL);
            _objc_msgSend(v24, "setNewsRequestType:", 1LL);
            v28 = self;
            v25 = v36;
            v29 = -[ZiXunRequestModule currentZiXunType](v28, "currentZiXunType");
            _objc_msgSend(v24, "setZiXunType:", v29);
            _objc_msgSend(v24, "setPropertiesArr:", v33);
          }
        }
        else
        {
          v24 = 0LL;
          v25 = v36;
        }
      }
      else
      {
        v24 = 0LL;
        v23 = v35;
        v25 = v36;
      }
    }
    else
    {
      v24 = 0LL;
      v25 = v36;
      v23 = v35;
    }
  }
  else
  {
    v24 = 0LL;
  }
  return objc_autoreleaseReturnValue(v24);
}

//----- (00000001005AD5F5) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getJiJinHangYeNewsItemWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  NSDateFormatter *v15; // rax
  NSDateFormatter *v16; // r15
  NSString *v22; // rax
  NSString *v23; // rbx

  v4 = objc_retain(a3);
  if ( _objc_msgSend(v4, "length") )
  {
    v5 = -[ZiXunRequestModule getNewsTitleWithDataStr:](self, "getNewsTitleWithDataStr:", v4);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v8 = v7(self, "getNewsLinkWithDataStr:", v4);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = v10(self, "getNewsPubDateWithDataStr:", v4);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v26 = v6;
    if ( v13(v6, "length") )
    {
      if ( _objc_msgSend(v9, "length") )
      {
        if ( _objc_msgSend(v12, "length") )
        {
          v15 = -[ZiXunRequestModule dateFormatter](self, "dateFormatter");
          v16 = objc_retainAutoreleasedReturnValue(v15);
          v18 = _objc_msgSend(v16, "dateFromString:", v17);
          v19 = objc_retainAutoreleasedReturnValue(v18);
          if ( v19 )
          {
            v27 = v19;
            v20 = objc_alloc((Class)&OBJC_CLASS___NewsItem);
            v21 = _objc_msgSend(v20, "init");
            _objc_msgSend(v21, "setTitle:", v26);
            _objc_msgSend(v21, "setNewsURL:", v9);
            _objc_msgSend(v19, "timeIntervalSince1970");
            v22 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), (unsigned int)(int)v3);
            v23 = objc_retainAutoreleasedReturnValue(v22);
            _objc_msgSend(v21, "setTime:", v23);
            _objc_msgSend(v21, "setNewsRequestType:", 0LL);
            v24 = -[ZiXunRequestModule currentZiXunType](self, "currentZiXunType");
            _objc_msgSend(v21, "setZiXunType:", v24);
            v19 = v27;
          }
          else
          {
            v21 = 0LL;
          }
        }
        else
        {
          v21 = 0LL;
        }
        goto LABEL_14;
      }
      v21 = 0LL;
    }
    else
    {
      v21 = 0LL;
    }
    v14 = v12;
LABEL_14:
    goto LABEL_15;
  }
  v21 = 0LL;
LABEL_15:
  return objc_autoreleaseReturnValue(v21);
}

//----- (00000001005AD854) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getHttpNewsItemWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  NSDateFormatter *v15; // rax
  NSDateFormatter *v16; // r15
  NSString *v22; // rax
  NSString *v23; // rbx

  v4 = objc_retain(a3);
  if ( _objc_msgSend(v4, "length") )
  {
    v5 = -[ZiXunRequestModule getNewsTitleWithDataStr:](self, "getNewsTitleWithDataStr:", v4);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v8 = v7(self, "getNewsClientLinkWithDataStr:", v4);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = v10(self, "getNewsPubDateWithDataStr:", v4);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v26 = v6;
    if ( v13(v6, "length") )
    {
      if ( _objc_msgSend(v9, "length") )
      {
        if ( _objc_msgSend(v12, "length") )
        {
          v15 = -[ZiXunRequestModule dateFormatter](self, "dateFormatter");
          v16 = objc_retainAutoreleasedReturnValue(v15);
          v18 = _objc_msgSend(v16, "dateFromString:", v17);
          v19 = objc_retainAutoreleasedReturnValue(v18);
          if ( v19 )
          {
            v27 = v19;
            v20 = objc_alloc((Class)&OBJC_CLASS___NewsItem);
            v21 = _objc_msgSend(v20, "init");
            _objc_msgSend(v21, "setTitle:", v26);
            _objc_msgSend(v21, "setNewsURL:", v9);
            _objc_msgSend(v19, "timeIntervalSince1970");
            v22 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), (unsigned int)(int)v3);
            v23 = objc_retainAutoreleasedReturnValue(v22);
            _objc_msgSend(v21, "setTime:", v23);
            _objc_msgSend(v21, "setNewsRequestType:", 0LL);
            v24 = -[ZiXunRequestModule currentZiXunType](self, "currentZiXunType");
            _objc_msgSend(v21, "setZiXunType:", v24);
            v19 = v27;
          }
          else
          {
            v21 = 0LL;
          }
        }
        else
        {
          v21 = 0LL;
        }
        goto LABEL_14;
      }
      v21 = 0LL;
    }
    else
    {
      v21 = 0LL;
    }
    v14 = v12;
LABEL_14:
    goto LABEL_15;
  }
  v21 = 0LL;
LABEL_15:
  return objc_autoreleaseReturnValue(v21);
}

//----- (00000001005ADAB3) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsTimeWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<time>[0-9]+</time>"),
           1LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = (__int64)v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v12 )
          v12 = 1LL;
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8 * v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<time>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</time>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_14;
          }
          v13 = v22 + 1;
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_14:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005ADDD2) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsTitleWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<title>.*</title>"),
           9LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8LL * (_QWORD)v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<title><![CDATA["), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("]]></title>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_12;
          }
          v13 = (id)(v22 + 1);
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_12:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AE0F7) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsIdWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<id>[0-9]+</id>"),
           1LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = (__int64)v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v12 )
          v12 = 1LL;
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8 * v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<id>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</id>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_14;
          }
          v13 = v22 + 1;
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_14:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AE416) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsStockCodeWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<stock>.*?</stock>"),
           1LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = (__int64)v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v12 )
          v12 = 1LL;
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8 * v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<stock>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</stock>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_14;
          }
          v13 = v22 + 1;
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_14:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AE735) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsPropertiesWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  NSRegularExpression *v5; // rbx
  id *v6; // r12

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v23 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<properties>.*?</properties>"),
           9LL,
           &v23);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v25 = objc_retain(*v6);
    v7 = _objc_msgSend(v3, "length");
    v8 = 0LL;
    v24 = v5;
    v9 = _objc_msgSend(v5, "matchesInString:options:range:", v3, 1LL, 0LL, v7);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( _objc_msgSend(v10, "count") )
    {
      v12 = _objc_msgSend(v11, "firstObject");
      v26 = objc_retainAutoreleasedReturnValue(v12);
      v13 = _objc_msgSend(v26, "range");
      v15 = _objc_msgSend(v3, "substringWithRange:", v13, v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v17 = _objc_msgSend(
              v16,
              "stringByReplacingOccurrencesOfString:withString:",
              CFSTR("<properties><![CDATA["),
              &charsToLeaveEscaped);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(
              v18,
              "stringByReplacingOccurrencesOfString:withString:",
              CFSTR("]]></properties>"),
              &charsToLeaveEscaped);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v20, "componentsSeparatedByString:", CFSTR("\n"));
      v8 = objc_retainAutoreleasedReturnValue(v21);
    }
  }
  else
  {
    v8 = 0LL;
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (00000001005AE917) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsOperationWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  NSNumber *v27; // rax
  SEL v39; // [rsp+48h] [rbp-F8h]
  SEL v40; // [rsp+50h] [rbp-F0h]
  SEL v46; // [rsp+80h] [rbp-C0h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v38 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<op>[0-9]+</op>"),
           1LL,
           &v38);
    objc_retainAutoreleasedReturnValue(v4);
    v5 = objc_retain(v38);
    v6 = _objc_msgSend(v3, "length");
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v6);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v34 = 0LL;
    v35 = 0LL;
    v36 = 0LL;
    v37 = 0LL;
    obj = objc_retain(v9);
    v10 = 0LL;
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v34, v48, 16LL);
    if ( v11 )
    {
      v13 = (__int64)v11;
      v45 = v5;
      v44 = v12;
      v41 = *(_QWORD *)v35;
      v43 = v3;
      while ( 2 )
      {
        v46 = "range";
        v39 = "substringWithRange:";
        v40 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v13 )
          v13 = 1LL;
        v14 = 0LL;
        v42 = v13;
        do
        {
          if ( *(_QWORD *)v35 != v41 )
            objc_enumerationMutation(obj);
          v15 = _objc_msgSend(*(id *)(*((_QWORD *)&v34 + 1) + 8 * v14), v46);
          v17 = _objc_msgSend(v3, v39, v15, v16);
          v18 = objc_retainAutoreleasedReturnValue(v17);
          v19 = v40;
          v20 = _objc_msgSend(v18, v40, CFSTR("<op>"), &charsToLeaveEscaped);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          v23 = _objc_msgSend(v21, v19, CFSTR("</op>"), v22);
          objc_retainAutoreleasedReturnValue(v23);
          if ( _objc_msgSend(v24, "length") )
          {
            v26 = _objc_msgSend(v25, "integerValue");
            v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v26);
            v10 = objc_retainAutoreleasedReturnValue(v27);
            v3 = v43;
            v5 = v45;
            goto LABEL_14;
          }
          ++v14;
          v3 = v43;
        }
        while ( v42 != v14 );
        v13 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v34, v48, 16LL);
        if ( v13 )
          continue;
        break;
      }
      v5 = v45;
      v10 = 0LL;
    }
LABEL_14:
    v46 = v10;
    v29 = v5;
    v30 = obj;
    v32 = (char *)v46;
  }
  else
  {
    v32 = 0LL;
  }
  return objc_autoreleaseReturnValue(v32);
}

//----- (00000001005AECA2) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsLinkWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<link>.*?</link>"),
           1LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = (__int64)v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v12 )
          v12 = 1LL;
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8 * v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<link>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</link>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_14;
          }
          v13 = v22 + 1;
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_14:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AEFC1) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsClientLinkWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<clientlink>.*?</clientlink>"),
           9LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8LL * (_QWORD)v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<clientlink>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</clientlink>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_12;
          }
          v13 = (id)(v22 + 1);
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_12:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AF2E6) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getNewsPubDateWithDataStr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  NSRegularExpression *v4; // rax
  SEL v30; // [rsp+48h] [rbp-F8h]
  SEL v31; // [rsp+50h] [rbp-F0h]
  SEL v32; // [rsp+58h] [rbp-E8h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v29 = 0LL;
    v4 = _objc_msgSend(
           &OBJC_CLASS___NSRegularExpression,
           "regularExpressionWithPattern:options:error:",
           CFSTR("<pubDate>.*?</pubDate>"),
           1LL,
           &v29);
    objc_retainAutoreleasedReturnValue(v4);
    v36 = objc_retain(v29);
    v5 = _objc_msgSend(v3, "length");
    v6 = 0LL;
    v35 = v7;
    v8 = _objc_msgSend(v7, "matchesInString:options:range:", v3, 1LL, 0LL, v5);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v25 = 0LL;
    v26 = 0LL;
    v27 = 0LL;
    v28 = 0LL;
    obj = objc_retain(v9);
    v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v10, v39, 16LL);
    if ( v11 )
    {
      v12 = (__int64)v11;
      v33 = *(_QWORD *)v26;
      v37 = v3;
      while ( 2 )
      {
        v30 = "range";
        v31 = "substringWithRange:";
        v32 = "stringByReplacingOccurrencesOfString:withString:";
        if ( !v12 )
          v12 = 1LL;
        v13 = 0LL;
        v34 = v12;
        do
        {
          if ( *(_QWORD *)v26 != v33 )
            objc_enumerationMutation(obj);
          v14 = _objc_msgSend(*(id *)(*((_QWORD *)&v25 + 1) + 8 * v13), v30);
          v16 = _objc_msgSend(v3, v31, v14, v15);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v18 = v32;
          v19 = _objc_msgSend(v17, v32, CFSTR("<pubDate>"), &charsToLeaveEscaped);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v21 = _objc_msgSend(v20, v18, CFSTR("</pubDate>"), &charsToLeaveEscaped);
          v6 = objc_retainAutoreleasedReturnValue(v21);
          if ( _objc_msgSend(v6, "length") )
          {
            v3 = v37;
            goto LABEL_14;
          }
          v13 = v22 + 1;
          v3 = v37;
        }
        while ( v34 != v13 );
        v12 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v39, 16LL);
        if ( v12 )
          continue;
        break;
      }
      v6 = 0LL;
    }
LABEL_14:
    v23 = obj;
  }
  else
  {
    v6 = 0LL;
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (00000001005AF605) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getCreateTimeWithPropArr:](ZiXunRequestModule *self, SEL a2, id a3)
{
  __CFString *v5; // r14
  SEL v19; // [rsp+40h] [rbp-D0h]
  SEL v20; // [rsp+48h] [rbp-C8h]
  id obj; // [rsp+58h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  v5 = &charsToLeaveEscaped;
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v18 = 0LL;
    v17 = 0LL;
    v16 = 0LL;
    v15 = 0LL;
    v21 = v3;
    obj = objc_retain(v3);
    v6 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v23, 16LL);
    if ( v6 )
    {
      v7 = (__int64)v6;
      v8 = *(_QWORD *)v16;
      while ( 2 )
      {
        v20 = "length";
        v19 = "hasPrefix:";
        if ( !v7 )
          v7 = 1LL;
        for ( i = 0LL; i != v7; ++i )
        {
          if ( *(_QWORD *)v16 != v8 )
            objc_enumerationMutation(obj);
          v10 = *(void **)(*((_QWORD *)&v15 + 1) + 8 * i);
          v11 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
          if ( (unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v11)
            && _objc_msgSend(v10, v20)
            && (unsigned __int8)_objc_msgSend(v10, v19, CFSTR("ctime=")) )
          {
            v12 = _objc_msgSend(CFSTR("ctime="), v20);
            v13 = _objc_msgSend(v10, "substringFromIndex:", v12);
            v5 = (__CFString *)objc_retainAutoreleasedReturnValue(v13);
            v3 = v21;
            goto LABEL_17;
          }
        }
        v7 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v15, v23, 16LL);
        if ( v7 )
          continue;
        break;
      }
    }
    v3 = v21;
    v5 = &charsToLeaveEscaped;
  }
LABEL_17:
  return objc_autoreleaseReturnValue(v5);
}

//----- (00000001005AF852) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule transCodingXMLData:](ZiXunRequestModule *self, SEL a2, id a3)
{
  unsigned __int64 v6; // rbx
  NSString *v21; // rax
  NSString *v29; // [rsp+8h] [rbp-38h]

  if ( !a3 )
    return objc_autoreleaseReturnValue(0LL);
  v3 = objc_retain(a3);
  v4 = objc_alloc(&OBJC_CLASS___NSData);
  v5 = _objc_msgSend(v4, "initWithData:", v3);
  v6 = CFStringConvertEncodingToNSStringEncoding(0x631u);
  v7 = objc_alloc(&OBJC_CLASS___NSString);
  v8 = _objc_msgSend(v7, "initWithData:encoding:", v5, v6);
  if ( _objc_msgSend(v8, "rangeOfString:", CFSTR("encoding=\"UTF-8\"")) == (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v11 = (char *)_objc_msgSend(v9, "rangeOfString:", CFSTR("encoding=\""));
    if ( v11 != (char *)0x7FFFFFFFFFFFFFFFLL )
    {
      v13 = &v11[v10];
      v14 = _objc_msgSend(v12, "substringFromIndex:", v13);
      v30 = objc_retainAutoreleasedReturnValue(v14);
      v15 = _objc_msgSend(v30, "rangeOfString:", CFSTR("\""));
      if ( v15 != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v19 = _objc_msgSend(v16, "substringWithRange:", v13, v15);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v21 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@\""), CFSTR("encoding=\""), v20);
        v29 = objc_retainAutoreleasedReturnValue(v21);
        v23 = _objc_msgSend(v22, "stringByReplacingOccurrencesOfString:withString:", v29, CFSTR("encoding=\"UTF-8\""));
        v24 = objc_retainAutoreleasedReturnValue(v23);
        v26 = _objc_msgSend(v24, "dataUsingEncoding:", 4LL);
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v17 = _objc_msgSend(v27, "copy");
        v12 = v24;
        goto LABEL_10;
      }
    }
    v17 = 0LL;
  }
  else
  {
    v17 = _objc_msgSend(v5, "copy");
  }
LABEL_10:
  return objc_autoreleaseReturnValue(v17);
}

//----- (00000001005AFA8D) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule postZiXunDataToZiXunTable](ZiXunRequestModule *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12

  if ( !(unsigned __int8)-[ZiXunRequestModule needWaiting](self, "needWaiting") )
  {
    v2 = -[ZiXunRequestModule currentZiXunType](self, "currentZiXunType");
    v4 = v3(self, "getTableFlagValue:", v2);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = v6(self, "delegate");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = (unsigned __int8)v9(v8, "respondsToSelector:", "receiveZiXunData:tableFlag:");
    if ( v10 )
    {
      v12 = v11(self, "delegate");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v15 = v14(self, "ziXunItems");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v18 = v17(v5, "integerValue");
      v19(v13, "receiveZiXunData:tableFlag:", v16, v18);
      v20(v13);
    }
  }
}

//----- (00000001005AFBA4) ----------------------------------------------------
id __cdecl -[ZiXunRequestModule getTableFlagValue:](ZiXunRequestModule *self, SEL a2, unsigned __int64 a3)
{
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v11; // rax
  NSNumber *v12; // rax
  NSNumber *v14; // rax
  NSNumber *v15; // rax
  NSNumber *v17; // rax
  NSNumber *v18; // rax
  NSNumber *v20; // rax
  NSNumber *v21; // rax
  NSNumber *v23; // rax
  NSNumber *v24; // rax
  NSNumber *v26; // rax
  NSNumber *v27; // rax
  NSNumber *v29; // rax
  NSNumber *v30; // rax
  NSNumber *v32; // rax
  NSNumber *v33; // rax
  NSNumber *v35; // rax
  NSNumber *v36; // rax
  NSNumber *v38; // rax
  NSNumber *v39; // rax
  NSNumber *v41; // rax
  NSNumber *v42; // rax
  NSNumber *v44; // rax
  NSNumber *v45; // rax
  NSNumber *v47; // rax
  NSNumber *v48; // rax
  NSNumber *v50; // rax
  NSNumber *v51; // r13
  NSNumber *v52; // rax
  NSNumber *v53; // r15
  NSNumber *v55; // rax
  NSNumber *v56; // r14
  NSDictionary *v58; // rax
  NSDictionary *v59; // rbx
  NSNumber *v98; // [rsp+0h] [rbp-240h]
  NSNumber *v99; // [rsp+10h] [rbp-230h]
  NSNumber *v100; // [rsp+18h] [rbp-228h]
  NSNumber *v101; // [rsp+20h] [rbp-220h]
  NSNumber *v102; // [rsp+28h] [rbp-218h]
  NSNumber *v103; // [rsp+30h] [rbp-210h]
  NSNumber *v104; // [rsp+38h] [rbp-208h]
  NSNumber *v105; // [rsp+40h] [rbp-200h]
  NSNumber *v106; // [rsp+48h] [rbp-1F8h]
  NSNumber *v107; // [rsp+50h] [rbp-1F0h]
  NSNumber *v108; // [rsp+58h] [rbp-1E8h]
  NSNumber *v109; // [rsp+60h] [rbp-1E0h]
  NSNumber *v110; // [rsp+68h] [rbp-1D8h]
  NSNumber *v111; // [rsp+70h] [rbp-1D0h]
  NSNumber *v112; // [rsp+78h] [rbp-1C8h]
  NSNumber *v113; // [rsp+80h] [rbp-1C0h]
  NSNumber *v114; // [rsp+88h] [rbp-1B8h]
  NSNumber *v115; // [rsp+90h] [rbp-1B0h]
  NSNumber *v116; // [rsp+98h] [rbp-1A8h]
  NSNumber *v117; // [rsp+A0h] [rbp-1A0h]
  NSNumber *v118; // [rsp+A8h] [rbp-198h]
  NSNumber *v119; // [rsp+B0h] [rbp-190h]
  NSNumber *v120; // [rsp+B8h] [rbp-188h]
  NSNumber *v121; // [rsp+C0h] [rbp-180h]
  NSNumber *v122; // [rsp+C8h] [rbp-178h]
  NSNumber *v123; // [rsp+D0h] [rbp-170h]
  NSNumber *v124; // [rsp+D8h] [rbp-168h]
  NSNumber *v125; // [rsp+E0h] [rbp-160h]
  NSNumber *v126; // [rsp+E8h] [rbp-158h]
  NSNumber *v127; // [rsp+F0h] [rbp-150h]
  NSNumber *v128; // [rsp+F8h] [rbp-148h]
  NSNumber *v129; // [rsp+100h] [rbp-140h]
  _QWORD v130[17]; // [rsp+188h] [rbp-B8h] BYREF

  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", a3);
  v98 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14349LL);
  v129 = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v99 = objc_retainAutoreleasedReturnValue(v5);
  v130[0] = v99;
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14355LL);
  v100 = objc_retainAutoreleasedReturnValue(v6);
  *(_QWORD *)(v7 + 8) = v100;
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v101 = objc_retainAutoreleasedReturnValue(v8);
  v130[1] = v101;
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v102 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v10 + 16) = v102;
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v103 = objc_retainAutoreleasedReturnValue(v11);
  v130[2] = v103;
  v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2078LL);
  v104 = objc_retainAutoreleasedReturnValue(v12);
  *(_QWORD *)(v13 + 24) = v104;
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v105 = objc_retainAutoreleasedReturnValue(v14);
  v130[3] = v105;
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL);
  v106 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v16 + 32) = v106;
  v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v107 = objc_retainAutoreleasedReturnValue(v17);
  v130[4] = v107;
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6146LL);
  v108 = objc_retainAutoreleasedReturnValue(v18);
  *(_QWORD *)(v19 + 40) = v108;
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v109 = objc_retainAutoreleasedReturnValue(v20);
  v130[5] = v109;
  v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14369LL);
  v110 = objc_retainAutoreleasedReturnValue(v21);
  *(_QWORD *)(v22 + 48) = v110;
  v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v111 = objc_retainAutoreleasedReturnValue(v23);
  v130[6] = v111;
  v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7363LL);
  v112 = objc_retainAutoreleasedReturnValue(v24);
  *(_QWORD *)(v25 + 56) = v112;
  v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v113 = objc_retainAutoreleasedReturnValue(v26);
  v130[7] = v113;
  v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL);
  v114 = objc_retainAutoreleasedReturnValue(v27);
  *(_QWORD *)(v28 + 64) = v114;
  v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v115 = objc_retainAutoreleasedReturnValue(v29);
  v130[8] = v115;
  v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14340LL);
  v116 = objc_retainAutoreleasedReturnValue(v30);
  *(_QWORD *)(v31 + 72) = v116;
  v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v117 = objc_retainAutoreleasedReturnValue(v32);
  v130[9] = v117;
  v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7364LL);
  v118 = objc_retainAutoreleasedReturnValue(v33);
  *(_QWORD *)(v34 + 80) = v118;
  v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v119 = objc_retainAutoreleasedReturnValue(v35);
  v130[10] = v119;
  v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2077LL);
  v120 = objc_retainAutoreleasedReturnValue(v36);
  *(_QWORD *)(v37 + 88) = v120;
  v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v121 = objc_retainAutoreleasedReturnValue(v38);
  v130[11] = v121;
  v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14356LL);
  v122 = objc_retainAutoreleasedReturnValue(v39);
  *(_QWORD *)(v40 + 96) = v122;
  v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v123 = objc_retainAutoreleasedReturnValue(v41);
  v130[12] = v123;
  v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 32770LL);
  v124 = objc_retainAutoreleasedReturnValue(v42);
  *(_QWORD *)(v43 + 104) = v124;
  v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v125 = objc_retainAutoreleasedReturnValue(v44);
  v130[13] = v125;
  v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v126 = objc_retainAutoreleasedReturnValue(v45);
  *(_QWORD *)(v46 + 112) = v126;
  v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v127 = objc_retainAutoreleasedReturnValue(v47);
  v130[14] = v127;
  v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14342LL);
  v128 = objc_retainAutoreleasedReturnValue(v48);
  *(_QWORD *)(v49 + 120) = v128;
  v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v51 = objc_retainAutoreleasedReturnValue(v50);
  v130[15] = v51;
  v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14343LL);
  v53 = objc_retainAutoreleasedReturnValue(v52);
  *(_QWORD *)(v54 + 128) = v53;
  v55 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
  v56 = objc_retainAutoreleasedReturnValue(v55);
  v130[16] = v56;
  v58 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v130, v57, 17LL);
  v59 = objc_retainAutoreleasedReturnValue(v58);
  v60(v53);
  v61(v51);
  v62(v128);
  v63(v127);
  v64(v126);
  v65(v125);
  v66(v124);
  v67(v123);
  v68(v122);
  v69(v121);
  v70(v120);
  v71(v119);
  v72(v118);
  v73(v117);
  v74(v116);
  v75(v115);
  v76(v114);
  v77(v113);
  v78(v112);
  v79(v111);
  v80(v110);
  v81(v109);
  v82(v108);
  v83(v107);
  v84(v106);
  v85(v105);
  v86(v104);
  v87(v103);
  v88(v102);
  v89(v101);
  v90(v100);
  v91(v99);
  v92(v129);
  v93 = _objc_msgSend(v59, "objectForKeyedSubscript:", v98);
  v94 = objc_retainAutoreleasedReturnValue(v93);
  v95(v59);
  v96(v98);
  return objc_autoreleaseReturnValue(v94);
}

//----- (00000001005B0290) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule resetParamsBeforeRequest](ZiXunRequestModule *self, SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx

  -[ZiXunRequestModule setBackReqSum:](self, "setBackReqSum:", 0LL);
  -[ZiXunRequestModule setFailReqSum:](self, "setFailReqSum:", 0LL);
  -[ZiXunRequestModule setAllZiXunReqFail:](self, "setAllZiXunReqFail:", 0LL);
  v2 = -[ZiXunRequestModule ziXunItems](self, "ziXunItems");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeAllObjects");
}

//----- (00000001005B02FD) ----------------------------------------------------
NSString *__cdecl -[ZiXunRequestModule stockCode](ZiXunRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (00000001005B030E) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setStockCode:](ZiXunRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (00000001005B031D) ----------------------------------------------------
NSString *__cdecl -[ZiXunRequestModule market](ZiXunRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (00000001005B032E) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setMarket:](ZiXunRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (00000001005B033D) ----------------------------------------------------
char __cdecl -[ZiXunRequestModule needWaiting](ZiXunRequestModule *self, SEL a2)
{
  return self->_needWaiting;
}

//----- (00000001005B0347) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setNeedWaiting:](ZiXunRequestModule *self, SEL a2, char a3)
{
  self->_needWaiting = a3;
}

//----- (00000001005B0350) ----------------------------------------------------
ZiXunDispatchDelegate *__cdecl -[ZiXunRequestModule delegate](ZiXunRequestModule *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_delegate);
  return (ZiXunDispatchDelegate *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001005B0366) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setDelegate:](ZiXunRequestModule *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_delegate, a3);
}

//----- (00000001005B0377) ----------------------------------------------------
NSMutableArray *__cdecl -[ZiXunRequestModule ziXunItems](ZiXunRequestModule *self, SEL a2)
{
  return self->_ziXunItems;
}

//----- (00000001005B0381) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setZiXunItems:](ZiXunRequestModule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ziXunItems, a3);
}

//----- (00000001005B0392) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunRequestModule currentZiXunType](ZiXunRequestModule *self, SEL a2)
{
  return self->_currentZiXunType;
}

//----- (00000001005B039C) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setCurrentZiXunType:](ZiXunRequestModule *self, SEL a2, unsigned __int64 a3)
{
  self->_currentZiXunType = a3;
}

//----- (00000001005B03A6) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunRequestModule requestSum](ZiXunRequestModule *self, SEL a2)
{
  return self->_requestSum;
}

//----- (00000001005B03B0) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setRequestSum:](ZiXunRequestModule *self, SEL a2, unsigned __int64 a3)
{
  self->_requestSum = a3;
}

//----- (00000001005B03BA) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunRequestModule backReqSum](ZiXunRequestModule *self, SEL a2)
{
  return self->_backReqSum;
}

//----- (00000001005B03C4) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setBackReqSum:](ZiXunRequestModule *self, SEL a2, unsigned __int64 a3)
{
  self->_backReqSum = a3;
}

//----- (00000001005B03CE) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunRequestModule failReqSum](ZiXunRequestModule *self, SEL a2)
{
  return self->_failReqSum;
}

//----- (00000001005B03D8) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setFailReqSum:](ZiXunRequestModule *self, SEL a2, unsigned __int64 a3)
{
  self->_failReqSum = a3;
}

//----- (00000001005B03E2) ----------------------------------------------------
char __cdecl -[ZiXunRequestModule allZiXunReqFail](ZiXunRequestModule *self, SEL a2)
{
  return self->_allZiXunReqFail;
}

//----- (00000001005B03EC) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setAllZiXunReqFail:](ZiXunRequestModule *self, SEL a2, char a3)
{
  self->_allZiXunReqFail = a3;
}

//----- (00000001005B03F5) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule setDateFormatter:](ZiXunRequestModule *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_dateFormatter, a3);
}

//----- (00000001005B0406) ----------------------------------------------------
void __cdecl -[ZiXunRequestModule .cxx_destruct](ZiXunRequestModule *self, SEL a2)
{
  objc_storeStrong((id *)&self->_dateFormatter, 0LL);
  objc_storeStrong((id *)&self->_ziXunItems, 0LL);
  objc_destroyWeak((id *)&self->_delegate);
  objc_storeStrong((id *)&self->_market, 0LL);
  objc_storeStrong((id *)&self->_stockCode, 0LL);
}

