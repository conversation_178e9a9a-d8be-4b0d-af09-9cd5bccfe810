WtSettingsWindowController *__cdecl -[WtSettingsWindowController initWithWindowNibName:](
        WtSettingsWindowController *self,
        SEL a2,
        id a3)
{

  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___WtSettingsWindowController;
  return (WtSettingsWindowController *)-[HXBaseTradeWindow initWithWindowNibName:](&v4, "initWithWindowNibName:", a3);
}

//----- (0000000100C9B84B) ----------------------------------------------------
id __cdecl +[WtSettingsWindowController shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100C9B8AE;
  block[3] = (__int64)&unk_1012E3900;
  block[4] = (__int64)a1;
  if ( qword_1016D3AA8 != -1 )
    dispatch_once(&qword_1016D3AA8, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3AA0);
}

//----- (0000000100C9B8AE) ----------------------------------------------------
void __fastcall sub_100C9B8AE(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "initWithWindowNibName:", CFSTR("WtSettingsWindowController"));
  v3 = qword_1016D3AA0;
  qword_1016D3AA0 = v2;
}

//----- (0000000100C9B8E7) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController windowDidLoad](WtSettingsWindowController *self, SEL a2)
{
  NSComboBox *v2; // rax
  NSComboBox *v3; // rbx
  NSComboBox *v4; // rax
  NSComboBox *v11; // rax
  NSComboBox *v12; // rbx
  _QWORD v15[4]; // [rsp+8h] [rbp-68h] BYREF
  id to; // [rsp+28h] [rbp-48h] BYREF
  id location[6]; // [rsp+40h] [rbp-30h] BYREF

  v17.receiver = self;
  v17.super_class = (Class)&OBJC_CLASS___WtSettingsWindowController;
  -[HXBaseTradeWindow windowDidLoad](&v17, "windowDidLoad");
  v2 = -[WtSettingsWindowController wtInfoComboBox](self, "wtInfoComboBox");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeAllItems");
  v4 = -[WtSettingsWindowController wtInfoComboBox](self, "wtInfoComboBox");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = +[WtInfoManager shareInstance](&OBJC_CLASS___WtInfoManager, "shareInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "serverList");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v9, "addItemsWithObjectValues:", v8);
  v11 = -[WtSettingsWindowController wtInfoComboBox](self, "wtInfoComboBox");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "selectItemAtIndex:", 0LL);
  objc_initWeak(location, self);
  v13 = +[WtInfoManager shareInstance](&OBJC_CLASS___WtInfoManager, "shareInstance");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15[0] = _NSConcreteStackBlock;
  v15[1] = 3254779904LL;
  v15[2] = sub_100C9BAFF;
  v15[3] = &unk_1012EA728;
  objc_copyWeak(&to, location);
  _objc_msgSend(v14, "setBrokerChanged:", v15);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100C9BAFF) ----------------------------------------------------
void __fastcall sub_100C9BAFF(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  SEL v7; // r12
  SEL v11; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v14 = v2;
  v4 = _objc_msgSend(WeakRetained, "wtInfoComboBox");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "removeAllItems");
  v6 = objc_loadWeakRetained((id *)(a1 + 32));
  v8 = _objc_msgSend(v6, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "addItemsWithObjectValues:", v14);
  v10 = objc_loadWeakRetained((id *)(a1 + 32));
  v12 = _objc_msgSend(v10, v11);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "selectItemAtIndex:", 0LL);
}

//----- (0000000100C9BC48) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController showWindow:](WtSettingsWindowController *self, SEL a2, id a3)
{
  JYBaseWindow *v4; // rax
  JYBaseWindow *v5; // rbx

  v3 = objc_retain(a3);
  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___WtSettingsWindowController;
  -[HXBaseTradeWindow showWindow:](&v6, "showWindow:", v3);
  v4 = -[HXBaseTradeWindow jyWindow](self, "jyWindow");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "makeKeyAndOrderFront:", 0LL);
}

//----- (0000000100C9BCF2) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController windowWillClose:](WtSettingsWindowController *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "object");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = _objc_msgSend(v6, "window");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( v5 == v8 )
  {
    v21.receiver = v9;
    v21.super_class = (Class)&OBJC_CLASS___WtSettingsWindowController;
    -[HXBaseTradeWindow windowWillClose:](&v21, "windowWillClose:", v3);
    v11 = _objc_msgSend(v10, "window");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = (unsigned __int8)_objc_msgSend(v12, "isSheet");
    if ( v13 )
    {
      v15 = _objc_msgSend(v14, "window");
      v22 = objc_retainAutoreleasedReturnValue(v15);
      v16 = _objc_msgSend(v22, "sheetParent");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v19 = _objc_msgSend(v18, "window");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      _objc_msgSend(v17, "endSheet:", v20);
    }
  }
}

//----- (0000000100C9BEA0) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController wtASetBtnClicked:](WtSettingsWindowController *self, SEL a2, id a3)
{

  v3 = +[WtEditWindowController shareInstance](&OBJC_CLASS___WtEditWindowController, "shareInstance", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setType:", 1LL);
  _objc_msgSend(v4, "showWindow:", v5);
  _objc_msgSend(NSApp, "activateIgnoringOtherApps:", 1LL);
  v6 = _objc_msgSend(v4, "window");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  _objc_msgSend(v7, "makeKeyAndOrderFront:", v8);
  v10 = _objc_msgSend(v9, "wtInfoComboBox");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "indexOfSelectedItem");
  v12 = +[WtInfoManager shareInstance](&OBJC_CLASS___WtInfoManager, "shareInstance");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v15 = _objc_msgSend(v13, "getServerItem:", v14);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v4, "shouldReloadData");
}

//----- (0000000100C9C013) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController wtAddBtnClicked:](WtSettingsWindowController *self, SEL a2, id a3)
{

  v3 = +[WtEditWindowController shareInstance](&OBJC_CLASS___WtEditWindowController, "shareInstance", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setType:", 0LL);
  _objc_msgSend(v4, "showWindow:", self);
  _objc_msgSend(NSApp, "activateIgnoringOtherApps:", 1LL);
  v5 = _objc_msgSend(v4, "window");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "makeKeyAndOrderFront:", self);
}

//----- (0000000100C9C0EE) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController wtDeleteBtnClicked:](WtSettingsWindowController *self, SEL a2, id a3)
{
  NSComboBox *v3; // rax
  NSComboBox *v4; // rbx
  SEL v10; // r12
  SEL v13; // r12

  v3 = -[WtSettingsWindowController wtInfoComboBox](self, "wtInfoComboBox", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "indexOfSelectedItem");
  v6 = +[WtInfoManager shareInstance](&OBJC_CLASS___WtInfoManager, "shareInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "getServerItem:", v5);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v31 = v9;
  v11 = _objc_msgSend(&OBJC_CLASS___WtInfoManager, v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "removeServerItem:", v9);
  v14 = _objc_msgSend(&OBJC_CLASS___WtInfoManager, v13);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(v15, "serverList");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "mutableCopy");
  if ( _objc_msgSend(v18, "count") > v5 )
  {
    _objc_msgSend(v18, "removeObjectAtIndex:", v5);
    v21 = _objc_msgSend(v20, "wtInfoComboBox");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    _objc_msgSend(v22, "removeAllItems");
    v24 = _objc_msgSend(v23, "wtInfoComboBox");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    _objc_msgSend(v25, "addItemsWithObjectValues:", v18);
    v27 = _objc_msgSend(v26, "wtInfoComboBox");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    _objc_msgSend(v28, "selectItemAtIndex:", 0LL);
    v29 = +[WtInfoManager shareInstance](&OBJC_CLASS___WtInfoManager, "shareInstance");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    _objc_msgSend(v30, "serverlistUpdate");
  }
}

//----- (0000000100C9C39D) ----------------------------------------------------
NSComboBox *__cdecl -[WtSettingsWindowController wtInfoComboBox](WtSettingsWindowController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._document);
  return (NSComboBox *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100C9C3B6) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController setWtInfoComboBox:](WtSettingsWindowController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._document, a3);
}

//----- (0000000100C9C3CA) ----------------------------------------------------
void __cdecl -[WtSettingsWindowController .cxx_destruct](WtSettingsWindowController *self, SEL a2)
{
  objc_destroyWeak((id *)&self->super.super._document);
}

