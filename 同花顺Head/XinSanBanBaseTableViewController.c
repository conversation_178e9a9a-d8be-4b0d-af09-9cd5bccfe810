id __cdecl -[XinSanBanBaseTableViewController nibName](XinSanBanBaseTableViewController *self, SEL a2)
{
  return CFSTR("XinSanBanBaseTableViewController");
}

//----- (000000010018216D) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController viewDidLoad](XinSanBanBaseTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XinSanBanBaseTableViewController;
  -[QuoteBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setDisableIWenCaiEditEntrance:](self, "setDisableIWenCaiEditEntrance:", 1LL);
  -[QuoteBaseTableViewController setDisableTableHeaderMenu:](self, "setDisableTableHeaderMenu:", 1LL);
  -[QuoteBaseTableViewController setTextMarkState](self, "setTextMarkState");
}

//----- (00000001001821DA) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController requestForMyTable](XinSanBanBaseTableViewController *self, SEL a2)
{
  -[QuoteBaseTableViewController deleteOrder](self, "deleteOrder");
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    -[QuoteBaseTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
    -[XinSanBanBaseTableViewController requestCodeList](self, "requestCodeList");
  }
}

//----- (000000010018223D) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController requestCodeList](XinSanBanBaseTableViewController *self, SEL a2)
{
  NSNumber *v14; // rax
  NSNumber *v15; // rax
  NSNumber *v20; // rax
  NSNumber *v21; // r15
  NSNumber *v24; // rax
  NSNumber *v25; // r13
  NSDictionary *v26; // rax
  id *v31; // r12
  _QWORD v32[4]; // [rsp+0h] [rbp-D0h] BYREF
  id to; // [rsp+20h] [rbp-B0h] BYREF
  id location; // [rsp+48h] [rbp-88h] BYREF

  if ( -[QuoteBaseTableViewController blockID](self, "blockID") != (id)-1LL )
  {
    v3 = _objc_msgSend(v2, "sortOrder");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    if ( v4 )
    {
      objc_initWeak(&location, v5);
      v7 = _objc_msgSend(v6, "begin");
      v9 = _objc_msgSend(v8, "count");
      if ( _objc_msgSend(v10, "sortID") != (id)12345670 )
      {
        v12 = _objc_msgSend(v11, "wenCaiSortIdentifier");
        v13 = objc_retainAutoreleasedReturnValue(v12);
        if ( !v13 )
        {
          v39[0] = (__int64)CFSTR("sortbegin");
          v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v7);
          v34 = objc_retainAutoreleasedReturnValue(v14);
          v40[0] = (__int64)v34;
          v39[1] = (__int64)CFSTR("sortcount");
          v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v9);
          v35 = objc_retain(v15);
          v40[1] = (__int64)v35;
          v39[2] = (__int64)CFSTR("sortorder");
          v17 = _objc_msgSend(v16, "sortOrder");
          v36 = objc_retain(v17);
          v40[2] = (__int64)v36;
          v39[3] = (__int64)CFSTR("sortid");
          v19 = _objc_msgSend(v18, "sortID");
          v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v19);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          v40[3] = (__int64)v21;
          v39[4] = (__int64)CFSTR("blockid");
          v23 = _objc_msgSend(v22, "blockID");
          v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v23);
          v25 = objc_retainAutoreleasedReturnValue(v24);
          v40[4] = (__int64)v25;
          v26 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v39, 5LL);
          v37 = objc_retainAutoreleasedReturnValue(v26);
          v28 = _objc_msgSend(v27, "basicHQCodeListRequestModule");
          v29 = objc_retain(v28);
          v32[0] = _NSConcreteStackBlock;
          v32[1] = 3254779904LL;
          v32[2] = sub_10018259A;
          v32[3] = &unk_1012DAED8;
          objc_copyWeak(&to, &location);
          v30 = v37;
          _objc_msgSend(v29, "request:params:callBack:", 14LL, v37, v32);
          objc_destroyWeak(v31);
        }
      }
      objc_destroyWeak(&location);
    }
  }
}

//----- (000000010018259A) ----------------------------------------------------
void __fastcall sub_10018259A(__int64 a1, void *a2, void *a3)
{
  id *v8; // r13
  id WeakRetained; // rbx
  NSNumber *v12; // rax
  NSNumber *v22; // [rsp+10h] [rbp-30h]

  objc_retain(a2);
  v4 = objc_retain(a3);
  v6 = _objc_msgSend(v5, "count");
  if ( v4 && v6 )
  {
    _objc_msgSend(v7, "mutableCopy");
    v8 = (id *)(a1 + 32);
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "setOrderCodeMArray:", v10);
    v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34056LL);
    v22 = objc_retainAutoreleasedReturnValue(v12);
    v13 = _objc_msgSend(v4, "thsNumberForKey:", v22);
    v21 = v4;
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "unsignedIntegerValue");
    v16 = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(v16, "setAllCodesNum:", v15);
    v18 = v14;
    v4 = v21;
    v19 = objc_loadWeakRetained(v8);
    _objc_msgSend(v19, "requestDetailData");
  }
}

//----- (000000010018270F) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController requestDetailData](XinSanBanBaseTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // r15
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // rax
  HXStockModel *v8; // rax
  HXStockModel *v9; // rbx
  NSArray *v10; // rax
  NSArray *v11; // rbx
  NSMutableArray *v12; // rax
  NSDictionary *v13; // rax
  HXTableRequestModule *v15; // rax
  HXTableRequestModule *v16; // rbx
  _QWORD v17[4]; // [rsp+0h] [rbp-80h] BYREF
  id to; // [rsp+20h] [rbp-60h] BYREF
  id location; // [rsp+28h] [rbp-58h] BYREF

  objc_initWeak(&location, self);
  v2 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !_objc_msgSend(v3, "count") )
    goto LABEL_4;
  v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  if ( v6 )
  {
    v8 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    -[HXStockModel setMainRequestDidBack:](v9, "setMainRequestDidBack:", 0LL);
    v20[0] = (__int64)CFSTR("datatype");
    v10 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v21[0] = (__int64)v11;
    v20[1] = (__int64)CFSTR("StockCodesAndMarket");
    v12 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v21[1] = (__int64)objc_retain(v12);
    v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 2LL);
    v3 = objc_retainAutoreleasedReturnValue(v13);
    v15 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
    v16 = objc_retain(v15);
    v17[0] = _NSConcreteStackBlock;
    v17[1] = 3254779904LL;
    v17[2] = sub_10018295A;
    v17[3] = &unk_1012DAED8;
    objc_copyWeak(&to, &location);
    -[HXTableRequestModule request:params:callBack:](v16, "request:params:callBack:", 4LL, v3, v17);
    objc_destroyWeak(&to);
LABEL_4:
  }
  objc_destroyWeak(&location);
}

//----- (000000010018295A) ----------------------------------------------------
void __fastcall sub_10018295A(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // rax

  v9 = objc_retain(a3);
  v10 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setMainRequestDidBack:", 1LL);
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v7, "dealWithRequestData:extension:", v10, v9);
}

//----- (0000000100182A25) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController setOriginalSortInfoForMyFrozenTable](
        XinSanBanBaseTableViewController *self,
        SEL a2)
{
  HXTableManager *v2; // rax
  NSString *v13; // rax
  NSString *v14; // rax
  NSSortDescriptor *v15; // rax
  NSSortDescriptor *v16; // r13
  NSArray *v17; // rax
  NSArray *v18; // r15
  NSSortDescriptor *v28; // [rsp+58h] [rbp-38h] BYREF

  v27 = self;
  v22 = 0LL;
  v23 = &v22;
  v24 = 0x2020000000LL;
  v25 = 0;
  v2 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v27, "tableKey");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "getSelectedSchemesForKey:", v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v26 = v7;
  v9 = _objc_msgSend(v7, "dataItemEntities");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "enumerateObjectsUsingBlock:");
  v11 = 199112LL;
  if ( !*((_BYTE *)v23 + 24) )
    v11 = 5LL;
  _objc_msgSend(v27, "setSortID:", v11);
  _objc_msgSend(v27, "setIsSetSortInfoByCode:", 1LL);
  v12 = _objc_msgSend(v27, "sortID");
  v13 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(&OBJC_CLASS___NSSortDescriptor, "sortDescriptorWithKey:ascending:", v14, 0LL);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v28 = v16;
  v17 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v28, 1LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v27, "myFrozenTable");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  _objc_msgSend(v20, "setSortDescriptors:", v18);
  _objc_msgSend(v27, "setSortOrder:", CFSTR("D"));
  _objc_msgSend(v27, "setWenCaiSortIdentifier:", 0LL);
  _Block_object_dispose(&v22, 8);
}

//----- (0000000100182CDA) ----------------------------------------------------
void __fastcall sub_100182CDA(__int64 a1, void *a2, __int64 a3, _BYTE *a4)
{

  v5 = _objc_msgSend(a2, "identifier");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "longLongValue");
  if ( v8 == 199112 )
  {
    *(_BYTE *)(*(_QWORD *)(*(_QWORD *)(a1 + 32) + 8LL) + 24LL) = 1;
    *a4 = 1;
  }
}

//----- (0000000100182D48) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController dealWithRequestData:extension:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  HXStockModel *v6; // rax
  HXStockModel *v7; // rbx

  v4 = -[XinSanBanBaseTableViewController calculateItemsWithRequestData:](
         self,
         "calculateItemsWithRequestData:",
         a3,
         a4);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[HXStockModel setMainStockTableViewDataArray:](v7, "setMainStockTableViewDataArray:", v5);
  v8(v5);
  -[QuoteBaseTableViewController setOrderCodeList](self, "setOrderCodeList");
  -[QuoteBaseTableViewController reloadData:](self, "reloadData:", 0LL);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  -[QuoteBaseTableViewController setRequestFromZero:](self, "setRequestFromZero:", 0LL);
  -[QuoteBaseTableViewController order](self, "order");
}

//----- (0000000100182E0A) ----------------------------------------------------
id __cdecl -[XinSanBanBaseTableViewController calculateItemsWithRequestData:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3)
{
  id (**v4)(id, SEL, ...); // r14
  unsigned __int64 v5; // r15
  SEL v14; // r12
  id (**v18)(id, SEL, ...); // r13
  unsigned __int64 v43; // [rsp+8h] [rbp-A8h]

  v3 = objc_retain(a3);
  v4 = &_objc_msgSend;
  v47 = _objc_msgSend(v3, "mutableCopy");
  v48 = v3;
  if ( _objc_msgSend(v3, "count") )
  {
    v5 = 0LL;
    do
    {
      v6 = ((id (*)(id, SEL, ...))v4)(v48, "thsDictionaryAtIndex:", v5);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      if ( ((id (*)(id, SEL, ...))v4)(v7, "count") )
      {
        v43 = v5;
        v9 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        v11 = ((id (*)(id, SEL, ...))v4)(v7, "thsNumberForKey:", v10);
        v46 = objc_retainAutoreleasedReturnValue(v11);
        v12 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v15 = ((id (*)(id, SEL, ...))v4)(v7, v14, v13);
        objc_retainAutoreleasedReturnValue(v15);
        v16 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "class");
        v17 = (unsigned __int8)((id (*)(id, SEL, ...))v4)(v46, "isKindOfClass:", v16);
        v18 = v4;
        v52 = v7;
        v44 = v19;
        if ( v17 )
        {
          if ( ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 4294967295.0
            || ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 2147483648.0
            || ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 0.0
            || (v33 = ((__int64 (__fastcall *)(void *, const char *))v4)(&OBJC_CLASS___NSNumber, "class"),
                !((unsigned __int8 (__fastcall *)(__int64, const char *, __int64))v4)(v34, "isKindOfClass:", v33))
            || ((double (__fastcall *)(__int64, const char *))v4)(v35, "doubleValue") == 4294967295.0
            || ((double (__fastcall *)(__int64, const char *))v4)(v36, "doubleValue") == 2147483648.0
            || ((double (__fastcall *)(__int64, const char *))v4)(v37, "doubleValue") == 0.0 )
          {
            v20 = 0LL;
          }
          else
          {
            v50 = ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue");
            v51 = v50 - ((double (__fastcall *)(__int64, const char *))v4)(v38, "doubleValue");
            v45 = v51 / ((double (__fastcall *)(__int64, const char *))v4)(v39, "doubleValue");
            v40 = (void *)((__int64 (__fastcall *)(void *, const char *, double))v4)(
                            &OBJC_CLASS___NSNumber,
                            "numberWithDouble:",
                            v51);
            v20 = objc_retainAutoreleasedReturnValue(v40);
            v41 = (void *)((__int64 (__fastcall *)(void *, const char *, double))v18)(
                            &OBJC_CLASS___NSNumber,
                            "numberWithDouble:",
                            v45 * 100.0);
            objc_retainAutoreleasedReturnValue(v41);
          }
        }
        else
        {
          v20 = 0LL;
        }
        v21 = ((__int64 (__fastcall *)(id, const char *))v18)(v7, "mutableCopy");
        v22 = ((__int64 (__fastcall *)(void *, const char *))v18)(&OBJC_CLASS___NSNumber, "class");
        if ( ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v18)(v20, "isKindOfClass:", v22)
          && ((double (__fastcall *)(id, const char *))v18)(v20, "doubleValue") != 4294967295.0
          && ((double (__fastcall *)(id, const char *))v18)(v20, "doubleValue") != 2147483648.0 )
        {
          v23 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                          &OBJC_CLASS___NSNumber,
                          "numberWithInt:",
                          264648LL);
          v24 = objc_retainAutoreleasedReturnValue(v23);
          ((void (__fastcall *)(__int64, const char *, id, id))v18)(v21, "setObject:forKey:", v20, v24);
        }
        v25 = ((__int64 (__fastcall *)(void *, const char *))v18)(&OBJC_CLASS___NSNumber, "class");
        if ( ((unsigned __int8 (__fastcall *)(__int64, const char *, __int64))v18)(v26, "isKindOfClass:", v25)
          && ((double (__fastcall *)(__int64, const char *))v18)(v27, "doubleValue") != 4294967295.0
          && ((double (__fastcall *)(__int64, const char *))v18)(v28, "doubleValue") != 2147483648.0 )
        {
          v29 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                          &OBJC_CLASS___NSNumber,
                          "numberWithInt:",
                          199112LL);
          v49 = objc_retainAutoreleasedReturnValue(v29);
          ((void (__fastcall *)(__int64, const char *, __int64, id))v18)(v21, "setObject:forKey:", v30, v49);
        }
        ((void (__fastcall *)(id, const char *, __int64, unsigned __int64))v18)(
          v47,
          "setObject:atIndexedSubscript:",
          v21,
          v43);
        v31 = (void *)v21;
        v5 = v43;
        v4 = v18;
        v7 = v52;
      }
      v8(v7);
      ++v5;
    }
    while ( (unsigned __int64)((id (*)(id, SEL, ...))v4)(v48, "count") > v5 );
  }
  return objc_autoreleaseReturnValue(v47);
}

//----- (00000001001832C7) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController myTableIsSelected:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3)
{
  NSString *v7; // rax
  NSString *v8; // rbx
  NSString *v10; // rax
  NSString *v11; // rbx
  NSDictionary *v14; // rax
  NSString *v22; // [rsp+8h] [rbp-68h]
  _QWORD v25[2]; // [rsp+20h] [rbp-50h] BYREF
  _QWORD v26[2]; // [rsp+30h] [rbp-40h] BYREF

  v3 = _objc_msgSend(self, "className", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v21 = v4;
  _objc_msgSend(v6, "postNotificationName:object:", CFSTR("FocusDidChangedBetweenViews"), v4);
  if ( (unsigned __int8)-[HXBaseTableViewController isForCustomizablePage](self, "isForCustomizablePage") )
  {
    v7 = -[HXBaseTableViewController selectedCode](self, "selectedCode");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "length");
    if ( v9 )
    {
      v10 = -[HXBaseTableViewController selectedCode](self, "selectedCode");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v11);
      v23 = objc_retainAutoreleasedReturnValue(v12);
      v13 = +[HXTools marketConvertToTablePanKouType:](&OBJC_CLASS___HXTools, "marketConvertToTablePanKouType:", v23);
      v25[0] = CFSTR("PanKouLinkCode");
      v22 = v11;
      v26[0] = v11;
      v25[1] = CFSTR("PanKouModularType");
      v24 = objc_retainAutoreleasedReturnValue(v13);
      v26[1] = v24;
      v14 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v26, v25, 2LL);
      objc_retainAutoreleasedReturnValue(v14);
      v15 = -[QuoteBaseTableViewController refreshRightViewBlock](self, "refreshRightViewBlock");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      if ( v16 )
      {
        v18 = -[QuoteBaseTableViewController refreshRightViewBlock](self, "refreshRightViewBlock");
        v19 = (void (__fastcall **)(id, _QWORD))objc_retainAutoreleasedReturnValue(v18);
        v19[2](v19, v20);
      }
    }
  }
}

//----- (00000001001834D3) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController myTableIsDoubleClicked:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3)
{
  HXFrozenTableView *v4; // rax
  HXFrozenTableView *v5; // rbx
  _BYTE *v6; // r14
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx
  HXStockModel *v10; // rax
  HXStockModel *v11; // r14
  NSArray *v12; // rax
  NSArray *v13; // rbx
  NSNumber *v18; // rax
  NSNumber *v19; // rbx
  NSString *v24; // rax
  __CFString *v25; // rbx
  __CFString *v26; // r14
  __CFString *v27; // rdi
  __CFString *v29; // rax
  id (__cdecl *v30)(id); // r12
  __CFString *v31; // rbx
  NSString *v35; // rax
  __CFString *v36; // rax
  __CFString *v37; // rbx
  __CFString *v38; // rdi
  NSNumber *v39; // rax
  NSNumber *v45; // rax
  NSNumber *v48; // rax
  _QWORD *v49; // r12
  NSNumber *v51; // rax
  NSNumber *v53; // rax
  SEL v54; // r12
  NSNumber *v56; // rax
  NSNumber *v58; // rax
  NSNumber *v59; // rbx
  NSDictionary *v60; // rax
  NSDictionary *v61; // r14
  SEL v67; // r12
  NSDictionary *v71; // rdi
  NSNumber *v76; // [rsp+18h] [rbp-148h]
  NSNumber *v78; // [rsp+28h] [rbp-138h]
  NSNumber *v79; // [rsp+30h] [rbp-130h]
  NSNumber *v80; // [rsp+38h] [rbp-128h]
  NSNumber *v81; // [rsp+40h] [rbp-120h]
  __CFString *v83; // [rsp+50h] [rbp-110h]
  _QWORD v86[12]; // [rsp+70h] [rbp-F0h] BYREF
  _QWORD v87[12]; // [rsp+D0h] [rbp-90h] BYREF

  -[QuoteBaseTableViewController setRequestRowRange](self, "setRequestRowRange", a3);
  v4 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXFrozenTableView selectedRow](v5, "selectedRow");
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8);
  if ( v8 )
  {
    if ( v6 == (_BYTE *)-1LL )
      return;
    v85 = (char *)(v6 - (_BYTE *)-[QuoteBaseTableViewController begin](self, "begin"));
  }
  else
  {
    v85 = -[QuoteBaseTableViewController begin](self, "begin");
  }
  v10 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = -[HXStockModel mainStockTableViewDataArray](v11, "mainStockTableViewDataArray");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "thsDictionaryAtIndex:", v85);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(v13);
  v17(v11);
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v15, "thsStringForKey:", v19);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  if ( _objc_msgSend(v21, "length") )
  {
    v24 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v82 = v21;
    v26 = &charsToLeaveEscaped;
    v27 = v25;
    if ( !v25 )
      v27 = &charsToLeaveEscaped;
    v83 = objc_retain(v27);
    v28 = -[XinSanBanBaseTableViewController tableKey](self, "tableKey");
    v29 = (__CFString *)objc_retainAutoreleasedReturnValue(v28);
    v31 = v29;
    if ( v29 )
      v26 = v29;
    v32 = v30(v26);
    v34 = v33;
    v75 = v32;
    v35 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    v37 = v36;
    v38 = CFSTR("D");
    if ( v36 )
      v38 = v36;
    v84 = (id)v34(v38);
    v86[0] = CFSTR("requesttype");
    v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
    v76 = objc_retainAutoreleasedReturnValue(v39);
    v87[0] = v76;
    v86[1] = CFSTR("tablekey");
    v87[1] = v40;
    v86[2] = CFSTR("TableID");
    v41 = -[QuoteBaseTableViewController tableID](self, "tableID");
    v43 = _objc_msgSend(v42, "numberWithLong:", v41);
    v77 = objc_retainAutoreleasedReturnValue(v43);
    v87[2] = v77;
    v86[3] = CFSTR("blockid");
    v44 = -[QuoteBaseTableViewController blockID](self, "blockID");
    v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v44);
    v78 = objc_retainAutoreleasedReturnValue(v45);
    v87[3] = v78;
    *(_QWORD *)(v46 + 32) = CFSTR("sortid");
    v47 = -[HXBaseTableViewController sortID](self, "sortID");
    v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v47);
    v79 = objc_retainAutoreleasedReturnValue(v48);
    v87[4] = v79;
    v49[5] = CFSTR("IWenCaiSortIdentifier");
    v87[5] = v83;
    v49[6] = CFSTR("sortorder");
    v87[6] = v84;
    v49[7] = CFSTR("sortbegin");
    v50 = -[QuoteBaseTableViewController begin](self, "begin");
    v51 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v50);
    v80 = objc_retainAutoreleasedReturnValue(v51);
    v87[7] = v80;
    v86[8] = CFSTR("sortcount");
    v52 = -[QuoteBaseTableViewController count](self, "count");
    v53 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v52);
    v81 = objc_retainAutoreleasedReturnValue(v53);
    v87[8] = v81;
    v86[9] = CFSTR("Index");
    v55 = _objc_msgSend(self, v54);
    v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedLong:", &v85[(_QWORD)v55]);
    v87[9] = objc_retainAutoreleasedReturnValue(v56);
    v86[10] = CFSTR("SelectedCode");
    v87[10] = v82;
    v86[11] = CFSTR("totalnumber");
    v57 = -[QuoteBaseTableViewController allCodesNum](self, "allCodesNum");
    v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v57);
    v59 = objc_retainAutoreleasedReturnValue(v58);
    v87[11] = v59;
    v60 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v87, v86, 12LL);
    v61 = objc_retainAutoreleasedReturnValue(v60);
    v63 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v64 = objc_retainAutoreleasedReturnValue(v63);
    _objc_msgSend(v64, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
    v65 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v66 = objc_retainAutoreleasedReturnValue(v65);
    _objc_msgSend(v66, v67, CFSTR("DeliverQuotationTableDataNotification"), v61);
    v68(v84);
    v69(v75);
    v70(v66);
    v71 = v61;
    v21 = v82;
    v72(v71);
    v73(v83);
  }
  v23(v21);
  v74(v15);
}

//----- (0000000100183B15) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController actionForTableViewSelectionDidChange:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v3; // rax
  HXStockModel *v4; // rax
  NSArray *v5; // rax
  NSArray *v6; // r13
  NSArray *v7; // rax
  NSArray *v8; // rbx
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  HXMarkTableView *v24; // r13
  HXFrozenTableView *v25; // rax
  HXMarkTableView *v26; // rax
  HXMarkTableView *v27; // rax
  HXMarkTableView *v32; // rbx
  HXFrozenTableView *v33; // rax
  HXMarkTableView *v34; // rax
  HXMarkTableView *v35; // r12
  HXBaseTableView *v40; // rbx
  HXBaseTableView *v41; // rax
  HXBaseTableView *v42; // rax
  _BOOL4 v43; // r13d
  NSDictionary *v50; // rax
  NSDictionary *v51; // r15
  XinSanBanBaseTableViewController *v54; // r15
  NSMutableArray *v55; // rax
  NSMutableArray *v56; // rbx
  NSIndexSet *v57; // rax
  NSIndexSet *v58; // rax
  _BYTE *v61; // rax
  NSNumber *v64; // rax
  NSNumber *v65; // r13
  NSMutableArray *v68; // rax
  NSMutableArray *v69; // rbx
  NSIndexSet *v71; // rax
  NSIndexSet *v72; // rbx
  NSMutableArray *v75; // rax
  NSMutableArray *v76; // rbx
  NSMutableArray *v82; // rax
  NSMutableArray *v83; // rbx
  NSMutableArray *v85; // rax
  NSDictionary *v87; // rax
  HXFrozenTableView *v88; // rax
  HXFrozenTableView *v89; // rbx
  HXMarkTableView *v94; // [rsp+10h] [rbp-F0h]
  HXMarkTableView *v97; // [rsp+28h] [rbp-D8h]
  HXFrozenTableView *v98; // [rsp+30h] [rbp-D0h]
  NSArray *v100; // [rsp+40h] [rbp-C0h]
  HXFrozenTableView *v101; // [rsp+48h] [rbp-B8h]
  HXMarkTableView *v107; // [rsp+70h] [rbp-90h]
  NSMutableArray *v109; // [rsp+70h] [rbp-90h]
  _QWORD v110[3]; // [rsp+98h] [rbp-68h] BYREF
  _QWORD v111[2]; // [rsp+B0h] [rbp-50h] BYREF
  _QWORD v112[2]; // [rsp+C0h] [rbp-40h] BYREF

  v3 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXStockModel mainStockTableViewDataArray](v4, "mainStockTableViewDataArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v100 = v8;
  v10 = _objc_msgSend(v8, "count");
  if ( (unsigned __int64)a3 <= 0x7FFFFFFFFFFFFFFELL && v10 )
  {
    v11 = -[QuoteBaseTableViewController begin](self, "begin");
    v12 = _objc_msgSend(v8, "thsDictionaryAtIndex:", a3 - (_QWORD)v11);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(v13, "thsStringForKey:", v15);
    objc_retainAutoreleasedReturnValue(v16);
    v18 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v17);
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = +[HXTools marketConvertToTablePanKouType:](&OBJC_CLASS___HXTools, "marketConvertToTablePanKouType:", v19);
    v102 = objc_retainAutoreleasedReturnValue(v20);
    v21 = _objc_msgSend(self, "view");
    v104 = objc_retainAutoreleasedReturnValue(v21);
    v22 = _objc_msgSend(v104, "window");
    v105 = objc_retainAutoreleasedReturnValue(v22);
    v23 = _objc_msgSend(v105, "firstResponder");
    v24 = (HXMarkTableView *)objc_retainAutoreleasedReturnValue(v23);
    v25 = (HXFrozenTableView *)-[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
    v101 = objc_retainAutoreleasedReturnValue(v25);
    v26 = (HXMarkTableView *)-[HXFrozenTableView rightTableView](v101, "rightTableView");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v107 = v24;
    v103 = v28;
    v93 = v19;
    if ( v24 == v27 )
    {
      v43 = 1;
      v44 = v104;
      v45 = v105;
    }
    else
    {
      v94 = v27;
      v29 = _objc_msgSend(self, "view");
      v95 = objc_retainAutoreleasedReturnValue(v29);
      v30 = _objc_msgSend(v95, "window");
      v96 = objc_retainAutoreleasedReturnValue(v30);
      v31 = _objc_msgSend(v96, "firstResponder");
      v32 = (HXMarkTableView *)objc_retainAutoreleasedReturnValue(v31);
      v33 = (HXFrozenTableView *)-[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
      v98 = objc_retainAutoreleasedReturnValue(v33);
      v34 = (HXMarkTableView *)-[HXFrozenTableView leftTableView](v98, "leftTableView");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v97 = v32;
      if ( v32 == v35 )
      {
        v43 = 1;
      }
      else
      {
        v36 = _objc_msgSend(self, "view");
        v99 = objc_retainAutoreleasedReturnValue(v36);
        v37 = _objc_msgSend(v99, "window");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        v39 = _objc_msgSend(v38, "firstResponder");
        v40 = (HXBaseTableView *)objc_retainAutoreleasedReturnValue(v39);
        v41 = (HXBaseTableView *)-[HXBaseTableViewController myTable](self, "myTable");
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v43 = v40 == v42;
      }
      v44 = v104;
      v45 = v105;
    }
    if ( v102 )
    {
      if ( v46 )
      {
        if ( v43 )
        {
          v47 = -[QuoteBaseTableViewController refreshRightViewBlock](self, "refreshRightViewBlock");
          v48 = objc_retainAutoreleasedReturnValue(v47);
          if ( v48 )
          {
            v111[0] = CFSTR("PanKouLinkCode");
            v112[0] = v49;
            v111[1] = CFSTR("PanKouModularType");
            v112[1] = v102;
            v50 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v112, v111, 2LL);
            v51 = objc_retainAutoreleasedReturnValue(v50);
            v52 = -[QuoteBaseTableViewController refreshRightViewBlock](self, "refreshRightViewBlock");
            v53 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v52);
            v53[2](v53, v51);
          }
        }
      }
    }
    v54 = self;
    v55 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
    v56 = objc_retainAutoreleasedReturnValue(v55);
    _objc_msgSend(v56, "removeAllObjects");
    v57 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
    v58 = objc_retainAutoreleasedReturnValue(v57);
    v59 = _objc_msgSend(v58, "firstIndex");
    if ( v59 != (id)0x7FFFFFFFFFFFFFFFLL )
    {
      do
      {
        v61 = -[QuoteBaseTableViewController begin](v54, "begin");
        v108 = v59;
        v62 = _objc_msgSend(v100, "thsDictionaryAtIndex:", (_BYTE *)v59 - v61);
        v63 = objc_retainAutoreleasedReturnValue(v62);
        v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
        v65 = objc_retainAutoreleasedReturnValue(v64);
        v66 = _objc_msgSend(v63, "thsStringForKey:", v65);
        objc_retainAutoreleasedReturnValue(v66);
        v54 = self;
        if ( _objc_msgSend(v67, "length") )
        {
          v68 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
          v69 = objc_retainAutoreleasedReturnValue(v68);
          _objc_msgSend(v69, "addObject:", v70);
        }
        v71 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
        v72 = objc_retainAutoreleasedReturnValue(v71);
        v73 = _objc_msgSend(v72, "indexGreaterThanIndex:", v108);
        v59 = v73;
      }
      while ( v73 != (id)0x7FFFFFFFFFFFFFFFLL );
    }
    v75 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](v54, "selfStockCodesToBeAddMArr");
    v76 = objc_retainAutoreleasedReturnValue(v75);
    v77 = _objc_msgSend(v76, "count");
    -[HXBaseTableViewController setIsBatchAddOperation:](v54, "setIsBatchAddOperation:", (unsigned __int64)v77 > 1);
    v78 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v103);
    v79 = objc_retainAutoreleasedReturnValue(v78);
    v80 = v79;
    if ( v81 )
    {
      if ( v79 )
      {
        v82 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
        v83 = objc_retainAutoreleasedReturnValue(v82);
        if ( v83 )
        {
          -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v84);
          v110[0] = v80;
          v110[1] = v103;
          v85 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
          v109 = objc_retainAutoreleasedReturnValue(v85);
          v110[2] = v109;
          v87 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v110, v86, 3LL);
          objc_retainAutoreleasedReturnValue(v87);
          v88 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
          v89 = objc_retainAutoreleasedReturnValue(v88);
          -[HXFrozenTableView setParamsDic:](v89, "setParamsDic:", v90);
        }
      }
    }
  }
}

//----- (00000001001843AA) ----------------------------------------------------
id __cdecl -[XinSanBanBaseTableViewController frozenTableView:menuForHeaderOfTableColumn:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  return 0LL;
}

//----- (00000001001843B2) ----------------------------------------------------
id __cdecl -[XinSanBanBaseTableViewController frozenTableView:dataCellForTableColumn:row:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXStockModel *v5; // rax
  HXStockModel *v6; // r15
  NSArray *v7; // rax
  NSArray *v8; // rbx
  NSDictionary *v14; // rax
  NSDictionary *v15; // rbx
  bool v24; // zf
  NSArray *v26; // rax
  NSArray *v27; // r15
  NSNumber *v32; // rax
  NSNumber *v33; // rbx
  HXStockModel *v56; // rax
  HXStockModel *v57; // r13
  NSArray *v58; // rax
  NSArray *v59; // r14
  NSNumber *v62; // rax
  NSNumber *v63; // rbx
  TextMarkManager *v66; // rax
  TextMarkManager *v67; // rbx
  NSString *v77; // rax
  NSString *v78; // rbx
  HXStockModel *v81; // rax
  signed __int64 v85; // r13
  HXStockModel *v86; // rax
  HXStockModel *v87; // r14
  NSArray *v88; // rax
  NSArray *v89; // rbx
  SEL v95; // [rsp+28h] [rbp-38h]

  v92 = objc_retain(a4);
  v5 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXStockModel mainStockTableViewDataArray](v6, "mainStockTableViewDataArray");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "count");
  if ( v9 )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___HXTableColumn, "class");
    if ( (unsigned __int8)_objc_msgSend(v92, "isKindOfClass:", v10) )
    {
      v11 = objc_retain(v92);
      if ( _objc_msgSend(v11, "tableHeaderItemType") )
      {
        if ( _objc_msgSend(v12, "tableHeaderItemType") != (id)1 )
          goto LABEL_10;
        v14 = -[QuoteBaseTableViewController iWenCaiItemParams](self, "iWenCaiItemParams");
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v16 = _objc_msgSend(v15, "thsArrayForKey:", CFSTR("IWenCaiIdentifier"));
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v19 = _objc_msgSend(v18, "identifier");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        _objc_msgSend(v17, "containsObject:", v20);
        v22 = v21;
        v24 = v23 == 0;
        v13 = v22;
        if ( v24 )
          goto LABEL_10;
      }
      else
      {
        v26 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v29 = _objc_msgSend(v28, "identifier");
        v30 = objc_retainAutoreleasedReturnValue(v29);
        v31 = _objc_msgSend(v30, "integerValue");
        v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v31);
        v33 = objc_retainAutoreleasedReturnValue(v32);
        v97 = (unsigned __int8)_objc_msgSend(v27, "containsObject:", v33);
        v35 = v34;
        v36 = _objc_msgSend(v34, "identifier");
        v37 = objc_retainAutoreleasedReturnValue(v36);
        v38 = _objc_msgSend(v37, "integerValue");
        if ( v97 )
        {
          v13 = v35;
        }
        else
        {
          v13 = v35;
          if ( v38 != (id)12345670 )
          {
LABEL_10:
            v25 = 0LL;
LABEL_28:
            goto LABEL_29;
          }
        }
      }
      v39 = _objc_msgSend(v13, "dataCell");
      v40 = objc_retainAutoreleasedReturnValue(v39);
      v42 = _objc_msgSend(v41, "headerCell");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v44 = _objc_msgSend(v43, "alignment");
      v98 = v40;
      _objc_msgSend(v40, "setTextAlignment:", v44);
      v45 = (char *)-[QuoteBaseTableViewController begin](self, "begin") - 50;
      v46 = -[QuoteBaseTableViewController begin](self, "begin");
      v47 = -[QuoteBaseTableViewController count](self, "count");
      if ( (__int64)v45 > a5 || (__int64)v47 + (_QWORD)v46 + 50 <= a5 )
      {
        v74 = _objc_msgSend(v48, "identifier");
        v75 = objc_retainAutoreleasedReturnValue(v74);
        v76 = _objc_msgSend(v75, "integerValue");
        if ( v76 == (id)12345670 )
        {
          v77 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), a5 + 1);
          v78 = objc_retainAutoreleasedReturnValue(v77);
          v73 = v98;
          _objc_msgSend(v98, "setText:", v78);
          v79 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
          v80 = objc_retainAutoreleasedReturnValue(v79);
          _objc_msgSend(v98, "setTextColor:", v80);
          goto LABEL_26;
        }
      }
      else
      {
        if ( (unsigned __int64)-[QuoteBaseTableViewController begin](self, "begin") <= a5 )
        {
          v50 = -[QuoteBaseTableViewController begin](self, "begin");
          if ( (char *)-[QuoteBaseTableViewController count](self, "count") + (unsigned __int64)v50 > (char *)a5 )
          {
            if ( !_objc_msgSend(v49, "tableHeaderItemType") )
            {
              v52 = _objc_msgSend(v51, "identifier");
              v53 = objc_retainAutoreleasedReturnValue(v52);
              v54 = _objc_msgSend(v53, "integerValue");
              v95 = v55;
              if ( v54 == (id)20190901 )
              {
                v56 = -[HXBaseTableViewController stockModel](self, "stockModel");
                v57 = objc_retainAutoreleasedReturnValue(v56);
                v58 = -[HXStockModel mainStockTableViewDataArray](v57, "mainStockTableViewDataArray");
                v59 = objc_retainAutoreleasedReturnValue(v58);
                v60 = -[QuoteBaseTableViewController begin](self, "begin");
                v61 = _objc_msgSend(v59, "thsDictionaryAtIndex:", a5 - (_QWORD)v60);
                v94 = objc_retainAutoreleasedReturnValue(v61);
                v62 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
                v63 = objc_retainAutoreleasedReturnValue(v62);
                v65 = _objc_msgSend(v64, "thsStringForKey:", v63);
                objc_retainAutoreleasedReturnValue(v65);
                v66 = +[TextMarkManager sharedInstance](&OBJC_CLASS___TextMarkManager, "sharedInstance");
                v67 = objc_retainAutoreleasedReturnValue(v66);
                v69 = -[TextMarkManager getPersonalLabelArray:](v67, "getPersonalLabelArray:", v68);
                v70 = objc_retainAutoreleasedReturnValue(v69);
                v71 = objc_alloc((Class)&OBJC_CLASS___TextMarkTableCell);
                v25 = _objc_msgSend(v71, "initWithLabelData:", v70);
                v73 = v98;
LABEL_27:
                goto LABEL_28;
              }
              v85 = a5 - (_QWORD)-[QuoteBaseTableViewController begin](self, "begin");
              v86 = -[HXBaseTableViewController stockModel](self, "stockModel");
              v87 = objc_retainAutoreleasedReturnValue(v86);
              v88 = -[HXStockModel mainStockTableViewDataArray](v87, "mainStockTableViewDataArray");
              v89 = objc_retainAutoreleasedReturnValue(v88);
              _objc_msgSend(
                v90,
                "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
                v98,
                v95,
                a5,
                v85,
                v89);
            }
            v73 = v98;
LABEL_26:
            v25 = objc_retain(v73);
            goto LABEL_27;
          }
        }
        if ( !_objc_msgSend(v49, "tableHeaderItemType") )
        {
          v81 = -[HXBaseTableViewController stockModel](self, "stockModel");
          v96 = objc_retainAutoreleasedReturnValue(v81);
          v82 = _objc_msgSend(v96, "backgroundData");
          v83 = objc_retainAutoreleasedReturnValue(v82);
          v73 = v98;
          -[HXBaseTableViewController reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:](
            self,
            "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
            v98,
            v84,
            a5,
            a5,
            v83);
          goto LABEL_26;
        }
      }
      v73 = v98;
      _objc_msgSend(v98, "setText:", &charsToLeaveEscaped);
      goto LABEL_26;
    }
  }
  v25 = 0LL;
LABEL_29:
  return objc_autoreleaseReturnValue(v25);
}

//----- (0000000100184ADA) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController frozenTableView:didDragTableColumn:toIndex:](
        XinSanBanBaseTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXTableManager *v6; // rax
  HXTableManager *v7; // r15

  v5 = objc_retain(a4);
  v6 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "tableKey");
  objc_retainAutoreleasedReturnValue(v9);
  v10 = _objc_msgSend(v5, "identifier");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[HXTableManager moveEntityOfSelectedScheme:identifier:toIndex:](
    v7,
    "moveEntityOfSelectedScheme:identifier:toIndex:",
    v12,
    v11,
    a5);
}

//----- (0000000100184B9D) ----------------------------------------------------
id __cdecl -[XinSanBanBaseTableViewController tableKey](XinSanBanBaseTableViewController *self, SEL a2)
{

  begin = (void *)self->super._begin;
  if ( !begin )
  {
    self->super._begin = (unsigned __int64)CFSTR("__xinSanBanTableKey");
    begin = (void *)self->super._begin;
  }
  return objc_retainAutoreleaseReturnValue(begin);
}

//----- (0000000100184BD5) ----------------------------------------------------
signed __int64 __cdecl -[XinSanBanBaseTableViewController frozenCount](XinSanBanBaseTableViewController *self, SEL a2)
{
  return 3LL;
}

//----- (0000000100184BE0) ----------------------------------------------------
void __cdecl -[XinSanBanBaseTableViewController .cxx_destruct](XinSanBanBaseTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._begin, 0LL);
}

