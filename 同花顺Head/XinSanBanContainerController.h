//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXBaseViewController.h"

@class AGuTuiShiTableViewController, BGuTuiShiTableViewController, ChuangXinCengTableViewController, GuZhuanIntegratedScreenViewController, HXBaseView, HXTabbarController, JiChuCengTableViewController, NSArray, ScrollNavigationBarViewController, ShouRiGuaPaiTableViewController, XieYiZhuanRangChuangXinCengTableViewController, XieYiZhuanRangJiChuCengTableViewController, XieYiZhuanRangTableViewController, XinSanBanZhiShuTableViewController, YouXianGuTableViewController, ZuoShiZhuanRangChuangXinCengTableViewController, ZuoShiZhuanRangJiChuCengTableViewController, ZuoShiZhuanRangTableViewController;

@interface XinSanBanContainerController : HXBaseViewController
{
    BOOL _guZhuanIntegratedScreenSelected;
    HXBaseView *_topView;
    HXBaseView *_viewForTable;
    CDUnknownBlockType _refreshRightViewBlock;
    CDUnknownBlockType _showAndHideRightBtnBlock;
    HXTabbarController *_xinSanBanTabbarController;
    id _currentTableVC;
    ChuangXinCengTableViewController *_chuangXinCengTableVC;
    JiChuCengTableViewController *_jiChuCengTableVC;
    ZuoShiZhuanRangTableViewController *_zuoShiZhuanRangTableVC;
    XieYiZhuanRangTableViewController *_xieYiZhuanRangTableVC;
    YouXianGuTableViewController *_youXianGuTableVC;
    ShouRiGuaPaiTableViewController *_shouFaGuaPaiTableVC;
    ShouRiGuaPaiTableViewController *_zengFaGuaPaiTableVC;
    AGuTuiShiTableViewController *_aGuTuiShiTableVC;
    BGuTuiShiTableViewController *_bGuTuiShiTableVC;
    XinSanBanZhiShuTableViewController *_xinSanBanZhiShuTableVC;
    GuZhuanIntegratedScreenViewController *_guZhuanIntegratedScreenVC;
    ZuoShiZhuanRangChuangXinCengTableViewController *_zuoShiZhuanRangChuangXinCengVC;
    ZuoShiZhuanRangJiChuCengTableViewController *_zuoShiZhuanRangJiChuCengVC;
    XieYiZhuanRangChuangXinCengTableViewController *_xieYiZhuanRangChuangXinCengVC;
    XieYiZhuanRangJiChuCengTableViewController *_xieYiZhuanRangJiChuCengVC;
    NSArray *_toolBarItems;
    ScrollNavigationBarViewController *_navigationBarVC;
}


@property(retain, nonatomic) ScrollNavigationBarViewController *navigationBarVC; // @synthesize navigationBarVC=_navigationBarVC;
@property(retain, nonatomic) NSArray *toolBarItems; // @synthesize toolBarItems=_toolBarItems;
@property(retain, nonatomic) XieYiZhuanRangJiChuCengTableViewController *xieYiZhuanRangJiChuCengVC; // @synthesize xieYiZhuanRangJiChuCengVC=_xieYiZhuanRangJiChuCengVC;
@property(retain, nonatomic) XieYiZhuanRangChuangXinCengTableViewController *xieYiZhuanRangChuangXinCengVC; // @synthesize xieYiZhuanRangChuangXinCengVC=_xieYiZhuanRangChuangXinCengVC;
@property(retain, nonatomic) ZuoShiZhuanRangJiChuCengTableViewController *zuoShiZhuanRangJiChuCengVC; // @synthesize zuoShiZhuanRangJiChuCengVC=_zuoShiZhuanRangJiChuCengVC;
@property(retain, nonatomic) ZuoShiZhuanRangChuangXinCengTableViewController *zuoShiZhuanRangChuangXinCengVC; // @synthesize zuoShiZhuanRangChuangXinCengVC=_zuoShiZhuanRangChuangXinCengVC;
@property(retain, nonatomic) GuZhuanIntegratedScreenViewController *guZhuanIntegratedScreenVC; // @synthesize guZhuanIntegratedScreenVC=_guZhuanIntegratedScreenVC;
@property(retain, nonatomic) XinSanBanZhiShuTableViewController *xinSanBanZhiShuTableVC; // @synthesize xinSanBanZhiShuTableVC=_xinSanBanZhiShuTableVC;
@property(retain, nonatomic) BGuTuiShiTableViewController *bGuTuiShiTableVC; // @synthesize bGuTuiShiTableVC=_bGuTuiShiTableVC;
@property(retain, nonatomic) AGuTuiShiTableViewController *aGuTuiShiTableVC; // @synthesize aGuTuiShiTableVC=_aGuTuiShiTableVC;
@property(retain, nonatomic) ShouRiGuaPaiTableViewController *zengFaGuaPaiTableVC; // @synthesize zengFaGuaPaiTableVC=_zengFaGuaPaiTableVC;
@property(retain, nonatomic) ShouRiGuaPaiTableViewController *shouFaGuaPaiTableVC; // @synthesize shouFaGuaPaiTableVC=_shouFaGuaPaiTableVC;
@property(retain, nonatomic) YouXianGuTableViewController *youXianGuTableVC; // @synthesize youXianGuTableVC=_youXianGuTableVC;
@property(retain, nonatomic) XieYiZhuanRangTableViewController *xieYiZhuanRangTableVC; // @synthesize xieYiZhuanRangTableVC=_xieYiZhuanRangTableVC;
@property(retain, nonatomic) ZuoShiZhuanRangTableViewController *zuoShiZhuanRangTableVC; // @synthesize zuoShiZhuanRangTableVC=_zuoShiZhuanRangTableVC;
@property(retain, nonatomic) JiChuCengTableViewController *jiChuCengTableVC; // @synthesize jiChuCengTableVC=_jiChuCengTableVC;
@property(retain, nonatomic) ChuangXinCengTableViewController *chuangXinCengTableVC; // @synthesize chuangXinCengTableVC=_chuangXinCengTableVC;
@property(retain, nonatomic) id currentTableVC; // @synthesize currentTableVC=_currentTableVC;
@property(retain, nonatomic) HXTabbarController *xinSanBanTabbarController; // @synthesize xinSanBanTabbarController=_xinSanBanTabbarController;
@property(nonatomic) BOOL guZhuanIntegratedScreenSelected; // @synthesize guZhuanIntegratedScreenSelected=_guZhuanIntegratedScreenSelected;
@property(copy, nonatomic) CDUnknownBlockType showAndHideRightBtnBlock; // @synthesize showAndHideRightBtnBlock=_showAndHideRightBtnBlock;
@property(copy, nonatomic) CDUnknownBlockType refreshRightViewBlock; // @synthesize refreshRightViewBlock=_refreshRightViewBlock;
@property __weak HXBaseView *viewForTable; // @synthesize viewForTable=_viewForTable;
@property __weak HXBaseView *topView; // @synthesize topView=_topView;
- (void)sendChangeTableUserLog:(id)arg1;
- (double)widthOfString:(id)arg1 withFont:(id)arg2;
- (void)setSelectedItem:(id)arg1;
- (void)refreshAllModules;
- (void)twoBtnMenuClicked:(id)arg1;
- (void)twoBtnClicked:(id)arg1;
- (void)normalBtnClicked:(id)arg1;
- (void)keyDownFromSuper:(id)arg1;
- (void)setErJiMenuState;
- (void)initToolBarItems;
- (void)initViewForTable;
- (void)initObjects;
- (void)pageShowBtnClicked;
- (void)viewDidLoad;

@end

