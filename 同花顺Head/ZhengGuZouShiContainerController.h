//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "FenshiKlineTrendChartContainerController.h"

#import "ReceiveDispatchData-Protocol.h"

@class NSString, QuoteBaseItem, ZhengGuHangQingView;

@interface ZhengGuZouShiContainerController : FenshiKlineTrendChartContainerController <ReceiveDispatchData>
{
    ZhengGuHangQingView *_zhengGuHangQingView;
    QuoteBaseItem *_quoteItem;
    NSString *_singleStockCode;
    NSString *_singleMarket;
    NSString *_dapanStockCode;
    NSString *_dapanMarket;
}


@property(retain, nonatomic) NSString *dapanMarket; // @synthesize dapanMarket=_dapanMarket;
@property(retain, nonatomic) NSString *dapanStockCode; // @synthesize dapanStockCode=_dapanStockCode;
@property(retain, nonatomic) NSString *singleMarket; // @synthesize singleMarket=_singleMarket;
@property(retain, nonatomic) NSString *singleStockCode; // @synthesize singleStockCode=_singleStockCode;
@property(retain, nonatomic) QuoteBaseItem *quoteItem; // @synthesize quoteItem=_quoteItem;
@property __weak ZhengGuHangQingView *zhengGuHangQingView; // @synthesize zhengGuHangQingView=_zhengGuHangQingView;
- (void)frameDidChange:(id)arg1;
- (void)addNotifications;
- (void)setViewStates;
- (void)updateForPush:(id)arg1;
- (void)updateForRequest:(id)arg1;
- (void)receiveRealTimeData:(id)arg1;
- (void)deleteOrderForHangQing;
- (void)orderForHangQing;
- (void)requestForHangQingData:(id)arg1 market:(id)arg2;
- (void)setSubclassModularMode;
- (void)sendZhengGuRequset:(id)arg1;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
- (void)dealloc;
- (void)viewDidDisappear;
- (void)viewDidLoad;
- (id)initWithNibName:(id)arg1 bundle:(id)arg2;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

