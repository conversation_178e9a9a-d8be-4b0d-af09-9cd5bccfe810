void __cdecl -[XinSanBanIndexPageContainerController viewDidLoad](XinSanBanIndexPageContainerController *self, SEL a2)
{
  HXPageMainGraphTabContainer *v2; // rax
  HXPageMainGraphTabContainer *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  _QWORD v7[2]; // [rsp+10h] [rbp-30h] BYREF

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___XinSanBanIndexPageContainerController;
  -[HXPageBaseViewController viewDidLoad](&v6, "viewDidLoad");
  v2 = -[HXPageBaseViewController mainGraphNavigationVC](self, "mainGraphNavigationVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v7[0] = CFSTR("个股资料");
  v7[1] = CFSTR("诊股");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v7, 2LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXPageMainGraphTabContainer disable:forTitles:](v3, "disable:forTitles:", 1LL, v5);
}

//----- (0000000100A56458) ----------------------------------------------------
id __cdecl -[XinSanBanIndexPageContainerController nibName](XinSanBanIndexPageContainerController *self, SEL a2)
{
  return CFSTR("HXPageBaseViewController");
}

//----- (0000000100A56465) ----------------------------------------------------
void __cdecl -[XinSanBanIndexPageContainerController updatePageControllerView](
        XinSanBanIndexPageContainerController *self,
        SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx

  v2 = -[HXBaseViewController market](self, "market");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "length");
  if ( v4 )
  {
    v5.receiver = self;
    v5.super_class = (Class)&OBJC_CLASS___XinSanBanIndexPageContainerController;
    -[HXPageBaseViewController updatePageControllerView](&v5, "updatePageControllerView");
  }
}

//----- (0000000100A564D8) ----------------------------------------------------
void __cdecl -[XinSanBanIndexPageContainerController actionForSelfStockUpdate](
        XinSanBanIndexPageContainerController *self,
        SEL a2)
{
  ;
}

//----- (0000000100A564DE) ----------------------------------------------------
id __cdecl -[XinSanBanIndexPageContainerController splitBottomViewHeightName](
        XinSanBanIndexPageContainerController *self,
        SEL a2)
{
  return CFSTR("splitBottomViewHeightXinSanBanIndex");
}

//----- (0000000100A564EB) ----------------------------------------------------
id __cdecl -[XinSanBanIndexPageContainerController splitRightViewWidthName](
        XinSanBanIndexPageContainerController *self,
        SEL a2)
{
  return CFSTR("splitRightViewWidthXinSanBanIndex");
}

//----- (0000000100A564F8) ----------------------------------------------------
id __cdecl -[XinSanBanIndexPageContainerController strGeGuPankouType](
        XinSanBanIndexPageContainerController *self,
        SEL a2)
{
  return CFSTR("XinSanBanIndexPanKou");
}

//----- (0000000100A56505) ----------------------------------------------------
void __cdecl -[XinSanBanIndexPageContainerController F1KeyDown](XinSanBanIndexPageContainerController *self, SEL a2)
{
  ;
}

