//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXBaseViewController.h"

#import "WebFrameLoadDelegate-Protocol.h"
#import "WebPolicyDecisionListener-Protocol.h"
#import "WebPolicyDelegate-Protocol.h"
#import "WebUIDelegate-Protocol.h"

@class NSString, WebView;

@interface ZiXunWebViewController : HXBaseViewController <WebPolicyDecisionListener, WebPolicyDelegate, WebUIDelegate, WebFrameLoadDelegate>
{
    NSString *_initialUrlString;
    WebView *_webView;
}


@property __weak WebView *webView; // @synthesize webView=_webView;
@property(retain, nonatomic) NSString *initialUrlString; // @synthesize initialUrlString=_initialUrlString;
- (void)webView:(id)arg1 didCreateJavaScriptContext:(id)arg2 forFrame:(id)arg3;
- (id)webView:(id)arg1 createWebViewWithRequest:(id)arg2;
- (void)webView:(id)arg1 decidePolicyForNewWindowAction:(id)arg2 request:(id)arg3 newFrameName:(id)arg4 decisionListener:(id)arg5;
- (void)ignore;
- (void)download;
- (void)use;
- (void)setDebugEnabled;
- (void)loadUrl:(id)arg1;
- (void)dealloc;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

