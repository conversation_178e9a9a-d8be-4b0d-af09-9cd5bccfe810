aNoeZQVOSoPboiub *__cdecl -[aNoeZQVOSoPboiub init](aNoeZQVOSoPboiub *self, SEL a2)
{
  aNoeZQVOSoPboiub *v2; // rax
  aNoeZQVOSoPboiub *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___aNoeZQVOSoPboiub;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
    -[aNoeZQVOSoPboiub reseTradeLoginModel](v2, "reseTradeLoginModel");
  return v3;
}

//----- (0000000100C3074C) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub reseTradeLoginModel](aNoeZQVOSoPboiub *self, SEL a2)
{
  -[aNoeZQVOSoPboiub setStrUserid:](self, "setStrUserid:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrBroker:](self, "setStrBroker:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrUniqueQsid:](self, "setStrUniqueQsid:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrServer:](self, "setStrServer:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrAccount:](self, "setStrAccount:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrAccType:](self, "setStrAccType:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrTradePwd:](self, "setStrTradePwd:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrCommPwd:](self, "setStrCommPwd:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrCrypt:](self, "setStrCrypt:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrYybid:](self, "setStrYybid:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrDefCommPWD:](self, "setStrDefCommPWD:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrMacLoginVersion:](self, "setStrMacLoginVersion:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrMacVersion:](self, "setStrMacVersion:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setBHTMode:](self, "setBHTMode:", 0LL);
  -[aNoeZQVOSoPboiub setBIsMeigu:](self, "setBIsMeigu:", 0LL);
  -[aNoeZQVOSoPboiub setBIsAutoReg:](self, "setBIsAutoReg:", 0LL);
  -[aNoeZQVOSoPboiub setStrThsName:](self, "setStrThsName:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrThsPwd:](self, "setStrThsPwd:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setStrKernelver:](self, "setStrKernelver:", &charsToLeaveEscaped);
  -[aNoeZQVOSoPboiub setWtLinkMode:](self, "setWtLinkMode:", -1LL);
}

//----- (0000000100C308B1) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strLinkMode](aNoeZQVOSoPboiub *self, SEL a2)
{
  unsigned __int64 wtLinkMode; // rcx
  NSString *v4; // rax
  NSString *v5; // rdi

  wtLinkMode = self->_wtLinkMode;
  if ( wtLinkMode == -2LL )
    return (NSString *)objc_autoreleaseReturnValue(CFSTR("raddom"));
  if ( wtLinkMode > 0x7F )
    return (NSString *)objc_autoreleaseReturnValue(CFSTR("fast"));
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("assign=%ld"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return (NSString *)objc_autoreleaseReturnValue(v5);
}

//----- (0000000100C30908) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strUserid](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (0000000100C30919) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrUserid:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (0000000100C30928) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strBroker](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (0000000100C30939) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrBroker:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (0000000100C30948) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strUniqueQsid](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100C30959) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrUniqueQsid:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (0000000100C30968) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strServer](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100C30979) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrServer:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100C30988) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strAccount](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 48LL, 0);
}

//----- (0000000100C30999) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrAccount:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 48LL);
}

//----- (0000000100C309A8) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strAccType](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 56LL, 0);
}

//----- (0000000100C309B9) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrAccType:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 56LL);
}

//----- (0000000100C309C8) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strTradePwd](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 64LL, 0);
}

//----- (0000000100C309D9) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrTradePwd:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 64LL);
}

//----- (0000000100C309E8) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strCommPwd](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 72LL, 0);
}

//----- (0000000100C309F9) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrCommPwd:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 72LL);
}

//----- (0000000100C30A08) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strCrypt](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 80LL, 0);
}

//----- (0000000100C30A19) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrCrypt:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 80LL);
}

//----- (0000000100C30A28) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strYybid](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 88LL, 0);
}

//----- (0000000100C30A39) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrYybid:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (0000000100C30A48) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strDefCommPWD](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 96LL, 0);
}

//----- (0000000100C30A59) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrDefCommPWD:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (0000000100C30A68) ----------------------------------------------------
char __cdecl -[aNoeZQVOSoPboiub bHTMode](aNoeZQVOSoPboiub *self, SEL a2)
{
  return self->_bHTMode;
}

//----- (0000000100C30A72) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setBHTMode:](aNoeZQVOSoPboiub *self, SEL a2, char a3)
{
  self->_bHTMode = a3;
}

//----- (0000000100C30A7B) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strMacLoginVersion](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 104LL, 0);
}

//----- (0000000100C30A8C) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrMacLoginVersion:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 104LL);
}

//----- (0000000100C30A9B) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strMacVersion](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 112LL, 0);
}

//----- (0000000100C30AAC) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrMacVersion:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 112LL);
}

//----- (0000000100C30ABB) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strKernelver](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 120LL, 0);
}

//----- (0000000100C30ACC) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrKernelver:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 120LL);
}

//----- (0000000100C30ADB) ----------------------------------------------------
char __cdecl -[aNoeZQVOSoPboiub bIsMeigu](aNoeZQVOSoPboiub *self, SEL a2)
{
  return self->_bIsMeigu;
}

//----- (0000000100C30AE5) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setBIsMeigu:](aNoeZQVOSoPboiub *self, SEL a2, char a3)
{
  self->_bIsMeigu = a3;
}

//----- (0000000100C30AEE) ----------------------------------------------------
char __cdecl -[aNoeZQVOSoPboiub bIsAutoReg](aNoeZQVOSoPboiub *self, SEL a2)
{
  return self->_bIsAutoReg;
}

//----- (0000000100C30AF8) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setBIsAutoReg:](aNoeZQVOSoPboiub *self, SEL a2, char a3)
{
  self->_bIsAutoReg = a3;
}

//----- (0000000100C30B01) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strThsName](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 128LL, 0);
}

//----- (0000000100C30B12) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrThsName:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 128LL);
}

//----- (0000000100C30B21) ----------------------------------------------------
NSString *__cdecl -[aNoeZQVOSoPboiub strThsPwd](aNoeZQVOSoPboiub *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 136LL, 0);
}

//----- (0000000100C30B32) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setStrThsPwd:](aNoeZQVOSoPboiub *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 136LL);
}

//----- (0000000100C30B41) ----------------------------------------------------
signed __int64 __cdecl -[aNoeZQVOSoPboiub wtLinkMode](aNoeZQVOSoPboiub *self, SEL a2)
{
  return self->_wtLinkMode;
}

//----- (0000000100C30B4E) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub setWtLinkMode:](aNoeZQVOSoPboiub *self, SEL a2, signed __int64 a3)
{
  self->_wtLinkMode = a3;
}

//----- (0000000100C30B5B) ----------------------------------------------------
void __cdecl -[aNoeZQVOSoPboiub .cxx_destruct](aNoeZQVOSoPboiub *self, SEL a2)
{
  objc_storeStrong((id *)&self->_strThsPwd, 0LL);
  objc_storeStrong((id *)&self->_strThsName, 0LL);
  objc_storeStrong((id *)&self->_strKernelver, 0LL);
  objc_storeStrong((id *)&self->_strMacVersion, 0LL);
  objc_storeStrong((id *)&self->_strMacLoginVersion, 0LL);
  objc_storeStrong((id *)&self->_strDefCommPWD, 0LL);
  objc_storeStrong((id *)&self->_strYybid, 0LL);
  objc_storeStrong((id *)&self->_strCrypt, 0LL);
  objc_storeStrong((id *)&self->_strCommPwd, 0LL);
  objc_storeStrong((id *)&self->_strTradePwd, 0LL);
  objc_storeStrong((id *)&self->_strAccType, 0LL);
  objc_storeStrong((id *)&self->_strAccount, 0LL);
  objc_storeStrong((id *)&self->_strServer, 0LL);
  objc_storeStrong((id *)&self->_strUniqueQsid, 0LL);
  objc_storeStrong((id *)&self->_strBroker, 0LL);
  objc_storeStrong((id *)&self->_strUserid, 0LL);
}

