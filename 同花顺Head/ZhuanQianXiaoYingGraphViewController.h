//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "GraphBaseViewController.h"

@class HXBaseView;

@interface ZhuanQianXiaoYingGraphViewController : GraphBaseViewController
{
    HXBaseView *_topView;
}


@property __weak HXBaseView *topView; // @synthesize topView=_topView;
- (id)createDataForPlot;
- (void)requestData;
- (id)view:(id)arg1 stringForToolTip:(long long)arg2 point:(struct CGPoint)arg3 userData:(void *)arg4;
- (void)frameDidChaged:(id)arg1;
- (void)dealloc;
- (void)viewDidLoad;

@end

