//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSString;

@interface zhuanzhangParam : NSObject
{
    NSString *_strYhdm;
    NSString *_strHbdm;
    NSString *_strZzje;
    NSString *_strZjmm;
    NSString *_strYhmm;
    NSString *_strYhzh;
    long long _nReqType;
}


@property(nonatomic) long long nReqType; // @synthesize nReqType=_nReqType;
@property(copy, nonatomic) NSString *strYhzh; // @synthesize strYhzh=_strYhzh;
@property(copy, nonatomic) NSString *strYhmm; // @synthesize strYhmm=_strYhmm;
@property(copy, nonatomic) NSString *strZjmm; // @synthesize strZjmm=_strZjmm;
@property(copy, nonatomic) NSString *strZzje; // @synthesize strZzje=_strZzje;
@property(copy, nonatomic) NSString *strHbdm; // @synthesize strHbdm=_strHbdm;
@property(copy, nonatomic) NSString *strYhdm; // @synthesize strYhdm=_strYhdm;
- (void)resetData;
- (id)init;

@end

