void __cdecl -[XinSanBanContainerController viewDidLoad](XinSanBanContainerController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XinSanBanContainerController;
  -[HXBaseViewController viewDidLoad](&v2, "viewDidLoad");
  -[XinSanBanContainerController initObjects](self, "initObjects");
  -[XinSanBanContainerController initViewForTable](self, "initViewForTable");
  -[XinSanBanContainerController initToolBarItems](self, "initToolBarItems");
  -[XinSanBanContainerController setErJiMenuState](self, "setErJiMenuState");
  -[XinSanBanContainerController pageShowBtnClicked](self, "pageShowBtnClicked");
}

//----- (00000001000B63EF) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController pageShowBtnClicked](XinSanBanContainerController *self, SEL a2)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  NSArray *v6; // rdi
  SEL v10; // r12
  HXTabbarController *v11; // rax
  HXTabbarController *v12; // rbx
  HXTabbarController *v14; // rax
  HXTabbarController *v15; // rax

  v3 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "firstObject");
  v6 = v4;
  v7 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v8) )
  {
    v19 = v7;
    v9 = objc_retain(v7);
    _objc_msgSend(self, v10, v9);
    _objc_msgSend(v9, "tag");
    v11 = -[XinSanBanContainerController xinSanBanTabbarController](self, "xinSanBanTabbarController");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    -[HXTabbarController setSelectedIndex:](v12, "setSelectedIndex:", v13);
    v14 = -[XinSanBanContainerController xinSanBanTabbarController](self, "xinSanBanTabbarController");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = -[HXTabbarController selectedViewController](v15, "selectedViewController");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    -[XinSanBanContainerController setCurrentTableVC:](self, "setCurrentTableVC:", v17);
    v7 = v19;
  }
}

//----- (00000001000B653B) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController initObjects](XinSanBanContainerController *self, SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v3; // rbx
  HXBaseView *v4; // rax
  HXBaseView *v5; // rbx
  HXBaseView *v7; // rax
  HXBaseView *v8; // rbx
  HXBaseView *v13; // rax
  HXBaseView *v14; // rbx

  v2 = -[XinSanBanContainerController topView](self, "topView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseView setTopBorder:](v3, "setTopBorder:", 1LL);
  v4 = -[XinSanBanContainerController topView](self, "topView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXBaseView setBorderWidth:](v5, "setBorderWidth:", 1.0);
  v6 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v6);
  v7 = -[XinSanBanContainerController topView](self, "topView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[HXBaseView setBorderColor:](v8, "setBorderColor:", v9);
  v11 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = -[XinSanBanContainerController viewForTable](self, "viewForTable");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[HXBaseView setBackgroundColor:](v14, "setBackgroundColor:", v12);
}

//----- (00000001000B6671) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController initViewForTable](XinSanBanContainerController *self, SEL a2)
{
  ChuangXinCengTableViewController *v2; // rax
  ChuangXinCengTableViewController *v3; // rbx

  v2 = -[XinSanBanContainerController chuangXinCengTableVC](self, "chuangXinCengTableVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[XinSanBanContainerController setCurrentTableVC:](self, "setCurrentTableVC:", v3);
}

//----- (00000001000B66BD) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController initToolBarItems](XinSanBanContainerController *self, SEL a2)
{
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSDictionary *v9; // rax
  SEL v10; // r12
  NSNumber *v12; // rax
  NSDictionary *v13; // rax
  SEL v14; // r12
  NSNumber *v16; // rax
  NSNumber *v17; // rax
  NSDictionary *v18; // rax
  NSNumber *v20; // rax
  NSDictionary *v21; // rax
  NSDictionary *v24; // rax
  NSArray *v25; // rax
  NSDictionary *v26; // rax
  NSNumber *v27; // rax
  NSNumber *v28; // rax
  NSNumber *v29; // rax
  NSDictionary *v30; // rax
  NSNumber *v33; // rax
  NSDictionary *v34; // rax
  NSNumber *v35; // rax
  NSDictionary *v37; // rax
  NSArray *v38; // rax
  NSDictionary *v39; // rax
  NSNumber *v42; // rax
  NSDictionary *v43; // rax
  NSNumber *v44; // rax
  NSNumber *v47; // rax
  NSDictionary *v48; // rax
  NSNumber *v49; // rax
  NSNumber *v50; // rax
  SEL v51; // r12
  NSNumber *v53; // rax
  NSNumber *v54; // rax
  NSDictionary *v55; // rax
  NSNumber *v57; // rax
  NSNumber *v75; // rax
  NSNumber *v76; // rax
  NSDictionary *v77; // rax
  NSNumber *v78; // rax
  NSNumber *v79; // rax
  SEL v80; // r12
  NSNumber *v82; // rax
  NSNumber *v83; // rax
  SEL v84; // r12
  NSNumber *v86; // rax
  NSNumber *v87; // rax
  NSNumber *v88; // rax
  NSDictionary *v89; // rax
  NSNumber *v91; // rax
  NSDictionary *v92; // rax
  NSNumber *v93; // rax
  SEL v94; // r12
  NSArray *v96; // rax
  SEL v97; // r12
  NSNumber *v99; // rax
  NSNumber *v100; // rax
  NSDictionary *v108; // rax
  NSNumber *v110; // rax
  NSDictionary *v111; // rax
  NSArray *v112; // rax
  NSDictionary *v113; // rax
  NSNumber *v114; // rax
  NSNumber *v115; // rax
  NSDictionary *v116; // rax
  NSNumber *v117; // rax
  SEL v118; // r12
  NSDictionary *v120; // rax
  NSNumber *v121; // rax
  NSNumber *v122; // rax
  NSDictionary *v124; // rax
  NSNumber *v125; // rax
  NSNumber *v127; // rax
  NSDictionary *v129; // rax
  NSNumber *v130; // rax
  NSDictionary *v132; // rax
  NSArray *v133; // rax
  NSDictionary *v134; // rax
  NSNumber *v135; // rax
  NSNumber *v136; // rax
  NSNumber *v137; // r15
  NSDictionary *v139; // rax
  NSDictionary *v140; // r14
  NSArray *v141; // rax
  NSArray *v142; // rax
  SEL v167; // rax
  unsigned __int64 v178; // r12
  __CFString *v184; // rbx
  __CFString *v185; // rdi
  NSString *v187; // r14
  SEL v189; // rax
  unsigned __int64 v191; // rax
  __CFString *v223; // rbx
  __CFString *v224; // rdi
  SEL v231; // rax
  NSNumber *v238; // [rsp+158h] [rbp-AF8h]
  NSDictionary *v239; // [rsp+158h] [rbp-AF8h]
  NSDictionary *v240; // [rsp+160h] [rbp-AF0h]
  NSNumber *v241; // [rsp+160h] [rbp-AF0h]
  NSDictionary *v243; // [rsp+168h] [rbp-AE8h]
  NSDictionary *v244; // [rsp+170h] [rbp-AE0h]
  NSNumber *v245; // [rsp+170h] [rbp-AE0h]
  NSArray *v246; // [rsp+178h] [rbp-AD8h]
  NSDictionary *v248; // [rsp+180h] [rbp-AD0h]
  NSArray *v249; // [rsp+180h] [rbp-AD0h]
  NSNumber *v250; // [rsp+188h] [rbp-AC8h]
  NSNumber *v252; // [rsp+190h] [rbp-AC0h]
  NSNumber *v253; // [rsp+190h] [rbp-AC0h]
  NSNumber *v254; // [rsp+198h] [rbp-AB8h]
  NSNumber *v255; // [rsp+198h] [rbp-AB8h]
  NSDictionary *v256; // [rsp+1A0h] [rbp-AB0h]
  NSNumber *v258; // [rsp+1A8h] [rbp-AA8h]
  NSDictionary *v260; // [rsp+1B0h] [rbp-AA0h]
  NSNumber *v262; // [rsp+1B8h] [rbp-A98h]
  NSDictionary *v263; // [rsp+1B8h] [rbp-A98h]
  NSDictionary *v264; // [rsp+1C0h] [rbp-A90h]
  NSNumber *v265; // [rsp+1C0h] [rbp-A90h]
  NSArray *v266; // [rsp+1C8h] [rbp-A88h]
  NSDictionary *v267; // [rsp+1C8h] [rbp-A88h]
  NSDictionary *v268; // [rsp+1D0h] [rbp-A80h]
  NSArray *v269; // [rsp+1D0h] [rbp-A80h]
  NSDictionary *v271; // [rsp+1D8h] [rbp-A78h]
  NSNumber *v272; // [rsp+1E0h] [rbp-A70h]
  NSNumber *v273; // [rsp+1E0h] [rbp-A70h]
  NSDictionary *v274; // [rsp+1E8h] [rbp-A68h]
  NSNumber *v275; // [rsp+1E8h] [rbp-A68h]
  NSNumber *v276; // [rsp+1F0h] [rbp-A60h]
  NSDictionary *v277; // [rsp+1F0h] [rbp-A60h]
  NSNumber *v278; // [rsp+1F8h] [rbp-A58h]
  NSNumber *v279; // [rsp+1F8h] [rbp-A58h]
  NSDictionary *v280; // [rsp+200h] [rbp-A50h]
  NSNumber *v282; // [rsp+208h] [rbp-A48h]
  NSDictionary *v283; // [rsp+208h] [rbp-A48h]
  NSNumber *v284; // [rsp+210h] [rbp-A40h]
  NSNumber *v285; // [rsp+210h] [rbp-A40h]
  NSNumber *v287; // [rsp+218h] [rbp-A38h]
  NSNumber *v288; // [rsp+220h] [rbp-A30h]
  NSDictionary *v289; // [rsp+220h] [rbp-A30h]
  NSNumber *v291; // [rsp+228h] [rbp-A28h]
  NSNumber *v292; // [rsp+230h] [rbp-A20h]
  NSNumber *v293; // [rsp+230h] [rbp-A20h]
  NSDictionary *v294; // [rsp+238h] [rbp-A18h]
  NSDictionary *v295; // [rsp+238h] [rbp-A18h]
  NSNumber *v296; // [rsp+240h] [rbp-A10h]
  NSNumber *v297; // [rsp+240h] [rbp-A10h]
  NSDictionary *v299; // [rsp+248h] [rbp-A08h]
  NSArray *v301; // [rsp+250h] [rbp-A00h]
  NSDictionary *v303; // [rsp+258h] [rbp-9F8h]
  NSNumber *v305; // [rsp+260h] [rbp-9F0h]
  unsigned __int64 i; // [rsp+280h] [rbp-9D0h]
  NSDictionary *v309; // [rsp+288h] [rbp-9C8h]
  NSDictionary *v310; // [rsp+288h] [rbp-9C8h]
  __CFString *v311; // [rsp+288h] [rbp-9C8h]
  NSDictionary *v312; // [rsp+290h] [rbp-9C0h]
  NSNumber *v315; // [rsp+298h] [rbp-9B8h]
  NSNumber *v316; // [rsp+2A0h] [rbp-9B0h]
  NSNumber *v317; // [rsp+2A0h] [rbp-9B0h]
  NSDictionary *v318; // [rsp+2A8h] [rbp-9A8h]
  NSNumber *v320; // [rsp+2B0h] [rbp-9A0h]
  NSNumber *v321; // [rsp+2B0h] [rbp-9A0h]
  NSDictionary *v323; // [rsp+2B8h] [rbp-998h]
  NSNumber *v324; // [rsp+2B8h] [rbp-998h]
  NSNumber *v326; // [rsp+2C0h] [rbp-990h]
  NSNumber *v327; // [rsp+2C0h] [rbp-990h]
  NSNumber *v329; // [rsp+2C8h] [rbp-988h]
  NSNumber *v330; // [rsp+2C8h] [rbp-988h]
  __CFString *v332; // [rsp+2C8h] [rbp-988h]
  NSNumber *v335; // [rsp+2E0h] [rbp-970h]
  NSNumber *v336; // [rsp+2E0h] [rbp-970h]
  NSString *v337; // [rsp+2E0h] [rbp-970h]
  NSNumber *v338; // [rsp+2E8h] [rbp-968h]
  NSNumber *v339; // [rsp+2E8h] [rbp-968h]
  NSNumber *v341; // [rsp+2F0h] [rbp-960h]
  NSNumber *v342; // [rsp+2F0h] [rbp-960h]
  __CFString *v344; // [rsp+2F0h] [rbp-960h]
  __CFString *v345; // [rsp+2F8h] [rbp-958h]
  __CFString *v346; // [rsp+300h] [rbp-950h]
  __CFString *v347; // [rsp+318h] [rbp-938h]

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v307 = objc_retainAutoreleasedReturnValue(v2);
  v345 = off_1012E1960[0];
  v347 = off_1012E1970[0];
  v346 = off_1012E1978[0];
  for ( i = 0LL; ; ++i )
  {
    v425[0] = (__int64)CFSTR("isTwoBtn");
    v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v341 = objc_retainAutoreleasedReturnValue(v3);
    v426[0] = (__int64)v341;
    v425[1] = (__int64)v345;
    v426[1] = (__int64)CFSTR("股转综合页面");
    v425[2] = (__int64)v347;
    v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
    v329 = objc_retainAutoreleasedReturnValue(v4);
    v426[2] = (__int64)v329;
    v425[3] = (__int64)v346;
    v426[3] = (__int64)CFSTR("normalBtnClicked:");
    v5 = (NSDictionary *)_objc_msgSend(
                           &OBJC_CLASS___NSDictionary,
                           "dictionaryWithObjects:forKeys:count:",
                           v426,
                           v425,
                           4LL);
    v309 = objc_retainAutoreleasedReturnValue(v5);
    v427[0] = (__int64)v309;
    v423[0] = v6;
    v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v338 = objc_retainAutoreleasedReturnValue(v7);
    v424[0] = (__int64)v338;
    v423[1] = (__int64)v345;
    v424[1] = (__int64)CFSTR("创新层");
    v423[2] = (__int64)v347;
    v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
    v326 = objc_retainAutoreleasedReturnValue(v8);
    v424[2] = (__int64)v326;
    v423[3] = (__int64)v346;
    v424[3] = (__int64)CFSTR("normalBtnClicked:");
    v9 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v424, v423, 4LL);
    v312 = objc_retainAutoreleasedReturnValue(v9);
    v427[1] = (__int64)v312;
    v421[0] = (__int64)CFSTR("isTwoBtn");
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, v10, 0LL);
    v314 = objc_retainAutoreleasedReturnValue(v11);
    v422[0] = (__int64)v314;
    v421[1] = (__int64)v345;
    v422[1] = (__int64)CFSTR("基础层");
    v421[2] = (__int64)v347;
    v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
    v316 = objc_retainAutoreleasedReturnValue(v12);
    v422[2] = (__int64)v316;
    v421[3] = (__int64)v346;
    v422[3] = (__int64)CFSTR("normalBtnClicked:");
    v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v422, v421, 4LL);
    v318 = objc_retainAutoreleasedReturnValue(v13);
    v427[2] = (__int64)v318;
    v419[0] = (__int64)CFSTR("isTwoBtn");
    v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, v14, 1LL);
    v333 = objc_retainAutoreleasedReturnValue(v15);
    v420[0] = (__int64)v333;
    v419[1] = (__int64)v346;
    v420[1] = (__int64)CFSTR("twoBtnClicked:");
    v419[2] = (__int64)CFSTR("width");
    v16 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 90LL);
    v335 = objc_retainAutoreleasedReturnValue(v16);
    v420[2] = (__int64)v335;
    v419[3] = (__int64)CFSTR("towBtnItems");
    v416[0] = (__int64)v345;
    v417[0] = (__int64)CFSTR("做市转让");
    v416[1] = (__int64)v347;
    v17 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 3LL);
    v320 = objc_retainAutoreleasedReturnValue(v17);
    v417[1] = (__int64)v320;
    v416[2] = (__int64)v346;
    v417[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v18 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v417,
                            v416,
                            3LL);
    v323 = objc_retainAutoreleasedReturnValue(v18);
    v418[0] = (__int64)v323;
    v414[0] = v19;
    v415[0] = (__int64)CFSTR("做市转让-创新层");
    v414[1] = (__int64)v347;
    v20 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
    v238 = objc_retainAutoreleasedReturnValue(v20);
    v415[1] = (__int64)v238;
    v414[2] = (__int64)v346;
    v415[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v21 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v415,
                            v414,
                            3LL);
    v240 = objc_retainAutoreleasedReturnValue(v21);
    v418[1] = (__int64)v240;
    v412[0] = (__int64)v345;
    v413[0] = (__int64)CFSTR("做市转让-基础层");
    v412[1] = (__int64)v347;
    v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, v22, 5LL);
    v242 = objc_retainAutoreleasedReturnValue(v23);
    v413[1] = (__int64)v242;
    v412[2] = (__int64)v346;
    v413[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v24 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v413,
                            v412,
                            3LL);
    v244 = objc_retainAutoreleasedReturnValue(v24);
    v418[2] = (__int64)v244;
    v25 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v418, 3LL);
    v246 = objc_retainAutoreleasedReturnValue(v25);
    v420[3] = (__int64)v246;
    v26 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v420,
                            v419,
                            4LL);
    v248 = objc_retainAutoreleasedReturnValue(v26);
    v427[3] = (__int64)v248;
    v410[0] = (__int64)CFSTR("isTwoBtn");
    v27 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
    v250 = objc_retainAutoreleasedReturnValue(v27);
    v411[0] = (__int64)v250;
    v410[1] = (__int64)v346;
    v411[1] = (__int64)CFSTR("twoBtnClicked:");
    v410[2] = (__int64)CFSTR("width");
    v28 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 110LL);
    v252 = objc_retainAutoreleasedReturnValue(v28);
    v411[2] = (__int64)v252;
    v410[3] = (__int64)CFSTR("towBtnItems");
    v407[0] = (__int64)v345;
    v408[0] = (__int64)CFSTR("集合竞价转让");
    v407[1] = (__int64)v347;
    v29 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 6LL);
    v254 = objc_retainAutoreleasedReturnValue(v29);
    v408[1] = (__int64)v254;
    v407[2] = (__int64)v346;
    v408[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v30 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v408,
                            v407,
                            3LL);
    v256 = objc_retainAutoreleasedReturnValue(v30);
    v409[0] = (__int64)v256;
    v405[0] = v31;
    v32 = v31;
    v406[0] = (__int64)CFSTR("集合竞价转让-创新层");
    v405[1] = (__int64)v347;
    v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 7LL);
    v258 = objc_retainAutoreleasedReturnValue(v33);
    v406[1] = (__int64)v258;
    v405[2] = (__int64)v346;
    v406[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v34 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v406, v405, 3LL);
    v260 = objc_retainAutoreleasedReturnValue(v34);
    v409[1] = (__int64)v260;
    v403[0] = v32;
    v404[0] = (__int64)CFSTR("集合竞价转让-基础层");
    v403[1] = (__int64)v347;
    v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 8LL);
    v262 = objc_retainAutoreleasedReturnValue(v35);
    v404[1] = (__int64)v262;
    v403[2] = v36;
    v404[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v37 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v404, v403, 3LL);
    v264 = objc_retainAutoreleasedReturnValue(v37);
    v409[2] = (__int64)v264;
    v38 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v409, 3LL);
    v266 = objc_retainAutoreleasedReturnValue(v38);
    v411[3] = (__int64)v266;
    v39 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v411, v410, 4LL);
    v268 = objc_retainAutoreleasedReturnValue(v39);
    v427[4] = (__int64)v268;
    v401[0] = (__int64)CFSTR("isTwoBtn");
    v41 = (void *)v40(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v270 = objc_retainAutoreleasedReturnValue(v41);
    v402[0] = (__int64)v270;
    v401[1] = v32;
    v402[1] = (__int64)CFSTR("优先股");
    v401[2] = (__int64)v347;
    v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 9LL);
    v272 = objc_retainAutoreleasedReturnValue(v42);
    v402[2] = (__int64)v272;
    v401[3] = (__int64)v346;
    v402[3] = (__int64)CFSTR("normalBtnClicked:");
    v43 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v402, v401, 4LL);
    v274 = objc_retainAutoreleasedReturnValue(v43);
    v427[5] = (__int64)v274;
    v399[0] = (__int64)CFSTR("isTwoBtn");
    v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v276 = objc_retainAutoreleasedReturnValue(v44);
    v400[0] = (__int64)v276;
    v399[1] = (__int64)v345;
    v400[1] = (__int64)CFSTR("首发挂牌");
    v399[2] = v45;
    v46 = v45;
    v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 10LL);
    v278 = objc_retainAutoreleasedReturnValue(v47);
    v400[2] = (__int64)v278;
    v399[3] = (__int64)v346;
    v400[3] = (__int64)CFSTR("normalBtnClicked:");
    v48 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v400,
                            v399,
                            4LL);
    v280 = objc_retainAutoreleasedReturnValue(v48);
    v427[6] = (__int64)v280;
    v397[0] = (__int64)CFSTR("isTwoBtn");
    v49 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v282 = objc_retainAutoreleasedReturnValue(v49);
    v398[0] = (__int64)v282;
    v397[1] = (__int64)v345;
    v398[1] = (__int64)CFSTR("增发挂牌");
    v397[2] = v46;
    v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 11LL);
    v284 = objc_retainAutoreleasedReturnValue(v50);
    v398[2] = (__int64)v284;
    v397[3] = (__int64)v346;
    v398[3] = (__int64)CFSTR("normalBtnClicked:");
    v52 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v51, v398, v397, 4LL);
    v286 = objc_retainAutoreleasedReturnValue(v52);
    v427[7] = (__int64)v286;
    v395[0] = (__int64)CFSTR("isTwoBtn");
    v53 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
    v288 = objc_retainAutoreleasedReturnValue(v53);
    v396[0] = (__int64)v288;
    v395[1] = (__int64)v346;
    v396[1] = (__int64)CFSTR("twoBtnClicked:");
    v395[2] = (__int64)CFSTR("towBtnItems");
    v392[0] = (__int64)v345;
    v393[0] = (__int64)CFSTR("两网及A股退市");
    v392[1] = (__int64)v347;
    v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 12LL);
    v292 = objc_retainAutoreleasedReturnValue(v54);
    v393[1] = (__int64)v292;
    v392[2] = (__int64)v346;
    v393[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v55 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v393, v392, 3LL);
    v294 = objc_retainAutoreleasedReturnValue(v55);
    v394[0] = (__int64)v294;
    v390[0] = (__int64)v345;
    v391[0] = (__int64)CFSTR("B股退市");
    v390[1] = v56;
    v57 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 13LL);
    v296 = objc_retainAutoreleasedReturnValue(v57);
    v391[1] = (__int64)v296;
    v390[2] = (__int64)v346;
    v391[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v59 = (void *)v58(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v391, v390, 3LL);
    v298 = objc_retainAutoreleasedReturnValue(v59);
    v394[1] = (__int64)v298;
    v61 = (void *)v60(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v394, 2LL);
    v63 = v62;
    v300 = objc_retainAutoreleasedReturnValue(v61);
    v396[2] = (__int64)v300;
    v65 = (void *)v64(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v396, v395, 3LL);
    v302 = objc_retainAutoreleasedReturnValue(v65);
    v427[8] = (__int64)v302;
    v388[0] = (__int64)CFSTR("isTwoBtn");
    v66 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v304 = objc_retainAutoreleasedReturnValue(v66);
    v389[0] = (__int64)v304;
    v388[1] = (__int64)v345;
    v389[1] = (__int64)CFSTR("股转(新三板)指数");
    v388[2] = (__int64)v347;
    v67 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 14LL);
    v68 = objc_retainAutoreleasedReturnValue(v67);
    v389[2] = (__int64)v68;
    v388[3] = (__int64)v346;
    v389[3] = (__int64)CFSTR("normalBtnClicked:");
    v70 = (void *)((__int64 (__fastcall *)(void *, __int64, __int64 *, __int64 *, __int64))v63)(
                    &OBJC_CLASS___NSDictionary,
                    v69,
                    v389,
                    v388,
                    4LL);
    v71 = objc_retainAutoreleasedReturnValue(v70);
    v427[9] = (__int64)v71;
    v72 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64 *, __int64))v63)(
                    &OBJC_CLASS___NSArray,
                    "arrayWithObjects:count:",
                    v427,
                    10LL);
    v73 = objc_retainAutoreleasedReturnValue(v72);
    v290 = (id)((__int64 (__fastcall *)(id, const char *))v63)(v73, "count");
    if ( (unsigned __int64)v290 <= i )
      break;
    v385[0] = (__int64)CFSTR("isTwoBtn");
    v75 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v342 = objc_retainAutoreleasedReturnValue(v75);
    v386[0] = (__int64)v342;
    v385[1] = (__int64)v345;
    v386[1] = (__int64)CFSTR("股转综合页面");
    v385[2] = (__int64)v347;
    v76 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
    v330 = objc_retainAutoreleasedReturnValue(v76);
    v386[2] = (__int64)v330;
    v385[3] = (__int64)v346;
    v386[3] = (__int64)CFSTR("normalBtnClicked:");
    v77 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v386,
                            v385,
                            4LL);
    v310 = objc_retainAutoreleasedReturnValue(v77);
    v387[0] = (__int64)v310;
    v383[0] = (__int64)CFSTR("isTwoBtn");
    v78 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v339 = objc_retainAutoreleasedReturnValue(v78);
    v384[0] = (__int64)v339;
    v383[1] = (__int64)v345;
    v384[1] = (__int64)CFSTR("创新层");
    v383[2] = (__int64)v347;
    v79 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
    v327 = objc_retainAutoreleasedReturnValue(v79);
    v384[2] = (__int64)v327;
    v383[3] = (__int64)v346;
    v384[3] = (__int64)CFSTR("normalBtnClicked:");
    v81 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v80, v384, v383, 4LL);
    v313 = objc_retainAutoreleasedReturnValue(v81);
    v387[1] = (__int64)v313;
    v381[0] = (__int64)CFSTR("isTwoBtn");
    v82 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v315 = objc_retainAutoreleasedReturnValue(v82);
    v382[0] = (__int64)v315;
    v381[1] = (__int64)v345;
    v382[1] = (__int64)CFSTR("基础层");
    v381[2] = (__int64)v347;
    v83 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
    v317 = objc_retainAutoreleasedReturnValue(v83);
    v382[2] = (__int64)v317;
    v381[3] = (__int64)v346;
    v382[3] = (__int64)CFSTR("normalBtnClicked:");
    v85 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v84, v382, v381, 4LL);
    v319 = objc_retainAutoreleasedReturnValue(v85);
    v387[2] = (__int64)v319;
    v379[0] = (__int64)CFSTR("isTwoBtn");
    v86 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
    v336 = objc_retainAutoreleasedReturnValue(v86);
    v380[0] = (__int64)v336;
    v379[1] = (__int64)v346;
    v380[1] = (__int64)CFSTR("twoBtnClicked:");
    v379[2] = (__int64)CFSTR("width");
    v87 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 90LL);
    v321 = objc_retainAutoreleasedReturnValue(v87);
    v380[2] = (__int64)v321;
    v379[3] = (__int64)CFSTR("towBtnItems");
    v376[0] = (__int64)v345;
    v377[0] = (__int64)CFSTR("做市转让");
    v376[1] = (__int64)v347;
    v88 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 3LL);
    v324 = objc_retainAutoreleasedReturnValue(v88);
    v377[1] = (__int64)v324;
    v376[2] = (__int64)v346;
    v377[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v89 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v377, v376, 3LL);
    v239 = objc_retainAutoreleasedReturnValue(v89);
    v378[0] = (__int64)v239;
    v374[0] = (__int64)v345;
    v375[0] = (__int64)CFSTR("做市转让-创新层");
    v374[1] = v90;
    v91 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
    v241 = objc_retainAutoreleasedReturnValue(v91);
    v375[1] = (__int64)v241;
    v374[2] = (__int64)v346;
    v375[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v92 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v375, v374, 3LL);
    v243 = objc_retainAutoreleasedReturnValue(v92);
    v378[1] = (__int64)v243;
    v372[0] = (__int64)v345;
    v373[0] = (__int64)CFSTR("做市转让-基础层");
    v372[1] = (__int64)v347;
    v93 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
    v245 = objc_retainAutoreleasedReturnValue(v93);
    v373[1] = (__int64)v245;
    v372[2] = (__int64)v346;
    v373[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v95 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v94, v373, v372, 3LL);
    v247 = objc_retainAutoreleasedReturnValue(v95);
    v378[2] = (__int64)v247;
    v96 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v378, 3LL);
    v249 = objc_retainAutoreleasedReturnValue(v96);
    v380[3] = (__int64)v249;
    v98 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v97, v380, v379, 4LL);
    v251 = objc_retainAutoreleasedReturnValue(v98);
    v387[3] = (__int64)v251;
    v370[0] = (__int64)CFSTR("isTwoBtn");
    v99 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
    v253 = objc_retainAutoreleasedReturnValue(v99);
    v371[0] = (__int64)v253;
    v370[1] = (__int64)v346;
    v371[1] = (__int64)CFSTR("twoBtnClicked:");
    v370[2] = (__int64)CFSTR("width");
    v100 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 110LL);
    v255 = objc_retainAutoreleasedReturnValue(v100);
    v371[2] = (__int64)v255;
    v370[3] = (__int64)CFSTR("towBtnItems");
    v367[0] = (__int64)v345;
    v368[0] = (__int64)CFSTR("集合竞价转让");
    v367[1] = (__int64)v347;
    v102 = (void *)v101(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 6LL);
    v257 = objc_retainAutoreleasedReturnValue(v102);
    v368[1] = (__int64)v257;
    v367[2] = (__int64)v346;
    v368[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v104 = (void *)v103(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v368, v367, 3LL);
    v106 = v105;
    v259 = objc_retainAutoreleasedReturnValue(v104);
    v369[0] = (__int64)v259;
    v365[0] = (__int64)v345;
    v366[0] = (__int64)CFSTR("集合竞价转让-创新层");
    v365[1] = (__int64)v347;
    v107 = (void *)v106(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 7LL);
    v261 = objc_retainAutoreleasedReturnValue(v107);
    v366[1] = (__int64)v261;
    v365[2] = (__int64)v346;
    v366[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v108 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v366, v365, 3LL);
    v263 = objc_retainAutoreleasedReturnValue(v108);
    v369[1] = (__int64)v263;
    v363[0] = v109;
    v364[0] = (__int64)CFSTR("集合竞价转让-基础层");
    v363[1] = (__int64)v347;
    v110 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 8LL);
    v265 = objc_retainAutoreleasedReturnValue(v110);
    v364[1] = (__int64)v265;
    v363[2] = (__int64)v346;
    v364[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v111 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v364, v363, 3LL);
    v267 = objc_retainAutoreleasedReturnValue(v111);
    v369[2] = (__int64)v267;
    v112 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v369, 3LL);
    v269 = objc_retainAutoreleasedReturnValue(v112);
    v371[3] = (__int64)v269;
    v113 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v371, v370, 4LL);
    v271 = objc_retainAutoreleasedReturnValue(v113);
    v387[4] = (__int64)v271;
    v361[0] = (__int64)CFSTR("isTwoBtn");
    v114 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v273 = objc_retainAutoreleasedReturnValue(v114);
    v362[0] = (__int64)v273;
    v361[1] = (__int64)v345;
    v362[1] = (__int64)CFSTR("优先股");
    v361[2] = (__int64)v347;
    v115 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 9LL);
    v275 = objc_retainAutoreleasedReturnValue(v115);
    v362[2] = (__int64)v275;
    v361[3] = (__int64)v346;
    v362[3] = (__int64)CFSTR("normalBtnClicked:");
    v116 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v362, v361, 4LL);
    v277 = objc_retainAutoreleasedReturnValue(v116);
    v387[5] = (__int64)v277;
    v359[0] = (__int64)CFSTR("isTwoBtn");
    v117 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v279 = objc_retainAutoreleasedReturnValue(v117);
    v360[0] = (__int64)v279;
    v359[1] = (__int64)v345;
    v360[1] = (__int64)CFSTR("首发挂牌");
    v359[2] = (__int64)v347;
    v119 = _objc_msgSend(&OBJC_CLASS___NSNumber, v118, 10LL);
    v281 = objc_retainAutoreleasedReturnValue(v119);
    v360[2] = (__int64)v281;
    v359[3] = (__int64)v346;
    v360[3] = (__int64)CFSTR("normalBtnClicked:");
    v120 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v360, v359, 4LL);
    v283 = objc_retainAutoreleasedReturnValue(v120);
    v387[6] = (__int64)v283;
    v357[0] = (__int64)CFSTR("isTwoBtn");
    v121 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v285 = objc_retainAutoreleasedReturnValue(v121);
    v358[0] = (__int64)v285;
    v357[1] = (__int64)v345;
    v358[1] = (__int64)CFSTR("增发挂牌");
    v357[2] = (__int64)v347;
    v122 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 11LL);
    v287 = objc_retainAutoreleasedReturnValue(v122);
    v358[2] = (__int64)v287;
    v357[3] = v123;
    v358[3] = (__int64)CFSTR("normalBtnClicked:");
    v124 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v358, v357, 4LL);
    v289 = objc_retainAutoreleasedReturnValue(v124);
    v387[7] = (__int64)v289;
    v355[0] = (__int64)CFSTR("isTwoBtn");
    v125 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 1LL);
    v291 = objc_retainAutoreleasedReturnValue(v125);
    v356[0] = (__int64)v291;
    v355[1] = v126;
    v356[1] = (__int64)CFSTR("twoBtnClicked:");
    v355[2] = (__int64)CFSTR("towBtnItems");
    v352[0] = (__int64)v345;
    v353[0] = (__int64)CFSTR("两网及A股退市");
    v352[1] = (__int64)v347;
    v127 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 12LL);
    v293 = objc_retainAutoreleasedReturnValue(v127);
    v353[1] = (__int64)v293;
    v352[2] = v128;
    v353[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v129 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v353, v352, 3LL);
    v295 = objc_retainAutoreleasedReturnValue(v129);
    v354[0] = (__int64)v295;
    v350[0] = (__int64)v345;
    v351[0] = (__int64)CFSTR("B股退市");
    v350[1] = (__int64)v347;
    v130 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 13LL);
    v297 = objc_retainAutoreleasedReturnValue(v130);
    v351[1] = (__int64)v297;
    v350[2] = v131;
    v351[2] = (__int64)CFSTR("twoBtnMenuClicked:");
    v132 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v351, v350, 3LL);
    v299 = objc_retainAutoreleasedReturnValue(v132);
    v354[1] = (__int64)v299;
    v133 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v354, 2LL);
    v301 = objc_retainAutoreleasedReturnValue(v133);
    v356[2] = (__int64)v301;
    v134 = (NSDictionary *)_objc_msgSend(
                             &OBJC_CLASS___NSDictionary,
                             "dictionaryWithObjects:forKeys:count:",
                             v356,
                             v355,
                             3LL);
    v303 = objc_retainAutoreleasedReturnValue(v134);
    v387[8] = (__int64)v303;
    v348[0] = (__int64)CFSTR("isTwoBtn");
    v135 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithBool:", 0LL);
    v305 = objc_retainAutoreleasedReturnValue(v135);
    v349[0] = (__int64)v305;
    v348[1] = (__int64)v345;
    v349[1] = (__int64)CFSTR("股转(新三板)指数");
    v348[2] = (__int64)v347;
    v136 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 14LL);
    v137 = objc_retainAutoreleasedReturnValue(v136);
    v349[2] = (__int64)v137;
    v348[3] = v138;
    v349[3] = (__int64)CFSTR("normalBtnClicked:");
    v139 = (NSDictionary *)_objc_msgSend(
                             &OBJC_CLASS___NSDictionary,
                             "dictionaryWithObjects:forKeys:count:",
                             v349,
                             v348,
                             4LL);
    v140 = objc_retainAutoreleasedReturnValue(v139);
    v387[9] = (__int64)v140;
    v141 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v387, 10LL);
    v142 = objc_retainAutoreleasedReturnValue(v141);
    v143 = _objc_msgSend(v142, "thsDictionaryAtIndex:", i);
    v334 = objc_retainAutoreleasedReturnValue(v143);
    v145 = _objc_msgSend(v334, "thsNumberForKey:", CFSTR("isTwoBtn"));
    v146 = objc_retainAutoreleasedReturnValue(v145);
    LOBYTE(v140) = v147(v146, "boolValue");
    if ( (_BYTE)v140 )
    {
      v149 = (void *)v148(v334, "thsStringForKey:", v346);
      v337 = objc_retainAutoreleasedReturnValue(v149);
      v151 = (void *)v150(v334, "thsNumberForKey:", CFSTR("width"));
      v152 = objc_retainAutoreleasedReturnValue(v151);
      v154 = v152;
      if ( v152 )
        v153(v152, "integerValue", 118.0);
      v322 = v154;
      v155 = objc_alloc((Class)&OBJC_CLASS___HXTwoButton);
      v157 = v156(v155, "initWithFrame:");
      v158(v157, "initLeftButton");
      v159(v157, "initRightButton");
      v161 = (void *)v160(v157, "leftButton");
      v162 = objc_retainAutoreleasedReturnValue(v161);
      v163(v162, "setTarget:", self);
      v325 = (id)v157;
      v165 = (void *)v164(v157, "leftButton");
      v166 = objc_retainAutoreleasedReturnValue(v165);
      v167 = NSSelectorFromString(v337);
      v168(v166, "setAction:", v167);
      v170 = (void *)v169(v334, "thsArrayForKey:", CFSTR("towBtnItems"));
      v171 = objc_retainAutoreleasedReturnValue(v170);
      v173 = (void *)v172(&OBJC_CLASS___NSMutableArray, "array");
      v340 = objc_retainAutoreleasedReturnValue(v173);
      v175 = v174(v171, "count");
      v177 = v176;
      v328 = v171;
      if ( v175 )
      {
        v178 = 0LL;
        do
        {
          v179 = (void *)v177(v171, "thsDictionaryAtIndex:", v178);
          v180 = objc_retainAutoreleasedReturnValue(v179);
          v181 = (void *)v177(v180, "thsNumberForKey:", (unsigned __int64)v347);
          v182 = objc_retainAutoreleasedReturnValue(v181);
          v343 = (id)((__int64 (__fastcall *)(id, const char *))v177)(v182, "integerValue");
          v331 = v180;
          v183 = (void *)v177(v180, "thsStringForKey:", (unsigned __int64)v345);
          v184 = objc_retainAutoreleasedReturnValue(v183);
          v185 = v184;
          if ( !v184 )
            v185 = &charsToLeaveEscaped;
          v311 = objc_retain(v185);
          v186 = (void *)v177(v180, "thsStringForKey:", (unsigned __int64)v346);
          v187 = objc_retainAutoreleasedReturnValue(v186);
          v188 = objc_alloc(&OBJC_CLASS___NSMenuItem);
          v189 = NSSelectorFromString(v187);
          v190 = (void *)((__int64 (__fastcall *)(id, const char *, __CFString *, SEL, __CFString *))v177)(
                           v188,
                           "initWithTitle:action:keyEquivalent:",
                           v311,
                           v189,
                           &charsToLeaveEscaped);
          v177(v190, "setTag:", (unsigned __int64)v343);
          v177(v340, "addObject:", (unsigned __int64)v190);
          v171 = v328;
          v191 = ((__int64 (__fastcall *)(id, const char *))v177)(v328, "count");
        }
        while ( v191 > v178 );
      }
      v192 = ((__int64 (__fastcall *)(id, const char *))v177)(v340, "count");
      v194 = (void *)v157;
      if ( v192 )
      {
        v195 = (void *)v193(v340, "firstObject");
        v196 = objc_retainAutoreleasedReturnValue(v195);
        v198 = (void *)v197(v196, "title");
        v199 = objc_retainAutoreleasedReturnValue(v198);
        v200(v325, "setTitleOnLeft:", v199);
        v202 = (void *)v201(v325, "rightButton");
        v203 = objc_retainAutoreleasedReturnValue(v202);
        v204(v203, "setMenus:", v340);
        v206 = (void *)v205(v325, "titleOnLeft");
        v207 = objc_retainAutoreleasedReturnValue(v206);
        v209 = (void *)v208(v325, "rightButton");
        v210 = objc_retainAutoreleasedReturnValue(v209);
        v211(v210, "setSelectedMenuItemTitle:", v207);
        v212 = v210;
        v194 = v325;
        v171 = v328;
        v213(v307, "addObject:", v325);
      }
      v214 = v334;
      v215 = v322;
    }
    else
    {
      v216 = (void *)v148(v334, "thsNumberForKey:", v347);
      v217 = objc_retainAutoreleasedReturnValue(v216);
      v344 = (__CFString *)v218(v217, "integerValue");
      v220 = v219;
      v221 = (void *)v219(v334, (__int64)"thsStringForKey:", v345);
      v222 = v220;
      v223 = objc_retainAutoreleasedReturnValue(v221);
      v224 = v223;
      if ( !v223 )
        v224 = &charsToLeaveEscaped;
      v332 = objc_retain(v224);
      v226 = (void *)v222(v334, v225, v346);
      v337 = objc_retainAutoreleasedReturnValue(v226);
      v227 = objc_alloc((Class)&OBJC_CLASS___HXButton);
      v228 = ((__int64 (__fastcall *)(id, const char *))v222)(v227, "initWithFrame:");
      v222((id)v228, (__int64)"setTag:", v344);
      v222(v229, (__int64)"setTitle:", v332);
      v222(v230, (__int64)"setTarget:", (__CFString *)self);
      v231 = NSSelectorFromString(v337);
      v222(v232, (__int64)"setAction:", (__CFString *)v231);
      v234 = (void *)((__int64 (__fastcall *)(__int64, const char *))v222)(v233, "font");
      v235 = objc_retainAutoreleasedReturnValue(v234);
      -[XinSanBanContainerController widthOfString:withFont:](self, "widthOfString:withFont:", v332, v235);
      _objc_msgSend(v236, "setFrame:");
      _objc_msgSend(v307, "addObject:", v237);
      v214 = v334;
    }
  }
  -[XinSanBanContainerController setToolBarItems:](self, "setToolBarItems:", v307);
}

//----- (00000001000B9054) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setErJiMenuState](XinSanBanContainerController *self, SEL a2)
{
  id (**v2)(id, SEL, ...); // r15
  NSArray *v3; // rax
  unsigned __int64 v6; // r12
  id (**v7)(id, SEL, ...); // r14
  id obj; // [rsp+110h] [rbp-C0h]

  v41 = 0LL;
  v42 = 0LL;
  v43 = 0LL;
  v44 = 0LL;
  v2 = &_objc_msgSend;
  v64 = self;
  v3 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
  obj = objc_retainAutoreleasedReturnValue(v3);
  v65 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v41, v73, 16LL);
  if ( v65 )
  {
    v45 = *(_QWORD *)v42;
    do
    {
      v4 = "class";
      v5 = "isKindOfClass:";
      v48 = "systemFontOfSize:";
      v49 = "setFont:";
      v50 = "cell";
      v51 = "setHighlightsBy:";
      v52 = "setBordered:";
      v53 = "setShouldTracking:";
      v54 = "setCanBeSelected:";
      v55 = "normalTextColor";
      v56 = "setTextColorDefault:";
      v57 = "thirdNavigationBarSelectedTextColor";
      v58 = "setTextColorSelected:";
      v59 = "clearColor";
      v60 = "setBackgroundColor:";
      v70 = "thirdNavigationBarSelectedColor";
      v66 = "setBackgroundColorSelected:";
      v67 = "setWantsLayer:";
      v68 = "layer";
      v69 = "setCornerRadius:";
      v61 = "setAllBorder:";
      v62 = "setBorderWidth:";
      v63 = "setBorderColor:";
      v6 = 0LL;
      v46 = "class";
      v47 = "isKindOfClass:";
      do
      {
        v7 = v2;
        if ( *(_QWORD *)v42 != v45 )
          objc_enumerationMutation(obj);
        v8 = *(void **)(*((_QWORD *)&v41 + 1) + 8 * v6);
        v9 = ((__int64 (__fastcall *)(__objc2_class *, const char *))v7)(&OBJC_CLASS___HXButton, v4);
        v72 = v8;
        v10 = v8;
        v2 = v7;
        if ( ((unsigned __int8 (__fastcall *)(void *, const char *, __int64))v7)(v10, v5, v9) )
        {
          v11 = objc_retain(v72);
          v12 = (void *)((__int64 (__fastcall *)(void *, const char *, double))v2)(&OBJC_CLASS___NSFont, v48, 13.0);
          v13 = objc_retainAutoreleasedReturnValue(v12);
          ((void (__fastcall *)(id, const char *, id))v2)(v11, v49, v13);
          v14 = (void *)((__int64 (__fastcall *)(id, const char *))v2)(v11, v50);
          v15 = objc_retainAutoreleasedReturnValue(v14);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v15, v51, 1LL);
          ((void (__fastcall *)(id, const char *, _QWORD))v2)(v11, v52, 0LL);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v11, v53, 1LL);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v11, v54, 1LL);
          v16 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v2)(&OBJC_CLASS___HXThemeManager, v55);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          ((void (__fastcall *)(id, const char *, id))v2)(v11, v56, v17);
          v18 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v2)(&OBJC_CLASS___HXThemeManager, v57);
          v19 = objc_retainAutoreleasedReturnValue(v18);
          ((void (__fastcall *)(id, const char *, id))v2)(v11, v58, v19);
          v20 = (void *)((__int64 (__fastcall *)(void *, const char *))v2)(&OBJC_CLASS___NSColor, v59);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          ((void (__fastcall *)(id, const char *, id))v2)(v11, v60, v21);
          v22 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v2)(&OBJC_CLASS___HXThemeManager, v70);
          v23 = objc_retainAutoreleasedReturnValue(v22);
          ((void (__fastcall *)(id, const char *, id))v2)(v11, v66, v23);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v11, v67, 1LL);
          v24 = (void *)((__int64 (__fastcall *)(id, const char *))v2)(v11, v68);
          v25 = objc_retainAutoreleasedReturnValue(v24);
          ((void (__fastcall *)(id, const char *, double))v2)(v25, v69, 3.0);
        }
        else
        {
          v26 = ((__int64 (__fastcall *)(__objc2_class *, const char *))v7)(&OBJC_CLASS___HXTwoButton, v4);
          if ( !((unsigned __int8 (__fastcall *)(id, const char *, __int64))v7)(v72, v5, v26) )
            goto LABEL_11;
          v28 = objc_retain(v72);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v28, v67, 1LL);
          v29 = (void *)((__int64 (__fastcall *)(id, const char *))v2)(v28, v68);
          v30 = objc_retainAutoreleasedReturnValue(v29);
          ((void (__fastcall *)(id, const char *, double))v2)(v30, v69, 3.0);
          ((void (__fastcall *)(id, const char *, __int64))v2)(v28, v61, 1LL);
          ((void (__fastcall *)(id, const char *, double))v2)(v28, v62, 2.0);
          v31 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v2)(&OBJC_CLASS___HXThemeManager, v70);
          v32 = objc_retainAutoreleasedReturnValue(v31);
          ((void (__fastcall *)(id, const char *, id))v2)(v28, v63, v32);
          v33 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v2)(&OBJC_CLASS___HXThemeManager, v70);
          v25 = objc_retainAutoreleasedReturnValue(v33);
          ((void (__fastcall *)(id, const char *, id))v2)(v28, v66, v25);
        }
        v4 = v46;
        v5 = v47;
LABEL_11:
        v6 = v27 + 1;
      }
      while ( v6 < (unsigned __int64)v65 );
      v65 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v2)(
                  obj,
                  "countByEnumeratingWithState:objects:count:",
                  &v41,
                  v73,
                  16LL);
    }
    while ( v65 );
  }
  v34 = v64;
  v35 = ((id (*)(id, SEL, ...))v2)(v64, "navigationBarVC");
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v37 = ((id (*)(id, SEL, ...))v2)(v34, "toolBarItems");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  ((void (*)(id, SEL, ...))v2)(v36, "updateNavigationItems:", v38);
  v39(v38);
  v40(v36);
}

//----- (00000001000B964C) ----------------------------------------------------
HXTabbarController *__cdecl -[XinSanBanContainerController xinSanBanTabbarController](
        XinSanBanContainerController *self,
        SEL a2)
{
  id WeakRetained; // rbx

  v2 = *(void **)&self->super.super._viewIsAppearing;
  if ( !v2 )
  {
    v3 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v4 = _objc_msgSend(v3, "init");
    v6 = *(void **)(v5 + 96);
    *(_QWORD *)(v5 + 96) = v4;
    WeakRetained = objc_loadWeakRetained((id *)(v7 + 72));
    _objc_msgSend(*(id *)(v9 + 96), "setView:", WeakRetained);
    v10 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v13 = _objc_msgSend(v12, "guZhuanIntegratedScreenVC");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v14, 0LL);
    v16 = _objc_msgSend(v15, "chuangXinCengTableVC");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v17, 1LL);
    v19 = _objc_msgSend(v18, "jiChuCengTableVC");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v20, 2LL);
    v22 = _objc_msgSend(v21, "zuoShiZhuanRangTableVC");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v23, 3LL);
    v25 = _objc_msgSend(v24, "zuoShiZhuanRangChuangXinCengVC");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v26, 4LL);
    v28 = _objc_msgSend(v27, "zuoShiZhuanRangJiChuCengVC");
    v29 = objc_retainAutoreleasedReturnValue(v28);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v29, 5LL);
    v31 = _objc_msgSend(v30, "xieYiZhuanRangTableVC");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v32, 6LL);
    v34 = _objc_msgSend(v33, "xieYiZhuanRangChuangXinCengVC");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v35, 7LL);
    v37 = _objc_msgSend(v36, "xieYiZhuanRangJiChuCengVC");
    v38 = objc_retainAutoreleasedReturnValue(v37);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v38, 8LL);
    v40 = _objc_msgSend(v39, "youXianGuTableVC");
    v41 = objc_retainAutoreleasedReturnValue(v40);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v41, 9LL);
    v43 = _objc_msgSend(v42, "shouFaGuaPaiTableVC");
    v44 = objc_retainAutoreleasedReturnValue(v43);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v44, 10LL);
    v46 = _objc_msgSend(v45, "zengFaGuaPaiTableVC");
    v47 = objc_retainAutoreleasedReturnValue(v46);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v47, 11LL);
    v49 = _objc_msgSend(v48, "aGuTuiShiTableVC");
    v50 = objc_retainAutoreleasedReturnValue(v49);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v50, 12LL);
    v52 = _objc_msgSend(v51, "bGuTuiShiTableVC");
    v53 = objc_retainAutoreleasedReturnValue(v52);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v53, 13LL);
    v55 = _objc_msgSend(v54, "xinSanBanZhiShuTableVC");
    v56 = objc_retainAutoreleasedReturnValue(v55);
    _objc_msgSend(v11, "setObject:atIndexedSubscript:", v56, 14LL);
    _objc_msgSend(*(id *)(v57 + 96), "setViewControllers:", v11);
    v2 = *(void **)(v58 + 96);
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(v2);
}

//----- (00000001000B9A36) ----------------------------------------------------
GuZhuanIntegratedScreenViewController *__cdecl -[XinSanBanContainerController guZhuanIntegratedScreenVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id refreshRightViewBlock; // rdi

  refreshRightViewBlock = self->_refreshRightViewBlock;
  if ( !refreshRightViewBlock )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuZhuanIntegratedScreenViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->_refreshRightViewBlock;
    self->_refreshRightViewBlock = v5;
    refreshRightViewBlock = self->_refreshRightViewBlock;
  }
  return (GuZhuanIntegratedScreenViewController *)objc_retainAutoreleaseReturnValue(refreshRightViewBlock);
}

//----- (00000001000B9A87) ----------------------------------------------------
ChuangXinCengTableViewController *__cdecl -[XinSanBanContainerController chuangXinCengTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id (*v7)(id, SEL, ...); // r12

  v3 = *(void **)&self->super.super._reserved;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ChuangXinCengTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super.super._reserved;
    *(_QWORD *)&self->super.super._reserved = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(*(id *)&self->super.super._reserved, "setRefreshRightViewBlock:", v9);
    v3 = *(void **)&self->super.super._reserved;
  }
  return (ChuangXinCengTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001000B9B20) ----------------------------------------------------
JiChuCengTableViewController *__cdecl -[XinSanBanContainerController jiChuCengTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id (*v7)(id, SEL, ...); // r12

  v3 = *(void **)&self->super._shouldRefresh;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___JiChuCengTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._shouldRefresh;
    *(_QWORD *)&self->super._shouldRefresh = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(*(id *)&self->super._shouldRefresh, "setRefreshRightViewBlock:", v9);
    v3 = *(void **)&self->super._shouldRefresh;
  }
  return (JiChuCengTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001000B9BB9) ----------------------------------------------------
ZuoShiZhuanRangTableViewController *__cdecl -[XinSanBanContainerController zuoShiZhuanRangTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  NSString *stockCode; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  stockCode = self->super._stockCode;
  if ( !stockCode )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZuoShiZhuanRangTableViewController);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super._stockCode;
    self->super._stockCode = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->super._stockCode, "setRefreshRightViewBlock:", v9);
    stockCode = self->super._stockCode;
  }
  return (ZuoShiZhuanRangTableViewController *)objc_retainAutoreleaseReturnValue(stockCode);
}

//----- (00000001000B9C52) ----------------------------------------------------
XieYiZhuanRangTableViewController *__cdecl -[XinSanBanContainerController xieYiZhuanRangTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  NSString *market; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  market = self->super._market;
  if ( !market )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___XieYiZhuanRangTableViewController);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super._market;
    self->super._market = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->super._market, "setRefreshRightViewBlock:", v9);
    market = self->super._market;
  }
  return (XieYiZhuanRangTableViewController *)objc_retainAutoreleaseReturnValue(market);
}

//----- (00000001000B9CEB) ----------------------------------------------------
YouXianGuTableViewController *__cdecl -[XinSanBanContainerController youXianGuTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  NSMutableArray *contentsObjMArr; // rdi
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  contentsObjMArr = self->super._contentsObjMArr;
  if ( !contentsObjMArr )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___YouXianGuTableViewController);
    v5 = (NSMutableArray *)_objc_msgSend(v4, "init");
    v6 = self->super._contentsObjMArr;
    self->super._contentsObjMArr = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->super._contentsObjMArr, "setRefreshRightViewBlock:", v9);
    contentsObjMArr = self->super._contentsObjMArr;
  }
  return (YouXianGuTableViewController *)objc_retainAutoreleaseReturnValue(contentsObjMArr);
}

//----- (00000001000B9D84) ----------------------------------------------------
ShouRiGuaPaiTableViewController *__cdecl -[XinSanBanContainerController shouFaGuaPaiTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id (*v7)(id, SEL, ...); // r12

  controllerID = (void *)self->super._controllerID;
  if ( !controllerID )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ShouRiGuaPaiTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = (void *)self->super._controllerID;
    self->super._controllerID = (signed __int64)v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10((id)self->super._controllerID, "setRefreshRightViewBlock:", v9);
    v11((id)self->super._controllerID, "setZhuangRangZhuangTaiJudgeString:", CFSTR("Y"));
    controllerID = (void *)self->super._controllerID;
  }
  return (ShouRiGuaPaiTableViewController *)objc_retainAutoreleaseReturnValue(controllerID);
}

//----- (00000001000B9E32) ----------------------------------------------------
ShouRiGuaPaiTableViewController *__cdecl -[XinSanBanContainerController zengFaGuaPaiTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  NSMutableDictionary *paramsMDic; // rdi
  NSMutableDictionary *v5; // rax
  NSMutableDictionary *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  paramsMDic = self->super._paramsMDic;
  if ( !paramsMDic )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ShouRiGuaPaiTableViewController);
    v5 = (NSMutableDictionary *)_objc_msgSend(v4, "init");
    v6 = self->super._paramsMDic;
    self->super._paramsMDic = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->super._paramsMDic, "setRefreshRightViewBlock:", v9);
    v11(self->super._paramsMDic, "setZhuangRangZhuangTaiJudgeString:", CFSTR("D"));
    paramsMDic = self->super._paramsMDic;
  }
  return (ShouRiGuaPaiTableViewController *)objc_retainAutoreleaseReturnValue(paramsMDic);
}

//----- (00000001000B9EE0) ----------------------------------------------------
AGuTuiShiTableViewController *__cdecl -[XinSanBanContainerController aGuTuiShiTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id (*v7)(id, SEL, ...); // r12

  v3 = *(void **)&self->_guZhuanIntegratedScreenSelected;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___AGuTuiShiTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->_guZhuanIntegratedScreenSelected;
    *(_QWORD *)&self->_guZhuanIntegratedScreenSelected = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(*(id *)&self->_guZhuanIntegratedScreenSelected, "setRefreshRightViewBlock:", v9);
    v3 = *(void **)&self->_guZhuanIntegratedScreenSelected;
  }
  return (AGuTuiShiTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001000B9F79) ----------------------------------------------------
BGuTuiShiTableViewController *__cdecl -[XinSanBanContainerController bGuTuiShiTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  HXBaseView *topView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  topView = self->_topView;
  if ( !topView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___BGuTuiShiTableViewController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_topView;
    self->_topView = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_topView, "setRefreshRightViewBlock:", v9);
    topView = self->_topView;
  }
  return (BGuTuiShiTableViewController *)objc_retainAutoreleaseReturnValue(topView);
}

//----- (00000001000BA012) ----------------------------------------------------
XinSanBanZhiShuTableViewController *__cdecl -[XinSanBanContainerController xinSanBanZhiShuTableVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  HXBaseView *viewForTable; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  viewForTable = self->_viewForTable;
  if ( !viewForTable )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___XinSanBanZhiShuTableViewController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_viewForTable;
    self->_viewForTable = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_viewForTable, "setRefreshRightViewBlock:", v9);
    viewForTable = self->_viewForTable;
  }
  return (XinSanBanZhiShuTableViewController *)objc_retainAutoreleaseReturnValue(viewForTable);
}

//----- (00000001000BA0AB) ----------------------------------------------------
ZuoShiZhuanRangChuangXinCengTableViewController *__cdecl -[XinSanBanContainerController zuoShiZhuanRangChuangXinCengVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id showAndHideRightBtnBlock; // rdi
  id (*v7)(id, SEL, ...); // r12

  showAndHideRightBtnBlock = self->_showAndHideRightBtnBlock;
  if ( !showAndHideRightBtnBlock )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZuoShiZhuanRangChuangXinCengTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->_showAndHideRightBtnBlock;
    self->_showAndHideRightBtnBlock = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_showAndHideRightBtnBlock, "setRefreshRightViewBlock:", v9);
    showAndHideRightBtnBlock = self->_showAndHideRightBtnBlock;
  }
  return (ZuoShiZhuanRangChuangXinCengTableViewController *)objc_retainAutoreleaseReturnValue(showAndHideRightBtnBlock);
}

//----- (00000001000BA144) ----------------------------------------------------
ZuoShiZhuanRangJiChuCengTableViewController *__cdecl -[XinSanBanContainerController zuoShiZhuanRangJiChuCengVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  HXTabbarController *xinSanBanTabbarController; // rdi
  HXTabbarController *v5; // rax
  HXTabbarController *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  xinSanBanTabbarController = self->_xinSanBanTabbarController;
  if ( !xinSanBanTabbarController )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZuoShiZhuanRangJiChuCengTableViewController);
    v5 = (HXTabbarController *)_objc_msgSend(v4, "init");
    v6 = self->_xinSanBanTabbarController;
    self->_xinSanBanTabbarController = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_xinSanBanTabbarController, "setRefreshRightViewBlock:", v9);
    xinSanBanTabbarController = self->_xinSanBanTabbarController;
  }
  return (ZuoShiZhuanRangJiChuCengTableViewController *)objc_retainAutoreleaseReturnValue(xinSanBanTabbarController);
}

//----- (00000001000BA1DD) ----------------------------------------------------
XieYiZhuanRangChuangXinCengTableViewController *__cdecl -[XinSanBanContainerController xieYiZhuanRangChuangXinCengVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  id currentTableVC; // rdi
  id (*v7)(id, SEL, ...); // r12

  currentTableVC = self->_currentTableVC;
  if ( !currentTableVC )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___XieYiZhuanRangChuangXinCengTableViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->_currentTableVC;
    self->_currentTableVC = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_currentTableVC, "setRefreshRightViewBlock:", v9);
    currentTableVC = self->_currentTableVC;
  }
  return (XieYiZhuanRangChuangXinCengTableViewController *)objc_retainAutoreleaseReturnValue(currentTableVC);
}

//----- (00000001000BA276) ----------------------------------------------------
XieYiZhuanRangJiChuCengTableViewController *__cdecl -[XinSanBanContainerController xieYiZhuanRangJiChuCengVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  ChuangXinCengTableViewController *chuangXinCengTableVC; // rdi
  ChuangXinCengTableViewController *v5; // rax
  ChuangXinCengTableViewController *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  chuangXinCengTableVC = self->_chuangXinCengTableVC;
  if ( !chuangXinCengTableVC )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___XieYiZhuanRangJiChuCengTableViewController);
    v5 = (ChuangXinCengTableViewController *)_objc_msgSend(v4, "init");
    v6 = self->_chuangXinCengTableVC;
    self->_chuangXinCengTableVC = v5;
    v8 = v7(self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_chuangXinCengTableVC, "setRefreshRightViewBlock:", v9);
    chuangXinCengTableVC = self->_chuangXinCengTableVC;
  }
  return (XieYiZhuanRangJiChuCengTableViewController *)objc_retainAutoreleaseReturnValue(chuangXinCengTableVC);
}

//----- (00000001000BA30F) ----------------------------------------------------
ScrollNavigationBarViewController *__cdecl -[XinSanBanContainerController navigationBarVC](
        XinSanBanContainerController *self,
        SEL a2)
{
  ZuoShiZhuanRangTableViewController *zuoShiZhuanRangTableVC; // rdi
  id WeakRetained; // rbx
  ZuoShiZhuanRangTableViewController *v6; // rax
  ZuoShiZhuanRangTableViewController *v7; // rdi
  ZuoShiZhuanRangTableViewController *v16; // rdi

  zuoShiZhuanRangTableVC = self->_zuoShiZhuanRangTableVC;
  if ( !zuoShiZhuanRangTableVC )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
    if ( !WeakRetained )
      return (ScrollNavigationBarViewController *)objc_autoreleaseReturnValue(0LL);
    v5 = objc_alloc((Class)&OBJC_CLASS___ScrollNavigationBarViewController);
    v6 = (ZuoShiZhuanRangTableViewController *)_objc_msgSend(v5, "init");
    v7 = self->_zuoShiZhuanRangTableVC;
    self->_zuoShiZhuanRangTableVC = v6;
    v8 = (const char *)objc_loadWeakRetained((id *)&self->super.super._editors);
    if ( v8 )
      objc_msgSend_stret(v17, v8, "bounds");
    else
      memset(v17, 0, 32);
    v10 = _objc_msgSend(self->_zuoShiZhuanRangTableVC, "view");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "setFrame:");
    v13 = objc_loadWeakRetained((id *)&self->super.super._editors);
    v14 = _objc_msgSend(self->_zuoShiZhuanRangTableVC, "view");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(v13, "addSubview:", v15);
    zuoShiZhuanRangTableVC = self->_zuoShiZhuanRangTableVC;
  }
  v16 = objc_retain(zuoShiZhuanRangTableVC);
  return (ScrollNavigationBarViewController *)objc_autoreleaseReturnValue(v16);
}

//----- (00000001000BA487) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController keyDownFromSuper:](XinSanBanContainerController *self, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = -[XinSanBanContainerController currentTableVC](self, "currentTableVC");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = (unsigned __int8)v6(v5, "respondsToSelector:", "keyDownFromSuper:");
  if ( v7 )
  {
    v9 = v8(self, "currentTableVC");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11(v10, "keyDownFromSuper:", v3);
  }
}

//----- (00000001000BA530) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController normalBtnClicked:](XinSanBanContainerController *self, SEL a2, id a3)
{
  SEL v6; // r12
  HXTabbarController *v8; // rax
  HXTabbarController *v9; // rax
  HXTabbarController *v11; // rax
  HXTabbarController *v12; // rax

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = objc_retain(v3);
    _objc_msgSend(self, v6, v5);
    v21 = v3;
    v7 = _objc_msgSend(v5, "tag");
    v8 = -[XinSanBanContainerController xinSanBanTabbarController](self, "xinSanBanTabbarController");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    -[HXTabbarController setSelectedIndex:](v9, "setSelectedIndex:", v7);
    v11 = -[XinSanBanContainerController xinSanBanTabbarController](self, "xinSanBanTabbarController");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = -[HXTabbarController selectedViewController](v12, "selectedViewController");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    -[XinSanBanContainerController setCurrentTableVC:](self, "setCurrentTableVC:", v14);
    v16 = _objc_msgSend(v5, "title");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18(v5);
    -[XinSanBanContainerController sendChangeTableUserLog:](self, "sendChangeTableUserLog:", v17);
    v19 = v17;
    v3 = v21;
    v20(v19);
  }
}

//----- (00000001000BA684) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController twoBtnClicked:](XinSanBanContainerController *self, SEL a2, id a3)
{
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  SEL v51; // [rsp+98h] [rbp-1C8h]
  SEL v52; // [rsp+A0h] [rbp-1C0h]
  SEL v56; // [rsp+C0h] [rbp-1A0h]
  SEL v57; // [rsp+C8h] [rbp-198h]
  SEL v58; // [rsp+D0h] [rbp-190h]
  SEL v59; // [rsp+D8h] [rbp-188h]
  SEL v60; // [rsp+E0h] [rbp-180h]
  SEL v61; // [rsp+E8h] [rbp-178h]
  id obj; // [rsp+108h] [rbp-158h]

  v66 = self;
  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v44 = 0LL;
    v43 = 0LL;
    v42 = 0LL;
    v41 = 0LL;
    v5 = _objc_msgSend(v66, "toolBarItems");
    obj = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v41, v71, 16LL);
    if ( !v6 )
      goto LABEL_30;
    v8 = v6;
    v54 = *(_QWORD *)v42;
    v64 = v3;
    while ( 1 )
    {
      v56 = "leftButton";
      v57 = "setSelectedItem:";
      v58 = "rightButton";
      v59 = "menus";
      v55 = v8;
      v9 = 0LL;
      do
      {
        if ( *(_QWORD *)v42 != v54 )
          objc_enumerationMutation(obj);
        v53 = v9;
        v10 = *(void **)(*((_QWORD *)&v41 + 1) + 8 * v9);
        v11 = _objc_msgSend(&OBJC_CLASS___HXTwoButton, v7);
        if ( !(unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v11) )
          goto LABEL_28;
        v68 = objc_retain(v10);
        v12 = _objc_msgSend(v68, v56);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        if ( v13 != v3 )
          goto LABEL_27;
        v14 = v68;
        _objc_msgSend(v66, v57, v68);
        v48 = 0LL;
        v47 = 0LL;
        v46 = 0LL;
        v45 = 0LL;
        v16 = v15(v14, v58);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v19 = v18(v17, v59);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v67 = v20;
        v22 = v21(v20, "countByEnumeratingWithState:objects:count:", &v45, v70, 16LL);
        if ( !v22 )
          goto LABEL_26;
        v24 = v22;
        v50 = *(_QWORD *)v46;
        do
        {
          v61 = "titleOnLeft";
          v60 = "length";
          v51 = "title";
          v52 = "isEqualToString:";
          v25 = 0LL;
          v49 = v24;
          do
          {
            if ( *(_QWORD *)v46 != v50 )
              objc_enumerationMutation(v67);
            v26 = *(void **)(*((_QWORD *)&v45 + 1) + 8LL * (_QWORD)v25);
            v27 = v23(v68, v61);
            v28 = objc_retainAutoreleasedReturnValue(v27);
            if ( !v29(v28, v60) )
              goto LABEL_23;
            v62 = v28;
            v63 = v26;
            v31 = v26;
            v32 = v51;
            v33 = v30(v31, v51);
            v34 = objc_retainAutoreleasedReturnValue(v33);
            if ( !v35(v34, v60) )
            {
              v28 = v62;
LABEL_23:
              goto LABEL_24;
            }
            v37 = v36(v68, v61);
            v38 = objc_retainAutoreleasedReturnValue(v37);
            v39 = _objc_msgSend(v63, v32);
            v40 = objc_retainAutoreleasedReturnValue(v39);
            v69 = (unsigned __int8)_objc_msgSend(v38, v52, v40);
            if ( v69 )
            {
              _objc_msgSend(v66, "twoBtnMenuClicked:", v63);
              v3 = v64;
              goto LABEL_30;
            }
LABEL_24:
            v25 = (char *)v25 + 1;
          }
          while ( v49 != v25 );
          v24 = v23(v67, "countByEnumeratingWithState:objects:count:", &v45, v70, 16LL);
        }
        while ( v24 );
LABEL_26:
        v3 = v64;
LABEL_27:
LABEL_28:
        v9 = v53 + 1;
      }
      while ( (id)(v53 + 1) != v55 );
      v8 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v41, v71, 16LL);
      if ( !v8 )
      {
LABEL_30:
        break;
      }
    }
  }
}

//----- (00000001000BABCE) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController twoBtnMenuClicked:](XinSanBanContainerController *self, SEL a2, id a3)
{
  NSArray *v5; // rax
  NSArray *v6; // rax
  SEL v58; // [rsp+A8h] [rbp-178h]
  SEL v60; // [rsp+B8h] [rbp-168h]
  SEL v61; // [rsp+C0h] [rbp-160h]
  id obj; // [rsp+E8h] [rbp-138h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSMenuItem, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v57 = v3;
    v65 = objc_retain(v3);
    v45 = 0LL;
    v46 = 0LL;
    v47 = 0LL;
    v48 = 0LL;
    v5 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "countByEnumeratingWithState:objects:count:", &v45, v68, 16LL);
    if ( v7 )
    {
      v9 = v7;
      v56 = *(_QWORD *)v46;
      v62 = self;
      v63 = v8;
      do
      {
        v61 = "rightButton";
        v58 = "menus";
        v10 = 0LL;
        v59 = v9;
        do
        {
          if ( *(_QWORD *)v46 != v56 )
            objc_enumerationMutation(v8);
          v55 = v10;
          v11 = *(void **)(*((_QWORD *)&v45 + 1) + 8 * v10);
          v12 = _objc_msgSend(&OBJC_CLASS___HXTwoButton, "class");
          if ( (unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12) )
          {
            v13 = objc_retain(v11);
            v49 = 0LL;
            v50 = 0LL;
            v51 = 0LL;
            v52 = 0LL;
            v64 = v13;
            v14 = _objc_msgSend(v13, v61);
            v15 = objc_retainAutoreleasedReturnValue(v14);
            v16 = _objc_msgSend(v15, v58);
            v17 = objc_retainAutoreleasedReturnValue(v16);
            obj = v17;
            v18 = _objc_msgSend(v17, "countByEnumeratingWithState:objects:count:", &v49, v67, 16LL);
            if ( v18 )
            {
              v19 = v18;
              v53 = *(_QWORD *)v50;
              while ( 2 )
              {
                v60 = "tag";
                v20 = 0LL;
                v54 = v19;
                do
                {
                  if ( *(_QWORD *)v50 != v53 )
                    objc_enumerationMutation(obj);
                  v21 = *(void **)(*((_QWORD *)&v49 + 1) + 8LL * (_QWORD)v20);
                  v22 = v60;
                  _objc_msgSend(v65, v60);
                  v23 = _objc_msgSend(v21, v22);
                  if ( v24 == v23 )
                  {
                    v25 = v64;
                    _objc_msgSend(v62, "setSelectedItem:", v64);
                    v26 = _objc_msgSend(v65, "title");
                    v27 = objc_retainAutoreleasedReturnValue(v26);
                    _objc_msgSend(v25, "setTitleOnLeft:", v27);
                    v29 = _objc_msgSend(v28, "title");
                    objc_retainAutoreleasedReturnValue(v29);
                    v30 = _objc_msgSend(v25, v61);
                    v31 = objc_retainAutoreleasedReturnValue(v30);
                    _objc_msgSend(v31, "setSelectedMenuItemTitle:", v32);
                    v34 = _objc_msgSend(v25, "titleOnLeft");
                    v35 = objc_retainAutoreleasedReturnValue(v34);
                    v36 = v62;
                    _objc_msgSend(v62, "sendChangeTableUserLog:", v35);
                    _objc_msgSend(v65, v60);
                    v37 = _objc_msgSend(v36, "xinSanBanTabbarController");
                    v38 = objc_retainAutoreleasedReturnValue(v37);
                    _objc_msgSend(v38, "setSelectedIndex:", v39);
                    v40 = _objc_msgSend(v36, "xinSanBanTabbarController");
                    v41 = objc_retainAutoreleasedReturnValue(v40);
                    v42 = _objc_msgSend(v41, "selectedViewController");
                    v43 = objc_retainAutoreleasedReturnValue(v42);
                    _objc_msgSend(v44, "setCurrentTableVC:", v43);
                    v8 = v63;
                    goto LABEL_21;
                  }
                  v20 = (char *)v20 + 1;
                }
                while ( v54 != v20 );
                v19 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v49, v67, 16LL);
                if ( v19 )
                  continue;
                break;
              }
            }
            v8 = v63;
          }
          v10 = v55 + 1;
        }
        while ( (id)(v55 + 1) != v59 );
        v9 = _objc_msgSend(v8, "countByEnumeratingWithState:objects:count:", &v45, v68, 16LL);
      }
      while ( v9 );
    }
LABEL_21:
    v3 = v57;
  }
}

//----- (00000001000BB10A) ----------------------------------------------------
char __cdecl -[XinSanBanContainerController guZhuanIntegratedScreenSelected](
        XinSanBanContainerController *self,
        SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSArray *v5; // rax
  NSArray *v6; // r14

  v2 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "count");
  if ( !v4 )
    return 1;
  v5 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "firstObject");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (unsigned __int8)_objc_msgSend(v8, "isSelected");
  v10(v6);
  return v9;
}

//----- (00000001000BB1C1) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController refreshAllModules](XinSanBanContainerController *self, SEL a2)
{

  -[HXBaseViewController setShouldRefresh:](self, "setShouldRefresh:", 0LL);
  v3 = _objc_msgSend(v2, "currentTableVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)_objc_msgSend(v4, "respondsToSelector:", "refreshAllModules");
  if ( v5 )
  {
    v7 = _objc_msgSend(v6, "currentTableVC");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "refreshAllModules");
  }
}

//----- (00000001000BB26C) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setSelectedItem:](XinSanBanContainerController *self, SEL a2, id a3)
{
  NSArray *v5; // rax
  NSArray *v6; // rax
  NSArray *v10; // rax
  unsigned __int64 v12; // r12
  SEL v27; // [rsp+40h] [rbp-100h]
  SEL v31; // [rsp+60h] [rbp-E0h]
  SEL v32; // [rsp+68h] [rbp-D8h]
  SEL v33; // [rsp+70h] [rbp-D0h]
  id obj; // [rsp+78h] [rbp-C8h]
  SEL v36; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  privateData = (void (__fastcall **)(id, bool))self->super.super.__privateData;
  v35 = v3;
  if ( privateData )
  {
    v5 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "firstObject");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    privateData[2](privateData, v8 == v3);
  }
  v26 = 0LL;
  v25 = 0LL;
  v24 = 0LL;
  v23 = 0LL;
  v10 = -[XinSanBanContainerController toolBarItems](self, "toolBarItems");
  obj = objc_retainAutoreleasedReturnValue(v10);
  v30 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v23, v37, 16LL);
  if ( v30 )
  {
    v28 = *(_QWORD *)v24;
    do
    {
      v11 = "class";
      v27 = "isKindOfClass:";
      v36 = "setIsSelected:";
      v31 = "setAllBorder:";
      v32 = "imageNamed:";
      v33 = "setImageOnRight:";
      v12 = 0LL;
      v29 = "class";
      do
      {
        if ( *(_QWORD *)v24 != v28 )
        {
          objc_enumerationMutation(obj);
          v11 = v29;
        }
        v13 = *(id *)(*((_QWORD *)&v23 + 1) + 8 * v12);
        v14 = _objc_msgSend(&OBJC_CLASS___HXButton, v11);
        v15 = v27;
        if ( (unsigned __int8)_objc_msgSend(v13, v27, v14) )
        {
          if ( v13 == v35 )
            _objc_msgSend(v35, v36, 1LL);
          else
            _objc_msgSend(v13, v36, 0LL);
        }
        else
        {
          v17 = _objc_msgSend(&OBJC_CLASS___HXTwoButton, v11);
          if ( (unsigned __int8)_objc_msgSend(v13, v15, v17) )
          {
            v18 = objc_retain(v13);
            if ( v13 == v35 )
            {
              v21 = v35;
              _objc_msgSend(v35, v31, 1LL);
              _objc_msgSend(v21, v36, 1LL);
              v22 = _objc_msgSend(&OBJC_CLASS___NSImage, v32, CFSTR("pulldownpure_white"));
              v20 = objc_retainAutoreleasedReturnValue(v22);
              _objc_msgSend(v35, v33, v20);
            }
            else
            {
              _objc_msgSend(v18, v31, 0LL);
              _objc_msgSend(v18, v36, 0LL);
              v19 = _objc_msgSend(&OBJC_CLASS___NSImage, v32, CFSTR("pulldownpure"));
              v20 = objc_retainAutoreleasedReturnValue(v19);
              _objc_msgSend(v18, v33, v20);
            }
          }
        }
        v12 = v16 + 1;
        v11 = v29;
      }
      while ( v12 < (unsigned __int64)v30 );
      v30 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v23, v37, 16LL);
    }
    while ( v30 );
  }
}

//----- (00000001000BB5DA) ----------------------------------------------------
double __cdecl -[XinSanBanContainerController widthOfString:withFont:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSDictionary *v10; // rax
  NSDictionary *v11; // rbx
  NSAttributedStringKey v14; // [rsp+10h] [rbp-40h] BYREF

  v5 = objc_retain(a3);
  objc_retain(a4);
  v6 = _objc_msgSend(v5, "length");
  if ( v7 && v6 )
  {
    v8 = objc_alloc(&OBJC_CLASS___NSAttributedString);
    v14 = NSFontAttributeName;
    v15 = v9;
    v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v15, &v14, 1LL);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v8, "initWithString:attributes:", v5, v11);
    _objc_msgSend(v12, "size");
  }
  return 0.0;
}

//----- (00000001000BB709) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController sendChangeTableUserLog:](XinSanBanContainerController *self, SEL a2, id a3)
{
  NSString *v6; // rax
  NSString *v7; // r14

  v8 = v3;
  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 && !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("新三板_%@"), v5);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
      &OBJC_CLASS___UserLogSendingQueueManager,
      "sendUserLog:action:params:needWait:",
      11LL,
      v7,
      0LL,
      1LL,
      v8);
  }
}

//----- (00000001000BB7AC) ----------------------------------------------------
HXBaseView *__cdecl -[XinSanBanContainerController topView](XinSanBanContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001000BB7C5) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setTopView:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (00000001000BB7D9) ----------------------------------------------------
HXBaseView *__cdecl -[XinSanBanContainerController viewForTable](XinSanBanContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._autounbinder);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001000BB7F2) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setViewForTable:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super._autounbinder, a3);
}

//----- (00000001000BB806) ----------------------------------------------------
id __cdecl -[XinSanBanContainerController refreshRightViewBlock](XinSanBanContainerController *self, SEL a2)
{
  return objc_getProperty(self, a2, 80LL, 0);
}

//----- (00000001000BB819) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setRefreshRightViewBlock:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 80LL);
}

//----- (00000001000BB82A) ----------------------------------------------------
id __cdecl -[XinSanBanContainerController showAndHideRightBtnBlock](XinSanBanContainerController *self, SEL a2)
{
  return objc_getProperty(self, a2, 88LL, 0);
}

//----- (00000001000BB83D) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setShowAndHideRightBtnBlock:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (00000001000BB84E) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setGuZhuanIntegratedScreenSelected:](
        XinSanBanContainerController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super.super._topLevelObjects) = a3;
}

//----- (00000001000BB85E) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setXinSanBanTabbarController:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._viewIsAppearing, a3);
}

//----- (00000001000BB872) ----------------------------------------------------
id __cdecl -[XinSanBanContainerController currentTableVC](XinSanBanContainerController *self, SEL a2)
{
  return *(id *)&self->super.super._isContentViewController;
}

//----- (00000001000BB883) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setCurrentTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._isContentViewController, a3);
}

//----- (00000001000BB897) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setChuangXinCengTableVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._reserved, a3);
}

//----- (00000001000BB8AB) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setJiChuCengTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._shouldRefresh, a3);
}

//----- (00000001000BB8BF) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setZuoShiZhuanRangTableVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._stockCode, a3);
}

//----- (00000001000BB8D3) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setXieYiZhuanRangTableVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._market, a3);
}

//----- (00000001000BB8E7) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setYouXianGuTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._contentsObjMArr, a3);
}

//----- (00000001000BB8FB) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setShouFaGuaPaiTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._controllerID, a3);
}

//----- (00000001000BB90F) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setZengFaGuaPaiTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._paramsMDic, a3);
}

//----- (00000001000BB923) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setAGuTuiShiTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_guZhuanIntegratedScreenSelected, a3);
}

//----- (00000001000BB937) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setBGuTuiShiTableVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_topView, a3);
}

//----- (00000001000BB94B) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setXinSanBanZhiShuTableVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_viewForTable, a3);
}

//----- (00000001000BB95F) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setGuZhuanIntegratedScreenVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->_refreshRightViewBlock, a3);
}

//----- (00000001000BB973) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setZuoShiZhuanRangChuangXinCengVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->_showAndHideRightBtnBlock, a3);
}

//----- (00000001000BB987) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setZuoShiZhuanRangJiChuCengVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_xinSanBanTabbarController, a3);
}

//----- (00000001000BB99B) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setXieYiZhuanRangChuangXinCengVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->_currentTableVC, a3);
}

//----- (00000001000BB9AF) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setXieYiZhuanRangJiChuCengVC:](
        XinSanBanContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_chuangXinCengTableVC, a3);
}

//----- (00000001000BB9C3) ----------------------------------------------------
NSArray *__cdecl -[XinSanBanContainerController toolBarItems](XinSanBanContainerController *self, SEL a2)
{
  return (NSArray *)self->_jiChuCengTableVC;
}

//----- (00000001000BB9D4) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setToolBarItems:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_jiChuCengTableVC, a3);
}

//----- (00000001000BB9E8) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController setNavigationBarVC:](XinSanBanContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_zuoShiZhuanRangTableVC, a3);
}

//----- (00000001000BB9FC) ----------------------------------------------------
void __cdecl -[XinSanBanContainerController .cxx_destruct](XinSanBanContainerController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_zuoShiZhuanRangTableVC, 0LL);
  objc_storeStrong((id *)&self->_jiChuCengTableVC, 0LL);
  objc_storeStrong((id *)&self->_chuangXinCengTableVC, 0LL);
  objc_storeStrong(&self->_currentTableVC, 0LL);
  objc_storeStrong((id *)&self->_xinSanBanTabbarController, 0LL);
  objc_storeStrong(&self->_showAndHideRightBtnBlock, 0LL);
  objc_storeStrong(&self->_refreshRightViewBlock, 0LL);
  objc_storeStrong((id *)&self->_viewForTable, 0LL);
  objc_storeStrong((id *)&self->_topView, 0LL);
  objc_storeStrong((id *)&self->_guZhuanIntegratedScreenSelected, 0LL);
  objc_storeStrong((id *)&self->super._paramsMDic, 0LL);
  objc_storeStrong((id *)&self->super._controllerID, 0LL);
  objc_storeStrong((id *)&self->super._contentsObjMArr, 0LL);
  objc_storeStrong((id *)&self->super._market, 0LL);
  objc_storeStrong((id *)&self->super._stockCode, 0LL);
  objc_storeStrong((id *)&self->super._shouldRefresh, 0LL);
  objc_storeStrong((id *)&self->super.super._reserved, 0LL);
  objc_storeStrong((id *)&self->super.super._isContentViewController, 0LL);
  objc_storeStrong((id *)&self->super.super._viewIsAppearing, 0LL);
  objc_storeStrong(&self->super.super.__privateData, 0LL);
  objc_storeStrong((id *)&self->super.super._designNibBundleIdentifier, 0LL);
  objc_destroyWeak(&self->super.super._autounbinder);
  objc_destroyWeak((id *)&self->super.super._editors);
}

