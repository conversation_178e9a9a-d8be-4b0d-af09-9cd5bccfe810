void __cdecl -[ZhuanQianXiaoYingGraphViewController viewDidLoad](ZhuanQianXiaoYingGraphViewController *self, SEL a2)
{
  id (*v2)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  ZhuanQianXiaoYingGraphViewController *v16; // r14
  id *p_plotItem; // r15
  id WeakRetained; // rbx
  id (*v24)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  NSNotificationName v37; // r15
  id (*v39)(id, SEL, ...); // r12
  SEL v45; // [rsp+20h] [rbp-40h]
  SEL v46; // [rsp+28h] [rbp-38h]
  SEL v47; // [rsp+30h] [rbp-30h]

  v43.receiver = self;
  v43.super_class = (Class)&OBJC_CLASS___ZhuanQianXiaoYingGraphViewController;
  -[GraphBaseViewController viewDidLoad](&v43, "viewDidLoad");
  -[GraphBaseViewController setCurrentPlotType:](self, "setCurrentPlotType:", 119LL);
  v3 = v2(self, "view");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(&OBJC_CLASS___HXBaseView, "class");
  v8 = (unsigned __int8)v7(v4, "isKindOfClass:", v6);
  if ( v8 )
  {
    v10 = v9(self, "view");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "setAllBorder:", 1LL);
    v47 = "setBorderWidth:";
    v13(v11, "setBorderWidth:", 2.0);
    v46 = "majorModuleLineColor";
    v15 = v14(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v16 = self;
    v17 = objc_retainAutoreleasedReturnValue(v15);
    v45 = "setBorderColor:";
    v18(v11, "setBorderColor:", v17);
  }
  else
  {
    v47 = "setBorderWidth:";
    v46 = "majorModuleLineColor";
    v45 = "setBorderColor:";
    v16 = self;
  }
  v44 = v16;
  p_plotItem = (id *)&v16->super._plotItem;
  WeakRetained = objc_loadWeakRetained((id *)&v16->super._plotItem);
  v21(WeakRetained, "setBottomBorder:", 1LL);
  v22 = objc_loadWeakRetained((id *)&v16->super._plotItem);
  v23(v22, v47, 1.0);
  v25 = v24(&OBJC_CLASS___HXThemeManager, v46);
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v27 = objc_loadWeakRetained(p_plotItem);
  v28(v27, v45, v26);
  v30 = v29(&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  v32 = objc_loadWeakRetained(p_plotItem);
  v33(v32, "setBackgroundColor:", v31);
  v35 = v34(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v37 = NSViewFrameDidChangeNotification;
  v38 = v44;
  v40 = v39(v44, "view");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  v42(v36, "addObserver:selector:name:object:", v38, "frameDidChaged:", v37, v41);
}

//----- (0000000100A430C3) ----------------------------------------------------
void __cdecl -[ZhuanQianXiaoYingGraphViewController dealloc](ZhuanQianXiaoYingGraphViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZhuanQianXiaoYingGraphViewController;
  -[GraphBaseViewController dealloc](&v4, "dealloc");
}

//----- (0000000100A43138) ----------------------------------------------------
void __cdecl -[ZhuanQianXiaoYingGraphViewController frameDidChaged:](
        ZhuanQianXiaoYingGraphViewController *self,
        SEL a2,
        id a3)
{
  NSView *v3; // rax
  NSView *v8; // rax
  NSView *v13; // rax
  NSView *v17; // rax
  NSView *v18; // rbx

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying", a3) )
  {
    v3 = -[GraphBaseViewController drawContentView](self, "drawContentView");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = (char *)v4;
    if ( v4 )
    {
      objc_msgSend_stret(v21, v4, "bounds");
      v7 = *(double *)(v6 + 24) + -20.0;
    }
    else
    {
      memset(v21, 0, sizeof(v21));
      v7 = -20.0;
    }
    v29 = *(_QWORD *)&v7;
    v8 = -[GraphBaseViewController drawContentView](self, "drawContentView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = (char *)v9;
    if ( v9 )
    {
      objc_msgSend_stret(v22, v9, "bounds");
      v12 = *(_QWORD *)(v11 + 16);
    }
    else
    {
      memset(v22, 0, sizeof(v22));
      v12 = 0LL;
    }
    v30 = v12;
    v13 = -[GraphBaseViewController drawContentView](self, "drawContentView");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (char *)v14;
    if ( v14 )
    {
      objc_msgSend_stret(&v23, v14, "bounds");
      v16 = *((_QWORD *)&v24 + 1);
    }
    else
    {
      v24 = 0LL;
      v23 = 0LL;
      v16 = 0LL;
    }
    v25 = 0LL;
    v26 = v29;
    v27 = v30;
    v28 = v16;
    v17 = -[GraphBaseViewController drawContentView](self, "drawContentView");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "addToolTipRect:owner:userData:", self, 0LL);
    v20(self, "updatePlotWhenParseFinished");
  }
}

//----- (0000000100A43317) ----------------------------------------------------
id __cdecl -[ZhuanQianXiaoYingGraphViewController view:stringForToolTip:point:userData:](
        ZhuanQianXiaoYingGraphViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4,
        CGPoint a5,
        void *a6)
{
  PlotBaseView *v6; // rax
  PlotBaseView *v7; // rbx

  v6 = -[GraphBaseViewController plotItem](self, "plotItem", a3, a4, a6, a5.x, a5.y);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[PlotBaseView currentTitle](v7, "currentTitle");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100A43367) ----------------------------------------------------
void __cdecl -[ZhuanQianXiaoYingGraphViewController requestData](ZhuanQianXiaoYingGraphViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSNumber *v5; // rax
  NSNumber *v6; // rbx
  NSMutableDictionary *v8; // rax
  NSMutableDictionary *v9; // r15
  DataRequestCenter *v11; // rax
  DataRequestCenter *v12; // rbx
  NSArray *v13; // rax
  DataRequestCenter *v16; // rax
  DataRequestCenter *v17; // r13
  NSArray *v18; // rax
  NSArray *v19; // rbx
  id *v20; // r12
  _QWORD v21[4]; // [rsp+50h] [rbp-A0h] BYREF
  _QWORD v23[4]; // [rsp+78h] [rbp-78h] BYREF
  id to[2]; // [rsp+98h] [rbp-58h] BYREF
  id location; // [rsp+A8h] [rbp-48h] BYREF
  NSMutableDictionary *v26; // [rsp+B0h] [rbp-40h] BYREF
  NSMutableDictionary *v27; // [rsp+B8h] [rbp-38h] BYREF

  to[1] = self;
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0x4000LL);
  objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 100LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = (NSMutableDictionary *)_objc_msgSend(
                                &OBJC_CLASS___NSMutableDictionary,
                                "dictionaryWithObjectsAndKeys:",
                                CFSTR("883913"),
                                CFSTR("CodeList"),
                                CFSTR("URFI"),
                                CFSTR("Market"),
                                CFSTR("11"),
                                CFSTR("DataTypes"),
                                v7,
                                CFSTR("Period"),
                                v4,
                                CFSTR("Start"),
                                v6,
                                CFSTR("End"),
                                0LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  objc_initWeak(&location, self);
  v11 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v27 = v9;
  v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v27, 1LL);
  objc_retainAutoreleasedReturnValue(v13);
  v23[0] = _NSConcreteStackBlock;
  v23[1] = 3254779904LL;
  v23[2] = sub_100A436F8;
  v23[3] = &unk_1012DAA70;
  objc_copyWeak(to, &location);
  -[DataRequestCenter request:type:params:callBack:fail:](
    v12,
    "request:type:params:callBack:fail:",
    1LL,
    2LL,
    v14,
    v23,
    &stru_1012E80D8);
  _objc_msgSend(v9, "setObject:forKeyedSubscript:", CFSTR("11"), CFSTR("DataTypes"));
  _objc_msgSend(v9, "setObject:forKeyedSubscript:", CFSTR("1B0016"), CFSTR("CodeList"));
  _objc_msgSend(v9, "setObject:forKeyedSubscript:", CFSTR("USHI"), CFSTR("Market"));
  v16 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v26 = v9;
  v18 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v26, 1LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21[0] = _NSConcreteStackBlock;
  v21[1] = 3254779904LL;
  v21[2] = sub_100A437C8;
  v21[3] = &unk_1012DAA70;
  objc_copyWeak(&v22, &location);
  -[DataRequestCenter request:type:params:callBack:fail:](
    v17,
    "request:type:params:callBack:fail:",
    1LL,
    2LL,
    v19,
    v21,
    &stru_1012E80F8);
  objc_destroyWeak(v20);
  objc_destroyWeak(to);
  objc_destroyWeak(&location);
}

//----- (0000000100A436F8) ----------------------------------------------------
void __fastcall sub_100A436F8(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  id *v8; // r12

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    WeakRetained = objc_loadWeakRetained((id *)(v4 + 32));
    v6 = _objc_msgSend(WeakRetained, "SGParser");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "setLongHuBangShouPanModel:", v2);
    v9 = objc_loadWeakRetained(v8);
    _objc_msgSend(v9, "updatePlotWhenParseFinished");
  }
}

//----- (0000000100A437C2) ----------------------------------------------------
void __cdecl sub_100A437C2(id a1, unsigned __int64 a2)
{
  ;
}

//----- (0000000100A437C8) ----------------------------------------------------
void __fastcall sub_100A437C8(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  id *v8; // r12

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    WeakRetained = objc_loadWeakRetained((id *)(v4 + 32));
    v6 = _objc_msgSend(WeakRetained, "SGParser");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "setLongHuBangShangZheng50ShouPanModel:", v2);
    v9 = objc_loadWeakRetained(v8);
    _objc_msgSend(v9, "updatePlotWhenParseFinished");
  }
}

//----- (0000000100A43892) ----------------------------------------------------
void __cdecl sub_100A43892(id a1, unsigned __int64 a2)
{
  ;
}

//----- (0000000100A43898) ----------------------------------------------------
id __cdecl -[ZhuanQianXiaoYingGraphViewController createDataForPlot](
        ZhuanQianXiaoYingGraphViewController *self,
        SEL a2)
{
  SimpleGraphDataParseModule *v2; // rax
  SimpleGraphDataParseModule *v3; // rbx
  NSDictionary *v6; // rax
  NSDictionary *v7; // r14
  __CFString *v9; // [rsp+0h] [rbp-30h] BYREF

  v2 = -[GraphBaseViewController SGParser](self, "SGParser");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[SimpleGraphDataParseModule getLongHuBangZhuanQianXiaoYingData](v3, "getLongHuBangZhuanQianXiaoYingData");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v9 = off_1012E3DB8;
    v10 = v5;
    v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v10, &v9, 1LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
  }
  else
  {
    v7 = 0LL;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100A43967) ----------------------------------------------------
HXBaseView *__cdecl -[ZhuanQianXiaoYingGraphViewController topView](ZhuanQianXiaoYingGraphViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._plotItem);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100A43980) ----------------------------------------------------
void __cdecl -[ZhuanQianXiaoYingGraphViewController setTopView:](
        ZhuanQianXiaoYingGraphViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._plotItem, a3);
}

//----- (0000000100A43994) ----------------------------------------------------
void __cdecl -[ZhuanQianXiaoYingGraphViewController .cxx_destruct](ZhuanQianXiaoYingGraphViewController *self, SEL a2)
{
  objc_destroyWeak((id *)&self->super._plotItem);
}

