void __cdecl -[ZhongCangStocksPoolTableViewController viewDidLoad](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhongCangStocksPoolTableViewController;
  -[QuoteBaseTableViewController viewDidLoad](&v2, "viewDidLoad");
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 328193LL);
  -[ZhongCangStocksPoolTableViewController initViewState](self, "initViewState");
}

//----- (000000010053139F) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController initViewState](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v3; // rbx

  v2 = -[ZhongCangStocksPoolTableViewController noStocksTipView](self, "noStocksTipView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setWantsLayer:", 1LL);
  v4 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = _objc_msgSend(v6, "noStocksTipView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "setBackgroundColor:", v5);
  v10 = _objc_msgSend(v9, "noStocksTipView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "setTopBorder:", 1LL);
  v13 = _objc_msgSend(v12, "noStocksTipView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setBorderWidth:", 2.0);
  v15 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = _objc_msgSend(v17, "noStocksTipView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "setBorderColor:", v16);
  v21 = _objc_msgSend(v20, "noStocksTipBtn");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = _objc_msgSend(v22, "cell");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  _objc_msgSend(v24, "setHighlightsBy:", 0LL);
  v26 = _objc_msgSend(v25, "noStocksTipBtn");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  _objc_msgSend(v27, "setTextColor:", v29);
  if ( !(unsigned __int8)_objc_msgSend(v30, "isInstructionTipShowed") )
  {
    v32 = _objc_msgSend(v31, "view");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v35 = _objc_msgSend(v34, "instructionTipView");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    _objc_msgSend(v33, "addSubview:", v36);
    v38 = _objc_msgSend(v37, "view");
    v39 = objc_retainAutoreleasedReturnValue(v38);
    _objc_msgSend(v39, "height");
    v41 = _objc_msgSend(v40, "instructionTipView");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    _objc_msgSend(v42, "height");
    v44 = _objc_msgSend(v43, "myFrozenTable");
    v45 = objc_retainAutoreleasedReturnValue(v44);
    _objc_msgSend(v45, "setHeight:", 2.0 - 2.0);
  }
}

//----- (00000001005316B1) ----------------------------------------------------
HXBaseView *__cdecl -[ZhongCangStocksPoolTableViewController instructionTipView](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  signed __int64 v11; // rdi
  NSColor *v18; // rax
  NSColor *v19; // rbx

  frozenCount = (void *)self->super._frozenCount;
  if ( !frozenCount )
  {
    v4 = _objc_msgSend(self, "view");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "height");
    v6 = _objc_msgSend(self, "view");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "width");
    v9 = objc_alloc((Class)&OBJC_CLASS___HXBaseView);
    v10 = _objc_msgSend(v9, "initWithFrame:");
    v11 = self->super._frozenCount;
    self->super._frozenCount = (signed __int64)v10;
    v12(v11);
    _objc_msgSend((id)self->super._frozenCount, "setAutoresizingMask:", 15LL);
    v13 = +[HXThemeManager tipsBgColor](&OBJC_CLASS___HXThemeManager, "tipsBgColor");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend((id)self->super._frozenCount, "setBackgroundColor:", v14);
    v15(v14);
    ((double (__fastcall *)(signed __int64, const char *))_objc_msgSend)(self->super._frozenCount, "width");
    v16 = objc_alloc(&OBJC_CLASS___NSTextField);
    v17 = _objc_msgSend(v16, "initWithFrame:");
    _objc_msgSend(v17, "setAutoresizingMask:", 14LL);
    _objc_msgSend(v17, "setDrawsBackground:", 0LL);
    _objc_msgSend(v17, "setBordered:", 0LL);
    _objc_msgSend(v17, "setEditable:", 0LL);
    _objc_msgSend(v17, "setLineBreakMode:", 4LL);
    v34 = v17;
    _objc_msgSend(
      v17,
      "setStringValue:",
      CFSTR("基金重仓股，基于同花顺人工智能技术自动筛选出基金重仓的股票。仅供参考，不构成投资建议。"));
    v18 = (NSColor *)_objc_msgSend(
                       &OBJC_CLASS___NSColor,
                       "colorWithRed:green:blue:alpha:",
                       0.09803921568627451,
                       0.09803921568627451,
                       0.09803921568627451,
                       1.0);
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v17, "setTextColor:", v19);
    v20(v19);
    ((double (__fastcall *)(signed __int64, const char *))_objc_msgSend)(self->super._frozenCount, "width");
    v21 = objc_alloc(&OBJC_CLASS___NSButton);
    v22 = _objc_msgSend(v21, "initWithFrame:");
    _objc_msgSend(v22, "setAutoresizingMask:", 9LL);
    _objc_msgSend(v22, "setBordered:", 0LL);
    v23 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("close_pure.png"));
    v24 = objc_retainAutoreleasedReturnValue(v23);
    _objc_msgSend(v22, "setImage:", v24);
    v25(v24);
    v26 = _objc_msgSend(v22, "cell");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    _objc_msgSend(v27, "setHighlightsBy:", 1LL);
    v28(v27);
    v29 = _objc_msgSend(v22, "cell");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    _objc_msgSend(v30, "setImageScaling:", 1LL);
    v31(v30);
    _objc_msgSend(v22, "setTarget:", self);
    _objc_msgSend(v22, "setAction:", "closeInstructionBtnClicked:");
    _objc_msgSend((id)self->super._frozenCount, "addSubview:", v34);
    v32(self->super._frozenCount, "addSubview:", v22);
    frozenCount = (void *)self->super._frozenCount;
  }
  return (HXBaseView *)objc_retainAutoreleaseReturnValue(frozenCount);
}

//----- (0000000100531AD4) ----------------------------------------------------
NSMutableArray *__cdecl -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSArray *orderHQDataTypes; // rdi
  NSArray *v5; // rax
  NSArray *v6; // rdi

  orderHQDataTypes = self->super._orderHQDataTypes;
  if ( !orderHQDataTypes )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (NSArray *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._orderHQDataTypes;
    self->super._orderHQDataTypes = v5;
    orderHQDataTypes = self->super._orderHQDataTypes;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(orderHQDataTypes);
}

//----- (0000000100531B25) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ZhongCangStocksPoolTableViewController backgroundCodeListSortRequestModule](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{

  cachedViewWidth = self->super._cachedViewWidth;
  if ( cachedViewWidth == 0.0 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super._cachedViewWidth;
    *(_QWORD *)&self->super._cachedViewWidth = v5;
    cachedViewWidth = self->super._cachedViewWidth;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(*(id *)&cachedViewWidth);
}

//----- (0000000100531B76) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ZhongCangStocksPoolTableViewController backgroundDetailDataRequestModule](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSTimer *requestTimerForWindowResize; // rdi
  NSTimer *v5; // rax
  NSTimer *v6; // rdi

  requestTimerForWindowResize = self->super._requestTimerForWindowResize;
  if ( !requestTimerForWindowResize )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = (NSTimer *)_objc_msgSend(v4, "init");
    v6 = self->super._requestTimerForWindowResize;
    self->super._requestTimerForWindowResize = v5;
    requestTimerForWindowResize = self->super._requestTimerForWindowResize;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(requestTimerForWindowResize);
}

//----- (0000000100531BC7) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestForMyTable](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  -[QuoteBaseTableViewController deleteOrder](self, "deleteOrder");
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    -[QuoteBaseTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
    -[ZhongCangStocksPoolTableViewController requestCompleteCodeListWithDataItems](
      self,
      "requestCompleteCodeListWithDataItems");
  }
}

//----- (0000000100531C2A) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController frameDidChange:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  HXBaseView *v4; // rax
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12

  v27.receiver = self;
  v27.super_class = (Class)&OBJC_CLASS___ZhongCangStocksPoolTableViewController;
  -[QuoteBaseTableViewController frameDidChange:](&v27, "frameDidChange:", a3);
  v4 = -[ZhongCangStocksPoolTableViewController noStocksTipView](self, "noStocksTipView");
  v28 = objc_retainAutoreleasedReturnValue(v4);
  v5(v28, "width");
  v29 = v3;
  v7 = v6(self, "noStocksTipBtn");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "width");
  v29 = (v29 - v3) * 0.5;
  v11 = v10(self, "noStocksTipBtn");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = v29;
  v14(v12, "setX:", v29);
  v16 = v15(self, "noStocksTipView");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18(v17, "height");
  v29 = v13;
  v20 = v19(self, "noStocksTipBtn");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v21, "height");
  v29 = (v29 - v13) * 0.5;
  v24 = v23(self, "noStocksTipBtn");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26(v25, "setY:", v29);
}

//----- (0000000100531DBE) ----------------------------------------------------
char __cdecl -[ZhongCangStocksPoolTableViewController isInstructionTipShowed](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "objectForKey:", off_1012DBA38[0]);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6) )
    v7 = (unsigned __int8)_objc_msgSend(v5, "containsObject:", CFSTR("ZhongCangInstructionTip"));
  else
    v7 = 0;
  return v7;
}

//----- (0000000100531E6F) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setInstructionTipShowed](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  __CFString *v4; // r15
  NSMutableArray *v11; // rax
  NSMutableArray *v12; // rbx

  v2 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = off_1012DBA38[0];
  v5 = _objc_msgSend(v3, "objectForKey:", off_1012DBA38[0]);
  objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v9, "isKindOfClass:", v8) )
  {
    v11 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v10);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v7 = v12;
  }
  _objc_msgSend(v7, "addObject:", CFSTR("ZhongCangInstructionTip"));
  v13 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setObject:forKey:", v7, v4);
}

//----- (0000000100531FA9) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setOriginalSortInfoForMyFrozenTable](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSString *v5; // rax
  NSSortDescriptor *v6; // rax
  NSSortDescriptor *v7; // r15
  NSArray *v8; // rax
  NSArray *v9; // r13
  NSString *v15; // [rsp+0h] [rbp-40h]
  NSSortDescriptor *v16; // [rsp+8h] [rbp-38h] BYREF

  -[HXBaseTableViewController setSortID:](self, "setSortID:", 199112LL);
  _objc_msgSend(v2, "setIsSetSortInfoByCode:", 1LL);
  v4 = _objc_msgSend(v3, "sortID");
  v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v4);
  v15 = objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSSortDescriptor, "sortDescriptorWithKey:ascending:", v15, 0LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v16 = v7;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v16, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v10, "myFrozenTable");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setSortDescriptors:", v9);
  _objc_msgSend(v13, "setSortOrder:", CFSTR("D"));
  _objc_msgSend(v14, "setWenCaiSortIdentifier:", 0LL);
}

//----- (0000000100532108) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController showOrHideNoStocksTipView](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v3; // rbx

  v2 = -[ZhongCangStocksPoolTableViewController noStocksTipView](self, "noStocksTipView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeFromSuperview");
  if ( !_objc_msgSend(v4, "allCodesNum") )
  {
    v6 = _objc_msgSend(v5, "myFrozenTable");
    v7 = (const char *)objc_retainAutoreleasedReturnValue(v6);
    v9 = (char *)v7;
    if ( v7 )
      objc_msgSend_stret(v18, v7, "bounds");
    else
      memset(v18, 0, 32);
    v10 = _objc_msgSend(v8, "noStocksTipView");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "setFrame:");
    v13 = _objc_msgSend(v12, "view");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v16 = _objc_msgSend(v15, "noStocksTipView");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(v14, "addSubview:", v17);
  }
}

//----- (0000000100532247) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestCompleteCodeListWithDataItems](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSString *v3; // rax
  HXSocketCenter *v4; // rax
  HXSocketCenter *v5; // rbx

  v2 = +[HXTools getTenBillionLongFromTimeStamp](&OBJC_CLASS___HXTools, "getTenBillionLongFromTimeStamp");
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("id=1610&instance=%ld&querykey=fundstocks"), v2);
  objc_retainAutoreleasedReturnValue(v3);
  v4 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXSocketCenter writeTextData:encodingType:withTimeout:delegate:instance:](
    v5,
    "writeTextData:encodingType:withTimeout:delegate:instance:",
    v6,
    1LL,
    self,
    v2,
    10.0);
}

//----- (00000001005322FD) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController receiveStuffData:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  HXSocketCenter *v3; // rax
  HXSocketCenter *v4; // rbx
  unsigned __int64 v11; // rax
  NSMutableArray *v23; // rax
  NSMutableArray *v24; // rbx

  objc_retain(a3);
  v3 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXSocketCenter removeObjectFromMapTable:](v4, "removeObjectFromMapTable:", self);
  v5 = _objc_msgSend(&OBJC_CLASS___PCTXTDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v5) )
  {
    v27 = v7;
    v8 = objc_retain(v7);
    objc_alloc(&OBJC_CLASS___NSString);
    v28 = v8;
    v9 = _objc_msgSend(v8, "txtData");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = CFStringConvertEncodingToNSStringEncoding(0x632u);
    _objc_msgSend(v12, "initWithData:encoding:", v10, v11);
    if ( !v13 )
    {
      objc_alloc(&OBJC_CLASS___NSString);
      v14 = _objc_msgSend(v8, "txtData");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v16, "initWithData:encoding:", v15, 4LL);
    }
    v17 = _objc_msgSend(v13, "dataUsingEncoding:", 4LL);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = v18;
    if ( v18 )
    {
      v20 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "JSONObjectWithData:options:error:", v18, 1LL, 0LL);
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v22 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
      if ( (unsigned __int8)_objc_msgSend(v21, "isKindOfClass:", v22) )
        -[ZhongCangStocksPoolTableViewController resetCompleteCodeListWithDataItems:](
          self,
          "resetCompleteCodeListWithDataItems:",
          v21);
    }
    v23 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25 = _objc_msgSend(v24, "count");
    -[QuoteBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v25);
    if ( (unsigned __int8)-[QuoteBaseTableViewController requestFromZero](self, "requestFromZero") )
      -[ZhongCangStocksPoolTableViewController requestBackgroundCodeListSort](self, "requestBackgroundCodeListSort");
    -[ZhongCangStocksPoolTableViewController requestCodeList](self, "requestCodeList");
    v7 = v27;
  }
}

//----- (0000000100532564) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController failToReceiveStuffData:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXSocketCenter *v3; // rax
  HXSocketCenter *v4; // rbx

  v3 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXSocketCenter removeObjectFromMapTable:](v4, "removeObjectFromMapTable:", self);
}

//----- (00000001005325B7) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController resetCompleteCodeListWithDataItems:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  unsigned __int64 v24; // r13
  id (*v27)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v42)(id, SEL, ...); // r12
  id (*v45)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  id (*v55)(id, SEL, ...); // r12

  v3 = _objc_msgSend(a3, "thsDictionaryForKey:", CFSTR("result"));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(v4, "thsArrayForKey:", CFSTR("valueorder"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(v4, "thsArrayForKey:", CFSTR("codevalue"));
  v70 = objc_retainAutoreleasedReturnValue(v9);
  if ( v10(v7, "count") )
  {
    if ( _objc_msgSend(v70, "count") )
    {
      v12 = v11(v7, "indexOfObject:", CFSTR("stock_code"));
      v63 = v13(v7, "indexOfObject:", CFSTR("marketstr"));
      v64 = v14(v7, "indexOfObject:", CFSTR("position_cap"));
      v65 = v15(v7, "indexOfObject:", CFSTR("position_stock_cnt"));
      v66 = v16(v7, "indexOfObject:", CFSTR("position_rate"));
      v18 = v17(v7, "indexOfObject:", CFSTR("position_cnt"));
      v67 = v18;
      v56 = v12;
      if ( v12 != (id)0x7FFFFFFFFFFFFFFFLL
        && v63 != (id)0x7FFFFFFFFFFFFFFFLL
        && v64 != (id)0x7FFFFFFFFFFFFFFFLL
        && v65 != (id)0x7FFFFFFFFFFFFFFFLL
        && v66 != (id)0x7FFFFFFFFFFFFFFFLL
        && v18 != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v58 = v7;
        v57 = v4;
        v20 = v19(&OBJC_CLASS___NSMutableArray, "array");
        v68 = objc_retainAutoreleasedReturnValue(v20);
        v21 = v70;
        if ( v22(v70, "count") )
        {
          v24 = 0LL;
          do
          {
            v25 = v23(v21, "thsArrayAtIndex:", v24);
            v26 = objc_retainAutoreleasedReturnValue(v25);
            v28 = v27(v26, "thsStringAtIndex:", v56);
            v29 = objc_retainAutoreleasedReturnValue(v28);
            v31 = v30(v26, "thsStringAtIndex:", v63);
            v69 = objc_retainAutoreleasedReturnValue(v31);
            v33 = v32(v26, "thsNumberAtIndex:", v64);
            v60 = objc_retainAutoreleasedReturnValue(v33);
            v35 = v34(v26, "thsNumberAtIndex:", v65);
            v61 = objc_retainAutoreleasedReturnValue(v35);
            v37 = v36(v26, "thsNumberAtIndex:", v66);
            v62 = objc_retainAutoreleasedReturnValue(v37);
            v39 = v38(v26, "thsNumberAtIndex:", v67);
            v40 = objc_retainAutoreleasedReturnValue(v39);
            v59 = v29;
            if ( v41(v29, "length") && v42(v69, "length") )
            {
              v43 = v40;
              v44 = objc_alloc((Class)&OBJC_CLASS___ZhongCangStockModel);
              v46 = v45(v44, "init");
              v47(v46, "setCode:", v59);
              v48(v46, "setMarket:", v69);
              if ( v60 )
                v49(v60, "doubleValue", 1.797693134862316e308);
              v49(v46, "setValue:", 1.797693134862316e308);
              v51 = 0x7FFFFFFFFFFFFFFFLL;
              if ( v61 )
                v51 = (__int64)v50(v61, "integerValue", 0x7FFFFFFFFFFFFFFFLL);
              v50(v46, "setCompanyCount:", v51);
              if ( v62 )
                v52(v62, "doubleValue", 1.797693134862316e308);
              v52(v46, "setRate:", 1.797693134862316e308);
              if ( v43 )
                v53(v43, "doubleValue", 1.797693134862316e308);
              v53(v46, "setStockCount:", 1.797693134862316e308);
              v54(v68, "addObject:", v46);
              v40 = v43;
            }
            ++v24;
            v21 = v70;
          }
          while ( (unsigned __int64)v55(v70, "count") > v24 );
        }
        -[ZhongCangStocksPoolTableViewController setCompleteCodeAndDataArr:](self, "setCompleteCodeAndDataArr:", v68);
        v4 = v57;
        v7 = v58;
      }
    }
  }
}

//----- (0000000100532AD6) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestCodeList](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSString *v4; // rax
  NSString *v5; // rbx
  NSString *v6; // rax
  NSString *v7; // rbx
  NSString *v14; // rax
  __CFString *v15; // rax
  __CFString *v16; // rbx
  __CFString *v17; // rdi
  NSNumber *v19; // rax
  NSNumber *v21; // rax
  NSNumber *v24; // rax
  NSNumber *v25; // r13
  NSDictionary *v26; // rax
  NSDictionary *v27; // rbx
  NSDictionary *v30; // rax
  NSDictionary *v31; // rbx
  HXTableRequestModule *v32; // rax
  HXTableRequestModule *v33; // r14
  _QWORD v36[4]; // [rsp+8h] [rbp-E8h] BYREF
  id to; // [rsp+28h] [rbp-C8h] BYREF
  __CFString *v41; // [rsp+48h] [rbp-A8h]
  id location; // [rsp+50h] [rbp-A0h] BYREF
  __CFString *v44; // [rsp+60h] [rbp-90h] BYREF
  __CFString *v45; // [rsp+68h] [rbp-88h] BYREF
  _QWORD v46[5]; // [rsp+70h] [rbp-80h] BYREF
  _QWORD v47[5]; // [rsp+98h] [rbp-58h] BYREF

  if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670
    || (v4 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier"),
        v5 = objc_retainAutoreleasedReturnValue(v4),
        v5) )
  {
    if ( _objc_msgSend(self, v3) == (id)12345670 )
    {
      v6 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
      v7 = objc_retainAutoreleasedReturnValue(v6);
      if ( v7 )
      {
        v8 = -[ZhongCangStocksPoolTableViewController getOrderCodeArrWhenSortByWenCai](
               self,
               "getOrderCodeArrWhenSortByWenCai");
        v9 = objc_retainAutoreleasedReturnValue(v8);
        v11 = v10;
        v10(self, "setOrderCodeMArray:", v9);
        v11(self, "requestDetailData");
      }
    }
  }
  else
  {
    v12 = -[ZhongCangStocksPoolTableViewController getCompleteCodeList](self, "getCompleteCodeList");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    if ( _objc_msgSend(v13, "length") )
    {
      v14 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v40 = v13;
      v16 = v15;
      v17 = CFSTR("D");
      if ( v15 )
        v17 = v15;
      v41 = objc_retain(v17);
      v46[0] = CFSTR("sortbegin");
      v18 = -[QuoteBaseTableViewController begin](self, "begin");
      v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v18);
      v38 = objc_retainAutoreleasedReturnValue(v19);
      v47[0] = v38;
      v46[1] = CFSTR("sortcount");
      v20 = -[QuoteBaseTableViewController count](self, "count");
      v21 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v20);
      v39 = objc_retainAutoreleasedReturnValue(v21);
      v47[1] = v39;
      v46[2] = CFSTR("sortorder");
      v47[2] = v41;
      v46[3] = CFSTR("sortid");
      v23 = _objc_msgSend(self, v22);
      v24 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v23);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v47[3] = v25;
      v46[4] = CFSTR("blockcodelist");
      v47[4] = v40;
      v26 = (NSDictionary *)_objc_msgSend(
                              &OBJC_CLASS___NSDictionary,
                              "dictionaryWithObjects:forKeys:count:",
                              v47,
                              v46,
                              5LL);
      v27 = objc_retainAutoreleasedReturnValue(v26);
      v43 = _objc_msgSend(v27, "mutableCopy");
      v29 = 9LL;
      if ( (__int64)_objc_msgSend(self, v28) == 84 )
      {
        v44 = CFSTR("suoShuHangYeSortType");
        v45 = CFSTR("codelist");
        v30 = (NSDictionary *)_objc_msgSend(
                                &OBJC_CLASS___NSDictionary,
                                "dictionaryWithObjects:forKeys:count:",
                                &v45,
                                &v44,
                                1LL);
        v31 = objc_retainAutoreleasedReturnValue(v30);
        _objc_msgSend(v43, "addEntriesFromDictionary:", v31);
        v29 = 20LL;
      }
      objc_initWeak(&location, self);
      v32 = -[QuoteBaseTableViewController basicHQCodeListRequestModule](self, "basicHQCodeListRequestModule");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      _objc_msgSend(v43, "copy");
      v36[0] = _NSConcreteStackBlock;
      v36[1] = 3254779904LL;
      v36[2] = sub_100532F7D;
      v36[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      -[HXTableRequestModule request:params:callBack:](v33, "request:params:callBack:", v29, v34, v36);
      objc_destroyWeak(&to);
      objc_destroyWeak(&location);
      v13 = v40;
    }
  }
}

//----- (0000000100532F7D) ----------------------------------------------------
void __fastcall sub_100532F7D(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // rbx
  id *v7; // r12
  id *v10; // r12
  id *v12; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "sortID");
  if ( v5 == (id)84 )
  {
    v6 = _objc_msgSend(v2, "mutableCopy");
    v8 = objc_loadWeakRetained(v7);
    _objc_msgSend(v8, "setOrderCodeMArray:", v6);
  }
  else
  {
    v9 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v2);
    v6 = objc_retainAutoreleasedReturnValue(v9);
    v8 = _objc_msgSend(v6, "mutableCopy");
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v8);
  }
  v13 = objc_loadWeakRetained(v12);
  _objc_msgSend(v13, "requestDetailData");
}

//----- (00000001005330AF) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController getCompleteCodeList](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // rbx
  SEL v22; // r12
  id (*v43)(id, SEL, ...); // r12
  unsigned __int64 v44; // r12
  __CFString *v46; // rcx
  unsigned __int64 v47; // r14
  SEL v52; // rbx
  id (*v58)(id, SEL, ...); // r12
  __CFString *v60; // r12
  __CFString *v62; // r12
  SEL v69; // [rsp+48h] [rbp-128h]
  SEL v70; // [rsp+50h] [rbp-120h]
  SEL v71; // [rsp+58h] [rbp-118h]
  SEL v72; // [rsp+60h] [rbp-110h]
  SEL v73; // [rsp+68h] [rbp-108h]
  SEL v74; // [rsp+70h] [rbp-100h]
  SEL v75; // [rsp+78h] [rbp-F8h]
  SEL v76; // [rsp+80h] [rbp-F0h]
  SEL v77; // [rsp+88h] [rbp-E8h]
  SEL v80; // [rsp+A0h] [rbp-D0h]
  SEL v83; // [rsp+B8h] [rbp-B8h]

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v79 = objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "count");
  if ( v5 )
  {
    v6 = self;
    v75 = "objectAtIndex:";
    v76 = "class";
    v77 = "isKindOfClass:";
    v80 = "code";
    v69 = "length";
    v83 = "market";
    v70 = "thsArrayForKey:";
    v71 = "arrayWithArray:";
    v72 = "containsObject:";
    v74 = "addObject:";
    v73 = "setObject:forKey:";
    v78 = self;
    do
    {
      v7 = _objc_msgSend(v6, "completeCodeAndDataArr");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v10 = _objc_msgSend(v8, v75, v9);
      objc_retainAutoreleasedReturnValue(v10);
      v12 = v11;
      v13 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, v76);
      v15 = (unsigned __int8)_objc_msgSend(v14, v77, v13);
      v17 = v16;
      if ( v15 )
      {
        v81 = v12;
        v18 = _objc_msgSend(v16, v80);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        if ( _objc_msgSend(v19, v69) )
        {
          v20 = _objc_msgSend(v17, v83);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          _objc_msgSend(v21, v22);
          v82 = v17;
          v12 = (char *)v81;
          if ( v23 )
          {
            v24 = _objc_msgSend(v17, v83);
            v25 = objc_retainAutoreleasedReturnValue(v24);
            v26 = _objc_msgSend(v79, v70, v25);
            v27 = objc_retainAutoreleasedReturnValue(v26);
            v29 = _objc_msgSend(v28, v71, v27);
            objc_retainAutoreleasedReturnValue(v29);
            v30 = _objc_msgSend(v82, v80);
            v31 = objc_retainAutoreleasedReturnValue(v30);
            LOBYTE(v25) = (unsigned __int8)_objc_msgSend(v32, v72, v31);
            if ( !(_BYTE)v25 )
            {
              v34 = _objc_msgSend(v17, v80);
              v35 = objc_retainAutoreleasedReturnValue(v34);
              _objc_msgSend(v36, v74, v35);
            }
            if ( _objc_msgSend(v33, "count") )
            {
              v38 = _objc_msgSend(v17, v83);
              v39 = objc_retainAutoreleasedReturnValue(v38);
              _objc_msgSend(v79, v73, v40, v39);
            }
            v17 = v82;
          }
        }
        else
        {
          v12 = (char *)v81;
        }
      }
      v81 = v12 + 1;
      v6 = v78;
      v41 = _objc_msgSend(v78, "completeCodeAndDataArr");
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v82 = v43(v42, "count");
    }
    while ( (unsigned __int64)v82 > v44 );
  }
  v68 = 0LL;
  v67 = 0LL;
  v66 = 0LL;
  v65 = 0LL;
  v45 = _objc_msgSend(v79, "allKeys");
  v83 = (SEL)objc_retainAutoreleasedReturnValue(v45);
  v78 = _objc_msgSend((id)v83, "countByEnumeratingWithState:objects:count:", &v65, v84, 16LL);
  if ( v78 )
  {
    v80 = *(SEL *)v66;
    v46 = &charsToLeaveEscaped;
    do
    {
      v75 = "thsArrayForKey:";
      v76 = "componentsJoinedByString:";
      v77 = "stringWithFormat:";
      v47 = 0LL;
      do
      {
        v81 = v46;
        if ( *(SEL *)v66 != v80 )
          objc_enumerationMutation((id)v83);
        v48 = *(_QWORD *)(*((_QWORD *)&v65 + 1) + 8 * v47);
        v49 = _objc_msgSend(v79, v75, v48);
        v82 = objc_retainAutoreleasedReturnValue(v49);
        v50 = _objc_msgSend(v82, v76, CFSTR(","));
        v51 = objc_retainAutoreleasedReturnValue(v50);
        v52 = v77;
        v54 = _objc_msgSend(v53, v77, CFSTR("%@(%@,);"), v48, v51);
        v55 = objc_retainAutoreleasedReturnValue(v54);
        v56 = v52;
        v57 = v81;
        v59 = v58(&OBJC_CLASS___NSString, v56, CFSTR("%@%@"), v81, v55);
        objc_retainAutoreleasedReturnValue(v59);
        ++v47;
        v46 = v60;
      }
      while ( v47 < (unsigned __int64)v78 );
      v61 = _objc_msgSend((id)v83, "countByEnumeratingWithState:objects:count:", &v65, v84, 16LL);
      v46 = v62;
      v78 = v61;
    }
    while ( v61 );
  }
  return objc_autoreleaseReturnValue(v63);
}

//----- (000000010053371D) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController getOrderCodeArrWhenSortByWenCai](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12

  v2 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "sortedArrayUsingComparator:");
  v46 = objc_retainAutoreleasedReturnValue(v5);
  v7 = v6(&OBJC_CLASS___NSMutableArray, "array");
  v48 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (char *)v8(self, "begin");
  v11 = v10(self, "begin");
  v12 = v46;
  if ( v9 < (char *)v13(self, "count") + (unsigned __int64)v11 )
  {
    do
    {
      if ( v9 >= v14(v12, "count") )
        break;
      v16 = v15(v12, "objectAtIndex:", v9);
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v19 = v18(&OBJC_CLASS___ZhongCangStockModel, "class");
      if ( (unsigned __int8)v20(v17, "isKindOfClass:", v19) )
      {
        v22 = v21(v17, "code");
        v23 = objc_retainAutoreleasedReturnValue(v22);
        if ( !v24(v23, "length") )
        {
          v38 = v23;
          goto LABEL_8;
        }
        v26 = v25(v17, "market");
        v47 = v17;
        v49 = v23;
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v45 = v28(v27, "length");
        if ( v45 )
        {
          v51[0] = (__int64)CFSTR("StockCode");
          v30 = v29(v17, "code");
          v50 = objc_retainAutoreleasedReturnValue(v30);
          v52[0] = (__int64)v50;
          v51[1] = (__int64)CFSTR("Market");
          v32 = v31(v17, "market");
          v33 = objc_retainAutoreleasedReturnValue(v32);
          v52[1] = (__int64)v33;
          v35 = v34(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v52, v51, 2LL);
          v36 = objc_retainAutoreleasedReturnValue(v35);
          v37(v48, "addObject:", v36);
          v17 = v47;
          v38 = v50;
LABEL_8:
        }
      }
      ++v9;
      v40 = v39(self, "begin");
      v42 = (char *)v41(self, "count") + (_QWORD)v40;
      v12 = v46;
    }
    while ( v9 < v42 );
  }
  return objc_autoreleaseReturnValue(v48);
}

//----- (0000000100533A7E) ----------------------------------------------------
id __fastcall sub_100533A7E(__int64 a1, void *a2, void *a3, double a4)
{

  objc_retain(a2);
  v5 = objc_retain(a3);
  v6 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
  v35 = v7;
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
  {
    v8 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8) )
    {
      v34 = v5;
      v9 = _objc_msgSend(*(id *)(a1 + 32), "wenCaiSortIdentifier");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("基金持股比例0"));
      v13 = *(void **)(v12 + 32);
      if ( v11 )
      {
        v14 = "rate";
        _objc_msgSend(v35, "rate");
      }
      else
      {
        v17 = _objc_msgSend(*(id *)(v12 + 32), "wenCaiSortIdentifier");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v19 = (unsigned __int8)_objc_msgSend(v18, "isEqualToString:", CFSTR("基金持股家数0"));
        v21 = *(void **)(v20 + 32);
        if ( v19 )
        {
          v22 = _objc_msgSend(v35, "companyCount");
          v5 = v34;
          v23 = _objc_msgSend(v34, "companyCount");
          v15 = _objc_msgSend(v21, "compareInteger:withInteger:", v22, v23);
          goto LABEL_9;
        }
        v26 = _objc_msgSend(*(id *)(v20 + 32), "wenCaiSortIdentifier");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = (unsigned __int8)_objc_msgSend(v27, "isEqualToString:", CFSTR("基金持股数量0"));
        v13 = *(void **)(v29 + 32);
        if ( v28 )
        {
          v14 = "stockCount";
          _objc_msgSend(v35, "stockCount");
        }
        else
        {
          v30 = _objc_msgSend(*(id *)(v29 + 32), "wenCaiSortIdentifier");
          v31 = objc_retainAutoreleasedReturnValue(v30);
          v32 = (unsigned __int8)_objc_msgSend(v31, "isEqualToString:", CFSTR("基金持股市值0"));
          if ( !v32 )
          {
            v16 = 0LL;
            v5 = v34;
            goto LABEL_10;
          }
          v13 = *(void **)(v33 + 32);
          v14 = "value";
          _objc_msgSend(v35, "value");
        }
      }
      v5 = v34;
      _objc_msgSend(v34, v14);
      v15 = _objc_msgSend(v13, "compareDouble:withDouble:", a4, a4);
LABEL_9:
      v16 = v15;
      goto LABEL_10;
    }
  }
  v16 = 0LL;
LABEL_10:
  return v16;
}

//----- (0000000100533CDB) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestDetailData](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx
  NSDictionary *v19; // rax
  NSDictionary *v20; // r13
  NSNumber *v26; // rax
  NSNumber *v27; // rbx
  NSDictionary *v32; // rax
  NSDictionary *v33; // r15
  _QWORD v46[4]; // [rsp+8h] [rbp-B8h] BYREF
  id to; // [rsp+28h] [rbp-98h] BYREF
  _QWORD v48[4]; // [rsp+30h] [rbp-90h] BYREF
  id location; // [rsp+58h] [rbp-68h] BYREF
  __CFString *v51; // [rsp+60h] [rbp-60h] BYREF

  v2 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
  {
    objc_initWeak(&location, v5);
    v7 = _objc_msgSend(v6, "basicHQDataTypes");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v8, "count");
    if ( v9 )
    {
      v11 = _objc_msgSend(v10, "stockModel");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      _objc_msgSend(v12, "setMainRequestDidBack:", 0LL);
      v53[0] = (__int64)CFSTR("datatype");
      v14 = _objc_msgSend(v13, "basicHQDataTypes");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v54[0] = (__int64)v15;
      v53[1] = (__int64)CFSTR("StockCodesAndMarket");
      v17 = _objc_msgSend(v16, "orderCodeMArray");
      v18 = objc_retain(v17);
      v54[1] = (__int64)v18;
      v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v54, v53, 2LL);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v22 = _objc_msgSend(v21, "tableRequestModule");
      v23 = objc_retain(v22);
      v46[0] = _NSConcreteStackBlock;
      v46[1] = 3254779904LL;
      v46[2] = sub_100534124;
      v46[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      _objc_msgSend(v23, "request:params:callBack:", 4LL, v20, v46);
      objc_destroyWeak(&to);
    }
    v24 = _objc_msgSend(v10, "basicHQDataTypes");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 84LL);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v28 = (unsigned __int8)_objc_msgSend(v25, "containsObject:", v27);
    if ( v28 )
    {
      v51 = CFSTR("StockCodesAndMarket");
      v30 = _objc_msgSend(v29, "orderCodeMArray");
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v52 = v31;
      v32 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v52, &v51, 1LL);
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v35 = _objc_msgSend(v34, "suoShuHangYeRequestModule");
      v36 = objc_retain(v35);
      v48[0] = _NSConcreteStackBlock;
      v48[1] = 3254779904LL;
      v48[2] = sub_1005341EF;
      v48[3] = &unk_1012DAED8;
      objc_copyWeak(&v49, &location);
      _objc_msgSend(v36, "request:params:callBack:", 11LL, v33, v48);
      objc_destroyWeak(&v49);
    }
    v37 = _objc_msgSend(v29, "iWenCaiItemParams");
    v38 = objc_retainAutoreleasedReturnValue(v37);
    v39 = _objc_msgSend(v38, "count");
    if ( v39 )
    {
      v41 = _objc_msgSend(v40, "getWenCaiDataWithOrderCodeArr");
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v44 = _objc_msgSend(v43, "stockModel");
      v45 = objc_retainAutoreleasedReturnValue(v44);
      _objc_msgSend(v45, "setIWenCaiData:", v42);
    }
    objc_destroyWeak(&location);
  }
}

//----- (0000000100534124) ----------------------------------------------------
void __fastcall sub_100534124(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // rax

  v9 = objc_retain(a3);
  v10 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setMainRequestDidBack:", 1LL);
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v7, "dealWithRequestData:extension:", v10, v9);
}

//----- (00000001005341EF) ----------------------------------------------------
void __fastcall sub_1005341EF(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithSuoShuHangYeRequest:", v2);
}

//----- (0000000100534249) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController dealWithRequestData:extension:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  HXStockModel *v6; // rax
  HXStockModel *v7; // rbx

  v4 = -[ZhongCangStocksPoolTableViewController calculateItemsWithRequestData:](
         self,
         "calculateItemsWithRequestData:",
         a3,
         a4);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[HXStockModel setMainStockTableViewDataArray:](v7, "setMainStockTableViewDataArray:", v5);
  v8(v5);
  -[QuoteBaseTableViewController reloadData:](self, "reloadData:", 0LL);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  -[QuoteBaseTableViewController setRequestFromZero:](self, "setRequestFromZero:", 0LL);
  -[QuoteBaseTableViewController setOrderCodeList](self, "setOrderCodeList");
  -[QuoteBaseTableViewController order](self, "order");
}

//----- (000000010053430B) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController calculateItemsWithRequestData:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v8; // r14
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  NSNumber *v18; // rax
  NSNumber *v19; // rbx
  NSNumber *v31; // rax
  NSNumber *v32; // rbx
  NSNumber *v37; // rax

  v4 = objc_retain(a3);
  v42 = _objc_msgSend(v4, "mutableCopy");
  v5 = "count";
  if ( _objc_msgSend(v6, "count") )
  {
    v8 = 0LL;
    v39 = v7;
    do
    {
      v9 = v7;
      v10 = _objc_msgSend(v7, "thsDictionaryAtIndex:", v8);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = v5;
      if ( _objc_msgSend(v11, v5) )
      {
        v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v17 = _objc_msgSend(v16, "thsNumberForKey:", v15);
        v40 = objc_retainAutoreleasedReturnValue(v17);
        v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v43 = v20;
        v21 = _objc_msgSend(v20, "thsNumberForKey:", v19);
        v41 = objc_retainAutoreleasedReturnValue(v21);
        v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
        if ( (unsigned __int8)_objc_msgSend(v23, "isKindOfClass:", v22) )
        {
          _objc_msgSend(v24, "doubleValue");
          if ( v3 != 4294967295.0 )
          {
            _objc_msgSend(v25, "doubleValue");
            if ( v3 != 2147483648.0 )
            {
              v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
              if ( (unsigned __int8)_objc_msgSend(v41, "isKindOfClass:", v35) )
              {
                _objc_msgSend(v41, "doubleValue");
                if ( v3 != 4294967295.0 )
                {
                  _objc_msgSend(v41, "doubleValue");
                  if ( v3 != 2147483648.0 )
                  {
                    _objc_msgSend(v41, "doubleValue");
                    if ( v3 != 0.0 )
                    {
                      _objc_msgSend(v36, "doubleValue");
                      _objc_msgSend(v41, "doubleValue");
                      _objc_msgSend(v41, "doubleValue");
                      v3 = (v3 - v3) / v3 * 100.0;
                      v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3);
                      objc_retainAutoreleasedReturnValue(v37);
                    }
                  }
                }
              }
            }
          }
        }
        v26 = _objc_msgSend(v43, "mutableCopy");
        v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
        if ( (unsigned __int8)_objc_msgSend(v28, "isKindOfClass:", v27) )
        {
          _objc_msgSend(v29, "doubleValue");
          if ( v3 != 4294967295.0 )
          {
            _objc_msgSend(v30, "doubleValue");
            if ( v3 != 2147483648.0 )
            {
              v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
              v32 = objc_retainAutoreleasedReturnValue(v31);
              _objc_msgSend(v26, "setObject:forKey:", v33, v32);
            }
          }
        }
        _objc_msgSend(v42, "setObject:atIndexedSubscript:", v26, v8);
        v9 = v39;
        v13 = "count";
        v12 = v43;
      }
      ++v8;
      v5 = v13;
    }
    while ( (unsigned __int64)_objc_msgSend(v9, v13) > v8 );
  }
  return objc_autoreleaseReturnValue(v42);
}

//----- (000000010053471E) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController getWenCaiDataWithOrderCodeArr](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // rbx
  NSMutableArray *v9; // rax
  NSMutableArray *v10; // r14
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  NSMutableArray *v23; // rax
  NSMutableArray *v24; // rbx
  bool v26; // zf
  NSMutableArray *v27; // rax
  NSMutableArray *v28; // r15
  ZhongCangStocksPoolTableViewController *v35; // r12
  ZhongCangStocksPoolTableViewController *v36; // rbx
  SEL v41; // r12
  NSMutableArray *v51; // rax
  NSMutableArray *v52; // rbx
  unsigned __int64 v54; // r12
  bool v55; // cc
  NSMutableArray *v56; // rax
  NSMutableArray *v57; // rax
  id (*v64)(id, SEL, ...); // r12
  NSNumber *v74; // rax
  NSNumber *v75; // rbx
  id (*v85)(id, SEL, ...); // r12
  SEL v93; // [rsp+B0h] [rbp-70h]

  v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v92 = objc_retainAutoreleasedReturnValue(v3);
  v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  if ( !v6 )
    return objc_autoreleaseReturnValue(v92);
  v7 = 0LL;
  do
  {
    v9 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v90 = v7;
    v12 = v11(v10, "thsDictionaryAtIndex:", v7);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    ((void (__cdecl *)(id))v8)(v10);
    v15 = v14(v13, "thsStringForKey:", CFSTR("StockCode"));
    v16 = v8;
    v17 = objc_retainAutoreleasedReturnValue(v15);
    v91 = v13;
    v19 = v18(v13, "thsStringForKey:", CFSTR("Market"));
    v96 = objc_retainAutoreleasedReturnValue(v19);
    v94 = v17;
    v20 = "length";
    v22 = v16;
    if ( !v21(v17, "length") )
      goto LABEL_15;
    v20 = "length";
    if ( !_objc_msgSend(v96, "length") )
      goto LABEL_15;
    v23 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v20 = "count";
    v25 = _objc_msgSend(v24, "count");
    v26 = v25 == 0LL;
    if ( v26 )
      goto LABEL_15;
    while ( 1 )
    {
      v27 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v30 = _objc_msgSend(v28, "objectAtIndex:", v29);
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v32 = _objc_msgSend(&OBJC_CLASS___ZhongCangStockModel, "class");
      v33 = (unsigned __int8)_objc_msgSend(v31, "isKindOfClass:", v32);
      v34 = v31;
      v36 = v35;
      if ( v33 )
      {
        v37 = _objc_msgSend(v31, "code");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        if ( !_objc_msgSend(v38, "length") )
        {
          v36 = self;
          goto LABEL_14;
        }
        v39 = _objc_msgSend(v31, "market");
        v40 = objc_retainAutoreleasedReturnValue(v39);
        _objc_msgSend(v40, v41);
        v42 = v38;
        v36 = self;
        if ( v43 )
        {
          v44 = _objc_msgSend(v34, "code");
          v45 = objc_retainAutoreleasedReturnValue(v44);
          if ( !(unsigned __int8)_objc_msgSend(v94, "isEqualToString:", v45) )
          {
            goto LABEL_14;
          }
          v47 = _objc_msgSend(v34, "market");
          v97 = v34;
          v48 = objc_retainAutoreleasedReturnValue(v47);
          v49 = (unsigned __int8)_objc_msgSend(v96, "isEqualToString:", v48);
          v36 = self;
          if ( v49 )
            break;
        }
      }
LABEL_14:
      v51 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](v36, "completeCodeAndDataArr");
      v52 = objc_retainAutoreleasedReturnValue(v51);
      v53 = _objc_msgSend(v52, "count");
      v20 = "completeCodeAndDataArr";
      v55 = (unsigned __int64)v53 <= v54;
      if ( v55 )
        goto LABEL_15;
    }
    v60 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
            &OBJC_CLASS___HXTools,
            "getMarketAndCodeByAppendingMarket:andStockCode:",
            v96,
            v94);
    v61 = (char *)objc_retainAutoreleasedReturnValue(v60);
    v20 = "length";
    if ( _objc_msgSend(v61, "length") )
    {
      v62 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v65 = v64(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v66 = objc_retainAutoreleasedReturnValue(v65);
      v95 = v63;
      v93 = v61;
      v67(v63, "setObject:forKey:", v61, v66);
      v68 = v97;
      v69(v97, "value");
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v97, "value");
        v71 = _objc_msgSend(v70, "numberWithDouble:");
        v72 = objc_retainAutoreleasedReturnValue(v71);
        _objc_msgSend(v95, "setObject:forKey:", v72, CFSTR("基金持股市值0"));
      }
      if ( _objc_msgSend(v97, "companyCount") != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v73 = _objc_msgSend(v97, "companyCount");
        v74 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v73);
        v75 = objc_retainAutoreleasedReturnValue(v74);
        _objc_msgSend(v76, "setObject:forKey:", v75, CFSTR("基金持股家数0"));
        v68 = v97;
      }
      v77 = v68;
      _objc_msgSend(v68, "rate");
      v79 = v78;
      v80 = "stockCount";
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v77, "rate");
        v82 = _objc_msgSend(v81, "numberWithDouble:");
        v83 = objc_retainAutoreleasedReturnValue(v82);
        _objc_msgSend(v79, "setObject:forKey:", v83, CFSTR("基金持股比例0"));
      }
      _objc_msgSend(v77, v80);
      if ( v2 != 1.797693134862316e308 )
      {
        _objc_msgSend(v77, v84);
        v86 = v85(&OBJC_CLASS___NSNumber, "numberWithDouble:");
        v87 = objc_retainAutoreleasedReturnValue(v86);
        v88(v79, "setObject:forKey:", v87, CFSTR("基金持股数量0"));
      }
      v20 = "addObject:";
      _objc_msgSend(v92, "addObject:", v79);
      v61 = (char *)v93;
    }
LABEL_15:
    ((void (__fastcall *)(id, const char *))v22)(v96, v20);
    ((void (__fastcall *)(id))v22)(v94);
    ((void (__fastcall *)(id))v22)(v91);
    v7 = v90 + 1;
    v56 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v57 = objc_retainAutoreleasedReturnValue(v56);
    v8 = v22;
    v58 = _objc_msgSend(v57, "count");
    ((void (__fastcall *)(__int64))v8)(v59);
  }
  while ( (unsigned __int64)v58 > v90 + 1 );
  return objc_autoreleaseReturnValue(v92);
}

//----- (0000000100534EC9) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestBackgroundCodeListSort](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  NSString *v5; // rax
  NSString *v6; // rbx
  NSString *v7; // rax
  __CFString *v8; // rax
  __CFString *v9; // rbx
  __CFString *v10; // rdi
  NSNumber *v11; // rax
  NSNumber *v12; // rax
  NSNumber *v13; // rbx
  SEL v14; // r12
  NSNumber *v16; // rax
  NSNumber *v17; // r13
  NSDictionary *v18; // rax
  HXTableRequestModule *v21; // rax
  HXTableRequestModule *v22; // r15
  _QWORD v27[4]; // [rsp+8h] [rbp-C8h] BYREF
  id to; // [rsp+28h] [rbp-A8h] BYREF
  id location; // [rsp+48h] [rbp-88h] BYREF

  v3 = -[ZhongCangStocksPoolTableViewController getCompleteCodeList](self, "getCompleteCodeList");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( _objc_msgSend(v4, "length") )
  {
    if ( -[HXBaseTableViewController sortID](self, "sortID") != (id)12345670 )
    {
      v5 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
      v6 = objc_retainAutoreleasedReturnValue(v5);
      if ( !v6 )
      {
        objc_initWeak(&location, self);
        v7 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
        v8 = objc_retainAutoreleasedReturnValue(v7);
        v9 = v8;
        v10 = CFSTR("D");
        if ( v8 )
          v10 = v8;
        v31 = objc_retain(v10);
        v33[0] = (__int64)CFSTR("sortbegin");
        v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
        v29 = objc_retainAutoreleasedReturnValue(v11);
        v34[0] = (__int64)v29;
        v33[1] = (__int64)CFSTR("sortcount");
        v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
        v13 = objc_retain(v12);
        v34[1] = (__int64)v13;
        v33[2] = (__int64)CFSTR("sortorder");
        v34[2] = (__int64)v31;
        v33[3] = (__int64)CFSTR("sortid");
        v15 = _objc_msgSend(self, v14);
        v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v15);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v34[3] = (__int64)v17;
        v33[4] = (__int64)CFSTR("blockcodelist");
        v34[4] = (__int64)v4;
        v18 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v34, v33, 5LL);
        v30 = objc_retainAutoreleasedReturnValue(v18);
        v19(v13);
        v20(v29);
        v21 = -[ZhongCangStocksPoolTableViewController backgroundCodeListSortRequestModule](
                self,
                "backgroundCodeListSortRequestModule");
        v22 = objc_retain(v21);
        v27[0] = _NSConcreteStackBlock;
        v27[1] = 3254779904LL;
        v27[2] = sub_1005351D1;
        v27[3] = &unk_1012DAED8;
        objc_copyWeak(&to, &location);
        v23 = v30;
        -[HXTableRequestModule request:params:callBack:](v22, "request:params:callBack:", 9LL, v30, v27);
        v24(v22);
        objc_destroyWeak(&to);
        v25(v23);
        v26(v31);
        objc_destroyWeak(&location);
      }
    }
  }
}

//----- (00000001005351D1) ----------------------------------------------------
void __fastcall sub_1005351D1(__int64 a1, __int64 a2)
{
  id WeakRetained; // rbx

  v2 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", a2);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "requestBackgroundDetailDataWithCodeList:", v3);
}

//----- (0000000100535242) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController requestBackgroundDetailDataWithCodeList:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  NSArray *v9; // rax
  NSDictionary *v10; // rax
  NSDictionary *v11; // r15
  HXTableRequestModule *v13; // rax
  _QWORD v16[4]; // [rsp+0h] [rbp-80h] BYREF
  id to; // [rsp+20h] [rbp-60h] BYREF
  id location; // [rsp+28h] [rbp-58h] BYREF
  _QWORD v20[2]; // [rsp+40h] [rbp-40h] BYREF

  v3 = objc_retain(a3);
  v4 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( _objc_msgSend(v5, "count") )
  {
    _objc_msgSend(v3, v6);
    v7 = v3;
    if ( v8 )
    {
      objc_initWeak(&location, self);
      v19[0] = (__int64)CFSTR("datatype");
      v9 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
      v20[0] = objc_retainAutoreleasedReturnValue(v9);
      v19[1] = (__int64)CFSTR("StockCodesAndMarket");
      v20[1] = v3;
      v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v20, v19, 2LL);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = -[ZhongCangStocksPoolTableViewController backgroundDetailDataRequestModule](
              self,
              "backgroundDetailDataRequestModule");
      objc_retain(v13);
      v16[0] = _NSConcreteStackBlock;
      v16[1] = 3254779904LL;
      v16[2] = sub_10053543E;
      v16[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      _objc_msgSend(v14, "request:params:callBack:", 4LL, v11, v16);
      objc_destroyWeak(&to);
      objc_destroyWeak(&location);
    }
  }
  else
  {
    v7 = v3;
  }
}

//----- (000000010053543E) ----------------------------------------------------
__int64 __fastcall sub_10053543E(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  id (*v4)(id, SEL, ...); // r12

  v2 = _objc_msgSend(a2, "copy");
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v5 = v4(WeakRetained, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setBackgroundData:", v2);
  v8(WeakRetained);
  return v9(v2);
}

//----- (00000001005354C0) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController myTableIsDoubleClicked:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  HXFrozenTableView *v4; // rax
  HXFrozenTableView *v5; // rbx
  _BYTE *v6; // r14
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx
  HXStockModel *v10; // rax
  HXStockModel *v11; // r14
  NSArray *v12; // rax
  NSArray *v13; // rbx
  NSNumber *v18; // rax
  NSNumber *v19; // rbx
  __CFString *v25; // rbx
  __CFString *v26; // r14
  __CFString *v27; // rdi
  NSString *v28; // rax
  __CFString *v29; // rax
  __CFString *v30; // rbx
  NSString *v31; // rax
  __CFString *v32; // rax
  __CFString *v33; // rbx
  __CFString *v34; // rdi
  NSNumber *v37; // rax
  NSNumber *v40; // rax
  _QWORD *v41; // r12
  NSNumber *v43; // rax
  NSNumber *v45; // rax
  SEL v46; // r12
  NSNumber *v48; // rax
  NSNumber *v50; // rax
  NSMutableArray *v51; // rax
  NSMutableArray *v52; // rbx
  NSDictionary *v53; // rax
  NSDictionary *v54; // r14
  SEL v60; // r12
  NSDictionary *v64; // rdi
  __CFString *v68; // [rsp+10h] [rbp-130h]
  __CFString *v69; // [rsp+18h] [rbp-128h]
  NSNumber *v70; // [rsp+20h] [rbp-120h]
  NSNumber *v71; // [rsp+28h] [rbp-118h]
  NSNumber *v72; // [rsp+30h] [rbp-110h]
  NSNumber *v73; // [rsp+38h] [rbp-108h]
  __CFString *v75; // [rsp+48h] [rbp-F8h]
  NSNumber *v77; // [rsp+58h] [rbp-E8h]
  _QWORD v78[11]; // [rsp+60h] [rbp-E0h] BYREF
  _QWORD v79[11]; // [rsp+B8h] [rbp-88h] BYREF

  -[QuoteBaseTableViewController setRequestRowRange](self, "setRequestRowRange", a3);
  v4 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXFrozenTableView selectedRow](v5, "selectedRow");
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8);
  if ( v8 )
  {
    if ( v6 == (_BYTE *)-1LL )
      return;
    v76 = (char *)(v6 - (_BYTE *)-[QuoteBaseTableViewController begin](self, "begin"));
  }
  else
  {
    v76 = -[QuoteBaseTableViewController begin](self, "begin");
  }
  v10 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = -[HXStockModel mainStockTableViewDataArray](v11, "mainStockTableViewDataArray");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "thsDictionaryAtIndex:", v76);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(v13);
  v17(v11);
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v15, "thsStringForKey:", v19);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22(v19);
  if ( _objc_msgSend(v21, "length") )
  {
    v24 = -[ZhongCangStocksPoolTableViewController tableKey](self, "tableKey");
    v25 = (__CFString *)objc_retainAutoreleasedReturnValue(v24);
    v74 = v21;
    v26 = &charsToLeaveEscaped;
    v27 = v25;
    if ( !v25 )
      v27 = &charsToLeaveEscaped;
    v68 = objc_retain(v27);
    v28 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v30 = v29;
    if ( v29 )
      v26 = v29;
    v75 = objc_retain(v26);
    v31 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33 = v32;
    v34 = CFSTR("D");
    if ( v32 )
      v34 = v32;
    v69 = objc_retain(v34);
    v78[0] = CFSTR("tablekey");
    v79[0] = v35;
    v78[1] = CFSTR("TableID");
    v36 = -[QuoteBaseTableViewController tableID](self, "tableID");
    v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v36);
    v70 = objc_retainAutoreleasedReturnValue(v37);
    *(_QWORD *)(v38 + 8) = v70;
    v78[2] = CFSTR("sortid");
    v39 = -[HXBaseTableViewController sortID](self, "sortID");
    v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v39);
    v71 = objc_retainAutoreleasedReturnValue(v40);
    v41[2] = v71;
    v78[3] = CFSTR("IWenCaiSortIdentifier");
    v41[3] = v75;
    v78[4] = CFSTR("sortorder");
    v41[4] = v69;
    v78[5] = CFSTR("sortbegin");
    v42 = -[QuoteBaseTableViewController begin](self, "begin");
    v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v42);
    v72 = objc_retainAutoreleasedReturnValue(v43);
    v79[5] = v72;
    v78[6] = CFSTR("sortcount");
    v44 = -[QuoteBaseTableViewController count](self, "count");
    v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v44);
    v73 = objc_retainAutoreleasedReturnValue(v45);
    v79[6] = v73;
    v78[7] = CFSTR("Index");
    v47 = _objc_msgSend(self, v46);
    v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedLong:", &v76[(_QWORD)v47]);
    v77 = objc_retainAutoreleasedReturnValue(v48);
    v79[7] = v77;
    v78[8] = CFSTR("SelectedCode");
    v79[8] = v74;
    v78[9] = CFSTR("totalnumber");
    v49 = -[QuoteBaseTableViewController allCodesNum](self, "allCodesNum");
    v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v49);
    v79[9] = objc_retainAutoreleasedReturnValue(v50);
    v78[10] = CFSTR("CompleteCodeAndDataArr");
    v51 = -[ZhongCangStocksPoolTableViewController completeCodeAndDataArr](self, "completeCodeAndDataArr");
    v52 = objc_retainAutoreleasedReturnValue(v51);
    v79[10] = v52;
    v53 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v79, v78, 11LL);
    v54 = objc_retainAutoreleasedReturnValue(v53);
    v56 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v57 = objc_retainAutoreleasedReturnValue(v56);
    _objc_msgSend(v57, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
    v58 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v59 = objc_retainAutoreleasedReturnValue(v58);
    _objc_msgSend(v59, v60, CFSTR("DeliverQuotationTableDataNotification"), v54);
    v61(v69);
    v62(v75);
    v63(v59);
    v64 = v54;
    v21 = v74;
    v65(v64);
    v66(v68);
  }
  v23(v21);
  v67(v15);
}

//----- (0000000100535AC8) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController actionForTableViewSelectionDidChange:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v3; // rax
  HXStockModel *v4; // r13
  NSArray *v5; // rax
  NSArray *v6; // r14
  NSArray *v7; // rax
  NSArray *v8; // rbx
  ZhongCangStocksPoolTableViewController *v11; // r13
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  NSDictionary *v26; // rax
  NSDictionary *v27; // r13
  SEL v28; // r12
  NSDictionary *v31; // rdi
  NSMutableArray *v33; // rax
  NSMutableArray *v34; // rbx
  NSIndexSet *v36; // rax
  NSIndexSet *v37; // r14
  _BYTE *v40; // rax
  NSNumber *v43; // rax
  NSNumber *v44; // r13
  NSMutableArray *v49; // rax
  NSMutableArray *v50; // rbx
  NSIndexSet *v52; // rax
  NSIndexSet *v53; // rbx
  NSMutableArray *v57; // rax
  NSMutableArray *v58; // rbx
  NSMutableArray *v63; // rax
  NSMutableArray *v64; // rbx
  ZhongCangStocksPoolTableViewController *v65; // r15
  NSMutableArray *v66; // rax
  NSArray *v74; // [rsp+20h] [rbp-A0h]
  NSMutableArray *v80; // [rsp+38h] [rbp-88h]
  _QWORD v81[3]; // [rsp+40h] [rbp-80h] BYREF
  _QWORD v82[3]; // [rsp+58h] [rbp-68h] BYREF
  _QWORD v83[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v84[2]; // [rsp+80h] [rbp-40h] BYREF

  v3 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXStockModel mainStockTableViewDataArray](v4, "mainStockTableViewDataArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v4);
  v74 = v8;
  v10 = _objc_msgSend(v8, "count");
  if ( (unsigned __int64)a3 <= 0x7FFFFFFFFFFFFFFELL && v10 )
  {
    v11 = self;
    v12 = -[QuoteBaseTableViewController begin](self, "begin");
    v13 = _objc_msgSend(v8, "thsDictionaryAtIndex:", a3 - (_QWORD)v12);
    v78 = objc_retainAutoreleasedReturnValue(v13);
    v14 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(v78, "thsStringForKey:", v15);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18(v15);
    v19(v78);
    v76 = v17;
    if ( v17 )
    {
      v20 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v17);
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v22 = +[HXTools marketConvertToTablePanKouType:](&OBJC_CLASS___HXTools, "marketConvertToTablePanKouType:", v21);
      v23 = objc_retainAutoreleasedReturnValue(v22);
      if ( v23 )
      {
        v24 = -[QuoteBaseTableViewController refreshRightViewBlock](self, "refreshRightViewBlock");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        if ( v25 )
        {
          v83[0] = CFSTR("PanKouLinkCode");
          v84[0] = v76;
          v83[1] = CFSTR("PanKouModularType");
          v84[1] = v23;
          v26 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v84, v83, 2LL);
          v27 = objc_retainAutoreleasedReturnValue(v26);
          v29 = _objc_msgSend(self, v28);
          v30 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v29);
          v30[2](v30, v27);
          v31 = v27;
          v11 = self;
        }
      }
      v32(v21);
    }
    v33 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](v11, "selfStockCodesToBeAddMArr");
    v34 = objc_retainAutoreleasedReturnValue(v33);
    _objc_msgSend(v34, "removeAllObjects");
    v35(v34);
    v36 = (NSIndexSet *)-[HXBaseTableViewController selectedRowIndexs](v11, "selectedRowIndexs");
    v37 = objc_retainAutoreleasedReturnValue(v36);
    v38 = _objc_msgSend(v37, "firstIndex");
    v39(v37);
    while ( v38 != (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v40 = (_BYTE *)-[QuoteBaseTableViewController begin](v11, "begin");
      v79 = v38;
      v41 = _objc_msgSend(v74, "thsDictionaryAtIndex:", (_BYTE *)v38 - v40);
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v43 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v44 = objc_retainAutoreleasedReturnValue(v43);
      v45 = _objc_msgSend(v42, "thsStringForKey:", v44);
      v46 = objc_retainAutoreleasedReturnValue(v45);
      v47(v44);
      v48(v42);
      if ( (__int64)_objc_msgSend(v46, "length") )
      {
        v49 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](
                                  self,
                                  "selfStockCodesToBeAddMArr");
        v50 = objc_retainAutoreleasedReturnValue(v49);
        _objc_msgSend(v50, "addObject:", v46);
        v51(v50);
      }
      v52 = (NSIndexSet *)-[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
      v53 = objc_retainAutoreleasedReturnValue(v52);
      v54 = _objc_msgSend(v53, "indexGreaterThanIndex:", v79);
      v55(v53);
      v38 = v54;
      v11 = self;
      v56(v46);
    }
    v57 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](v11, "selfStockCodesToBeAddMArr");
    v58 = objc_retainAutoreleasedReturnValue(v57);
    v59 = _objc_msgSend(v58, "count");
    -[HXBaseTableViewController setIsBatchAddOperation:](v11, "setIsBatchAddOperation:", (unsigned __int64)v59 > 1);
    v60 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v76);
    v61 = objc_retainAutoreleasedReturnValue(v60);
    v62 = v61;
    if ( v76 )
    {
      if ( v61 )
      {
        v63 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](v11, "selfStockCodesToBeAddMArr");
        v64 = objc_retainAutoreleasedReturnValue(v63);
        if ( v64 )
        {
          -[HXBaseTableViewController setSelectedCode:](v11, "setSelectedCode:", v76);
          v65 = v11;
          v81[0] = CFSTR("StockCode");
          v82[0] = v62;
          v81[1] = off_1012E0FA8;
          v82[1] = v76;
          v81[2] = off_1012E0FA0[0];
          v66 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](
                                    v11,
                                    "selfStockCodesToBeAddMArr");
          v80 = objc_retainAutoreleasedReturnValue(v66);
          v82[2] = v80;
          v68 = (void *)v67(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v82, v81, 3LL);
          v69 = objc_retainAutoreleasedReturnValue(v68);
          v71 = (void *)v70(v65, "myFrozenTable");
          v72 = objc_retainAutoreleasedReturnValue(v71);
          v73(v72, "setParamsDic:", v69);
        }
      }
    }
  }
}

//----- (00000001005360DF) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController closeInstructionBtnClicked:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // rbx
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12

  v3 = -[ZhongCangStocksPoolTableViewController instructionTipView](self, "instructionTipView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5(v4, "removeFromSuperview");
  v7 = v6(self, "view");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "height");
  v11 = v10(self, "myFrozenTable");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13(v12, "setHeight:");
  v15 = v14(self, "view");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17(v16, "height");
  v19 = v18(self, "noStocksTipView");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21(v20, "setHeight:");
  v22(self, "setInstructionTipShowed");
}

//----- (0000000100536203) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController frozenTableView:menuForHeaderOfTableColumn:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  return 0LL;
}

//----- (000000010053620B) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController frozenTableView:dataCellForTableColumn:row:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXStockModel *v5; // rax
  HXStockModel *v6; // r14
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  ZhongCangStocksPoolTableViewController *v18; // r14
  ZhongCangStocksPoolTableViewController *v24; // r12
  ZhongCangStocksPoolTableViewController *v33; // rax
  ZhongCangStocksPoolTableViewController *v39; // rbx
  ZhongCangStocksPoolTableViewController *v45; // rbx
  signed __int64 v49; // r12
  ZhongCangStocksPoolTableViewController *v61; // rbx
  ZhongCangStocksPoolTableViewController *v73; // r12
  ZhongCangStocksPoolTableViewController *v93; // [rsp+38h] [rbp-38h]
  ZhongCangStocksPoolTableViewController *v94; // [rsp+38h] [rbp-38h]
  ZhongCangStocksPoolTableViewController *v95; // [rsp+38h] [rbp-38h]
  ZhongCangStocksPoolTableViewController *v96; // [rsp+40h] [rbp-30h]

  v89 = objc_retain(a4);
  v5 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "mainStockTableViewDataArray");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = v10(v9, "count");
  if ( v11 )
  {
    v13 = v12(&OBJC_CLASS___HXTableColumn, "class");
    v14 = v89;
    if ( (unsigned __int8)v15(v89, "isKindOfClass:", v13) )
    {
      v17 = v16;
      v18 = (ZhongCangStocksPoolTableViewController *)objc_retain(v89);
      if ( _objc_msgSend(v18, "tableHeaderItemType") )
      {
        if ( _objc_msgSend(v18, v19) != (id)1 )
        {
          v29 = 0LL;
          goto LABEL_31;
        }
        v20 = (void *)v17(self, "iWenCaiItemParams");
        v21 = objc_retainAutoreleasedReturnValue(v20);
        v22 = (void *)((__int64 (__fastcall *)(id, const char *, __CFString *))v17)(
                        v21,
                        "thsArrayForKey:",
                        CFSTR("IWenCaiIdentifier"));
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v96 = v24;
        v25 = (void *)((__int64 (__fastcall *)(ZhongCangStocksPoolTableViewController *))v17)(v24);
        v26 = objc_retainAutoreleasedReturnValue(v25);
        v27 = v17;
        ((void (__fastcall *)(id, const char *, id))v17)(v23, "containsObject:", v26);
        if ( !v28 )
        {
LABEL_11:
          v29 = 0LL;
          v14 = v89;
          v18 = v96;
LABEL_31:
          goto LABEL_32;
        }
      }
      else
      {
        v30 = (void *)v17(self, "basicHQDataTypes");
        v31 = objc_retainAutoreleasedReturnValue(v30);
        v32 = (void *)v17(v18, "identifier");
        v33 = objc_retainAutoreleasedReturnValue(v32);
        v34 = v17(v33, "integerValue");
        v35 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v17)(
                        &OBJC_CLASS___NSNumber,
                        "numberWithInteger:",
                        v34);
        v36 = objc_retainAutoreleasedReturnValue(v35);
        v90 = ((__int64 (__fastcall *)(id, const char *, id))v17)(v31, "containsObject:", v36);
        v96 = v18;
        v38 = (void *)v17(v18, "identifier");
        v39 = objc_retainAutoreleasedReturnValue(v38);
        v27 = v17;
        v17(v39, "integerValue");
        if ( !v90 && v40 != 12345670 )
          goto LABEL_11;
      }
      v41 = (void (__fastcall *)(id, const char *, id))v27;
      v42 = (void *)v27(v96, "dataCell");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v44 = (void *)v27(v96, "headerCell");
      v45 = objc_retainAutoreleasedReturnValue(v44);
      v46 = v27(v45, "alignment");
      v91 = v43;
      ((void (__fastcall *)(id, const char *, __int64))v27)(v43, "setTextAlignment:", v46);
      v27(self, "begin");
      v47 = v27(self, "begin");
      v48 = v27(self, "count");
      if ( v49 > a5 || v47 + v48 + 50 <= a5 )
      {
        v60 = (void *)v27(v96, "identifier");
        v61 = objc_retainAutoreleasedReturnValue(v60);
        v62 = v27(v61, "integerValue");
        if ( v62 == 12345670 )
        {
          v64 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __CFString *, __int64))v27)(
                          &OBJC_CLASS___NSString,
                          "stringWithFormat:",
                          CFSTR("%ld"),
                          v63 + 1);
          v65 = objc_retainAutoreleasedReturnValue(v64);
          v29 = v91;
          v41(v91, "setText:", v65);
          v66 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *))v41)(
                          &OBJC_CLASS___HXThemeManager,
                          "normalTextColor");
          v67 = objc_retainAutoreleasedReturnValue(v66);
          v41(v91, "setTextColor:", v67);
        }
        else
        {
          v29 = v91;
          _objc_msgSend(v91, "setText:", &charsToLeaveEscaped);
        }
        v18 = v96;
        goto LABEL_30;
      }
      v50 = -[QuoteBaseTableViewController begin](self, "begin");
      v51 = "tableHeaderItemType";
      if ( (unsigned __int64)v50 > a5
        || (v92 = (id)v27(self, "begin"), (unsigned __int64)v92 + v27(self, "count") <= a5) )
      {
        if ( !_objc_msgSend(v96, v51) )
        {
          v69 = (void *)v27(self, "stockModel");
          v94 = objc_retainAutoreleasedReturnValue(v69);
          v70 = (void *)v27(v94, "backgroundData");
          v71 = objc_retainAutoreleasedReturnValue(v70);
          v29 = v91;
          ((void (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *, id, ZhongCangStocksPoolTableViewController *, signed __int64, signed __int64, id))v41)(
            self,
            "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
            v91,
            v96,
            a5,
            a5,
            v71);
          v72 = v71;
          v18 = v73;
LABEL_30:
          v14 = v89;
          goto LABEL_31;
        }
      }
      v18 = v96;
      if ( _objc_msgSend(v96, v51) )
      {
        if ( _objc_msgSend(v96, v52) == (id)1 )
        {
          v27(self, "begin");
          v53 = (void *)v27(self, "stockModel");
          v93 = objc_retainAutoreleasedReturnValue(v53);
          v54 = (void *)v27(v93, "iWenCaiData");
          v55 = objc_retainAutoreleasedReturnValue(v54);
          v56 = (void *)((__int64 (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *))v41)(
                          self,
                          "tableKey");
          v57 = objc_retainAutoreleasedReturnValue(v56);
          ((void (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *, id, ZhongCangStocksPoolTableViewController *, signed __int64, __int64, id, id))v41)(
            self,
            "reloadCellViewForIWenCaiColumnItem:column:rowInTable:indexInDataSource:dataSource:forTableKey:",
            v91,
            v96,
            a5,
            v58,
            v55,
            v57);
          v18 = v96;
          v59 = v55;
LABEL_28:
        }
      }
      else
      {
        v27(self, "begin");
        v74 = (void *)v27(self, "stockModel");
        v95 = objc_retainAutoreleasedReturnValue(v74);
        v75 = (void *)v27(v95, "mainStockTableViewDataArray");
        v76 = objc_retainAutoreleasedReturnValue(v75);
        ((void (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *, id, ZhongCangStocksPoolTableViewController *, signed __int64, __int64, id))v27)(
          self,
          "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
          v91,
          v96,
          a5,
          v77,
          v76);
        v78 = (void *)v27(self, "basicHQDataTypes");
        v79 = objc_retainAutoreleasedReturnValue(v78);
        v80 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v41)(
                        &OBJC_CLASS___NSNumber,
                        "numberWithInt:",
                        84LL);
        v81 = objc_retainAutoreleasedReturnValue(v80);
        v41(v79, "containsObject:", v81);
        v18 = v96;
        if ( v82 )
        {
          ((void (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *))v41)(self, "begin");
          v83 = (void *)((__int64 (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *))v41)(
                          self,
                          "stockModel");
          v93 = objc_retainAutoreleasedReturnValue(v83);
          v84 = (void *)((__int64 (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *))v41)(
                          v93,
                          "suoShuHangYeData");
          v85 = objc_retainAutoreleasedReturnValue(v84);
          ((void (__fastcall *)(ZhongCangStocksPoolTableViewController *, const char *, id, ZhongCangStocksPoolTableViewController *, signed __int64, __int64, id))v41)(
            self,
            "reloadCellViewForSpecialColumnItem:column:rowInTable:indexInDataSource:dataSource:",
            v91,
            v96,
            a5,
            v86,
            v85);
          v59 = v85;
          v18 = v96;
          goto LABEL_28;
        }
      }
      v29 = v91;
      goto LABEL_30;
    }
    v29 = 0LL;
  }
  else
  {
    v29 = 0LL;
    v14 = v89;
  }
LABEL_32:
  return objc_autoreleaseReturnValue(v29);
}

//----- (0000000100536983) ----------------------------------------------------
signed __int64 __cdecl -[ZhongCangStocksPoolTableViewController numberOfRowsInFrozenTableView:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  -[ZhongCangStocksPoolTableViewController showOrHideNoStocksTipView](self, "showOrHideNoStocksTipView", a3);
  return (signed __int64)-[QuoteBaseTableViewController allCodesNum](self, "allCodesNum");
}

//----- (00000001005369B1) ----------------------------------------------------
id __cdecl -[ZhongCangStocksPoolTableViewController tableKey](ZhongCangStocksPoolTableViewController *self, SEL a2)
{

  begin = (void *)self->super._begin;
  if ( !begin )
  {
    self->super._begin = (unsigned __int64)CFSTR("__zhongCangStocksPoolTableKey");
    begin = (void *)self->super._begin;
  }
  return objc_retainAutoreleaseReturnValue(begin);
}

//----- (00000001005369E9) ----------------------------------------------------
signed __int64 __cdecl -[ZhongCangStocksPoolTableViewController frozenCount](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  return 3LL;
}

//----- (00000001005369F4) ----------------------------------------------------
signed __int64 __cdecl -[ZhongCangStocksPoolTableViewController compareDouble:withDouble:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        double a3,
        double a4)
{
  NSString *v4; // rax
  NSString *v5; // rbx
  NSString *v12; // rax
  NSString *v13; // rbx
  signed __int64 v15; // r12
  __m128d v18; // [rsp+10h] [rbp-40h]

  v18.f64[0] = a4;
  v4 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "isEqualToString:", CFSTR("D"));
  if ( v6 )
  {
    v7 = _mm_cmpeq_sd(*(__m128d *)&a3, (__m128d)0x7FEFFFFFFFFFFFFFuLL).f64[0];
    *(_QWORD *)&v8 = ~*(_QWORD *)&v7 & *(_QWORD *)&a3 | *(_QWORD *)&v7 & 0xFFEFFFFFFFFFFFFFLL;
    v9 = _mm_cmpeq_sd((__m128d)0x7FEFFFFFFFFFFFFFuLL, v18).f64[0];
    *(_QWORD *)&v10 = ~*(_QWORD *)&v9 & *(_QWORD *)&v18.f64[0] | *(_QWORD *)&v9 & 0xFFEFFFFFFFFFFFFFLL;
  }
  else
  {
    v10 = a4;
    v8 = a3;
  }
  if ( v8 <= v10 )
  {
    if ( v10 <= v8 )
    {
      v11 = 0;
      v19 = 0LL;
    }
    else
    {
      v19 = -1LL;
      v11 = 0;
    }
  }
  else
  {
    v19 = 1LL;
    v11 = 1;
  }
  v12 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = (unsigned __int8)_objc_msgSend(v13, "isEqualToString:", CFSTR("D"));
  if ( v11 || !v14 )
    return v19 | -(__int64)(v14 != 0);
  else
    return v15;
}

//----- (0000000100536B43) ----------------------------------------------------
signed __int64 __cdecl -[ZhongCangStocksPoolTableViewController compareInteger:withInteger:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3,
        signed __int64 a4)
{
  NSString *v5; // rax
  NSString *v6; // rax
  signed __int64 v9; // rbx
  NSString *v10; // rax
  NSString *v11; // r15
  signed __int64 v13; // r12

  v5 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("D"));
  v9 = 0x8000000000000001LL;
  if ( a4 != 0x7FFFFFFFFFFFFFFFLL )
    v9 = a4;
  if ( !v7 )
    v9 = a4;
  v10 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("D"));
  if ( v13 <= v9 && v12 )
    return v13 < v9;
  v15 = 1LL;
  if ( v13 <= v9 )
    v15 = -(__int64)(v13 < v9);
  return v15 | -(__int64)(v12 != 0);
}

//----- (0000000100536C48) ----------------------------------------------------
HXBaseView *__cdecl -[ZhongCangStocksPoolTableViewController noStocksTipView](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._count);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100536C61) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setNoStocksTipView:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._count, a3);
}

//----- (0000000100536C75) ----------------------------------------------------
NSButton *__cdecl -[ZhongCangStocksPoolTableViewController noStocksTipBtn](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._allCodesNum);
  return (NSButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100536C8E) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setNoStocksTipBtn:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._allCodesNum, a3);
}

//----- (0000000100536CA2) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setInstructionTipView:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._frozenCount, a3);
}

//----- (0000000100536CB6) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setCompleteCodeAndDataArr:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._orderHQDataTypes, a3);
}

//----- (0000000100536CCA) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setBackgroundCodeListSortRequestModule:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._cachedViewWidth, a3);
}

//----- (0000000100536CDE) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController setBackgroundDetailDataRequestModule:](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._requestTimerForWindowResize, a3);
}

//----- (0000000100536CF2) ----------------------------------------------------
void __cdecl -[ZhongCangStocksPoolTableViewController .cxx_destruct](
        ZhongCangStocksPoolTableViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super._requestTimerForWindowResize, 0LL);
  objc_storeStrong((id *)&self->super._cachedViewWidth, 0LL);
  objc_storeStrong((id *)&self->super._orderHQDataTypes, 0LL);
  objc_storeStrong((id *)&self->super._frozenCount, 0LL);
  objc_destroyWeak((id *)&self->super._allCodesNum);
  objc_destroyWeak((id *)&self->super._count);
  objc_storeStrong((id *)&self->super._begin, 0LL);
}

