//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXBaseViewController.h"

#import "HXBaseTabContainerDelegate-Protocol.h"
#import "HXCPToolBoxDelegate-Protocol.h"
#import "HXTabDidSwitched-Protocol.h"
#import "HXTipManagerDataSource-Protocol.h"
#import "NSSplitViewDelegate-Protocol.h"

@class AIGroupContainerController, HXBaseView, HXPageTabContainer, HXPanKouContainerController, HXRightAccessaryViewController, HXSplitView, HXTabbarController, NSButton, NSMutableArray, NSString, NSView, SelfStockContainerController;

@interface ZiXuanGuViewController : HXBaseViewController <NSSplitViewDelegate, HXTipManagerDataSource, HXBaseTabContainerDelegate, HXCPToolBoxDelegate, HXTabDidSwitched>
{
    BOOL _isRightViewHide;
    BOOL _isExpand;
    BOOL _toolBoxOpened;
    int _rightAccessaryState;
    NSView *_leftMainView;
    HXBaseView *_topView;
    HXPageTabContainer *_tabContainer;
    HXBaseView *_tabAccessaryToolView;
    HXBaseView *_rightAccessaryView;
    HXBaseView *_contentView;
    NSButton *_showAndHideRightBtn;
    NSButton *_customizablePageToolBoxBtn;
    HXTabbarController *_ziXuanTabbarController;
    SelfStockContainerController *_selfStockContainerController;
    AIGroupContainerController *_aiGroupContainerController;
    HXRightAccessaryViewController *_rightAccessaryVC;
    HXPanKouContainerController *_tableRightPanKouVC;
    HXSplitView *_baseSplitView;
    CDUnknownBlockType _refreshRightViewBlock;
    NSMutableArray *_tipArray;
}


@property(nonatomic) int rightAccessaryState; // @synthesize rightAccessaryState=_rightAccessaryState;
@property(nonatomic) BOOL toolBoxOpened; // @synthesize toolBoxOpened=_toolBoxOpened;
@property(retain, nonatomic) NSMutableArray *tipArray; // @synthesize tipArray=_tipArray;
@property(copy, nonatomic) CDUnknownBlockType refreshRightViewBlock; // @synthesize refreshRightViewBlock=_refreshRightViewBlock;
@property(nonatomic) BOOL isExpand; // @synthesize isExpand=_isExpand;
@property(retain, nonatomic) HXSplitView *baseSplitView; // @synthesize baseSplitView=_baseSplitView;
@property(retain, nonatomic) HXPanKouContainerController *tableRightPanKouVC; // @synthesize tableRightPanKouVC=_tableRightPanKouVC;
@property(retain, nonatomic) HXRightAccessaryViewController *rightAccessaryVC; // @synthesize rightAccessaryVC=_rightAccessaryVC;
@property(nonatomic) BOOL isRightViewHide; // @synthesize isRightViewHide=_isRightViewHide;
@property(retain, nonatomic) AIGroupContainerController *aiGroupContainerController; // @synthesize aiGroupContainerController=_aiGroupContainerController;
@property(retain, nonatomic) SelfStockContainerController *selfStockContainerController; // @synthesize selfStockContainerController=_selfStockContainerController;
@property(retain, nonatomic) HXTabbarController *ziXuanTabbarController; // @synthesize ziXuanTabbarController=_ziXuanTabbarController;
@property __weak NSButton *customizablePageToolBoxBtn; // @synthesize customizablePageToolBoxBtn=_customizablePageToolBoxBtn;
@property __weak NSButton *showAndHideRightBtn; // @synthesize showAndHideRightBtn=_showAndHideRightBtn;
@property __weak HXBaseView *contentView; // @synthesize contentView=_contentView;
@property(retain) HXBaseView *rightAccessaryView; // @synthesize rightAccessaryView=_rightAccessaryView;
@property __weak HXBaseView *tabAccessaryToolView; // @synthesize tabAccessaryToolView=_tabAccessaryToolView;
@property __weak HXPageTabContainer *tabContainer; // @synthesize tabContainer=_tabContainer;
@property __weak HXBaseView *topView; // @synthesize topView=_topView;
@property __weak NSView *leftMainView; // @synthesize leftMainView=_leftMainView;
- (long long)getVcIndexWithTabId:(long long)arg1;
- (id)tipsToShow;
- (BOOL)splitView:(id)arg1 shouldAdjustSizeOfSubview:(id)arg2;
- (double)splitView:(id)arg1 constrainMaxCoordinate:(double)arg2 ofSubviewAt:(long long)arg3;
- (double)splitView:(id)arg1 constrainMinCoordinate:(double)arg2 ofSubviewAt:(long long)arg3;
- (void)componentSelect:(id)arg1 params:(id)arg2;
- (void)toolBoxOpenStateChanged:(BOOL)arg1;
- (void)renameFinishedTabItem:(id)arg1 withTabID:(long long)arg2 newName:(id)arg3;
- (BOOL)disableCloseAlert;
- (void)copyFromTabItem:(id)arg1 withTabID:(long long)arg2;
- (id)getViewControllerForTabID:(long long)arg1;
- (void)addBtnClicked:(id)arg1;
- (void)deleteTabWithId:(long long)arg1 index:(unsigned long long)arg2;
- (void)selectTabWithId:(long long)arg1 index:(unsigned long long)arg2;
- (void)hxTabDidSwitchOld:(unsigned long long)arg1 toNew:(unsigned long long)arg2;
- (void)insertCustomizablePageWithObject:(id)arg1;
- (void)insertCustomizablePageWithType:(unsigned long long)arg1;
- (void)loadMyTemplate:(id)arg1;
- (void)deleteMyTemplateTab:(id)arg1;
- (void)renameMyTemplateTab:(id)arg1;
- (void)theSelectRowDidChanged:(id)arg1;
- (void)changeFrameForWiget:(double)arg1;
- (void)setRightAccessaryViewOriginalState;
- (void)setViewOriginalState;
- (void)closeCustomizablePageToolBox;
- (void)openCustomizablePageToolBox;
- (void)closeRightAccessarySwitch;
- (void)openRightAccessarySwitch;
- (void)updateTabAccessaryToolViewState;
- (void)insertDefaultPages;
- (void)setViewControllersOriginalState;
- (id)createTabItemObjects;
- (void)showCustomizablePageToolBoxBtn;
- (id)getSelectedViewController;
- (id)getPageTabContainer;
- (BOOL)jumpToCustomizeTemplatePage:(unsigned long long)arg1;
- (void)insertSelectedPageWithPageType:(unsigned long long)arg1;
- (void)addNewViewController:(id)arg1 withTabItem:(id)arg2;
- (void)jumpToLongHuBang;
- (void)jumpTozuiJinLinLan;
- (void)jumpToStockFormPick;
- (void)selectSSGWithGroupIndex:(long long)arg1;
- (void)keyDownFromSuper:(id)arg1;
- (void)displayPickControl:(id)arg1 multiQuote:(id)arg2;
- (void)showToolBox:(id)arg1;
- (void)showAndHideRightClick:(id)arg1;
- (void)zuiJinLinLanBtnClicked:(id)arg1;
- (void)quanBuZiXuanBtnClicked:(id)arg1;
- (void)refreshAllModules;
- (void)dealloc;
- (void)viewDidDisappear;
- (void)viewDidAppear;
- (void)viewWillAppear;
- (void)becomeKeyWindow:(id)arg1;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

