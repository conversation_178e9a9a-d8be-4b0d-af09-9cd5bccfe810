//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

#import "ReceiveDispatchData-Protocol.h"

@class NSDateFormatter, NSMutableArray, NSString;
@protocol ZiXunDispatchDelegate;

@interface ZiXunRequestModule : NSObject <ReceiveDispatchData>
{
    BOOL _needWaiting;
    BOOL _allZiXunReqFail;
    NSString *_stockCode;
    NSString *_market;
    id <ZiXunDispatchDelegate> _delegate;
    NSMutableArray *_ziXunItems;
    unsigned long long _currentZiXunType;
    unsigned long long _requestSum;
    unsigned long long _backReqSum;
    unsigned long long _failReqSum;
    NSDateFormatter *_dateFormatter;
}


@property(retain, nonatomic) NSDateFormatter *dateFormatter; // @synthesize dateFormatter=_dateFormatter;
@property(nonatomic) BOOL allZiXunReqFail; // @synthesize allZiXunReqFail=_allZiXunReqFail;
@property(nonatomic) unsigned long long failReqSum; // @synthesize failReqSum=_failReqSum;
@property(nonatomic) unsigned long long backReqSum; // @synthesize backReqSum=_backReqSum;
@property(nonatomic) unsigned long long requestSum; // @synthesize requestSum=_requestSum;
@property(nonatomic) unsigned long long currentZiXunType; // @synthesize currentZiXunType=_currentZiXunType;
@property(retain, nonatomic) NSMutableArray *ziXunItems; // @synthesize ziXunItems=_ziXunItems;
@property(nonatomic) __weak id <ZiXunDispatchDelegate> delegate; // @synthesize delegate=_delegate;
@property(nonatomic) BOOL needWaiting; // @synthesize needWaiting=_needWaiting;
@property(copy, nonatomic) NSString *market; // @synthesize market=_market;
@property(copy, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
- (void)resetParamsBeforeRequest;
- (id)getTableFlagValue:(unsigned long long)arg1;
- (void)postZiXunDataToZiXunTable;
- (id)transCodingXMLData:(id)arg1;
- (id)getCreateTimeWithPropArr:(id)arg1;
- (id)getNewsPubDateWithDataStr:(id)arg1;
- (id)getNewsClientLinkWithDataStr:(id)arg1;
- (id)getNewsLinkWithDataStr:(id)arg1;
- (id)getNewsOperationWithDataStr:(id)arg1;
- (id)getNewsPropertiesWithDataStr:(id)arg1;
- (id)getNewsStockCodeWithDataStr:(id)arg1;
- (id)getNewsIdWithDataStr:(id)arg1;
- (id)getNewsTitleWithDataStr:(id)arg1;
- (id)getNewsTimeWithDataStr:(id)arg1;
- (id)getHttpNewsItemWithDataStr:(id)arg1;
- (id)getJiJinHangYeNewsItemWithDataStr:(id)arg1;
- (id)getNewsItemWithDataStr:(id)arg1;
- (void)parseHttpXmlWithRegularExpression:(id)arg1;
- (void)parseHttpZiXunItemData:(id)arg1;
- (void)parseJiJinHangYeXmlWithRegularExpression:(id)arg1;
- (void)parseXmlWithRegularExpression:(id)arg1;
- (void)parserZiXunItemData:(id)arg1;
- (void)unregisterInMacDataServiceWithRequstInstanceId:(long long)arg1;
- (void)failToReceiveStuffData:(long long)arg1;
- (void)receiveStuffData:(id)arg1;
- (void)requestJiJinHangYe:(id)arg1;
- (void)requestNormalZiXunBySocket:(id)arg1;
- (void)requestZiXunBySocket:(id)arg1;
- (void)requestZiXunByHttp:(id)arg1;
- (void)requestZiXun:(id)arg1;
- (void)requestForZiXun:(id)arg1;
- (id)init;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

