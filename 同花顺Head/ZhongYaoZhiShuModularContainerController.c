void __cdecl -[<PERSON><PERSON><PERSON><PERSON><PERSON>hiShuModularContainerController viewDidLoad](
        Z<PERSON><PERSON>aoZhiShuModularContainerController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___Z<PERSON><PERSON>aoZhiShuModularContainerController;
  -[PanKouModularBaseViewController viewDidLoad](&v2, "viewDidLoad");
  -[<PERSON><PERSON><PERSON>aoZhiShuModularContainerController setViewState](self, "setViewState");
}

//----- (000000010013B6A4) ----------------------------------------------------
void __cdecl -[Z<PERSON><PERSON><PERSON><PERSON>hiShuModularContainerController setViewState](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  id (*v43)(id, SEL, ...); // r12
  id (*v47)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  id (*v54)(id, SEL, ...); // r12
  id (*v57)(id, SEL, ...); // r12
  id (*v63)(id, SEL, ...); // r12
  _QWORD v66[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  v2 = _objc_msgSend(self, "view");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(&OBJC_CLASS___HXDragAndDropBaseView, "class");
  v7 = (unsigned __int8)v6(v3, "isKindOfClass:", v5);
  if ( v7 )
  {
    v9 = v8(self, "view");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = v11(self, "className");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14(v10, "setTargetViewControllerName:", v13);
  }
  v15 = v8(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = v17(self, "tableContentView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20(v19, "setBackgroundColor:", v16);
  v22 = v21(self, "view");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = v24(self, "titleContainerView");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v27(v26, "setDragImageView:", v23);
  v29 = v28(&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v32 = v31(self, "titleContainerView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v34(v33, "setBackgroundColor:", v30);
  v36 = v35(self, "titleContainerView");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  v38(v37, "setTopBorder:", 1LL);
  v40 = v39(self, "titleContainerView");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  v42(v41, "setBottomBorder:", 1LL);
  v44 = v43(self, "titleContainerView");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46(v45, "setBorderWidth:", 1.0);
  v48 = v47(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v49 = objc_retainAutoreleasedReturnValue(v48);
  v51 = v50(self, "titleContainerView");
  v52 = objc_retainAutoreleasedReturnValue(v51);
  v53(v52, "setBorderColor:", v49);
  v55 = v54(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v56 = objc_retainAutoreleasedReturnValue(v55);
  v58 = v57(self, "titleTF");
  v59 = objc_retainAutoreleasedReturnValue(v58);
  v60(v59, "setTextColor:", v56);
  v61(self, "setIsIntersectionNeedRequset:", 1LL);
  v62(self, "setIsTableModular:", 1LL);
  objc_initWeak(location, self);
  v64 = v63(self, "resizeView");
  v65 = objc_retainAutoreleasedReturnValue(v64);
  v66[0] = _NSConcreteStackBlock;
  v66[1] = 3254779904LL;
  v66[2] = sub_10013BA6F;
  v66[3] = &unk_1012DBC30;
  objc_copyWeak(&to, location);
  _objc_msgSend(v65, "setViewResizeCallBackBlock:", v66);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (000000010013BA6F) ----------------------------------------------------
void __fastcall sub_10013BA6F(__int64 a1, double a2)
{
  id WeakRetained; // r14
  id *v9; // r12
  id *v12; // r12
  id *v15; // r12
  id *v28; // r14
  SEL v32; // r12
  SEL v37; // r12
  id *v44; // r14
  SEL v47; // r12
  SEL v51; // r12
  id *v63; // r14
  SEL v67; // r12
  SEL v72; // r12
  id *v80; // r13
  id *v113; // r12
  id *location; // [rsp+1A8h] [rbp-68h]

  v148 = a2;
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "view");
  v4 = (const char *)objc_retainAutoreleasedReturnValue(v3);
  v5 = (char *)v4;
  if ( v4 )
  {
    objc_msgSend_stret(&v117, v4, "frame");
    v6 = *((double *)&v118 + 1);
  }
  else
  {
    v118 = 0LL;
    v117 = 0LL;
    v6 = 0.0;
  }
  v7 = v148;
  v8 = v6 - v148;
  v148 = v8;
  v142 = objc_loadWeakRetained(v9);
  v10 = _objc_msgSend(v142, "zhongYaoZhiShuTableVC");
  v143 = objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(v143, "myTable");
  v144 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v144, "rowHeight");
  *(double *)&location = v8 + 22.0;
  v145 = objc_loadWeakRetained(v12);
  v13 = _objc_msgSend(v145, "zhongYaoZhiShuTableVC");
  v146 = objc_retainAutoreleasedReturnValue(v13);
  v14 = _objc_msgSend(v146, "myTable");
  v140 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v140, "intercellSpacing");
  *(double *)&v147 = v7 + *(double *)&location;
  location = v15;
  v16 = objc_loadWeakRetained(v15);
  v17 = _objc_msgSend(v16, "zhongYaoZhiShuTableVC");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v18, "myTable");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21 = _objc_msgSend(v20, "headerView");
  v22 = (const char *)objc_retainAutoreleasedReturnValue(v21);
  v23 = (char *)v22;
  if ( v22 )
  {
    objc_msgSend_stret(&v119, v22, "frame");
    v24 = *((double *)&v120 + 1);
  }
  else
  {
    v120 = 0LL;
    v119 = 0LL;
    v24 = 0.0;
  }
  v25 = *(double *)&v147 + v24;
  *(double *)&v147 = *(double *)&v147 + v24;
  v27 = *(double *)&v147;
  if ( *(double *)&v147 > v148 )
  {
    v28 = location;
    *(double *)&v147 = COERCE_DOUBLE(objc_loadWeakRetained(location));
    v29 = _objc_msgSend(v147, "zhongYaoZhiShuTableVC");
    v142 = objc_retainAutoreleasedReturnValue(v29);
    v30 = _objc_msgSend(v142, "myTable");
    v143 = objc_retainAutoreleasedReturnValue(v30);
    _objc_msgSend(v143, "rowHeight");
    v148 = v27 + 22.0;
    v144 = objc_loadWeakRetained(v28);
    v31 = _objc_msgSend(v144, "zhongYaoZhiShuTableVC");
    v145 = objc_retainAutoreleasedReturnValue(v31);
    v33 = _objc_msgSend(v145, v32);
    v146 = objc_retainAutoreleasedReturnValue(v33);
    _objc_msgSend(v146, "intercellSpacing");
    v148 = v25 + v148;
    v34 = objc_loadWeakRetained(v28);
    v35 = _objc_msgSend(v34, "zhongYaoZhiShuTableVC");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    v38 = _objc_msgSend(v36, v37);
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = _objc_msgSend(v39, "headerView");
    v41 = (const char *)objc_retainAutoreleasedReturnValue(v40);
    v42 = (char *)v41;
    if ( v41 )
    {
      objc_msgSend_stret(&v121, v41, "frame");
      v27 = *((double *)&v122 + 1);
    }
    else
    {
      v122 = 0LL;
      v121 = 0LL;
      v27 = 0.0;
    }
    v25 = v148 + v27;
    v148 = v148 + v27;
  }
  v44 = location;
  v142 = objc_loadWeakRetained(location);
  v45 = _objc_msgSend(v142, "zhongYaoZhiShuTableVC");
  v143 = objc_retainAutoreleasedReturnValue(v45);
  v46 = _objc_msgSend(v143, "myTable");
  v144 = objc_retainAutoreleasedReturnValue(v46);
  _objc_msgSend(v144, "rowHeight");
  *(double *)&v147 = v27;
  v145 = objc_loadWeakRetained(v44);
  v48 = _objc_msgSend(v145, v47);
  v146 = objc_retainAutoreleasedReturnValue(v48);
  v49 = _objc_msgSend(v146, "myTable");
  v140 = objc_retainAutoreleasedReturnValue(v49);
  _objc_msgSend(v140, "intercellSpacing");
  *(double *)&v147 = (v25 + *(double *)&v147) * 50.0 + 22.0;
  v50 = objc_loadWeakRetained(v44);
  v52 = _objc_msgSend(v50, v51);
  v53 = objc_retainAutoreleasedReturnValue(v52);
  v54 = _objc_msgSend(v53, "myTable");
  v55 = objc_retainAutoreleasedReturnValue(v54);
  v56 = _objc_msgSend(v55, "headerView");
  v57 = (const char *)objc_retainAutoreleasedReturnValue(v56);
  v58 = (char *)v57;
  if ( v57 )
  {
    objc_msgSend_stret(&v123, v57, "frame");
    v59 = *((double *)&v124 + 1);
  }
  else
  {
    v124 = 0LL;
    v123 = 0LL;
    v59 = 0.0;
  }
  v60 = *(double *)&v147 + v59;
  *(double *)&v147 = *(double *)&v147 + v59;
  v62 = v148;
  if ( v148 > *(double *)&v147 )
  {
    v63 = location;
    *(double *)&v147 = COERCE_DOUBLE(objc_loadWeakRetained(location));
    v64 = _objc_msgSend(v147, "zhongYaoZhiShuTableVC");
    v142 = objc_retainAutoreleasedReturnValue(v64);
    v65 = _objc_msgSend(v142, "myTable");
    v143 = objc_retainAutoreleasedReturnValue(v65);
    _objc_msgSend(v143, "rowHeight");
    v148 = v62;
    v144 = objc_loadWeakRetained(v63);
    v66 = _objc_msgSend(v144, "zhongYaoZhiShuTableVC");
    v145 = objc_retainAutoreleasedReturnValue(v66);
    v68 = _objc_msgSend(v145, v67);
    v146 = objc_retainAutoreleasedReturnValue(v68);
    _objc_msgSend(v146, "intercellSpacing");
    v148 = (v60 + v148) * 50.0 + 22.0;
    v69 = objc_loadWeakRetained(v63);
    v70 = _objc_msgSend(v69, "zhongYaoZhiShuTableVC");
    v71 = objc_retainAutoreleasedReturnValue(v70);
    v73 = _objc_msgSend(v71, v72);
    v74 = objc_retainAutoreleasedReturnValue(v73);
    v75 = _objc_msgSend(v74, "headerView");
    v76 = (const char *)objc_retainAutoreleasedReturnValue(v75);
    v77 = (char *)v76;
    if ( v76 )
    {
      objc_msgSend_stret(&v125, v76, "frame");
      v78 = *((double *)&v126 + 1);
    }
    else
    {
      v126 = 0LL;
      v125 = 0LL;
      v78 = 0.0;
    }
    v148 = v148 + v78;
  }
  v80 = location;
  v81 = objc_loadWeakRetained(location);
  v82 = _objc_msgSend(v81, "view");
  v83 = (const char *)objc_retainAutoreleasedReturnValue(v82);
  v84 = (char *)v83;
  if ( v83 )
  {
    objc_msgSend_stret(&v127, v83, "frame");
    v85 = *((double *)&v128 + 1);
  }
  else
  {
    v128 = 0LL;
    v127 = 0LL;
    v85 = 0.0;
  }
  v148 = v85 - v148;
  if ( v148 != 0.0 )
  {
    v86 = COERCE_DOUBLE(objc_loadWeakRetained(v80));
    v87 = _objc_msgSend(*(id *)&v86, "view");
    objc_retainAutoreleasedReturnValue(v87);
    v142 = objc_loadWeakRetained(v80);
    v88 = _objc_msgSend(v142, "view");
    v89 = objc_retainAutoreleasedReturnValue(v88);
    *(double *)&v147 = v86;
    v143 = v89;
    v91 = v90;
    if ( v89 )
    {
      objc_msgSend_stret(v129, (SEL)v89, "frame");
      *(_QWORD *)&v138 = *(_QWORD *)&v129[0];
    }
    else
    {
      memset(v129, 0, sizeof(v129));
      *(_QWORD *)&v138 = 0LL;
    }
    v92 = objc_loadWeakRetained(v80);
    v93 = _objc_msgSend(v92, "view");
    v94 = objc_retainAutoreleasedReturnValue(v93);
    v144 = v92;
    v145 = v94;
    v140 = v91;
    if ( v94 )
    {
      objc_msgSend_stret(v130, (SEL)v94, v95);
      v96 = *((_QWORD *)&v130[0] + 1);
    }
    else
    {
      memset(v130, 0, sizeof(v130));
      v96 = 0LL;
    }
    *((_QWORD *)&v138 + 1) = v96;
    v97 = objc_loadWeakRetained(v80);
    v98 = _objc_msgSend(v97, "view");
    v99 = (const char *)objc_retainAutoreleasedReturnValue(v98);
    v101 = (char *)v99;
    v146 = v97;
    if ( v99 )
    {
      objc_msgSend_stret(&v131, v99, v100);
      v102 = v132;
    }
    else
    {
      v132 = 0LL;
      v131 = 0LL;
      v102 = 0LL;
    }
    v139 = v102;
    v103 = objc_loadWeakRetained(v80);
    v104 = _objc_msgSend(v103, "view");
    v105 = (const char *)objc_retainAutoreleasedReturnValue(v104);
    v107 = (char *)v105;
    if ( v105 )
    {
      objc_msgSend_stret(&v133, v105, v106);
      v108 = *((double *)&v134 + 1);
    }
    else
    {
      v134 = 0LL;
      v133 = 0LL;
      v108 = 0.0;
    }
    v135 = v138;
    v136 = v139;
    v137 = v108 - v148;
    _objc_msgSend(v140, "setFrame:");
    v110 = objc_loadWeakRetained(location);
    v111 = _objc_msgSend(v110, "viewResizeCallBackBlock");
    v112 = objc_retainAutoreleasedReturnValue(v111);
    if ( v112 )
    {
      v114 = objc_loadWeakRetained(v113);
      v115 = _objc_msgSend(v114, "viewResizeCallBackBlock");
      v116 = (void (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v115);
      v116[2](v116, v148);
    }
  }
}

//----- (000000010013C505) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController requestForModularData:market:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v7)(id); // r12
  NSMutableArray *v12; // rax
  NSMutableArray *contentsObjMArr; // rdi
  id WeakRetained; // rax
  HXBaseView *v20; // rax
  HXBaseView *v21; // r13

  v6 = objc_retain(a3);
  v8 = v7(a4);
  v9 = v8;
  if ( v8 )
  {
    v10 = (unsigned __int8)_objc_msgSend(v8, "isEqualToString:", &charsToLeaveEscaped);
    if ( v6 )
    {
      if ( !v10 && !(unsigned __int8)_objc_msgSend(v6, "isEqualToString:", &charsToLeaveEscaped) )
      {
        -[ZhongYaoZhiShuModularContainerController jugeCanRequset:market:](self, "jugeCanRequset:market:", v6, v9);
        if ( self->super.super._contentsObjMArr )
        {
          if ( !(unsigned __int8)-[ZhongYaoZhiShuModularContainerController modularRequseted](self, "modularRequseted") )
          {
            -[ZhongYaoZhiShuModularContainerController setModularRequseted:](self, "setModularRequseted:", 1LL);
            -[ZhongYaoZhiShuModularContainerController requestForTableViewData](self, "requestForTableViewData");
          }
        }
        else
        {
          v11 = objc_alloc((Class)&OBJC_CLASS___ZhongYaoZhiShuTableModularViewController);
          v12 = (NSMutableArray *)_objc_msgSend(
                                    v11,
                                    "initWithNibName:bundle:",
                                    CFSTR("ZhongYaoZhiShuTableModularViewController"),
                                    0LL);
          contentsObjMArr = self->super.super._contentsObjMArr;
          self->super.super._contentsObjMArr = v12;
          -[ZhongYaoZhiShuModularContainerController setModularRequseted:](self, "setModularRequseted:", 1LL);
          v14 = _objc_msgSend(self->super.super._contentsObjMArr, "view");
          v31 = objc_retainAutoreleasedReturnValue(v14);
          WeakRetained = objc_loadWeakRetained((id *)&self->super.super._shouldRefresh);
          v32 = WeakRetained;
          if ( WeakRetained )
          {
            objc_msgSend_stret(&v24, (SEL)WeakRetained, "frame");
            v16 = v25;
          }
          else
          {
            v25 = 0LL;
            v24 = 0LL;
            v16 = 0LL;
          }
          v34 = v16;
          v17 = objc_loadWeakRetained((id *)&self->super.super._shouldRefresh);
          v33 = v17;
          if ( v17 )
          {
            objc_msgSend_stret(&v26, (SEL)v17, "frame");
            v18 = *((_QWORD *)&v27 + 1);
          }
          else
          {
            v27 = 0LL;
            v26 = 0LL;
            v18 = 0LL;
          }
          v28 = 0LL;
          v29 = v34;
          v30 = v18;
          v19 = v31;
          _objc_msgSend(v31, "setFrame:");
          v20 = -[ZhongYaoZhiShuModularContainerController tableContentView](self, "tableContentView");
          v21 = objc_retainAutoreleasedReturnValue(v20);
          v22 = _objc_msgSend(self->super.super._contentsObjMArr, "view");
          v23 = objc_retainAutoreleasedReturnValue(v22);
          _objc_msgSend(v21, "addSubview:", v23);
        }
      }
    }
  }
}

//----- (000000010013C7B3) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController deleteModularOrder](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  _objc_msgSend(self->super.super._contentsObjMArr, "deleteOrder");
  _objc_msgSend(self->super.super._contentsObjMArr, "invalidateRequestTimer");
  -[ZhongYaoZhiShuModularContainerController setModularRequseted:](self, "setModularRequseted:", 0LL);
}

//----- (000000010013C805) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController requestForTableViewData](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  _objc_msgSend(self->super.super._contentsObjMArr, "requestForTableViewData");
}

//----- (000000010013C822) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController fireTableModularTimer](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  if ( -[PanKouModularBaseViewController modularVisibleType](self, "modularVisibleType") )
    _objc_msgSend(self->super.super._contentsObjMArr, "fireRequestTimer");
}

//----- (000000010013C862) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController disableScroll](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{

  v2 = _objc_msgSend(self->super.super._contentsObjMArr, "zhongYaoZhiShuScrollView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setDisEnableScroll:", 1LL);
}

//----- (000000010013C8AF) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController jugeCanRequset:market:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  bool v17; // zf

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v8 = _objc_msgSend(v7, "singleStockCode");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", v9) )
  {
    goto LABEL_5;
  }
  v11 = _objc_msgSend(v10, "singleMarket");
  v12 = v5;
  v13 = objc_retainAutoreleasedReturnValue(v11);
  v19 = v6;
  v14 = (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", v13);
  v15 = v13;
  v5 = v12;
  v17 = v14 == 0;
  v6 = v19;
  if ( v17 )
  {
LABEL_5:
    _objc_msgSend(v16, "setSingleStockCode:", v5);
    _objc_msgSend(v18, "setSingleMarket:", v6);
    goto LABEL_6;
  }
  _objc_msgSend(v16, "setModularRequseted:", 0LL);
LABEL_6:
}

//----- (000000010013C9C3) ----------------------------------------------------
HXBaseView *__cdecl -[ZhongYaoZhiShuModularContainerController tableContentView](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._shouldRefresh);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010013C9DC) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setTableContentView:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._shouldRefresh, a3);
}

//----- (000000010013C9F0) ----------------------------------------------------
NSTextField *__cdecl -[ZhongYaoZhiShuModularContainerController titleTF](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._stockCode);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010013CA09) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setTitleTF:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._stockCode, a3);
}

//----- (000000010013CA1D) ----------------------------------------------------
HXDragToResizeView *__cdecl -[ZhongYaoZhiShuModularContainerController resizeView](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._market);
  return (HXDragToResizeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010013CA36) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setResizeView:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._market, a3);
}

//----- (000000010013CA4A) ----------------------------------------------------
ZhongYaoZhiShuTableModularViewController *__cdecl -[ZhongYaoZhiShuModularContainerController zhongYaoZhiShuTableVC](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  return (ZhongYaoZhiShuTableModularViewController *)self->super.super._contentsObjMArr;
}

//----- (000000010013CA5B) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setZhongYaoZhiShuTableVC:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._contentsObjMArr, a3);
}

//----- (000000010013CA6F) ----------------------------------------------------
char __cdecl -[ZhongYaoZhiShuModularContainerController modularRequseted](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  return self->super.super.super._reserved;
}

//----- (000000010013CA80) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setModularRequseted:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super.super.super._reserved) = a3;
}

//----- (000000010013CA90) ----------------------------------------------------
NSString *__cdecl -[ZhongYaoZhiShuModularContainerController singleStockCode](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  return (NSString *)self->super.super._controllerID;
}

//----- (000000010013CAA1) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setSingleStockCode:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._controllerID, a3);
}

//----- (000000010013CAB5) ----------------------------------------------------
NSString *__cdecl -[ZhongYaoZhiShuModularContainerController singleMarket](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  return (NSString *)&self->super.super._paramsMDic->super.super.isa;
}

//----- (000000010013CAC6) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController setSingleMarket:](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._paramsMDic, a3);
}

//----- (000000010013CADA) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuModularContainerController .cxx_destruct](
        ZhongYaoZhiShuModularContainerController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super.super._paramsMDic, 0LL);
  objc_storeStrong((id *)&self->super.super._controllerID, 0LL);
  objc_storeStrong((id *)&self->super.super._contentsObjMArr, 0LL);
  objc_destroyWeak((id *)&self->super.super._market);
  objc_destroyWeak((id *)&self->super.super._stockCode);
  objc_destroyWeak((id *)&self->super.super._shouldRefresh);
}

