//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "PanKouModularBaseViewController.h"

#import "JYMaiMaiViewDelegate-Protocol.h"

@class DragTextField, HXBaseTradeView, HXButton, NSDictionary, NSString, WidgetMaiMaiView, WidgetUserListView;
@protocol JYInQuotaQuickTradeViewControllerDelegate;

@interface WidgetJiaoyiViewController : PanKouModularBaseViewController <JYMaiMaiViewDelegate>
{
    WidgetMaiMaiView *_aMaiMaiView;
    WidgetUserListView *_aShiPanUserInfoItemView;
    BOOL _showAll;
    BOOL _isMairuSelected;
    id <JYInQuotaQuickTradeViewControllerDelegate> _delegate;
    HXBaseTradeView *_jyView;
    HXButton *_switchBtn;
    DragTextField *_mairuTextField;
    DragTextField *_maichuTextField;
    HXBaseTradeView *_aDetailContainerView;
    HXBaseTradeView *_aUserInfoRoomView;
    HXBaseTradeView *_aMaimaiRoomView;
    NSString *_notificationNameString;
    NSString *_currentMarket;
    NSDictionary *_marketPageRelationDic;
}


@property(copy, nonatomic) NSDictionary *marketPageRelationDic; // @synthesize marketPageRelationDic=_marketPageRelationDic;
@property(copy, nonatomic) NSString *currentMarket; // @synthesize currentMarket=_currentMarket;
@property(copy, nonatomic) NSString *notificationNameString; // @synthesize notificationNameString=_notificationNameString;
@property(nonatomic) BOOL isMairuSelected; // @synthesize isMairuSelected=_isMairuSelected;
@property(nonatomic) BOOL showAll; // @synthesize showAll=_showAll;
@property __weak HXBaseTradeView *aMaimaiRoomView; // @synthesize aMaimaiRoomView=_aMaimaiRoomView;
@property __weak HXBaseTradeView *aUserInfoRoomView; // @synthesize aUserInfoRoomView=_aUserInfoRoomView;
@property __weak HXBaseTradeView *aDetailContainerView; // @synthesize aDetailContainerView=_aDetailContainerView;
@property __weak DragTextField *maichuTextField; // @synthesize maichuTextField=_maichuTextField;
@property __weak DragTextField *mairuTextField; // @synthesize mairuTextField=_mairuTextField;
@property __weak HXButton *switchBtn; // @synthesize switchBtn=_switchBtn;
@property __weak HXBaseTradeView *jyView; // @synthesize jyView=_jyView;
@property(nonatomic) __weak id <JYInQuotaQuickTradeViewControllerDelegate> delegate; // @synthesize delegate=_delegate;
- (void)sendMairuMaichuMaiDian;
- (void)sendZhanKaiZheDieMaiDian;
- (void)dealloc;
- (void)addNotification;
- (void)upPageViewSelectedIndexForActiveUser;
- (void)upThemes;
- (void)requestStockInfoSucceedWithStockCode:(id)arg1 andMkt:(id)arg2 reqMkt:(BOOL)arg3;
- (void)shouldClearTradeDataWithAppClose:(id)arg1;
- (void)shouldClearTradeDataWithTradeLogin:(id)arg1;
- (void)shouldReloadThemes:(id)arg1;
- (void)shouldReloadTradeData:(id)arg1;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
@property(readonly, nonatomic) BOOL isVisible;
- (void)setupButtton;
- (void)clickBuyOrSellAciton:(id)arg1;
- (void)switchContainerViewState:(BOOL)arg1;
- (void)switchBtnClicked:(id)arg1;
- (void)expandWidgetJY;
- (void)setJiaGeTFValueWithStrPrice:(id)arg1;
- (void)setStockCode:(id)arg1 market:(id)arg2 buySell:(int)arg3;
- (void)viewDidDisappear;
- (void)viewDidAppear;
- (void)viewWillAppear;
- (void)setViewState;
- (void)loadViewFromNib;
- (void)initObjects;
- (void)viewDidLoad;
- (id)nibBundle;
- (id)nibName;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

