void __cdecl -[<PERSON>anGuViewController viewDidLoad](XuanGuViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  _QWORD v25[3]; // [rsp+18h] [rbp-48h] BYREF

  v24.receiver = self;
  v24.super_class = (Class)&OBJC_CLASS___XuanGuViewController;
  -[HXBaseViewController viewDidLoad](&v24, "viewDidLoad");
  -[XuanGuViewController initObjects](self, "initObjects");
  v2(self, "initViewControllers");
  v3(self, "addNotifications");
  v5 = v4(self, "tabContainer");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setSelectedIndex:", 0LL);
  v9 = v8(self, "xuanGuTabbarController");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11(v10, "setSelectedIndex:", 0LL);
  v12(self, "setCeLveTongUrlIfNecessary");
  v14 = v13(&OBJC_CLASS___HXTipManager, "sharedInstance");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(v15, "addDataSource:", self);
  v18 = v17(&OBJC_CLASS___HXTipManager, "sharedInstance");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v25[0] = CFSTR("龙头寻宝");
  v25[1] = CFSTR("操盘总纲");
  v25[2] = CFSTR("AI分时顶底");
  v21 = v20(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v25, 3LL);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23(v19, "showTipsWithIdArray:", v22);
}

//----- (0000000100199F9D) ----------------------------------------------------
void __cdecl -[XuanGuViewController viewWillAppear](XuanGuViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XuanGuViewController;
  objc_msgSendSuper2(&v2, "viewWillAppear");
  +[UserLogSendingQueueManager updatePagePrefix:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "updatePagePrefix:",
    CFSTR("选股"));
}

//----- (0000000100199FE7) ----------------------------------------------------
void __cdecl -[XuanGuViewController viewDidAppear](XuanGuViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XuanGuViewController;
  -[HXBaseViewController viewDidAppear](&v2, "viewDidAppear");
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    10LL,
    CFSTR("pageshow"),
    0LL,
    1LL);
}

//----- (000000010019A03F) ----------------------------------------------------
void __cdecl -[XuanGuViewController initObjects](XuanGuViewController *self, SEL a2)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // rbx
  HXBaseView *v8; // rax
  HXBaseView *v9; // rbx

  v2 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[XuanGuViewController topView](self, "topView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseView setBackgroundColor:](v4, "setBackgroundColor:", v5);
  v7 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  objc_retainAutoreleasedReturnValue(v7);
  v8 = -[XuanGuViewController contentView](self, "contentView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXBaseView setBackgroundColor:](v9, "setBackgroundColor:", v10);
}

//----- (000000010019A10E) ----------------------------------------------------
void __cdecl -[XuanGuViewController initViewControllers](XuanGuViewController *self, SEL a2)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  __CFString *v13; // rbx
  id (*v14)(id, SEL, ...); // r12
  __CFString *v15; // rdi
  __CFString *v16; // rax
  __CFString *v17; // rdi
  __CFString *v19; // r15
  id (*v20)(id, SEL, ...); // r12
  bool v38; // r15
  unsigned __int8 (__fastcall *v43)(__CFString *, const char *, __CFString *); // r13
  bool v44; // r14
  __CFString *v46; // r12
  __CFString *v52; // r14
  __CFString *v55; // rbx
  unsigned __int64 v56; // r13
  unsigned __int64 v60; // r14
  id to; // [rsp+28h] [rbp-128h] BYREF
  bool v64; // [rsp+30h] [rbp-120h]
  SEL v66; // [rsp+38h] [rbp-118h]
  SEL v67; // [rsp+40h] [rbp-110h]
  id location; // [rsp+D8h] [rbp-78h] BYREF
  SEL v87; // [rsp+E0h] [rbp-70h]
  __CFString *v92; // [rsp+108h] [rbp-48h]

  v2 = self;
  v3 = -[XuanGuViewController xuanGuTitleArr](self, "xuanGuTitleArr");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(v4, "count");
  if ( v6 )
  {
    v87 = "thsStringAtIndex:";
    v66 = "length";
    v67 = "isTouristAccount";
    v94 = "isEqualToString:";
    v69 = "xuanGuUrlArr";
    v70 = "initWithTitle:isDeletable:";
    v71 = "tabContainer";
    v72 = "addTabWithItem:atIndex:";
    v73 = "init";
    v74 = "tabID";
    v75 = "setupControllerID:";
    v76 = "setInitialUrlString:";
    v77 = "shouldShowCeLveTong";
    v78 = "setReloadWhenAppear:";
    v79 = "setReloadWhenThemeChanged:";
    v80 = "setReloadWhenIAPSuccess:";
    v81 = "setNewWindowActionBlock:";
    v89 = "xuanGuTabbarController";
    v82 = "viewControllers";
    v83 = "arrayWithArray:";
    v84 = "addObject:";
    v85 = "setViewControllers:";
    v8 = 0LL;
    val = self;
    do
    {
      v9 = v7(v2, "xuanGuTitleArr");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v90 = v8;
      v12 = v11(v10, v87, v8);
      v13 = (__CFString *)objc_retainAutoreleasedReturnValue(v12);
      v15 = v13;
      if ( !v14(v13, v66) )
        v15 = CFSTR("未命名");
      v16 = objc_retain(v15);
      v17 = v13;
      v18 = v2;
      v19 = v16;
      v21 = (unsigned __int8)v20(v18, v67);
      v23 = v22;
      if ( !v21
        || !((unsigned __int8 (__fastcall *)(__CFString *, const char *, __CFString *))v22)(
              v19,
              v94,
              CFSTR("操盘总纲"))
        && !((unsigned __int8 (__fastcall *)(__CFString *, const char *, __CFString *))v23)(
              v19,
              v94,
              CFSTR("龙头寻宝"))
        && !((unsigned __int8 (__fastcall *)(__CFString *, const char *, __CFString *))v23)(
              v19,
              v94,
              CFSTR("AI分时顶底")) )
      {
        v24 = (void *)v23(val, v69);
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v92 = v19;
        v26 = v25;
        v27 = (void *)((__int64 (__fastcall *)(id, SEL, __int64))v23)(v25, v87, v90);
        v88 = objc_retainAutoreleasedReturnValue(v27);
        v28 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
        v29 = (void *)((__int64 (__fastcall *)(id, const char *, __CFString *, _QWORD))v23)(v28, v70, v19, 0LL);
        v31 = (void *)v23(v30, v71);
        v32 = objc_retainAutoreleasedReturnValue(v31);
        ((void (__fastcall *)(id, const char *, void *, __int64))v23)(v32, v72, v29, v90);
        v33 = objc_alloc((Class)&OBJC_CLASS___QuoteWebViewController);
        v34 = (void *)v23(v33, v73);
        v68 = v29;
        v35 = v23(v29, v74);
        ((void (__fastcall *)(void *, const char *, __int64))v23)(v34, v75, v35);
        ((void (__fastcall *)(void *, const char *, id))v23)(v34, v76, v88);
        v36 = v23(val, v77);
        v38 = v36 != 0 && v37 == 0;
        ((void (__fastcall *)(void *, const char *, bool))v23)(v34, v78, v38);
        v39 = (__int64)v92;
        v95 = ((__int64 (__fastcall *)(__CFString *, const char *, __CFString *))v23)(v92, v94, CFSTR("龙头寻宝"));
        v91 = v34;
        ((void (__fastcall *)(void *, const char *, _QWORD))v23)(v34, v79, (unsigned int)v95);
        v41 = ((__int64 (__fastcall *)(__int64, __int64, __CFString *))v23)(v39, v40, CFSTR("操盘总纲"));
        v43 = (unsigned __int8 (__fastcall *)(__CFString *, const char *, __CFString *))v23;
        v44 = 1;
        if ( !v41 && !v43((__CFString *)v39, v42, CFSTR("龙头寻宝")) )
          v44 = v43(v92, v94, CFSTR("AI分时顶底")) != 0;
        v43((__CFString *)v91, v80, (__CFString *)v44);
        v45 = val;
        objc_initWeak(&location, val);
        v62[0] = (__int64)_NSConcreteStackBlock;
        v62[1] = 3254779904LL;
        v62[2] = (__int64)sub_10019A6E9;
        v62[3] = (__int64)&unk_1012DDDE8;
        objc_copyWeak(&to, &location);
        v64 = v38;
        v65 = v95;
        v43(v46, v81, (__CFString *)v62);
        v47 = (void *)((__int64 (__fastcall *)(id, const char *))v43)(v45, v89);
        v48 = objc_retainAutoreleasedReturnValue(v47);
        v49 = (void *)((__int64 (__fastcall *)(id, const char *))v43)(v48, v82);
        v50 = objc_retainAutoreleasedReturnValue(v49);
        v51 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, id))v43)(
                        &OBJC_CLASS___NSMutableArray,
                        v83,
                        v50);
        v52 = objc_retainAutoreleasedReturnValue(v51);
        v43(v52, v84, (__CFString *)v91);
        v54 = (void *)((__int64 (__fastcall *)(id, const char *))v43)(val, v89);
        v55 = objc_retain(v54);
        v43(v55, v85, v52);
        objc_destroyWeak(&to);
        objc_destroyWeak(&location);
        v23 = (__int64 (__fastcall *)(id, const char *))v43;
        v19 = v92;
      }
      v56 = v90 + 1;
      v2 = val;
      v58 = (void *)v23(val, v57);
      v59 = objc_retainAutoreleasedReturnValue(v58);
      v60 = v23(v59, "count");
      v61 = v59;
      v8 = v56;
    }
    while ( v60 > v56 );
  }
}

//----- (000000010019A6E9) ----------------------------------------------------
void __fastcall sub_10019A6E9(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v5 = v2;
  v3 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(
    WeakRetained,
    "newWindowActionWithRequest:isLast:reloadWhenAppear:reloadWhenThemeChanged:",
    v3,
    0LL,
    (unsigned int)*(char *)(a1 + 40),
    (unsigned int)*(char *)(a1 + 41),
    v5);
}

//----- (000000010019A74E) ----------------------------------------------------
void __cdecl -[XuanGuViewController addNotifications](XuanGuViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "addObserver:selector:name:object:", v4, "searchIWenCai:", off_1012E24A0, 0LL);
  if ( (unsigned __int8)_objc_msgSend(v5, "shouldShowCeLveTong") )
  {
    v6 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(
      v7,
      "addObserver:selector:name:object:",
      v8,
      "updateStrategyMallUrl:",
      CFSTR("updateStrategyMallUrlNotification"),
      0LL);
  }
}

//----- (000000010019A821) ----------------------------------------------------
void __cdecl -[XuanGuViewController setCeLveTongUrlIfNecessary](XuanGuViewController *self, SEL a2)
{
  dispatch_time_t v2; // rax
  _QWORD block[6]; // [rsp+0h] [rbp-30h] BYREF

  if ( (unsigned __int8)-[XuanGuViewController shouldShowCeLveTong](self, "shouldShowCeLveTong") )
  {
    -[XuanGuViewController requestCeLveTongUrl](self, "requestCeLveTongUrl");
    v2 = dispatch_time(0LL, 1000000000LL);
    block[0] = _NSConcreteStackBlock;
    block[1] = 3254779904LL;
    block[2] = sub_10019A8A1;
    block[3] = &unk_1012DABC0;
    block[4] = self;
    dispatch_after(v2, &_dispatch_main_q, block);
  }
}

//----- (000000010019A8A1) ----------------------------------------------------
void __fastcall sub_10019A8A1(__int64 a1)
{
  LoadLocalURLManager *v1; // rax
  LoadLocalURLManager *v2; // r15
  id (*v3)(id, SEL, ...); // r12

  v1 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v4 = v3(v2, "getCurrentURLWithKey:", CFSTR("策略宝"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(*(id *)(a1 + 32), "setCeLveTongUrl:", v5);
}

//----- (000000010019A91F) ----------------------------------------------------
void __cdecl -[XuanGuViewController dealloc](XuanGuViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___XuanGuViewController;
  -[HXBaseViewController dealloc](&v4, "dealloc");
}

//----- (000000010019A994) ----------------------------------------------------
HXBrowserTabContainer *__cdecl -[XuanGuViewController tabContainer](XuanGuViewController *self, SEL a2)
{
  id autounbinder; // rdi
  const char *WeakRetained; // rax
  id (*v11)(id, SEL, ...); // r12
  id *location; // [rsp+40h] [rbp-30h]

  autounbinder = self->super.super._autounbinder;
  if ( !autounbinder )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXBrowserTabContainer);
    location = (id *)&self->super.super._topLevelObjects;
    WeakRetained = (const char *)objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
    v6 = (char *)WeakRetained;
    if ( WeakRetained )
      objc_msgSend_stret(v21, WeakRetained, "bounds");
    else
      memset(v21, 0, sizeof(v21));
    v7 = _objc_msgSend(v4, "initWithFrame:");
    v8 = self->super.super._autounbinder;
    self->super.super._autounbinder = v7;
    v9(self->super.super._autounbinder, "setBottomBorder:", 1LL);
    v10(self->super.super._autounbinder, "setBorderWidth:", 1.0);
    v12 = v11(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14(self->super.super._autounbinder, "setBorderColor:", v13);
    v15(self->super.super._autounbinder, "setHasAddBtn:", 0LL);
    v16(self->super.super._autounbinder, "setEnableDrag:", 1LL);
    v17(self->super.super._autounbinder, "setDelegate:", self);
    v18 = objc_loadWeakRetained(location);
    v19(v18, "addSubview:", self->super.super._autounbinder);
    autounbinder = self->super.super._autounbinder;
  }
  return (HXBrowserTabContainer *)objc_retainAutoreleaseReturnValue(autounbinder);
}

//----- (000000010019AB10) ----------------------------------------------------
HXTabbarController *__cdecl -[XuanGuViewController xuanGuTabbarController](XuanGuViewController *self, SEL a2)
{
  NSString *designNibBundleIdentifier; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi
  id WeakRetained; // r14

  designNibBundleIdentifier = self->super.super._designNibBundleIdentifier;
  if ( !designNibBundleIdentifier )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super.super._designNibBundleIdentifier;
    self->super.super._designNibBundleIdentifier = v5;
    WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
    v8(self->super.super._designNibBundleIdentifier, "setView:", WeakRetained);
    designNibBundleIdentifier = self->super.super._designNibBundleIdentifier;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(designNibBundleIdentifier);
}

//----- (000000010019ABA3) ----------------------------------------------------
NSArray *__cdecl -[XuanGuViewController xuanGuTitleArr](XuanGuViewController *self, SEL a2)
{
  NSArray *v4; // rax
  NSArray *v5; // r15
  objc_class *v6; // r14
  _QWORD v11[6]; // [rsp+0h] [rbp-60h] BYREF

  v3 = *(void **)&self->super.super._viewIsAppearing;
  if ( !v3 )
  {
    v11[0] = CFSTR("一句话选股");
    v11[1] = CFSTR("指标选股");
    v11[2] = CFSTR("AI分时顶底");
    v11[3] = CFSTR("龙头寻宝");
    v11[4] = CFSTR("操盘总纲");
    v11[5] = CFSTR("题材挖掘");
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v11, 6LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (objc_class *)_objc_msgSend(v5, "mutableCopy");
    if ( (unsigned __int8)-[XuanGuViewController shouldShowCeLveTong](self, "shouldShowCeLveTong") )
      _objc_msgSend(v6, "insertObject:atIndex:", CFSTR("策略宝"), 0LL);
    v8 = *(Class *)((char *)&self->super.super.super.super.isa + v7);
    *(Class *)((char *)&self->super.super.super.super.isa + v7) = v6;
    v3 = *(Class *)((char *)&self->super.super.super.super.isa + v9);
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010019ACC3) ----------------------------------------------------
NSArray *__cdecl -[XuanGuViewController xuanGuUrlArr](XuanGuViewController *self, SEL a2)
{
  LoadLocalURLManager *v4; // rax
  LoadLocalURLManager *v5; // r14
  __CFString *v7; // rbx
  __CFString *v8; // rdi
  LoadLocalURLManager *v10; // rax
  LoadLocalURLManager *v11; // r15
  __CFString *v13; // rbx
  __CFString *v15; // rdi
  LoadLocalURLManager *v17; // rax
  LoadLocalURLManager *v18; // r15
  __CFString *v20; // rbx
  __CFString *v22; // rdi
  LoadLocalURLManager *v24; // rax
  LoadLocalURLManager *v25; // r15
  __CFString *v27; // rbx
  __CFString *v29; // rdi
  LoadLocalURLManager *v31; // rax
  LoadLocalURLManager *v32; // rbx
  __CFString *v34; // r15
  __CFString *v36; // rdi
  __CFString *v37; // r14
  LoadLocalURLManager *v39; // rax
  LoadLocalURLManager *v40; // r15
  __CFString *v42; // rbx
  LoadLocalURLManager *v43; // rdi
  __CFString *v46; // rdi
  NSArray *v48; // rax
  NSArray *v49; // rbx
  XuanGuViewController *v61; // [rsp+8h] [rbp-98h]
  __CFString *v63; // [rsp+20h] [rbp-80h]
  __CFString *v64; // [rsp+28h] [rbp-78h]
  __CFString *v65; // [rsp+30h] [rbp-70h]
  __CFString *v66; // [rsp+38h] [rbp-68h]
  _QWORD v67[6]; // [rsp+40h] [rbp-60h] BYREF

  v3 = *(void **)&self->super.super._isContentViewController;
  if ( !v3 )
  {
    v4 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[LoadLocalURLManager getCurrentURLWithKey:](v5, "getCurrentURLWithKey:", CFSTR("一句话选股"));
    v61 = self;
    v7 = (__CFString *)objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    if ( !v7 )
      v8 = &charsToLeaveEscaped;
    v63 = objc_retain(v8);
    v9(v7);
    v10 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = -[LoadLocalURLManager getCurrentURLWithKey:](v11, "getCurrentURLWithKey:", CFSTR("指标选股"));
    v13 = (__CFString *)objc_retainAutoreleasedReturnValue(v12);
    v14(v11);
    v15 = v13;
    if ( !v13 )
      v15 = &charsToLeaveEscaped;
    v64 = objc_retain(v15);
    v16(v13);
    v17 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = -[LoadLocalURLManager getCurrentURLWithKey:](v18, "getCurrentURLWithKey:", CFSTR("操盘总纲"));
    v20 = (__CFString *)objc_retainAutoreleasedReturnValue(v19);
    v21(v18);
    v22 = v20;
    if ( !v20 )
      v22 = &charsToLeaveEscaped;
    v65 = objc_retain(v22);
    v23(v20);
    v24 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = -[LoadLocalURLManager getCurrentURLWithKey:](v25, "getCurrentURLWithKey:", CFSTR("题材挖掘"));
    v27 = (__CFString *)objc_retainAutoreleasedReturnValue(v26);
    v28(v25);
    v29 = v27;
    if ( !v27 )
      v29 = &charsToLeaveEscaped;
    v66 = objc_retain(v29);
    v30(v27);
    v31 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33 = -[LoadLocalURLManager getCurrentURLWithKey:](v32, "getCurrentURLWithKey:", CFSTR("龙头寻宝"));
    v34 = (__CFString *)objc_retainAutoreleasedReturnValue(v33);
    v35(v32);
    v36 = v34;
    if ( !v34 )
      v36 = &charsToLeaveEscaped;
    v37 = objc_retain(v36);
    v38(v34);
    v39 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v41 = -[LoadLocalURLManager getCurrentURLWithKey:](v40, "getCurrentURLWithKey:", CFSTR("AI分时顶底"));
    v42 = (__CFString *)objc_retainAutoreleasedReturnValue(v41);
    v43 = v40;
    v45 = v44;
    v44(v43);
    v46 = &charsToLeaveEscaped;
    if ( v42 )
      v46 = v42;
    objc_retain(v46);
    ((void (__fastcall *)(__CFString *))v45)(v42);
    v67[0] = v63;
    v67[1] = v64;
    v62 = v47;
    v67[2] = v47;
    v67[3] = v37;
    v67[4] = v65;
    v67[5] = v66;
    v48 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v67, 6LL);
    v49 = objc_retainAutoreleasedReturnValue(v48);
    v50 = _objc_msgSend(v49, "mutableCopy");
    v51(v49);
    if ( (unsigned __int8)-[XuanGuViewController shouldShowCeLveTong](v61, "shouldShowCeLveTong") )
      _objc_msgSend(v50, "insertObject:atIndex:", &charsToLeaveEscaped, 0LL);
    v53 = *(_QWORD *)&v61->super.super._isContentViewController;
    *(_QWORD *)&v61->super.super._isContentViewController = v50;
    v52(v53);
    v54(v62);
    v55(v37);
    v56(v66);
    v57(v65);
    v58(v64);
    v59(v63);
    v3 = *(void **)&v61->super.super._isContentViewController;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010019B063) ----------------------------------------------------
NSDictionary *__cdecl -[XuanGuViewController titleActionDic](XuanGuViewController *self, SEL a2)
{
  NSDictionary *v4; // rax
  NSDictionary *v5; // rax
  _QWORD v8[7]; // [rsp+8h] [rbp-88h] BYREF
  _QWORD v9[7]; // [rsp+40h] [rbp-50h] BYREF

  v3 = *(void **)&self->super.super._reserved;
  if ( !v3 )
  {
    v8[0] = CFSTR("策略宝");
    v9[0] = CFSTR("策略宝");
    v8[1] = CFSTR("一句话选股");
    v9[1] = CFSTR("一句话");
    v8[2] = CFSTR("指标选股");
    v9[2] = CFSTR("指标选股");
    v8[3] = CFSTR("操盘总纲");
    v9[3] = CFSTR("操盘总纲");
    v8[4] = CFSTR("题材挖掘");
    v9[4] = CFSTR("题材挖掘");
    v8[5] = CFSTR("龙头寻宝");
    v9[5] = CFSTR("龙头寻宝");
    v8[6] = CFSTR("AI分时顶底");
    v9[6] = CFSTR("AI分时顶底");
    v4 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v9, v8, 7LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = *(void **)&self->super.super._reserved;
    *(_QWORD *)&self->super.super._reserved = v5;
    v3 = *(void **)&self->super.super._reserved;
  }
  return (NSDictionary *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010019B168) ----------------------------------------------------
void __cdecl -[XuanGuViewController searchIWenCai:](XuanGuViewController *self, SEL a2, id a3)
{
  LoadLocalURLManager *v8; // rax
  LoadLocalURLManager *v9; // rbx
  NSString *v12; // rax
  NSString *v13; // r15
  NSURL *v18; // rax
  NSURL *v19; // rbx
  NSURLRequest *v20; // rax
  NSURLRequest *v21; // r15

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    v6 = _objc_msgSend(v4, "thsStringForKey:", off_1012E2488);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    if ( _objc_msgSend(v7, "length") )
    {
      v23 = v4;
      v8 = +[LoadLocalURLManager sharedInstance](&OBJC_CLASS___LoadLocalURLManager, "sharedInstance");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v10 = -[LoadLocalURLManager getCurrentURLWithKey:](v9, "getCurrentURLWithKey:", CFSTR("i问财搜索"));
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v22 = v7;
      v12 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v11, v7);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v15 = +[HXTools urlEncodeOtherCharacters:](&OBJC_CLASS___HXTools, "urlEncodeOtherCharacters:", v13);
      objc_retainAutoreleasedReturnValue(v15);
      if ( _objc_msgSend(v16, "length") )
      {
        v18 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v17);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v20 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v19);
        v21 = objc_retainAutoreleasedReturnValue(v20);
        -[XuanGuViewController newWindowActionWithRequest:isLast:reloadWhenAppear:reloadWhenThemeChanged:](
          self,
          "newWindowActionWithRequest:isLast:reloadWhenAppear:reloadWhenThemeChanged:",
          v21,
          1LL,
          0LL,
          0LL);
      }
      v7 = v22;
      v4 = v23;
    }
  }
}

//----- (000000010019B35A) ----------------------------------------------------
void __cdecl -[XuanGuViewController updateStrategyMallUrl:](XuanGuViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  unsigned __int64 v16; // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v34)(id, SEL, ...); // r12
  id (*v37)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12

  v3 = _objc_msgSend(a3, "userInfo");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(&OBJC_CLASS___NSDictionary, "class");
  if ( (unsigned __int8)v7(v4, "isKindOfClass:", v6) )
  {
    v8 = -[XuanGuViewController getTabIndexWithVcIndex:](self, "getTabIndexWithVcIndex:", 0LL);
    if ( (__int64)v8 >= 0 )
    {
      v41 = v8;
      v10 = v9(self, "tabContainer");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = v12(v11, "tabViewArray");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15(v14, "count");
      if ( (unsigned __int64)v41 < v16 )
      {
        v42 = v4;
        v17 = _objc_msgSend(v4, "thsStringForKey:", off_1012E39D8);
        v43 = objc_retainAutoreleasedReturnValue(v17);
        v19 = v18(self, "tabContainer");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v21(v20, "setSelectedIndex:", v41);
        v23 = v22(self, "xuanGuTabbarController");
        v24 = objc_retainAutoreleasedReturnValue(v23);
        v25(v24, "setSelectedIndex:", 0LL);
        v27 = v26(self, "ceLveTongUrl");
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v30 = v29(v28, "length");
        if ( v30 )
        {
          v32 = v31(self, "xuanGuTabbarController");
          v33 = objc_retainAutoreleasedReturnValue(v32);
          v35 = v34(v33, "selectedViewController");
          v36 = objc_retainAutoreleasedReturnValue(v35);
          v38 = v37(&OBJC_CLASS___QuoteWebViewController, "class");
          v40 = v43;
          if ( (unsigned __int8)v39(v36, "isKindOfClass:", v38) )
            _objc_msgSend(v36, "loadUrl:", v43);
        }
        else
        {
          v40 = v43;
          -[XuanGuViewController setCeLveTongUrl:](self, "setCeLveTongUrl:", v43);
        }
        v4 = v42;
      }
    }
  }
}

//----- (000000010019B5C7) ----------------------------------------------------
void __cdecl -[XuanGuViewController newWindowActionWithRequest:isLast:reloadWhenAppear:reloadWhenThemeChanged:](
        XuanGuViewController *self,
        SEL a2,
        id a3,
        char a4,
        char a5,
        char a6)
{
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  NSMutableArray *v54; // rax
  _QWORD v64[4]; // [rsp+8h] [rbp-B8h] BYREF
  _QWORD v68[4]; // [rsp+38h] [rbp-88h] BYREF
  id to; // [rsp+60h] [rbp-60h] BYREF
  int v72; // [rsp+70h] [rbp-50h]
  int v73; // [rsp+74h] [rbp-4Ch]
  id location; // [rsp+78h] [rbp-48h] BYREF
  int v76; // [rsp+8Ch] [rbp-34h]

  v72 = a6;
  v76 = a5;
  v73 = a4;
  val = self;
  v71 = objc_retain(a3);
  v6 = _objc_msgSend(v71, "URL");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(v7, "absoluteString");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(v10, "length");
  if ( v12 )
  {
    v13 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
    v75 = v14(v13, "initWithTitle:isDeletable:", CFSTR("未命名"), 1LL);
    v16 = v15(val, "tabContainer");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = v17;
    if ( (_BYTE)v73 )
    {
      v20 = v18(v17, "tabViewArray");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v23 = (char *)v22(v21, "count");
    }
    else
    {
      v23 = (char *)_objc_msgSend(v17, "selectedIndex") + 1;
    }
    v25 = v24(val, "tabContainer");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27(v26, "addTabWithItem:atIndex:", v75, v23);
    v29 = v28(val, "tabContainer");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31(v30, "setSelectedIndex:", v23);
    v32 = objc_alloc((Class)&OBJC_CLASS___QuoteWebViewController);
    v34 = v33(v32, "init");
    v36 = v35(v75, "tabID");
    v37(v34, "setupControllerID:", v36);
    v39 = v38(v71, "URL");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v42 = v41(v40, "absoluteString");
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v44(v34, "setInitialUrlString:", v43);
    v45(v34, "setReloadWhenAppear:", (unsigned int)(char)v76);
    LOBYTE(v40) = v72;
    v46(v34, "setReloadWhenThemeChanged:", (unsigned int)(char)v72);
    objc_initWeak(&location, val);
    v68[0] = _NSConcreteStackBlock;
    v68[1] = 3254779904LL;
    v68[2] = sub_10019BA74;
    v68[3] = &unk_1012DBAD8;
    objc_copyWeak(&to, &location);
    v47 = objc_retain(v75);
    v69 = v47;
    v48 = v34;
    v49(v34, "setReceiveTitleActionBlock:", v68);
    v75 = v47;
    v64[0] = _NSConcreteStackBlock;
    v64[1] = 3254779904LL;
    v64[2] = sub_10019BB3A;
    v64[3] = &unk_1012DDDE8;
    objc_copyWeak(&v65, &location);
    v66 = v76;
    v67 = (char)v40;
    _objc_msgSend(v34, "setNewWindowActionBlock:", v64);
    v50 = _objc_msgSend(val, "xuanGuTabbarController");
    v51 = objc_retainAutoreleasedReturnValue(v50);
    v52 = _objc_msgSend(v51, "viewControllers");
    v53 = objc_retainAutoreleasedReturnValue(v52);
    v54 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v53);
    objc_retainAutoreleasedReturnValue(v54);
    _objc_msgSend(v55, "addObject:", v48);
    v56 = _objc_msgSend(val, "xuanGuTabbarController");
    v57 = objc_retain(v56);
    _objc_msgSend(v57, "setViewControllers:", v58);
    v60 = (char *)_objc_msgSend(v59, "count");
    v61 = _objc_msgSend(val, "xuanGuTabbarController");
    v62 = objc_retain(v61);
    _objc_msgSend(v62, "setSelectedIndex:", v60 - 1);
    objc_destroyWeak(&v65);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
  }
}

//----- (000000010019BA74) ----------------------------------------------------
void __fastcall sub_10019BA74(__int64 a1, void *a2)
{
  __CFString *v3; // rbx
  id (__cdecl *v4)(id); // r12
  __CFString *v5; // rdi
  id WeakRetained; // rax

  v3 = objc_retain(a2);
  v5 = CFSTR("未命名");
  if ( _objc_msgSend(v3, "length") )
    v5 = v3;
  v6 = v4(v5);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v8 = _objc_msgSend(WeakRetained, "tabContainer");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(*(id *)(a1 + 32), "tabID");
  _objc_msgSend(v9, "updateTabTitle:withTabId:", v6, v10);
}

//----- (000000010019BB3A) ----------------------------------------------------
void __fastcall sub_10019BB3A(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v5 = v2;
  v3 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(
    WeakRetained,
    "newWindowActionWithRequest:isLast:reloadWhenAppear:reloadWhenThemeChanged:",
    v3,
    0LL,
    (unsigned int)*(char *)(a1 + 40),
    (unsigned int)*(char *)(a1 + 41),
    v5);
}

//----- (000000010019BB9F) ----------------------------------------------------
signed __int64 __cdecl -[XuanGuViewController getVcIndexWithTabId:](
        XuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{
  signed __int64 v3; // r12
  HXTabbarController *v4; // rax
  HXTabbarController *v5; // rbx
  NSArray *v6; // rax
  NSArray *v7; // r15
  id i; // r12
  id (*v18)(id, SEL, ...); // r12
  SEL v27; // [rsp+40h] [rbp-E0h]
  SEL v30; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v3 = 0x7FFFFFFFFFFFFFFFLL;
  v29 = (id)a3;
  if ( a3 > 0 )
  {
    v26 = 0LL;
    v25 = 0LL;
    v24 = 0LL;
    v23 = 0LL;
    v31 = self;
    v4 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[HXTabbarController viewControllers](v5, "viewControllers");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    obj = v7;
    v8 = _objc_msgSend(v7, "countByEnumeratingWithState:objects:count:", &v23, v33, 16LL);
    if ( v8 )
    {
      v9 = v8;
      v28 = *(_QWORD *)v24;
      while ( 2 )
      {
        v27 = "class";
        v30 = "controllerID";
        for ( i = 0LL; i != v9; i = (id)(v13 + 1) )
        {
          if ( *(_QWORD *)v24 != v28 )
            objc_enumerationMutation(obj);
          v11 = *(void **)(*((_QWORD *)&v23 + 1) + 8LL * (_QWORD)i);
          v12 = _objc_msgSend(&OBJC_CLASS___HXBaseViewController, v27);
          if ( (unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12) )
          {
            v14 = objc_retain(v11);
            v15 = _objc_msgSend(v14, v30);
            if ( v15 == v29 )
            {
              v16 = _objc_msgSend(v31, "xuanGuTabbarController");
              v17 = objc_retainAutoreleasedReturnValue(v16);
              v19 = v18(v17, "viewControllers");
              v20 = objc_retainAutoreleasedReturnValue(v19);
              v21(v20, "indexOfObject:", v14);
              goto LABEL_16;
            }
          }
        }
        v9 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v23, v33, 16LL);
        if ( v9 )
          continue;
        break;
      }
    }
LABEL_16:
  }
  return v3;
}

//----- (000000010019BE22) ----------------------------------------------------
signed __int64 __cdecl -[XuanGuViewController getTabIndexWithVcIndex:](
        XuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{
  signed __int64 v3; // rbx
  HXTabbarController *v5; // rax
  HXTabbarController *v6; // rax
  NSArray *v7; // rax
  NSArray *v8; // r13
  HXTabbarController *v11; // rax
  HXTabbarController *v12; // r14
  NSArray *v13; // rax
  NSArray *v14; // rax
  HXBrowserTabContainer *v19; // rax
  HXBrowserTabContainer *v20; // r14

  v3 = 0x7FFFFFFFFFFFFFFFLL;
  if ( a3 >= 0 )
  {
    v5 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = -[HXTabbarController viewControllers](v6, "viewControllers");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v8, "count");
    if ( (unsigned __int64)v9 <= a3 )
    {
      return 0x7FFFFFFFFFFFFFFFLL;
    }
    else
    {
      v11 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = -[HXTabbarController viewControllers](v12, "viewControllers");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = _objc_msgSend(v14, "objectAtIndex:", a3);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v18 = _objc_msgSend(&OBJC_CLASS___HXBaseViewController, "class");
      v3 = 0x7FFFFFFFFFFFFFFFLL;
      if ( (unsigned __int8)_objc_msgSend(v16, "isKindOfClass:", v18) )
      {
        v19 = -[XuanGuViewController tabContainer](self, "tabContainer");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v21 = _objc_msgSend(v16, "controllerID");
        v3 = -[HXBaseTabContainer getIndexWithId:](v20, "getIndexWithId:", v21);
      }
    }
  }
  return v3;
}

//----- (000000010019BFA2) ----------------------------------------------------
char __cdecl -[XuanGuViewController isTouristAccount](XuanGuViewController *self, SEL a2)
{

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)_objc_msgSend(v3, "currentUserIsGuest");
  return v4;
}

//----- (000000010019BFEE) ----------------------------------------------------
char __cdecl -[XuanGuViewController shouldShowCeLveTong](XuanGuViewController *self, SEL a2)
{
  return 0;
}

//----- (000000010019BFF6) ----------------------------------------------------
void __cdecl -[XuanGuViewController requestCeLveTongUrl](XuanGuViewController *self, SEL a2)
{
  NSURL *v2; // rax
  NSURL *v3; // rbx
  NSURLRequest *v4; // rax
  NSURLRequest *v5; // r14
  _QWORD v11[4]; // [rsp+8h] [rbp-88h] BYREF
  _QWORD v13[4]; // [rsp+30h] [rbp-60h] BYREF
  id to; // [rsp+50h] [rbp-40h] BYREF
  id location[6]; // [rsp+60h] [rbp-30h] BYREF

  objc_initWeak(location, self);
  v2 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", CFSTR("http://sp.10jqka.com.cn/ads/api/get/adinfo/194"));
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = objc_alloc((Class)&OBJC_CLASS___AFHTTPRequestOperation);
  _objc_msgSend(v6, "initWithRequest:", v5);
  v15 = v5;
  v13[0] = _NSConcreteStackBlock;
  v13[1] = 3254779904LL;
  v13[2] = sub_10019C18C;
  v13[3] = &unk_1012DC180;
  objc_copyWeak(&to, location);
  v11[0] = _NSConcreteStackBlock;
  v11[1] = 3254779904LL;
  v11[2] = sub_10019C285;
  v11[3] = &unk_1012DD6D0;
  objc_copyWeak(&v12, location);
  _objc_msgSend(v7, "setCompletionBlockWithSuccess:failure:", v13, v11);
  _objc_msgSend(v8, "start");
  v9 = v15;
  objc_destroyWeak(&v12);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (000000010019C18C) ----------------------------------------------------
void __fastcall sub_10019C18C(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = _objc_msgSend(a2, "responseString");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "dataUsingEncoding:", 4LL);
  objc_retainAutoreleasedReturnValue(v4);
  if ( v5 )
  {
    v6 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "JSONObjectWithData:options:error:", v5, 1LL, 0LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v8) )
    {
      WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
      _objc_msgSend(WeakRetained, "dealWithCeLveTongUrlResponse:", v7);
    }
  }
}

//----- (000000010019C285) ----------------------------------------------------
void __fastcall sub_10019C285(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithCeLveTongUrlResponse:", 0LL);
}

//----- (000000010019C2B8) ----------------------------------------------------
void __cdecl -[XuanGuViewController dealWithCeLveTongUrlResponse:](XuanGuViewController *self, SEL a2, id a3)
{

  v3 = _objc_msgSend(a3, "thsDictionaryForKey:", CFSTR("result"));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "thsDictionaryForKey:", CFSTR("194-1336"));
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "thsDictionaryForKey:", CFSTR("data"));
  v16 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v16, "thsArrayForKey:", CFSTR("ad"));
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "thsDictionaryAtIndex:", 0LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v12, "thsStringForKey:", CFSTR("ad_url"));
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[XuanGuViewController setCeLveTongUrl:](self, "setCeLveTongUrl:", v14);
}

//----- (000000010019C3D8) ----------------------------------------------------
void __cdecl -[XuanGuViewController tabURLFromSelectedIndex:](XuanGuViewController *self, SEL a2, signed __int64 a3)
{
  HXTabbarController *v3; // rax
  HXTabbarController *v4; // r14
  NSArray *v5; // rax
  NSArray *v6; // rax
  HXTabbarController *v9; // rax
  HXTabbarController *v10; // r14
  NSArray *v11; // rax
  NSArray *v12; // rbx
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  __CFString *v31; // rbx
  __CFString *v36; // r15

  if ( a3 >= 0 )
  {
    v3 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = -[HXTabbarController viewControllers](v4, "viewControllers");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "count");
    if ( (unsigned __int64)v7 > a3 )
    {
      v9 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = -[HXTabbarController viewControllers](v10, "viewControllers");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = _objc_msgSend(v12, "count");
      if ( v13 )
      {
        v37 = v14;
        v15 = _objc_msgSend(v14, "xuanGuTabbarController");
        v16 = objc_retainAutoreleasedReturnValue(v15);
        v18 = v17(v16, "viewControllers");
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v21 = v20(v19, "objectAtIndex:", a3);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        v24 = v23(&OBJC_CLASS___HXBaseWKWebViewController, "class");
        if ( (unsigned __int8)v25(v22, "isKindOfClass:", v24) )
        {
          v26 = _objc_msgSend(v22, "webView");
          v27 = objc_retainAutoreleasedReturnValue(v26);
          v28 = _objc_msgSend(v27, "URL");
          v29 = objc_retainAutoreleasedReturnValue(v28);
          v30 = _objc_msgSend(v29, "absoluteString");
          v31 = (__CFString *)objc_retainAutoreleasedReturnValue(v30);
          v33(v27);
          if ( !_objc_msgSend(v31, "length") )
          {
            v35 = _objc_msgSend(v22, "initialUrlString");
            v36 = (__CFString *)objc_retainAutoreleasedReturnValue(v35);
            v31 = v36;
          }
          if ( _objc_msgSend(v31, v34) )
            _objc_msgSend(v37, "passURLToPasteboard:", v31);
        }
        else
        {
          v31 = &charsToLeaveEscaped;
        }
      }
      else
      {
      }
    }
  }
}

//----- (000000010019C64B) ----------------------------------------------------
void __cdecl -[XuanGuViewController passURLToPasteboard:](XuanGuViewController *self, SEL a2, id a3)
{
  NSArray *v8; // rax
  NSString *v10; // rax
  NSString *v11; // r13
  HXLogPrintWindowController *v12; // rax
  HXLogPrintWindowController *v13; // rbx
  NSArray *v14; // [rsp+0h] [rbp-30h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSPasteboard, "generalPasteboard");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObject:", v7);
    v14 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(v6, "clearContents");
    _objc_msgSend(v6, "writeObjects:", v14);
    v10 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("%s 右键复制的URL为: %@"),
            "-[XuanGuViewController passURLToPasteboard:]",
            v9);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = +[HXLogPrintWindowController sharedInstance](&OBJC_CLASS___HXLogPrintWindowController, "sharedInstance");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    -[HXLogPrintWindowController appendToLogTextView:](v13, "appendToLogTextView:", v11);
  }
}

//----- (000000010019C773) ----------------------------------------------------
void __cdecl -[XuanGuViewController selectTabWithId:index:](
        XuanGuViewController *self,
        SEL a2,
        signed __int64 a3,
        unsigned __int64 a4)
{
  NSArray *v5; // rax
  NSArray *v6; // r14
  NSDictionary *v8; // rax
  NSDictionary *v9; // rbx
  XuanGuViewController *v14; // rbx
  unsigned __int8 (__fastcall *v32)(id, const char *, __int64); // r12
  unsigned __int64 v43; // [rsp-48h] [rbp-48h]

  if ( a3 > 0 )
  {
    v5 = -[XuanGuViewController xuanGuTitleArr](self, "xuanGuTitleArr");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v43 = a4;
    v7 = _objc_msgSend(v6, "thsStringAtIndex:", a4);
    objc_retainAutoreleasedReturnValue(v7);
    v8 = -[XuanGuViewController titleActionDic](self, "titleActionDic");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v44 = v10;
    v11 = _objc_msgSend(v9, "thsStringForKey:", v10);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    if ( v13(v12, "length") )
      +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
        &OBJC_CLASS___UserLogSendingQueueManager,
        "sendUserLog:action:params:needWait:",
        11LL,
        v12,
        0LL,
        1LL);
    v14 = self;
    v16 = -[XuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", a3);
    if ( v16 != (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v17 = (void *)v15(self, "tabContainer");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19(v18, "setSelectedIndex:", v43);
      v21 = (void *)v20(self, "xuanGuTabbarController");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23(v22, "setSelectedIndex:", v16);
      v14 = self;
    }
    if ( (unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("龙头寻宝"))
      || (unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("操盘总纲"))
      || (unsigned __int8)_objc_msgSend(v44, "isEqualToString:", CFSTR("AI分时顶底")) )
    {
      v25 = (void *)v24(v14, "tabContainer");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      v28 = (void *)v27(v26, "getSelectedTabView");
      v29 = objc_retainAutoreleasedReturnValue(v28);
      v31 = v30(&OBJC_CLASS___HXBrowserTabView, "class");
      if ( v32(v29, "isKindOfClass:", v31) )
      {
        v33(v29, "setDrawRedDot:", 0LL);
        v35 = (void *)v34(&OBJC_CLASS___HXTipManager, "sharedInstance");
        v36 = objc_retainAutoreleasedReturnValue(v35);
        v37(v36, "hideTipWithId:", v44);
        v39 = (void *)v38(&OBJC_CLASS___HXTipManager, "sharedInstance");
        v40 = objc_retainAutoreleasedReturnValue(v39);
        v41(v40, "setTipShowedWithId:", v44);
      }
    }
  }
}

//----- (000000010019CA57) ----------------------------------------------------
void __cdecl -[XuanGuViewController deleteTabWithId:index:](
        XuanGuViewController *self,
        SEL a2,
        signed __int64 a3,
        unsigned __int64 a4)
{
  HXBrowserTabContainer *v7; // rax
  HXBrowserTabContainer *v8; // r14
  NSArray *v9; // rax
  NSArray *v10; // rbx
  HXBrowserTabContainer *v13; // rax
  HXBrowserTabContainer *v14; // rbx
  HXBrowserTabContainer *v16; // rax
  HXBrowserTabContainer *v17; // r14
  NSArray *v18; // rax
  NSArray *v19; // rbx
  HXBrowserTabContainer *v23; // rax
  HXBrowserTabContainer *v24; // rax
  HXBrowserTabContainer *v26; // r13
  XuanGuViewController *v38; // r13
  HXTabbarController *v58; // rax
  id (*v59)(id, SEL, ...); // r12
  HXTabbarController *v60; // r15
  id (*v61)(id, SEL, ...); // r12
  id (*v64)(id, SEL, ...); // r12
  id (*v67)(id, SEL, ...); // r12
  id (*v69)(id, SEL, ...); // r12
  id (*v72)(id, SEL, ...); // r12
  id (*v79)(id, SEL, ...); // r12
  id (*v85)(id, SEL, ...); // r12
  id (*v88)(id, SEL, ...); // r12
  id (*v91)(id, SEL, ...); // r12
  id (*v96)(id, SEL, ...); // r12
  HXTabbarController *v99; // rax
  HXTabbarController *v100; // rbx
  SEL v101; // r12
  id (*v102)(id, SEL, ...); // r12
  id (*v104)(id, SEL, ...); // r12

  v6 = -[XuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:");
  if ( a3 > 0 && v6 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v109 = v6;
    v7 = -[XuanGuViewController tabContainer](self, "tabContainer");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = -[HXBaseTabContainer tabViewArray](v8, "tabViewArray");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v10, "count");
    v12(v8);
    if ( (unsigned __int64)v11 > a4 )
    {
      v13 = -[XuanGuViewController tabContainer](self, "tabContainer");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      -[HXBaseTabContainer deleteTabAtIndex:](v14, "deleteTabAtIndex:", a4);
      v15(v14);
      v16 = -[XuanGuViewController tabContainer](self, "tabContainer");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18 = -[HXBaseTabContainer tabViewArray](v17, "tabViewArray");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v111 = (id)a4;
      v20 = _objc_msgSend(v19, "count");
      v21(v19);
      v22(v17);
      v23 = -[XuanGuViewController tabContainer](self, "tabContainer");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      v26 = v24;
      if ( v20 )
      {
        v27 = (void *)v25(v24, "tabViewArray");
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v31 = (__int64)v111;
        if ( v29(v28, "count") <= (unsigned __int64)v111 )
        {
          v32 = (void *)v30(self, "tabContainer");
          v112 = objc_retainAutoreleasedReturnValue(v32);
          v34 = (void *)v33(v112, "tabViewArray");
          v35 = objc_retainAutoreleasedReturnValue(v34);
          v37 = v36(v35, "count") - 1;
          v31 = v37;
        }
        v38 = self;
        v40 = (void *)v39(self, "tabContainer");
        v41 = objc_retainAutoreleasedReturnValue(v40);
        v42(v41, "setSelectedIndex:", v31);
        v44 = (void *)v43(self, "tabContainer");
        v45 = v31;
        v46 = objc_retainAutoreleasedReturnValue(v44);
        v48 = (void *)v47(v46, "tabViewArray");
        v49 = objc_retainAutoreleasedReturnValue(v48);
        v51 = (void *)v50(v49, "objectAtIndex:", v45);
        objc_retainAutoreleasedReturnValue(v51);
        v53 = v52;
        v55 = (void *)v53(v54, "tabItem");
        v56 = objc_retainAutoreleasedReturnValue(v55);
        v108 = v53((__int64)v56, "tabID");
        v58 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
      }
      else
      {
        -[HXBrowserTabContainer setSelectedIndex:](v24, "setSelectedIndex:", -1LL);
        v108 = 0LL;
        v38 = self;
        v58 = (HXTabbarController *)v59(self, "xuanGuTabbarController");
      }
      v60 = objc_retainAutoreleasedReturnValue(v58);
      v62 = v61(v60, "viewControllers");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v65 = v64(v63, "objectAtIndex:", v109);
      v66 = objc_retainAutoreleasedReturnValue(v65);
      v68 = v67(v38, "xuanGuTabbarController");
      v113 = objc_retainAutoreleasedReturnValue(v68);
      v70 = v69(v113, "view");
      v71 = objc_retainAutoreleasedReturnValue(v70);
      v73 = v72(v71, "subviews");
      objc_retainAutoreleasedReturnValue(v73);
      v110 = v66;
      v74 = _objc_msgSend(v66, "view");
      v75 = objc_retainAutoreleasedReturnValue(v74);
      v77 = (unsigned __int8)_objc_msgSend(v76, "containsObject:", v75);
      if ( v77 )
      {
        v80 = v79(v110, "view");
        v81 = objc_retainAutoreleasedReturnValue(v80);
        v82(v81, "removeFromSuperview");
      }
      v83 = v79(self, "xuanGuTabbarController");
      v84 = objc_retainAutoreleasedReturnValue(v83);
      v86 = v85(v84, "viewControllers");
      v87 = objc_retainAutoreleasedReturnValue(v86);
      v89 = v88(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v87);
      v90 = objc_retainAutoreleasedReturnValue(v89);
      v92 = v91(self, "xuanGuTabbarController");
      v93 = objc_retainAutoreleasedReturnValue(v92);
      v94(v93, "setSelectedViewController:", 0LL);
      v95 = __NSArray0__;
      v97 = v96(self, "xuanGuTabbarController");
      v98 = objc_retainAutoreleasedReturnValue(v97);
      _objc_msgSend(v98, "setViewControllers:", v95);
      _objc_msgSend(v90, "removeObject:", v110);
      v99 = -[XuanGuViewController xuanGuTabbarController](self, "xuanGuTabbarController");
      v100 = objc_retainAutoreleasedReturnValue(v99);
      _objc_msgSend(v100, v101, v90);
      v103 = v102(self, "getVcIndexWithTabId:", v108);
      if ( v103 != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v105 = v104(self, "xuanGuTabbarController");
        v106 = objc_retainAutoreleasedReturnValue(v105);
        v107(v106, "setSelectedIndex:", v103);
      }
    }
  }
}

//----- (000000010019D03A) ----------------------------------------------------
void __cdecl -[XuanGuViewController rightMouseSelectedTabWithId:](
        XuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{

  v4 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "boolForKey:", off_1012E8218[0]);
  if ( a3 > 0 )
  {
    if ( v8 )
    {
      v9 = ((__int64 (__fastcall *)(XuanGuViewController *, const char *, signed __int64))v7)(
             self,
             "getVcIndexWithTabId:",
             a3);
      v7(self, "tabURLFromSelectedIndex:", v9);
    }
  }
}

//----- (000000010019D0E0) ----------------------------------------------------
id __cdecl -[XuanGuViewController tipsToShow](XuanGuViewController *self, SEL a2)
{
  return -[XuanGuViewController tipArray](self, "tipArray");
}

//----- (000000010019D0F2) ----------------------------------------------------
NSMutableArray *__cdecl -[XuanGuViewController tipArray](XuanGuViewController *self, SEL a2)
{
  id privateData; // rdi
  objc_class *v5; // rax
  id *v19; // r12
  _QWORD v21[4]; // [rsp+0h] [rbp-130h] BYREF
  _QWORD v23[4]; // [rsp+28h] [rbp-108h] BYREF
  _QWORD v25[4]; // [rsp+50h] [rbp-E0h] BYREF
  _QWORD v27[4]; // [rsp+78h] [rbp-B8h] BYREF
  _QWORD v29[4]; // [rsp+A0h] [rbp-90h] BYREF
  _QWORD v31[4]; // [rsp+C8h] [rbp-68h] BYREF
  id to; // [rsp+E8h] [rbp-48h] BYREF
  id location[6]; // [rsp+100h] [rbp-30h] BYREF

  privateData = self->super.super.__privateData;
  if ( !privateData )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (objc_class *)objc_retainAutoreleasedReturnValue(v4);
    v7 = *(Class *)((char *)&self->super.super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.super.isa + v6) = v5;
    objc_initWeak(location, self);
    v8 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
    v9 = _objc_msgSend(v8, "initWithIdentifier:", CFSTR("龙头寻宝"));
    v31[0] = _NSConcreteStackBlock;
    v31[1] = 3254779904LL;
    v31[2] = sub_10019D4D4;
    v31[3] = &unk_1012DAA10;
    objc_copyWeak(&to, location);
    _objc_msgSend(v9, "setShowTipBlock:", v31);
    v25[0] = _NSConcreteStackBlock;
    v25[1] = 3254779904LL;
    v25[2] = sub_10019D511;
    v25[3] = &unk_1012DAA10;
    objc_copyWeak(&v26, location);
    _objc_msgSend(v9, "setHideTipBlock:", v25);
    v11 = *(Class *)((char *)&self->super.super.super.super.isa + v10);
    v33 = v9;
    _objc_msgSend(v11, "addObject:", v9);
    v12 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
    v13 = _objc_msgSend(v12, "initWithIdentifier:", CFSTR("操盘总纲"));
    v27[0] = _NSConcreteStackBlock;
    v27[1] = 3254779904LL;
    v27[2] = sub_10019D54B;
    v27[3] = &unk_1012DAA10;
    objc_copyWeak(&v28, location);
    _objc_msgSend(v13, "setShowTipBlock:", v27);
    v29[0] = _NSConcreteStackBlock;
    v29[1] = 3254779904LL;
    v29[2] = sub_10019D588;
    v29[3] = v14;
    objc_copyWeak(&v30, location);
    _objc_msgSend(v13, "setHideTipBlock:", v29);
    v15 = v13;
    _objc_msgSend(self->super.super.__privateData, "addObject:", v13);
    v16 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
    v17 = _objc_msgSend(v16, "initWithIdentifier:", CFSTR("AI分时顶底"));
    v34 = v15;
    v21[0] = v18;
    v21[1] = 3254779904LL;
    v21[2] = sub_10019D5C2;
    v21[3] = &unk_1012DAA10;
    objc_copyWeak(&v22, location);
    _objc_msgSend(v17, "setShowTipBlock:", v21);
    v23[0] = _NSConcreteStackBlock;
    v23[1] = 3254779904LL;
    v23[2] = sub_10019D5FF;
    v23[3] = &unk_1012DAA10;
    objc_copyWeak(&v24, location);
    _objc_msgSend(v17, "setHideTipBlock:", v23);
    _objc_msgSend(self->super.super.__privateData, "addObject:", v17);
    objc_destroyWeak(v19);
    objc_destroyWeak(&v22);
    objc_destroyWeak(&v30);
    objc_destroyWeak(&v28);
    objc_destroyWeak(&v26);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    privateData = self->super.super.__privateData;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(privateData);
}

//----- (000000010019D4D4) ----------------------------------------------------
void __fastcall sub_10019D4D4(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("龙头寻宝"), 1LL);
}

//----- (000000010019D511) ----------------------------------------------------
void __fastcall sub_10019D511(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("龙头寻宝"), 0LL);
}

//----- (000000010019D54B) ----------------------------------------------------
void __fastcall sub_10019D54B(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("操盘总纲"), 1LL);
}

//----- (000000010019D588) ----------------------------------------------------
void __fastcall sub_10019D588(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("操盘总纲"), 0LL);
}

//----- (000000010019D5C2) ----------------------------------------------------
void __fastcall sub_10019D5C2(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("AI分时顶底"), 1LL);
}

//----- (000000010019D5FF) ----------------------------------------------------
void __fastcall sub_10019D5FF(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "setTipStateWithTitle:state:", CFSTR("AI分时顶底"), 0LL);
}

//----- (000000010019D639) ----------------------------------------------------
void __cdecl -[XuanGuViewController setTipStateWithTitle:state:](XuanGuViewController *self, SEL a2, id a3, char a4)
{
  HXBrowserTabContainer *v4; // rax
  HXBrowserTabContainer *v5; // rax
  NSArray *v6; // rax
  NSArray *v7; // rbx
  HXBrowserTabContainer *v10; // rax
  HXBrowserTabContainer *v11; // rbx
  NSArray *v12; // rax
  NSArray *v13; // r14
  int v41; // [rsp+94h] [rbp-BCh]
  id obj; // [rsp+98h] [rbp-B8h]

  v41 = a4;
  v40 = objc_retain(a3);
  v4 = -[XuanGuViewController tabContainer](self, "tabContainer");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXBaseTabContainer tabViewArray](v5, "tabViewArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "count");
  if ( v8 )
  {
    v31 = 0LL;
    v30 = 0LL;
    v29 = 0LL;
    v28 = 0LL;
    v10 = -[XuanGuViewController tabContainer](self, "tabContainer");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = -[HXBaseTabContainer tabViewArray](v11, "tabViewArray");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    obj = v13;
    v15 = _objc_msgSend(v13, "countByEnumeratingWithState:objects:count:", v14, v43, 16LL);
    if ( v15 )
    {
      v16 = v15;
      v35 = *(_QWORD *)v29;
      while ( 2 )
      {
        v33 = "class";
        v34 = "isKindOfClass:";
        v37 = "tabItem";
        v38 = "title";
        v39 = "isEqualToString:";
        v17 = 0LL;
        v36 = v16;
        do
        {
          if ( *(_QWORD *)v29 != v35 )
            objc_enumerationMutation(obj);
          v18 = *(void **)(*((_QWORD *)&v28 + 1) + 8LL * (_QWORD)v17);
          v19 = _objc_msgSend(&OBJC_CLASS___HXBrowserTabView, v33);
          if ( (unsigned __int8)_objc_msgSend(v18, v34, v19) )
          {
            v20 = _objc_msgSend(v18, v37);
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v32 = v18;
            v22 = v21;
            v23 = _objc_msgSend(v21, v38);
            v24 = objc_retainAutoreleasedReturnValue(v23);
            _objc_msgSend(v24, v39, v40);
            v25 = v22;
            v26 = v32;
            v16 = v36;
            if ( v27 )
            {
              _objc_msgSend(v26, "setDrawRedDot:", (unsigned int)(char)v41);
              goto LABEL_13;
            }
          }
          v17 = (char *)v17 + 1;
        }
        while ( v16 != v17 );
        v16 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v28, v43, 16LL);
        if ( v16 )
          continue;
        break;
      }
    }
LABEL_13:
  }
}

//----- (000000010019D935) ----------------------------------------------------
void __cdecl -[XuanGuViewController setCeLveTongUrl:](XuanGuViewController *self, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  if ( !_objc_msgSend(*(id *)(v5 + 120), "length") && _objc_msgSend(v4, "length") )
  {
    objc_storeStrong((id *)(v6 + 120), a3);
    v8 = _objc_msgSend(v7, "xuanGuTabbarController");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "viewControllers");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v11, "firstObject");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v15 = _objc_msgSend(&OBJC_CLASS___QuoteWebViewController, "class");
    if ( (unsigned __int8)_objc_msgSend(v13, "isKindOfClass:", v15) )
      _objc_msgSend(v13, "loadUrl:", v4);
  }
}

//----- (000000010019DA5C) ----------------------------------------------------
HXBaseView *__cdecl -[XuanGuViewController topView](XuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._topLevelObjects);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010019DA75) ----------------------------------------------------
void __cdecl -[XuanGuViewController setTopView:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._topLevelObjects, a3);
}

//----- (000000010019DA89) ----------------------------------------------------
HXBaseView *__cdecl -[XuanGuViewController contentView](XuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010019DAA2) ----------------------------------------------------
void __cdecl -[XuanGuViewController setContentView:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (000000010019DAB6) ----------------------------------------------------
void __cdecl -[XuanGuViewController setTabContainer:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super._autounbinder, a3);
}

//----- (000000010019DACA) ----------------------------------------------------
void __cdecl -[XuanGuViewController setXuanGuTabbarController:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._designNibBundleIdentifier, a3);
}

//----- (000000010019DADE) ----------------------------------------------------
void __cdecl -[XuanGuViewController setTipArray:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super.__privateData, a3);
}

//----- (000000010019DAF2) ----------------------------------------------------
void __cdecl -[XuanGuViewController setXuanGuTitleArr:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._viewIsAppearing, a3);
}

//----- (000000010019DB06) ----------------------------------------------------
void __cdecl -[XuanGuViewController setXuanGuUrlArr:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._isContentViewController, a3);
}

//----- (000000010019DB1A) ----------------------------------------------------
void __cdecl -[XuanGuViewController setTitleActionDic:](XuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._reserved, a3);
}

//----- (000000010019DB2E) ----------------------------------------------------
NSString *__cdecl -[XuanGuViewController ceLveTongUrl](XuanGuViewController *self, SEL a2)
{
  return *(NSString **)&self->super._shouldRefresh;
}

//----- (000000010019DB3F) ----------------------------------------------------
void __cdecl -[XuanGuViewController .cxx_destruct](XuanGuViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._shouldRefresh, 0LL);
  objc_storeStrong((id *)&self->super.super._reserved, 0LL);
  objc_storeStrong((id *)&self->super.super._isContentViewController, 0LL);
  objc_storeStrong((id *)&self->super.super._viewIsAppearing, 0LL);
  objc_storeStrong(&self->super.super.__privateData, 0LL);
  objc_storeStrong((id *)&self->super.super._designNibBundleIdentifier, 0LL);
  objc_storeStrong(&self->super.super._autounbinder, 0LL);
  objc_destroyWeak((id *)&self->super.super._editors);
  objc_destroyWeak((id *)&self->super.super._topLevelObjects);
}

