id __cdecl +[notificationManager shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100CA594A;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D3AC8 != -1 )
    dispatch_once(&qword_1016D3AC8, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3AC0);
}

//----- (0000000100CA594A) ----------------------------------------------------
void __fastcall sub_100CA594A(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3AC0;
  qword_1016D3AC0 = v2;
}

//----- (0000000100CA597C) ----------------------------------------------------
void __cdecl -[notificationManager userNotificationCenter:didDeliverNotification:](
        notificationManager *self,
        SEL a2,
        id a3,
        id a4)
{
  ;
}

//----- (0000000100CA5982) ----------------------------------------------------
void __cdecl -[notificationManager userNotificationCenter:didActivateNotification:](
        notificationManager *self,
        SEL a2,
        id a3,
        id a4)
{

  objc_retain(a4);
  v4 = _objc_msgSend(&OBJC_CLASS___NSUserNotificationCenter, "defaultUserNotificationCenter");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "removeDeliveredNotification:", v6);
}

//----- (0000000100CA59F8) ----------------------------------------------------
char __cdecl -[notificationManager userNotificationCenter:shouldPresentNotification:](
        notificationManager *self,
        SEL a2,
        id a3,
        id a4)
{
  return 1;
}

//----- (0000000100CA5A03) ----------------------------------------------------
notificationManager *__cdecl -[notificationManager init](notificationManager *self, SEL a2)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___notificationManager;
  return (notificationManager *)objc_msgSendSuper2(&v3, "init");
}

//----- (0000000100CA5A32) ----------------------------------------------------
void __cdecl -[notificationManager showNotificationWithTitle:Content:HasActionBtn:](
        notificationManager *self,
        SEL a2,
        id a3,
        id a4,
        char a5)
{
  unsigned int v15; // [rsp+14h] [rbp-2Ch]

  v15 = a5;
  objc_retain(a4);
  v6 = objc_retain(a3);
  v7 = objc_alloc(&OBJC_CLASS___NSUserNotification);
  v8 = _objc_msgSend(v7, "init");
  _objc_msgSend(v8, "setTitle:", v6);
  _objc_msgSend(v8, "setInformativeText:", v9);
  _objc_msgSend(v8, "setHasActionButton:", v15);
  v11 = _objc_msgSend(&OBJC_CLASS___NSUserNotificationCenter, "defaultUserNotificationCenter");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "scheduleNotification:", v8);
  v13 = _objc_msgSend(&OBJC_CLASS___NSUserNotificationCenter, "defaultUserNotificationCenter");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setDelegate:", self);
}

