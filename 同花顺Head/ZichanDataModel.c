ZichanDataModel *__cdecl -[ZichanDataModel init](ZichanDataModel *self, SEL a2)
{
  ZichanDataModel *v2; // rax
  ZichanDataModel *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___ZichanDataModel;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
    -[ZichanDataModel resetZichanDataModel](v2, "resetZichanDataModel");
  return v3;
}

//----- (0000000100D0CF4E) ----------------------------------------------------
void __cdecl -[ZichanDataModel resetZichanDataModel](ZichanDataModel *self, SEL a2)
{
  -[ZichanDataModel setStrZjzh:](self, "setStrZjzh:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrKhqs:](self, "setStrKhqs:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrHblx:](self, "setStrHblx:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrZzc:](self, "setStrZzc:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrZsz:](self, "setStrZsz:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrZyk:](self, "setStrZyk:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrDryk:](self, "setStrDryk:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrZjye:](self, "setStrZjye:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrDjje:](self, "setStrDjje:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrKqje:](self, "setStrKqje:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrKyje:](self, "setStrKyje:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguZzc:](self, "setStrMeiguZzc:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguGml:](self, "setStrMeiguGml:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguGyfk:](self, "setStrMeiguGyfk:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguZhjb:](self, "setStrMeiguZhjb:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguHl:](self, "setStrMeiguHl:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguZqzz:](self, "setStrMeiguZqzz:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguZhlx:](self, "setStrMeiguZhlx:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguYgzt:](self, "setStrMeiguYgzt:", &charsToLeaveEscaped);
  -[ZichanDataModel setStrMeiguZyk:](self, "setStrMeiguZyk:", &charsToLeaveEscaped);
  -[ZichanDataModel setNeedAddJjsz:](self, "setNeedAddJjsz:", 0LL);
  -[ZichanDataModel setNeedCalcZsz:](self, "setNeedCalcZsz:", 0LL);
  -[ZichanDataModel setNeedCalcZzc:](self, "setNeedCalcZzc:", 0LL);
}

//----- (0000000100D0D0DF) ----------------------------------------------------
id __cdecl -[ZichanDataModel copyWithZone:](ZichanDataModel *self, SEL a2, _NSZone *a3)
{
  signed __int8 v104; // al
  signed __int8 v106; // al
  signed __int8 v108; // al

  v4 = _objc_msgSend(self, "class");
  v5 = _objc_msgSend(v4, "allocWithZone:", a3);
  v7 = _objc_msgSend(v5, "init");
  if ( v7 )
  {
    v8 = _objc_msgSend(v6, "strZjzh");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "copy");
    _objc_msgSend(v7, "setStrZjzh:", v10);
    v12 = _objc_msgSend(v11, "strKhqs");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "copy");
    _objc_msgSend(v7, "setStrKhqs:", v14);
    v16 = _objc_msgSend(v15, "strHblx");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18 = _objc_msgSend(v17, "copy");
    _objc_msgSend(v7, "setStrHblx:", v18);
    v20 = _objc_msgSend(v19, "strZzc");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = _objc_msgSend(v21, "copy");
    _objc_msgSend(v7, "setStrZzc:", v22);
    v24 = _objc_msgSend(v23, "strZsz");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = _objc_msgSend(v25, "copy");
    _objc_msgSend(v7, "setStrZsz:", v26);
    v28 = _objc_msgSend(v27, "strZyk");
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v30 = _objc_msgSend(v29, "copy");
    _objc_msgSend(v7, "setStrZyk:", v30);
    v32 = _objc_msgSend(v31, "strDryk");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = _objc_msgSend(v33, "copy");
    _objc_msgSend(v7, "setStrDryk:", v34);
    v36 = _objc_msgSend(v35, "strZjye");
    v37 = objc_retainAutoreleasedReturnValue(v36);
    v38 = _objc_msgSend(v37, "copy");
    _objc_msgSend(v7, "setStrZjye:", v38);
    v40 = _objc_msgSend(v39, "strDjje");
    v41 = objc_retainAutoreleasedReturnValue(v40);
    v42 = _objc_msgSend(v41, "copy");
    _objc_msgSend(v7, "setStrDjje:", v42);
    v44 = _objc_msgSend(v43, "strKqje");
    v45 = objc_retainAutoreleasedReturnValue(v44);
    v46 = _objc_msgSend(v45, "copy");
    _objc_msgSend(v7, "setStrKqje:", v46);
    v48 = _objc_msgSend(v47, "strKyje");
    v49 = objc_retainAutoreleasedReturnValue(v48);
    v50 = _objc_msgSend(v49, "copy");
    _objc_msgSend(v7, "setStrKyje:", v50);
    v52 = _objc_msgSend(v51, "strSjje");
    v53 = objc_retainAutoreleasedReturnValue(v52);
    v54 = _objc_msgSend(v53, "copy");
    _objc_msgSend(v7, "setStrSjje:", v54);
    v56 = _objc_msgSend(v55, "strJjsz");
    v57 = objc_retainAutoreleasedReturnValue(v56);
    v58 = _objc_msgSend(v57, "copy");
    _objc_msgSend(v7, "setStrJjsz:", v58);
    v60 = _objc_msgSend(v59, "strMeiguZzc");
    v61 = objc_retainAutoreleasedReturnValue(v60);
    v62 = _objc_msgSend(v61, "copy");
    _objc_msgSend(v7, "setStrMeiguZzc:", v62);
    v64 = _objc_msgSend(v63, "strMeiguGml");
    v65 = objc_retainAutoreleasedReturnValue(v64);
    v66 = _objc_msgSend(v65, "copy");
    _objc_msgSend(v7, "setStrMeiguGml:", v66);
    v68 = _objc_msgSend(v67, "strMeiguGyfk");
    v69 = objc_retainAutoreleasedReturnValue(v68);
    v70 = _objc_msgSend(v69, "copy");
    _objc_msgSend(v7, "setStrMeiguGyfk:", v70);
    v72 = _objc_msgSend(v71, "strMeiguZhjb");
    v73 = objc_retainAutoreleasedReturnValue(v72);
    v74 = _objc_msgSend(v73, "copy");
    _objc_msgSend(v7, "setStrMeiguZhjb:", v74);
    v76 = _objc_msgSend(v75, "strMeiguHl");
    v77 = objc_retainAutoreleasedReturnValue(v76);
    v78 = _objc_msgSend(v77, "copy");
    _objc_msgSend(v7, "setStrMeiguHl:", v78);
    v80 = _objc_msgSend(v79, "strMeiguZqzz");
    v81 = objc_retainAutoreleasedReturnValue(v80);
    v82 = _objc_msgSend(v81, "copy");
    _objc_msgSend(v7, "setStrMeiguZqzz:", v82);
    v84 = _objc_msgSend(v83, "strMeiguZhlx");
    v85 = objc_retainAutoreleasedReturnValue(v84);
    v86 = _objc_msgSend(v85, "copy");
    _objc_msgSend(v7, "setStrMeiguZhlx:", v86);
    v88 = _objc_msgSend(v87, "strMeiguYgzt");
    v89 = objc_retainAutoreleasedReturnValue(v88);
    v90 = _objc_msgSend(v89, "copy");
    _objc_msgSend(v7, "setStrMeiguYgzt:", v90);
    v92 = _objc_msgSend(v91, "strMeiguZyk");
    v93 = objc_retainAutoreleasedReturnValue(v92);
    v94 = _objc_msgSend(v93, "copy");
    _objc_msgSend(v7, "setStrMeiguZyk:", v94);
    v96 = _objc_msgSend(v95, "strMeiguT0cs");
    v97 = objc_retainAutoreleasedReturnValue(v96);
    v98 = _objc_msgSend(v97, "copy");
    _objc_msgSend(v7, "setStrMeiguT0cs:", v98);
    v100 = _objc_msgSend(v99, "strMeiguRnfk");
    v101 = objc_retainAutoreleasedReturnValue(v100);
    v102 = _objc_msgSend(v101, "copy");
    _objc_msgSend(v7, "setStrMeiguRnfk:", v102);
    v104 = (unsigned __int8)_objc_msgSend(v103, "needAddJjsz");
    _objc_msgSend(v7, "setNeedAddJjsz:", (unsigned int)v104);
    v106 = (unsigned __int8)_objc_msgSend(v105, "needCalcZsz");
    _objc_msgSend(v7, "setNeedCalcZsz:", (unsigned int)v106);
    v108 = (unsigned __int8)_objc_msgSend(v107, "needCalcZzc");
    _objc_msgSend(v7, "setNeedCalcZzc:", (unsigned int)v108);
  }
  return v7;
}

//----- (0000000100D0D973) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strZjzh](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (0000000100D0D984) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrZjzh:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (0000000100D0D993) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strHblx](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (0000000100D0D9A4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrHblx:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (0000000100D0D9B3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strKhqs](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100D0D9C4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrKhqs:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (0000000100D0D9D3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strZzc](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100D0D9E4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrZzc:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100D0D9F3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strZsz](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 48LL, 0);
}

//----- (0000000100D0DA04) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrZsz:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 48LL);
}

//----- (0000000100D0DA13) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strZyk](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 56LL, 0);
}

//----- (0000000100D0DA24) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrZyk:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 56LL);
}

//----- (0000000100D0DA33) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strDryk](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 64LL, 0);
}

//----- (0000000100D0DA44) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrDryk:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 64LL);
}

//----- (0000000100D0DA53) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strZjye](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 72LL, 0);
}

//----- (0000000100D0DA64) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrZjye:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 72LL);
}

//----- (0000000100D0DA73) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strDjje](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 80LL, 0);
}

//----- (0000000100D0DA84) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrDjje:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 80LL);
}

//----- (0000000100D0DA93) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strKqje](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 88LL, 0);
}

//----- (0000000100D0DAA4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrKqje:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (0000000100D0DAB3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strKyje](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 96LL, 0);
}

//----- (0000000100D0DAC4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrKyje:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (0000000100D0DAD3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strSjje](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 104LL, 0);
}

//----- (0000000100D0DAE4) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrSjje:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 104LL);
}

//----- (0000000100D0DAF3) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strJjsz](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 112LL, 0);
}

//----- (0000000100D0DB04) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrJjsz:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 112LL);
}

//----- (0000000100D0DB13) ----------------------------------------------------
char __cdecl -[ZichanDataModel needAddJjsz](ZichanDataModel *self, SEL a2)
{
  return self->_needAddJjsz;
}

//----- (0000000100D0DB1D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setNeedAddJjsz:](ZichanDataModel *self, SEL a2, char a3)
{
  self->_needAddJjsz = a3;
}

//----- (0000000100D0DB26) ----------------------------------------------------
char __cdecl -[ZichanDataModel needCalcZsz](ZichanDataModel *self, SEL a2)
{
  return self->_needCalcZsz;
}

//----- (0000000100D0DB30) ----------------------------------------------------
void __cdecl -[ZichanDataModel setNeedCalcZsz:](ZichanDataModel *self, SEL a2, char a3)
{
  self->_needCalcZsz = a3;
}

//----- (0000000100D0DB39) ----------------------------------------------------
char __cdecl -[ZichanDataModel needCalcZzc](ZichanDataModel *self, SEL a2)
{
  return self->_needCalcZzc;
}

//----- (0000000100D0DB43) ----------------------------------------------------
void __cdecl -[ZichanDataModel setNeedCalcZzc:](ZichanDataModel *self, SEL a2, char a3)
{
  self->_needCalcZzc = a3;
}

//----- (0000000100D0DB4C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguZzc](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 120LL, 0);
}

//----- (0000000100D0DB5D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguZzc:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 120LL);
}

//----- (0000000100D0DB6C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguGml](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 128LL, 0);
}

//----- (0000000100D0DB7D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguGml:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 128LL);
}

//----- (0000000100D0DB8C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguT0cs](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 136LL, 0);
}

//----- (0000000100D0DB9D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguT0cs:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 136LL);
}

//----- (0000000100D0DBAC) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguRnfk](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 144LL, 0);
}

//----- (0000000100D0DBBD) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguRnfk:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 144LL);
}

//----- (0000000100D0DBCC) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguGyfk](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 152LL, 0);
}

//----- (0000000100D0DBDD) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguGyfk:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 152LL);
}

//----- (0000000100D0DBEC) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguZhjb](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 160LL, 0);
}

//----- (0000000100D0DBFD) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguZhjb:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 160LL);
}

//----- (0000000100D0DC0C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguHl](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 168LL, 0);
}

//----- (0000000100D0DC1D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguHl:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 168LL);
}

//----- (0000000100D0DC2C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguZqzz](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 176LL, 0);
}

//----- (0000000100D0DC3D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguZqzz:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 176LL);
}

//----- (0000000100D0DC4C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguZhlx](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 184LL, 0);
}

//----- (0000000100D0DC5D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguZhlx:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 184LL);
}

//----- (0000000100D0DC6C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguYgzt](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 192LL, 0);
}

//----- (0000000100D0DC7D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguYgzt:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 192LL);
}

//----- (0000000100D0DC8C) ----------------------------------------------------
NSString *__cdecl -[ZichanDataModel strMeiguZyk](ZichanDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 200LL, 0);
}

//----- (0000000100D0DC9D) ----------------------------------------------------
void __cdecl -[ZichanDataModel setStrMeiguZyk:](ZichanDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 200LL);
}

//----- (0000000100D0DCAC) ----------------------------------------------------
void __cdecl -[ZichanDataModel .cxx_destruct](ZichanDataModel *self, SEL a2)
{
  objc_storeStrong((id *)&self->_strMeiguZyk, 0LL);
  objc_storeStrong((id *)&self->_strMeiguYgzt, 0LL);
  objc_storeStrong((id *)&self->_strMeiguZhlx, 0LL);
  objc_storeStrong((id *)&self->_strMeiguZqzz, 0LL);
  objc_storeStrong((id *)&self->_strMeiguHl, 0LL);
  objc_storeStrong((id *)&self->_strMeiguZhjb, 0LL);
  objc_storeStrong((id *)&self->_strMeiguGyfk, 0LL);
  objc_storeStrong((id *)&self->_strMeiguRnfk, 0LL);
  objc_storeStrong((id *)&self->_strMeiguT0cs, 0LL);
  objc_storeStrong((id *)&self->_strMeiguGml, 0LL);
  objc_storeStrong((id *)&self->_strMeiguZzc, 0LL);
  objc_storeStrong((id *)&self->_strJjsz, 0LL);
  objc_storeStrong((id *)&self->_strSjje, 0LL);
  objc_storeStrong((id *)&self->_strKyje, 0LL);
  objc_storeStrong((id *)&self->_strKqje, 0LL);
  objc_storeStrong((id *)&self->_strDjje, 0LL);
  objc_storeStrong((id *)&self->_strZjye, 0LL);
  objc_storeStrong((id *)&self->_strDryk, 0LL);
  objc_storeStrong((id *)&self->_strZyk, 0LL);
  objc_storeStrong((id *)&self->_strZsz, 0LL);
  objc_storeStrong((id *)&self->_strZzc, 0LL);
  objc_storeStrong((id *)&self->_strKhqs, 0LL);
  objc_storeStrong((id *)&self->_strHblx, 0LL);
  objc_storeStrong((id *)&self->_strZjzh, 0LL);
}

