zhuanzhangParam *__cdecl -[zhuanzhangParam init](zhuanzhangParam *self, SEL a2)
{
  zhuanzhangParam *v2; // rax
  zhuanzhangParam *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___zhuanzhangParam;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
    -[zhuanzhangParam resetData](v2, "resetData");
  return v3;
}

//----- (0000000100D283FA) ----------------------------------------------------
void __cdecl -[zhuanzhangParam resetData](zhuanzhangParam *self, SEL a2)
{
  -[zhuanzhangParam setStrYhdm:](self, "setStrYhdm:", &charsToLeaveEscaped);
  -[zhuanzhangParam setStrHbdm:](self, "setStrHbdm:", &charsToLeaveEscaped);
  -[zhuanzhangParam setStrZzje:](self, "setStrZzje:", &charsToLeaveEscaped);
  -[zhuanzhangParam setStrZjmm:](self, "setStrZjmm:", &charsToLeaveEscaped);
  -[zhuanzhangParam setStrYhmm:](self, "setStrYhmm:", &charsToLeaveEscaped);
  -[zhuanzhangParam setStrYhzh:](self, "setStrYhzh:", &charsToLeaveEscaped);
  -[zhuanzhangParam setNReqType:](self, "setNReqType:", 0LL);
}

//----- (0000000100D2848D) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strYhdm](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 8LL, 0);
}

//----- (0000000100D2849E) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrYhdm:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 8LL);
}

//----- (0000000100D284AD) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strHbdm](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (0000000100D284BE) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrHbdm:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (0000000100D284CD) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strZzje](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (0000000100D284DE) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrZzje:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (0000000100D284ED) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strZjmm](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100D284FE) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrZjmm:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (0000000100D2850D) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strYhmm](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100D2851E) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrYhmm:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100D2852D) ----------------------------------------------------
NSString *__cdecl -[zhuanzhangParam strYhzh](zhuanzhangParam *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 48LL, 0);
}

//----- (0000000100D2853E) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setStrYhzh:](zhuanzhangParam *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 48LL);
}

//----- (0000000100D2854D) ----------------------------------------------------
signed __int64 __cdecl -[zhuanzhangParam nReqType](zhuanzhangParam *self, SEL a2)
{
  return self->_nReqType;
}

//----- (0000000100D28557) ----------------------------------------------------
void __cdecl -[zhuanzhangParam setNReqType:](zhuanzhangParam *self, SEL a2, signed __int64 a3)
{
  self->_nReqType = a3;
}

//----- (0000000100D28561) ----------------------------------------------------
void __cdecl -[zhuanzhangParam .cxx_destruct](zhuanzhangParam *self, SEL a2)
{
  objc_storeStrong((id *)&self->_strYhzh, 0LL);
  objc_storeStrong((id *)&self->_strYhmm, 0LL);
  objc_storeStrong((id *)&self->_strZjmm, 0LL);
  objc_storeStrong((id *)&self->_strZzje, 0LL);
  objc_storeStrong((id *)&self->_strHbdm, 0LL);
  objc_storeStrong((id *)&self->_strYhdm, 0LL);
}

