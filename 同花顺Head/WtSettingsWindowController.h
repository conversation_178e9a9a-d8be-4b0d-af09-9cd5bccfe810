//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXBaseTradeWindow.h"

@class NSComboBox;

@interface WtSettingsWindowController : HXBaseTradeWindow
{
    NSComboBox *_wtInfoComboBox;
}

+ (id)shareInstance;

@property __weak NSComboBox *wtInfoComboBox; // @synthesize wtInfoComboBox=_wtInfoComboBox;
- (void)wtDeleteBtnClicked:(id)arg1;
- (void)wtAddBtnClicked:(id)arg1;
- (void)wtASetBtnClicked:(id)arg1;
- (void)windowWillClose:(id)arg1;
- (void)showWindow:(id)arg1;
- (void)windowDidLoad;
- (id)initWithWindowNibName:(id)arg1;

@end

