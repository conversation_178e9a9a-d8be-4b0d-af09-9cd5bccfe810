id __cdecl +[ZiXunTreeRequestModule shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_1005AAB27;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D3098 != -1 )
    dispatch_once(&qword_1016D3098, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3090);
}

//----- (00000001005AAB27) ----------------------------------------------------
void __fastcall sub_1005AAB27(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3090;
  qword_1016D3090 = v2;
}

//----- (00000001005AAB59) ----------------------------------------------------
ZiXunTreeRequestModule *__cdecl -[ZiXunTreeRequestModule init](ZiXunTreeRequestModule *self, SEL a2)
{
  ZiXunTreeRequestModule *v2; // rax
  ZiXunTreeRequestModule *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___ZiXunTreeRequestModule;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
  {
    -[ZiXunTreeRequestModule setHasLocalTreeDatas:](v2, "setHasLocalTreeDatas:", 0LL);
    -[ZiXunTreeRequestModule setGeGuZiXunURL:](v3, "setGeGuZiXunURL:", obj);
    -[ZiXunTreeRequestModule setGongGaoURL:](v3, "setGongGaoURL:", obj);
    -[ZiXunTreeRequestModule setGeGuYanBaoURL:](v3, "setGeGuYanBaoURL:", obj);
  }
  return v3;
}

//----- (00000001005AABEA) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule requestZiXunTree](ZiXunTreeRequestModule *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r14
  id (*v4)(id, SEL, ...); // r12

  if ( !(unsigned __int8)-[ZiXunTreeRequestModule hasLocalTreeDatas](self, "hasLocalTreeDatas") )
  {
    v2 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("id=1000&instance=%ld&lastmodify=0"),
           10101010LL);
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v5 = v4(&OBJC_CLASS___HXSocketCenter, "sharedInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7(v6, "writeTextData:withTimeout:delegate:instance:", v3, self, 10101010LL, 10.0);
  }
}

//----- (00000001005AAC9D) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getGeGuZiXunURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return -[ZiXunTreeRequestModule geGuZiXunURL](self, "geGuZiXunURL");
}

//----- (00000001005AACAF) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getMeiGuGongGaoURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return -[ZiXunTreeRequestModule gongGaoURL](self, "gongGaoURL");
}

//----- (00000001005AACC1) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getNormalGongGaoURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return CFSTR("http://m.10jqka.com.cn/sn/%3/{guid}.shtml");
}

//----- (00000001005AACCE) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getGeGuYanBaoURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return -[ZiXunTreeRequestModule geGuYanBaoURL](self, "geGuYanBaoURL");
}

//----- (00000001005AACE0) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getShiShiJiePanURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return -[ZiXunTreeRequestModule shiShiJiePanURL](self, "shiShiJiePanURL");
}

//----- (00000001005AACF2) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getHttpRequestURL:](ZiXunTreeRequestModule *self, SEL a2, signed __int64 a3)
{
  id result; // rax

  result = &charsToLeaveEscaped;
  if ( a3 <= 7363 )
  {
    if ( a3 > 6145 )
    {
      if ( a3 == 6146 )
      {
        return CFSTR("http://news.10jqka.com.cn/cjzx_list/index.xml");
      }
      else if ( a3 == 7363 )
      {
        return CFSTR("http://stock.10jqka.com.cn/hks/ggydg_list/index.xml");
      }
    }
    else if ( a3 == 2077 )
    {
      return CFSTR("http://index.10jqka.com.cn/list/whfx/index.xml");
    }
    else if ( a3 == 2078 )
    {
      return CFSTR("http://index.10jqka.com.cn/list/510/index.xml");
    }
  }
  else if ( a3 <= 32769 )
  {
    if ( a3 == 7364 )
    {
      return CFSTR("http://stock.10jqka.com.cn/usstock/mggd_list/index.xml");
    }
    else if ( a3 == 14365 )
    {
      return CFSTR("http://goodsfu.10jqka.com.cn/qhgd_list/index.xml");
    }
  }
  else
  {
    switch ( a3 )
    {
      case 49155LL:
        return CFSTR("http://stock.10jqka.com.cn/usstock/gjgs_list/index.xml");
      case 49154LL:
        return CFSTR("http://index.10jqka.com.cn/list/gjcj/index.xml");
      case 32770LL:
        return CFSTR("http://stock.10jqka.com.cn/hks/hknews_list/index.xml");
    }
  }
  return result;
}

//----- (00000001005AADC1) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule receiveStuffData:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  signed int v6; // eax
  SEL v8; // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___HXPCBaseDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = objc_retain(v3);
    v6 = (unsigned int)_objc_msgSend(v5, "instanceId");
    -[ZiXunTreeRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
      self,
      "unregisterInMacDataServiceWithRequstInstanceId:",
      v6);
    v7 = _objc_msgSend(&OBJC_CLASS___PCBinaryDataModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, v8, v7) )
    {
      v9 = objc_retain(v5);
      if ( (unsigned int)_objc_msgSend(v9, "instanceId") == 10101010 )
      {
        -[ZiXunTreeRequestModule parserZiXunTree:](self, "parserZiXunTree:", v10);
        -[ZiXunTreeRequestModule setHasLocalTreeDatas:](self, "setHasLocalTreeDatas:", 1LL);
      }
    }
  }
}

//----- (00000001005AAED1) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule failToReceiveStuffData:](ZiXunTreeRequestModule *self, SEL a2, signed __int64 a3)
{
  -[ZiXunTreeRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
    self,
    "unregisterInMacDataServiceWithRequstInstanceId:");
  if ( a3 == 10101010 )
    -[ZiXunTreeRequestModule setHasLocalTreeDatas:](self, "setHasLocalTreeDatas:", 0LL);
}

//----- (00000001005AAF0F) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule unregisterInMacDataServiceWithRequstInstanceId:](
        ZiXunTreeRequestModule *self,
        SEL a2,
        signed __int64 a3)
{
  HXSocketCenter *v4; // rax
  HXSocketCenter *v5; // rbx

  v4 = +[HXSocketCenter sharedInstance](&OBJC_CLASS___HXSocketCenter, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(v5, "removeObjectFromMapTable:forInstance:", self, a3);
}

//----- (00000001005AAF67) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule parserZiXunTree:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  unsigned int v9; // eax
  unsigned __int64 v12; // rbx
  unsigned __int16 v26; // ax
  unsigned __int64 v35; // [rsp+0h] [rbp-B0h]
  unsigned __int64 v37; // [rsp+10h] [rbp-A0h]

  v40 = objc_retain(a3);
  v3 = _objc_msgSend(v40, "binaryData");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = objc_retainAutorelease(v4);
  v6 = (char *)_objc_msgSend(v5, "bytes");
  objc_alloc((Class)&OBJC_CLASS___UltiTextData);
  v7 = _objc_msgSend(v40, "binaryData");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (unsigned int)_objc_msgSend(v8, "length");
  v36 = v6;
  v11 = _objc_msgSend(v10, "initWithBuffer:length:", v6, v9);
  v38 = v11;
  v12 = (unsigned __int16)_objc_msgSend(v11, "itemSize") + 36LL;
  v13 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v39 = _objc_msgSend(v13, "initWithCapacity:", 0LL);
  v14 = _objc_msgSend(v40, "binaryData");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(v15, "length");
  v17(v15);
  if ( v12 <= (unsigned __int64)v16 )
  {
    v37 = (unsigned __int64)v16;
    do
    {
      v18 = objc_alloc((Class)&OBJC_CLASS___UltiTextDataItem);
      v19 = _objc_msgSend(v18, "initWithBuffer:length:", &v36[v12], (unsigned int)((_DWORD)v16 - v12));
      if ( !v19 )
        break;
      v20 = v19;
      v35 = (unsigned __int16)_objc_msgSend(v19, "totalSize") + v12;
      v21 = objc_alloc((Class)&OBJC_CLASS___NewsTreeItem);
      _objc_msgSend(v21, "init");
      v22 = _objc_msgSend(v20, "name");
      v16 = objc_retainAutoreleasedReturnValue(v22);
      v23 = -[ZiXunTreeRequestModule getName:](self, "getName:", v16);
      v24 = objc_retainAutoreleasedReturnValue(v23);
      _objc_msgSend(v25, "setName:", v24);
      v26 = (unsigned __int16)_objc_msgSend(v20, "ID");
      _objc_msgSend(v27, "setID:", v26);
      v28 = _objc_msgSend(v20, "name");
      v29 = objc_retainAutoreleasedReturnValue(v28);
      v30 = -[ZiXunTreeRequestModule getURL:](self, "getURL:", v29);
      v31 = objc_retainAutoreleasedReturnValue(v30);
      _objc_msgSend(v32, "setURL:", v31);
      _objc_msgSend(v39, "addObject:", v33);
      LODWORD(v16) = v37;
      v12 = v35;
    }
    while ( v35 <= v37 );
  }
  -[ZiXunTreeRequestModule filterZiXunURL:](self, "filterZiXunURL:", v39);
}

//----- (00000001005AB2C5) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule filterZiXunURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  SEL v21; // [rsp+48h] [rbp-108h]
  SEL v23; // [rsp+58h] [rbp-F8h]
  SEL v24; // [rsp+60h] [rbp-F0h]
  SEL v25; // [rsp+68h] [rbp-E8h]
  SEL v26; // [rsp+70h] [rbp-E0h]
  SEL v27; // [rsp+78h] [rbp-D8h]
  SEL v30; // [rsp+90h] [rbp-C0h]
  id obj; // [rsp+98h] [rbp-B8h]

  v29 = self;
  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v20 = 0LL;
    v19 = 0LL;
    v18 = 0LL;
    v17 = 0LL;
    v28 = v4;
    obj = objc_retain(v4);
    v5 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v32, 16LL);
    if ( v5 )
    {
      v6 = (__int64)v5;
      v22 = *(_QWORD *)v18;
LABEL_5:
      v21 = "class";
      v23 = "ID";
      v30 = "URL";
      v24 = "setShiShiJiePanURL:";
      v25 = "setGeGuYanBaoURL:";
      v26 = "setGongGaoURL:";
      v27 = "setGeGuZiXunURL:";
      if ( !v6 )
        v6 = 1LL;
      v7 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v18 != v22 )
          objc_enumerationMutation(obj);
        v8 = *(void **)(*((_QWORD *)&v17 + 1) + 8 * v7);
        v9 = _objc_msgSend(&OBJC_CLASS___NewsTreeItem, v21);
        if ( !(unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v9) )
          break;
        v10 = (__int64)_objc_msgSend(v8, v23);
        if ( v10 > 14341 )
        {
          if ( v10 == 14342 )
          {
            v16 = _objc_msgSend(v8, v30);
            v13 = objc_retainAutoreleasedReturnValue(v16);
            _objc_msgSend(v29, v25, v13);
            goto LABEL_20;
          }
          if ( v10 == 57344 )
          {
            v14 = _objc_msgSend(v8, v30);
            v13 = objc_retainAutoreleasedReturnValue(v14);
            _objc_msgSend(v29, v24, v13);
            goto LABEL_20;
          }
        }
        else
        {
          if ( v10 == 14339 )
          {
            v15 = _objc_msgSend(v8, v30);
            v13 = objc_retainAutoreleasedReturnValue(v15);
            _objc_msgSend(v29, v27, v13);
            goto LABEL_20;
          }
          if ( v10 == 14341 )
          {
            v12 = _objc_msgSend(v8, v30);
            v13 = objc_retainAutoreleasedReturnValue(v12);
            _objc_msgSend(v29, v26, v13);
LABEL_20:
          }
        }
        v7 = v11 + 1;
        if ( v6 == v7 )
        {
          v6 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v17, v32, 16LL);
          if ( v6 )
            goto LABEL_5;
          break;
        }
      }
    }
    v4 = v28;
  }
}

//----- (00000001005AB5D2) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getName:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = (char *)_objc_msgSend(v3, "rangeOfString:options:", CFSTR("[a-z|A-Z]*.[0-9]*[一-龥]+"), 1024LL);
    if ( &v4[v5] <= _objc_msgSend(v3, "length") )
    {
      v7 = _objc_msgSend(v3, "substringWithRange:", v4, v6);
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v3 = v8;
    }
    v3 = objc_retain(v3);
    v9 = v3;
  }
  else
  {
    v9 = 0LL;
  }
  return objc_autoreleaseReturnValue(v9);
}

//----- (00000001005AB69E) ----------------------------------------------------
id __cdecl -[ZiXunTreeRequestModule getURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  SEL v6; // r12
  SEL v8; // r12
  unsigned __int64 v10; // r12

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = (char *)_objc_msgSend(v3, "rangeOfString:options:", CFSTR("[a-z|A-Z]*.[0-9]*[一-龥]+"), 1024LL);
    v21 = v5;
    v7 = (char *)_objc_msgSend(v3, v6);
    v9 = _objc_msgSend(v3, v8);
    if ( v10 <= (unsigned __int64)v9 )
    {
      v12 = _objc_msgSend(v3, "substringWithRange:", &v4[v21], &v7[-v21]);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = _objc_msgSend(v13, "componentsSeparatedByString:", CFSTR("?"));
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = _objc_msgSend(v15, "objectAtIndexedSubscript:", 0LL);
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18(v13);
      v3 = objc_retain(v17);
      v19(v15);
    }
    else
    {
      v3 = objc_retain(v3);
    }
    v11 = v3;
  }
  else
  {
    v11 = 0LL;
  }
  return objc_autoreleaseReturnValue(v11);
}

//----- (00000001005AB7E0) ----------------------------------------------------
char __cdecl -[ZiXunTreeRequestModule hasLocalTreeDatas](ZiXunTreeRequestModule *self, SEL a2)
{
  return self->_hasLocalTreeDatas;
}

//----- (00000001005AB7EA) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule setHasLocalTreeDatas:](ZiXunTreeRequestModule *self, SEL a2, char a3)
{
  self->_hasLocalTreeDatas = a3;
}

//----- (00000001005AB7F3) ----------------------------------------------------
NSString *__cdecl -[ZiXunTreeRequestModule geGuZiXunURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (00000001005AB804) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule setGeGuZiXunURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (00000001005AB813) ----------------------------------------------------
NSString *__cdecl -[ZiXunTreeRequestModule gongGaoURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (00000001005AB824) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule setGongGaoURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (00000001005AB833) ----------------------------------------------------
NSString *__cdecl -[ZiXunTreeRequestModule geGuYanBaoURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (00000001005AB844) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule setGeGuYanBaoURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (00000001005AB853) ----------------------------------------------------
NSString *__cdecl -[ZiXunTreeRequestModule shiShiJiePanURL](ZiXunTreeRequestModule *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (00000001005AB864) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule setShiShiJiePanURL:](ZiXunTreeRequestModule *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (00000001005AB873) ----------------------------------------------------
void __cdecl -[ZiXunTreeRequestModule .cxx_destruct](ZiXunTreeRequestModule *self, SEL a2)
{
  objc_storeStrong((id *)&self->_shiShiJiePanURL, 0LL);
  objc_storeStrong((id *)&self->_geGuYanBaoURL, 0LL);
  objc_storeStrong((id *)&self->_gongGaoURL, 0LL);
  objc_storeStrong((id *)&self->_geGuZiXunURL, 0LL);
}

