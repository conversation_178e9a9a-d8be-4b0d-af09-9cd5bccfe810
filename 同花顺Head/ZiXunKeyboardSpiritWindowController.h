//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSWindowController.h>

@class HXKeyboardSpiritPromptViewController, HXSearchTextFieldView, HXTabbarController, NSMutableArray, NSString, ZiXunKeyboradSpiritViewController;

@interface ZiXunKeyboardSpiritWindowController : NSWindowController
{
    HXSearchTextFieldView *_keyboardTextFieldView;
    NSMutableArray *_searchHistoryArray;
    NSString *_stockCode;
    NSString *_stockName;
    ZiXunKeyboradSpiritViewController *_keyboardVC;
    HXKeyboardSpiritPromptViewController *_promptVC;
    HXTabbarController *_tabbarController;
    id _localEventMonitor;
}

+ (id)sharedInstance;

@property(retain, nonatomic) id localEventMonitor; // @synthesize localEventMonitor=_localEventMonitor;
@property(retain, nonatomic) HXTabbarController *tabbarController; // @synthesize tabbarController=_tabbarController;
@property(retain, nonatomic) HXKeyboardSpiritPromptViewController *promptVC; // @synthesize promptVC=_promptVC;
@property(retain, nonatomic) ZiXunKeyboradSpiritViewController *keyboardVC; // @synthesize keyboardVC=_keyboardVC;
@property(retain, nonatomic) NSString *stockName; // @synthesize stockName=_stockName;
@property(retain, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
@property(retain, nonatomic) NSMutableArray *searchHistoryArray; // @synthesize searchHistoryArray=_searchHistoryArray;
@property(nonatomic) __weak HXSearchTextFieldView *keyboardTextFieldView; // @synthesize keyboardTextFieldView=_keyboardTextFieldView;
- (void)setSearchResultBlock:(CDUnknownBlockType)arg1;
- (void)selectNext;
- (void)selectPrevious;
- (void)keyboardSpiritRowDoubleClicked;
- (void)keyboardSpiritRowClicked;
- (void)backToMain;
- (void)localMouseEvent:(id)arg1;
- (void)windowDidShow;
- (void)textDidChange;
- (void)voiceRequest:(id)arg1;
- (void)keyDownThenRequest:(id)arg1;
- (void)close;
- (void)windowDidLoad;

@end

