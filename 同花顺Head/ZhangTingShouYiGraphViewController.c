void __cdecl -[ZhangTingShouYiGraphViewController viewDidLoad](ZhangTingShouYiGraphViewController *self, SEL a2)
{
  id (*v2)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  v10.receiver = self;
  v10.super_class = (Class)&OBJC_CLASS___ZhangTingShouYiGraphViewController;
  -[GraphBaseViewController viewDidLoad](&v10, "viewDidLoad");
  -[GraphBaseViewController setCurrentPlotType:](self, "setCurrentPlotType:", 111LL);
  v3 = v2(self, "titleLabel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5(v4, "setStringValue:", CFSTR("昨日涨停今收益"));
  v7 = v6(self, "infoMainString");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(
    v8,
    "setStringValue:",
    CFSTR("该数据实时计算昨天涨停个股(剔除新股)今天的涨幅情况。多数投资者用来观察今天盘面的赚钱效应，因为只有当活跃资金短期获得较大收益时，才能刺激市场吸引更多资金。涨幅>=4%，即可认为赚钱效应较强。"));
}

//----- (0000000100640017) ----------------------------------------------------
void __cdecl -[ZhangTingShouYiGraphViewController requestForModularData:market:](
        ZhangTingShouYiGraphViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  NSNumber *v11; // rax
  NSNumber *v12; // rbx
  NSDictionary *v13; // rax
  SimpleGraphDataRequestModule *v14; // rax
  id (__cdecl *v15)(id); // r12
  id (__cdecl *v17)(id); // r12
  _QWORD v19[4]; // [rsp+0h] [rbp-A0h] BYREF
  id to; // [rsp+28h] [rbp-78h] BYREF
  id location; // [rsp+30h] [rbp-70h] BYREF

  v5 = objc_retain(a3);
  v7 = v6(a4);
  v8 = v7;
  if ( v7 )
  {
    v9 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped);
    if ( v5 )
    {
      if ( !v9 && !(unsigned __int8)_objc_msgSend(v5, "isEqualToString:", &charsToLeaveEscaped) )
      {
        -[GraphBaseViewController jugeCanRequset:market:](self, "jugeCanRequset:market:", v5, v8);
        objc_initWeak(&location, self);
        if ( !(unsigned __int8)-[GraphBaseViewController modularRequseted](self, "modularRequseted") )
        {
          v24[0] = (__int64)off_1012E2B50;
          v25[0] = (__int64)CFSTR("883900");
          v24[1] = (__int64)off_1012E2B58;
          v25[1] = (__int64)CFSTR("URFI");
          v24[2] = (__int64)off_1012E2B60;
          v10 = -[GraphBaseViewController currentPlotType](self, "currentPlotType");
          v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v10);
          v12 = objc_retainAutoreleasedReturnValue(v11);
          v25[2] = (__int64)v12;
          v13 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v25, v24, 3LL);
          v23 = objc_retainAutoreleasedReturnValue(v13);
          v14 = -[GraphBaseViewController SGRequest](self, "SGRequest");
          v16 = v15(v14);
          v19[0] = _NSConcreteStackBlock;
          v19[1] = 3254779904LL;
          v19[2] = sub_10064028D;
          v19[3] = &unk_1012DAB30;
          objc_copyWeak(&to, &location);
          v20 = v17(v23);
          v23 = v16;
          _objc_msgSend(v16, "requestZhangTingShouYi:callBack:", v20, v19);
          objc_destroyWeak(&to);
        }
        objc_destroyWeak(&location);
      }
    }
  }
}

//----- (000000010064028D) ----------------------------------------------------
void __fastcall sub_10064028D(__int64 a1, void *a2)
{
  NSNumber *v6; // rax
  NSNumber *v7; // r13
  NSNumber *v12; // rdi
  id WeakRetained; // rbx
  id *v17; // r12
  id *v21; // r12
  id *v23; // r12

  v2 = objc_retain(a2);
  v3 = objc_retain(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = _objc_msgSend(v3, "dicExt");
    v27 = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v25 = v3;
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v27, "objectForKey:", v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v9);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = v7;
    v3 = v25;
    v13(v12);
    v15 = v14;
    v14(v27);
    if ( (unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("883900")) )
    {
      WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
      _objc_msgSend(WeakRetained, "setModularRequseted:", 1LL);
      ((void (__fastcall *)(id))v15)(WeakRetained);
      v18 = objc_loadWeakRetained(v17);
      v19 = _objc_msgSend(v18, "SGParser");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      _objc_msgSend(v20, "replaceAllWithZhangTingShouYiDataModel:", v25);
      v22 = objc_loadWeakRetained(v21);
      _objc_msgSend(v22, "updatePlotWhenParseFinished");
      v24 = objc_loadWeakRetained(v23);
      _objc_msgSend(v24, "order:", *(_QWORD *)(a1 + 32));
    }
  }
}

//----- (0000000100640483) ----------------------------------------------------
void __cdecl -[ZhangTingShouYiGraphViewController order:](ZhangTingShouYiGraphViewController *self, SEL a2, id a3)
{
  SimpleGraphDataRequestModule *v4; // rax
  SimpleGraphDataRequestModule *v5; // r15
  _QWORD v6[4]; // [rsp+8h] [rbp-48h] BYREF
  id to; // [rsp+28h] [rbp-28h] BYREF
  id location[4]; // [rsp+30h] [rbp-20h] BYREF

  v3 = objc_retain(a3);
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    objc_initWeak(location, self);
    v4 = -[GraphBaseViewController SGRequest](self, "SGRequest");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6[0] = _NSConcreteStackBlock;
    v6[1] = 3254779904LL;
    v6[2] = sub_10064057E;
    v6[3] = &unk_1012DAB60;
    objc_copyWeak(&to, location);
    -[SimpleGraphDataRequestModule order:callBack:](v5, "order:callBack:", v3, v6);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (000000010064057E) ----------------------------------------------------
void __fastcall sub_10064057E(__int64 a1, void *a2)
{
  id WeakRetained; // rbx
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  id *v15; // r12
  id *v19; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(v3 + 32));
  v5 = _objc_msgSend(WeakRetained, "currentPlotType");
  if ( v5 == (id)111 )
  {
    v6 = objc_retain(v2);
    v7 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
    {
      v22 = v2;
      v8 = _objc_msgSend(v6, "dicExt");
      v21 = v6;
      v23 = objc_retainAutoreleasedReturnValue(v8);
      v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v23, "objectForKey:", v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v12);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v6 = v21;
      if ( (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", CFSTR("883900")) )
      {
        v16 = objc_loadWeakRetained(v15);
        v17 = _objc_msgSend(v16, "SGParser");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        _objc_msgSend(v18, "updateDataModel:withPlotType:", v21, 111LL);
        v20 = objc_loadWeakRetained(v19);
        _objc_msgSend(v20, "updatePlotWhenParseFinished");
      }
      v2 = v22;
    }
  }
}

//----- (0000000100640771) ----------------------------------------------------
void __cdecl -[ZhangTingShouYiGraphViewController deleteOrder](ZhangTingShouYiGraphViewController *self, SEL a2)
{
  NSDictionary *v2; // rax
  NSDictionary *v3; // r15
  id (*v4)(id, SEL, ...); // r12
  _QWORD v8[2]; // [rsp+8h] [rbp-48h] BYREF
  _QWORD v9[2]; // [rsp+18h] [rbp-38h] BYREF

  v8[0] = off_1012E2B50;
  v9[0] = CFSTR("883900");
  v8[1] = off_1012E2B58;
  v9[1] = CFSTR("URFI");
  v2 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v9, v8, 2LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "SGRequest");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "deleteOrder:", v3);
}

//----- (000000010064084F) ----------------------------------------------------
id __cdecl -[ZhangTingShouYiGraphViewController createDataForPlot](ZhangTingShouYiGraphViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "SGParser");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "getZhangTingShouYiData");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "setObject:forKeyedSubscript:", v9, off_1012E3D00);
  v11(v6);
  return objc_autoreleaseReturnValue(v3);
}

