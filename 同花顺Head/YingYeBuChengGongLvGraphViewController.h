//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "GraphBaseViewController.h"

@class HXBaseView, NSString;

@interface YingYeBuChengGongLvGraphViewController : GraphBaseViewController
{
    NSString *_jiBie;
    HXBaseView *_topView;
}


@property __weak HXBaseView *topView; // @synthesize topView=_topView;
- (id)createDataForPlot;
- (void)dealWithRequestData:(id)arg1;
- (void)requestDataWithDict:(id)arg1 buy:(BOOL)arg2;
- (void)frameDidChaged:(id)arg1;
- (void)dealloc;
- (void)viewDidLoad;

@end

