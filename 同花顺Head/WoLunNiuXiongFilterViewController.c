void __cdecl -[WoLunNiuXiongFilterViewController viewDidLoad](WoLunNiuXiongFilterViewController *self, SEL a2)
{
  HXBaseScrollView *v2; // rax
  HXBaseScrollView *v3; // r14
  WoLunNiuXiongFilterDocumentView *v4; // rax
  WoLunNiuXiongFilterDocumentView *v5; // rbx

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___WoLunNiuXiongFilterViewController;
  -[HXBaseViewController viewDidLoad](&v6, "viewDidLoad");
  v2 = -[WoLunNiuXiongFilterViewController filterScrollView](self, "filterScrollView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[WoLunNiuXiongFilterViewController filterViewController](self, "filterViewController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v3, "setDocumentView:", v5);
}

//----- (0000000100166CD7) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController viewWillDisappear](WoLunNiuXiongFilterViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___WoLunNiuXiongFilterViewController;
  objc_msgSendSuper2(&v2, "viewWillDisappear");
  -[WoLunNiuXiongFilterViewController keyDownFromSuper:](self, "keyDownFromSuper:", 0LL);
}

//----- (0000000100166D1A) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController keyDownFromSuper:](
        WoLunNiuXiongFilterViewController *self,
        SEL a2,
        id a3)
{
  _objc_msgSend(self->super.super._autounbinder, "keyDownFromSuper:", a3);
}

//----- (0000000100166D37) ----------------------------------------------------
WoLunNiuXiongFilterDocumentView *__cdecl -[WoLunNiuXiongFilterViewController filterViewController](
        WoLunNiuXiongFilterViewController *self,
        SEL a2)
{
  id autounbinder; // rdi
  id WeakRetained; // r14
  id location[6]; // [rsp+40h] [rbp-30h] BYREF

  autounbinder = self->super.super._autounbinder;
  if ( !autounbinder )
  {
    objc_initWeak(location, self);
    v4 = objc_alloc((Class)&OBJC_CLASS___WoLunNiuXiongFilterDocumentView);
    v6 = _objc_msgSend(v5, "view");
    v7 = (const char *)objc_retainAutoreleasedReturnValue(v6);
    v8 = (char *)v7;
    if ( v7 )
      objc_msgSend_stret(v18, v7, "bounds");
    else
      memset(v18, 0, sizeof(v18));
    v9 = _objc_msgSend(v4, "initWithFrame:");
    v11 = *(void **)(v10 + 72);
    *(_QWORD *)(v10 + 72) = v9;
    WeakRetained = objc_loadWeakRetained(location);
    v13 = _objc_msgSend(WeakRetained, "filterCondictionChangedCallBack");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(*(id *)(v15 + 72), "setFilterCondictionChangedCallBack:", v14);
    objc_destroyWeak(location);
    autounbinder = *(id *)(v16 + 72);
  }
  return (WoLunNiuXiongFilterDocumentView *)objc_retainAutoreleaseReturnValue(autounbinder);
}

//----- (0000000100166E83) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController redisplayFilterView:](
        WoLunNiuXiongFilterViewController *self,
        SEL a2,
        id a3)
{
  WoLunNiuXiongFilterDocumentView *v3; // rax
  WoLunNiuXiongFilterDocumentView *v4; // rbx

  objc_retain(a3);
  v3 = -[WoLunNiuXiongFilterViewController filterViewController](self, "filterViewController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[WoLunNiuXiongFilterDocumentView redisplayFilterView:](v4, "redisplayFilterView:", v5);
}

//----- (0000000100166EF5) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController setFilterCondictionChangedCallBack:](
        WoLunNiuXiongFilterViewController *self,
        SEL a2,
        id a3)
{
  NSArray *v4; // rax
  NSArray *topLevelObjects; // rdi

  v4 = (NSArray *)objc_retainBlock(a3);
  topLevelObjects = self->super.super._topLevelObjects;
  self->super.super._topLevelObjects = v4;
}

//----- (0000000100166F21) ----------------------------------------------------
id __cdecl -[WoLunNiuXiongFilterViewController filterCondictionChangedCallBack](
        WoLunNiuXiongFilterViewController *self,
        SEL a2)
{
  return objc_getProperty(self, a2, 56LL, 0);
}

//----- (0000000100166F34) ----------------------------------------------------
HXBaseScrollView *__cdecl -[WoLunNiuXiongFilterViewController filterScrollView](
        WoLunNiuXiongFilterViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (HXBaseScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100166F4D) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController setFilterScrollView:](
        WoLunNiuXiongFilterViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (0000000100166F61) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController setFilterViewController:](
        WoLunNiuXiongFilterViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super.super._autounbinder, a3);
}

//----- (0000000100166F75) ----------------------------------------------------
void __cdecl -[WoLunNiuXiongFilterViewController .cxx_destruct](WoLunNiuXiongFilterViewController *self, SEL a2)
{
  objc_storeStrong(&self->super.super._autounbinder, 0LL);
  objc_destroyWeak((id *)&self->super.super._editors);
  objc_storeStrong((id *)&self->super.super._topLevelObjects, 0LL);
}

