void __cdecl -[ZTFenXiHeaderCell drawWithFrame:inView:](ZTFenXiHeaderCell *self, SEL a2, CGRect a3, id a4)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  __m128d v28; // xmm0
  __m128d v29; // xmm1
  unsigned __int64 v34; // [rsp+50h] [rbp-100h]
  CGRect v35; // [rsp+60h] [rbp-F0h]
  __m128d v36; // [rsp+80h] [rbp-D0h]
  CGPoint origin; // [rsp+A0h] [rbp-B0h]
  __m128d v39; // [rsp+B0h] [rbp-A0h]
  __m128d v43; // [rsp+E0h] [rbp-70h]
  __m128d size; // [rsp+F0h] [rbp-60h]
  _QWORD v45[2]; // [rsp+100h] [rbp-50h] BYREF
  _QWORD v46[2]; // [rsp+110h] [rbp-40h] BYREF

  v4 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("widgetInfo"));
  v40 = objc_retainAutoreleasedReturnValue(v4);
  origin = a3.origin;
  size = (__m128d)a3.size;
  v35 = a3;
  v43 = (__m128d)*(unsigned __int64 *)&a3.origin.y;
  v6 = v5(&OBJC_CLASS___NSBezierPath, "bezierPathWithRoundedRect:xRadius:yRadius:", 0.0, 0.0);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(&OBJC_CLASS___HXThemeManager, "tableHeaderBgColor");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11(v10, "setFill");
  v41 = v7;
  v12(v7, "fill");
  v42 = v13(v7, "copy");
  v15 = v14(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17(v16, "setStroke");
  v18(v42, "stroke");
  v45[0] = NSFontAttributeName;
  v20 = v19(&OBJC_CLASS___NSFont, "systemFontOfSize:", 11.0);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v46[0] = v21;
  v45[1] = NSForegroundColorAttributeName;
  v23 = v22(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v46[1] = v24;
  v26 = v25(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v46, v45, 2LL);
  v27 = objc_retainAutoreleasedReturnValue(v26);
  objc_msgSend_stret(
    v33,
    stru_101322098,
    "boundingRectWithSize:options:attributes:",
    3LL,
    v27,
    a3.size.width,
    3.402823466385289e38);
  v39 = (__m128d)v34;
  v28.f64[1] = size.f64[1];
  v28.f64[0] = (size.f64[0] - *(double *)&v34) * 0.5 + origin.x + -5.0;
  size = v28;
  v29.f64[1] = v43.f64[1];
  v29.f64[0] = v43.f64[0] + 3.0;
  v43 = v29;
  v30(stru_101322098, "drawAtPoint:withAttributes:", v27);
  v36 = _mm_add_pd(_mm_shuffle_pd(size, (__m128d)xmmword_1010DD530, 2), _mm_unpacklo_pd(v39, v43));
  v37 = xmmword_1010CE660;
  v31 = v40;
  v32(
    v40,
    "drawInRect:fromRect:operation:fraction:respectFlipped:hints:",
    2LL,
    1LL,
    0LL,
    1.0,
    *(_OWORD *)&v36,
    0x4032000000000000LL,
    0x4032000000000000LL,
    *(_OWORD *)&NSZeroRect.origin,
    *(_QWORD *)&NSZeroRect.size.width,
    *(_QWORD *)&NSZeroRect.size.height);
}

