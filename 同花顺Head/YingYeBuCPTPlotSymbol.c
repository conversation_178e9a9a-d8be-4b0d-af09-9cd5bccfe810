char __cdecl -[YingYeBuCPTPlotSymbol buy](YingYeBuCPTPlotSymbol *self, SEL a2)
{
  return self->_buy;
}

//----- (00000001000A90AA) ----------------------------------------------------
void __cdecl -[YingYeBuCPTPlotSymbol setBuy:](YingYeBuCPTPlotSymbol *self, SEL a2, char a3)
{
  self->_buy = a3;
}

//----- (00000001000A90BA) ----------------------------------------------------
char __cdecl -[YingYeBuCPTPlotSymbol selected](YingYeBuCPTPlotSymbol *self, SEL a2)
{
  return self->_selected;
}

//----- (00000001000A90CB) ----------------------------------------------------
void __cdecl -[YingYeBuCPTPlotSymbol setSelected:](YingYeBuCPTPlotSymbol *self, SEL a2, char a3)
{
  self->_selected = a3;
}

//----- (00000001000A90DB) ----------------------------------------------------
CPTScatterPlot *__cdecl -[YingYeBuCPTPlotSymbol curLayer](YingYeBuCPTPlotSymbol *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_curLayer);
  return (CPTScatterPlot *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001000A90F4) ----------------------------------------------------
void __cdecl -[YingYeBuCPTPlotSymbol setCurLayer:](YingYeBuCPTPlotSymbol *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_curLayer, a3);
}

//----- (00000001000A9108) ----------------------------------------------------
YingYeBuSymbolAnimationLayer *__cdecl -[YingYeBuCPTPlotSymbol animationLayer](YingYeBuCPTPlotSymbol *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_animationLayer);
  return (YingYeBuSymbolAnimationLayer *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001000A9121) ----------------------------------------------------
void __cdecl -[YingYeBuCPTPlotSymbol setAnimationLayer:](YingYeBuCPTPlotSymbol *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_animationLayer, a3);
}

//----- (00000001000A9135) ----------------------------------------------------
void __cdecl -[YingYeBuCPTPlotSymbol .cxx_destruct](YingYeBuCPTPlotSymbol *self, SEL a2)
{
  objc_destroyWeak((id *)&self->_animationLayer);
  objc_destroyWeak((id *)&self->_curLayer);
}

