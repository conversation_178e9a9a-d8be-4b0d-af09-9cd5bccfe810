void __cdecl -[serverListDataModel resetData](serverListDataModel *self, SEL a2)
{
  -[serverListDataModel setStrQsid:](self, "setStrQsid:", &charsToLeaveEscaped);
  -[serverListDataModel setStrYybrid:](self, "setStrYybrid:", &charsToLeaveEscaped);
  -[serverListDataModel setStrYybid:](self, "setStrYybid:", &charsToLeaveEscaped);
  -[serverListDataModel setStrVersion:](self, "setStrVersion:", &charsToLeaveEscaped);
  -[serverListDataModel setStrBrokerName:](self, "setStrBrokerName:", &charsToLeaveEscaped);
  -[serverListDataModel setStrConnectMode:](self, "setStrConnectMode:", &charsToLeaveEscaped);
  -[serverListDataModel setStrMacLoginVersion:](self, "setStrMacLoginVersion:", &charsToLeaveEscaped);
  -[serverListDataModel setStryybFunc:](self, "setStryybFunc:", &charsToLeaveEscaped);
}

//----- (0000000100C57534) ----------------------------------------------------
serverListDataModel *__cdecl -[serverListDataModel initWithName:TypeData:TypeValue:](
        serverListDataModel *self,
        SEL a2,
        id a3,
        id a4,
        id a5)
{
  serverListDataModel *v12; // rax
  __CFString *v19; // r13
  __CFString *v22; // rbx
  __CFString *v27; // rbx
  __CFString *v30; // r13
  __CFString *v35; // rbx
  __CFString *v38; // r13
  __CFString *v43; // rbx
  __CFString *v46; // r13
  __CFString *v51; // rbx
  serverListDataModel *v58; // r12

  v7 = objc_retain(a3);
  v8 = a5;
  v9 = objc_retain(a4);
  v10 = objc_retain(v8);
  v60.receiver = v11;
  v60.super_class = (Class)&OBJC_CLASS___serverListDataModel;
  v61 = v10;
  v12 = objc_msgSendSuper2(&v60, "init");
  if ( v12 )
  {
    -[serverListDataModel resetData](v12, "resetData");
    if ( v9 )
    {
      v14 = _objc_msgSend(v9, "componentsSeparatedByString:", CFSTR("#_#"));
      v15 = objc_retainAutoreleasedReturnValue(v14);
      if ( v15 )
      {
        v62 = v7;
        v63 = v15;
        v17 = (__int64)_objc_msgSend(v15, "count");
        if ( v17 <= 0 )
        {
          v19 = &charsToLeaveEscaped;
          _objc_msgSend(v16, "setStrQsid:", &charsToLeaveEscaped);
        }
        else
        {
          v18 = _objc_msgSend(v63, "objectAtIndex:", 0LL);
          v19 = (__CFString *)objc_retainAutoreleasedReturnValue(v18);
          _objc_msgSend(v20, "setStrQsid:", v19);
        }
        v22 = &charsToLeaveEscaped;
        {
          v23 = 0;
        }
        else
        {
          v24 = _objc_msgSend(v63, "objectAtIndex:", 1LL);
          v22 = (__CFString *)objc_retainAutoreleasedReturnValue(v24);
          v23 = 1;
        }
        _objc_msgSend(v21, "setStrYybrid:", v22);
        if ( v23 )
        if ( v17 < 3 )
        {
          v27 = &charsToLeaveEscaped;
          _objc_msgSend(v25, "setStrYybid:", &charsToLeaveEscaped);
        }
        else
        {
          v26 = _objc_msgSend(v63, "objectAtIndex:", 2LL);
          v27 = (__CFString *)objc_retainAutoreleasedReturnValue(v26);
          _objc_msgSend(v28, "setStrYybid:", v27);
        }
        v30 = &charsToLeaveEscaped;
        {
          v31 = 0;
        }
        else
        {
          v32 = _objc_msgSend(v63, "objectAtIndex:", 3LL);
          v30 = (__CFString *)objc_retainAutoreleasedReturnValue(v32);
          v31 = 1;
        }
        _objc_msgSend(v29, "setStrVersion:", v30);
        if ( v31 )
        if ( v17 < 5 )
        {
          v35 = &charsToLeaveEscaped;
          _objc_msgSend(v33, "setStrAuthType:", &charsToLeaveEscaped);
        }
        else
        {
          v34 = _objc_msgSend(v63, "objectAtIndex:", 4LL);
          v35 = (__CFString *)objc_retainAutoreleasedReturnValue(v34);
          _objc_msgSend(v36, "setStrAuthType:", v35);
        }
        v38 = &charsToLeaveEscaped;
        {
          v39 = 0;
        }
        else
        {
          v40 = _objc_msgSend(v63, "objectAtIndex:", 5LL);
          v38 = (__CFString *)objc_retainAutoreleasedReturnValue(v40);
          v39 = 1;
        }
        _objc_msgSend(v37, "setStrAccountType:", v38);
        if ( v39 )
        if ( v17 < 7 )
        {
          v43 = &charsToLeaveEscaped;
          _objc_msgSend(v41, "setStrMacVersion:", &charsToLeaveEscaped);
        }
        else
        {
          v42 = _objc_msgSend(v63, "objectAtIndex:", 6LL);
          v43 = (__CFString *)objc_retainAutoreleasedReturnValue(v42);
          _objc_msgSend(v44, "setStrMacVersion:", v43);
        }
        v46 = &charsToLeaveEscaped;
        {
          v47 = 0;
        }
        else
        {
          v48 = _objc_msgSend(v63, "objectAtIndex:", 7LL);
          v46 = (__CFString *)objc_retainAutoreleasedReturnValue(v48);
          v47 = 1;
        }
        _objc_msgSend(v45, "setStrMacLoginVersion:", v46);
        if ( v47 )
        if ( v17 < 9 )
        {
          v51 = &charsToLeaveEscaped;
          v10 = v61;
          _objc_msgSend(v49, "setStryybFunc:", &charsToLeaveEscaped);
        }
        else
        {
          v10 = v61;
          v50 = _objc_msgSend(v63, "objectAtIndex:", 8LL);
          v51 = (__CFString *)objc_retainAutoreleasedReturnValue(v50);
          _objc_msgSend(v52, "setStryybFunc:", v51);
        }
          v54 = _objc_msgSend(v53, "strMacVersion");
        else
          v54 = _objc_msgSend(v63, "objectAtIndex:", 9LL);
        v55 = objc_retainAutoreleasedReturnValue(v54);
        _objc_msgSend(v56, "setStrMacKernel:", v55);
        v7 = v62;
        v15 = v63;
      }
    }
    _objc_msgSend(v13, "setStrBrokerName:", v7);
    _objc_msgSend(v57, "setStrConnectMode:", v10);
  }
  return v58;
}

//----- (0000000100C57A6F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strBrokerName](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 8LL, 0);
}

//----- (0000000100C57A80) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrBrokerName:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 8LL);
}

//----- (0000000100C57A8F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strYybid](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 16LL, 0);
}

//----- (0000000100C57AA0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrYybid:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 16LL);
}

//----- (0000000100C57AAF) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strQsid](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 24LL, 0);
}

//----- (0000000100C57AC0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrQsid:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (0000000100C57ACF) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strYybrid](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100C57AE0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrYybrid:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (0000000100C57AEF) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strVersion](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100C57B00) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrVersion:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100C57B0F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strAuthType](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 48LL, 0);
}

//----- (0000000100C57B20) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrAuthType:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 48LL);
}

//----- (0000000100C57B2F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strAccountType](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 56LL, 0);
}

//----- (0000000100C57B40) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrAccountType:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 56LL);
}

//----- (0000000100C57B4F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strConnectMode](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 64LL, 0);
}

//----- (0000000100C57B60) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrConnectMode:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 64LL);
}

//----- (0000000100C57B6F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strMacVersion](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 72LL, 0);
}

//----- (0000000100C57B80) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrMacVersion:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 72LL);
}

//----- (0000000100C57B8F) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strMacLoginVersion](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 80LL, 0);
}

//----- (0000000100C57BA0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrMacLoginVersion:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 80LL);
}

//----- (0000000100C57BAF) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel stryybFunc](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 88LL, 0);
}

//----- (0000000100C57BC0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStryybFunc:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (0000000100C57BCF) ----------------------------------------------------
NSString *__cdecl -[serverListDataModel strMacKernel](serverListDataModel *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 96LL, 0);
}

//----- (0000000100C57BE0) ----------------------------------------------------
void __cdecl -[serverListDataModel setStrMacKernel:](serverListDataModel *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (0000000100C57BEF) ----------------------------------------------------
void __cdecl -[serverListDataModel .cxx_destruct](serverListDataModel *self, SEL a2)
{
  objc_storeStrong((id *)&self->_strMacKernel, 0LL);
  objc_storeStrong((id *)&self->_stryybFunc, 0LL);
  objc_storeStrong((id *)&self->_strMacLoginVersion, 0LL);
  objc_storeStrong((id *)&self->_strMacVersion, 0LL);
  objc_storeStrong((id *)&self->_strConnectMode, 0LL);
  objc_storeStrong((id *)&self->_strAccountType, 0LL);
  objc_storeStrong((id *)&self->_strAuthType, 0LL);
  objc_storeStrong((id *)&self->_strVersion, 0LL);
  objc_storeStrong((id *)&self->_strYybrid, 0LL);
  objc_storeStrong((id *)&self->_strQsid, 0LL);
  objc_storeStrong((id *)&self->_strYybid, 0LL);
  objc_storeStrong((id *)&self->_strBrokerName, 0LL);
}

//----- (0000000100C57C86) ----------------------------------------------------
void __fastcall sub_100C57C86(__int64 a1)
{

  *(_OWORD *)a1 = 0LL;
  v2 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v3 = _objc_msgSend(v2, "init");
  v4 = *(void **)a1;
  *(_QWORD *)a1 = v3;
  v5 = *(void **)(a1 + 8);
  *(_QWORD *)(a1 + 8) = &charsToLeaveEscaped;
}

//----- (0000000100C57D02) ----------------------------------------------------
void __fastcall sub_100C57D02(__int64 a1)
{
  sub_100C57C86(a1);
}

//----- (0000000100C57D0C) ----------------------------------------------------
__int64 __fastcall sub_100C57D0C(id *a1, __int64 a2)
{

  v2 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", a2);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(*a1, "addObject:", v3);
  return 0LL;
}

//----- (0000000100C57D7A) ----------------------------------------------------
__int64 __fastcall sub_100C57D7A(__int64 a1, __int64 a2)
{

  v3 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", a2);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = *(void **)(a1 + 8);
  sub_100D448A0(v11, v6);
  v7 = _objc_msgSend(v5, "stringByAppendingFormat:", CFSTR("%@:%s|"), v4, v11[0]);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = *(void **)(a1 + 8);
  *(_QWORD *)(a1 + 8) = v8;
  sub_100C56008(v11);
  return 0LL;
}

