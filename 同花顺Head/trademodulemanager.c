trademodulemanager *__cdecl -[trademodulemanager init](trademodulemanager *self, SEL a2)
{
  trademodulemanager *v2; // rax
  trademodulemanager *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___trademodulemanager;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
  {
    -[trademodulemanager initializaton](v2, "initializaton");
    v3->_aModuleCount = 1LL;
  }
  return v3;
}

//----- (0000000100C4D58C) ----------------------------------------------------
id __cdecl +[trademodulemanager shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100C4D5EF;
  block[3] = (__int64)&unk_1012E3900;
  block[4] = (__int64)a1;
  if ( qword_1016D39E8 != -1 )
    dispatch_once(&qword_1016D39E8, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D39E0);
}

//----- (0000000100C4D5EF) ----------------------------------------------------
void __fastcall sub_100C4D5EF(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D39E0;
  qword_1016D39E0 = v2;
}

//----- (0000000100C4D621) ----------------------------------------------------
void __cdecl -[trademodulemanager initializaton](trademodulemanager *self, SEL a2)
{
  NSArray *v4; // rax
  NSArray *v5; // r15
  NSString *v8; // rax
  NSString *v9; // r15
  SEL v12; // r12

  v2 = +[configmanager shareInstance](&OBJC_CLASS___configmanager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, 1uLL, 1);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "firstObject");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@/"), v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v9);
  sub_100D2FF50(v10);
  v11 = (char *)sub_100D6D500();
  self->lpModuleName = v11;
  v13 = _objc_msgSend(&OBJC_CLASS___tools, v12, CFSTR("mactrade"));
  sub_100D300F0((unsigned __int64)v11, (__int64)v13, 0, (__int64 *)&self->hModuleComm);
}

//----- (0000000100C4D754) ----------------------------------------------------
void *__cdecl -[trademodulemanager getPublicModule](trademodulemanager *self, SEL a2)
{
  return self->hModuleComm;
}

//----- (0000000100C4D75E) ----------------------------------------------------
void *__cdecl -[trademodulemanager getOneModule](trademodulemanager *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r14
  id (*v5)(id, SEL, ...); // r12

  v2 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("mactrade-%ld"), self->_aModuleCount);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  lpModuleName = self->lpModuleName;
  v6 = v5(&OBJC_CLASS___tools, "NSString2CString:", v3);
  sub_100D300F0((unsigned __int64)lpModuleName, (__int64)v6, 0, (__int64 *)&v9);
  ++self->_aModuleCount;
  v7 = v9;
  return v7;
}

//----- (0000000100C4D7FE) ----------------------------------------------------
void __cdecl -[trademodulemanager ReleaseOneModule:](trademodulemanager *self, SEL a2, void *a3)
{
  if ( a3 )
    sub_100D30130((int)a3);
}

//----- (0000000100C4D820) ----------------------------------------------------
__int64 sub_100C4D820()
{
  hostent *v1; // rax
  __int16 v15; // [rsp+18Ah] [rbp-26h]

  v13 = 0LL;
  v12 = 0LL;
  v11 = 0LL;
  v10 = 0LL;
  v9 = 0LL;
  v8 = 0LL;
  v7 = 0LL;
  *(_OWORD *)v6 = 0LL;
  if ( socket(2, 1, 0) < 0 )
  {
    v4 = "create socket failed!";
LABEL_8:
    perror(v4);
    LODWORD(v0) = 0;
    return (unsigned int)v0;
  }
  bzero(v14, 0x10uLL);
  v14[1] = 2;
  v15 = 20480;
  v0 = v5;
  memset(v5, 0, 255);
  gethostname((char *)v5, 0xFFuLL);
  v1 = gethostbyname((const char *)v5);
  if ( !v1 )
  {
    v4 = "gethostbyname error!";
    goto LABEL_8;
  }
  v2 = *v1->h_addr_list;
  LOBYTE(v0) = 1;
  if ( v2 )
  {
    inet_ntop(v1->h_addrtype, v2, v6, 0x80u);
    printf("addr:%s\n", v6);
  }
  return (unsigned int)v0;
}

