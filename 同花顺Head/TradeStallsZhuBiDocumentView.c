TradeStallsZhuBiDocumentView *__cdecl -[TradeStallsZhuBiDocumentView initWithCoder:](
        TradeStallsZhuBiDocumentView *self,
        SEL a2,
        id a3)
{
  TradeStallsZhuBiDocumentView *result; // rax

  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___TradeStallsZhuBiDocumentView;
  result = objc_msgSendSuper2(&v4, "initWithCoder:", a3);
  if ( result )
  {
    result->super.super._nextResponder = (id)0x404B800000000000LL;
    result->super._frame.origin.x = 23.0;
    result->super._frame.origin.y = 25.0;
    result->super._frame.size.width = 2.0;
    *(_QWORD *)&result->super._frame.size.height = 5LL;
  }
  return result;
}

//----- (0000000100B78F60) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView reloadWithOrdersque:isSell:](
        TradeStallsZhuBiDocumentView *self,
        SEL a2,
        id a3,
        char a4)
{
  __m128i si128; // xmm1
  __m128d v8; // xmm0
  __m128d v9; // xmm3
  unsigned __int64 v11; // rax
  __m128d v12; // xmm0
  __m128d v13; // xmm0
  long double v28; // [rsp+0h] [rbp-D0h]
  __m128d v34; // [rsp+70h] [rbp-60h]
  int v37; // [rsp+A4h] [rbp-2Ch]

  v37 = a4;
  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 && _objc_msgSend(v4, "count") )
  {
    v6 = _objc_msgSend(v5, "count");
    si128 = _mm_load_si128((const __m128i *)&xmmword_1010CE400);
    v8 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)(unsigned __int64)v6, si128), (__m128d)xmmword_1010CE410);
    v9 = _mm_sub_pd(
           (__m128d)_mm_unpacklo_epi32(_mm_loadl_epi64((const __m128i *)&self->super._frame.size.height), si128),
           (__m128d)xmmword_1010CE410);
    v10 = _mm_hadd_pd(v8, v8).f64[0] / _mm_hadd_pd(v9, v9).f64[0];
    ceil(v28);
    v11 = (unsigned int)(int)(v10 - 9.223372036854776e18) ^ 0x8000000000000000LL;
    if ( v10 < 9.223372036854776e18 )
      v11 = (unsigned int)(int)v10;
    *(_QWORD *)&self->super._bounds.origin.y = v11;
    if ( v11 >= 5 )
    {
      v12 = _mm_sub_pd(
              (__m128d)_mm_unpacklo_epi32((__m128i)v11, (__m128i)xmmword_1010CE400),
              (__m128d)xmmword_1010CE410);
      v13 = _mm_hadd_pd(v12, v12);
      v13.f64[0] = v13.f64[0] * self->super._frame.origin.y;
      v34 = v13;
      v14 = _objc_msgSend(self, "window");
      v15 = (const char *)objc_retainAutoreleasedReturnValue(v14);
      v16 = (char *)v15;
      if ( v15 )
      {
        objc_msgSend_stret(&v35, v15, "frame");
      }
      else
      {
        v36 = 0LL;
        v35 = 0LL;
      }
      v33 = fmin(200.0, v34.f64[0]);
      *((double *)&v36 + 1) = v33 + 22.0;
      v21 = _objc_msgSend(self, "window");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      _objc_msgSend(v22, "setFrame:display:", 0LL);
      objc_msgSend_stret(&v31, (SEL)self, "frame");
      v32 = v34.f64[0];
      _objc_msgSend(self, "setFrame:");
      goto LABEL_18;
    }
    v17 = _objc_msgSend(self, "window");
    v18 = (const char *)objc_retainAutoreleasedReturnValue(v17);
    v19 = (char *)v18;
    if ( v18 )
    {
      objc_msgSend_stret(&v29, v18);
      v34.f64[0] = *((double *)&v30 + 1);
      if ( v34.f64[0] == 122.0 )
      {
LABEL_18:
        objc_storeStrong((id *)&self->super._bounds.size.height, v20);
        LOBYTE(self->super._bounds.origin.x) = v37;
        _objc_msgSend(self, "setNeedsDisplay:", 1LL);
        goto LABEL_19;
      }
    }
    else
    {
      v30 = 0LL;
      v29 = 0LL;
    }
    v23 = _objc_msgSend(self, "window");
    v24 = (const char *)objc_retainAutoreleasedReturnValue(v23);
    v25 = (char *)v24;
    if ( v24 )
    {
      objc_msgSend_stret(&v35, v24, "frame");
    }
    else
    {
      v36 = 0LL;
      v35 = 0LL;
    }
    *((_QWORD *)&v36 + 1) = 0x405E800000000000LL;
    v26 = _objc_msgSend(self, "window");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    _objc_msgSend(v27, "setFrame:display:", 0LL);
    objc_msgSend_stret(&v31, (SEL)self, "frame");
    v32 = self->super._frame.origin.y * 4.0;
    _objc_msgSend(self, "setFrame:");
    goto LABEL_18;
  }
LABEL_19:
}

//----- (0000000100B7937B) ----------------------------------------------------
char __cdecl -[TradeStallsZhuBiDocumentView isFlipped](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  return 1;
}

//----- (0000000100B79386) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView drawRect:](TradeStallsZhuBiDocumentView *self, SEL a2, CGRect a3)
{
  CGFloat height; // rdi
  SEL *v5; // rcx
  CGContext *v14; // rbx
  unsigned __int64 i; // r15
  CGContext *v17; // r14
  unsigned __int64 v21; // rdx
  __m128i v22; // xmm1
  __m128d v23; // xmm2
  __m128d v24; // xmm0
  int v27; // ebx
  NSString *v29; // rax
  float v30; // xmm0_4
  float v31; // xmm0_4
  NSDictionary *v33; // rax
  NSDictionary *v34; // r15
  CGRect rect; // [rsp+0h] [rbp-1E0h]
  NSRect recta; // [rsp+0h] [rbp-1E0h]
  CGSize v38; // [rsp+50h] [rbp-190h]
  CGFloat x; // [rsp+60h] [rbp-180h]
  __m128d v40; // [rsp+100h] [rbp-E0h]
  CGPoint v41; // [rsp+130h] [rbp-B0h]
  unsigned __int64 v42; // [rsp+130h] [rbp-B0h]
  CGFloat y; // [rsp+178h] [rbp-68h]
  NSString *ya; // [rsp+178h] [rbp-68h]

  v3 = 72LL;
  height = self->super._bounds.size.height;
  if ( height != 0.0 && _objc_msgSend(*(id *)&height, "count") )
  {
    v5 = &selRef_fallTextColor;
    if ( !LOBYTE(self->super._bounds.origin.x) )
      v5 = &selRef_riseTextColor;
    v6 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v5);
    v44 = objc_retainAutoreleasedReturnValue(v6);
    if ( LOBYTE(self->super._bounds.origin.x) )
      v7 = +[HXThemeManager alphaFallBgColor](&OBJC_CLASS___HXThemeManager, "alphaFallBgColor");
    else
      v7 = +[HXThemeManager alphaRiseBgColor](&OBJC_CLASS___HXThemeManager, "alphaRiseBgColor");
    v46 = objc_retainAutoreleasedReturnValue(v7);
    v8 = _objc_msgSend(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "mutableCopy");
    v45 = v10;
    _objc_msgSend(v10, "setAlignment:", 1LL);
    _objc_msgSend(v10, "setMinimumLineHeight:", 20.0);
    _objc_msgSend(v10, "setMaximumLineHeight:", 20.0);
    v11 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "currentContext");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = objc_retainAutorelease(v12);
    v14 = (CGContext *)_objc_msgSend(v13, "graphicsPort");
    width = self->super._frame.size.width;
    CGContextSetLineWidth(v14, width);
    for ( i = 0LL; i < (unsigned __int64)_objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v3), "count"); ++i )
    {
      v17 = v14;
      v18 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v3), "objectAtIndexedSubscript:", i);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      if ( (unsigned __int8)_objc_msgSend(v19, "isKindOfClass:", v20) )
      {
        _objc_msgSend(v19, "doubleValue");
        if ( width != 4294967295.0 )
        {
          _objc_msgSend(v19, "doubleValue");
          if ( width != 2147483648.0 )
          {
            *(_QWORD *)&v23.f64[0] = self->super.super._nextResponder;
            v21 = i % *(_QWORD *)&self->super._frame.size.height;
            v22 = _mm_unpacklo_epi64((__m128i)v21, (__m128i)(i / *(_QWORD *)&self->super._frame.size.height));
            v23.f64[1] = self->super._frame.origin.y;
            v41 = (CGPoint)_mm_mul_pd(
                             v23,
                             _mm_add_pd(
                               _mm_sub_pd(
                                 (__m128d)_mm_or_si128(_mm_srli_epi64(v22, 0x20u), (__m128i)xmmword_1010CE470),
                                 (__m128d)xmmword_1010CE480),
                               (__m128d)_mm_or_si128(
                                          _mm_and_si128(v22, (__m128i)xmmword_1010CE450),
                                          (__m128i)xmmword_1010CE460)));
            v40 = (__m128d)*(unsigned __int64 *)&self->super._frame.origin.x;
            if ( !v21 )
            {
              v38.width = v23.f64[0];
              v38.height = self->super._frame.origin.x;
              rect.size = v38;
              rect.origin = v41;
              y = CGRectGetMaxY(rect) + self->super._frame.size.width * 0.5;
              CGContextMoveToPoint(v14, 0.0, y);
              v24 = _mm_sub_pd(
                      (__m128d)_mm_unpacklo_ps(
                                 (__m128)*(unsigned __int64 *)&self->super._frame.size.height,
                                 (__m128)xmmword_1010CE400),
                      (__m128d)xmmword_1010CE410);
              CGContextAddLineToPoint(
                v14,
                _mm_hadd_pd(v24, v24).f64[0] * *(double *)&self->super.super._nextResponder,
                y);
              v25 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
              v26 = objc_retainAutoreleasedReturnValue(v25);
              _objc_msgSend(v26, "setStroke");
              CGContextStrokePath(v17);
            }
            v27 = (unsigned int)_objc_msgSend(v19, "integerValue");
            if ( (v27 & 0x8000000) != 0 )
            {
              _objc_msgSend(v46, "setFill");
              recta.size = (CGSize)_mm_add_pd(
                                     _mm_unpacklo_pd((__m128d)*(unsigned __int64 *)&v23.f64[0], v40),
                                     (__m128d)xmmword_1010CE420);
              recta.origin = (CGPoint)_mm_add_pd((__m128d)v41, (__m128d)xmmword_1010CF0B0);
              NSRectFill(recta);
              v27 &= 0xFFFFFFu;
            }
            v28 = self->super._bounds.size.width;
            if ( v28 <= 0.0 )
            {
              v30 = (double)v27 / 100.0;
              v31 = roundf(v30);
              v29 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"), v31);
            }
            else
            {
              v29 = +[HXTools tenThousandWithNumber:](
                      &OBJC_CLASS___HXTools,
                      "tenThousandWithNumber:",
                      v28 * (double)v27);
            }
            ya = objc_retainAutoreleasedReturnValue(v29);
            x = v41.x;
            v50[0] = (__int64)NSFontAttributeName;
            v32 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 13.0);
            v42 = i;
            v43 = objc_retainAutoreleasedReturnValue(v32);
            v51[0] = (__int64)v43;
            v50[1] = (__int64)NSForegroundColorAttributeName;
            v51[1] = (__int64)v44;
            v50[2] = (__int64)NSParagraphStyleAttributeName;
            v51[2] = (__int64)v45;
            v33 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v51, v50, 3LL);
            width = x;
            v34 = objc_retainAutoreleasedReturnValue(v33);
            _objc_msgSend(ya, "drawInRect:withAttributes:", v34);
            i = v42;
          }
        }
      }
      v3 = v35;
      v14 = v17;
    }
  }
}

//----- (0000000100B79B96) ----------------------------------------------------
unsigned __int64 __cdecl -[TradeStallsZhuBiDocumentView rowHCount](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  return *(_QWORD *)&self->super._bounds.origin.y;
}

//----- (0000000100B79BA7) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView setRowHCount:](
        TradeStallsZhuBiDocumentView *self,
        SEL a2,
        unsigned __int64 a3)
{
  *(_QWORD *)&self->super._bounds.origin.y = a3;
}

//----- (0000000100B79BB8) ----------------------------------------------------
double __cdecl -[TradeStallsZhuBiDocumentView price](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  return self->super._bounds.size.width;
}

//----- (0000000100B79BCA) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView setPrice:](TradeStallsZhuBiDocumentView *self, SEL a2, double a3)
{
  self->super._bounds.size.width = a3;
}

//----- (0000000100B79BDC) ----------------------------------------------------
NSArray *__cdecl -[TradeStallsZhuBiDocumentView ordersque](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  return *(NSArray **)&self->super._bounds.size.height;
}

//----- (0000000100B79BED) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView setOrdersque:](TradeStallsZhuBiDocumentView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._bounds.size.height, a3);
}

//----- (0000000100B79C01) ----------------------------------------------------
char __cdecl -[TradeStallsZhuBiDocumentView isSell](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  return LOBYTE(self->super._bounds.origin.x);
}

//----- (0000000100B79C12) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView setIsSell:](TradeStallsZhuBiDocumentView *self, SEL a2, char a3)
{
  LOBYTE(self->super._bounds.origin.x) = a3;
}

//----- (0000000100B79C22) ----------------------------------------------------
void __cdecl -[TradeStallsZhuBiDocumentView .cxx_destruct](TradeStallsZhuBiDocumentView *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._bounds.size.height, 0LL);
}

//----- (0000000100B79C38) ----------------------------------------------------
id __cdecl +[HXTools getCodesAndMarketArray:](id a1, SEL a2, id a3)
{
  SEL v15; // rbx
  SEL v35; // [rsp+60h] [rbp-130h]
  SEL v36; // [rsp+68h] [rbp-128h]
  SEL v38; // [rsp+78h] [rbp-118h]
  SEL v39; // [rsp+80h] [rbp-110h]
  SEL v41; // [rsp+90h] [rbp-100h]
  SEL v42; // [rsp+98h] [rbp-F8h]
  SEL v43; // [rsp+A0h] [rbp-F0h]
  SEL v44; // [rsp+A8h] [rbp-E8h]
  SEL v48; // [rsp+C8h] [rbp-C8h]
  id obj; // [rsp+D0h] [rbp-C0h]

  v34 = a1;
  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v50 = objc_retainAutoreleasedReturnValue(v5);
    v29 = 0LL;
    v30 = 0LL;
    v31 = 0LL;
    v32 = 0LL;
    v45 = v4;
    obj = objc_retain(v4);
    v6 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v51, 16LL);
    if ( v6 )
    {
      v7 = v6;
      v37 = *(_QWORD *)v30;
LABEL_5:
      v35 = "class";
      v36 = "isKindOfClass:";
      v38 = "numberWithInt:";
      v39 = "thsStringForKey:";
      v48 = "length";
      v41 = "getCodeString:";
      v42 = "getMarketString:";
      v43 = "dictionaryWithObjectsAndKeys:";
      v44 = "addObject:";
      v40 = v7;
      v8 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v30 != v37 )
          objc_enumerationMutation(obj);
        v9 = *(void **)(*((_QWORD *)&v29 + 1) + 8 * v8);
        v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v35);
        if ( !(unsigned __int8)_objc_msgSend(v9, v36, v10) )
          break;
        objc_retain(v9);
        v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, v38, 5LL);
        v12 = objc_retainAutoreleasedReturnValue(v11);
        v47 = v13;
        v14 = _objc_msgSend(v13, v39, v12);
        objc_retainAutoreleasedReturnValue(v14);
        v15 = v48;
        if ( !_objc_msgSend(v16, v48) )
        {
          break;
        }
        v33 = v8;
        v18 = v34;
        v19 = _objc_msgSend(v34, v41, v17);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v22 = _objc_msgSend(v18, v42, v21);
        v23 = v15;
        v24 = objc_retainAutoreleasedReturnValue(v22);
        v46 = v20;
        if ( _objc_msgSend(v20, v23) && _objc_msgSend(v24, v48) )
        {
          v25 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v43, v46, CFSTR("StockCode"), v24, CFSTR("Market"), 0LL);
          v26 = objc_retainAutoreleasedReturnValue(v25);
          _objc_msgSend(v50, v44, v26);
        }
        v8 = v33 + 1;
        if ( v40 == (id)(v33 + 1) )
        {
          v7 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v51, 16LL);
          if ( v7 )
            goto LABEL_5;
          break;
        }
      }
    }
    v4 = v45;
  }
  else
  {
    v50 = 0LL;
  }
  return objc_autoreleaseReturnValue(v50);
}

//----- (0000000100B7A022) ----------------------------------------------------
id __cdecl +[HXTools getCodeListStrFromArr:](id a1, SEL a2, id a3)
{
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  __CFString *v22; // rcx
  unsigned __int64 v23; // r12
  __CFString *v33; // r14
  __CFString *v36; // r15
  SEL v42; // [rsp+40h] [rbp-110h]
  SEL v43; // [rsp+48h] [rbp-108h]
  SEL v46; // [rsp+60h] [rbp-F0h]
  SEL v47; // [rsp+68h] [rbp-E8h]
  SEL v48; // [rsp+70h] [rbp-E0h]
  SEL v50; // [rsp+80h] [rbp-D0h]
  SEL v53; // [rsp+98h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v52 = objc_retainAutoreleasedReturnValue(v4);
  v44 = v3;
  if ( _objc_msgSend(v3, "count") )
  {
    v46 = "thsStringForKey:";
    v50 = "length";
    v47 = "thsArrayForKey:";
    v48 = "arrayWithArray:";
    v53 = "containsObject:";
    v43 = "addObject:";
    v42 = "setObject:forKey:";
    v5 = 0LL;
    do
    {
      v49 = v5;
      v6 = _objc_msgSend(v3, "thsDictionaryAtIndex:", v5);
      v7 = v3;
      v8 = objc_retainAutoreleasedReturnValue(v6);
      v10 = v9(v8, v46, CFSTR("StockCode"));
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v45 = v8;
      v12 = v8;
      v3 = v7;
      v14 = v13(v12, v46, CFSTR("Market"));
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v51 = v11;
      if ( v16(v11, v50) && _objc_msgSend(v15, v50) )
      {
        v17 = _objc_msgSend(v52, v47, v15);
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v19 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, v48, v18);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        if ( !(unsigned __int8)_objc_msgSend(v20, v53, v51) )
          _objc_msgSend(v20, v43, v51);
        if ( _objc_msgSend(v20, "count") )
          _objc_msgSend(v52, v42, v20, v15);
        v3 = v44;
      }
      v5 = v49 + 1;
    }
    while ( _objc_msgSend(v3, "count") > v49 + 1 );
  }
  v41 = 0LL;
  v40 = 0LL;
  v39 = 0LL;
  v38 = 0LL;
  v21 = _objc_msgSend(v52, "allKeys");
  v53 = (SEL)objc_retainAutoreleasedReturnValue(v21);
  v49 = (char *)_objc_msgSend((id)v53, "countByEnumeratingWithState:objects:count:", &v38, v54, 16LL);
  if ( v49 )
  {
    v48 = *(SEL *)v39;
    v22 = &charsToLeaveEscaped;
    do
    {
      v50 = "thsArrayForKey:";
      v46 = "componentsJoinedByString:";
      v47 = "stringWithFormat:";
      v23 = 0LL;
      do
      {
        v51 = v22;
        if ( *(SEL *)v39 != v48 )
          objc_enumerationMutation((id)v53);
        v24 = *(_QWORD *)(*((_QWORD *)&v38 + 1) + 8 * v23);
        v25 = _objc_msgSend(v52, v50, v24);
        v45 = objc_retainAutoreleasedReturnValue(v25);
        v26 = _objc_msgSend(v45, v46, CFSTR(","));
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = v47;
        v29 = _objc_msgSend(&OBJC_CLASS___NSString, v47, CFSTR("%@(%@,);"), v24, v27);
        v30 = objc_retainAutoreleasedReturnValue(v29);
        v31 = v51;
        v32 = _objc_msgSend(&OBJC_CLASS___NSString, v28, CFSTR("%@%@"), v51, v30);
        v33 = (__CFString *)objc_retainAutoreleasedReturnValue(v32);
        v23 = v34 + 1;
        v22 = v33;
      }
      while ( v23 < (unsigned __int64)v49 );
      v35 = (char *)_objc_msgSend((id)v53, "countByEnumeratingWithState:objects:count:", &v38, v54, 16LL);
      v22 = v33;
      v49 = v35;
    }
    while ( v35 );
    v36 = v33;
    v3 = v44;
  }
  else
  {
    v36 = &charsToLeaveEscaped;
  }
  return objc_autoreleaseReturnValue(v36);
}

//----- (0000000100B7A50B) ----------------------------------------------------
id __cdecl +[HXTools getCodeListWithoutMarketStrFromArr:](id a1, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  unsigned __int64 v7; // r14
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v21 = objc_retainAutoreleasedReturnValue(v4);
  if ( v5(v3, "count") )
  {
    v7 = 0LL;
    v20 = v3;
    do
    {
      v8 = v6(v3, "thsDictionaryAtIndex:", v7);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v11 = v10(v9, "thsStringForKey:", CFSTR("StockCode"));
      v12 = objc_retainAutoreleasedReturnValue(v11);
      if ( v13(v12, "length") )
        v14(v21, "addObject:", v12);
      ++v7;
      v3 = v20;
    }
    while ( (unsigned __int64)v15(v20, "count") > v7 );
  }
  v16 = _objc_msgSend(v21, "componentsJoinedByString:", CFSTR(","));
  v17 = objc_retainAutoreleasedReturnValue(v16);
  return objc_autoreleaseReturnValue(v17);
}

//----- (0000000100B7A657) ----------------------------------------------------
id __cdecl +[HXTools getCodesArray:](id a1, SEL a2, id a3)
{
  id (**v5)(id, SEL, ...); // r15
  unsigned __int64 v7; // r12
  id (**v12)(id, SEL, ...); // r13
  unsigned __int64 v18; // rax

  v3 = objc_retain(a3);
  v4 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v5 = &_objc_msgSend;
  v6 = _objc_msgSend(v4, "init");
  if ( _objc_msgSend(v3, "count") )
  {
    v7 = 0LL;
    v22 = v3;
    v23 = v6;
    do
    {
      v8 = ((id (*)(id, SEL, ...))v5)(v3, "objectAtIndexedSubscript:", v7);
      v20 = objc_retainAutoreleasedReturnValue(v8);
      v9 = ((id (*)(id, SEL, ...))v5)(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = ((id (*)(id, SEL, ...))v5)(v20, "objectForKey:", v10);
      v12 = v5;
      v13 = objc_retainAutoreleasedReturnValue(v11);
      v14 = (void *)((__int64 (__fastcall *)(id, const char *, id))v12)(a1, "getCodeString:", v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = v13;
      v5 = v12;
      v6 = v23;
      ((void (__fastcall *)(id, const char *, id))v5)(v23, "addObject:", v15);
      v17 = v15;
      v3 = v22;
      v18 = ((__int64 (__fastcall *)(id, const char *))v5)(v22, "count");
    }
    while ( v18 > v7 );
  }
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100B7A7D0) ----------------------------------------------------
id __cdecl +[HXTools getMarketString:](id a1, SEL a2, id a3)
{
  __CFString *v11; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) && _objc_msgSend(v5, "length") )
  {
    v6 = _objc_msgSend(v5, "componentsSeparatedByString:", &stru_10131D2F8);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "objectAtIndexedSubscript:", 0LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( (unsigned __int64)_objc_msgSend(v9, "length") < 5 )
    {
      v11 = &charsToLeaveEscaped;
    }
    else
    {
      v10 = _objc_msgSend(v9, "substringToIndex:", 4LL);
      v11 = (__CFString *)objc_retainAutoreleasedReturnValue(v10);
    }
  }
  else
  {
    v11 = 0LL;
  }
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100B7A8E1) ----------------------------------------------------
id __cdecl +[HXTools getCodeString:](id a1, SEL a2, id a3)
{
  __CFString *v11; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) && _objc_msgSend(v5, "length") )
  {
    v6 = _objc_msgSend(v5, "componentsSeparatedByString:", &stru_10131D2F8);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "objectAtIndexedSubscript:", 0LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( (unsigned __int64)_objc_msgSend(v9, "length") < 5 )
    {
      v11 = &charsToLeaveEscaped;
    }
    else
    {
      v10 = _objc_msgSend(v9, "substringFromIndex:", 4LL);
      v11 = (__CFString *)objc_retainAutoreleasedReturnValue(v10);
    }
  }
  else
  {
    v11 = 0LL;
  }
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100B7A9F2) ----------------------------------------------------
id __cdecl +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](id a1, SEL a2, id a3, id a4)
{
  __CFString *v8; // r13

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v8 = &charsToLeaveEscaped;
  if ( _objc_msgSend(v5, "length") && _objc_msgSend(v6, v7) )
  {
    v9 = _objc_msgSend(v5, "stringByAppendingString:", v6);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v8 = (__CFString *)objc_retain(v10);
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B7AA9F) ----------------------------------------------------
id __cdecl +[HXTools loadStringForCell:](id a1, SEL a2, id a3)
{

  v4 = (char *)objc_retain(a3);
  _objc_msgSend(v4, "doubleValue");
  if ( v3 == 4294967295.0 || (_objc_msgSend(v4, "doubleValue"), v3 == 2147483648.0) )
  {
    v4 = obj;
  }
  v5 = obj;
  if ( v4 )
    v5 = v4;
  v6 = objc_retainAutoreleaseReturnValue(v5);
  return v6;
}

//----- (0000000100B7AB20) ----------------------------------------------------
id __cdecl +[HXTools tenThousandWithNumber:](id a1, SEL a2, double a3)
{
  NSString *v4; // rax
  NSString *v5; // rdi

  if ( a3 == 0.0 )
    return objc_autoreleaseReturnValue(CFSTR("0万"));
  if ( a3 < 10000.0 )
    return objc_autoreleaseReturnValue(CFSTR("<1万"));
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f万"), a3 / 10000.0);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B7AB86) ----------------------------------------------------
id __cdecl +[HXTools withUnit:](id a1, SEL a2, id a3)
{
  __CFString *v14; // r15
  NSString *v15; // rax
  NSString *v16; // rbx
  NSString *v17; // r14
  SEL v18; // r12

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    v6 = _objc_msgSend(v4, "stringValue");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v4 = v7;
  }
  v8 = (char *)objc_retain(v4);
  v10 = v8;
  if ( !v8 )
  {
    v10 = obj;
  }
  v11 = _objc_msgSend(&OBJC_CLASS___NSString, v9);
  if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v11)
    && !(unsigned __int8)_objc_msgSend(v8, "isEqualTo:", obj) )
  {
    _objc_msgSend(v8, "doubleValue");
    if ( v3 >= 0.0 )
    {
      v12 = v8;
      v14 = &charsToLeaveEscaped;
      if ( (unsigned __int8)_objc_msgSend(v8, "hasPrefix:", CFSTR("+")) )
        v14 = CFSTR("+");
    }
    else
    {
      v12 = v8;
      v3 = -v3;
      v14 = CFSTR("-");
    }
    if ( v3 >= 1.0e12 )
    {
      v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f万亿"), v3 / 1.0e12);
    }
    else if ( v3 >= 100000000.0 )
    {
      v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f亿"), v3 / 100000000.0);
    }
    else if ( v3 >= 10000.0 )
    {
      v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f万"), v3 / 10000.0);
    }
    else
    {
      v15 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"), v3);
    }
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17 = v16;
    v19 = _objc_msgSend(&OBJC_CLASS___NSString, v18, CFSTR("%@%@"), v14, v16);
    v13 = objc_retainAutoreleasedReturnValue(v19);
  }
  else
  {
    v12 = v8;
    v13 = v10;
  }
  v20 = objc_retain(v13);
  return objc_autoreleaseReturnValue(v20);
}

//----- (0000000100B7ADFE) ----------------------------------------------------
id __cdecl +[HXTools withUnit:withFormat:](id a1, SEL a2, id a3, unsigned __int64 a4)
{
  NSString *v13; // rbx
  __CFString *v14; // rax
  bool v15; // zf
  unsigned __int64 v16; // r14
  __CFString *v17; // rax
  NSNumber *v18; // rax
  NSNumber *v19; // rax
  NSString *v23; // rax
  NSString *v24; // rbx
  __CFString *v27; // [rsp+10h] [rbp-40h]
  __CFString *v29; // [rsp+20h] [rbp-30h]

  v5 = objc_retain(a3);
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6) )
  {
    v7 = _objc_msgSend(v5, "stringValue");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v5 = v8;
  }
  v9 = (char *)objc_retain(v5);
  v11 = v9;
  if ( !v9 )
  {
    v9 = obj;
  }
  v26 = v9;
  v12 = _objc_msgSend(&OBJC_CLASS___NSString, v10);
  if ( (unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12)
    && !(unsigned __int8)_objc_msgSend(v11, "isEqualTo:", obj) )
  {
    _objc_msgSend(v11, "doubleValue");
    if ( v4 >= 0.0 )
    {
      v15 = (unsigned __int8)_objc_msgSend(v11, "hasPrefix:", CFSTR("+")) == 0;
      v14 = &charsToLeaveEscaped;
      if ( !v15 )
        v14 = CFSTR("+");
    }
    else
    {
      v4 = -v4;
      v14 = CFSTR("-");
    }
    v16 = a4;
    v29 = v14;
    if ( v4 >= 1.0e12 )
    {
      v4 = v4 / 1.0e12;
      v17 = CFSTR("万亿");
    }
    else if ( v4 >= 100000000.0 )
    {
      v4 = v4 / 100000000.0;
      v17 = CFSTR("亿");
    }
    else if ( v4 >= 10000.0 )
    {
      v4 = v4 / 10000.0;
      v17 = CFSTR("万");
    }
    else
    {
      v17 = &charsToLeaveEscaped;
    }
    v27 = v17;
    v18 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v4);
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = +[HXTools stringNumber:withFormat:](&OBJC_CLASS___HXTools, "stringNumber:withFormat:", v19, v16);
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v23 = (NSString *)_objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@%@"), v29, v21, v27);
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v13 = objc_retain(v24);
  }
  else
  {
    v13 = objc_retain(v26);
  }
  return objc_autoreleaseReturnValue(v13);
}

//----- (0000000100B7B06E) ----------------------------------------------------
id __cdecl +[HXTools withUnitNumber:](id a1, SEL a2, id a3)
{
  __CFString *v7; // r14
  NSString *v9; // rax
  NSString *v10; // rax

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    _objc_msgSend(v4, "doubleValue");
    v6 = v3;
    v7 = 0LL;
    if ( v3 < 0.0 )
      v7 = CFSTR("-");
    if ( v3 < 0.0 )
      v3 = -v3;
    if ( v3 >= 1.0e12 )
    {
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f万亿"), v3 / 1.0e12);
    }
    else if ( v3 >= 100000000.0 )
    {
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f亿"), v3 / 100000000.0);
    }
    else if ( v3 >= 10000.0 )
    {
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f万"), v3 / 10000.0);
    }
    else
    {
      v9 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"), v3);
    }
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v8 = (char *)v10;
    if ( v6 < 0.0 )
    {
      v11 = _objc_msgSend(v7, "stringByAppendingString:", v10);
      v12 = (char *)objc_retainAutoreleasedReturnValue(v11);
      v8 = v12;
    }
  }
  else
  {
    v8 = obj;
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B7B1F3) ----------------------------------------------------
id __cdecl +[HXTools withUnit:MaxValue:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rbx
  NSString *v11; // rax

  v5 = objc_retain(a3);
  v6 = objc_retain(v5);
  v8 = objc_retain(v7);
  _objc_msgSend(v6, "doubleValue");
  v9(v8, "doubleValue");
  if ( v4 >= 1.0e12 )
  {
    v12 = v4 / 1.0e12;
LABEL_9:
    v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f"), v12);
    goto LABEL_10;
  }
  if ( v4 >= 100000000.0 )
  {
    v12 = v4 / 100000000.0;
    goto LABEL_9;
  }
  if ( v4 >= 10000.0 )
  {
    v12 = v4 / 10000.0;
    goto LABEL_9;
  }
  v10 = (NSString *)v6;
  if ( v4 < 10000.0 )
  {
    v11 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"));
LABEL_10:
    v10 = objc_retainAutoreleasedReturnValue(v11);
  }
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100B7B313) ----------------------------------------------------
id __cdecl +[HXTools getUnit:](id a1, SEL a2, double a3)
{
  if ( a3 >= 1.0e12 )
    return CFSTR("万亿");
  if ( a3 >= 100000000.0 )
    return CFSTR("亿");
  if ( a3 >= 10000.0 )
    return CFSTR("万");
  return CFSTR("0");
}

//----- (0000000100B7B359) ----------------------------------------------------
signed __int64 __cdecl +[HXTools getMultiDelta:](id a1, SEL a2, double a3)
{
  signed __int64 result; // rax

  v3 = fabs(a3);
  result = 10000LL;
  if ( v3 < 1000000000.0 )
  {
    result = 1000LL;
    if ( v3 < 100000000.0 )
    {
      result = 100LL;
      if ( v3 < 10000000.0 )
      {
        result = 1LL;
        if ( v3 >= 1000000.0 )
          return 10LL;
      }
    }
  }
  return result;
}

//----- (0000000100B7B3A8) ----------------------------------------------------
id __cdecl +[HXTools getColorByJudgeNumberValue:](id a1, SEL a2, double a3)
{
  SEL *v6; // rcx
  SEL *v7; // rax

  if ( (unsigned __int8)_objc_msgSend(a1, "isAbnormalNumber:") )
  {
    v4 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v5 = objc_retainAutoreleasedReturnValue(v4);
  }
  else
  {
    v6 = &selRef_normalTextColor;
    if ( a3 < 0.0 )
      v6 = &selRef_fallTextColor;
    v7 = &selRef_riseTextColor;
    if ( a3 <= 0.0 )
      v7 = v6;
    v8 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v7, v3, v6);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v5 = objc_retain(v9);
  }
  return objc_autorelease(v5);
}

//----- (0000000100B7B44F) ----------------------------------------------------
id __cdecl +[HXTools getJudgedColorOfTargetValue:JudgedValue:](id a1, SEL a2, double a3, double a4)
{
  SEL *v8; // rcx
  SEL *v9; // rax

  if ( (unsigned __int8)_objc_msgSend(a1, "isAbnormalNumber:", a4)
    || (unsigned __int8)_objc_msgSend(a1, "isAbnormalNumber:", a3) )
  {
    v5 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v6 = objc_retainAutoreleasedReturnValue(v5);
  }
  else
  {
    v8 = &selRef_normalTextColor;
    if ( a3 > a4 )
      v8 = &selRef_fallTextColor;
    v9 = &selRef_riseTextColor;
    if ( a4 <= a3 )
      v9 = v8;
    v10 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v9, v4, v8);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v6 = objc_retain(v11);
  }
  return objc_autorelease(v6);
}

//----- (0000000100B7B522) ----------------------------------------------------
char __cdecl +[HXTools isAbnormalNumber:](id a1, SEL a2, double a3)
{
  return a3 == 4294967295.0 || a3 == 2147483648.0;
}

//----- (0000000100B7B54D) ----------------------------------------------------
char __cdecl +[HXTools isAbnormalNumberEx:](id a1, SEL a2, double a3)
{
  return a3 == 4294967295.0 || a3 == 2147483648.0 || a3 == 0.0;
}

//----- (0000000100B7B58A) ----------------------------------------------------
id __cdecl +[HXTools getTableDataTypes:](id a1, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  unsigned __int64 v12; // r15
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v34 = v3;
    v7 = v6(v3, "tableColumns");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(v8, "count");
    if ( v10 )
    {
      v32 = v5;
      v12 = 0LL;
      v33 = 0LL;
      do
      {
        v13 = v11(v3, "tableColumns");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v16 = v15(v14, "objectAtIndexedSubscript:", v12);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v19 = v18(v17, "identifier");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        LOBYTE(v14) = (unsigned __int8)v21(v20, "isEqualToString:", CFSTR("1"));
        v23 = v17;
        if ( !(_BYTE)v14 )
        {
          v24 = v22(v17, "identifier");
          v25 = objc_retainAutoreleasedReturnValue(v24);
          v26(v32, "addObject:", v25);
          v23 = 0LL;
        }
        v33 = v23;
        v27 = v22(v34, "tableColumns");
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v30 = v29(v28, "count");
        v3 = v34;
        ++v12;
      }
      while ( (unsigned __int64)v30 > v12 );
      v5 = v32;
    }
  }
  else
  {
    v5 = 0LL;
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B7B78F) ----------------------------------------------------
id __cdecl +[HXTools dataTypesToString:](id a1, SEL a2, id a3)
{
  unsigned __int64 v4; // r14
  __CFString *v23; // [rsp+18h] [rbp-38h]

  v24 = objc_retain(a3);
  if ( v24 && _objc_msgSend(v24, "count") )
  {
    if ( _objc_msgSend(v24, "count") )
    {
      v3 = "objectAtIndexedSubscript:";
      v4 = 0LL;
      v23 = &charsToLeaveEscaped;
      do
      {
        v5 = _objc_msgSend(v24, v3, v4);
        v6 = objc_retainAutoreleasedReturnValue(v5);
        if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToString:", CFSTR("1")) )
        {
        }
        else
        {
          v8 = _objc_msgSend(v24, v7, v4);
          v9 = objc_retainAutoreleasedReturnValue(v8);
          v10 = (unsigned __int8)_objc_msgSend(v9, "isEqualToString:", &charsToLeaveEscaped);
          v12(v6);
          if ( !v10 )
          {
            v13 = _objc_msgSend(v24, "objectAtIndexedSubscript:", v4);
            v14 = objc_retainAutoreleasedReturnValue(v13);
            v15 = _objc_msgSend(v23, "stringByAppendingString:", v14);
            v16 = objc_retainAutoreleasedReturnValue(v15);
            v18 = _objc_msgSend(v16, "stringByAppendingString:", CFSTR(","));
            v23 = (__CFString *)objc_retainAutoreleasedReturnValue(v18);
          }
        }
        ++v4;
      }
      while ( (unsigned __int64)_objc_msgSend(v24, "count") > v4 );
    }
    else
    {
      v23 = &charsToLeaveEscaped;
    }
    v20 = (char *)_objc_msgSend(v23, "length");
    v21 = _objc_msgSend(v23, "stringByReplacingCharactersInRange:withString:", v20 - 1, 1LL, &charsToLeaveEscaped);
    v19 = objc_retainAutoreleasedReturnValue(v21);
  }
  else
  {
    v19 = 0LL;
  }
  return objc_autoreleaseReturnValue(v19);
}

//----- (0000000100B7B9AB) ----------------------------------------------------
id __cdecl +[HXTools dataTypesToNumberArray:](id a1, SEL a2, id a3)
{
  NSNumber *v7; // r14
  unsigned int v15; // r14d
  NSNumber *v17; // rax

  v20 = objc_retain(a3);
  if ( !v20 || !_objc_msgSend(v20, "count") )
  {
    v4 = 0LL;
    goto LABEL_13;
  }
  v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( _objc_msgSend(v20, "count") )
  {
    v19 = v4;
    v5 = 0LL;
    while ( 1 )
    {
      v6 = _objc_msgSend(v20, "objectAtIndexedSubscript:", v5);
      v7 = (NSNumber *)objc_retainAutoreleasedReturnValue(v6);
      if ( !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("1")) )
      {
        v8 = _objc_msgSend(v20, "objectAtIndexedSubscript:", v5);
        v9 = objc_retainAutoreleasedReturnValue(v8);
        v10 = (unsigned __int8)_objc_msgSend(v9, "isEqualToString:", &charsToLeaveEscaped);
        v12(v7);
        if ( v10 )
          goto LABEL_8;
        v13 = _objc_msgSend(v20, "objectAtIndexedSubscript:", v5);
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = (unsigned int)_objc_msgSend(v14, "intValue");
        if ( !v15 )
          goto LABEL_8;
        v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", v15);
        v7 = objc_retainAutoreleasedReturnValue(v17);
        _objc_msgSend(v19, "addObject:", v7);
      }
LABEL_8:
      if ( (unsigned __int64)_objc_msgSend(v20, "count") <= ++v5 )
      {
        v4 = v19;
        break;
      }
    }
  }
LABEL_13:
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100B7BB95) ----------------------------------------------------
id __cdecl +[HXTools stringNumber:withFormat:](id a1, SEL a2, id a3, unsigned __int64 a4)
{
  NSString *v7; // rax
  __CFString *v8; // rbx

  v5 = objc_retain(a3);
  if ( v5
    && (v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6)) )
  {
    switch ( a4 )
    {
      case 0uLL:
        _objc_msgSend(v5, "doubleValue");
        v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f"));
        goto LABEL_11;
      case 1uLL:
        _objc_msgSend(v5, "doubleValue");
        v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.1f"));
        goto LABEL_11;
      case 2uLL:
        _objc_msgSend(v5, "doubleValue");
        v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f"));
        goto LABEL_11;
      case 3uLL:
        _objc_msgSend(v5, "doubleValue");
        v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.3f"));
        goto LABEL_11;
      case 4uLL:
        _objc_msgSend(v5, "doubleValue");
        v7 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.4f"));
LABEL_11:
        v8 = objc_retainAutoreleasedReturnValue(v7);
        break;
      default:
        v8 = &charsToLeaveEscaped;
        break;
    }
  }
  else
  {
    v8 = 0LL;
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B7BD00) ----------------------------------------------------
id __cdecl +[HXTools precisionToFit:](id a1, SEL a2, id a3)
{
  NSString *v5; // rax
  NSString *v6; // rax

  _objc_msgSend(a3, "doubleValue");
  v4 = fabs(v3);
  if ( v3 == 0.0 || v4 >= 0.001 )
  {
    if ( v3 == 0.0 || v4 >= 0.01 )
      v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f"));
    else
      v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.3f"));
  }
  else
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.4f"));
  }
  v6 = objc_retainAutoreleasedReturnValue(v5);
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100B7BD9E) ----------------------------------------------------
id __cdecl +[HXTools reducePrecision:forNumber:](id a1, SEL a2, int a3, id a4)
{
  NSNumber *v14; // rax
  NSNumber *v15; // rbx

  v5 = objc_retain(a4);
  v6 = objc_alloc(&OBJC_CLASS___NSNumberFormatter);
  v7 = _objc_msgSend(v6, "init");
  _objc_msgSend(v7, "setNumberStyle:", 1LL);
  _objc_msgSend(v8, "setMinimumFractionDigits:", a3);
  _objc_msgSend(v9, "setMaximumFractionDigits:", a3);
  _objc_msgSend(v10, "setHasThousandSeparators:", 0LL);
  v12 = _objc_msgSend(v11, "stringFromNumber:", v5);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "doubleValue");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  return objc_autoreleaseReturnValue(v15);
}

//----- (0000000100B7BE9E) ----------------------------------------------------
id __cdecl +[HXTools number:FormatedWithPrecision:NumberSignMode:United:](
        id a1,
        SEL a2,
        id a3,
        int a4,
        unsigned __int64 a5,
        char a6)
{
  NSString *v24; // rax
  NSString *v25; // rbx
  bool v26; // cc
  NSString *v27; // rax
  NSString *v28; // rbx
  unsigned __int64 v32; // [rsp+8h] [rbp-48h]

  v9 = objc_retain(a3);
  v10 = obj;
  if ( v9 )
  {
    v32 = a5;
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    if ( (unsigned __int8)_objc_msgSend(v12, "isKindOfClass:", v11) )
    {
      v13 = _objc_msgSend(v9, "stringValue");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      if ( (unsigned __int8)_objc_msgSend(v16, "isEqualToString:", obj) )
      {
        v10 = (char *)objc_retain(v16);
LABEL_20:
        goto LABEL_21;
      }
      v17 = objc_alloc(&OBJC_CLASS___NSNumberFormatter);
      v18 = _objc_msgSend(v17, "init");
      _objc_msgSend(v18, "setNumberStyle:", 1LL);
      _objc_msgSend(v18, "setMaximumFractionDigits:", a4);
      _objc_msgSend(v18, "setMinimumFractionDigits:", a4);
      _objc_msgSend(v18, "setHasThousandSeparators:", 0LL);
      v34 = v18;
      v20 = _objc_msgSend(v18, "stringFromNumber:", v19);
      v10 = (char *)objc_retainAutoreleasedReturnValue(v20);
      if ( v32 )
      {
        v22 = a1;
        if ( v32 == 3 )
        {
          _objc_msgSend(v21, "doubleValue");
          if ( v6 <= 0.0 )
            goto LABEL_17;
          v27 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("+%@"), v10);
LABEL_16:
          v28 = objc_retainAutoreleasedReturnValue(v27);
          v10 = (char *)v28;
          v22 = a1;
          goto LABEL_17;
        }
        if ( v32 != 1 )
        {
LABEL_17:
          if ( a6 )
          {
            v29 = _objc_msgSend(v22, "withUnit:", v10);
            v30 = (char *)objc_retainAutoreleasedReturnValue(v29);
            v10 = v30;
          }
          goto LABEL_20;
        }
        _objc_msgSend(v21, "doubleValue");
        if ( v6 > 0.0 )
        {
          v24 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("+%@"), v10);
          v25 = objc_retainAutoreleasedReturnValue(v24);
          v10 = (char *)v25;
          v22 = a1;
          goto LABEL_17;
        }
        _objc_msgSend(v23, "doubleValue");
        v26 = v6 >= 0.0;
      }
      else
      {
        _objc_msgSend(v21, "doubleValue");
        v26 = v6 >= 0.0;
        v22 = a1;
      }
      if ( v26 )
        goto LABEL_17;
      v27 = (NSString *)_objc_msgSend(
                          v10,
                          "stringByReplacingCharactersInRange:withString:",
                          0LL,
                          1LL,
                          &charsToLeaveEscaped);
      goto LABEL_16;
    }
  }
LABEL_21:
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100B7C19B) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools createRequestTypeWithKLineCycleType:](id a1, SEL a2, unsigned __int64 a3)
{
  if ( (__int64)a3 > 12527 )
  {
    if ( (__int64)a3 > 24576 )
    {
      switch ( a3 )
      {
        case 0x6001uLL:
          return 6LL;
        case 0x6003uLL:
          return 7LL;
        case 0x7001uLL:
          return 8LL;
      }
    }
    else
    {
      switch ( a3 )
      {
        case 0x30F0uLL:
          return 15LL;
        case 0x4000uLL:
          return 3LL;
        case 0x5001uLL:
          return 5LL;
      }
    }
  }
  else if ( (__int64)a3 > 12317 )
  {
    switch ( a3 )
    {
      case 0x301EuLL:
        return 12LL;
      case 0x303CuLL:
        return 13LL;
      case 0x3078uLL:
        return 14LL;
    }
  }
  else
  {
    switch ( a3 )
    {
      case 0x3001uLL:
        return 9LL;
      case 0x3005uLL:
        return 10LL;
      case 0x300FuLL:
        return 11LL;
    }
  }
  return 0LL;
}

//----- (0000000100B7C291) ----------------------------------------------------
void __cdecl +[HXTools getStockNameWithCode:market:resultBlock:](id a1, SEL a2, id a3, id a4, id a5)
{
  KLineAccessoryManager *v12; // rax
  KLineAccessoryManager *v13; // rbx
  NSNumber *v16; // rax
  NSNumber *v17; // rax
  NSDictionary *v19; // rax
  DataRequestCenter *v21; // rax
  NSArray *v22; // rax
  id (__cdecl *v24)(id); // r12
  id (__cdecl *v25)(id); // r12
  id (__cdecl *v26)(id); // r12
  _QWORD v29[4]; // [rsp+0h] [rbp-D0h] BYREF
  _QWORD v33[4]; // [rsp+38h] [rbp-98h] BYREF

  v6 = objc_retain(a3);
  v40 = objc_retain(v7);
  objc_retain(a5);
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  v41 = v6;
  if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v8) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    v11 = (unsigned __int8)_objc_msgSend(v40, "isKindOfClass:", v10);
    if ( v9 )
    {
      if ( v11 )
      {
        v39 = v9;
        v12 = +[KLineAccessoryManager sharedInstance](&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v14 = -[KLineAccessoryManager getaStockName:withCode:](v13, "getaStockName:withCode:", v40, v41);
        v15 = objc_retainAutoreleasedReturnValue(v14);
        if ( v15 && _objc_msgSend(v15, "length") )
        {
          (*((void (__fastcall **)(id, id))v39 + 2))(v39, v15);
        }
        else
        {
          v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 200LL);
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v35 = v15;
          v18 = v40;
          v19 = _objc_msgSend(
                  &OBJC_CLASS___NSDictionary,
                  "dictionaryWithObjectsAndKeys:",
                  v41,
                  CFSTR("CodeList"),
                  v40,
                  CFSTR("Market"),
                  CFSTR("5,55"),
                  CFSTR("DataTypes"),
                  v17,
                  CFSTR("DataRequest_Type"),
                  0LL);
          v36 = objc_retainAutoreleasedReturnValue(v19);
          v21 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
          v37 = objc_retainAutoreleasedReturnValue(v21);
          v42 = v36;
          v22 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v42, 1LL);
          v38 = objc_retainAutoreleasedReturnValue(v22);
          v29[0] = _NSConcreteStackBlock;
          v29[1] = 3254779904LL;
          v29[2] = sub_100B7C5CB;
          v29[3] = &unk_1012E5388;
          v23 = objc_retain(v39);
          v30 = v23;
          v31 = v24(v41);
          v32 = v25(v18);
          v33[0] = _NSConcreteStackBlock;
          v33[1] = 3254779904LL;
          v33[2] = sub_100B7C968;
          v33[3] = &unk_1012DACE0;
          v34 = v26(v23);
          v27 = v38;
          _objc_msgSend(v37, "request:type:params:callBack:fail:", 2LL, 1LL, v38, v29, v33);
          v15 = v35;
        }
        v9 = v39;
      }
    }
  }
}

//----- (0000000100B7C5CB) ----------------------------------------------------
void __fastcall sub_100B7C5CB(_QWORD *a1, void *a2)
{
  NSNumber *v11; // rax
  NSNumber *v12; // rbx
  NSNumber *v17; // rax
  NSNumber *v18; // r13
  KLineAccessoryManager *v31; // rax
  KLineAccessoryManager *v32; // rbx
  _QWORD *v33; // r13
  KLineAccessoryManager *v36; // rax
  KLineAccessoryManager *v37; // rax

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) && a1[4] )
  {
    v4 = objc_retain(v2);
    v5 = _objc_msgSend(v4, "arrBody");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "lastObject");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v9) )
    {
      v47 = v2;
      v49 = v10;
      v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = _objc_msgSend(v8, "thsStringForKey:", v12);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v14);
      v48 = objc_retainAutoreleasedReturnValue(v15);
      v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(v8, "thsStringForKey:", v18);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v22 = _objc_msgSend(v21, "getMarketString:", v20);
      objc_retainAutoreleasedReturnValue(v22);
      v23 = (unsigned __int8)_objc_msgSend(v48, "isEqualToString:", a1[5]);
      v44 = v24;
      if ( v23 && (unsigned __int8)_objc_msgSend(v24, "isEqualToString:", a1[6]) )
      {
        v25 = _objc_msgSend(v49, "arrBody");
        v26 = objc_retainAutoreleasedReturnValue(v25);
        v27 = _objc_msgSend(v26, "lastObject");
        v28 = v26;
        v29 = objc_retainAutoreleasedReturnValue(v27);
        v30 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
        if ( (unsigned __int8)_objc_msgSend(v29, "isKindOfClass:", v30) )
        {
          objc_retain(v29);
          v31 = +[KLineAccessoryManager sharedInstance](&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
          v45 = v29;
          v32 = objc_retainAutoreleasedReturnValue(v31);
          v33 = a1;
          -[KLineAccessoryManager saveKLineAccessoryInfoDict:withMarket:andCode:](
            v32,
            "saveKLineAccessoryInfoDict:withMarket:andCode:",
            v34,
            a1[6],
            a1[5]);
          v36 = +[KLineAccessoryManager sharedInstance](&OBJC_CLASS___KLineAccessoryManager, "sharedInstance");
          v37 = objc_retainAutoreleasedReturnValue(v36);
          v38 = -[KLineAccessoryManager getaStockName:withCode:](v37, "getaStockName:withCode:", v33[6], v33[5]);
          v39 = objc_retainAutoreleasedReturnValue(v38);
          (*(void (__fastcall **)(_QWORD, id))(v33[4] + 16LL))(v33[4], v39);
          v41 = v39;
          v29 = v45;
        }
        v2 = v47;
        v42 = v48;
      }
      else
      {
        (*(void (__fastcall **)(_QWORD, char *))(a1[4] + 16LL))(a1[4], obj);
        v2 = v47;
        v42 = v48;
      }
    }
  }
}

//----- (0000000100B7C968) ----------------------------------------------------
__int64 __fastcall sub_100B7C968(__int64 a1)
{

  v1 = *(_QWORD *)(a1 + 32);
  if ( v1 )
    return (*(__int64 (__fastcall **)(__int64, char *))(v1 + 16))(v1, obj);
  return result;
}

//----- (0000000100B7C987) ----------------------------------------------------
id __cdecl +[HXTools createDataTypeWithRequestType:market:](id a1, SEL a2, unsigned __int64 a3, id a4)
{
  __CFString *v8; // rbx

  v5 = objc_retain(a4);
  v6 = v5;
  switch ( a3 )
  {
    case 1uLL:
      v7 = +[HXTools dataTypeForRealLine:](&OBJC_CLASS___HXTools, "dataTypeForRealLine:", v5);
      goto LABEL_3;
    case 2uLL:
      v7 = +[HXTools dataTypeForDealDetail:](&OBJC_CLASS___HXTools, "dataTypeForDealDetail:", v5);
      goto LABEL_3;
    case 3uLL:
    case 5uLL:
    case 6uLL:
    case 9uLL:
    case 0xAuLL:
    case 0xBuLL:
    case 0xCuLL:
    case 0xDuLL:
    case 0xEuLL:
    case 0xFuLL:
      v7 = +[HXTools dataTypeForDayKLine:](&OBJC_CLASS___HXTools, "dataTypeForDayKLine:", v5);
      goto LABEL_3;
    case 4uLL:
      v7 = +[HXTools dataTypeForPriceVolume:](&OBJC_CLASS___HXTools, "dataTypeForPriceVolume:", v5);
      goto LABEL_3;
    case 7uLL:
    case 8uLL:
      v7 = +[HXTools dataTypeForQuarterKLine:](&OBJC_CLASS___HXTools, "dataTypeForQuarterKLine:", v5);
      goto LABEL_3;
    case 0x10uLL:
      v7 = +[HXTools dataTypeForFeedData:](&OBJC_CLASS___HXTools, "dataTypeForFeedData:", v5);
LABEL_3:
      v8 = (__CFString *)objc_retainAutoreleasedReturnValue(v7);
      break;
    default:
      v8 = &charsToLeaveEscaped;
      break;
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B7CA8C) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForRealLine:](id a1, SEL a2, id a3)
{
  NSArray *v5; // rax
  NSArray *v6; // rbx
  id result; // rax

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v4, 3LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  result = CFSTR("1,10,18");
  if ( !v7 )
    return CFSTR("1,10,13,19,40");
  return result;
}

//----- (0000000100B7CB6E) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForDealDetail:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  __CFString *v7; // rbx
  NSArray *v8; // rax
  NSArray *v9; // rbx
  NSArray *v11; // rax
  NSArray *v12; // rax
  NSArray *v14; // rax
  NSArray *v15; // rbx
  NSArray *v18; // rax
  NSArray *v19; // rbx
  _QWORD v22[3]; // [rsp+10h] [rbp-130h] BYREF
  _QWORD v23[6]; // [rsp+28h] [rbp-118h] BYREF
  _QWORD v24[11]; // [rsp+58h] [rbp-E8h] BYREF
  _QWORD v25[7]; // [rsp+B0h] [rbp-90h] BYREF
  _QWORD v26[5]; // [rsp+E8h] [rbp-58h] BYREF

  v3 = objc_retain(a3);
  v26[0] = CFSTR("UHKI");
  v26[1] = CFSTR("UHKM");
  v26[2] = CFSTR("UHKG");
  v26[3] = CFSTR("UHKB");
  v26[4] = CFSTR("UHKW");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 5LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "containsObject:", v3);
  if ( v6 )
  {
    v7 = CFSTR("1,5,10,12,18,49");
    goto LABEL_9;
  }
  v25[0] = CFSTR("UFII");
  v25[1] = CFSTR("UNQS");
  v25[2] = CFSTR("UNYN");
  v25[3] = CFSTR("UNQQ");
  v25[4] = CFSTR("UNYA");
  v25[5] = CFSTR("UUSD");
  v25[6] = CFSTR("UUSA");
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v25, 7LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "containsObject:", v3);
  if ( v10 )
  {
    v7 = CFSTR("1,5,10,12,49");
    goto LABEL_9;
  }
  v24[0] = CFSTR("UCFS");
  v24[1] = CFSTR("UCFD");
  v24[2] = CFSTR("UCFZ");
  v24[3] = CFSTR("UCMN");
  v24[4] = CFSTR("UIFF");
  v24[5] = CFSTR("UCMS");
  v24[6] = CFSTR("UFIS");
  v24[7] = CFSTR("UHFF");
  v24[8] = CFSTR("UHFS");
  v24[9] = CFSTR("UHFR");
  v24[10] = CFSTR("UHFZ");
  v11 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v24, 11LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  if ( (unsigned __int8)_objc_msgSend(v12, "containsObject:", v3) )
  {
LABEL_8:
    v7 = CFSTR("1,5,10,12,49,71");
    goto LABEL_9;
  }
  v23[0] = CFSTR("USOO");
  v23[1] = CFSTR("UZOO");
  v23[2] = CFSTR("UCFT");
  v23[3] = CFSTR("UCFL");
  v23[4] = CFSTR("UCFX");
  v23[5] = CFSTR("UIFB");
  v14 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v23, 6LL);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v21 = (unsigned __int8)_objc_msgSend(v15, "containsObject:", v3);
  if ( v21 )
    goto LABEL_8;
  v22[0] = CFSTR("UFXB");
  v22[1] = CFSTR("UFXC");
  v22[2] = CFSTR("UFXR");
  v18 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v22, 3LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = (unsigned __int8)_objc_msgSend(v19, "containsObject:", v3);
  v7 = CFSTR("1,5,10");
  if ( !v20 )
    v7 = CFSTR("1,5,10,12,18,49");
LABEL_9:
  return v7;
}

//----- (0000000100B7CED7) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForDayKLine:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  id (*v6)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  __CFString *v12; // rbx
  id (*v16)(id, SEL, ...); // r12
  _QWORD v20[6]; // [rsp+10h] [rbp-D0h] BYREF
  _QWORD v21[11]; // [rsp+40h] [rbp-A0h] BYREF
  _QWORD v22[3]; // [rsp+98h] [rbp-48h] BYREF

  v3 = objc_retain(a3);
  v22[0] = CFSTR("USHI");
  v22[1] = CFSTR("USZI");
  v22[2] = CFSTR("UHKM");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v22, 3LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( (unsigned __int8)v6(v5, "containsObject:", v3) )
  {
LABEL_5:
    v12 = CFSTR("1,7,8,9,11,13,19,6");
    goto LABEL_6;
  }
  v21[0] = CFSTR("UCFS");
  v21[1] = CFSTR("UCFD");
  v21[2] = CFSTR("UCFZ");
  v21[3] = CFSTR("UCMN");
  v21[4] = CFSTR("UIFF");
  v21[5] = CFSTR("UCMS");
  v21[6] = CFSTR("UFIS");
  v21[7] = CFSTR("UHFF");
  v21[8] = CFSTR("UHFS");
  v21[9] = CFSTR("UHFR");
  v21[10] = CFSTR("UHFZ");
  v8 = v7(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v21, 11LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( (unsigned __int8)v10(v9, "containsObject:", v3) )
  {
    goto LABEL_5;
  }
  v20[0] = CFSTR("USOO");
  v20[1] = CFSTR("UZOO");
  v20[2] = CFSTR("UCFT");
  v20[3] = CFSTR("UCFL");
  v20[4] = CFSTR("UCFX");
  v20[5] = CFSTR("UIFB");
  v14 = v11(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v20, 6LL);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v19 = (unsigned __int8)v16(v15, "containsObject:", v3);
  v17(v9);
  v18(v5);
  v12 = CFSTR("1,7,8,9,11,13,19,6");
  if ( !v19 )
    v12 = CFSTR("1,7,8,9,11,13,19,1968584,6");
LABEL_6:
  return v12;
}

//----- (0000000100B7D128) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForPriceVolume:](id a1, SEL a2, id a3)
{
  return CFSTR("13,18,14,61");
}

//----- (0000000100B7D135) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForQuarterKLine:](id a1, SEL a2, id a3)
{
  return CFSTR("1,7,8,9,11,13,19,1968584,6");
}

//----- (0000000100B7D142) ----------------------------------------------------
id __cdecl +[HXTools dataTypeForFeedData:](id a1, SEL a2, id a3)
{
  return CFSTR("5,13,19,201,202,207,208,203,204,209,210,205,206,211,212,213,214");
}

//----- (0000000100B7D14F) ----------------------------------------------------
signed __int64 __cdecl +[HXTools compareString:withString:](id a1, SEL a2, id a3, id a4)
{
  signed __int64 v13; // r15

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( !(unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
    goto LABEL_8;
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( !(unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8)
    || !_objc_msgSend(v9, "length")
    || !_objc_msgSend(v5, "length") )
  {
    goto LABEL_8;
  }
  if ( !(unsigned __int8)+[HXTools string:firstCharIsEqualToString:](
                           &OBJC_CLASS___HXTools,
                           "string:firstCharIsEqualToString:",
                           v10,
                           v5) )
  {
    v13 = +[HXTools comparisonResultOf:And:](&OBJC_CLASS___HXTools, "comparisonResultOf:And:", v11, v5);
    goto LABEL_9;
  }
  if ( _objc_msgSend(v11, "length") != (id)1 || _objc_msgSend(v5, "length") != (id)1 )
  {
    if ( _objc_msgSend(v12, "length") != (id)1 || (v13 = -1LL, (unsigned __int64)_objc_msgSend(v5, "length") <= 1) )
    {
      if ( (unsigned __int64)_objc_msgSend(v16, "length") < 2 || (v13 = 1LL, _objc_msgSend(v5, "length") != (id)1) )
      {
        v18 = _objc_msgSend(v17, "substringFromIndex:", 1LL);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v21 = _objc_msgSend(v5, "substringFromIndex:", 1LL);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        v23(v5);
        v13 = +[HXTools compareString:withString:](&OBJC_CLASS___HXTools, "compareString:withString:", v19, v22);
        v5 = v22;
      }
    }
  }
  else
  {
LABEL_8:
    v13 = 0LL;
  }
LABEL_9:
  return v13;
}

//----- (0000000100B7D34C) ----------------------------------------------------
signed __int64 __cdecl +[HXTools comparisonResultOf:And:](id a1, SEL a2, id a3, id a4)
{
  signed __int64 v9; // r13
  unsigned __int16 v10; // ax
  id (*v12)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  unsigned __int16 v15; // ax
  id (*v17)(id, SEL, ...); // r12
  unsigned __int16 v22; // ax
  unsigned __int16 v25; // ax
  NSString *v26; // rax

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( !_objc_msgSend(v5, "length") || !_objc_msgSend(v6, "length") )
  {
LABEL_11:
    v9 = 0LL;
    goto LABEL_12;
  }
  +[HXTools firstCharIsChineseChar:](&OBJC_CLASS___HXTools, "firstCharIsChineseChar:", v5);
  v7 = (unsigned __int8)+[HXTools firstCharIsChineseChar:](&OBJC_CLASS___HXTools, "firstCharIsChineseChar:", v6);
  if ( !v8 || (v9 = 1LL, v7) )
  {
    if ( v8 || (v9 = -1LL, !v7) )
    {
      if ( v8 && v7 )
      {
        v10 = (unsigned __int16)_objc_msgSend(v5, "characterAtIndex:", 0LL);
        v11 = sub_10029D6B8(v10);
        v13 = v12(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%c"), (unsigned int)v11);
        v29 = objc_retainAutoreleasedReturnValue(v13);
        v15 = (unsigned __int16)v14(v6, "characterAtIndex:", 0LL);
        v16 = sub_10029D6B8(v15);
        v18 = v17(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%c"), (unsigned int)v16);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v20 = _objc_msgSend(v29, "compare:", v19);
LABEL_14:
        v9 = (signed __int64)v20;
        v28(v29);
        goto LABEL_12;
      }
      if ( !(v8 | v7) )
      {
        v22 = (unsigned __int16)_objc_msgSend(v5, "characterAtIndex:", 0LL);
        v24 = _objc_msgSend(v23, "stringWithFormat:", CFSTR("%C"), v22);
        v29 = objc_retainAutoreleasedReturnValue(v24);
        v25 = (unsigned __int16)_objc_msgSend(v6, "characterAtIndex:", 0LL);
        v26 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%C"), v25);
        v19 = objc_retainAutoreleasedReturnValue(v26);
        v20 = _objc_msgSend(v27, "compare:", v19);
        goto LABEL_14;
      }
      goto LABEL_11;
    }
  }
LABEL_12:
  return v9;
}

//----- (0000000100B7D58D) ----------------------------------------------------
char __cdecl +[HXTools string:firstCharIsEqualToString:](id a1, SEL a2, id a3, id a4)
{
  unsigned __int16 v8; // bx
  bool v9; // r15

  objc_retain(a3);
  v5 = objc_retain(a4);
  if ( _objc_msgSend(v6, "length") && _objc_msgSend(v5, "length") )
  {
    v8 = (unsigned __int16)_objc_msgSend(v7, "characterAtIndex:", 0LL);
    v9 = v8 == (unsigned __int16)_objc_msgSend(v5, "characterAtIndex:", 0LL);
  }
  else
  {
    v9 = 0;
  }
  return v9;
}

//----- (0000000100B7D636) ----------------------------------------------------
char __cdecl +[HXTools firstCharIsChineseChar:](id a1, SEL a2, id a3)
{
  unsigned __int16 v4; // ax
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  bool v10; // r14

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = (unsigned __int16)_objc_msgSend(v3, "characterAtIndex:", 0LL);
    v5 = sub_10029D6B8(v4);
    v7 = v6(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%c"), (unsigned int)v5);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = (unsigned __int8)v9(v8, "isEqual:", CFSTR("#")) == 0;
  }
  else
  {
    v10 = 0;
  }
  return v10;
}

//----- (0000000100B7D6E9) ----------------------------------------------------
id __cdecl +[HXTools getFirstLetter:](id a1, SEL a2, unsigned __int16 a3)
{
  NSString *v4; // rax
  NSString *v5; // rbx

  if ( (unsigned __int16)(a3 - 48) < 0xAu || (unsigned __int16)((a3 & 0xFFDF) - 65) <= 0x19u )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%c"), a3);
  }
  else
  {
    v3 = (unsigned int)(char)sub_10029D6B8(a3);
    v4 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%c"), v3);
  }
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "uppercaseString");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100B7D787) ----------------------------------------------------
id __cdecl +[HXTools getFirstLetterString:](id a1, SEL a2, id a3)
{
  unsigned __int64 v11; // r15
  SEL v19; // [rsp+10h] [rbp-50h]
  SEL v20; // [rsp+18h] [rbp-48h]

  v18 = a1;
  v3 = objc_retain(a3);
  v21 = &v17;
  v4 = (char *)&v17 - ((2LL * (_QWORD)_objc_msgSend(v3, "length") + 15) & 0xFFFFFFFFFFFFFFF0LL);
  v6 = _objc_msgSend(v5, "length");
  _objc_msgSend(v7, "getCharacters:range:", v4, 0LL, v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSMutableString, "string");
  v22 = objc_retainAutoreleasedReturnValue(v8);
  if ( _objc_msgSend(v9, "length") )
  {
    v19 = "isEqualToString:";
    v20 = "appendString:";
    v11 = 0LL;
    do
    {
      v12 = _objc_msgSend(v18, "getFirstLetter:", *(unsigned __int16 *)&v4[2 * v11]);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = v13;
      if ( v13 && !(unsigned __int8)_objc_msgSend(v13, v19, CFSTR("#")) )
        _objc_msgSend(v22, v20, v14);
      ++v11;
    }
    while ( (unsigned __int64)_objc_msgSend(v15, "length") > v11 );
  }
  return objc_autoreleaseReturnValue(v22);
}

//----- (0000000100B7D8E4) ----------------------------------------------------
char __cdecl +[HXTools isShenMarket:](id a1, SEL a2, id a3)
{
  _QWORD v9[2]; // [rsp+0h] [rbp-40h] BYREF

  v9[0] = CFSTR("USZA");
  v9[1] = CFSTR("USZB");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v9, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7D9A7) ----------------------------------------------------
char __cdecl +[HXTools isHuShenMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  _QWORD v9[18]; // [rsp+0h] [rbp-C0h] BYREF

  v9[0] = CFSTR("USHI");
  v9[1] = CFSTR("USHA");
  v9[2] = CFSTR("USHB");
  v9[3] = CFSTR("USHD");
  v9[4] = CFSTR("USHJ");
  v9[5] = CFSTR("USHP");
  v9[6] = CFSTR("USHT");
  v9[7] = CFSTR("UTII");
  v9[8] = CFSTR("USZI");
  v9[9] = CFSTR("USZA");
  v9[10] = CFSTR("USZB");
  v9[11] = CFSTR("USZD");
  v9[12] = CFSTR("USZJ");
  v9[13] = CFSTR("USZP");
  v9[14] = CFSTR("USZH");
  v9[15] = CFSTR("URFI");
  v9[16] = CFSTR("USZC");
  v9[17] = CFSTR("USHC");
  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 18LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  return v6;
}

//----- (0000000100B7DB29) ----------------------------------------------------
char __cdecl +[HXTools isHongKongMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  _QWORD v9[5]; // [rsp+8h] [rbp-58h] BYREF

  v9[0] = CFSTR("UHKI");
  v9[1] = CFSTR("UHKM");
  v9[2] = CFSTR("UHKG");
  v9[3] = CFSTR("UHKB");
  v9[4] = CFSTR("UHKW");
  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 5LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  return v6;
}

//----- (0000000100B7DC0D) ----------------------------------------------------
char __cdecl +[HXTools isWoLunNiuXiongMarket:](id a1, SEL a2, id a3)
{
  _QWORD v9[2]; // [rsp+0h] [rbp-40h] BYREF

  v9[0] = CFSTR("UHKB");
  v9[1] = CFSTR("UHKW");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v9, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7DCD0) ----------------------------------------------------
char __cdecl +[HXTools isMeiGuMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  _QWORD v9[7]; // [rsp+8h] [rbp-68h] BYREF

  v9[0] = CFSTR("UFII");
  v9[1] = CFSTR("UNQS");
  v9[2] = CFSTR("UNYN");
  v9[3] = CFSTR("UNQQ");
  v9[4] = CFSTR("UNYA");
  v9[5] = CFSTR("UUSD");
  v9[6] = CFSTR("UUSA");
  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 7LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  return v6;
}

//----- (0000000100B7DDCA) ----------------------------------------------------
char __cdecl +[HXTools isMeiGuSingleMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  _QWORD v9[6]; // [rsp+0h] [rbp-60h] BYREF

  v9[0] = CFSTR("UNQS");
  v9[1] = CFSTR("UNYN");
  v9[2] = CFSTR("UNQQ");
  v9[3] = CFSTR("UNYA");
  v9[4] = CFSTR("UUSD");
  v9[5] = CFSTR("UUSA");
  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 6LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  return v6;
}

//----- (0000000100B7DEB9) ----------------------------------------------------
char __cdecl +[HXTools isJiJinMarket:](id a1, SEL a2, id a3)
{
  _QWORD v9[2]; // [rsp+0h] [rbp-40h] BYREF

  v9[0] = CFSTR("USHJ");
  v9[1] = CFSTR("USZJ");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v9, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7DF7C) ----------------------------------------------------
char __cdecl +[HXTools isWaiHuiMarket:](id a1, SEL a2, id a3)
{
  NSArray *v5; // rax
  NSArray *v6; // rbx

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v4, 3LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7E04D) ----------------------------------------------------
char __cdecl +[HXTools isXinSanBanMarket:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // r13
  NSArray *v6; // rax
  NSArray *v7; // r15
  __CFString *v10; // [rsp+0h] [rbp-50h] BYREF
  _QWORD v11[3]; // [rsp+8h] [rbp-48h] BYREF

  v3 = objc_retain(a3);
  v11[0] = CFSTR("USTA");
  v11[1] = CFSTR("USTB");
  v11[2] = CFSTR("USTT");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v11, 3LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( !(unsigned __int8)_objc_msgSend(v5, "containsObject:", v3) )
  {
    v10 = CFSTR("USTI");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v10, 1LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "containsObject:", v3);
  }
  return v8;
}

//----- (0000000100B7E15D) ----------------------------------------------------
char __cdecl +[HXTools isHuLunTongCDRMarket:](id a1, SEL a2, id a3)
{
  _QWORD v9[2]; // [rsp+0h] [rbp-40h] BYREF

  v9[0] = CFSTR("USHC");
  v9[1] = CFSTR("USZC");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v9, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7E220) ----------------------------------------------------
char __cdecl +[HXTools isXinSanBanIndexMarket:](id a1, SEL a2, id a3)
{
  __CFString *v9; // [rsp+8h] [rbp-38h] BYREF

  v9 = CFSTR("USTI");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", &v9, 1LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7E2D8) ----------------------------------------------------
char __cdecl +[HXTools isGuoWaiZhiShuMarket:](id a1, SEL a2, id a3)
{
  _QWORD v9[2]; // [rsp+0h] [rbp-40h] BYREF

  v9[0] = CFSTR("UFII");
  v9[1] = CFSTR("UCMI");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v9, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7E39B) ----------------------------------------------------
char __cdecl +[HXTools isQiHuoMarketType:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  bool v7; // bl
  _QWORD v9[11]; // [rsp+8h] [rbp-78h] BYREF

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v9[0] = CFSTR("UCFS");
    v9[1] = CFSTR("UCFD");
    v9[2] = CFSTR("UCFZ");
    v9[3] = CFSTR("UCMN");
    v9[4] = CFSTR("UIFF");
    v9[5] = CFSTR("UCMS");
    v9[6] = CFSTR("UFIS");
    v9[7] = CFSTR("UHFF");
    v9[8] = CFSTR("UHFS");
    v9[9] = CFSTR("UHFR");
    v9[10] = CFSTR("UHFZ");
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 11LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (unsigned __int8)_objc_msgSend(v5, "containsObject:", v3);
    v7 = v6 != 0;
  }
  else
  {
    v7 = 0;
  }
  return v7;
}

//----- (0000000100B7E4D1) ----------------------------------------------------
char __cdecl +[HXTools isOptionMarketType:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  _QWORD v8[6]; // [rsp+0h] [rbp-50h] BYREF

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v8[0] = CFSTR("USOO");
    v8[1] = CFSTR("UZOO");
    v8[2] = CFSTR("UCFT");
    v8[3] = CFSTR("UCFL");
    v8[4] = CFSTR("UCFX");
    v8[5] = CFSTR("UIFB");
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v8, 6LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = (unsigned __int8)_objc_msgSend(v5, "containsObject:", v3);
  }
  else
  {
    v6 = 0;
  }
  return v6;
}

//----- (0000000100B7E5CC) ----------------------------------------------------
char __cdecl +[HXTools isQiYeBondWithstockCodeSubStr:](id a1, SEL a2, id a3)
{
  return (char *)_objc_msgSend(a3, "integerValue") - 11 < (char *)2;
}

//----- (0000000100B7E5F1) ----------------------------------------------------
char __cdecl +[HXTools isHideQueKouWithMarket:](id a1, SEL a2, id a3)
{
  bool v8; // bl
  _QWORD v11[2]; // [rsp+0h] [rbp-40h] BYREF

  if ( !a3 )
    return 0;
  v11[0] = CFSTR("USZD");
  v11[1] = CFSTR("USHD");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", v11, 2LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  v8 = v7 != 0;
  v9(v6);
  return v8;
}

//----- (0000000100B7E6C4) ----------------------------------------------------
char __cdecl +[HXTools isHuShenAMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  _QWORD v9[5]; // [rsp+8h] [rbp-58h] BYREF

  v9[0] = CFSTR("USHA");
  v9[1] = CFSTR("USZA");
  v9[2] = CFSTR("USHT");
  v9[3] = CFSTR("USHP");
  v9[4] = CFSTR("USZP");
  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v9, 5LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  return v6;
}

//----- (0000000100B7E7A8) ----------------------------------------------------
char __cdecl +[HXTools isHuShenABMarket:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // r13
  NSArray *v6; // rax
  NSArray *v7; // r15
  _QWORD v10[2]; // [rsp+8h] [rbp-68h] BYREF
  _QWORD v11[5]; // [rsp+18h] [rbp-58h] BYREF

  v3 = objc_retain(a3);
  v11[0] = CFSTR("USHA");
  v11[1] = CFSTR("USZA");
  v11[2] = CFSTR("USHT");
  v11[3] = CFSTR("USHP");
  v11[4] = CFSTR("USZP");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v11, 5LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( !(unsigned __int8)_objc_msgSend(v5, "containsObject:", v3) )
  {
    v10[0] = CFSTR("USHB");
    v10[1] = CFSTR("USZB");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v10, 2LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "containsObject:", v3);
  }
  return v8;
}

//----- (0000000100B7E8D9) ----------------------------------------------------
char __cdecl +[HXTools isSupportChaoJiPanKou:](id a1, SEL a2, id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // r13
  NSArray *v6; // rax
  NSArray *v7; // r15
  _QWORD v10[2]; // [rsp+8h] [rbp-68h] BYREF
  _QWORD v11[5]; // [rsp+18h] [rbp-58h] BYREF

  v3 = objc_retain(a3);
  v11[0] = CFSTR("USHA");
  v11[1] = CFSTR("USZA");
  v11[2] = CFSTR("USHT");
  v11[3] = CFSTR("USHP");
  v11[4] = CFSTR("USZP");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v11, 5LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( !(unsigned __int8)_objc_msgSend(v5, "containsObject:", v3) )
  {
    v10[0] = CFSTR("USHB");
    v10[1] = CFSTR("USZB");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v10, 2LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "containsObject:", v3);
  }
  return v8;
}

//----- (0000000100B7EA0A) ----------------------------------------------------
char __cdecl +[HXTools isJingAMarket:](id a1, SEL a2, id a3)
{
  __CFString *v9; // [rsp+8h] [rbp-38h] BYREF

  v9 = CFSTR("USTM");
  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "arrayWithObjects:count:", &v9, 1LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "containsObject:", v3);
  return v7;
}

//----- (0000000100B7EAC2) ----------------------------------------------------
char __cdecl +[HXTools isDaPanFengXiangStock:](id a1, SEL a2, id a3)
{
  bool v8; // bl

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) && (unsigned __int64)_objc_msgSend(v5, "length") >= 4 )
  {
    v6 = _objc_msgSend(v5, "substringToIndex:", 4LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("8839")) != 0;
  }
  else
  {
    v8 = 0;
  }
  return v8;
}

//----- (0000000100B7EB73) ----------------------------------------------------
char __cdecl +[HXTools isHideZhuBiWithMarket:](id a1, SEL a2, id a3)
{
  bool v4; // bl

  v3 = objc_retain(a3);
  v4 = 1;
  if ( !(unsigned __int8)+[HXTools isXinSanBanMarket:](&OBJC_CLASS___HXTools, "isXinSanBanMarket:", v3) )
    v4 = (unsigned __int8)+[HXTools isHongKongMarket:](&OBJC_CLASS___HXTools, "isHongKongMarket:", v3) != 0;
  return v4;
}

//----- (0000000100B7EBD0) ----------------------------------------------------
char __cdecl +[HXTools isKeChuangBanStock:market:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rax
  NSDictionary *v12; // rax
  BlockCodesListCacheManager *v13; // rax
  BlockCodesListCacheManager *v14; // r15
  NSString *v18; // [rsp+0h] [rbp-70h]
  NSDictionary *v19; // [rsp+8h] [rbp-68h]
  _QWORD v20[3]; // [rsp+10h] [rbp-60h] BYREF
  _QWORD v21[3]; // [rsp+28h] [rbp-48h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length")
    && ((unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHA"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHC"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHP"))) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%X"), 52197LL);
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v20[0] = CFSTR("StockCode");
    v21[0] = v11;
    v20[1] = CFSTR("Market");
    v21[1] = v5;
    v20[2] = CFSTR("BlockId");
    v21[2] = v18;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 3LL);
    v19 = objc_retainAutoreleasedReturnValue(v12);
    v13 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v14, "isStandardStock:", v19);
  }
  else
  {
    v15 = 0;
  }
  return v15;
}

//----- (0000000100B7EDEC) ----------------------------------------------------
char __cdecl +[HXTools isKeChuangBanCDRStock:market:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rax
  NSDictionary *v12; // rax
  BlockCodesListCacheManager *v13; // rax
  BlockCodesListCacheManager *v14; // r15
  NSString *v18; // [rsp+0h] [rbp-70h]
  NSDictionary *v19; // [rsp+8h] [rbp-68h]
  _QWORD v20[3]; // [rsp+10h] [rbp-60h] BYREF
  _QWORD v21[3]; // [rsp+28h] [rbp-48h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length")
    && ((unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHA"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHC"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHP"))) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%X"), 52166LL);
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v20[0] = CFSTR("StockCode");
    v21[0] = v11;
    v20[1] = CFSTR("Market");
    v21[1] = v5;
    v20[2] = CFSTR("BlockId");
    v21[2] = v18;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 3LL);
    v19 = objc_retainAutoreleasedReturnValue(v12);
    v13 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v14, "isStandardStock:", v19);
  }
  else
  {
    v15 = 0;
  }
  return v15;
}

//----- (0000000100B7F008) ----------------------------------------------------
char __cdecl +[HXTools isChuangYeBanStock:market:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rax
  NSDictionary *v12; // rax
  BlockCodesListCacheManager *v13; // rax
  BlockCodesListCacheManager *v14; // r15
  NSString *v18; // [rsp+0h] [rbp-70h]
  NSDictionary *v19; // [rsp+8h] [rbp-68h]
  _QWORD v20[3]; // [rsp+10h] [rbp-60h] BYREF
  _QWORD v21[3]; // [rsp+28h] [rbp-48h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length")
    && ((unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZA"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZC"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZP"))) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%X"), 53220LL);
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v20[0] = CFSTR("StockCode");
    v21[0] = v11;
    v20[1] = CFSTR("Market");
    v21[1] = v5;
    v20[2] = CFSTR("BlockId");
    v21[2] = v18;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 3LL);
    v19 = objc_retainAutoreleasedReturnValue(v12);
    v13 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v14, "isStandardStock:", v19);
  }
  else
  {
    v15 = 0;
  }
  return v15;
}

//----- (0000000100B7F224) ----------------------------------------------------
char __cdecl +[HXTools isKeZhuangZhaiCode:market:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rax
  NSDictionary *v12; // rax
  BlockCodesListCacheManager *v13; // rax
  BlockCodesListCacheManager *v14; // r15
  NSString *v18; // [rsp+0h] [rbp-70h]
  NSDictionary *v19; // [rsp+8h] [rbp-68h]
  _QWORD v20[3]; // [rsp+10h] [rbp-60h] BYREF
  _QWORD v21[3]; // [rsp+28h] [rbp-48h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length")
    && ((unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZD"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHD"))) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%X"), 52756LL);
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v20[0] = CFSTR("StockCode");
    v21[0] = v11;
    v20[1] = CFSTR("Market");
    v21[1] = v5;
    v20[2] = CFSTR("BlockId");
    v21[2] = v18;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 3LL);
    v19 = objc_retainAutoreleasedReturnValue(v12);
    v13 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v14, "isStandardStock:", v19);
  }
  else
  {
    v15 = 0;
  }
  return v15;
}

//----- (0000000100B7F429) ----------------------------------------------------
char __cdecl +[HXTools isREITsStock:market:](id a1, SEL a2, id a3, id a4)
{
  NSString *v10; // rax
  NSDictionary *v12; // rax
  BlockCodesListCacheManager *v13; // rax
  BlockCodesListCacheManager *v14; // r15
  NSString *v18; // [rsp+0h] [rbp-70h]
  NSDictionary *v19; // [rsp+8h] [rbp-68h]
  _QWORD v20[3]; // [rsp+10h] [rbp-60h] BYREF
  _QWORD v21[3]; // [rsp+28h] [rbp-48h] BYREF

  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class"), (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8))
    && _objc_msgSend(v9, "length")
    && _objc_msgSend(v5, "length")
    && ((unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHJ"))
     || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZJ"))) )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%X"), 51356LL);
    v18 = objc_retainAutoreleasedReturnValue(v10);
    v20[0] = CFSTR("StockCode");
    v21[0] = v11;
    v20[1] = CFSTR("Market");
    v21[1] = v5;
    v20[2] = CFSTR("BlockId");
    v21[2] = v18;
    v12 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v21, v20, 3LL);
    v19 = objc_retainAutoreleasedReturnValue(v12);
    v13 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v14, "isStandardStock:", v19);
  }
  else
  {
    v15 = 0;
  }
  return v15;
}

//----- (0000000100B7F62E) ----------------------------------------------------
char __cdecl +[HXTools isJingAStock:market:](id a1, SEL a2, id a3, id a4)
{
  NSArray *v10; // rax
  NSArray *v11; // rax
  __CFString *v15; // [rsp+8h] [rbp-38h] BYREF

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v7)
    && (v9 = _objc_msgSend(&OBJC_CLASS___NSString, v8), (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v9))
    && _objc_msgSend(v5, "length")
    && _objc_msgSend(v6, "length") )
  {
    v15 = CFSTR("USTM");
    v10 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v15, 1LL);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = (unsigned __int8)_objc_msgSend(v11, "containsObject:", v6);
  }
  else
  {
    v12 = 0;
  }
  return v12;
}

//----- (0000000100B7F76E) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools getMainStockCategoryWithMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  unsigned __int64 v8; // rbx
  NSArray *v9; // rax
  NSArray *v10; // rbx
  NSArray *v13; // rax
  NSArray *v14; // r13
  NSArray *v16; // rax
  NSArray *v18; // rax
  NSArray *v19; // r13
  _BOOL8 v21; // rbx
  NSArray *v23; // [rsp+0h] [rbp-E0h]
  _QWORD v25[3]; // [rsp+10h] [rbp-D0h] BYREF
  _QWORD v26[2]; // [rsp+28h] [rbp-B8h] BYREF
  _QWORD v27[3]; // [rsp+38h] [rbp-A8h] BYREF
  _QWORD v28[7]; // [rsp+50h] [rbp-90h] BYREF
  _QWORD v29[5]; // [rsp+88h] [rbp-58h] BYREF

  objc_retain(a3);
  v29[0] = CFSTR("UHKI");
  v29[1] = CFSTR("UHKM");
  v29[2] = CFSTR("UHKG");
  v29[3] = CFSTR("UHKB");
  v29[4] = CFSTR("UHKW");
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v29, 5LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  v8 = 1LL;
  if ( !v6 )
  {
    v28[0] = CFSTR("UFII");
    v28[1] = CFSTR("UNQS");
    v28[2] = CFSTR("UNYN");
    v28[3] = CFSTR("UNQQ");
    v28[4] = CFSTR("UNYA");
    v28[5] = CFSTR("UUSD");
    v28[6] = CFSTR("UUSA");
    v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v28, 7LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = (unsigned __int8)_objc_msgSend(v10, "containsObject:", v11);
    v8 = 2LL;
    if ( !v12 )
    {
      v27[0] = CFSTR("UCFS");
      v27[1] = CFSTR("UCFD");
      v27[2] = CFSTR("UCFZ");
      v8 = 3LL;
      v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v27, 3LL);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      if ( (unsigned __int8)_objc_msgSend(v14, "containsObject:", v15) )
      {
      }
      else
      {
        v26[0] = CFSTR("UCMN");
        v26[1] = CFSTR("UFIS");
        v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 2LL);
        v23 = objc_retainAutoreleasedReturnValue(v16);
        v24 = (unsigned __int8)_objc_msgSend(v23, "containsObject:", v17);
        if ( !v24 )
        {
          v25[0] = CFSTR("UFXB");
          v25[1] = CFSTR("UFXC");
          v25[2] = CFSTR("UFXR");
          v18 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v25, 3LL);
          v19 = objc_retainAutoreleasedReturnValue(v18);
          v21 = (unsigned __int8)_objc_msgSend(v19, "containsObject:", v20) != 0;
          v8 = 4 * v21;
        }
      }
    }
  }
  return v8;
}

//----- (0000000100B7FA3D) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools getStockType:stockCode:](id a1, SEL a2, id a3, id a4)
{
  unsigned __int64 v7; // rbx
  NSDictionary *v10; // rax
  NSDictionary *v11; // r13
  BlockCodesListCacheManager *v12; // rax
  BlockCodesListCacheManager *v13; // rbx
  NSDictionary *v15; // rdi
  unsigned __int64 v19; // [rsp+10h] [rbp-30h]

  objc_retain(a3);
  v5 = objc_retain(a4);
  v7 = (unsigned __int64)_objc_msgSend(a1, "getStockTypeWithMarket:", v6);
  if ( (unsigned __int8)_objc_msgSend(v8, "isEqualToString:", CFSTR("USHA")) )
  {
    v19 = v7;
    v10 = _objc_msgSend(
            &OBJC_CLASS___NSDictionary,
            "dictionaryWithObjectsAndKeys:",
            v5,
            CFSTR("StockCode"),
            v9,
            CFSTR("Market"),
            CFSTR("CC7B"),
            CFSTR("BlockId"),
            0LL);
    v18 = v5;
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = +[BlockCodesListCacheManager sharedInstance](&OBJC_CLASS___BlockCodesListCacheManager, "sharedInstance");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = (unsigned __int8)-[BlockCodesListCacheManager isStandardStock:](v13, "isStandardStock:", v11);
    v7 = v19;
    if ( v14 )
      v7 = 23LL;
    v15 = v11;
    v5 = v18;
  }
  return v7;
}

//----- (0000000100B7FB73) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools getStockTypeWithMarket:](id a1, SEL a2, id a3)
{
  unsigned __int64 v5; // rbx
  NSArray *v6; // rax
  NSArray *v7; // rbx
  NSArray *v10; // rax
  NSArray *v11; // rbx
  NSArray *v14; // rax
  NSArray *v15; // rbx
  NSArray *v18; // rax
  NSArray *v19; // rbx
  NSArray *v22; // rax
  NSArray *v23; // rbx
  NSArray *v26; // rax
  NSArray *v27; // rbx
  NSArray *v30; // rax
  NSArray *v31; // rbx
  NSArray *v34; // rax
  NSArray *v35; // rbx
  NSArray *v38; // rax
  NSArray *v39; // rbx
  NSArray *v42; // rax
  NSArray *v43; // rbx
  NSArray *v46; // rax
  NSArray *v47; // rbx
  NSArray *v50; // rax
  NSArray *v51; // rbx
  NSArray *v54; // rax
  NSArray *v55; // rbx
  NSArray *v58; // rax
  NSArray *v59; // rbx
  NSArray *v62; // rax
  NSArray *v63; // rbx
  NSArray *v66; // rax
  NSArray *v67; // rbx
  NSArray *v70; // rax
  NSArray *v71; // rbx
  NSArray *v74; // rax
  NSArray *v75; // rbx
  NSArray *v78; // rax
  NSArray *v79; // rbx
  NSArray *v82; // rax
  NSArray *v83; // rbx
  NSArray *v86; // rax
  NSArray *v87; // rbx
  NSArray *v90; // rax
  NSArray *v91; // rbx
  NSArray *v94; // rax
  NSArray *v95; // rbx
  NSArray *v98; // rax
  NSArray *v99; // rbx
  __CFString *v103; // [rsp+0h] [rbp-200h] BYREF
  _QWORD v104[4]; // [rsp+8h] [rbp-1F8h] BYREF
  _QWORD v105[2]; // [rsp+28h] [rbp-1D8h] BYREF
  _QWORD v106[2]; // [rsp+38h] [rbp-1C8h] BYREF
  _QWORD v107[3]; // [rsp+48h] [rbp-1B8h] BYREF
  _QWORD v108[3]; // [rsp+60h] [rbp-1A0h] BYREF
  _QWORD v109[2]; // [rsp+78h] [rbp-188h] BYREF
  __CFString *v110; // [rsp+88h] [rbp-178h] BYREF
  __CFString *v111; // [rsp+90h] [rbp-170h] BYREF
  _QWORD v112[2]; // [rsp+98h] [rbp-168h] BYREF
  _QWORD v113[3]; // [rsp+A8h] [rbp-158h] BYREF
  _QWORD v114[3]; // [rsp+C0h] [rbp-140h] BYREF
  _QWORD v115[2]; // [rsp+D8h] [rbp-128h] BYREF
  _QWORD v116[3]; // [rsp+E8h] [rbp-118h] BYREF
  _QWORD v117[2]; // [rsp+100h] [rbp-100h] BYREF
  _QWORD v118[6]; // [rsp+110h] [rbp-F0h] BYREF
  _QWORD v119[2]; // [rsp+140h] [rbp-C0h] BYREF
  __CFString *v120; // [rsp+150h] [rbp-B0h] BYREF
  _QWORD v121[2]; // [rsp+158h] [rbp-A8h] BYREF
  __CFString *v122; // [rsp+168h] [rbp-98h] BYREF
  _QWORD v123[2]; // [rsp+170h] [rbp-90h] BYREF
  _QWORD v124[3]; // [rsp+180h] [rbp-80h] BYREF
  _QWORD v125[5]; // [rsp+198h] [rbp-68h] BYREF
  _QWORD v126[2]; // [rsp+1C0h] [rbp-40h] BYREF

  v3 = objc_retain(a3);
  v5 = 11LL;
  if ( _objc_msgSend(v3, "length") )
  {
    v126[0] = CFSTR("USHC");
    v126[1] = CFSTR("USZC");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v126, 2LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = (unsigned __int8)_objc_msgSend(v7, "containsObject:", v8);
    v5 = 23LL;
    if ( !v9 )
    {
      v125[0] = CFSTR("USHA");
      v125[1] = CFSTR("USZA");
      v125[2] = CFSTR("USHT");
      v125[3] = CFSTR("USHP");
      v125[4] = CFSTR("USZP");
      v10 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v125, 5LL);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = (unsigned __int8)_objc_msgSend(v11, "containsObject:", v12);
      v5 = 11LL;
      if ( !v13 )
      {
        v124[0] = CFSTR("USHI");
        v124[1] = CFSTR("USZI");
        v124[2] = CFSTR("UZZI");
        v14 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v124, 3LL);
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v17 = (unsigned __int8)_objc_msgSend(v15, "containsObject:", v16);
        v5 = 2LL;
        if ( !v17 )
        {
          v123[0] = CFSTR("URFI");
          v123[1] = CFSTR("URFA");
          v18 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v123, 2LL);
          v19 = objc_retainAutoreleasedReturnValue(v18);
          v21 = (unsigned __int8)_objc_msgSend(v19, "containsObject:", v20);
          v5 = 3LL;
          if ( !v21 )
          {
            v122 = CFSTR("UHKI");
            v22 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v122, 1LL);
            v23 = objc_retainAutoreleasedReturnValue(v22);
            v25 = (unsigned __int8)_objc_msgSend(v23, "containsObject:", v24);
            v5 = 4LL;
            if ( !v25 )
            {
              v121[0] = CFSTR("UHKM");
              v121[1] = CFSTR("UHKG");
              v26 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v121, 2LL);
              v27 = objc_retainAutoreleasedReturnValue(v26);
              v29 = (unsigned __int8)_objc_msgSend(v27, "containsObject:", v28);
              v5 = 5LL;
              if ( !v29 )
              {
                v120 = CFSTR("UIFF");
                v30 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v120, 1LL);
                v31 = objc_retainAutoreleasedReturnValue(v30);
                v33 = (unsigned __int8)_objc_msgSend(v31, "containsObject:", v32);
                v5 = 6LL;
                if ( !v33 )
                {
                  v119[0] = CFSTR("UFII");
                  v119[1] = CFSTR("UCMI");
                  v34 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v119, 2LL);
                  v35 = objc_retainAutoreleasedReturnValue(v34);
                  v37 = (unsigned __int8)_objc_msgSend(v35, "containsObject:", v36);
                  v5 = 7LL;
                  if ( !v37 )
                  {
                    v118[0] = CFSTR("UNQS");
                    v118[1] = CFSTR("UNYN");
                    v118[2] = CFSTR("UNQQ");
                    v118[3] = CFSTR("UNYA");
                    v118[4] = CFSTR("UUSD");
                    v118[5] = CFSTR("UUSA");
                    v38 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v118, 6LL);
                    v39 = objc_retainAutoreleasedReturnValue(v38);
                    v41 = (unsigned __int8)_objc_msgSend(v39, "containsObject:", v40);
                    v5 = 8LL;
                    if ( !v41 )
                    {
                      v117[0] = CFSTR("USHJ");
                      v117[1] = CFSTR("USZJ");
                      v42 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v117, 2LL);
                      v43 = objc_retainAutoreleasedReturnValue(v42);
                      v45 = (unsigned __int8)_objc_msgSend(v43, "containsObject:", v44);
                      v5 = 9LL;
                      if ( !v45 )
                      {
                        v116[0] = CFSTR("UFXB");
                        v116[1] = CFSTR("UFXC");
                        v116[2] = CFSTR("UFXR");
                        v46 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v116, 3LL);
                        v47 = objc_retainAutoreleasedReturnValue(v46);
                        v49 = (unsigned __int8)_objc_msgSend(v47, "containsObject:", v48);
                        v5 = 10LL;
                        if ( !v49 )
                        {
                          v115[0] = CFSTR("USHB");
                          v115[1] = CFSTR("USZB");
                          v50 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v115, 2LL);
                          v51 = objc_retainAutoreleasedReturnValue(v50);
                          v53 = (unsigned __int8)_objc_msgSend(v51, "containsObject:", v52);
                          v5 = 12LL;
                          if ( !v53 )
                          {
                            v114[0] = CFSTR("UCMN");
                            v114[1] = CFSTR("UCMS");
                            v114[2] = CFSTR("UFIS");
                            v54 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v114, 3LL);
                            v55 = objc_retainAutoreleasedReturnValue(v54);
                            v57 = (unsigned __int8)_objc_msgSend(v55, "containsObject:", v56);
                            v5 = 14LL;
                            if ( !v57 )
                            {
                              v113[0] = CFSTR("UCFS");
                              v113[1] = CFSTR("UCFD");
                              v113[2] = CFSTR("UCFZ");
                              v58 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v113, 3LL);
                              v59 = objc_retainAutoreleasedReturnValue(v58);
                              v61 = (unsigned __int8)_objc_msgSend(v59, "containsObject:", v60);
                              v5 = 13LL;
                              if ( !v61 )
                              {
                                v112[0] = CFSTR("USTA");
                                v112[1] = CFSTR("USTB");
                                v62 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v112, 2LL);
                                v63 = objc_retainAutoreleasedReturnValue(v62);
                                v65 = (unsigned __int8)_objc_msgSend(v63, "containsObject:", v64);
                                v5 = 15LL;
                                if ( !v65 )
                                {
                                  v111 = CFSTR("USTT");
                                  v66 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v111, 1LL);
                                  v67 = objc_retainAutoreleasedReturnValue(v66);
                                  v69 = (unsigned __int8)_objc_msgSend(v67, "containsObject:", v68);
                                  v5 = 17LL;
                                  if ( !v69 )
                                  {
                                    v110 = CFSTR("USTI");
                                    v70 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v110, 1LL);
                                    v71 = objc_retainAutoreleasedReturnValue(v70);
                                    v73 = (unsigned __int8)_objc_msgSend(v71, "containsObject:", v72);
                                    v5 = 16LL;
                                    if ( !v73 )
                                    {
                                      v109[0] = CFSTR("USZD");
                                      v109[1] = CFSTR("USHD");
                                      v74 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v109, 2LL);
                                      v75 = objc_retainAutoreleasedReturnValue(v74);
                                      v77 = (unsigned __int8)_objc_msgSend(v75, "containsObject:", v76);
                                      v5 = 18LL;
                                      if ( !v77 )
                                      {
                                        v108[0] = CFSTR("USOO");
                                        v108[1] = CFSTR("UZOO");
                                        v108[2] = CFSTR("UIFB");
                                        v78 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v108, 3LL);
                                        v79 = objc_retainAutoreleasedReturnValue(v78);
                                        v81 = (unsigned __int8)_objc_msgSend(v79, "containsObject:", v80);
                                        v5 = 19LL;
                                        if ( !v81 )
                                        {
                                          v107[0] = CFSTR("UCFT");
                                          v107[1] = CFSTR("UCFL");
                                          v107[2] = CFSTR("UCFX");
                                          v82 = _objc_msgSend(
                                                  &OBJC_CLASS___NSArray,
                                                  "arrayWithObjects:count:",
                                                  v107,
                                                  3LL);
                                          v83 = objc_retainAutoreleasedReturnValue(v82);
                                          v85 = (unsigned __int8)_objc_msgSend(v83, "containsObject:", v84);
                                          v5 = 20LL;
                                          if ( !v85 )
                                          {
                                            v106[0] = CFSTR("UEUA");
                                            v106[1] = CFSTR("UEUB");
                                            v86 = _objc_msgSend(
                                                    &OBJC_CLASS___NSArray,
                                                    "arrayWithObjects:count:",
                                                    v106,
                                                    2LL);
                                            v87 = objc_retainAutoreleasedReturnValue(v86);
                                            v89 = (unsigned __int8)_objc_msgSend(v87, "containsObject:", v88);
                                            v5 = 21LL;
                                            if ( !v89 )
                                            {
                                              v105[0] = CFSTR("UHKB");
                                              v105[1] = CFSTR("UHKW");
                                              v90 = _objc_msgSend(
                                                      &OBJC_CLASS___NSArray,
                                                      "arrayWithObjects:count:",
                                                      v105,
                                                      2LL);
                                              v91 = objc_retainAutoreleasedReturnValue(v90);
                                              v93 = (unsigned __int8)_objc_msgSend(v91, "containsObject:", v92);
                                              v5 = 22LL;
                                              if ( !v93 )
                                              {
                                                v104[0] = CFSTR("UHFF");
                                                v104[1] = CFSTR("UHFS");
                                                v104[2] = CFSTR("UHFR");
                                                v104[3] = CFSTR("UHFZ");
                                                v94 = _objc_msgSend(
                                                        &OBJC_CLASS___NSArray,
                                                        "arrayWithObjects:count:",
                                                        v104,
                                                        4LL);
                                                v95 = objc_retainAutoreleasedReturnValue(v94);
                                                v97 = (unsigned __int8)_objc_msgSend(v95, "containsObject:", v96);
                                                v5 = 24LL;
                                                if ( !v97 )
                                                {
                                                  v103 = CFSTR("USTM");
                                                  v98 = _objc_msgSend(
                                                          &OBJC_CLASS___NSArray,
                                                          "arrayWithObjects:count:",
                                                          &v103,
                                                          1LL);
                                                  v99 = objc_retainAutoreleasedReturnValue(v98);
                                                  v101 = (unsigned __int8)_objc_msgSend(v99, "containsObject:", v100);
                                                  v5 = 25LL;
                                                  if ( !v101 )
                                                    v5 = 1LL;
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return v5;
}

//----- (0000000100B805AD) ----------------------------------------------------
id __cdecl +[HXTools getMarketChineseNameWithMarket:](id a1, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  __CFString *v8; // rbx
  NSArray *v9; // rax
  NSArray *v10; // rbx
  NSArray *v13; // rax
  NSArray *v14; // rbx
  NSArray *v17; // rax
  NSArray *v18; // rbx
  NSArray *v21; // rax
  NSArray *v22; // rbx
  NSArray *v24; // rax
  NSArray *v25; // r13
  NSArray *v28; // rax
  NSArray *v29; // rbx
  NSArray *v32; // rax
  NSArray *v33; // rbx
  NSArray *v36; // rax
  NSArray *v37; // rbx
  NSArray *v40; // rax
  NSArray *v41; // rbx
  NSArray *v44; // rax
  NSArray *v45; // rbx
  NSArray *v48; // rax
  NSArray *v49; // rbx
  NSArray *v52; // rax
  NSArray *v53; // rbx
  NSArray *v56; // rax
  NSArray *v57; // rbx
  NSArray *v60; // rax
  NSArray *v61; // rbx
  NSArray *v64; // rax
  NSArray *v65; // rbx
  NSArray *v68; // rax
  NSArray *v69; // rbx
  NSArray *v72; // rax
  NSArray *v73; // rbx
  NSArray *v76; // rax
  NSArray *v77; // rbx
  _QWORD v81[2]; // [rsp+8h] [rbp-1C8h] BYREF
  _QWORD v82[6]; // [rsp+18h] [rbp-1B8h] BYREF
  _QWORD v83[2]; // [rsp+48h] [rbp-188h] BYREF
  __CFString *v84; // [rsp+58h] [rbp-178h] BYREF
  _QWORD v85[3]; // [rsp+60h] [rbp-170h] BYREF
  _QWORD v86[3]; // [rsp+78h] [rbp-158h] BYREF
  _QWORD v87[3]; // [rsp+90h] [rbp-140h] BYREF
  _QWORD v88[2]; // [rsp+A8h] [rbp-128h] BYREF
  _QWORD v89[3]; // [rsp+B8h] [rbp-118h] BYREF
  _QWORD v90[2]; // [rsp+D0h] [rbp-100h] BYREF
  _QWORD v91[6]; // [rsp+E0h] [rbp-F0h] BYREF
  _QWORD v92[2]; // [rsp+110h] [rbp-C0h] BYREF
  __CFString *v93; // [rsp+120h] [rbp-B0h] BYREF
  _QWORD v94[2]; // [rsp+128h] [rbp-A8h] BYREF
  _QWORD v95[2]; // [rsp+138h] [rbp-98h] BYREF
  __CFString *v96; // [rsp+148h] [rbp-88h] BYREF
  _QWORD v97[2]; // [rsp+150h] [rbp-80h] BYREF
  _QWORD v98[3]; // [rsp+160h] [rbp-70h] BYREF
  _QWORD v99[5]; // [rsp+178h] [rbp-58h] BYREF

  objc_retain(a3);
  v99[0] = CFSTR("USHA");
  v99[1] = CFSTR("USZA");
  v99[2] = CFSTR("USHT");
  v99[3] = CFSTR("USHP");
  v99[4] = CFSTR("USZP");
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v99, 5LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v5);
  if ( v6 )
  {
    v8 = CFSTR("沪深A股");
    goto LABEL_13;
  }
  v98[0] = CFSTR("USHI");
  v98[1] = CFSTR("USZI");
  v98[2] = CFSTR("UZZI");
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v98, 3LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = (unsigned __int8)_objc_msgSend(v10, "containsObject:", v11);
  if ( v12 )
  {
    v8 = CFSTR("沪深指数");
    goto LABEL_13;
  }
  v97[0] = CFSTR("URFI");
  v97[1] = CFSTR("URFA");
  v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v97, 2LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = (unsigned __int8)_objc_msgSend(v14, "containsObject:", v15);
  if ( v16 )
  {
    v8 = CFSTR("沪深板块");
    goto LABEL_13;
  }
  v96 = CFSTR("UHKI");
  v17 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v96, 1LL);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v20 = (unsigned __int8)_objc_msgSend(v18, "containsObject:", v19);
  if ( v20 )
  {
    v8 = CFSTR("港股指数");
    goto LABEL_13;
  }
  v95[0] = CFSTR("UHKM");
  v95[1] = CFSTR("UHKG");
  v21 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v95, 2LL);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  if ( (unsigned __int8)_objc_msgSend(v22, "containsObject:", v23) )
  {
LABEL_12:
    v8 = CFSTR("港股");
    goto LABEL_13;
  }
  v94[0] = CFSTR("UHKB");
  v94[1] = CFSTR("UHKW");
  v24 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v94, 2LL);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v80 = (unsigned __int8)_objc_msgSend(v25, "containsObject:", v26);
  if ( v80 )
    goto LABEL_12;
  v93 = CFSTR("UIFF");
  v28 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v93, 1LL);
  v29 = objc_retainAutoreleasedReturnValue(v28);
  v31 = (unsigned __int8)_objc_msgSend(v29, "containsObject:", v30);
  if ( v31 )
  {
    v8 = CFSTR("股指期货");
  }
  else
  {
    v92[0] = CFSTR("UFII");
    v92[1] = CFSTR("UCMI");
    v32 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v92, 2LL);
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v35 = (unsigned __int8)_objc_msgSend(v33, "containsObject:", v34);
    if ( v35 )
    {
      v8 = CFSTR("国外指数");
    }
    else
    {
      v91[0] = CFSTR("UNQS");
      v91[1] = CFSTR("UNYN");
      v91[2] = CFSTR("UNQQ");
      v91[3] = CFSTR("UNYA");
      v91[4] = CFSTR("UUSD");
      v91[5] = CFSTR("UUSA");
      v36 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v91, 6LL);
      v37 = objc_retainAutoreleasedReturnValue(v36);
      v39 = (unsigned __int8)_objc_msgSend(v37, "containsObject:", v38);
      if ( v39 )
      {
        v8 = CFSTR("美股");
      }
      else
      {
        v90[0] = CFSTR("USHJ");
        v90[1] = CFSTR("USZJ");
        v40 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v90, 2LL);
        v41 = objc_retainAutoreleasedReturnValue(v40);
        v43 = (unsigned __int8)_objc_msgSend(v41, "containsObject:", v42);
        if ( v43 )
        {
          v8 = CFSTR("基金");
        }
        else
        {
          v89[0] = CFSTR("UFXB");
          v89[1] = CFSTR("UFXC");
          v89[2] = CFSTR("UFXR");
          v44 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v89, 3LL);
          v45 = objc_retainAutoreleasedReturnValue(v44);
          v47 = (unsigned __int8)_objc_msgSend(v45, "containsObject:", v46);
          if ( v47 )
          {
            v8 = CFSTR("外汇");
          }
          else
          {
            v88[0] = CFSTR("USHB");
            v88[1] = CFSTR("USZB");
            v48 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v88, 2LL);
            v49 = objc_retainAutoreleasedReturnValue(v48);
            v51 = (unsigned __int8)_objc_msgSend(v49, "containsObject:", v50);
            if ( v51 )
            {
              v8 = CFSTR("沪深B股");
            }
            else
            {
              v87[0] = CFSTR("UCMN");
              v87[1] = CFSTR("UCMS");
              v87[2] = CFSTR("UFIS");
              v52 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v87, 3LL);
              v53 = objc_retainAutoreleasedReturnValue(v52);
              v55 = (unsigned __int8)_objc_msgSend(v53, "containsObject:", v54);
              if ( v55 )
              {
                v8 = CFSTR("国外期货");
              }
              else
              {
                v86[0] = CFSTR("UCFS");
                v86[1] = CFSTR("UCFD");
                v86[2] = CFSTR("UCFZ");
                v56 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v86, 3LL);
                v57 = objc_retainAutoreleasedReturnValue(v56);
                v59 = (unsigned __int8)_objc_msgSend(v57, "containsObject:", v58);
                if ( v59 )
                {
                  v8 = CFSTR("国内期货");
                }
                else
                {
                  v85[0] = CFSTR("USTA");
                  v85[1] = CFSTR("USTB");
                  v85[2] = CFSTR("USTT");
                  v60 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v85, 3LL);
                  v61 = objc_retainAutoreleasedReturnValue(v60);
                  v63 = (unsigned __int8)_objc_msgSend(v61, "containsObject:", v62);
                  if ( v63 )
                  {
                    v8 = CFSTR("股转");
                  }
                  else
                  {
                    v84 = CFSTR("USTI");
                    v64 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v84, 1LL);
                    v65 = objc_retainAutoreleasedReturnValue(v64);
                    v67 = (unsigned __int8)_objc_msgSend(v65, "containsObject:", v66);
                    if ( v67 )
                    {
                      v8 = CFSTR("股转指数");
                    }
                    else
                    {
                      v83[0] = CFSTR("USZD");
                      v83[1] = CFSTR("USHD");
                      v68 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v83, 2LL);
                      v69 = objc_retainAutoreleasedReturnValue(v68);
                      v71 = (unsigned __int8)_objc_msgSend(v69, "containsObject:", v70);
                      if ( v71 )
                      {
                        v8 = CFSTR("沪深债券");
                      }
                      else
                      {
                        v82[0] = CFSTR("USOO");
                        v82[1] = CFSTR("UZOO");
                        v82[2] = CFSTR("UCFT");
                        v82[3] = CFSTR("UCFL");
                        v82[4] = CFSTR("UCFX");
                        v82[5] = CFSTR("UIFB");
                        v72 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v82, 6LL);
                        v73 = objc_retainAutoreleasedReturnValue(v72);
                        v75 = (unsigned __int8)_objc_msgSend(v73, "containsObject:", v74);
                        if ( v75 )
                        {
                          v8 = CFSTR("期权");
                        }
                        else
                        {
                          v81[0] = CFSTR("USHC");
                          v81[1] = CFSTR("USZC");
                          v76 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v81, 2LL);
                          v77 = objc_retainAutoreleasedReturnValue(v76);
                          v79 = (unsigned __int8)_objc_msgSend(v77, "containsObject:", v78);
                          v8 = CFSTR("CDR");
                          if ( !v79 )
                            v8 = CFSTR("未知");
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
LABEL_13:
  return v8;
}

//----- (0000000100B80E3E) ----------------------------------------------------
id __cdecl +[HXTools getMarketShortChineseNameWithMarket:](id a1, SEL a2, id a3)
{
  __CFString *v4; // r13
  NSArray *v6; // rax
  NSArray *v7; // r15
  NSArray *v9; // rax
  NSArray *v10; // r15
  NSArray *v12; // rax
  NSArray *v13; // r15
  NSArray *v15; // rax
  NSArray *v16; // r15
  NSArray *v18; // rax
  NSArray *v19; // r15
  NSArray *v21; // rax
  NSArray *v22; // r15
  NSArray *v24; // rax
  NSArray *v25; // r15
  NSArray *v27; // rax
  NSArray *v28; // r15
  NSArray *v30; // rax
  NSArray *v31; // r15
  NSArray *v33; // rax
  NSArray *v34; // r15
  NSArray *v36; // rax
  NSArray *v37; // r15
  NSArray *v39; // rax
  NSArray *v40; // r15
  _QWORD v44[2]; // [rsp+0h] [rbp-1A0h] BYREF
  _QWORD v45[2]; // [rsp+10h] [rbp-190h] BYREF
  __CFString *v46; // [rsp+20h] [rbp-180h] BYREF
  _QWORD v47[3]; // [rsp+28h] [rbp-178h] BYREF
  _QWORD v48[3]; // [rsp+40h] [rbp-160h] BYREF
  _QWORD v49[6]; // [rsp+58h] [rbp-148h] BYREF
  _QWORD v50[11]; // [rsp+88h] [rbp-118h] BYREF
  _QWORD v51[6]; // [rsp+E0h] [rbp-C0h] BYREF
  _QWORD v52[3]; // [rsp+110h] [rbp-90h] BYREF
  _QWORD v53[2]; // [rsp+128h] [rbp-78h] BYREF
  _QWORD v54[3]; // [rsp+138h] [rbp-68h] BYREF
  _QWORD v55[4]; // [rsp+150h] [rbp-50h] BYREF

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") == (id)4 )
  {
    if ( (unsigned __int8)_objc_msgSend(CFSTR("URFA"), "isEqualToString:", v3) )
    {
      v4 = CFSTR("三级");
    }
    else
    {
      v55[0] = CFSTR("USHA");
      v55[1] = CFSTR("USHB");
      v55[2] = CFSTR("USHT");
      v55[3] = CFSTR("USHP");
      v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v55, 4LL);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      _objc_msgSend(v7, "containsObject:", v3);
      if ( v8 )
      {
        v4 = CFSTR("沪市");
      }
      else
      {
        v54[0] = CFSTR("USZA");
        v54[1] = CFSTR("USZB");
        v54[2] = CFSTR("USZP");
        v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v54, 3LL);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        _objc_msgSend(v10, "containsObject:", v3);
        if ( v11 )
        {
          v4 = CFSTR("深市");
        }
        else
        {
          v53[0] = CFSTR("URFI");
          v53[1] = CFSTR("URFA");
          v12 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v53, 2LL);
          v13 = objc_retainAutoreleasedReturnValue(v12);
          _objc_msgSend(v13, "containsObject:", v3);
          if ( v14 )
          {
            v4 = CFSTR("指数");
          }
          else
          {
            v52[0] = CFSTR("UHKM");
            v52[1] = CFSTR("UHKG");
            v52[2] = CFSTR("USZH");
            v15 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v52, 3LL);
            v16 = objc_retainAutoreleasedReturnValue(v15);
            _objc_msgSend(v16, "containsObject:", v3);
            v4 = CFSTR("港股");
            if ( !v17 )
            {
              v51[0] = CFSTR("UNQS");
              v51[1] = CFSTR("UNYN");
              v51[2] = CFSTR("UNQQ");
              v51[3] = CFSTR("UNYA");
              v51[4] = CFSTR("UUSD");
              v51[5] = CFSTR("UUSA");
              v18 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v51, 6LL);
              v19 = objc_retainAutoreleasedReturnValue(v18);
              _objc_msgSend(v19, "containsObject:", v3);
              if ( v20 )
              {
                v4 = CFSTR("美股");
              }
              else
              {
                v50[0] = CFSTR("UCFS");
                v50[1] = CFSTR("UCFD");
                v50[2] = CFSTR("UCFZ");
                v50[3] = CFSTR("UCMN");
                v50[4] = CFSTR("UIFF");
                v50[5] = CFSTR("UCMS");
                v50[6] = CFSTR("UFIS");
                v50[7] = CFSTR("UHFF");
                v50[8] = CFSTR("UHFS");
                v50[9] = CFSTR("UHFR");
                v50[10] = CFSTR("UHFZ");
                v21 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v50, 11LL);
                v22 = objc_retainAutoreleasedReturnValue(v21);
                _objc_msgSend(v22, "containsObject:", v3);
                if ( v23 )
                {
                  v4 = CFSTR("期货");
                }
                else
                {
                  v49[0] = CFSTR("USOO");
                  v49[1] = CFSTR("UZOO");
                  v49[2] = CFSTR("UCFT");
                  v49[3] = CFSTR("UCFL");
                  v49[4] = CFSTR("UCFX");
                  v49[5] = CFSTR("UIFB");
                  v24 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v49, 6LL);
                  v25 = objc_retainAutoreleasedReturnValue(v24);
                  _objc_msgSend(v25, "containsObject:", v3);
                  if ( v26 )
                  {
                    v4 = CFSTR("期权");
                  }
                  else
                  {
                    v48[0] = CFSTR("UFXB");
                    v48[1] = CFSTR("UFXC");
                    v48[2] = CFSTR("UFXR");
                    v27 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v48, 3LL);
                    v28 = objc_retainAutoreleasedReturnValue(v27);
                    _objc_msgSend(v28, "containsObject:", v3);
                    if ( v29 )
                    {
                      v4 = CFSTR("外汇");
                    }
                    else
                    {
                      v47[0] = CFSTR("USTA");
                      v47[1] = CFSTR("USTB");
                      v47[2] = CFSTR("USTT");
                      v30 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v47, 3LL);
                      v31 = objc_retainAutoreleasedReturnValue(v30);
                      _objc_msgSend(v31, "containsObject:", v3);
                      if ( v32 )
                      {
                        v4 = CFSTR("股转");
                      }
                      else
                      {
                        v46 = CFSTR("USTM");
                        v33 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v46, 1LL);
                        v34 = objc_retainAutoreleasedReturnValue(v33);
                        _objc_msgSend(v34, "containsObject:", v3);
                        if ( v35 )
                        {
                          v4 = CFSTR("京市");
                        }
                        else
                        {
                          v45[0] = CFSTR("UEUA");
                          v45[1] = CFSTR("UEUB");
                          v36 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v45, 2LL);
                          v37 = objc_retainAutoreleasedReturnValue(v36);
                          _objc_msgSend(v37, "containsObject:", v3);
                          if ( v38 )
                          {
                            v4 = CFSTR("英股");
                          }
                          else
                          {
                            v44[0] = CFSTR("UHKB");
                            v44[1] = CFSTR("UHKW");
                            v39 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v44, 2LL);
                            v40 = objc_retainAutoreleasedReturnValue(v39);
                            _objc_msgSend(v40, "containsObject:", v3);
                            if ( !v41 )
                            {
                              v42 = _objc_msgSend(v3, "substringFromIndex:", 3LL);
                              v43 = objc_retainAutoreleasedReturnValue(v42);
                              if ( (unsigned __int8)_objc_msgSend(v43, "isEqualToString:", CFSTR("I")) )
                              {
                                v4 = CFSTR("指数");
                              }
                              else if ( (unsigned __int8)_objc_msgSend(v43, "isEqualToString:", CFSTR("J")) )
                              {
                                v4 = CFSTR("基金");
                              }
                              else
                              {
                                v4 = CFSTR("债券");
                                if ( !(unsigned __int8)_objc_msgSend(v43, "isEqualToString:", CFSTR("D")) )
                                  v4 = CFSTR("其它");
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  else
  {
    v4 = (__CFString *)objc_retain(v3);
  }
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100B8159D) ----------------------------------------------------
id __cdecl +[HXTools getDaPanZhiShu:stockCode:](id a1, SEL a2, id a3, id a4)
{
  __CFString *v7; // r13
  __CFString *v12; // rsi
  __CFString *v13; // rbx
  __CFString *v14; // r13
  NSDictionary *v17; // rax
  __CFString *v24; // r12
  __CFString *v26; // r12
  NSArray *v27; // rax
  NSArray *v28; // r13
  id (*v29)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  __CFString *v37; // [rsp+0h] [rbp-90h]
  _QWORD v39[3]; // [rsp+10h] [rbp-80h] BYREF
  _QWORD v40[3]; // [rsp+28h] [rbp-68h] BYREF
  _QWORD v41[2]; // [rsp+40h] [rbp-50h] BYREF
  _QWORD v42[2]; // [rsp+50h] [rbp-40h] BYREF

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( _objc_msgSend(v5, "length") && _objc_msgSend(v6, "length") )
  {
    if ( v6 )
    {
      v7 = &charsToLeaveEscaped;
      if ( (unsigned __int64)_objc_msgSend(v6, "length") >= 2 )
      {
        v8 = _objc_msgSend(v6, "substringToIndex:", 2LL);
        objc_retainAutoreleasedReturnValue(v8);
      }
      if ( (unsigned __int64)_objc_msgSend(v6, "length") >= 3 )
      {
        v9 = _objc_msgSend(v6, "substringToIndex:", 3LL);
        v7 = (__CFString *)objc_retainAutoreleasedReturnValue(v9);
      }
    }
    else
    {
      v7 = &charsToLeaveEscaped;
    }
    v10 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("300"));
    v12 = CFSTR("USZI");
    v38 = v11;
    v37 = v7;
    if ( v10 )
    {
      v13 = CFSTR("创业板指");
      v14 = CFSTR("399006");
LABEL_20:
      v39[0] = CFSTR("Market");
      v40[0] = v12;
      v39[1] = CFSTR("StockCode");
      v40[1] = v14;
      v39[2] = CFSTR("StockName");
      v40[2] = v13;
      v17 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v39, 3LL);
      objc_retainAutoreleasedReturnValue(v17);
LABEL_21:
      goto LABEL_22;
    }
    if ( (unsigned __int8)_objc_msgSend(v11, "isEqualToString:", CFSTR("IF")) )
    {
      v13 = CFSTR("沪深300");
      v14 = CFSTR("399300");
LABEL_14:
      v12 = CFSTR("USZI");
      goto LABEL_20;
    }
    v12 = CFSTR("USHI");
    if ( (unsigned __int8)_objc_msgSend(v15, "isEqualToString:", CFSTR("IH")) )
    {
      v13 = CFSTR("上证50");
      v14 = CFSTR("1B0016");
      goto LABEL_20;
    }
    if ( (unsigned __int8)_objc_msgSend(v16, "isEqualToString:", CFSTR("IC")) )
    {
      v13 = CFSTR("中证500");
      v14 = CFSTR("1B0905");
LABEL_19:
      v12 = CFSTR("USHI");
      goto LABEL_20;
    }
    v20 = (unsigned __int8)_objc_msgSend(v7, "isEqualToString:", CFSTR("002"));
    v13 = CFSTR("深证成指");
    v14 = CFSTR("399001");
    if ( v20 )
      goto LABEL_14;
    if ( (unsigned __int8)+[HXTools isKeChuangBanStock:market:](
                            &OBJC_CLASS___HXTools,
                            "isKeChuangBanStock:market:",
                            v6,
                            v5) )
    {
      v13 = CFSTR("科创50");
      v14 = CFSTR("1B0688");
      goto LABEL_19;
    }
    if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHA")) )
    {
      v14 = CFSTR("1A0001");
      v12 = CFSTR("USHI");
      v13 = CFSTR("上证指数");
      goto LABEL_20;
    }
    if ( (unsigned __int8)_objc_msgSend(v5, v21, CFSTR("USHI"), CFSTR("上证指数"))
      || (unsigned __int8)_objc_msgSend(v5, v22, CFSTR("USHT"))
      || (unsigned __int8)_objc_msgSend(v5, v23, CFSTR("USHJ")) )
    {
      v14 = CFSTR("1A0001");
      v12 = CFSTR("USHI");
      v13 = CFSTR("上证指数");
      goto LABEL_20;
    }
    if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZA")) )
      goto LABEL_14;
    if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:")
      || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZJ")) )
    {
LABEL_35:
      v12 = v24;
      goto LABEL_20;
    }
    if ( (unsigned __int8)+[HXTools isMeiGuMarket:](&OBJC_CLASS___HXTools, "isMeiGuMarket:", v5) )
    {
      v13 = CFSTR("道琼斯工业平均指数");
      v14 = CFSTR("DJI");
    }
    else
    {
      if ( (unsigned __int8)+[HXTools isHongKongMarket:](&OBJC_CLASS___HXTools, "isHongKongMarket:", v5) )
      {
        v13 = CFSTR("恒生指数");
        v12 = CFSTR("UHKI");
        v14 = CFSTR("HSI");
        goto LABEL_20;
      }
      if ( (unsigned __int8)+[HXTools isWaiHuiMarket:](&OBJC_CLASS___HXTools, "isWaiHuiMarket:", v5) )
      {
        v13 = CFSTR("美元指数");
        v12 = CFSTR("UFXB");
        v14 = CFSTR("USDIND");
        goto LABEL_20;
      }
      if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USHB")) )
      {
        v13 = CFSTR("B股指数");
        v14 = CFSTR("1A0003");
        goto LABEL_19;
      }
      if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USZB")) )
      {
        v13 = CFSTR("成份B指");
        v14 = CFSTR("399003");
        goto LABEL_35;
      }
      v13 = CFSTR("三板做市");
      v14 = CFSTR("899002");
      if ( (unsigned __int8)_objc_msgSend(v5, "isEqualToString:")
        || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USTA"))
        || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USTB")) )
      {
        goto LABEL_35;
      }
      v25 = (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", CFSTR("USTT"));
      v12 = v26;
      if ( v25 )
        goto LABEL_20;
      v42[0] = CFSTR("USZD");
      v42[1] = CFSTR("USHD");
      v27 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v42, 2LL);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v30 = (unsigned __int8)v29(v28, "containsObject:", v5);
      if ( v30 )
      {
        v32 = (unsigned __int8)+[HXTools isQiYeBondWithstockCodeSubStr:](
                                 &OBJC_CLASS___HXTools,
                                 "isQiYeBondWithstockCodeSubStr:",
                                 v38);
        v14 = CFSTR("000013");
        if ( !v32 )
          v14 = CFSTR("1B0012");
        v12 = CFSTR("UZZI");
        if ( !v32 )
          v12 = CFSTR("USHI");
        v13 = CFSTR("企债指数");
        if ( !v32 )
          v13 = CFSTR("国债指数");
        goto LABEL_20;
      }
      v41[0] = CFSTR("UEUA");
      v41[1] = CFSTR("UEUB");
      v33 = v31(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v41, 2LL);
      v34 = objc_retainAutoreleasedReturnValue(v33);
      v36 = (unsigned __int8)v35(v34, "containsObject:", v5);
      if ( !v36 )
        goto LABEL_21;
      v13 = CFSTR("伦敦金融时报100指数");
      v14 = CFSTR("FTSE");
    }
    v12 = CFSTR("UFII");
    goto LABEL_20;
  }
LABEL_22:
  return objc_autoreleaseReturnValue(v18);
}

//----- (0000000100B81C21) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools getPrecisionTypeWithMarket:Code:](id a1, SEL a2, id a3, id a4)
{
  unsigned __int64 v7; // r15
  NSArray *v9; // rax
  NSArray *v10; // rax
  NSArray *v13; // rax
  NSArray *v14; // rbx
  HXStaticTextManager *v16; // rax
  HXStaticTextManager *v17; // rbx
  _QWORD v24[6]; // [rsp+18h] [rbp-B8h] BYREF
  _QWORD v25[11]; // [rsp+48h] [rbp-88h] BYREF

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( !v5 || (unsigned __int8)_objc_msgSend(v5, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v7 = 0LL;
    goto LABEL_4;
  }
  v25[0] = CFSTR("UCFS");
  v25[1] = CFSTR("UCFD");
  v25[2] = CFSTR("UCFZ");
  v25[3] = CFSTR("UCMN");
  v25[4] = CFSTR("UIFF");
  v25[5] = CFSTR("UCMS");
  v25[6] = CFSTR("UFIS");
  v25[7] = CFSTR("UHFF");
  v25[8] = CFSTR("UHFS");
  v25[9] = CFSTR("UHFR");
  v25[10] = CFSTR("UHFZ");
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v25, 11LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  if ( !(unsigned __int8)_objc_msgSend(v10, "containsObject:", v5) )
  {
    v24[0] = CFSTR("USOO");
    v24[1] = CFSTR("UZOO");
    v24[2] = CFSTR("UCFT");
    v24[3] = CFSTR("UCFL");
    v24[4] = CFSTR("UCFX");
    v24[5] = CFSTR("UIFB");
    v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v24, 6LL);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    if ( !(unsigned __int8)_objc_msgSend(v14, "containsObject:", v5) )
    {
      goto LABEL_13;
    }
    v23 = _objc_msgSend(v6, "length");
    if ( !v23 )
      goto LABEL_13;
LABEL_10:
    v16 = (HXStaticTextManager *)+[HXStaticTextManager sharedInstance](
                                   &OBJC_CLASS___HXStaticTextManager,
                                   "sharedInstance");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18 = -[HXStaticTextManager getPriceDecimalWithMarket:Code:](v17, "getPriceDecimalWithMarket:Code:", v5, v6);
    objc_retainAutoreleasedReturnValue(v18);
    if ( v19 )
    {
      v7 = (int)_objc_msgSend(v19, "intValue");
      goto LABEL_4;
    }
    goto LABEL_13;
  }
  v11 = _objc_msgSend(v6, "length");
  if ( v11 )
    goto LABEL_10;
LABEL_13:
  v22 = (char *)_objc_msgSend(a1, "getStockTypeWithMarket:", v5);
  v7 = 2LL;
  if ( (unsigned __int64)(v22 - 5) <= 0x13 )
    v7 = qword_1010DE660[(_QWORD)v22 - 5];
LABEL_4:
  return v7;
}

//----- (0000000100B81F20) ----------------------------------------------------
double __cdecl +[HXTools getMinDealValue:](id a1, SEL a2, unsigned __int64 a3)
{
  if ( a3 > 4 )
    return 0.01;
  else
    return dbl_1010DE700[a3];
}

//----- (0000000100B81F42) ----------------------------------------------------
double __cdecl +[HXTools getVolumeUintWithMarket:](id a1, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = v3;
  v7 = 0x4059000000000000LL;
  if ( v3 )
  {
    v5 = +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", v3);
    if ( (unsigned __int64)(v5 - 4) <= 0x14 )
      v7 = qword_1010DE728[(_QWORD)v5 - 4];
  }
  return *(double *)&v7;
}

//----- (0000000100B81FAE) ----------------------------------------------------
char __cdecl +[HXTools isHuShenMarketOpenTime](id a1, SEL a2)
{
  NSDate *v6; // rax

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getServerTime");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "doubleValue");
  v6 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  objc_retainAutoreleasedReturnValue(v6);
  v7 = +[HXTools getDateFormatter](&OBJC_CLASS___HXTools, "getDateFormatter");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "setDateFormat:", CFSTR("HHmm"));
  v10 = _objc_msgSend(v8, "stringFromDate:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (unsigned __int8)_objc_msgSend(a1, "isHuShenMarketOpenTime:", v11);
  return v12;
}

//----- (0000000100B820BD) ----------------------------------------------------
char __cdecl +[HXTools currentTimeIsOpen:stockCode:](id a1, SEL a2, id a3, id a4)
{

  v15 = objc_retain(a4);
  objc_retain(a3);
  v5 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "getServerTime");
  v16 = objc_retainAutoreleasedReturnValue(v7);
  v8 = _objc_msgSend(v16, "stringValue");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = v10;
  v12 = (__int64)_objc_msgSend(a1, "isOpenTime:market:stockCode:", v9, v10, v15);
  return v12;
}

//----- (0000000100B821A7) ----------------------------------------------------
char __cdecl +[HXTools isOpenTime:market:stockCode:](id a1, SEL a2, id a3, id a4, id a5)
{
  FenShiAccessoryManager *v9; // rax
  FenShiAccessoryManager *v10; // r14
  int v14; // r12d
  unsigned __int64 v15; // r12
  unsigned __int64 v16; // r13

  v7 = objc_retain(a3);
  objc_retain(a4);
  v8 = objc_retain(a5);
  v9 = +[FenShiAccessoryManager sharedInstance](&OBJC_CLASS___FenShiAccessoryManager, "sharedInstance");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = -[FenShiAccessoryManager getTimeAxis:stockCode:](v10, "getTimeAxis:stockCode:", v11, v8);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  if ( (__int64)_objc_msgSend(v13, "count") )
  {
    _objc_msgSend(v7, "intValue");
    if ( (__int64)_objc_msgSend(v13, "count") )
    {
      v29 = v7;
      v28 = (double)v14;
      v15 = 0LL;
      while ( 1 )
      {
        v16 = v15 + 1;
        if ( (__int64)_objc_msgSend(v13, "count") > v15 + 1 )
        {
          v18 = _objc_msgSend(v13, "objectAtIndexedSubscript:", v17);
          v26 = objc_retainAutoreleasedReturnValue(v18);
          v19 = _objc_msgSend(v13, "objectAtIndexedSubscript:", v16);
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v25 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v26, "timeIntervalSince1970");
          v27 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v20, "timeIntervalSince1970");
          if ( v28 >= v25 && v27 >= v28 )
            break;
        }
        v21 = _objc_msgSend(v13, "count");
        if ( (unsigned __int64)v21 <= v15 )
        {
          v22 = 0;
          goto LABEL_12;
        }
      }
      v22 = 1;
LABEL_12:
      v7 = v29;
    }
    else
    {
      v22 = 0;
    }
  }
  else
  {
    v22 = 0;
  }
  return v22;
}

//----- (0000000100B82399) ----------------------------------------------------
char __cdecl +[HXTools isHuShenMarketOpenTime:](id a1, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  _objc_msgSend(v4, "doubleValue");
  if ( v3 < 930.0 || (_objc_msgSend(v4, "doubleValue"), v5 = 1, v3 > 1130.0) )
  {
    _objc_msgSend(v4, "doubleValue");
    if ( v3 < 1300.0 || (_objc_msgSend(v4, "doubleValue"), v5 = 1, v3 > 1500.0) )
      v5 = 0;
  }
  return v5;
}

//----- (0000000100B82437) ----------------------------------------------------
id __cdecl +[HXTools convertTradeTimeRangesToReadableFormat:](id a1, SEL a2, id a3)
{
  NSArray *v3; // r15
  NSArray *v5; // rbx
  unsigned __int64 v21; // r12
  NSArray *v30; // rax
  NSArray *v31; // r14
  NSArray *v34; // rdi
  SEL v42; // [rsp+48h] [rbp-F8h]
  SEL v43; // [rsp+50h] [rbp-F0h]
  SEL v44; // [rsp+58h] [rbp-E8h]
  SEL v45; // [rsp+60h] [rbp-E0h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = (NSArray *)objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  v5 = 0LL;
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && v3 )
  {
    if ( _objc_msgSend(v3, "count") )
    {
      v6 = _objc_msgSend(v3, "objectAtIndexedSubscript:", 0LL);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      v8 = _objc_msgSend(v7, "integerValue");
      v10 = _objc_msgSend(v9, "numberWithInteger:", v8);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = v7;
      v14 = v13;
      v47 = v11;
      v16 = (void *)v15(v11, "stringValue");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18(v17, "containsString:", CFSTR("12"));
      if ( v19 )
      {
        v20 = (void *)v14(&OBJC_CLASS___NSMutableArray, "array");
        v49 = objc_retainAutoreleasedReturnValue(v20);
        v38 = 0LL;
        v39 = 0LL;
        v40 = 0LL;
        v41 = 0LL;
        obj = objc_retain(v3);
        v48 = (id)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v14)(
                    obj,
                    "countByEnumeratingWithState:objects:count:",
                    &v38,
                    v51,
                    16LL);
        if ( v48 )
        {
          v46 = *(_QWORD *)v39;
          do
          {
            v42 = "longLongValue";
            v43 = "numberWithLongLong:";
            v44 = "get_yyMMddHHmm_DateFromHXTime:FormatStr:";
            v45 = "addObject:";
            v21 = 0LL;
            do
            {
              if ( *(_QWORD *)v39 != v46 )
                objc_enumerationMutation(obj);
              v22 = _objc_msgSend(*(id *)(*((_QWORD *)&v38 + 1) + 8 * v21), v42);
              v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, v43, v22);
              v24 = objc_retainAutoreleasedReturnValue(v23);
              v25 = _objc_msgSend(&OBJC_CLASS___HXTools, v44, v24, CFSTR("%04d%02d%02d%02d%02d"));
              v26 = objc_retainAutoreleasedReturnValue(v25);
              _objc_msgSend(v49, v45, v26);
              v21 = v27 + 1;
            }
            while ( v21 < (unsigned __int64)v48 );
            v48 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v38, v51, 16LL);
          }
          while ( v48 );
        }
        v28 = obj;
        v29 = v49;
        v30 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v49);
        v31 = objc_retainAutoreleasedReturnValue(v30);
        v32(v28);
        v3 = objc_retain(v31);
        v33(v29);
        v34 = v3;
      }
      else
      {
        v34 = 0LL;
      }
      v5 = objc_retain(v34);
      v35(v47);
      v36(v5);
    }
    else
    {
      v5 = 0LL;
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B827CF) ----------------------------------------------------
id __cdecl +[HXTools getOpenTimeWithTradeTimeRanges:](id a1, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  v5 = 0LL;
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && v3 )
  {
    if ( _objc_msgSend(v3, "count") )
    {
      v6 = +[HXTools convertTradeTimeRangesToReadableFormat:](
             &OBJC_CLASS___HXTools,
             "convertTradeTimeRangesToReadableFormat:",
             v3);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      v8 = v7;
      if ( v7 && _objc_msgSend(v7, "count") )
      {
        v9 = _objc_msgSend(v8, "objectAtIndexedSubscript:", 0LL);
        v5 = objc_retainAutoreleasedReturnValue(v9);
      }
      else
      {
        v5 = 0LL;
      }
    }
    else
    {
      v5 = 0LL;
    }
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B828B3) ----------------------------------------------------
void __cdecl +[HXTools newestTradeDate:market:callBack:](id a1, SEL a2, id a3, id a4, id a5)
{
  FenShiAccessoryManager *v8; // rax
  FenShiAccessoryManager *v9; // r15
  NSDictionary *v18; // rax
  DataRequestCenter *v19; // rax
  _QWORD v29[5]; // [rsp+10h] [rbp-D0h] BYREF
  FenShiAccessoryManager *v31; // [rsp+40h] [rbp-A0h]

  v44 = a1;
  objc_retain(a3);
  v7 = objc_retain(a4);
  v43 = objc_retain(a5);
  v8 = +[FenShiAccessoryManager sharedInstance](&OBJC_CLASS___FenShiAccessoryManager, "sharedInstance");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = -[FenShiAccessoryManager getFenShiAccessoryInfoDict:withCode:](
          v9,
          "getFenShiAccessoryInfoDict:withCode:",
          v7,
          v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v38 = v12;
  if ( v12 )
  {
    v14 = _objc_msgSend(v12, "thsStringForKey:", CFSTR("tradedate"));
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = v43;
    v17 = v15;
    if ( v43 )
      (*((void (__fastcall **)(id, id))v43 + 2))(v43, v15);
  }
  else
  {
    v18 = _objc_msgSend(
            &OBJC_CLASS___NSDictionary,
            "dictionaryWithObjectsAndKeys:",
            v13,
            CFSTR("CodeList"),
            v7,
            CFSTR("Market"),
            0LL);
    v42 = objc_retainAutoreleasedReturnValue(v18);
    v19 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
    v39 = objc_retainAutoreleasedReturnValue(v19);
    v29[0] = _NSConcreteStackBlock;
    v29[1] = 3254779904LL;
    v29[2] = sub_100B82B17;
    v29[3] = &unk_1012E98C8;
    v29[4] = v44;
    v20 = objc_retain(v43);
    v40 = v7;
    v41 = v20;
    v30 = v20;
    v31 = objc_retain(v9);
    v32 = objc_retain(v7);
    v33 = objc_retain(v21);
    v44 = v22;
    v34 = _NSConcreteStackBlock;
    v35 = 3254779904LL;
    v36 = sub_100B82C66;
    v37 = &unk_1012DACE0;
    v23 = objc_retain(v41);
    *(_QWORD *)(v24 + 32) = v23;
    v25 = v39;
    _objc_msgSend(v39, "request:params:callBack:fail:", 209LL, v42, v29, v24);
    v26 = v25;
    v7 = v40;
    v16 = v43;
  }
}

//----- (0000000100B82B17) ----------------------------------------------------
void __fastcall sub_100B82B17(__int64 a1, void *a2)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12

  v3 = objc_retain(a2);
  v4 = _objc_msgSend(&OBJC_CLASS___PCTXTDataModel, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v7 = v6(*(id *)(a1 + 32), "dealInfoRequestDetailData:", v3);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(v8, "thsStringForKey:", CFSTR("tradedate"));
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = *(_QWORD *)(a1 + 40);
    if ( v12 )
      (*(void (__fastcall **)(__int64, id))(v12 + 16))(v12, v11);
    _objc_msgSend(
      *(id *)(a1 + 48),
      "saveFenShiAccessoryInfoDict:withMarket:andCode:",
      v8,
      *(_QWORD *)(a1 + 56),
      *(_QWORD *)(a1 + 64));
  }
  else
  {
    v14 = *(_QWORD *)(a1 + 40);
    if ( v14 )
      (*(void (__fastcall **)(__int64, _QWORD))(v14 + 16))(v14, 0LL);
  }
}

//----- (0000000100B82BF5) ----------------------------------------------------
id __fastcall sub_100B82BF5(__int64 a1, __int64 a2)
{
  _Block_object_assign((void *)(a1 + 40), *(const void **)(a2 + 40), 7);
  objc_retain(*(id *)(a2 + 48));
  objc_retain(*(id *)(a2 + 56));
  return objc_retain(*(id *)(a2 + 64));
}

//----- (0000000100B82C33) ----------------------------------------------------
void __fastcall sub_100B82C33(id *a1)
{
}

//----- (0000000100B82C66) ----------------------------------------------------
__int64 __fastcall sub_100B82C66(__int64 a1)
{

  v1 = *(_QWORD *)(a1 + 32);
  if ( v1 )
    return (*(__int64 (__fastcall **)(__int64, _QWORD))(v1 + 16))(v1, 0LL);
  return result;
}

//----- (0000000100B82C7E) ----------------------------------------------------
id __cdecl +[HXTools dealInfoRequestDetailData:](id a1, SEL a2, id a3)
{
  SEL v9; // r12
  unsigned __int64 v14; // rax
  id (**v18)(id, SEL, ...); // r13
  unsigned __int64 v40; // r14
  id (**v42)(id, SEL, ...); // r12
  unsigned __int64 i; // r13
  unsigned __int64 v100; // [rsp+C0h] [rbp-190h]
  id obj; // [rsp+100h] [rbp-150h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___PCTXTDataModel, "class");
  v5 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4);
  v109 = 0LL;
  if ( v3 && v5 )
  {
    v6 = _objc_msgSend(v3, "txtData");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(&OBJC_CLASS___NSData, "class");
    v10 = (unsigned __int8)_objc_msgSend(v7, v9, v8);
    if ( v10 )
    {
      v11 = objc_alloc(&OBJC_CLASS___NSString);
      v98 = v3;
      v12 = _objc_msgSend(v3, "txtData");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = CFStringConvertEncodingToNSStringEncoding(0x632u);
      v15 = v13;
      v16 = _objc_msgSend(v11, "initWithData:encoding:", v13, v14);
      v17 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
      v109 = objc_retainAutoreleasedReturnValue(v17);
      if ( v16 )
      {
        v18 = &_objc_msgSend;
        if ( _objc_msgSend(v16, "length") )
        {
          v99 = v16;
          v19 = _objc_msgSend(v16, "componentsSeparatedByString:", CFSTR("\n"));
          v20 = objc_retainAutoreleasedReturnValue(v19);
          v85 = 0LL;
          v86 = 0LL;
          v87 = 0LL;
          v88 = 0LL;
          v100 = (__int64)_objc_msgSend(v20, "countByEnumeratingWithState:objects:count:", &v85, v113, 16LL);
          if ( v100 )
          {
            v94 = *(_QWORD *)v86;
            v104 = v20;
            do
            {
              v101 = "count";
              v111 = "objectAtIndexedSubscript:";
              v21 = "isEqualToString:";
              v95 = "array";
              v102 = "numberWithInt:";
              v106 = "setObject:forKey:";
              v97 = "thsStringAtIndex:";
              v22 = 0LL;
              v110 = "isEqualToString:";
              do
              {
                if ( *(_QWORD *)v86 != v94 )
                  objc_enumerationMutation(v20);
                v93 = v22;
                v23 = *(_QWORD *)(*((_QWORD *)&v85 + 1) + 8 * v22);
                if ( v23 && ((__int64 (__fastcall *)(__int64, const char *))v18)(v23, "length") )
                {
                  v24 = (void *)((__int64 (__fastcall *)(__int64, const char *, __CFString *))v18)(
                                  v23,
                                  "componentsSeparatedByString:",
                                  CFSTR("="));
                  v25 = objc_retainAutoreleasedReturnValue(v24);
                  if ( ((__int64 (__fastcall *)(id, const char *))v18)(v25, v101) == 2 )
                  {
                    v107 = v26;
                    v27 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(v26, v111, 1LL);
                    v28 = objc_retainAutoreleasedReturnValue(v27);
                    v29 = v28;
                    if ( v28 )
                    {
                      if ( ((__int64 (__fastcall *)(id, const char *))v18)(v28, "length") )
                      {
                        v30 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v18)(v107, v111, 0LL);
                        v31 = objc_retainAutoreleasedReturnValue(v30);
                        if ( v31 )
                        {
                          v32 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v18)(v107, v111, 0LL);
                          v33 = objc_retainAutoreleasedReturnValue(v32);
                          v34 = ((__int64 (__fastcall *)(id, const char *, __CFString *))v18)(
                                  v33,
                                  v21,
                                  CFSTR("timeindex"));
                          if ( v34 )
                          {
                            v96 = (void *)v35;
                            v36 = (void *)((__int64 (__fastcall *)(__int64, const char *, __CFString *))v18)(
                                            v35,
                                            "componentsSeparatedByString:",
                                            CFSTR(","));
                            v37 = objc_retainAutoreleasedReturnValue(v36);
                            v38 = (void *)((__int64 (__fastcall *)(objc_class *, const char *))v18)(
                                            &OBJC_CLASS___NSMutableArray,
                                            v95);
                            v103 = objc_retainAutoreleasedReturnValue(v38);
                            v89 = 0LL;
                            v90 = 0LL;
                            v91 = 0LL;
                            v92 = 0LL;
                            obj = objc_retain(v37);
                            v39 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v18)(
                                    obj,
                                    "countByEnumeratingWithState:objects:count:",
                                    &v89,
                                    v112,
                                    16LL);
                            if ( v39 )
                            {
                              v40 = v39;
                              v41 = *(_QWORD *)v90;
                              do
                              {
                                v42 = v18;
                                v105 = "addObject:";
                                for ( i = 0LL; i < v40; ++i )
                                {
                                  if ( *(_QWORD *)v90 != v41 )
                                    objc_enumerationMutation(obj);
                                  v44 = *(_QWORD *)(*((_QWORD *)&v89 + 1) + 8 * i);
                                  if ( v44
                                    && !((unsigned __int8 (__fastcall *)(_QWORD, const char *, __CFString *))v42)(
                                          *(_QWORD *)(*((_QWORD *)&v89 + 1) + 8 * i),
                                          v110,
                                          &charsToLeaveEscaped) )
                                  {
                                    ((void (__fastcall *)(id, id, __int64))v42)(v103, v105, v44);
                                  }
                                }
                                v18 = v42;
                                v40 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v42)(
                                        obj,
                                        "countByEnumeratingWithState:objects:count:",
                                        &v89,
                                        v112,
                                        16LL);
                              }
                              while ( v40 );
                            }
                            v45 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v18)(
                                            &OBJC_CLASS___NSNumber,
                                            v102,
                                            32770LL);
                            v46 = objc_retainAutoreleasedReturnValue(v45);
                            v47 = v103;
                            ((void (__fastcall *)(id, const char *, id, id))v18)(v109, v106, v103, v46);
                            v20 = v104;
                            v21 = v110;
                            v29 = v96;
                          }
                          else
                          {
                            v49 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v18)(v107, v111, 0LL);
                            v50 = objc_retainAutoreleasedReturnValue(v49);
                            v21 = v110;
                            v51 = ((__int64 (__fastcall *)(id, const char *, __CFString *))v18)(
                                    v50,
                                    v110,
                                    CFSTR("markettimezone"));
                            if ( v51 )
                            {
                              v52 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                                              v107,
                                              v111,
                                              1LL);
                              v53 = objc_retainAutoreleasedReturnValue(v52);
                              v54 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v18)(
                                              &OBJC_CLASS___NSNumber,
                                              v102,
                                              32769LL);
                              v55 = objc_retainAutoreleasedReturnValue(v54);
                              ((void (__fastcall *)(id, const char *, id, id))v18)(v109, v106, v53, v55);
                              v20 = v104;
                            }
                            else
                            {
                              v56 = (void *)((__int64 (__fastcall *)(void *, const char *, _QWORD))v18)(v107, v111, 0LL);
                              v57 = objc_retainAutoreleasedReturnValue(v56);
                              v58 = ((__int64 (__fastcall *)(id, const char *, __CFString *))v18)(
                                      v57,
                                      v21,
                                      CFSTR("auctiontime"));
                              if ( v58 )
                              {
                                v60 = (void *)((__int64 (__fastcall *)(__int64, const char *, __CFString *))v18)(
                                                v59,
                                                "componentsSeparatedByString:",
                                                CFSTR(","));
                                v61 = objc_retainAutoreleasedReturnValue(v60);
                                v62 = v61;
                                if ( v61
                                  && (unsigned __int64)((__int64 (__fastcall *)(id, const char *))v18)(v61, v101) >= 2 )
                                {
                                  v63 = (void *)((__int64 (__fastcall *)(id, const char *, _QWORD))v18)(v62, v111, 0LL);
                                  v64 = objc_retainAutoreleasedReturnValue(v63);
                                  v65 = (void *)((__int64 (__fastcall *)(id, const char *, __int64))v18)(v62, v111, 1LL);
                                  obj = objc_retainAutoreleasedReturnValue(v65);
                                  v105 = v64;
                                  if ( v64 )
                                  {
                                    v66 = ((__int64 (__fastcall *)(id, const char *, __CFString *))v18)(
                                            v105,
                                            v110,
                                            &charsToLeaveEscaped);
                                    if ( obj )
                                    {
                                      if ( !v66
                                        && !((unsigned __int8 (__fastcall *)(id, const char *, __CFString *))v18)(
                                              obj,
                                              v110,
                                              &charsToLeaveEscaped) )
                                      {
                                        v67 = v109;
                                        v68 = v106;
                                        ((void (__fastcall *)(id, const char *, id, __CFString *))v18)(
                                          v109,
                                          v106,
                                          v105,
                                          CFSTR("start"));
                                        ((void (__fastcall *)(id, const char *, id, __CFString *))v18)(
                                          v67,
                                          v68,
                                          obj,
                                          CFSTR("end"));
                                      }
                                    }
                                  }
                                  v69 = v97;
                                  v70 = (void *)((__int64 (__fastcall *)(id, const char *, __int64))v18)(v62, v97, 4LL);
                                  v71 = v62;
                                  v72 = objc_retainAutoreleasedReturnValue(v70);
                                  v103 = v71;
                                  v73 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                                                  v71,
                                                  v69,
                                                  5LL);
                                  v74 = objc_retainAutoreleasedReturnValue(v73);
                                  if ( v72 )
                                  {
                                    v75 = ((__int64 (__fastcall *)(id, const char *, __CFString *))v18)(
                                            v72,
                                            v110,
                                            &charsToLeaveEscaped);
                                    if ( v74 )
                                    {
                                      if ( !v75
                                        && !((unsigned __int8 (__fastcall *)(id, const char *, __CFString *))v18)(
                                              v74,
                                              v110,
                                              &charsToLeaveEscaped) )
                                      {
                                        v76 = v109;
                                        ((void (__fastcall *)(id, const char *, id, __CFString *))v18)(
                                          v109,
                                          v106,
                                          v72,
                                          CFSTR("PHStart"));
                                        ((void (__fastcall *)(id, const char *, id, __CFString *))v18)(
                                          v76,
                                          v106,
                                          v74,
                                          CFSTR("PHEnd"));
                                      }
                                    }
                                  }
                                  v62 = v103;
                                }
                                v77 = v62;
                              }
                              else
                              {
                                v78 = (__int64)v107;
                                v79 = v111;
                                v80 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                                                v107,
                                                v111,
                                                1LL);
                                v81 = objc_retainAutoreleasedReturnValue(v80);
                                v82 = (void *)((__int64 (__fastcall *)(__int64, const char *, _QWORD))v18)(
                                                v78,
                                                v79,
                                                0LL);
                                v83 = objc_retainAutoreleasedReturnValue(v82);
                                ((void (__fastcall *)(id, const char *, id, id))v18)(v109, v106, v81, v83);
                                v77 = v81;
                              }
                              v20 = v104;
                              v21 = v110;
                            }
                          }
                        }
                      }
                    }
                    v26 = v107;
                  }
                }
                v22 = v93 + 1;
              }
              while ( v93 + 1 < v100 );
              v100 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v18)(
                       v20,
                       "countByEnumeratingWithState:objects:count:",
                       &v85,
                       v113,
                       16LL);
            }
            while ( v100 );
          }
          v16 = v99;
        }
      }
      v3 = v98;
    }
    else
    {
      v109 = 0LL;
    }
  }
  return objc_autoreleaseReturnValue(v109);
}

//----- (0000000100B835FB) ----------------------------------------------------
id __cdecl +[HXTools toHex:](id a1, SEL a2, unsigned __int64 a3)
{
  __CFString *v3; // r15
  int v4; // r13d
  NSString *v6; // rax
  __CFString *v9; // rbx
  bool v11; // cf
  unsigned __int64 v13; // [rsp+0h] [rbp-40h]

  v3 = &charsToLeaveEscaped;
  v4 = 8;
  v5 = 0LL;
  do
  {
    v13 = a3 >> 4;
    switch ( a3 & 0xF )
    {
      case 0xAuLL:
      case 0xBuLL:
      case 0xCuLL:
      case 0xDuLL:
      case 0xEuLL:
      case 0xFuLL:
        break;
      default:
        v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%u"));
        objc_retainAutoreleasedReturnValue(v6);
        break;
    }
    v8 = _objc_msgSend(v7, "stringByAppendingString:", v3);
    v9 = (__CFString *)objc_retainAutoreleasedReturnValue(v8);
    v11 = v4-- == 0;
    a3 = v13;
    if ( !v13 )
      break;
    v3 = v9;
    v5 = v10;
  }
  while ( !v11 );
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100B83734) ----------------------------------------------------
id __cdecl +[HXTools getDateFormatter](id a1, SEL a2)
{
  HXTools *v2; // rax
  HXTools *v3; // rbx
  NSDateFormatter *v4; // rax
  NSDateFormatter *v5; // r14

  v2 = +[HXTools sharedInstance](&OBJC_CLASS___HXTools, "sharedInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTools ShanghaiDateFormatter](v3, "ShanghaiDateFormatter");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B8378B) ----------------------------------------------------
id __cdecl +[HXTools getDateFormatterWithMarket:stockCode:](id a1, SEL a2, id a3, id a4)
{
  FenShiAccessoryManager *v6; // rax
  FenShiAccessoryManager *v7; // rbx
  NSDateFormatter *v12; // rbx
  HXTools *v13; // rax
  HXTools *v14; // r14
  NSDateFormatter *v15; // rax

  objc_retain(a4);
  v5 = objc_retain(a3);
  v6 = +[FenShiAccessoryManager sharedInstance](&OBJC_CLASS___FenShiAccessoryManager, "sharedInstance");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = -[FenShiAccessoryManager getDateFormatter:stockCode:](v7, "getDateFormatter:stockCode:", v5, v8);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  if ( v10 )
  {
    v12 = (NSDateFormatter *)objc_retain(v10);
  }
  else
  {
    v13 = +[HXTools sharedInstance](&OBJC_CLASS___HXTools, "sharedInstance");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = -[HXTools ShanghaiDateFormatter](v14, "ShanghaiDateFormatter");
    v12 = objc_retainAutoreleasedReturnValue(v15);
  }
  return objc_autoreleaseReturnValue(v12);
}

//----- (0000000100B83886) ----------------------------------------------------
NSDateFormatter *__cdecl -[HXTools ShanghaiDateFormatter](HXTools *self, SEL a2)
{
  NSDateFormatter *ShanghaiDateFormatter; // rdi
  NSDateFormatter *v5; // rax
  NSDateFormatter *v6; // rdi
  NSTimeZone *v7; // rax
  NSTimeZone *v8; // r14
  NSLocale *v10; // rax
  NSLocale *v11; // r14

  ShanghaiDateFormatter = self->_ShanghaiDateFormatter;
  if ( !ShanghaiDateFormatter )
  {
    v4 = objc_alloc(&OBJC_CLASS___NSDateFormatter);
    v5 = (NSDateFormatter *)_objc_msgSend(v4, "init");
    v6 = self->_ShanghaiDateFormatter;
    self->_ShanghaiDateFormatter = v5;
    v7 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, "timeZoneWithName:", CFSTR("Asia/Shanghai"));
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(self->_ShanghaiDateFormatter, "setTimeZone:", v8);
    v9(v8);
    v10 = _objc_msgSend(&OBJC_CLASS___NSLocale, "localeWithLocaleIdentifier:", CFSTR("zh_CN"));
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(self->_ShanghaiDateFormatter, "setLocale:", v11);
    v12(v11);
    ShanghaiDateFormatter = self->_ShanghaiDateFormatter;
  }
  return (NSDateFormatter *)objc_retainAutoreleaseReturnValue(ShanghaiDateFormatter);
}

//----- (0000000100B83958) ----------------------------------------------------
id __cdecl +[HXTools USDateWithCNDate:](id a1, SEL a2, id a3)
{
  NSDate *v3; // rax
  NSTimeZone *v4; // rax
  NSTimeZone *v5; // r15
  int v6; // eax
  SEL v9; // r12
  int v12; // eax
  NSDate *v19; // [rsp+0h] [rbp-30h]

  _objc_msgSend(a3, "doubleValue");
  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v19 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, "timeZoneWithName:", CFSTR("Asia/Shanghai"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = (unsigned int)_objc_msgSend(v5, "secondsFromGMT");
  v7 = _objc_msgSend(v19, "dateByAddingTimeInterval:", -(double)v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, v9, CFSTR("America/New_York"));
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (unsigned int)_objc_msgSend(v11, "secondsFromGMT");
  v13 = _objc_msgSend(v8, "dateByAddingTimeInterval:", (double)v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15(v8);
  v16(v11);
  v17(v19);
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100B83A7A) ----------------------------------------------------
id __cdecl +[HXTools getDateFromDoubleNumber:](id a1, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  bool v19; // zf
  __CFString *v21; // rdx

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
  v27 = objc_retainAutoreleasedReturnValue(v4);
  v5(v3, "doubleValue");
  v7 = v6(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = v9(&OBJC_CLASS___HXTools, "getDateFormatter");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12(v11, "setDateFormat:", CFSTR("yyyy-MM-dd"));
  v14 = v13(v11, "stringFromDate:", v8);
  v28 = objc_retainAutoreleasedReturnValue(v14);
  v16 = v15(v11, "stringFromDate:", v27);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = (unsigned __int8)v18(v28, "isEqualToString:", v17) == 0;
  v21 = CFSTR("HH:mm");
  if ( v19 )
    v21 = CFSTR("MM-dd");
  v20(v11, "setDateFormat:", v21);
  v23 = (void *)v22(v11, "stringFromDate:", v8);
  v24 = objc_retainAutoreleasedReturnValue(v23);
  return objc_autoreleaseReturnValue(v24);
}

//----- (0000000100B83BF3) ----------------------------------------------------
id __cdecl +[HXTools getYYYYMMDDDate:](id a1, SEL a2, id a3)
{
  NSDate *v3; // rax
  NSDate *v4; // r14

  _objc_msgSend(a3, "doubleValue");
  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[HXTools getDateFormatter](&OBJC_CLASS___HXTools, "getDateFormatter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setDateFormat:", CFSTR("yyyyMMdd"));
  v7 = _objc_msgSend(v6, "stringFromDate:", v4);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v4);
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B83C9F) ----------------------------------------------------
id __cdecl +[HXTools getHHmmssDate:](id a1, SEL a2, id a3)
{
  NSDate *v3; // rax
  NSDate *v4; // r14

  _objc_msgSend(a3, "doubleValue");
  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[HXTools getDateFormatter](&OBJC_CLASS___HXTools, "getDateFormatter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setDateFormat:", CFSTR("HH:mm:ss"));
  v7 = _objc_msgSend(v6, "stringFromDate:", v4);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v4);
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B83D4B) ----------------------------------------------------
id __cdecl +[HXTools getYYYYMMDDHHmmssDate:](id a1, SEL a2, double a3)
{
  NSDate *v3; // rax
  NSDate *v4; // r14

  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[HXTools getDateFormatter](&OBJC_CLASS___HXTools, "getDateFormatter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setDateFormat:", CFSTR("yyyy-MM-dd HH:mm:ss"));
  v7 = _objc_msgSend(v6, "stringFromDate:", v4);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v4);
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B83DE7) ----------------------------------------------------
id __cdecl +[HXTools getYYYYMMDDColonDate:market:code:](id a1, SEL a2, id a3, id a4, id a5)
{

  v7 = objc_retain(a5);
  v8 = objc_retain(a4);
  _objc_msgSend(a3, "doubleValue");
  v10 = _objc_msgSend(v9, "dateWithTimeIntervalSince1970:");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = +[HXTools getDateFormatterWithMarket:stockCode:](
          &OBJC_CLASS___HXTools,
          "getDateFormatterWithMarket:stockCode:",
          v8,
          v7);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v8);
  _objc_msgSend(v13, "setDateFormat:", CFSTR("yyyy:MM:dd:HH:mm"));
  v15 = _objc_msgSend(v13, "stringFromDate:", v11);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17(v13);
  v18(v11);
  return objc_autoreleaseReturnValue(v16);
}

//----- (0000000100B83ED0) ----------------------------------------------------
signed __int64 __cdecl +[HXTools getTenBillionLongFromTimeStamp](id a1, SEL a2)
{

  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "date");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "timeIntervalSince1970");
  v5 = v2 * 10000000.0;
  return (unsigned int)(int)v5
       - 10000000
       * (((unsigned __int64)((unsigned int)(int)v5
                            + (((__int64)0xD6BF94D5E57A42BDLL * (unsigned __int128)(unsigned int)(int)v5) >> 64)) >> 63)
        + ((__int64)((unsigned int)(int)v5
                   + (((__int64)0xD6BF94D5E57A42BDLL * (unsigned __int128)(unsigned int)(int)v5) >> 64)) >> 23));
}

//----- (0000000100B83F4E) ----------------------------------------------------
id __cdecl +[HXTools LondonDateWithCNDate:](id a1, SEL a2, id a3)
{
  NSDate *v3; // rax
  NSTimeZone *v4; // rax
  NSTimeZone *v5; // r15
  int v6; // eax
  SEL v9; // r12
  int v12; // eax
  NSDate *v19; // [rsp+0h] [rbp-30h]

  _objc_msgSend(a3, "doubleValue");
  v3 = _objc_msgSend(&OBJC_CLASS___NSDate, "dateWithTimeIntervalSince1970:");
  v19 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, "timeZoneWithName:", CFSTR("Asia/Shanghai"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = (unsigned int)_objc_msgSend(v5, "secondsFromGMT");
  v7 = _objc_msgSend(v19, "dateByAddingTimeInterval:", -(double)v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(&OBJC_CLASS___NSTimeZone, v9, CFSTR("Europe/London"));
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (unsigned int)_objc_msgSend(v11, "secondsFromGMT");
  v13 = _objc_msgSend(v8, "dateByAddingTimeInterval:", (double)v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15(v8);
  v16(v11);
  v17(v19);
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100B84070) ----------------------------------------------------
char __cdecl +[HXTools number:hasEqualValueTo:](id a1, SEL a2, id a3, id a4)
{
  char result; // al
  __m128d v9; // xmm3

  result = 0;
  if ( a3 )
  {
    if ( a4 )
    {
      v7 = objc_retain(a4);
      _objc_msgSend(a3, "doubleValue");
      v8(v7, "doubleValue");
      v9.f64[1] = *((double *)&v4 + 1);
      v9.f64[0] = *(double *)&v4 - *(double *)&v4;
      v10 = _mm_cmplt_sd(v9, (__m128d)0LL).f64[0];
      return COERCE_DOUBLE(~*(_QWORD *)&v10 & *(_QWORD *)&v9.f64[0] | COERCE_UNSIGNED_INT64(-v9.f64[0]) & *(_QWORD *)&v10) < 0.0000001192092895507812;
    }
  }
  return result;
}

//----- (0000000100B84122) ----------------------------------------------------
char __cdecl +[HXTools isDoubleEqualToZero:](id a1, SEL a2, double a3)
{
  return fabs(a3) < 0.0000001192092895507812;
}

//----- (0000000100B84141) ----------------------------------------------------
id __cdecl +[HXTools get_yyMMddHHmm_DateFromHXTime:FormatStr:](id a1, SEL a2, id a3, id a4)
{
  unsigned int v7; // r12d
  unsigned int v10; // eax
  unsigned int v17; // [rsp+Ch] [rbp-34h]
  unsigned int v18; // [rsp+10h] [rbp-30h]
  unsigned int v19; // [rsp+14h] [rbp-2Ch]

  if ( !a3 )
    return objc_autoreleaseReturnValue(0LL);
  v16 = objc_retain(a4);
  v5 = objc_retain(a3);
  v6 = objc_alloc((Class)&OBJC_CLASS___HqTimeSet);
  _objc_msgSend(v5, "intValue");
  v8 = _objc_msgSend(v6, "initWithInt:", v7);
  v17 = (unsigned int)_objc_msgSend(v8, "year");
  v18 = (unsigned int)_objc_msgSend(v8, "month");
  v19 = (unsigned int)_objc_msgSend(v8, "day");
  v9 = (unsigned int)_objc_msgSend(v8, "hour");
  v10 = (unsigned int)_objc_msgSend(v8, "min");
  v12 = _objc_msgSend(v11, "stringWithFormat:", v16, v17, v18, v19, v9, v10);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  return objc_autoreleaseReturnValue(v13);
}

//----- (0000000100B84273) ----------------------------------------------------
int __cdecl +[HXTools getOperatingSystemVersion](id a1, SEL a2)
{
  NSNumber *v7; // rax
  NSNumber *v8; // r13
  id (*v9)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  NSString *v22; // rax
  NSString *v23; // rbx
  NSString *v24; // rax
  NSString *v25; // rbx
  NSString *v26; // rax
  int v29; // r14d

  v2 = _objc_msgSend(&OBJC_CLASS___NSProcessInfo, "processInfo");
  v3 = (const char *)objc_retainAutoreleasedReturnValue(v2);
  v4 = (char *)v3;
  if ( v3 )
  {
    objc_msgSend_stret(&v38, v3, "operatingSystemVersion");
  }
  else
  {
    v38 = 0LL;
    v39 = 0LL;
  }
  v5 = v38;
  v6 = v39;
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = v9(v8, "stringValue");
  v40 = objc_retainAutoreleasedReturnValue(v10);
  v12 = v11(&OBJC_CLASS___NSNumber, "numberWithInteger:", *((_QWORD *)&v5 + 1));
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v15 = v14(v13, "stringValue");
  v41 = objc_retainAutoreleasedReturnValue(v15);
  v17 = v16(&OBJC_CLASS___NSNumber, "numberWithInteger:", v6);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = v40;
  v21 = v20(v18, "stringValue");
  objc_retainAutoreleasedReturnValue(v21);
  if ( (__int64)v5 <= 9 )
  {
    v22 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("0%ld"));
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v19 = v23;
  }
  if ( *((__int64 *)&v5 + 1) <= 9 )
  {
    v24 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("0%ld"));
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v41 = v25;
  }
  if ( v6 <= 9 )
  {
    v26 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("0%ld"));
    objc_retainAutoreleasedReturnValue(v26);
  }
  v29 = -1;
  if ( _objc_msgSend(v19, "length") && _objc_msgSend(v41, "length") && _objc_msgSend(v28, "length") )
  {
    v30 = _objc_msgSend(v19, "stringByAppendingString:", v41);
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v33 = _objc_msgSend(v31, "stringByAppendingString:", v32);
    v34 = objc_retainAutoreleasedReturnValue(v33);
    v40 = v19;
    v35 = v34;
    v29 = (unsigned int)_objc_msgSend(v35, "intValue");
    v36 = v35;
    v19 = v40;
  }
  return v29;
}

//----- (0000000100B84540) ----------------------------------------------------
id __cdecl +[HXTools crcValue:](id a1, SEL a2, id a3)
{
  __CFString *v4; // r14
  NSString *v6; // rax

  v3 = objc_retain(a3);
  v4 = CFSTR(" ");
  if ( _objc_msgSend(v3, "length") )
  {
    v5 = +[HXCRC32 crc:](&OBJC_CLASS___HXCRC32, "crc:", v3);
    if ( v5 )
    {
      v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%lu"), v5);
      v4 = objc_retainAutoreleasedReturnValue(v6);
    }
  }
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100B845CB) ----------------------------------------------------
id __cdecl +[HXTools decodeStringWithData:](id a1, SEL a2, id a3)
{
  unsigned __int64 v6; // rax

  v3 = objc_retain(a3);
  if ( v3
    && (v4 = _objc_msgSend(&OBJC_CLASS___NSData, "class"), (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4)) )
  {
    v5 = objc_alloc(&OBJC_CLASS___NSString);
    v6 = CFStringConvertEncodingToNSStringEncoding(0x632u);
    v7 = _objc_msgSend(v5, "initWithData:encoding:", v3, v6);
  }
  else
  {
    v7 = 0LL;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100B84658) ----------------------------------------------------
id __cdecl +[HXTools getStockCodeArrayWithCodeList:](id a1, SEL a2, id a3)
{

  v3 = +[HXTools getMarketAndCodesDicFromCodeList:](&OBJC_CLASS___HXTools, "getMarketAndCodesDicFromCodeList:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = +[HXTools getStockCodesWithMarketAndCodesDic:](&OBJC_CLASS___HXTools, "getStockCodesWithMarketAndCodesDic:", v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  return objc_autoreleaseReturnValue(v6);
}

//----- (0000000100B846B6) ----------------------------------------------------
id __cdecl +[HXTools getMarketAndCodesDicFromCodeList:](id a1, SEL a2, id a3)
{
  unsigned __int64 i; // r14
  SEL v13; // r12
  SEL v29; // [rsp+40h] [rbp-120h]
  SEL v31; // [rsp+50h] [rbp-110h]
  SEL v34; // [rsp+68h] [rbp-F8h]
  SEL v35; // [rsp+70h] [rbp-F0h]
  SEL v36; // [rsp+78h] [rbp-E8h]
  SEL v37; // [rsp+80h] [rbp-E0h]
  id obj; // [rsp+A8h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v6 = _objc_msgSend(v4, "componentsSeparatedByString:", CFSTR(";"));
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = v7;
    if ( v7 && _objc_msgSend(v7, "count") )
    {
      v38 = v4;
      v9 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
      v41 = objc_retainAutoreleasedReturnValue(v9);
      v25 = 0LL;
      v26 = 0LL;
      v27 = 0LL;
      v28 = 0LL;
      v39 = v8;
      obj = objc_retain(v8);
      v40 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v43, 16LL);
      if ( v40 )
      {
        v30 = *(_QWORD *)v26;
        do
        {
          v29 = "class";
          v31 = "rangeOfString:";
          v34 = "substringToIndex:";
          v35 = "substringFromIndex:";
          v36 = "stringByReplacingOccurrencesOfString:withString:";
          v37 = "setObject:forKeyedSubscript:";
          for ( i = 0LL; i < (unsigned __int64)v40; ++i )
          {
            if ( *(_QWORD *)v26 != v30 )
              objc_enumerationMutation(obj);
            v11 = *(void **)(*((_QWORD *)&v25 + 1) + 8 * i);
            v12 = _objc_msgSend(&OBJC_CLASS___NSString, v29);
            v14 = (unsigned __int8)_objc_msgSend(v11, v13, v12);
            if ( v11 )
            {
              if ( v14 )
              {
                if ( !(unsigned __int8)_objc_msgSend(v11, "isEqualToString:", &charsToLeaveEscaped) )
                {
                  v15 = (char *)_objc_msgSend(v11, v31, CFSTR("("));
                  if ( v15 != (char *)0x7FFFFFFFFFFFFFFFLL )
                  {
                    v16 = _objc_msgSend(v11, v34, v15);
                    v33 = objc_retainAutoreleasedReturnValue(v16);
                    v17 = _objc_msgSend(v11, v35, v15 + 1);
                    v18 = objc_retainAutoreleasedReturnValue(v17);
                    v19 = _objc_msgSend(v18, v36, CFSTR(")"), &charsToLeaveEscaped);
                    v20 = objc_retainAutoreleasedReturnValue(v19);
                    v32 = v20;
                    v21 = _objc_msgSend(v20, "componentsSeparatedByString:", CFSTR(","));
                    v22 = objc_retainAutoreleasedReturnValue(v21);
                    v23 = v33;
                    if ( v33
                      && v22
                      && !(unsigned __int8)_objc_msgSend(v33, "isEqualToString:", &charsToLeaveEscaped)
                      && _objc_msgSend(v22, "count") )
                    {
                      _objc_msgSend(v41, v37, v22, v23);
                    }
                  }
                }
              }
            }
          }
          v40 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v25, v43, 16LL);
        }
        while ( v40 );
      }
      v4 = v38;
      v8 = v39;
      v5 = v41;
    }
    else
    {
      v5 = 0LL;
    }
  }
  else
  {
    v5 = 0LL;
  }
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100B84A99) ----------------------------------------------------
id __cdecl +[HXTools getStockCodesWithMarketAndCodesDic:](id a1, SEL a2, id a3)
{
  _QWORD v10[4]; // [rsp+8h] [rbp-48h] BYREF

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v10[0] = _NSConcreteStackBlock;
    v10[1] = 3254779904LL;
    v10[2] = sub_100B84B6A;
    v10[3] = &unk_1012DB028;
    v7 = objc_retain(v6);
    v11 = v7;
    v8(v4, "enumerateKeysAndObjectsUsingBlock:", v10);
  }
  else
  {
    v7 = 0LL;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100B84B6A) ----------------------------------------------------
__int64 __fastcall sub_100B84B6A(__int64 a1, void *a2, __int64 a3, _BYTE *a4)
{
  SEL v13; // r12
  unsigned __int64 v15; // r15
  id (*v19)(id, SEL, ...); // r12
  SEL v32; // [rsp+48h] [rbp-118h]
  SEL v34; // [rsp+58h] [rbp-108h]
  SEL v35; // [rsp+60h] [rbp-100h]
  id obj; // [rsp+88h] [rbp-D8h]

  v36 = a1;
  v5 = objc_retain(a2);
  v7 = objc_retain(v6);
  v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  v39 = v5;
  v9 = v5;
  v11 = v10;
  if ( (unsigned __int8)_objc_msgSend(v9, "isKindOfClass:", v8)
    && (v12 = _objc_msgSend(&OBJC_CLASS___NSArray, v11), (unsigned __int8)_objc_msgSend(v7, v13, v12)) )
  {
    v37 = v7;
    v27 = 0LL;
    v28 = 0LL;
    v29 = 0LL;
    v30 = 0LL;
    obj = objc_retain(v7);
    v38 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v27, v43, 16LL);
    if ( v38 )
    {
      v31 = *(_QWORD *)v28;
      do
      {
        v32 = "isEqualToString:";
        v34 = "dictionaryWithObjects:forKeys:count:";
        v35 = "addObject:";
        v15 = 0LL;
        do
        {
          if ( *(_QWORD *)v28 != v31 )
            objc_enumerationMutation(obj);
          v16 = *(void **)(*((_QWORD *)&v27 + 1) + 8 * v15);
          if ( v16 )
          {
            v17 = v14;
            v18 = _objc_msgSend(&OBJC_CLASS___NSString, v11);
            v20 = (unsigned __int8)v19(v16, v17, v18);
            v14 = v17;
            if ( v20 )
            {
              if ( !(unsigned __int8)_objc_msgSend(v16, v32, &charsToLeaveEscaped) )
              {
                v33 = *(id *)(v36 + 32);
                v41[0] = (__int64)CFSTR("Market");
                v42[0] = (__int64)v39;
                v41[1] = (__int64)CFSTR("StockCode");
                v42[1] = (__int64)v16;
                v21 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v34, v42, v41, 2LL);
                v22 = v11;
                v23 = objc_retainAutoreleasedReturnValue(v21);
                v24(v33, v35, v23);
                v25 = v23;
                v11 = v22;
              }
            }
          }
          ++v15;
        }
        while ( v15 < (unsigned __int64)v38 );
        v38 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v27, v43, 16LL);
      }
      while ( v38 );
    }
    v7 = v37;
  }
  else
  {
    *a4 = 1;
  }
  return __stack_chk_guard;
}

//----- (0000000100B84E79) ----------------------------------------------------
id __cdecl +[HXTools getThemeArrayWithThemeList:](id a1, SEL a2, id a3)
{
  id (**v4)(id, SEL, ...); // r13
  id (**v20)(id, SEL, ...); // r15
  NSArray *v37; // r15
  NSArray *v39; // rax
  SEL v50; // [rsp+60h] [rbp-100h]
  SEL v52; // [rsp+70h] [rbp-F0h]
  SEL v55; // [rsp+88h] [rbp-D8h]
  SEL v57; // [rsp+98h] [rbp-C8h]
  id obj; // [rsp+A8h] [rbp-B8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = &_objc_msgSend;
    v54 = v3;
    v5 = _objc_msgSend(v3, "componentsSeparatedByString:", CFSTR("\n"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v56 = objc_retainAutoreleasedReturnValue(v7);
    v42 = 0LL;
    v43 = 0LL;
    v44 = 0LL;
    v45 = 0LL;
    obj = objc_retain(v6);
    v8 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v42, v60, 16LL);
    if ( v8 )
    {
      v9 = (__int64)v8;
      v49 = *(_QWORD *)v43;
LABEL_4:
      v50 = "count";
      v52 = "dictionary";
      v57 = "objectAtIndexedSubscript:";
      v55 = "numberWithInt:";
      v58 = "setObject:forKeyedSubscript:";
      v53 = "addObject:";
      v51 = v9;
      v10 = 0LL;
      while ( 1 )
      {
        if ( *(_QWORD *)v43 != v49 )
          objc_enumerationMutation(obj);
        v11 = *(id *)(*((_QWORD *)&v42 + 1) + 8 * v10);
        if ( !((id (*)(id, SEL, ...))v4)(v11, "length") )
          break;
        v12 = ((id (*)(id, SEL, ...))v4)(v11, "componentsSeparatedByString:", CFSTR(","));
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v14 = v13;
        if ( !v13 || (unsigned __int64)((id (*)(id, SEL, ...))v4)(v13, v50) <= 2 )
        {
          break;
        }
        v15 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSMutableDictionary, v52);
        objc_retainAutoreleasedReturnValue(v15);
        v16 = ((id (*)(id, SEL, ...))v4)(v14, v57, 0LL);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, v55, 5LL);
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v46 = v10;
        v20 = v4;
        v21 = v19;
        v48 = v22;
        ((void (__fastcall *)(void *, const char *, id, id))v20)(v22, v58, v17, v19);
        v47 = v14;
        v23 = (void *)((__int64 (__fastcall *)(id, SEL, __int64))v20)(v14, v57, 1LL);
        v24 = objc_retainAutoreleasedReturnValue(v23);
        v25 = (void *)((__int64 (__fastcall *)(NSArray *, SEL, __int64))v20)(&OBJC_CLASS___NSNumber, v55, 12345671LL);
        v26 = objc_retainAutoreleasedReturnValue(v25);
        ((void (__fastcall *)(__int64, const char *, id, id))v20)(v27, v58, v24, v26);
        v28 = v24;
        v4 = v20;
        v29 = v46;
        v30 = v47;
        v31 = (void *)((__int64 (__fastcall *)(id, SEL, __int64))v4)(v47, v57, 2LL);
        v32 = objc_retainAutoreleasedReturnValue(v31);
        v33 = v48;
        ((void (__fastcall *)(id, const char *, id, __CFString *))v4)(v48, v58, v32, CFSTR("LinkCode"));
        ((void (__fastcall *)(id, const char *, id))v4)(v56, v53, v33);
        v35(v33);
        v36(v30);
        v10 = v29 + 1;
        if ( v51 == v10 )
        {
          v9 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v4)(
                 obj,
                 "countByEnumeratingWithState:objects:count:",
                 &v42,
                 v60,
                 16LL);
          if ( v9 )
            goto LABEL_4;
          break;
        }
      }
    }
    v38 = v56;
    v39 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v56);
    v37 = objc_retainAutoreleasedReturnValue(v39);
    v3 = v54;
  }
  else
  {
    v37 = 0LL;
  }
  return objc_autoreleaseReturnValue(v37);
}

//----- (0000000100B852A3) ----------------------------------------------------
id __cdecl +[HXTools sortData:withRightOrder:](id a1, SEL a2, id a3, id a4)
{
  NSArray *v8; // r14
  id (**v9)(id, SEL, ...); // r13
  unsigned __int64 v16; // rbx
  unsigned __int64 v26; // r12
  id (**v28)(id, SEL, ...); // r15
  NSArray *v38; // rax
  unsigned __int64 v39; // [rsp+8h] [rbp-88h]

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v43 = v5;
  if ( !v5 )
    goto LABEL_6;
  v7 = _objc_msgSend(v5, "count");
  v8 = 0LL;
  if ( v6 && v7 )
  {
    if ( !_objc_msgSend(v6, "count")
      || (v9 = &_objc_msgSend, v10 = _objc_msgSend(v5, "count"), v10 < _objc_msgSend(v11, "count")) )
    {
LABEL_6:
      v8 = 0LL;
      goto LABEL_7;
    }
    v13 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v41 = objc_retainAutoreleasedReturnValue(v13);
    if ( _objc_msgSend(v14, "count") )
    {
      v16 = 0LL;
      do
      {
        v17 = ((id (*)(id, SEL, ...))v9)(v15, "objectAtIndexedSubscript:", v16);
        v18 = objc_retainAutoreleasedReturnValue(v17);
        if ( v18 )
        {
          v19 = ((id (*)(id, SEL, ...))v9)(&OBJC_CLASS___NSDictionary, "class");
          if ( (unsigned __int8)((id (*)(id, SEL, ...))v9)(v18, "isKindOfClass:", v19) )
          {
            v39 = v16;
            v20 = ((id (*)(id, SEL, ...))v9)(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v40 = v18;
            v22 = ((id (*)(id, SEL, ...))v9)(v18, "objectForKeyedSubscript:", v21);
            v23 = objc_retainAutoreleasedReturnValue(v22);
            v42 = v23;
            v24 = v43;
            if ( v23 )
            {
              v25 = ((id (*)(id, SEL, ...))v9)(&OBJC_CLASS___NSString, "class");
              if ( (unsigned __int8)((id (*)(id, SEL, ...))v9)(v23, "isKindOfClass:", v25) )
              {
                if ( ((id (*)(id, SEL, ...))v9)(v43, "count") )
                {
                  v26 = 0LL;
                  do
                  {
                    v27 = ((id (*)(id, SEL, ...))v9)(v24, "objectAtIndexedSubscript:", v26);
                    v28 = v9;
                    v29 = objc_retainAutoreleasedReturnValue(v27);
                    if ( v29
                      && (v30 = ((__int64 (__fastcall *)(objc_class *, const char *))v28)(
                                  &OBJC_CLASS___NSDictionary,
                                  "class"),
                          ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v28)(v29, "isKindOfClass:", v30)) )
                    {
                      v31 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v28)(
                                      &OBJC_CLASS___NSNumber,
                                      "numberWithInt:",
                                      5LL);
                      v32 = objc_retainAutoreleasedReturnValue(v31);
                      v33 = (void *)((__int64 (__fastcall *)(id, const char *, id))v28)(
                                      v29,
                                      "objectForKeyedSubscript:",
                                      v32);
                      v34 = objc_retainAutoreleasedReturnValue(v33);
                      if ( v34 )
                      {
                        v35 = ((__int64 (__fastcall *)(objc_class *, const char *))v28)(&OBJC_CLASS___NSString, "class");
                        if ( ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v28)(
                               v34,
                               "isKindOfClass:",
                               v35) )
                        {
                          if ( ((unsigned __int8 (__fastcall *)(id, const char *, id))v28)(v34, "isEqualToString:", v42) )
                          {
                            ((void (__fastcall *)(id, const char *, id))v28)(v41, "addObject:", v29);
                            v9 = v28;
                            break;
                          }
                        }
                      }
                      v24 = v43;
                    }
                    else
                    {
                    }
                    v9 = &_objc_msgSend;
                    v36 = _objc_msgSend(v24, "count");
                  }
                  while ( (unsigned __int64)v36 > v26 );
                }
              }
            }
            v16 = v39;
            v18 = v40;
          }
        }
        ++v16;
      }
      while ( (unsigned __int64)((id (*)(id, SEL, ...))v9)(v37, "count") > v16 );
    }
    if ( _objc_msgSend(v41, "count") )
    {
      v38 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v41);
      v8 = objc_retainAutoreleasedReturnValue(v38);
    }
    else
    {
      v8 = 0LL;
    }
  }
LABEL_7:
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B85663) ----------------------------------------------------
id __cdecl +[HXTools sortArray:WithString:ascending:](id a1, SEL a2, id a3, id a4, char a5)
{
  _QWORD v17[4]; // [rsp+8h] [rbp-68h] BYREF

  v7 = objc_retain(a3);
  v8 = objc_retain(a4);
  if ( _objc_msgSend(v8, "length") )
  {
    v9 = _objc_msgSend(&OBJC_CLASS___NSNull, "null");
    v10 = a5;
    v11 = objc_retainAutoreleasedReturnValue(v9);
    v17[0] = _NSConcreteStackBlock;
    v17[1] = 3254779904LL;
    v17[2] = sub_100B85795;
    v17[3] = &unk_1012E1BA8;
    v18 = objc_retain(v12);
    v19 = v11;
    v20 = v10;
    v21 = objc_retain(v11);
    v13 = _objc_msgSend(v7, "sortedArrayUsingComparator:", v17);
    v14 = objc_retainAutoreleasedReturnValue(v13);
  }
  else
  {
    v14 = objc_retain(v7);
  }
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100B85795) ----------------------------------------------------
__int64 __fastcall sub_100B85795(__int64 a1, void *a2, void *a3)
{
  id (*v7)(id, SEL, ...); // r12
  bool v14; // zf

  v3 = *(_QWORD *)(a1 + 32);
  v4 = objc_retain(a3);
  v5 = _objc_msgSend(a2, "objectForKey:", v3);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v4, "objectForKey:", *(_QWORD *)(a1 + 32));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = 0LL;
  if ( v6 )
  {
    if ( v9 )
    {
      v11 = *(id *)(a1 + 40);
      v10 = 0LL;
      if ( v6 != v11 && v9 != v11 )
      {
        v12 = _objc_msgSend(v6, "compare:", v9);
        v10 = (__int64)v12;
        if ( !*(_BYTE *)(a1 + 48) )
        {
          v13 = -(__int64)(v12 == (id)1);
          v14 = v10 == -1;
          v10 = 1LL;
          if ( !v14 )
            v10 = v13;
        }
      }
    }
  }
  return v10;
}

//----- (0000000100B8587C) ----------------------------------------------------
id __cdecl +[HXTools getCompleteDatasWith:and:](id a1, SEL a2, id a3, id a4)
{
  NSArray *v9; // r15
  SEL v20; // r12
  int v25; // r13d
  NSArray *v41; // rax
  unsigned __int64 v44; // r12
  SEL v59; // rdx
  NSArray *v83; // rax
  SEL v90; // [rsp+48h] [rbp-168h]
  SEL v91; // [rsp+50h] [rbp-160h]
  SEL v92; // [rsp+58h] [rbp-158h]
  SEL v94; // [rsp+68h] [rbp-148h]
  SEL v95; // [rsp+70h] [rbp-140h]
  SEL v96; // [rsp+78h] [rbp-138h]
  SEL v97; // [rsp+80h] [rbp-130h]
  SEL v98; // [rsp+88h] [rbp-128h]
  SEL v99; // [rsp+90h] [rbp-120h]
  SEL v100; // [rsp+98h] [rbp-118h]
  SEL v102; // [rsp+A8h] [rbp-108h]
  SEL v103; // [rsp+B0h] [rbp-100h]
  id obj; // [rsp+B8h] [rbp-F8h]
  SEL v107; // [rsp+D0h] [rbp-E0h]
  SEL v108; // [rsp+D8h] [rbp-D8h]

  v93 = a1;
  objc_retain(a3);
  v6 = objc_retain(a4);
  if ( !v5 || !_objc_msgSend(v5, "count") )
    goto LABEL_6;
  if ( v6 && _objc_msgSend(v6, "count") )
  {
    v8 = _objc_msgSend(v7, "count");
    if ( v8 == _objc_msgSend(v6, "count") )
    {
LABEL_6:
      v9 = (NSArray *)objc_retain(v6);
      goto LABEL_63;
    }
    v109 = v6;
    v42 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v106 = objc_retainAutoreleasedReturnValue(v42);
    v105 = v43;
    if ( !_objc_msgSend(v43, "count") )
      goto LABEL_62;
    v98 = "class";
    v99 = "isKindOfClass:";
    v100 = "objectForKeyedSubscript:";
    v110 = "length";
    v96 = "getMarketAndCodeByAppendingMarket:andStockCode:";
    v90 = "isEqualToString:";
    v91 = "numberWithInt:";
    v92 = "dictionaryWithObject:forKey:";
    v94 = "addObject:";
    v44 = 0LL;
    while ( 1 )
    {
      v45 = _objc_msgSend(v105, "objectAtIndexedSubscript:", v44);
      v46 = objc_retainAutoreleasedReturnValue(v45);
      v47 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v98);
      if ( !(unsigned __int8)_objc_msgSend(v46, v99, v47) )
      {
        break;
      }
      v49 = objc_retain(v46);
      if ( !v49 && !_objc_msgSend(0LL, "count") )
        break;
      v108 = v48;
      v50 = _objc_msgSend(v49, v100, CFSTR("StockCode"));
      v51 = objc_retainAutoreleasedReturnValue(v50);
      v52 = _objc_msgSend(v49, v100, CFSTR("Market"));
      v53 = objc_retainAutoreleasedReturnValue(v52);
      LODWORD(v112) = 4;
      obj = v49;
      if ( !_objc_msgSend(v51, (SEL)v110) || !_objc_msgSend(v53, v54) )
        goto LABEL_57;
      v55 = _objc_msgSend(v93, v96, v53, v51);
      v111 = objc_retainAutoreleasedReturnValue(v55);
      if ( !_objc_msgSend(v111, (SEL)v110) )
      {
        LODWORD(v112) = 4;
        goto LABEL_57;
      }
      v97 = (SEL)v53;
      v102 = (SEL)v51;
      v95 = "numberWithInt:";
      v101 = "isEqualToString:";
      v56 = 0LL;
      v107 = (SEL)&charsToLeaveEscaped;
      v112 = 0LL;
      while ( 1 )
      {
        v57 = (const char *)_objc_msgSend(v109, "count");
        v59 = v56;
        if ( v57 <= v56 )
          goto LABEL_51;
        v103 = v56;
        v60 = _objc_msgSend(v109, "objectAtIndexedSubscript:");
        v61 = objc_retainAutoreleasedReturnValue(v60);
        v62 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v98);
        if ( !(unsigned __int8)_objc_msgSend(v61, v99, v62) )
          goto LABEL_48;
        v63 = objc_retain(v61);
        if ( !v63 )
        {
          v112 = 0LL;
          goto LABEL_48;
        }
        if ( !_objc_msgSend(v63, "count") )
        {
          v112 = v63;
LABEL_48:
LABEL_50:
          v59 = v103;
          goto LABEL_51;
        }
        v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, v95, 5LL);
        v65 = objc_retainAutoreleasedReturnValue(v64);
        v66 = _objc_msgSend(v63, v100, v65);
        v67 = objc_retainAutoreleasedReturnValue(v66);
        v69(v65);
        v70 = _objc_msgSend(v67, (SEL)v110);
        v71(v63);
        if ( !v70 )
        {
          v112 = v63;
          goto LABEL_50;
        }
        if ( _objc_msgSend(v109, "count") <= v103 )
          break;
        v56 = v103 + 1;
        v72 = (unsigned __int8)_objc_msgSend(v67, (SEL)v101, v111);
        v107 = (SEL)v67;
        v58 = v63;
        v112 = v63;
        if ( v72 )
        {
          v112 = v63;
          v59 = (SEL)(v73 + 1);
          goto LABEL_51;
        }
      }
      v59 = v103 + 1;
      v112 = v63;
LABEL_51:
      if ( (unsigned __int64)_objc_msgSend(v109, "count", v59, v58) > (int)v59
        || (unsigned __int8)_objc_msgSend(v74, v90, v111) )
      {
        v75 = v74;
        v76 = v112;
        v51 = (void *)v102;
        if ( v112 )
          _objc_msgSend(v106, v94, v112);
      }
      else
      {
        v75 = v74;
        v77 = _objc_msgSend(&OBJC_CLASS___NSNumber, v91, 5LL);
        v78 = objc_retainAutoreleasedReturnValue(v77);
        v79 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v92, v111, v78);
        v80 = objc_retainAutoreleasedReturnValue(v79);
        _objc_msgSend(v106, v94, v80);
        v51 = (void *)v102;
        v76 = v112;
      }
      LODWORD(v112) = 0;
      v53 = (void *)v97;
LABEL_57:
      if ( !(_DWORD)v112 )
      {
        v82 = _objc_msgSend(v105, "count");
        if ( (unsigned __int64)v82 > v44 )
          continue;
      }
      break;
    }
LABEL_62:
    v83 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v106);
    v9 = objc_retainAutoreleasedReturnValue(v83);
    v6 = v109;
  }
  else
  {
    v109 = v6;
    v10 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v101 = objc_retainAutoreleasedReturnValue(v10);
    v86 = 0LL;
    v87 = 0LL;
    v88 = 0LL;
    v89 = 0LL;
    v105 = v11;
    obj = objc_retain(v11);
    v12 = (const char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v86, v113, 16LL);
    if ( v12 )
    {
      v13 = v12;
      v99 = *(SEL *)v87;
LABEL_9:
      v103 = "class";
      v98 = "isKindOfClass:";
      v108 = "objectForKeyedSubscript:";
      v107 = "length";
      v95 = "getMarketAndCodeByAppendingMarket:andStockCode:";
      v96 = "numberWithInt:";
      v102 = "dictionaryWithObject:forKey:";
      v97 = "addObject:";
      v100 = v13;
      v14 = 0LL;
      while ( 1 )
      {
        if ( *(SEL *)v87 != v99 )
          objc_enumerationMutation(obj);
        v15 = *(void **)(*((_QWORD *)&v86 + 1) + 8 * v14);
        v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v103);
        if ( !(unsigned __int8)_objc_msgSend(v15, v98, v16) )
          break;
        v17 = objc_retain(v15);
        if ( !v17 && !_objc_msgSend(0LL, "count") )
          break;
        v18 = _objc_msgSend(v17, v108, CFSTR("StockCode"));
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v110 = v17;
        v21 = _objc_msgSend(v17, v20, CFSTR("Market"));
        objc_retainAutoreleasedReturnValue(v21);
        v112 = v19;
        v22 = v107;
        v23 = _objc_msgSend(v19, v107);
        v25 = 2;
        if ( v23 )
        {
          v26 = _objc_msgSend(v24, v22);
          v28 = v27;
          if ( v26 )
          {
            v29 = _objc_msgSend(v93, v95, v27, v112);
            v111 = objc_retainAutoreleasedReturnValue(v29);
            if ( _objc_msgSend(v111, v107) )
            {
              v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, v96, 5LL);
              v31 = objc_retainAutoreleasedReturnValue(v30);
              v32 = v111;
              v33 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v102, v111, v31);
              v34 = objc_retainAutoreleasedReturnValue(v33);
              v106 = v28;
              v35 = v34;
              _objc_msgSend(v101, v97, v35);
              v37 = v35;
              v28 = v106;
              v38 = v32;
              v25 = 0;
            }
            else
            {
              v38 = v111;
              v25 = 2;
            }
          }
        }
        else
        {
          v28 = v24;
        }
        if ( v25 )
          break;
        if ( v100 == (SEL)++v14 )
        {
          v13 = (const char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v86, v113, 16LL);
          if ( v13 )
            goto LABEL_9;
          break;
        }
      }
    }
    v40 = v101;
    v41 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v101);
    v9 = objc_retainAutoreleasedReturnValue(v41);
    v6 = v109;
  }
LABEL_63:
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100B86369) ----------------------------------------------------
id __cdecl +[HXTools getCompleteStockCodesWith:and:](id a1, SEL a2, id a3, id a4)
{
  _BYTE *v8; // rbx
  NSMutableArray *v9; // rax
  unsigned __int64 v11; // r12
  SEL v24; // r12
  int v39; // r12d
  NSArray *v49; // r15
  SEL v60; // r12
  int v65; // r13d
  NSArray *v81; // rax
  NSArray *v82; // rax
  SEL v89; // [rsp+40h] [rbp-170h]
  SEL v90; // [rsp+48h] [rbp-168h]
  SEL v91; // [rsp+50h] [rbp-160h]
  SEL v92; // [rsp+58h] [rbp-158h]
  SEL v96; // [rsp+78h] [rbp-138h]
  SEL v97; // [rsp+80h] [rbp-130h]
  SEL v98; // [rsp+88h] [rbp-128h]
  SEL v99; // [rsp+90h] [rbp-120h]
  SEL v100; // [rsp+98h] [rbp-118h]
  SEL v101; // [rsp+A0h] [rbp-110h]
  SEL v102; // [rsp+A8h] [rbp-108h]
  SEL v104; // [rsp+B8h] [rbp-F8h]
  SEL v105; // [rsp+C0h] [rbp-F0h]
  id obj; // [rsp+D0h] [rbp-E0h]
  SEL v109; // [rsp+E0h] [rbp-D0h]
  SEL v110; // [rsp+E8h] [rbp-C8h]

  v93 = a1;
  objc_retain(a3);
  v6 = objc_retain(a4);
  if ( !v5 || !_objc_msgSend(v5, "count") )
    goto LABEL_42;
  v103 = v6;
  if ( v6 && _objc_msgSend(v6, "count") )
  {
    v8 = _objc_msgSend(v7, "count");
    v97 = (SEL)(v8 - (_BYTE *)_objc_msgSend(v6, "count"));
    if ( v97 )
    {
      v9 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v6);
      v94 = objc_retainAutoreleasedReturnValue(v9);
      v106 = v10;
      if ( _objc_msgSend(v10, "count") )
      {
        v99 = "class";
        v100 = "isKindOfClass:";
        v101 = "objectForKeyedSubscript:";
        v102 = "length";
        v98 = "getMarketAndCodeByAppendingMarket:andStockCode:";
        v89 = "isEqualToString:";
        v90 = "numberWithInt:";
        v91 = "dictionaryWithObject:forKey:";
        v92 = "addObject:";
        v11 = 0LL;
        v105 = 0LL;
        while ( 1 )
        {
          v12 = _objc_msgSend(v106, "objectAtIndexedSubscript:", v11);
          v13 = objc_retainAutoreleasedReturnValue(v12);
          v14 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v99);
          if ( !(unsigned __int8)_objc_msgSend(v13, v100, v14) )
            break;
          v15 = objc_retain(v13);
          if ( v15 || _objc_msgSend(0LL, "count") )
          {
            v111 = v16;
            v17 = _objc_msgSend(v15, v101, CFSTR("StockCode"));
            objc_retainAutoreleasedReturnValue(v17);
            v95 = v15;
            v18 = _objc_msgSend(v15, v101, CFSTR("Market"));
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v108 = v20;
            obj = v19;
            if ( !_objc_msgSend(v20, v102) )
              goto LABEL_23;
            if ( _objc_msgSend(v19, v21) )
            {
              v22 = _objc_msgSend(v93, v98, v19, v108);
              v23 = (char *)objc_retainAutoreleasedReturnValue(v22);
              if ( _objc_msgSend(v23, v24) )
              {
                v110 = v23;
                v104 = "numberWithInt:";
                v96 = "isEqualToString:";
                v25 = 0LL;
                v112 = &charsToLeaveEscaped;
                while ( 1 )
                {
                  if ( _objc_msgSend(v6, "count") <= v25 )
                    goto LABEL_31;
                  v109 = v25;
                  v26 = _objc_msgSend(v6, "objectAtIndexedSubscript:", v25);
                  v27 = objc_retainAutoreleasedReturnValue(v26);
                  v28 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v99);
                  if ( !(unsigned __int8)_objc_msgSend(v27, v100, v28) )
                  {
                    goto LABEL_30;
                  }
                  v29 = objc_retain(v27);
                  v30 = v29;
                  if ( !v29 || !_objc_msgSend(v29, "count") )
                  {
                    goto LABEL_29;
                  }
                  v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, v104, 5LL);
                  v32 = objc_retainAutoreleasedReturnValue(v31);
                  v33 = _objc_msgSend(v30, v101, v32);
                  v34 = objc_retainAutoreleasedReturnValue(v33);
                  v36 = v32;
                  v38 = (void (__cdecl **)(id))v37;
                  v37(v36);
                  if ( !_objc_msgSend(v34, v102) )
                  {
                    ((void (__cdecl *)(id))v38)(v30);
LABEL_29:
                    ((void (__cdecl *)(id))v38)(v30);
                    v6 = v103;
LABEL_30:
                    LODWORD(v25) = (_DWORD)v109;
                    goto LABEL_31;
                  }
                  ((void (__fastcall *)(id))v38)(v30);
                  ((void (__fastcall *)(id))v38)(v30);
                  v6 = v103;
                  if ( _objc_msgSend(v103, "count") <= v109 )
                    break;
                  v25 = v109 + 1;
                  v112 = v34;
                  if ( (unsigned __int8)_objc_msgSend(v34, v96, v110) )
                  {
                    LODWORD(v25) = v39 + 1;
                    goto LABEL_31;
                  }
                }
                LODWORD(v25) = (_DWORD)v109 + 1;
LABEL_31:
                if ( (unsigned __int64)_objc_msgSend(v6, "count") <= (int)v25 )
                {
                  v41 = v40;
                  v23 = (char *)v110;
                  if ( !(unsigned __int8)_objc_msgSend(v40, v89, v110) )
                  {
                    v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, v90, 5LL);
                    v43 = objc_retainAutoreleasedReturnValue(v42);
                    v44 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v91, v110, v43);
                    v45 = objc_retainAutoreleasedReturnValue(v44);
                    _objc_msgSend(v94, v92, v45);
                    ++v105;
                    v46 = v45;
                    v23 = (char *)v110;
                  }
                }
                else
                {
                  v41 = v40;
                  v23 = (char *)v110;
                }
                LODWORD(v112) = 0;
              }
              else
              {
                LODWORD(v112) = 4;
              }
            }
            else
            {
LABEL_23:
              LODWORD(v112) = 4;
            }
            v6 = v103;
            if ( !(_DWORD)v112 )
            {
              v47 = _objc_msgSend(v106, "count");
              if ( v105 < v97 )
              {
                v11 = v48 + 1;
                if ( (unsigned __int64)v47 > v11 )
                  continue;
              }
            }
          }
          goto LABEL_63;
        }
      }
LABEL_63:
      v82 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v94);
      v49 = objc_retainAutoreleasedReturnValue(v82);
      goto LABEL_64;
    }
LABEL_42:
    v49 = (NSArray *)objc_retain(v6);
    goto LABEL_64;
  }
  v50 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v104 = (SEL)objc_retainAutoreleasedReturnValue(v50);
  v85 = 0LL;
  v86 = 0LL;
  v87 = 0LL;
  v88 = 0LL;
  v106 = v51;
  obj = objc_retain(v51);
  v52 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v85, v113, 16LL);
  if ( v52 )
  {
    v53 = v52;
    v101 = *(SEL *)v86;
LABEL_45:
    v99 = "class";
    v100 = "isKindOfClass:";
    v102 = "objectForKeyedSubscript:";
    v109 = "length";
    v110 = "getMarketAndCodeByAppendingMarket:andStockCode:";
    v97 = "numberWithInt:";
    v105 = "dictionaryWithObject:forKey:";
    v98 = "addObject:";
    v95 = v53;
    v54 = 0LL;
    while ( 1 )
    {
      if ( *(SEL *)v86 != v101 )
        objc_enumerationMutation(obj);
      v55 = *(void **)(*((_QWORD *)&v85 + 1) + 8 * v54);
      v56 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v99);
      if ( !(unsigned __int8)_objc_msgSend(v55, v100, v56) )
        break;
      v57 = objc_retain(v55);
      if ( !v57 && !_objc_msgSend(0LL, "count") )
        break;
      v58 = _objc_msgSend(v57, v102, CFSTR("StockCode"));
      v59 = objc_retainAutoreleasedReturnValue(v58);
      v108 = v57;
      v61 = _objc_msgSend(v57, v60, CFSTR("Market"));
      objc_retainAutoreleasedReturnValue(v61);
      v112 = v59;
      v62 = v109;
      v63 = _objc_msgSend(v59, v109);
      v65 = 2;
      if ( v63 )
      {
        v66 = _objc_msgSend(v64, v62);
        v68 = v67;
        if ( v66 )
        {
          v69 = _objc_msgSend(v93, v110, v67, v112);
          v111 = objc_retainAutoreleasedReturnValue(v69);
          if ( _objc_msgSend(v111, v109) )
          {
            v70 = _objc_msgSend(&OBJC_CLASS___NSNumber, v97, 5LL);
            v71 = objc_retainAutoreleasedReturnValue(v70);
            v72 = v111;
            v73 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v105, v111, v71);
            v74 = objc_retainAutoreleasedReturnValue(v73);
            v96 = v68;
            v75 = v74;
            _objc_msgSend((id)v104, v98, v75);
            v77 = v75;
            v68 = (char *)v96;
            v78 = v72;
            v65 = 0;
          }
          else
          {
            v78 = v111;
            v65 = 2;
          }
        }
      }
      else
      {
        v68 = (char *)v64;
      }
      if ( v65 )
        break;
      if ( v95 == (id)++v54 )
      {
        v53 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v85, v113, 16LL);
        if ( v53 )
          goto LABEL_45;
        break;
      }
    }
  }
  v80 = (char *)v104;
  v81 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v104);
  v49 = objc_retainAutoreleasedReturnValue(v81);
  v6 = v103;
LABEL_64:
  return objc_autoreleaseReturnValue(v49);
}

//----- (0000000100B86E0B) ----------------------------------------------------
id __cdecl +[HXTools getItemNameWithDataType:](id a1, SEL a2, id a3)
{
  SEL v3; // r12
  NSDictionary *v504; // rax
  NSDictionary *v505; // r13
  __CFString *v509; // rbx
  _QWORD v760[250]; // [rsp+7D0h] [rbp-FD0h] BYREF
  _QWORD v761[250]; // [rsp+FA0h] [rbp-800h] BYREF

  v559 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, v3, 49LL);
  v511 = objc_retainAutoreleasedReturnValue(v4);
  v760[0] = v511;
  v761[0] = CFSTR("现量");
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, v5, 19LL);
  v512 = objc_retainAutoreleasedReturnValue(v6);
  v760[1] = v512;
  v761[1] = CFSTR("总金额");
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, v7, 18LL);
  v513 = objc_retainAutoreleasedReturnValue(v8);
  v760[2] = v513;
  v761[2] = CFSTR("笔数");
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, v9, 1640904LL);
  v514 = objc_retainAutoreleasedReturnValue(v10);
  v760[3] = v514;
  v761[3] = CFSTR("手/笔");
  v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, v11, 14LL);
  v515 = objc_retainAutoreleasedReturnValue(v12);
  v760[4] = v515;
  v761[4] = CFSTR("外盘");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, v13, 15LL);
  v516 = objc_retainAutoreleasedReturnValue(v14);
  v760[5] = v516;
  v761[5] = CFSTR("内盘");
  v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, v15, 38LL);
  v517 = objc_retainAutoreleasedReturnValue(v16);
  v760[6] = v517;
  v761[6] = CFSTR("涨家数");
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, v17, 39LL);
  v518 = objc_retainAutoreleasedReturnValue(v18);
  v760[7] = v518;
  v761[7] = CFSTR("跌家数");
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, v19, 133778LL);
  v519 = objc_retainAutoreleasedReturnValue(v20);
  v760[8] = v519;
  v761[8] = CFSTR("基差");
  v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, v21, 25LL);
  v520 = objc_retainAutoreleasedReturnValue(v22);
  v760[9] = v520;
  v761[9] = CFSTR("买量");
  v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, v23, 31LL);
  v521 = objc_retainAutoreleasedReturnValue(v24);
  v760[10] = v521;
  v761[10] = CFSTR("卖量");
  v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, v25, 65LL);
  v522 = objc_retainAutoreleasedReturnValue(v26);
  v760[11] = v522;
  v761[11] = CFSTR("持仓");
  v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, v27, 71LL);
  v523 = objc_retainAutoreleasedReturnValue(v28);
  v760[12] = v523;
  v761[12] = CFSTR("现增仓");
  v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, v29, 66LL);
  v524 = objc_retainAutoreleasedReturnValue(v30);
  v760[13] = v524;
  v761[13] = CFSTR("昨结");
  v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, v31, 72LL);
  v525 = objc_retainAutoreleasedReturnValue(v32);
  v760[14] = v525;
  v761[14] = CFSTR("今结");
  v34 = _objc_msgSend(&OBJC_CLASS___NSNumber, v33, 1005LL);
  v526 = objc_retainAutoreleasedReturnValue(v34);
  v760[15] = v526;
  v761[15] = CFSTR("每股净资产");
  v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, v35, 95LL);
  v527 = objc_retainAutoreleasedReturnValue(v36);
  v760[16] = v527;
  v761[16] = CFSTR("52周最高");
  v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, v37, 96LL);
  v528 = objc_retainAutoreleasedReturnValue(v38);
  v760[17] = v528;
  v761[17] = CFSTR("52周最低");
  v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, v39, 3397LL);
  v529 = objc_retainAutoreleasedReturnValue(v40);
  v760[18] = v529;
  v761[18] = CFSTR("净值");
  v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, v41, 2820564LL);
  v530 = objc_retainAutoreleasedReturnValue(v42);
  v760[19] = v530;
  v761[19] = CFSTR("内盘");
  v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, v43, 20LL);
  v531 = objc_retainAutoreleasedReturnValue(v44);
  v760[20] = v531;
  v761[20] = CFSTR("买价");
  v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, v45, 21LL);
  v532 = objc_retainAutoreleasedReturnValue(v46);
  v760[21] = v532;
  v761[21] = CFSTR("卖价");
  v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, v47, 3082712LL);
  v533 = objc_retainAutoreleasedReturnValue(v48);
  v760[22] = v533;
  v761[22] = CFSTR("涨幅(结)%");
  v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, v49, 73LL);
  v534 = objc_retainAutoreleasedReturnValue(v50);
  v760[23] = v534;
  v761[23] = CFSTR("昨持仓");
  v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, v51, 64LL);
  v535 = objc_retainAutoreleasedReturnValue(v52);
  v760[24] = v535;
  v761[24] = CFSTR("状态");
  v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, v53, 899LL);
  v536 = objc_retainAutoreleasedReturnValue(v54);
  v760[25] = v536;
  v761[25] = CFSTR("财务数据项");
  v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, v55, 189546735LL);
  v537 = objc_retainAutoreleasedReturnValue(v56);
  v760[26] = v537;
  v761[26] = CFSTR("计算数据项");
  v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, v57, 223LL);
  v538 = objc_retainAutoreleasedReturnValue(v58);
  v760[27] = v538;
  v761[27] = CFSTR("主动买入特大单金额");
  v60 = _objc_msgSend(&OBJC_CLASS___NSNumber, v59, 225LL);
  v539 = objc_retainAutoreleasedReturnValue(v60);
  v760[28] = v539;
  v761[28] = CFSTR("主买大");
  v62 = _objc_msgSend(&OBJC_CLASS___NSNumber, v61, 259LL);
  v540 = objc_retainAutoreleasedReturnValue(v62);
  v760[29] = v540;
  v761[29] = CFSTR("主买中");
  v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, v63, 237LL);
  v541 = objc_retainAutoreleasedReturnValue(v64);
  v760[30] = v541;
  v761[30] = CFSTR("主买小");
  v66 = _objc_msgSend(&OBJC_CLASS___NSNumber, v65, 227LL);
  v542 = objc_retainAutoreleasedReturnValue(v66);
  v760[31] = v542;
  v761[31] = CFSTR("被买特");
  v68 = _objc_msgSend(&OBJC_CLASS___NSNumber, v67, 229LL);
  v543 = objc_retainAutoreleasedReturnValue(v68);
  v760[32] = v543;
  v761[32] = CFSTR("被买大");
  v70 = _objc_msgSend(&OBJC_CLASS___NSNumber, v69, 261LL);
  v544 = objc_retainAutoreleasedReturnValue(v70);
  v760[33] = v544;
  v761[33] = CFSTR("被买中");
  v72 = _objc_msgSend(&OBJC_CLASS___NSNumber, v71, 224LL);
  v545 = objc_retainAutoreleasedReturnValue(v72);
  v760[34] = v545;
  v761[34] = CFSTR("主动卖出特大单金额");
  v74 = _objc_msgSend(&OBJC_CLASS___NSNumber, v73, 226LL);
  v546 = objc_retainAutoreleasedReturnValue(v74);
  v760[35] = v546;
  v761[35] = CFSTR("主卖大");
  v76 = _objc_msgSend(&OBJC_CLASS___NSNumber, v75, 260LL);
  v547 = objc_retainAutoreleasedReturnValue(v76);
  v760[36] = v547;
  v761[36] = CFSTR("主卖中");
  v78 = _objc_msgSend(&OBJC_CLASS___NSNumber, v77, 238LL);
  v548 = objc_retainAutoreleasedReturnValue(v78);
  v760[37] = v548;
  v761[37] = CFSTR("主卖小");
  v80 = _objc_msgSend(&OBJC_CLASS___NSNumber, v79, 228LL);
  v549 = objc_retainAutoreleasedReturnValue(v80);
  v760[38] = v549;
  v761[38] = CFSTR("被卖特");
  v82 = _objc_msgSend(&OBJC_CLASS___NSNumber, v81, 230LL);
  v550 = objc_retainAutoreleasedReturnValue(v82);
  v760[39] = v550;
  v761[39] = CFSTR("被卖大");
  v84 = _objc_msgSend(&OBJC_CLASS___NSNumber, v83, 262LL);
  v551 = objc_retainAutoreleasedReturnValue(v84);
  v760[40] = v551;
  v761[40] = CFSTR("被卖中");
  v86 = _objc_msgSend(&OBJC_CLASS___NSNumber, v85, 24LL);
  v552 = objc_retainAutoreleasedReturnValue(v86);
  v760[41] = v552;
  v761[41] = CFSTR("买一");
  v88 = _objc_msgSend(&OBJC_CLASS___NSNumber, v87, 26LL);
  v553 = objc_retainAutoreleasedReturnValue(v88);
  v760[42] = v553;
  v761[42] = CFSTR("买二");
  v90 = _objc_msgSend(&OBJC_CLASS___NSNumber, v89, 28LL);
  v554 = objc_retainAutoreleasedReturnValue(v90);
  v760[43] = v554;
  v761[43] = CFSTR("买三");
  v92 = _objc_msgSend(&OBJC_CLASS___NSNumber, v91, 150LL);
  v555 = objc_retainAutoreleasedReturnValue(v92);
  v760[44] = v555;
  v761[44] = CFSTR("买四");
  v94 = _objc_msgSend(&OBJC_CLASS___NSNumber, v93, 154LL);
  v556 = objc_retainAutoreleasedReturnValue(v94);
  v760[45] = v556;
  v761[45] = CFSTR("买五");
  v96 = _objc_msgSend(&OBJC_CLASS___NSNumber, v95, 25LL);
  v557 = objc_retainAutoreleasedReturnValue(v96);
  v760[46] = v557;
  v761[46] = CFSTR("买一量");
  v98 = _objc_msgSend(&OBJC_CLASS___NSNumber, v97, 27LL);
  v558 = objc_retainAutoreleasedReturnValue(v98);
  v760[47] = v558;
  v761[47] = CFSTR("买二量");
  v100 = _objc_msgSend(&OBJC_CLASS___NSNumber, v99, 29LL);
  v560 = objc_retainAutoreleasedReturnValue(v100);
  v760[48] = v560;
  v761[48] = CFSTR("买三量");
  v102 = _objc_msgSend(&OBJC_CLASS___NSNumber, v101, 151LL);
  v561 = objc_retainAutoreleasedReturnValue(v102);
  v760[49] = v561;
  v761[49] = CFSTR("买四量");
  v104 = _objc_msgSend(&OBJC_CLASS___NSNumber, v103, 155LL);
  v562 = objc_retainAutoreleasedReturnValue(v104);
  v760[50] = v562;
  v761[50] = CFSTR("买五量");
  v106 = _objc_msgSend(&OBJC_CLASS___NSNumber, v105, 30LL);
  v563 = objc_retainAutoreleasedReturnValue(v106);
  v760[51] = v563;
  v761[51] = CFSTR("卖一");
  v108 = _objc_msgSend(&OBJC_CLASS___NSNumber, v107, 32LL);
  v564 = objc_retainAutoreleasedReturnValue(v108);
  v760[52] = v564;
  v761[52] = CFSTR("卖二");
  v110 = _objc_msgSend(&OBJC_CLASS___NSNumber, v109, 34LL);
  v565 = objc_retainAutoreleasedReturnValue(v110);
  v760[53] = v565;
  v761[53] = CFSTR("卖三");
  v112 = _objc_msgSend(&OBJC_CLASS___NSNumber, v111, 152LL);
  v566 = objc_retainAutoreleasedReturnValue(v112);
  v760[54] = v566;
  v761[54] = CFSTR("卖四");
  v114 = _objc_msgSend(&OBJC_CLASS___NSNumber, v113, 156LL);
  v567 = objc_retainAutoreleasedReturnValue(v114);
  v760[55] = v567;
  v761[55] = CFSTR("卖五");
  v116 = _objc_msgSend(&OBJC_CLASS___NSNumber, v115, 31LL);
  v568 = objc_retainAutoreleasedReturnValue(v116);
  v760[56] = v568;
  v761[56] = CFSTR("卖一量");
  v118 = _objc_msgSend(&OBJC_CLASS___NSNumber, v117, 33LL);
  v569 = objc_retainAutoreleasedReturnValue(v118);
  v760[57] = v569;
  v761[57] = CFSTR("卖二量");
  v120 = _objc_msgSend(&OBJC_CLASS___NSNumber, v119, 35LL);
  v570 = objc_retainAutoreleasedReturnValue(v120);
  v760[58] = v570;
  v761[58] = CFSTR("卖三量");
  v122 = _objc_msgSend(&OBJC_CLASS___NSNumber, v121, 153LL);
  v571 = objc_retainAutoreleasedReturnValue(v122);
  v760[59] = v571;
  v761[59] = CFSTR("卖四量");
  v124 = _objc_msgSend(&OBJC_CLASS___NSNumber, v123, 157LL);
  v572 = objc_retainAutoreleasedReturnValue(v124);
  v760[60] = v572;
  v761[60] = CFSTR("卖五量");
  v126 = _objc_msgSend(&OBJC_CLASS___NSNumber, v125, 160LL);
  v573 = objc_retainAutoreleasedReturnValue(v126);
  v760[61] = v573;
  v761[61] = CFSTR("市场分层");
  v128 = _objc_msgSend(&OBJC_CLASS___NSNumber, v127, 11LL);
  v574 = objc_retainAutoreleasedReturnValue(v128);
  v760[62] = v574;
  v761[62] = CFSTR("收盘");
  v130 = _objc_msgSend(&OBJC_CLASS___NSNumber, v129, 41LL);
  v575 = objc_retainAutoreleasedReturnValue(v130);
  v760[63] = v575;
  v761[63] = CFSTR("红绿柱");
  v132 = _objc_msgSend(&OBJC_CLASS___NSNumber, v131, 85LL);
  v576 = objc_retainAutoreleasedReturnValue(v132);
  v760[64] = v576;
  v761[64] = CFSTR("盈利情况");
  v134 = _objc_msgSend(&OBJC_CLASS___NSNumber, v133, 2018090320LL);
  v577 = objc_retainAutoreleasedReturnValue(v134);
  v760[65] = v577;
  v761[65] = CFSTR("竞价异动类型及说明颜色判断字段");
  v136 = _objc_msgSend(&OBJC_CLASS___NSNumber, v135, 900LL);
  v578 = objc_retainAutoreleasedReturnValue(v136);
  v760[66] = v578;
  v761[66] = CFSTR("流通股变动量");
  v138 = _objc_msgSend(&OBJC_CLASS___NSNumber, v137, 70LL);
  v579 = objc_retainAutoreleasedReturnValue(v138);
  v760[67] = v579;
  v761[67] = CFSTR("跌停");
  v140 = _objc_msgSend(&OBJC_CLASS___NSNumber, v139, 69LL);
  v580 = objc_retainAutoreleasedReturnValue(v140);
  v760[68] = v580;
  v761[68] = CFSTR("涨停");
  v142 = _objc_msgSend(&OBJC_CLASS___NSNumber, v141, 45LL);
  v581 = objc_retainAutoreleasedReturnValue(v142);
  v760[69] = v581;
  v761[69] = CFSTR("五日成交总量");
  v144 = _objc_msgSend(&OBJC_CLASS___NSNumber, v143, 471LL);
  v582 = objc_retainAutoreleasedReturnValue(v144);
  v760[70] = v582;
  v761[70] = CFSTR("权息资料");
  v146 = _objc_msgSend(&OBJC_CLASS___NSNumber, v145, 22LL);
  v583 = objc_retainAutoreleasedReturnValue(v146);
  v760[71] = v583;
  v761[71] = CFSTR("委买");
  v148 = _objc_msgSend(&OBJC_CLASS___NSNumber, v147, 23LL);
  v584 = objc_retainAutoreleasedReturnValue(v148);
  v760[72] = v584;
  v761[72] = CFSTR("委卖");
  v150 = _objc_msgSend(&OBJC_CLASS___NSNumber, v149, 251LL);
  v585 = objc_retainAutoreleasedReturnValue(v150);
  v760[73] = v585;
  v761[73] = CFSTR("卖出金额");
  v152 = _objc_msgSend(&OBJC_CLASS___NSNumber, v151, 250LL);
  v586 = objc_retainAutoreleasedReturnValue(v152);
  v760[74] = v586;
  v761[74] = CFSTR("买入金额");
  v154 = _objc_msgSend(&OBJC_CLASS___NSNumber, v153, 981LL);
  v587 = objc_retainAutoreleasedReturnValue(v154);
  v760[75] = v587;
  v761[75] = CFSTR("港元->人民币汇率");
  v156 = _objc_msgSend(&OBJC_CLASS___NSNumber, v155, 89LL);
  v588 = objc_retainAutoreleasedReturnValue(v156);
  v760[76] = v588;
  v761[76] = CFSTR("转让状态相关参数");
  v158 = _objc_msgSend(&OBJC_CLASS___NSNumber, v157, 54LL);
  v589 = objc_retainAutoreleasedReturnValue(v158);
  v760[77] = v589;
  v761[77] = CFSTR("均价(中金所专用)");
  v160 = _objc_msgSend(&OBJC_CLASS___NSNumber, v159, 40LL);
  v590 = objc_retainAutoreleasedReturnValue(v160);
  v760[78] = v590;
  v761[78] = CFSTR("领先指标");
  v162 = _objc_msgSend(&OBJC_CLASS___NSNumber, v161, 148LL);
  v591 = objc_retainAutoreleasedReturnValue(v162);
  v760[79] = v591;
  v761[79] = CFSTR("期权认购认沽");
  v164 = _objc_msgSend(&OBJC_CLASS___NSNumber, v163, 91LL);
  v592 = objc_retainAutoreleasedReturnValue(v164);
  v760[80] = v592;
  v761[80] = CFSTR("市盈率");
  v166 = _objc_msgSend(&OBJC_CLASS___NSNumber, v165, 5LL);
  v593 = objc_retainAutoreleasedReturnValue(v166);
  v760[81] = v593;
  v761[81] = CFSTR("代码");
  v168 = _objc_msgSend(&OBJC_CLASS___NSNumber, v167, 199112LL);
  v594 = objc_retainAutoreleasedReturnValue(v168);
  v760[82] = v594;
  v761[82] = CFSTR("涨幅%");
  v170 = _objc_msgSend(&OBJC_CLASS___NSNumber, v169, 3250LL);
  v595 = objc_retainAutoreleasedReturnValue(v170);
  v760[83] = v595;
  v761[83] = CFSTR("5日涨幅");
  v172 = _objc_msgSend(&OBJC_CLASS___NSNumber, v171, 3251LL);
  v596 = objc_retainAutoreleasedReturnValue(v172);
  v760[84] = v596;
  v761[84] = CFSTR("10日涨幅");
  v174 = _objc_msgSend(&OBJC_CLASS___NSNumber, v173, 3252LL);
  v597 = objc_retainAutoreleasedReturnValue(v174);
  v760[85] = v597;
  v761[85] = CFSTR("20日涨幅");
  v176 = _objc_msgSend(&OBJC_CLASS___NSNumber, v175, 1322LL);
  v598 = objc_retainAutoreleasedReturnValue(v176);
  v760[86] = v598;
  v761[86] = CFSTR("利率%");
  v178 = _objc_msgSend(&OBJC_CLASS___NSNumber, v177, 80LL);
  v599 = objc_retainAutoreleasedReturnValue(v178);
  v760[87] = v599;
  v761[87] = CFSTR("利息");
  v180 = _objc_msgSend(&OBJC_CLASS___NSNumber, v179, 12345671LL);
  v600 = objc_retainAutoreleasedReturnValue(v180);
  v760[88] = v600;
  v761[88] = CFSTR("A股关联主题");
  v182 = _objc_msgSend(&OBJC_CLASS___NSNumber, v181, 4065737LL);
  v601 = objc_retainAutoreleasedReturnValue(v182);
  v760[89] = v601;
  v761[89] = CFSTR("买价");
  v184 = _objc_msgSend(&OBJC_CLASS___NSNumber, v183, 4131273LL);
  v602 = objc_retainAutoreleasedReturnValue(v184);
  v760[90] = v602;
  v761[90] = CFSTR("卖价");
  v186 = _objc_msgSend(&OBJC_CLASS___NSNumber, v185, 264648LL);
  v603 = objc_retainAutoreleasedReturnValue(v186);
  v760[91] = v603;
  v761[91] = CFSTR("涨跌");
  v188 = _objc_msgSend(&OBJC_CLASS___NSNumber, v187, 6LL);
  v604 = objc_retainAutoreleasedReturnValue(v188);
  v760[92] = v604;
  v761[92] = CFSTR("昨收");
  v190 = _objc_msgSend(&OBJC_CLASS___NSNumber, v189, 7LL);
  v605 = objc_retainAutoreleasedReturnValue(v190);
  v760[93] = v605;
  v761[93] = CFSTR("开盘");
  v192 = _objc_msgSend(&OBJC_CLASS___NSNumber, v191, 8LL);
  v606 = objc_retainAutoreleasedReturnValue(v192);
  v760[94] = v606;
  v761[94] = CFSTR("最高");
  v194 = _objc_msgSend(&OBJC_CLASS___NSNumber, v193, 9LL);
  v607 = objc_retainAutoreleasedReturnValue(v194);
  v760[95] = v607;
  v761[95] = CFSTR("最低");
  v196 = _objc_msgSend(&OBJC_CLASS___NSNumber, v195, 10LL);
  v608 = objc_retainAutoreleasedReturnValue(v196);
  v760[96] = v608;
  v761[96] = CFSTR("现价");
  v198 = _objc_msgSend(&OBJC_CLASS___NSNumber, v197, 13LL);
  v609 = objc_retainAutoreleasedReturnValue(v198);
  v760[97] = v609;
  v761[97] = CFSTR("总量");
  v200 = _objc_msgSend(&OBJC_CLASS___NSNumber, v199, 48LL);
  v610 = objc_retainAutoreleasedReturnValue(v200);
  v760[98] = v610;
  v761[98] = CFSTR("涨速%");
  v202 = _objc_msgSend(&OBJC_CLASS___NSNumber, v201, 3934664LL);
  v611 = objc_retainAutoreleasedReturnValue(v202);
  v760[99] = v611;
  v761[99] = CFSTR("涨速%");
  v204 = _objc_msgSend(&OBJC_CLASS___NSNumber, v203, 526792LL);
  v612 = objc_retainAutoreleasedReturnValue(v204);
  v760[100] = v612;
  v761[100] = CFSTR("振幅%");
  v206 = _objc_msgSend(&OBJC_CLASS___NSNumber, v205, 55LL);
  v613 = objc_retainAutoreleasedReturnValue(v206);
  v760[101] = v613;
  v761[101] = CFSTR("名称");
  v208 = _objc_msgSend(&OBJC_CLASS___NSNumber, v207, 1771976LL);
  v614 = objc_retainAutoreleasedReturnValue(v208);
  v760[102] = v614;
  v761[102] = CFSTR("量比");
  v210 = _objc_msgSend(&OBJC_CLASS___NSNumber, v209, 84LL);
  v615 = objc_retainAutoreleasedReturnValue(v210);
  v760[103] = v615;
  v761[103] = CFSTR("所属行业");
  v212 = _objc_msgSend(&OBJC_CLASS___NSNumber, v211, 92LL);
  v616 = objc_retainAutoreleasedReturnValue(v212);
  v760[104] = v616;
  v761[104] = CFSTR("总市值");
  v214 = _objc_msgSend(&OBJC_CLASS___NSNumber, v213, 90LL);
  v617 = objc_retainAutoreleasedReturnValue(v214);
  v760[105] = v617;
  v761[105] = CFSTR("流通市值");
  v216 = _objc_msgSend(&OBJC_CLASS___NSNumber, v215, 402LL);
  v618 = objc_retainAutoreleasedReturnValue(v216);
  v760[106] = v618;
  v761[106] = CFSTR("总股本");
  v218 = _objc_msgSend(&OBJC_CLASS___NSNumber, v217, 407LL);
  v619 = objc_retainAutoreleasedReturnValue(v218);
  v760[107] = v619;
  v761[107] = CFSTR("流通股本");
  v220 = _objc_msgSend(&OBJC_CLASS___NSNumber, v219, 199643LL);
  v620 = objc_retainAutoreleasedReturnValue(v220);
  v760[108] = v620;
  v761[108] = CFSTR("大单净量");
  v222 = _objc_msgSend(&OBJC_CLASS___NSNumber, v221, 920371LL);
  v621 = objc_retainAutoreleasedReturnValue(v222);
  v760[109] = v621;
  v761[109] = CFSTR("开盘涨幅");
  v224 = _objc_msgSend(&OBJC_CLASS___NSNumber, v223, 461256LL);
  v622 = objc_retainAutoreleasedReturnValue(v224);
  v760[110] = v622;
  v761[110] = CFSTR("委比%");
  v226 = _objc_msgSend(&OBJC_CLASS___NSNumber, v225, 1968584LL);
  v623 = objc_retainAutoreleasedReturnValue(v226);
  v760[111] = v623;
  v761[111] = CFSTR("换手%");
  v228 = _objc_msgSend(&OBJC_CLASS___NSNumber, v227, 2427336LL);
  v624 = objc_retainAutoreleasedReturnValue(v228);
  v760[112] = v624;
  v761[112] = CFSTR("均笔额");
  v230 = _objc_msgSend(&OBJC_CLASS___NSNumber, v229, 24LL);
  v625 = objc_retainAutoreleasedReturnValue(v230);
  v760[113] = v625;
  v761[113] = CFSTR("买一价");
  v232 = _objc_msgSend(&OBJC_CLASS___NSNumber, v231, 30LL);
  v626 = objc_retainAutoreleasedReturnValue(v232);
  v760[114] = v626;
  v761[114] = CFSTR("卖一价");
  v234 = _objc_msgSend(&OBJC_CLASS___NSNumber, v233, 1509847LL);
  v627 = objc_retainAutoreleasedReturnValue(v234);
  v760[115] = v627;
  v761[115] = CFSTR("户均持股数");
  v236 = _objc_msgSend(&OBJC_CLASS___NSNumber, v235, 1002LL);
  v628 = objc_retainAutoreleasedReturnValue(v236);
  v760[116] = v628;
  v761[116] = CFSTR("每股收益");
  v238 = _objc_msgSend(&OBJC_CLASS___NSNumber, v237, 1005LL);
  v629 = objc_retainAutoreleasedReturnValue(v238);
  v760[117] = v629;
  v761[117] = CFSTR("每股净资产");
  v240 = _objc_msgSend(&OBJC_CLASS___NSNumber, v239, 1015LL);
  v630 = objc_retainAutoreleasedReturnValue(v240);
  v760[118] = v630;
  v761[118] = CFSTR("净资产收益");
  v242 = _objc_msgSend(&OBJC_CLASS___NSNumber, v241, 619LL);
  v631 = objc_retainAutoreleasedReturnValue(v242);
  v760[119] = v631;
  v761[119] = CFSTR("净利润");
  v244 = _objc_msgSend(&OBJC_CLASS___NSNumber, v243, 1566LL);
  v632 = objc_retainAutoreleasedReturnValue(v244);
  v760[120] = v632;
  v761[120] = CFSTR("净利润");
  v246 = _objc_msgSend(&OBJC_CLASS___NSNumber, v245, 1670LL);
  v633 = objc_retainAutoreleasedReturnValue(v246);
  v760[121] = v633;
  v761[121] = CFSTR("股东总数");
  v248 = _objc_msgSend(&OBJC_CLASS___NSNumber, v247, 2947LL);
  v634 = objc_retainAutoreleasedReturnValue(v248);
  v760[122] = v634;
  v761[122] = CFSTR("市净率");
  v250 = _objc_msgSend(&OBJC_CLASS___NSNumber, v249, 3541450LL);
  v635 = objc_retainAutoreleasedReturnValue(v250);
  v760[123] = v635;
  v761[123] = CFSTR("总市值");
  v252 = _objc_msgSend(&OBJC_CLASS___NSNumber, v251, 3475914LL);
  v636 = objc_retainAutoreleasedReturnValue(v252);
  v760[124] = v636;
  v761[124] = CFSTR("流通市值");
  v254 = _objc_msgSend(&OBJC_CLASS___NSNumber, v253, 134141LL);
  v637 = objc_retainAutoreleasedReturnValue(v254);
  v760[125] = v637;
  v761[125] = CFSTR("净利润增长率");
  v256 = _objc_msgSend(&OBJC_CLASS___NSNumber, v255, 134143LL);
  v638 = objc_retainAutoreleasedReturnValue(v256);
  v760[126] = v638;
  v761[126] = CFSTR("营业收入增长率");
  v258 = _objc_msgSend(&OBJC_CLASS___NSNumber, v257, 605LL);
  v639 = objc_retainAutoreleasedReturnValue(v258);
  v760[127] = v639;
  v761[127] = CFSTR("营业利润");
  v260 = _objc_msgSend(&OBJC_CLASS___NSNumber, v259, 2034121LL);
  v640 = objc_retainAutoreleasedReturnValue(v260);
  v760[128] = v640;
  v761[128] = CFSTR("资产负债率");
  v262 = _objc_msgSend(&OBJC_CLASS___NSNumber, v261, 2034120LL);
  v641 = objc_retainAutoreleasedReturnValue(v262);
  v760[129] = v641;
  v761[129] = CFSTR("市盈(动)");
  v264 = _objc_msgSend(&OBJC_CLASS___NSNumber, v263, 2946LL);
  v642 = objc_retainAutoreleasedReturnValue(v264);
  v760[130] = v642;
  v761[130] = CFSTR("市盈(静)");
  v266 = _objc_msgSend(&OBJC_CLASS___NSNumber, v265, 4525375LL);
  v643 = objc_retainAutoreleasedReturnValue(v266);
  v760[131] = v643;
  v761[131] = CFSTR("小单流入");
  v268 = _objc_msgSend(&OBJC_CLASS___NSNumber, v267, 4525376LL);
  v644 = objc_retainAutoreleasedReturnValue(v268);
  v760[132] = v644;
  v761[132] = CFSTR("中单流入");
  v270 = _objc_msgSend(&OBJC_CLASS___NSNumber, v269, 4525377LL);
  v645 = objc_retainAutoreleasedReturnValue(v270);
  v760[133] = v645;
  v761[133] = CFSTR("大单流入");
  v272 = _objc_msgSend(&OBJC_CLASS___NSNumber, v271, 8719679LL);
  v646 = objc_retainAutoreleasedReturnValue(v272);
  v760[134] = v646;
  v761[134] = CFSTR("小单流出");
  v274 = _objc_msgSend(&OBJC_CLASS___NSNumber, v273, 8719680LL);
  v647 = objc_retainAutoreleasedReturnValue(v274);
  v760[135] = v647;
  v761[135] = CFSTR("中单流出");
  v276 = _objc_msgSend(&OBJC_CLASS___NSNumber, v275, 8719681LL);
  v648 = objc_retainAutoreleasedReturnValue(v276);
  v760[136] = v648;
  v761[136] = CFSTR("大单流出");
  v278 = _objc_msgSend(&OBJC_CLASS___NSNumber, v277, 12913983LL);
  v649 = objc_retainAutoreleasedReturnValue(v278);
  v760[137] = v649;
  v761[137] = CFSTR("小单净额");
  v280 = _objc_msgSend(&OBJC_CLASS___NSNumber, v279, 12913984LL);
  v650 = objc_retainAutoreleasedReturnValue(v280);
  v760[138] = v650;
  v761[138] = CFSTR("中单净额");
  v282 = _objc_msgSend(&OBJC_CLASS___NSNumber, v281, 12913985LL);
  v651 = objc_retainAutoreleasedReturnValue(v282);
  v760[139] = v651;
  v761[139] = CFSTR("大单净额");
  v284 = _objc_msgSend(&OBJC_CLASS___NSNumber, v283, 17108287LL);
  v652 = objc_retainAutoreleasedReturnValue(v284);
  v760[140] = v652;
  v761[140] = CFSTR("小单净额占比%");
  v286 = _objc_msgSend(&OBJC_CLASS___NSNumber, v285, 17108288LL);
  v653 = objc_retainAutoreleasedReturnValue(v286);
  v760[141] = v653;
  v761[141] = CFSTR("中单净额占比%");
  v288 = _objc_msgSend(&OBJC_CLASS___NSNumber, v287, 17108289LL);
  v654 = objc_retainAutoreleasedReturnValue(v288);
  v760[142] = v654;
  v761[142] = CFSTR("大单净额占比%");
  v290 = _objc_msgSend(&OBJC_CLASS___NSNumber, v289, 21302591LL);
  v655 = objc_retainAutoreleasedReturnValue(v290);
  v760[143] = v655;
  v761[143] = CFSTR("小单总额");
  v292 = _objc_msgSend(&OBJC_CLASS___NSNumber, v291, 21302592LL);
  v656 = objc_retainAutoreleasedReturnValue(v292);
  v760[144] = v656;
  v761[144] = CFSTR("中单总额");
  v294 = _objc_msgSend(&OBJC_CLASS___NSNumber, v293, 21302593LL);
  v657 = objc_retainAutoreleasedReturnValue(v294);
  v760[145] = v657;
  v761[145] = CFSTR("大单总额");
  v296 = _objc_msgSend(&OBJC_CLASS___NSNumber, v295, 25496895LL);
  v658 = objc_retainAutoreleasedReturnValue(v296);
  v760[146] = v658;
  v761[146] = CFSTR("小单总额占比%");
  v298 = _objc_msgSend(&OBJC_CLASS___NSNumber, v297, 25496896LL);
  v659 = objc_retainAutoreleasedReturnValue(v298);
  v760[147] = v659;
  v761[147] = CFSTR("中单总额占比%");
  v300 = _objc_msgSend(&OBJC_CLASS___NSNumber, v299, 25496897LL);
  v660 = objc_retainAutoreleasedReturnValue(v300);
  v760[148] = v660;
  v761[148] = CFSTR("大单总额占比%");
  v302 = _objc_msgSend(&OBJC_CLASS___NSNumber, v301, 331070LL);
  v661 = objc_retainAutoreleasedReturnValue(v302);
  v760[149] = v661;
  v761[149] = CFSTR("今日主力增仓占比%");
  v304 = _objc_msgSend(&OBJC_CLASS___NSNumber, v303, 331128LL);
  v662 = objc_retainAutoreleasedReturnValue(v304);
  v760[150] = v662;
  v761[150] = CFSTR("今日主力增仓排名");
  v306 = _objc_msgSend(&OBJC_CLASS___NSNumber, v305, 331077LL);
  v663 = objc_retainAutoreleasedReturnValue(v306);
  v760[151] = v663;
  v761[151] = CFSTR("2日主力增仓占比%");
  v308 = _objc_msgSend(&OBJC_CLASS___NSNumber, v307, 331124LL);
  v664 = objc_retainAutoreleasedReturnValue(v308);
  v760[152] = v664;
  v761[152] = CFSTR("2日主力增仓排名");
  v310 = _objc_msgSend(&OBJC_CLASS___NSNumber, v309, 331078LL);
  v665 = objc_retainAutoreleasedReturnValue(v310);
  v760[153] = v665;
  v761[153] = CFSTR("3日主力增仓占比%");
  v312 = _objc_msgSend(&OBJC_CLASS___NSNumber, v311, 331125LL);
  v666 = objc_retainAutoreleasedReturnValue(v312);
  v760[154] = v666;
  v761[154] = CFSTR("3日主力增仓排名");
  v314 = _objc_msgSend(&OBJC_CLASS___NSNumber, v313, 331079LL);
  v667 = objc_retainAutoreleasedReturnValue(v314);
  v760[155] = v667;
  v761[155] = CFSTR("5日主力增仓占比%");
  v316 = _objc_msgSend(&OBJC_CLASS___NSNumber, v315, 331126LL);
  v668 = objc_retainAutoreleasedReturnValue(v316);
  v760[156] = v668;
  v761[156] = CFSTR("5日主力增仓排名");
  v318 = _objc_msgSend(&OBJC_CLASS___NSNumber, v317, 331080LL);
  v669 = objc_retainAutoreleasedReturnValue(v318);
  v760[157] = v669;
  v761[157] = CFSTR("10日主力增仓占比%");
  v320 = _objc_msgSend(&OBJC_CLASS___NSNumber, v319, 331127LL);
  v670 = objc_retainAutoreleasedReturnValue(v320);
  v760[158] = v670;
  v761[158] = CFSTR("10日主力增仓排名");
  v322 = _objc_msgSend(&OBJC_CLASS___NSNumber, v321, 592888LL);
  v671 = objc_retainAutoreleasedReturnValue(v322);
  v760[159] = v671;
  v761[159] = CFSTR("主力净量");
  v324 = _objc_msgSend(&OBJC_CLASS___NSNumber, v323, 68285LL);
  v672 = objc_retainAutoreleasedReturnValue(v324);
  v760[160] = v672;
  v761[160] = CFSTR("主力净量(板块)");
  v326 = _objc_msgSend(&OBJC_CLASS___NSNumber, v325, 462057LL);
  v673 = objc_retainAutoreleasedReturnValue(v326);
  v760[161] = v673;
  v761[161] = CFSTR("散户数量");
  v328 = _objc_msgSend(&OBJC_CLASS___NSNumber, v327, 592893LL);
  v674 = objc_retainAutoreleasedReturnValue(v328);
  v760[162] = v674;
  v761[162] = CFSTR("5日大单净量");
  v330 = _objc_msgSend(&OBJC_CLASS___NSNumber, v329, 592894LL);
  v675 = objc_retainAutoreleasedReturnValue(v330);
  v760[163] = v675;
  v761[163] = CFSTR("10日大单净量");
  v332 = _objc_msgSend(&OBJC_CLASS___NSNumber, v331, 592890LL);
  v676 = objc_retainAutoreleasedReturnValue(v332);
  v760[164] = v676;
  v761[164] = CFSTR("主力净流入");
  v334 = _objc_msgSend(&OBJC_CLASS___NSNumber, v333, 68166LL);
  v677 = objc_retainAutoreleasedReturnValue(v334);
  v760[165] = v677;
  v761[165] = CFSTR("主力流入");
  v336 = _objc_msgSend(&OBJC_CLASS___NSNumber, v335, 68167LL);
  v678 = objc_retainAutoreleasedReturnValue(v336);
  v760[166] = v678;
  v761[166] = CFSTR("主力流出");
  v338 = _objc_msgSend(&OBJC_CLASS___NSNumber, v337, 9810LL);
  v679 = objc_retainAutoreleasedReturnValue(v338);
  v760[167] = v679;
  v761[167] = CFSTR("溢价率");
  v340 = _objc_msgSend(&OBJC_CLASS___NSNumber, v339, 130LL);
  v680 = objc_retainAutoreleasedReturnValue(v340);
  v760[168] = v680;
  v761[168] = CFSTR("总手(港)");
  v342 = _objc_msgSend(&OBJC_CLASS___NSNumber, v341, 2646480LL);
  v681 = objc_retainAutoreleasedReturnValue(v342);
  v760[169] = v681;
  v761[169] = CFSTR("H股涨跌");
  v344 = _objc_msgSend(&OBJC_CLASS___NSNumber, v343, 1991120LL);
  v682 = objc_retainAutoreleasedReturnValue(v344);
  v760[170] = v682;
  v761[170] = CFSTR("涨幅(港)");
  v346 = _objc_msgSend(&OBJC_CLASS___NSNumber, v345, 100LL);
  v683 = objc_retainAutoreleasedReturnValue(v346);
  v760[171] = v683;
  v761[171] = CFSTR("现价(港)");
  v348 = _objc_msgSend(&OBJC_CLASS___NSNumber, v347, 60LL);
  v684 = objc_retainAutoreleasedReturnValue(v348);
  v760[172] = v684;
  v761[172] = CFSTR("H股昨收");
  v350 = _objc_msgSend(&OBJC_CLASS___NSNumber, v349, 527527LL);
  v685 = objc_retainAutoreleasedReturnValue(v350);
  v760[173] = v685;
  v761[173] = CFSTR("涨速%(1分)");
  v352 = _objc_msgSend(&OBJC_CLASS___NSNumber, v351, 527526LL);
  v686 = objc_retainAutoreleasedReturnValue(v352);
  v760[174] = v686;
  v761[174] = CFSTR("涨速%(3分)");
  v354 = _objc_msgSend(&OBJC_CLASS___NSNumber, v353, 461438LL);
  v687 = objc_retainAutoreleasedReturnValue(v354);
  v760[175] = v687;
  v761[175] = CFSTR("涨速%(10分)");
  v356 = _objc_msgSend(&OBJC_CLASS___NSNumber, v355, 461439LL);
  v688 = objc_retainAutoreleasedReturnValue(v356);
  v760[176] = v688;
  v761[176] = CFSTR("涨速%(15分)");
  v358 = _objc_msgSend(&OBJC_CLASS___NSNumber, v357, 1110LL);
  v689 = objc_retainAutoreleasedReturnValue(v358);
  v760[177] = v689;
  v761[177] = CFSTR("星级");
  v360 = _objc_msgSend(&OBJC_CLASS___NSNumber, v359, 4099083LL);
  v690 = objc_retainAutoreleasedReturnValue(v360);
  v760[178] = v690;
  v761[178] = CFSTR("日增仓");
  v362 = _objc_msgSend(&OBJC_CLASS___NSNumber, v361, 12LL);
  v691 = objc_retainAutoreleasedReturnValue(v362);
  v760[179] = v691;
  v761[179] = CFSTR("成交量分类");
  v364 = _objc_msgSend(&OBJC_CLASS___NSNumber, v363, 133964LL);
  v692 = objc_retainAutoreleasedReturnValue(v364);
  v760[180] = v692;
  v761[180] = CFSTR("日增仓");
  v366 = _objc_msgSend(&OBJC_CLASS___NSNumber, v365, 50LL);
  v693 = objc_retainAutoreleasedReturnValue(v366);
  v760[181] = v693;
  v761[181] = CFSTR("代码(港)");
  v368 = _objc_msgSend(&OBJC_CLASS___NSNumber, v367, 3153LL);
  v694 = objc_retainAutoreleasedReturnValue(v368);
  v760[182] = v694;
  v761[182] = CFSTR("市盈TTM");
  v370 = _objc_msgSend(&OBJC_CLASS___NSNumber, v369, 134071LL);
  v695 = objc_retainAutoreleasedReturnValue(v370);
  v760[183] = v695;
  v761[183] = CFSTR("市销TTM");
  v372 = _objc_msgSend(&OBJC_CLASS___NSNumber, v371, 134072LL);
  v696 = objc_retainAutoreleasedReturnValue(v372);
  v760[184] = v696;
  v761[184] = CFSTR("净资产收益TTM");
  v374 = _objc_msgSend(&OBJC_CLASS___NSNumber, v373, 61LL);
  v697 = objc_retainAutoreleasedReturnValue(v374);
  v760[185] = v697;
  v761[185] = CFSTR("异动类型");
  v376 = _objc_msgSend(&OBJC_CLASS___NSNumber, v375, 1LL);
  v698 = objc_retainAutoreleasedReturnValue(v376);
  v760[186] = v698;
  v761[186] = CFSTR("时间");
  v378 = _objc_msgSend(&OBJC_CLASS___NSNumber, v377, 2018090319LL);
  v699 = objc_retainAutoreleasedReturnValue(v378);
  v760[187] = v699;
  v761[187] = CFSTR("竞价评级");
  v380 = _objc_msgSend(&OBJC_CLASS___NSNumber, v379, 2018090410LL);
  v700 = objc_retainAutoreleasedReturnValue(v380);
  v760[188] = v700;
  v761[188] = CFSTR("异动说明");
  v382 = _objc_msgSend(&OBJC_CLASS___NSNumber, v381, 2018090411LL);
  v701 = objc_retainAutoreleasedReturnValue(v382);
  v760[189] = v701;
  v761[189] = CFSTR("竞价涨幅");
  v384 = _objc_msgSend(&OBJC_CLASS___NSNumber, v383, 1606LL);
  v702 = objc_retainAutoreleasedReturnValue(v384);
  v760[190] = v702;
  v761[190] = CFSTR("发行价");
  v386 = _objc_msgSend(&OBJC_CLASS___NSNumber, v385, 1612LL);
  v703 = objc_retainAutoreleasedReturnValue(v386);
  v760[191] = v703;
  v761[191] = CFSTR("中签率%");
  v388 = _objc_msgSend(&OBJC_CLASS___NSNumber, v387, 675LL);
  v704 = objc_retainAutoreleasedReturnValue(v388);
  v760[192] = v704;
  v761[192] = CFSTR("实际涨幅");
  v390 = _objc_msgSend(&OBJC_CLASS___NSNumber, v389, 676LL);
  v705 = objc_retainAutoreleasedReturnValue(v390);
  v760[193] = v705;
  v761[193] = CFSTR("首日振幅");
  v392 = _objc_msgSend(&OBJC_CLASS___NSNumber, v391, 74LL);
  v706 = objc_retainAutoreleasedReturnValue(v392);
  v760[194] = v706;
  v761[194] = CFSTR("期转现成交量");
  v394 = _objc_msgSend(&OBJC_CLASS___NSNumber, v393, 75LL);
  v707 = objc_retainAutoreleasedReturnValue(v394);
  v760[195] = v707;
  v761[195] = CFSTR("期转现持仓变化量");
  v396 = _objc_msgSend(&OBJC_CLASS___NSNumber, v395, 672LL);
  v708 = objc_retainAutoreleasedReturnValue(v396);
  v760[196] = v708;
  v761[196] = CFSTR("申购限额(万股)");
  v398 = _objc_msgSend(&OBJC_CLASS___NSNumber, v397, 280LL);
  v709 = objc_retainAutoreleasedReturnValue(v398);
  v760[197] = v709;
  v761[197] = CFSTR("行权价");
  v400 = _objc_msgSend(&OBJC_CLASS___NSNumber, v399, 881LL);
  v710 = objc_retainAutoreleasedReturnValue(v400);
  v760[198] = v710;
  v761[198] = CFSTR("标的证券");
  v402 = _objc_msgSend(&OBJC_CLASS___NSNumber, v401, 882LL);
  v711 = objc_retainAutoreleasedReturnValue(v402);
  v760[199] = v711;
  v761[199] = CFSTR("权证类型");
  v404 = _objc_msgSend(&OBJC_CLASS___NSNumber, v403, 876LL);
  v712 = objc_retainAutoreleasedReturnValue(v404);
  v760[200] = v712;
  v761[200] = CFSTR("街货量");
  v406 = _objc_msgSend(&OBJC_CLASS___NSNumber, v405, 875LL);
  v713 = objc_retainAutoreleasedReturnValue(v406);
  v760[201] = v713;
  v761[201] = CFSTR("街货占比");
  v408 = _objc_msgSend(&OBJC_CLASS___NSNumber, v407, 874LL);
  v714 = objc_retainAutoreleasedReturnValue(v408);
  v760[202] = v714;
  v761[202] = CFSTR("对冲值");
  v410 = _objc_msgSend(&OBJC_CLASS___NSNumber, v409, 873LL);
  v715 = objc_retainAutoreleasedReturnValue(v410);
  v760[203] = v715;
  v761[203] = CFSTR("引伸波幅");
  v412 = _objc_msgSend(&OBJC_CLASS___NSNumber, v411, 890LL);
  v716 = objc_retainAutoreleasedReturnValue(v412);
  v760[204] = v716;
  v761[204] = CFSTR("到期日");
  v414 = _objc_msgSend(&OBJC_CLASS___NSNumber, v413, 877LL);
  v717 = objc_retainAutoreleasedReturnValue(v414);
  v760[205] = v717;
  v761[205] = CFSTR("最后交易日");
  v416 = _objc_msgSend(&OBJC_CLASS___NSNumber, v415, 879LL);
  v718 = objc_retainAutoreleasedReturnValue(v416);
  v760[206] = v718;
  v761[206] = CFSTR("回收价");
  v418 = _objc_msgSend(&OBJC_CLASS___NSNumber, v417, 880LL);
  v719 = objc_retainAutoreleasedReturnValue(v418);
  v760[207] = v719;
  v761[207] = CFSTR("牛熊证种类");
  v420 = _objc_msgSend(&OBJC_CLASS___NSNumber, v419, 93LL);
  v720 = objc_retainAutoreleasedReturnValue(v420);
  v760[208] = v720;
  v761[208] = CFSTR("交易单位");
  v422 = _objc_msgSend(&OBJC_CLASS___NSNumber, v421, 191LL);
  v721 = objc_retainAutoreleasedReturnValue(v422);
  v760[209] = v721;
  v761[209] = CFSTR("买差价");
  v424 = _objc_msgSend(&OBJC_CLASS___NSNumber, v423, 192LL);
  v722 = objc_retainAutoreleasedReturnValue(v424);
  v760[210] = v722;
  v761[210] = CFSTR("卖差价");
  v426 = _objc_msgSend(&OBJC_CLASS___NSNumber, v425, 134238LL);
  v723 = objc_retainAutoreleasedReturnValue(v426);
  v760[211] = v723;
  v761[211] = CFSTR("溢价");
  v428 = _objc_msgSend(&OBJC_CLASS___NSNumber, v427, 134237LL);
  v724 = objc_retainAutoreleasedReturnValue(v428);
  v760[212] = v724;
  v761[212] = CFSTR("杠杆比率");
  v430 = _objc_msgSend(&OBJC_CLASS___NSNumber, v429, 887LL);
  v725 = objc_retainAutoreleasedReturnValue(v430);
  v760[213] = v725;
  v761[213] = CFSTR("行使价");
  v432 = _objc_msgSend(&OBJC_CLASS___NSNumber, v431, 888LL);
  v726 = objc_retainAutoreleasedReturnValue(v432);
  v760[214] = v726;
  v761[214] = CFSTR("换股比率");
  v434 = _objc_msgSend(&OBJC_CLASS___NSNumber, v433, 593LL);
  v727 = objc_retainAutoreleasedReturnValue(v434);
  v760[215] = v727;
  v761[215] = CFSTR("公积金");
  v436 = _objc_msgSend(&OBJC_CLASS___NSNumber, v435, 602LL);
  v728 = objc_retainAutoreleasedReturnValue(v436);
  v760[216] = v728;
  v761[216] = CFSTR("主营收入");
  v438 = _objc_msgSend(&OBJC_CLASS___NSNumber, v437, 658784LL);
  v729 = objc_retainAutoreleasedReturnValue(v438);
  v760[217] = v729;
  v761[217] = CFSTR("金叉个数");
  v440 = _objc_msgSend(&OBJC_CLASS___NSNumber, v439, 520LL);
  v730 = objc_retainAutoreleasedReturnValue(v440);
  v760[218] = v730;
  v761[218] = CFSTR("流动资产");
  v442 = _objc_msgSend(&OBJC_CLASS___NSNumber, v441, 920372LL);
  v731 = objc_retainAutoreleasedReturnValue(v442);
  v760[219] = v731;
  v761[219] = CFSTR("实体涨幅");
  v444 = _objc_msgSend(&OBJC_CLASS___NSNumber, v443, 658785LL);
  v732 = objc_retainAutoreleasedReturnValue(v444);
  v760[220] = v732;
  v761[220] = CFSTR("利好");
  v446 = _objc_msgSend(&OBJC_CLASS___NSNumber, v445, 658786LL);
  v733 = objc_retainAutoreleasedReturnValue(v446);
  v760[221] = v733;
  v761[221] = CFSTR("利空");
  v448 = _objc_msgSend(&OBJC_CLASS___NSNumber, v447, 592544LL);
  v734 = objc_retainAutoreleasedReturnValue(v448);
  v760[222] = v734;
  v761[222] = CFSTR("贡献度");
  v450 = _objc_msgSend(&OBJC_CLASS___NSNumber, v449, 592741LL);
  v735 = objc_retainAutoreleasedReturnValue(v450);
  v760[223] = v735;
  v761[223] = CFSTR("机构动向");
  v452 = _objc_msgSend(&OBJC_CLASS___NSNumber, v451, 2579LL);
  v736 = objc_retainAutoreleasedReturnValue(v452);
  v760[224] = v736;
  v761[224] = CFSTR("机构持股比例");
  v454 = _objc_msgSend(&OBJC_CLASS___NSNumber, v453, 2570LL);
  v737 = objc_retainAutoreleasedReturnValue(v454);
  v760[225] = v737;
  v761[225] = CFSTR("卖出信号");
  v456 = _objc_msgSend(&OBJC_CLASS___NSNumber, v455, 2719LL);
  v738 = objc_retainAutoreleasedReturnValue(v456);
  v760[226] = v738;
  v761[226] = CFSTR("人均持股数");
  v458 = _objc_msgSend(&OBJC_CLASS___NSNumber, v457, 615LL);
  v739 = objc_retainAutoreleasedReturnValue(v458);
  v760[227] = v739;
  v761[227] = CFSTR("利润总额");
  v460 = _objc_msgSend(&OBJC_CLASS___NSNumber, v459, 625362LL);
  v740 = objc_retainAutoreleasedReturnValue(v460);
  v760[228] = v740;
  v761[228] = CFSTR("每股公积金");
  v462 = _objc_msgSend(&OBJC_CLASS___NSNumber, v461, 543LL);
  v741 = objc_retainAutoreleasedReturnValue(v462);
  v760[229] = v741;
  v761[229] = CFSTR("资产总计");
  v464 = _objc_msgSend(&OBJC_CLASS___NSNumber, v463, 1674LL);
  v742 = objc_retainAutoreleasedReturnValue(v464);
  v760[230] = v742;
  v761[230] = CFSTR("流通A股");
  v466 = _objc_msgSend(&OBJC_CLASS___NSNumber, v465, 410LL);
  v743 = objc_retainAutoreleasedReturnValue(v466);
  v760[231] = v743;
  v761[231] = CFSTR("流通B股");
  v468 = _objc_msgSend(&OBJC_CLASS___NSNumber, v467, 2263506LL);
  v744 = objc_retainAutoreleasedReturnValue(v468);
  v760[232] = v744;
  v761[232] = CFSTR("流通比例%");
  v470 = _objc_msgSend(&OBJC_CLASS___NSNumber, v469, 592946LL);
  v745 = objc_retainAutoreleasedReturnValue(v470);
  v760[233] = v745;
  v761[233] = CFSTR("多空比");
  v472 = _objc_msgSend(&OBJC_CLASS___NSNumber, v471, 330321LL);
  v746 = objc_retainAutoreleasedReturnValue(v472);
  v760[234] = v746;
  v761[234] = CFSTR("异动类型");
  v474 = _objc_msgSend(&OBJC_CLASS___NSNumber, v473, 330322LL);
  v747 = objc_retainAutoreleasedReturnValue(v474);
  v760[235] = v747;
  v761[235] = CFSTR("竞价评级");
  v476 = _objc_msgSend(&OBJC_CLASS___NSNumber, v475, 920428LL);
  v748 = objc_retainAutoreleasedReturnValue(v476);
  v760[236] = v748;
  v761[236] = CFSTR("股票分类标记");
  v478 = _objc_msgSend(&OBJC_CLASS___NSNumber, v477, 1121LL);
  v749 = objc_retainAutoreleasedReturnValue(v478);
  v760[237] = v749;
  v761[237] = CFSTR("标记");
  v480 = _objc_msgSend(&OBJC_CLASS___NSNumber, v479, 133702LL);
  v750 = objc_retainAutoreleasedReturnValue(v480);
  v760[238] = v750;
  v761[238] = CFSTR("细分行业");
  v482 = _objc_msgSend(&OBJC_CLASS___NSNumber, v481, 330325LL);
  v751 = objc_retainAutoreleasedReturnValue(v482);
  v760[239] = v751;
  v761[239] = CFSTR("涨停类型");
  v484 = _objc_msgSend(&OBJC_CLASS___NSNumber, v483, 330329LL);
  v752 = objc_retainAutoreleasedReturnValue(v484);
  v760[240] = v752;
  v761[240] = CFSTR("涨停状态");
  v486 = _objc_msgSend(&OBJC_CLASS___NSNumber, v485, 134160LL);
  v753 = objc_retainAutoreleasedReturnValue(v486);
  v760[241] = v753;
  v761[241] = CFSTR("换股比率");
  v488 = _objc_msgSend(&OBJC_CLASS___NSNumber, v487, 134162LL);
  v754 = objc_retainAutoreleasedReturnValue(v488);
  v760[242] = v754;
  v761[242] = CFSTR("折溢率");
  v490 = _objc_msgSend(&OBJC_CLASS___NSNumber, v489, 20190901LL);
  v755 = objc_retainAutoreleasedReturnValue(v490);
  v760[243] = v755;
  v761[243] = CFSTR("标签");
  v492 = _objc_msgSend(&OBJC_CLASS___NSNumber, v491, 395720LL);
  v756 = objc_retainAutoreleasedReturnValue(v492);
  v760[244] = v756;
  v761[244] = CFSTR("委比");
  v494 = _objc_msgSend(&OBJC_CLASS___NSNumber, v493, 53LL);
  v757 = objc_retainAutoreleasedReturnValue(v494);
  v760[245] = v757;
  v761[245] = CFSTR("委比");
  v496 = _objc_msgSend(&OBJC_CLASS___NSNumber, v495, 82LL);
  v758 = objc_retainAutoreleasedReturnValue(v496);
  v760[246] = v758;
  v761[246] = CFSTR("撤单时间");
  v498 = _objc_msgSend(&OBJC_CLASS___NSNumber, v497, 56LL);
  v759 = objc_retainAutoreleasedReturnValue(v498);
  v760[247] = v759;
  v761[247] = CFSTR("挂单时间");
  v500 = _objc_msgSend(&OBJC_CLASS___NSNumber, v499, 18550831LL);
  v501 = objc_retainAutoreleasedReturnValue(v500);
  v760[248] = v501;
  v761[248] = CFSTR("成分股数");
  v503 = _objc_msgSend(&OBJC_CLASS___NSNumber, v502, 8311855LL);
  v760[249] = objc_retainAutoreleasedReturnValue(v503);
  v761[249] = CFSTR("类别");
  v504 = (NSDictionary *)_objc_msgSend(
                           &OBJC_CLASS___NSDictionary,
                           "dictionaryWithObjects:forKeys:count:",
                           v761,
                           v760,
                           250LL);
  v505 = objc_retainAutoreleasedReturnValue(v504);
  v506 = _objc_msgSend(v505, "objectForKeyedSubscript:", v559);
  v507 = objc_retainAutoreleasedReturnValue(v506);
  if ( v507 )
    v509 = (__CFString *)objc_retain(v507);
  else
    v509 = &charsToLeaveEscaped;
  return objc_autoreleaseReturnValue(v509);
}

//----- (0000000100B8AAF4) ----------------------------------------------------
id __cdecl +[HXTools getDataTypesWithItemName:](id a1, SEL a2, id a3)
{
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v10; // rax
  NSNumber *v11; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // rax
  NSNumber *v15; // rax
  NSNumber *v16; // rax
  NSNumber *v17; // rax
  NSNumber *v18; // rax
  NSNumber *v19; // rax
  NSNumber *v20; // rax
  NSNumber *v21; // rax
  NSNumber *v22; // rax
  NSNumber *v23; // rax
  NSNumber *v24; // rax
  NSNumber *v25; // rax
  NSNumber *v26; // rax
  NSNumber *v28; // rax
  NSNumber *v29; // rax
  NSNumber *v30; // rax
  NSNumber *v31; // rax
  NSNumber *v32; // rax
  NSNumber *v33; // rax
  NSNumber *v34; // rax
  NSNumber *v35; // rax
  NSNumber *v36; // rax
  NSNumber *v37; // rax
  NSNumber *v38; // rax
  NSNumber *v39; // rax
  NSNumber *v40; // rax
  NSNumber *v41; // rax
  NSNumber *v42; // rax
  NSNumber *v43; // rax
  NSNumber *v44; // rax
  NSNumber *v45; // rax
  NSNumber *v46; // rax
  NSNumber *v47; // rax
  NSNumber *v48; // rax
  NSNumber *v49; // rax
  NSNumber *v50; // rax
  NSNumber *v51; // rax
  NSNumber *v52; // rax
  NSNumber *v53; // rax
  NSNumber *v54; // rax
  NSNumber *v55; // rax
  NSNumber *v56; // rax
  NSNumber *v57; // rax
  NSNumber *v58; // rax
  NSNumber *v59; // rax
  NSNumber *v60; // rax
  NSNumber *v61; // rax
  NSNumber *v62; // rax
  NSNumber *v63; // rax
  NSNumber *v64; // rax
  NSNumber *v65; // rax
  NSNumber *v66; // rax
  NSNumber *v67; // rax
  NSNumber *v68; // rax
  NSNumber *v69; // rax
  NSNumber *v70; // rax
  NSNumber *v71; // rax
  NSNumber *v72; // rax
  NSNumber *v73; // rax
  NSNumber *v74; // rax
  NSNumber *v75; // rax
  NSNumber *v76; // rax
  NSNumber *v77; // rax
  NSNumber *v78; // rax
  NSNumber *v79; // rax
  NSNumber *v80; // rax
  NSNumber *v81; // rax
  NSNumber *v82; // rax
  NSNumber *v83; // rax
  NSNumber *v84; // rax
  NSNumber *v85; // rax
  NSNumber *v86; // rax
  NSNumber *v87; // rax
  NSNumber *v88; // rax
  NSNumber *v89; // rax
  NSNumber *v90; // rax
  NSNumber *v91; // rax
  NSNumber *v92; // rax
  NSNumber *v93; // rax
  NSNumber *v94; // rax
  NSNumber *v95; // rax
  NSNumber *v96; // rax
  NSNumber *v97; // rax
  NSNumber *v98; // rax
  NSNumber *v100; // rax
  NSNumber *v101; // rax
  NSNumber *v102; // rax
  NSNumber *v103; // rax
  NSNumber *v104; // rax
  NSNumber *v105; // rax
  NSNumber *v106; // rax
  NSNumber *v107; // rax
  NSNumber *v108; // rax
  NSNumber *v110; // rax
  NSNumber *v111; // rax
  NSNumber *v112; // rax
  NSNumber *v113; // rax
  NSNumber *v114; // rax
  NSNumber *v115; // rax
  NSNumber *v116; // rax
  NSNumber *v117; // rax
  NSNumber *v118; // rax
  NSNumber *v119; // rax
  NSNumber *v120; // rax
  NSNumber *v121; // rax
  NSNumber *v122; // rax
  NSNumber *v123; // rax
  NSNumber *v124; // rax
  NSNumber *v125; // rax
  NSNumber *v126; // rax
  NSNumber *v127; // rax
  NSNumber *v128; // rax
  NSNumber *v129; // rax
  NSNumber *v130; // rax
  NSNumber *v132; // rax
  NSNumber *v133; // rax
  NSNumber *v134; // rax
  NSNumber *v135; // rax
  NSNumber *v136; // rax
  NSNumber *v137; // rax
  NSNumber *v138; // rax
  NSNumber *v139; // rax
  NSNumber *v140; // rax
  NSNumber *v141; // rax
  NSNumber *v142; // rax
  NSNumber *v143; // rax
  NSNumber *v144; // rax
  NSNumber *v145; // rax
  NSNumber *v146; // rax
  NSNumber *v147; // rax
  NSNumber *v148; // rax
  NSNumber *v149; // rax
  NSNumber *v150; // rax
  NSNumber *v151; // rax
  NSNumber *v152; // rax
  NSNumber *v153; // rax
  NSNumber *v154; // rax
  NSNumber *v155; // rax
  NSNumber *v156; // rax
  NSNumber *v157; // rax
  NSNumber *v158; // rax
  NSNumber *v159; // rax
  NSNumber *v160; // rax
  NSNumber *v161; // rax
  NSNumber *v162; // rax
  NSNumber *v163; // rax
  NSNumber *v164; // rax
  NSNumber *v165; // rax
  NSNumber *v166; // rax
  NSNumber *v167; // rax
  NSNumber *v168; // rax
  NSNumber *v169; // rax
  NSNumber *v170; // rax
  NSNumber *v171; // rax
  NSNumber *v172; // rax
  NSNumber *v173; // rax
  NSNumber *v174; // rax
  NSNumber *v175; // rax
  NSNumber *v176; // rax
  NSNumber *v177; // rax
  NSNumber *v178; // rax
  NSNumber *v179; // rax
  NSNumber *v180; // rax
  NSNumber *v181; // rax
  NSNumber *v182; // rax
  NSNumber *v183; // rax
  NSNumber *v184; // rax
  NSNumber *v185; // rax
  NSNumber *v186; // rax
  NSNumber *v187; // rax
  NSNumber *v188; // rax
  NSNumber *v189; // rax
  NSNumber *v190; // rax
  NSNumber *v191; // rax
  NSNumber *v193; // rax
  NSNumber *v194; // rax
  NSNumber *v195; // rax
  NSNumber *v196; // rax
  NSNumber *v197; // rax
  NSNumber *v198; // rax
  NSNumber *v199; // rax
  NSNumber *v200; // rax
  NSNumber *v201; // rax
  NSNumber *v202; // rax
  NSNumber *v203; // rax
  NSNumber *v204; // rax
  NSNumber *v205; // rax
  NSNumber *v206; // rax
  NSNumber *v207; // rax
  NSNumber *v208; // rax
  NSNumber *v209; // rax
  NSNumber *v210; // rax
  NSNumber *v211; // rax
  NSNumber *v212; // rax
  NSNumber *v213; // rax
  NSNumber *v214; // rax
  NSNumber *v215; // rax
  NSNumber *v216; // rax
  NSNumber *v217; // rax
  NSNumber *v218; // rax
  NSNumber *v219; // rax
  NSNumber *v220; // rax
  NSNumber *v221; // rax
  NSNumber *v222; // rax
  NSNumber *v223; // rax
  NSNumber *v224; // rax
  NSNumber *v225; // rax
  NSNumber *v226; // rax
  NSNumber *v227; // rax
  NSNumber *v228; // rax
  NSNumber *v229; // rax
  NSNumber *v230; // rax
  NSNumber *v231; // rax
  NSNumber *v232; // rax
  NSNumber *v233; // rax
  NSNumber *v234; // rax
  NSNumber *v235; // rax
  NSNumber *v236; // rax
  NSNumber *v237; // rax
  NSNumber *v238; // rax
  NSNumber *v239; // rax
  NSNumber *v240; // rax
  NSNumber *v241; // rax
  NSNumber *v242; // rax
  NSNumber *v243; // rax
  NSNumber *v244; // rax
  NSNumber *v245; // rax
  NSNumber *v246; // rax
  NSNumber *v247; // rax
  NSNumber *v248; // rax
  NSNumber *v249; // rax
  NSNumber *v250; // rax
  NSNumber *v251; // rax
  NSNumber *v252; // rax
  NSNumber *v253; // rax
  NSNumber *v255; // rax
  NSNumber *v256; // rax
  NSNumber *v257; // rax
  NSNumber *v258; // rax
  NSNumber *v260; // rax
  NSNumber *v261; // rax
  NSNumber *v262; // rax
  NSNumber *v263; // rax
  NSNumber *v264; // r15
  NSDictionary *v265; // rax
  NSDictionary *v266; // r14
  _QWORD v272[4]; // [rsp+8h] [rbp-17D8h] BYREF
  _QWORD v526[250]; // [rsp+810h] [rbp-FD0h] BYREF
  _QWORD v527[250]; // [rsp+FE0h] [rbp-800h] BYREF

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) && _objc_msgSend(v5, "length") )
  {
    v323 = _objc_msgSend(__NSArray0__, "mutableCopy");
    v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49LL);
    v275 = objc_retainAutoreleasedReturnValue(v6);
    v526[0] = v275;
    v527[0] = CFSTR("现量");
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 19LL);
    v276 = objc_retainAutoreleasedReturnValue(v7);
    v526[1] = v276;
    v527[1] = CFSTR("总金额");
    v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 18LL);
    v277 = objc_retainAutoreleasedReturnValue(v8);
    v526[2] = v277;
    v527[2] = CFSTR("笔数");
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1640904LL);
    v278 = objc_retainAutoreleasedReturnValue(v9);
    v526[3] = v278;
    v527[3] = CFSTR("手/笔");
    v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14LL);
    v279 = objc_retainAutoreleasedReturnValue(v10);
    v526[4] = v279;
    v527[4] = CFSTR("外盘");
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 15LL);
    v280 = objc_retainAutoreleasedReturnValue(v11);
    v526[5] = v280;
    v525 = v12;
    v527[5] = CFSTR("内盘");
    v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 38LL);
    v281 = objc_retainAutoreleasedReturnValue(v13);
    v526[6] = v281;
    v527[6] = CFSTR("涨家数");
    v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 39LL);
    v282 = objc_retainAutoreleasedReturnValue(v14);
    v526[7] = v282;
    v527[7] = CFSTR("跌家数");
    v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 133778LL);
    v283 = objc_retainAutoreleasedReturnValue(v15);
    v526[8] = v283;
    v527[8] = CFSTR("基差");
    v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 25LL);
    v284 = objc_retainAutoreleasedReturnValue(v16);
    v526[9] = v284;
    v527[9] = CFSTR("买量");
    v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 31LL);
    v285 = objc_retainAutoreleasedReturnValue(v17);
    v526[10] = v285;
    v527[10] = CFSTR("卖量");
    v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 65LL);
    v286 = objc_retainAutoreleasedReturnValue(v18);
    v526[11] = v286;
    v527[11] = CFSTR("持仓");
    v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 71LL);
    v287 = objc_retainAutoreleasedReturnValue(v19);
    v526[12] = v287;
    v527[12] = CFSTR("现增仓");
    v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 66LL);
    v288 = objc_retainAutoreleasedReturnValue(v20);
    v526[13] = v288;
    v527[13] = CFSTR("昨结");
    v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 72LL);
    v289 = objc_retainAutoreleasedReturnValue(v21);
    v526[14] = v289;
    v527[14] = CFSTR("今结");
    v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1005LL);
    v290 = objc_retainAutoreleasedReturnValue(v22);
    v526[15] = v290;
    v527[15] = CFSTR("每股净资产");
    v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 95LL);
    v291 = objc_retainAutoreleasedReturnValue(v23);
    v526[16] = v291;
    v527[16] = CFSTR("52周最高");
    v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 96LL);
    v292 = objc_retainAutoreleasedReturnValue(v24);
    v526[17] = v292;
    v527[17] = CFSTR("52周最低");
    v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3397LL);
    v293 = objc_retainAutoreleasedReturnValue(v25);
    v526[18] = v293;
    v527[18] = CFSTR("净值");
    v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2820564LL);
    v294 = objc_retainAutoreleasedReturnValue(v26);
    v526[19] = v294;
    v527[19] = v27;
    v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 20LL);
    v295 = objc_retainAutoreleasedReturnValue(v28);
    v526[20] = v295;
    v527[20] = CFSTR("买价");
    v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 21LL);
    v296 = objc_retainAutoreleasedReturnValue(v29);
    v526[21] = v296;
    v527[21] = CFSTR("卖价");
    v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3082712LL);
    v297 = objc_retainAutoreleasedReturnValue(v30);
    v526[22] = v297;
    v527[22] = CFSTR("涨幅(结)%");
    v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 73LL);
    v298 = objc_retainAutoreleasedReturnValue(v31);
    v526[23] = v298;
    v527[23] = CFSTR("昨持仓");
    v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 64LL);
    v299 = objc_retainAutoreleasedReturnValue(v32);
    v526[24] = v299;
    v527[24] = CFSTR("状态");
    v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 899LL);
    v300 = objc_retainAutoreleasedReturnValue(v33);
    v526[25] = v300;
    v527[25] = CFSTR("财务数据项");
    v34 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 189546735LL);
    v301 = objc_retainAutoreleasedReturnValue(v34);
    v526[26] = v301;
    v527[26] = CFSTR("计算数据项");
    v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 223LL);
    v302 = objc_retainAutoreleasedReturnValue(v35);
    v526[27] = v302;
    v527[27] = CFSTR("主动买入特大单金额");
    v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 225LL);
    v303 = objc_retainAutoreleasedReturnValue(v36);
    v526[28] = v303;
    v527[28] = CFSTR("主买大");
    v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 259LL);
    v304 = objc_retainAutoreleasedReturnValue(v37);
    v526[29] = v304;
    v527[29] = CFSTR("主买中");
    v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 237LL);
    v305 = objc_retainAutoreleasedReturnValue(v38);
    v526[30] = v305;
    v527[30] = CFSTR("主买小");
    v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 227LL);
    v306 = objc_retainAutoreleasedReturnValue(v39);
    v526[31] = v306;
    v527[31] = CFSTR("被买特");
    v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 229LL);
    v307 = objc_retainAutoreleasedReturnValue(v40);
    v526[32] = v307;
    v527[32] = CFSTR("被买大");
    v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 261LL);
    v308 = objc_retainAutoreleasedReturnValue(v41);
    v526[33] = v308;
    v527[33] = CFSTR("被买中");
    v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 224LL);
    v309 = objc_retainAutoreleasedReturnValue(v42);
    v526[34] = v309;
    v527[34] = CFSTR("主动卖出特大单金额");
    v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 226LL);
    v310 = objc_retainAutoreleasedReturnValue(v43);
    v526[35] = v310;
    v527[35] = CFSTR("主卖大");
    v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 260LL);
    v311 = objc_retainAutoreleasedReturnValue(v44);
    v526[36] = v311;
    v527[36] = CFSTR("主卖中");
    v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 238LL);
    v312 = objc_retainAutoreleasedReturnValue(v45);
    v526[37] = v312;
    v527[37] = CFSTR("主卖小");
    v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 228LL);
    v313 = objc_retainAutoreleasedReturnValue(v46);
    v526[38] = v313;
    v527[38] = CFSTR("被卖特");
    v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 230LL);
    v314 = objc_retainAutoreleasedReturnValue(v47);
    v526[39] = v314;
    v527[39] = CFSTR("被卖大");
    v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 262LL);
    v315 = objc_retainAutoreleasedReturnValue(v48);
    v526[40] = v315;
    v527[40] = CFSTR("被卖中");
    v49 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 24LL);
    v316 = objc_retainAutoreleasedReturnValue(v49);
    v526[41] = v316;
    v527[41] = CFSTR("买一");
    v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 26LL);
    v317 = objc_retainAutoreleasedReturnValue(v50);
    v526[42] = v317;
    v527[42] = CFSTR("买二");
    v51 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 28LL);
    v318 = objc_retainAutoreleasedReturnValue(v51);
    v526[43] = v318;
    v527[43] = CFSTR("买三");
    v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 150LL);
    v319 = objc_retainAutoreleasedReturnValue(v52);
    v526[44] = v319;
    v527[44] = CFSTR("买四");
    v53 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 154LL);
    v320 = objc_retainAutoreleasedReturnValue(v53);
    v526[45] = v320;
    v527[45] = CFSTR("买五");
    v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 25LL);
    v321 = objc_retainAutoreleasedReturnValue(v54);
    v526[46] = v321;
    v527[46] = CFSTR("买一量");
    v55 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 27LL);
    v322 = objc_retainAutoreleasedReturnValue(v55);
    v526[47] = v322;
    v527[47] = CFSTR("买二量");
    v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 29LL);
    v324 = objc_retainAutoreleasedReturnValue(v56);
    v526[48] = v324;
    v527[48] = CFSTR("买三量");
    v57 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 151LL);
    v325 = objc_retainAutoreleasedReturnValue(v57);
    v526[49] = v325;
    v527[49] = CFSTR("买四量");
    v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 155LL);
    v326 = objc_retainAutoreleasedReturnValue(v58);
    v526[50] = v326;
    v527[50] = CFSTR("买五量");
    v59 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 30LL);
    v327 = objc_retainAutoreleasedReturnValue(v59);
    v526[51] = v327;
    v527[51] = CFSTR("卖一");
    v60 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 32LL);
    v328 = objc_retainAutoreleasedReturnValue(v60);
    v526[52] = v328;
    v527[52] = CFSTR("卖二");
    v61 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34LL);
    v329 = objc_retainAutoreleasedReturnValue(v61);
    v526[53] = v329;
    v527[53] = CFSTR("卖三");
    v62 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 152LL);
    v330 = objc_retainAutoreleasedReturnValue(v62);
    v526[54] = v330;
    v527[54] = CFSTR("卖四");
    v63 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 156LL);
    v331 = objc_retainAutoreleasedReturnValue(v63);
    v526[55] = v331;
    v527[55] = CFSTR("卖五");
    v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 31LL);
    v332 = objc_retainAutoreleasedReturnValue(v64);
    v526[56] = v332;
    v527[56] = CFSTR("卖一量");
    v65 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 33LL);
    v333 = objc_retainAutoreleasedReturnValue(v65);
    v526[57] = v333;
    v527[57] = CFSTR("卖二量");
    v66 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 35LL);
    v334 = objc_retainAutoreleasedReturnValue(v66);
    v526[58] = v334;
    v527[58] = CFSTR("卖三量");
    v67 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 153LL);
    v335 = objc_retainAutoreleasedReturnValue(v67);
    v526[59] = v335;
    v527[59] = CFSTR("卖四量");
    v68 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 157LL);
    v336 = objc_retainAutoreleasedReturnValue(v68);
    v526[60] = v336;
    v527[60] = CFSTR("卖五量");
    v69 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 160LL);
    v337 = objc_retainAutoreleasedReturnValue(v69);
    v526[61] = v337;
    v527[61] = CFSTR("市场分层");
    v70 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 11LL);
    v338 = objc_retainAutoreleasedReturnValue(v70);
    v526[62] = v338;
    v527[62] = CFSTR("收盘");
    v71 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 41LL);
    v339 = objc_retainAutoreleasedReturnValue(v71);
    v526[63] = v339;
    v527[63] = CFSTR("红绿柱");
    v72 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 85LL);
    v340 = objc_retainAutoreleasedReturnValue(v72);
    v526[64] = v340;
    v527[64] = CFSTR("盈利情况");
    v73 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2018090320LL);
    v341 = objc_retainAutoreleasedReturnValue(v73);
    v526[65] = v341;
    v527[65] = CFSTR("竞价异动类型及说明颜色判断字段");
    v74 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 900LL);
    v342 = objc_retainAutoreleasedReturnValue(v74);
    v526[66] = v342;
    v527[66] = CFSTR("流通股变动量");
    v75 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 70LL);
    v343 = objc_retainAutoreleasedReturnValue(v75);
    v526[67] = v343;
    v527[67] = CFSTR("跌停");
    v76 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 69LL);
    v344 = objc_retainAutoreleasedReturnValue(v76);
    v526[68] = v344;
    v527[68] = CFSTR("涨停");
    v77 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 45LL);
    v345 = objc_retainAutoreleasedReturnValue(v77);
    v526[69] = v345;
    v527[69] = CFSTR("五日成交总量");
    v78 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 471LL);
    v346 = objc_retainAutoreleasedReturnValue(v78);
    v526[70] = v346;
    v527[70] = CFSTR("权息资料");
    v79 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 22LL);
    v347 = objc_retainAutoreleasedReturnValue(v79);
    v526[71] = v347;
    v527[71] = CFSTR("委买");
    v80 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 23LL);
    v348 = objc_retainAutoreleasedReturnValue(v80);
    v526[72] = v348;
    v527[72] = CFSTR("委卖");
    v81 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 251LL);
    v349 = objc_retainAutoreleasedReturnValue(v81);
    v526[73] = v349;
    v527[73] = CFSTR("卖出金额");
    v82 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 250LL);
    v350 = objc_retainAutoreleasedReturnValue(v82);
    v526[74] = v350;
    v527[74] = CFSTR("买入金额");
    v83 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 981LL);
    v351 = objc_retainAutoreleasedReturnValue(v83);
    v526[75] = v351;
    v527[75] = CFSTR("港元->人民币汇率");
    v84 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 89LL);
    v352 = objc_retainAutoreleasedReturnValue(v84);
    v526[76] = v352;
    v527[76] = CFSTR("转让状态相关参数");
    v85 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 54LL);
    v353 = objc_retainAutoreleasedReturnValue(v85);
    v526[77] = v353;
    v527[77] = CFSTR("均价(中金所专用)");
    v86 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 40LL);
    v354 = objc_retainAutoreleasedReturnValue(v86);
    v526[78] = v354;
    v527[78] = CFSTR("领先指标");
    v87 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 148LL);
    v355 = objc_retainAutoreleasedReturnValue(v87);
    v526[79] = v355;
    v527[79] = CFSTR("期权认购认沽");
    v88 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 91LL);
    v356 = objc_retainAutoreleasedReturnValue(v88);
    v526[80] = v356;
    v527[80] = CFSTR("市盈率");
    v89 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v357 = objc_retainAutoreleasedReturnValue(v89);
    v526[81] = v357;
    v527[81] = CFSTR("代码");
    v90 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
    v358 = objc_retainAutoreleasedReturnValue(v90);
    v526[82] = v358;
    v527[82] = CFSTR("涨幅%");
    v91 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3250LL);
    v359 = objc_retainAutoreleasedReturnValue(v91);
    v526[83] = v359;
    v527[83] = CFSTR("5日涨幅");
    v92 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3251LL);
    v360 = objc_retainAutoreleasedReturnValue(v92);
    v526[84] = v360;
    v527[84] = CFSTR("10日涨幅");
    v93 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3252LL);
    v361 = objc_retainAutoreleasedReturnValue(v93);
    v526[85] = v361;
    v527[85] = CFSTR("20日涨幅");
    v94 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1322LL);
    v362 = objc_retainAutoreleasedReturnValue(v94);
    v526[86] = v362;
    v527[86] = CFSTR("利率%");
    v95 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 80LL);
    v363 = objc_retainAutoreleasedReturnValue(v95);
    v526[87] = v363;
    v527[87] = CFSTR("利息");
    v96 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12345671LL);
    v364 = objc_retainAutoreleasedReturnValue(v96);
    v526[88] = v364;
    v527[88] = CFSTR("A股关联主题");
    v97 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4065737LL);
    v365 = objc_retainAutoreleasedReturnValue(v97);
    v526[89] = v365;
    v527[89] = CFSTR("买价");
    v98 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4131273LL);
    v366 = objc_retainAutoreleasedReturnValue(v98);
    v526[90] = v366;
    v527[90] = v99;
    v100 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 264648LL);
    v367 = objc_retainAutoreleasedReturnValue(v100);
    v526[91] = v367;
    v527[91] = CFSTR("涨跌");
    v101 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
    v368 = objc_retainAutoreleasedReturnValue(v101);
    v526[92] = v368;
    v527[92] = CFSTR("昨收");
    v102 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7LL);
    v369 = objc_retainAutoreleasedReturnValue(v102);
    v526[93] = v369;
    v527[93] = CFSTR("开盘");
    v103 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 8LL);
    v370 = objc_retainAutoreleasedReturnValue(v103);
    v526[94] = v370;
    v527[94] = CFSTR("最高");
    v104 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 9LL);
    v371 = objc_retainAutoreleasedReturnValue(v104);
    v526[95] = v371;
    v527[95] = CFSTR("最低");
    v105 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v372 = objc_retainAutoreleasedReturnValue(v105);
    v526[96] = v372;
    v527[96] = CFSTR("现价");
    v106 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 13LL);
    v373 = objc_retainAutoreleasedReturnValue(v106);
    v526[97] = v373;
    v527[97] = CFSTR("总量");
    v107 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 48LL);
    v374 = objc_retainAutoreleasedReturnValue(v107);
    v526[98] = v374;
    v527[98] = CFSTR("涨速%");
    v108 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3934664LL);
    v375 = objc_retainAutoreleasedReturnValue(v108);
    v526[99] = v375;
    v527[99] = v109;
    v110 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 526792LL);
    v376 = objc_retainAutoreleasedReturnValue(v110);
    v526[100] = v376;
    v527[100] = CFSTR("振幅%");
    v111 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
    v377 = objc_retainAutoreleasedReturnValue(v111);
    v526[101] = v377;
    v527[101] = CFSTR("名称");
    v112 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1771976LL);
    v378 = objc_retainAutoreleasedReturnValue(v112);
    v526[102] = v378;
    v527[102] = CFSTR("量比");
    v113 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 84LL);
    v379 = objc_retainAutoreleasedReturnValue(v113);
    v526[103] = v379;
    v527[103] = CFSTR("所属行业");
    v114 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 92LL);
    v380 = objc_retainAutoreleasedReturnValue(v114);
    v526[104] = v380;
    v527[104] = CFSTR("总市值");
    v115 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 90LL);
    v381 = objc_retainAutoreleasedReturnValue(v115);
    v526[105] = v381;
    v527[105] = CFSTR("流通市值");
    v116 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 402LL);
    v382 = objc_retainAutoreleasedReturnValue(v116);
    v526[106] = v382;
    v527[106] = CFSTR("总股本");
    v117 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 407LL);
    v383 = objc_retainAutoreleasedReturnValue(v117);
    v526[107] = v383;
    v527[107] = CFSTR("流通股本");
    v118 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199643LL);
    v384 = objc_retainAutoreleasedReturnValue(v118);
    v526[108] = v384;
    v527[108] = CFSTR("大单净量");
    v119 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 920371LL);
    v385 = objc_retainAutoreleasedReturnValue(v119);
    v526[109] = v385;
    v527[109] = CFSTR("开盘涨幅");
    v120 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 461256LL);
    v386 = objc_retainAutoreleasedReturnValue(v120);
    v526[110] = v386;
    v527[110] = CFSTR("委比%");
    v121 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1968584LL);
    v387 = objc_retainAutoreleasedReturnValue(v121);
    v526[111] = v387;
    v527[111] = CFSTR("换手%");
    v122 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2427336LL);
    v388 = objc_retainAutoreleasedReturnValue(v122);
    v526[112] = v388;
    v527[112] = CFSTR("均笔额");
    v123 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 24LL);
    v389 = objc_retainAutoreleasedReturnValue(v123);
    v526[113] = v389;
    v527[113] = CFSTR("买一价");
    v124 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 30LL);
    v390 = objc_retainAutoreleasedReturnValue(v124);
    v526[114] = v390;
    v527[114] = CFSTR("卖一价");
    v125 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1509847LL);
    v391 = objc_retainAutoreleasedReturnValue(v125);
    v526[115] = v391;
    v527[115] = CFSTR("户均持股数");
    v126 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1002LL);
    v392 = objc_retainAutoreleasedReturnValue(v126);
    v526[116] = v392;
    v527[116] = CFSTR("每股收益");
    v127 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1005LL);
    v393 = objc_retainAutoreleasedReturnValue(v127);
    v526[117] = v393;
    v527[117] = CFSTR("每股净资产");
    v128 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1015LL);
    v394 = objc_retainAutoreleasedReturnValue(v128);
    v526[118] = v394;
    v527[118] = CFSTR("净资产收益");
    v129 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 619LL);
    v395 = objc_retainAutoreleasedReturnValue(v129);
    v526[119] = v395;
    v527[119] = CFSTR("净利润");
    v130 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1566LL);
    v396 = objc_retainAutoreleasedReturnValue(v130);
    v526[120] = v396;
    v527[120] = v131;
    v132 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1670LL);
    v397 = objc_retainAutoreleasedReturnValue(v132);
    v526[121] = v397;
    v527[121] = CFSTR("股东总数");
    v133 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2947LL);
    v398 = objc_retainAutoreleasedReturnValue(v133);
    v526[122] = v398;
    v527[122] = CFSTR("市净率");
    v134 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3541450LL);
    v399 = objc_retainAutoreleasedReturnValue(v134);
    v526[123] = v399;
    v527[123] = CFSTR("总市值");
    v135 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3475914LL);
    v400 = objc_retainAutoreleasedReturnValue(v135);
    v526[124] = v400;
    v527[124] = CFSTR("流通市值");
    v136 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134141LL);
    v401 = objc_retainAutoreleasedReturnValue(v136);
    v526[125] = v401;
    v527[125] = CFSTR("净利润增长率");
    v137 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134143LL);
    v402 = objc_retainAutoreleasedReturnValue(v137);
    v526[126] = v402;
    v527[126] = CFSTR("营业收入增长率");
    v138 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 605LL);
    v403 = objc_retainAutoreleasedReturnValue(v138);
    v526[127] = v403;
    v527[127] = CFSTR("营业利润");
    v139 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2034121LL);
    v404 = objc_retainAutoreleasedReturnValue(v139);
    v526[128] = v404;
    v527[128] = CFSTR("资产负债率");
    v140 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2034120LL);
    v405 = objc_retainAutoreleasedReturnValue(v140);
    v526[129] = v405;
    v527[129] = CFSTR("市盈(动)");
    v141 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2946LL);
    v406 = objc_retainAutoreleasedReturnValue(v141);
    v526[130] = v406;
    v527[130] = CFSTR("市盈(静)");
    v142 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4525375LL);
    v407 = objc_retainAutoreleasedReturnValue(v142);
    v526[131] = v407;
    v527[131] = CFSTR("小单流入");
    v143 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4525376LL);
    v408 = objc_retainAutoreleasedReturnValue(v143);
    v526[132] = v408;
    v527[132] = CFSTR("中单流入");
    v144 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4525377LL);
    v409 = objc_retainAutoreleasedReturnValue(v144);
    v526[133] = v409;
    v527[133] = CFSTR("大单流入");
    v145 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 8719679LL);
    v410 = objc_retainAutoreleasedReturnValue(v145);
    v526[134] = v410;
    v527[134] = CFSTR("小单流出");
    v146 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 8719680LL);
    v411 = objc_retainAutoreleasedReturnValue(v146);
    v526[135] = v411;
    v527[135] = CFSTR("中单流出");
    v147 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 8719681LL);
    v412 = objc_retainAutoreleasedReturnValue(v147);
    v526[136] = v412;
    v527[136] = CFSTR("大单流出");
    v148 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12913983LL);
    v413 = objc_retainAutoreleasedReturnValue(v148);
    v526[137] = v413;
    v527[137] = CFSTR("小单净额");
    v149 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12913984LL);
    v414 = objc_retainAutoreleasedReturnValue(v149);
    v526[138] = v414;
    v527[138] = CFSTR("中单净额");
    v150 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12913985LL);
    v415 = objc_retainAutoreleasedReturnValue(v150);
    v526[139] = v415;
    v527[139] = CFSTR("大单净额");
    v151 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 17108287LL);
    v416 = objc_retainAutoreleasedReturnValue(v151);
    v526[140] = v416;
    v527[140] = CFSTR("小单净额占比%");
    v152 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 17108288LL);
    v417 = objc_retainAutoreleasedReturnValue(v152);
    v526[141] = v417;
    v527[141] = CFSTR("中单净额占比%");
    v153 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 17108289LL);
    v418 = objc_retainAutoreleasedReturnValue(v153);
    v526[142] = v418;
    v527[142] = CFSTR("大单净额占比%");
    v154 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 21302591LL);
    v419 = objc_retainAutoreleasedReturnValue(v154);
    v526[143] = v419;
    v527[143] = CFSTR("小单总额");
    v155 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 21302592LL);
    v420 = objc_retainAutoreleasedReturnValue(v155);
    v526[144] = v420;
    v527[144] = CFSTR("中单总额");
    v156 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 21302593LL);
    v421 = objc_retainAutoreleasedReturnValue(v156);
    v526[145] = v421;
    v527[145] = CFSTR("大单总额");
    v157 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 25496895LL);
    v422 = objc_retainAutoreleasedReturnValue(v157);
    v526[146] = v422;
    v527[146] = CFSTR("小单总额占比%");
    v158 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 25496896LL);
    v423 = objc_retainAutoreleasedReturnValue(v158);
    v526[147] = v423;
    v527[147] = CFSTR("中单总额占比%");
    v159 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 25496897LL);
    v424 = objc_retainAutoreleasedReturnValue(v159);
    v526[148] = v424;
    v527[148] = CFSTR("大单总额占比%");
    v160 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331070LL);
    v425 = objc_retainAutoreleasedReturnValue(v160);
    v526[149] = v425;
    v527[149] = CFSTR("今日主力增仓占比%");
    v161 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331128LL);
    v426 = objc_retainAutoreleasedReturnValue(v161);
    v526[150] = v426;
    v527[150] = CFSTR("今日主力增仓排名");
    v162 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331077LL);
    v427 = objc_retainAutoreleasedReturnValue(v162);
    v526[151] = v427;
    v527[151] = CFSTR("2日主力增仓占比%");
    v163 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331124LL);
    v428 = objc_retainAutoreleasedReturnValue(v163);
    v526[152] = v428;
    v527[152] = CFSTR("2日主力增仓排名");
    v164 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331078LL);
    v429 = objc_retainAutoreleasedReturnValue(v164);
    v526[153] = v429;
    v527[153] = CFSTR("3日主力增仓占比%");
    v165 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331125LL);
    v430 = objc_retainAutoreleasedReturnValue(v165);
    v526[154] = v430;
    v527[154] = CFSTR("3日主力增仓排名");
    v166 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331079LL);
    v431 = objc_retainAutoreleasedReturnValue(v166);
    v526[155] = v431;
    v527[155] = CFSTR("5日主力增仓占比%");
    v167 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331126LL);
    v432 = objc_retainAutoreleasedReturnValue(v167);
    v526[156] = v432;
    v527[156] = CFSTR("5日主力增仓排名");
    v168 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331080LL);
    v433 = objc_retainAutoreleasedReturnValue(v168);
    v526[157] = v433;
    v527[157] = CFSTR("10日主力增仓占比%");
    v169 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 331127LL);
    v434 = objc_retainAutoreleasedReturnValue(v169);
    v526[158] = v434;
    v527[158] = CFSTR("10日主力增仓排名");
    v170 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592888LL);
    v435 = objc_retainAutoreleasedReturnValue(v170);
    v526[159] = v435;
    v527[159] = CFSTR("主力净量");
    v171 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 68285LL);
    v436 = objc_retainAutoreleasedReturnValue(v171);
    v526[160] = v436;
    v527[160] = CFSTR("主力净量(板块)");
    v172 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 462057LL);
    v437 = objc_retainAutoreleasedReturnValue(v172);
    v526[161] = v437;
    v527[161] = CFSTR("散户数量");
    v173 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592893LL);
    v438 = objc_retainAutoreleasedReturnValue(v173);
    v526[162] = v438;
    v527[162] = CFSTR("5日大单净量");
    v174 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592894LL);
    v439 = objc_retainAutoreleasedReturnValue(v174);
    v526[163] = v439;
    v527[163] = CFSTR("10日大单净量");
    v175 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592890LL);
    v440 = objc_retainAutoreleasedReturnValue(v175);
    v526[164] = v440;
    v527[164] = CFSTR("主力净流入");
    v176 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 68166LL);
    v441 = objc_retainAutoreleasedReturnValue(v176);
    v526[165] = v441;
    v527[165] = CFSTR("主力流入");
    v177 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 68167LL);
    v442 = objc_retainAutoreleasedReturnValue(v177);
    v526[166] = v442;
    v527[166] = CFSTR("主力流出");
    v178 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 9810LL);
    v443 = objc_retainAutoreleasedReturnValue(v178);
    v526[167] = v443;
    v527[167] = CFSTR("溢价率");
    v179 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 130LL);
    v444 = objc_retainAutoreleasedReturnValue(v179);
    v526[168] = v444;
    v527[168] = CFSTR("总手(港)");
    v180 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2646480LL);
    v445 = objc_retainAutoreleasedReturnValue(v180);
    v526[169] = v445;
    v527[169] = CFSTR("H股涨跌");
    v181 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1991120LL);
    v446 = objc_retainAutoreleasedReturnValue(v181);
    v526[170] = v446;
    v527[170] = CFSTR("涨幅(港)");
    v182 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 100LL);
    v447 = objc_retainAutoreleasedReturnValue(v182);
    v526[171] = v447;
    v527[171] = CFSTR("现价(港)");
    v183 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 60LL);
    v448 = objc_retainAutoreleasedReturnValue(v183);
    v526[172] = v448;
    v527[172] = CFSTR("H股昨收");
    v184 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 527527LL);
    v449 = objc_retainAutoreleasedReturnValue(v184);
    v526[173] = v449;
    v527[173] = CFSTR("涨速%(1分)");
    v185 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 527526LL);
    v450 = objc_retainAutoreleasedReturnValue(v185);
    v526[174] = v450;
    v527[174] = CFSTR("涨速%(3分)");
    v186 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 461438LL);
    v451 = objc_retainAutoreleasedReturnValue(v186);
    v526[175] = v451;
    v527[175] = CFSTR("涨速%(10分)");
    v187 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 461439LL);
    v452 = objc_retainAutoreleasedReturnValue(v187);
    v526[176] = v452;
    v527[176] = CFSTR("涨速%(15分)");
    v188 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1110LL);
    v453 = objc_retainAutoreleasedReturnValue(v188);
    v526[177] = v453;
    v527[177] = CFSTR("星级");
    v189 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 4099083LL);
    v454 = objc_retainAutoreleasedReturnValue(v189);
    v526[178] = v454;
    v527[178] = CFSTR("日增仓");
    v190 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 12LL);
    v455 = objc_retainAutoreleasedReturnValue(v190);
    v526[179] = v455;
    v527[179] = CFSTR("成交量分类");
    v191 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 133964LL);
    v456 = objc_retainAutoreleasedReturnValue(v191);
    v526[180] = v456;
    v527[180] = v192;
    v193 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 50LL);
    v457 = objc_retainAutoreleasedReturnValue(v193);
    v526[181] = v457;
    v527[181] = CFSTR("代码(港)");
    v194 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 3153LL);
    v458 = objc_retainAutoreleasedReturnValue(v194);
    v526[182] = v458;
    v527[182] = CFSTR("市盈TTM");
    v195 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134071LL);
    v459 = objc_retainAutoreleasedReturnValue(v195);
    v526[183] = v459;
    v527[183] = CFSTR("市销TTM");
    v196 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134072LL);
    v460 = objc_retainAutoreleasedReturnValue(v196);
    v526[184] = v460;
    v527[184] = CFSTR("净资产收益TTM");
    v197 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 61LL);
    v461 = objc_retainAutoreleasedReturnValue(v197);
    v526[185] = v461;
    v527[185] = CFSTR("异动类型");
    v198 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
    v462 = objc_retainAutoreleasedReturnValue(v198);
    v526[186] = v462;
    v527[186] = CFSTR("时间");
    v199 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2018090319LL);
    v463 = objc_retainAutoreleasedReturnValue(v199);
    v526[187] = v463;
    v527[187] = CFSTR("竞价评级");
    v200 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2018090410LL);
    v464 = objc_retainAutoreleasedReturnValue(v200);
    v526[188] = v464;
    v527[188] = CFSTR("异动说明");
    v201 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2018090411LL);
    v465 = objc_retainAutoreleasedReturnValue(v201);
    v526[189] = v465;
    v527[189] = CFSTR("竞价涨幅");
    v202 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1606LL);
    v466 = objc_retainAutoreleasedReturnValue(v202);
    v526[190] = v466;
    v527[190] = CFSTR("发行价");
    v203 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1612LL);
    v467 = objc_retainAutoreleasedReturnValue(v203);
    v526[191] = v467;
    v527[191] = CFSTR("中签率%");
    v204 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 675LL);
    v468 = objc_retainAutoreleasedReturnValue(v204);
    v526[192] = v468;
    v527[192] = CFSTR("实际涨幅");
    v205 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 676LL);
    v469 = objc_retainAutoreleasedReturnValue(v205);
    v526[193] = v469;
    v527[193] = CFSTR("首日振幅");
    v206 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 74LL);
    v470 = objc_retainAutoreleasedReturnValue(v206);
    v526[194] = v470;
    v527[194] = CFSTR("期转现成交量");
    v207 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 75LL);
    v471 = objc_retainAutoreleasedReturnValue(v207);
    v526[195] = v471;
    v527[195] = CFSTR("期转现持仓变化量");
    v208 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 672LL);
    v472 = objc_retainAutoreleasedReturnValue(v208);
    v526[196] = v472;
    v527[196] = CFSTR("申购限额(万股)");
    v209 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 280LL);
    v473 = objc_retainAutoreleasedReturnValue(v209);
    v526[197] = v473;
    v527[197] = CFSTR("行权价");
    v210 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 881LL);
    v474 = objc_retainAutoreleasedReturnValue(v210);
    v526[198] = v474;
    v527[198] = CFSTR("标的证券");
    v211 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 882LL);
    v475 = objc_retainAutoreleasedReturnValue(v211);
    v526[199] = v475;
    v527[199] = CFSTR("权证类型");
    v212 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 876LL);
    v476 = objc_retainAutoreleasedReturnValue(v212);
    v526[200] = v476;
    v527[200] = CFSTR("街货量");
    v213 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 875LL);
    v477 = objc_retainAutoreleasedReturnValue(v213);
    v526[201] = v477;
    v527[201] = CFSTR("街货占比");
    v214 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 874LL);
    v478 = objc_retainAutoreleasedReturnValue(v214);
    v526[202] = v478;
    v527[202] = CFSTR("对冲值");
    v215 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 873LL);
    v479 = objc_retainAutoreleasedReturnValue(v215);
    v526[203] = v479;
    v527[203] = CFSTR("引伸波幅");
    v216 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 890LL);
    v480 = objc_retainAutoreleasedReturnValue(v216);
    v526[204] = v480;
    v527[204] = CFSTR("到期日");
    v217 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 877LL);
    v481 = objc_retainAutoreleasedReturnValue(v217);
    v526[205] = v481;
    v527[205] = CFSTR("最后交易日");
    v218 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 879LL);
    v482 = objc_retainAutoreleasedReturnValue(v218);
    v526[206] = v482;
    v527[206] = CFSTR("回收价");
    v219 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 880LL);
    v483 = objc_retainAutoreleasedReturnValue(v219);
    v526[207] = v483;
    v527[207] = CFSTR("牛熊证种类");
    v220 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 93LL);
    v484 = objc_retainAutoreleasedReturnValue(v220);
    v526[208] = v484;
    v527[208] = CFSTR("交易单位");
    v221 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 191LL);
    v485 = objc_retainAutoreleasedReturnValue(v221);
    v526[209] = v485;
    v527[209] = CFSTR("买差价");
    v222 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 192LL);
    v486 = objc_retainAutoreleasedReturnValue(v222);
    v526[210] = v486;
    v527[210] = CFSTR("卖差价");
    v223 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134238LL);
    v487 = objc_retainAutoreleasedReturnValue(v223);
    v526[211] = v487;
    v527[211] = CFSTR("溢价");
    v224 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134237LL);
    v488 = objc_retainAutoreleasedReturnValue(v224);
    v526[212] = v488;
    v527[212] = CFSTR("杠杆比率");
    v225 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 887LL);
    v489 = objc_retainAutoreleasedReturnValue(v225);
    v526[213] = v489;
    v527[213] = CFSTR("行使价");
    v226 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 888LL);
    v490 = objc_retainAutoreleasedReturnValue(v226);
    v526[214] = v490;
    v527[214] = CFSTR("换股比率");
    v227 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 593LL);
    v491 = objc_retainAutoreleasedReturnValue(v227);
    v526[215] = v491;
    v527[215] = CFSTR("公积金");
    v228 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 602LL);
    v492 = objc_retainAutoreleasedReturnValue(v228);
    v526[216] = v492;
    v527[216] = CFSTR("主营收入");
    v229 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 658784LL);
    v493 = objc_retainAutoreleasedReturnValue(v229);
    v526[217] = v493;
    v527[217] = CFSTR("金叉个数");
    v230 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 520LL);
    v494 = objc_retainAutoreleasedReturnValue(v230);
    v526[218] = v494;
    v527[218] = CFSTR("流动资产");
    v231 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 920372LL);
    v495 = objc_retainAutoreleasedReturnValue(v231);
    v526[219] = v495;
    v527[219] = CFSTR("实体涨幅");
    v232 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 658785LL);
    v496 = objc_retainAutoreleasedReturnValue(v232);
    v526[220] = v496;
    v527[220] = CFSTR("利好");
    v233 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 658786LL);
    v497 = objc_retainAutoreleasedReturnValue(v233);
    v526[221] = v497;
    v527[221] = CFSTR("利空");
    v234 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592544LL);
    v498 = objc_retainAutoreleasedReturnValue(v234);
    v526[222] = v498;
    v527[222] = CFSTR("贡献度");
    v235 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592741LL);
    v499 = objc_retainAutoreleasedReturnValue(v235);
    v526[223] = v499;
    v527[223] = CFSTR("机构动向");
    v236 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2579LL);
    v500 = objc_retainAutoreleasedReturnValue(v236);
    v526[224] = v500;
    v527[224] = CFSTR("机构持股比例");
    v237 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2570LL);
    v501 = objc_retainAutoreleasedReturnValue(v237);
    v526[225] = v501;
    v527[225] = CFSTR("卖出信号");
    v238 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2719LL);
    v502 = objc_retainAutoreleasedReturnValue(v238);
    v526[226] = v502;
    v527[226] = CFSTR("人均持股数");
    v239 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 615LL);
    v503 = objc_retainAutoreleasedReturnValue(v239);
    v526[227] = v503;
    v527[227] = CFSTR("利润总额");
    v240 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 625362LL);
    v504 = objc_retainAutoreleasedReturnValue(v240);
    v526[228] = v504;
    v527[228] = CFSTR("每股公积金");
    v241 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 543LL);
    v505 = objc_retainAutoreleasedReturnValue(v241);
    v526[229] = v505;
    v527[229] = CFSTR("资产总计");
    v242 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1674LL);
    v506 = objc_retainAutoreleasedReturnValue(v242);
    v526[230] = v506;
    v527[230] = CFSTR("流通A股");
    v243 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 410LL);
    v507 = objc_retainAutoreleasedReturnValue(v243);
    v526[231] = v507;
    v527[231] = CFSTR("流通B股");
    v244 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2263506LL);
    v508 = objc_retainAutoreleasedReturnValue(v244);
    v526[232] = v508;
    v527[232] = CFSTR("流通比例%");
    v245 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 592946LL);
    v509 = objc_retainAutoreleasedReturnValue(v245);
    v526[233] = v509;
    v527[233] = CFSTR("多空比");
    v246 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330321LL);
    v510 = objc_retainAutoreleasedReturnValue(v246);
    v526[234] = v510;
    v527[234] = CFSTR("异动类型");
    v247 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330322LL);
    v511 = objc_retainAutoreleasedReturnValue(v247);
    v526[235] = v511;
    v527[235] = CFSTR("竞价评级");
    v248 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 920428LL);
    v512 = objc_retainAutoreleasedReturnValue(v248);
    v526[236] = v512;
    v527[236] = CFSTR("股票分类标记");
    v249 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1121LL);
    v513 = objc_retainAutoreleasedReturnValue(v249);
    v526[237] = v513;
    v527[237] = CFSTR("标记");
    v250 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 133702LL);
    v514 = objc_retainAutoreleasedReturnValue(v250);
    v526[238] = v514;
    v527[238] = CFSTR("细分行业");
    v251 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330325LL);
    v515 = objc_retainAutoreleasedReturnValue(v251);
    v526[239] = v515;
    v527[239] = CFSTR("涨停类型");
    v252 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330329LL);
    v516 = objc_retainAutoreleasedReturnValue(v252);
    v526[240] = v516;
    v527[240] = CFSTR("涨停状态");
    v253 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134160LL);
    v517 = objc_retainAutoreleasedReturnValue(v253);
    v526[241] = v517;
    v527[241] = v254;
    v255 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 134162LL);
    v518 = objc_retainAutoreleasedReturnValue(v255);
    v526[242] = v518;
    v527[242] = CFSTR("折溢率");
    v256 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 20190901LL);
    v519 = objc_retainAutoreleasedReturnValue(v256);
    v526[243] = v519;
    v527[243] = CFSTR("标签");
    v257 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 395720LL);
    v520 = objc_retainAutoreleasedReturnValue(v257);
    v526[244] = v520;
    v527[244] = CFSTR("委比");
    v258 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 53LL);
    v521 = objc_retainAutoreleasedReturnValue(v258);
    v526[245] = v521;
    v527[245] = v259;
    v260 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 82LL);
    v522 = objc_retainAutoreleasedReturnValue(v260);
    v526[246] = v522;
    v527[246] = CFSTR("撤单时间");
    v261 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 56LL);
    v523 = objc_retainAutoreleasedReturnValue(v261);
    v526[247] = v523;
    v527[247] = CFSTR("挂单时间");
    v262 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 18550831LL);
    v524 = objc_retainAutoreleasedReturnValue(v262);
    v526[248] = v524;
    v527[248] = CFSTR("成分股数");
    v263 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 8311855LL);
    v264 = objc_retainAutoreleasedReturnValue(v263);
    v526[249] = v264;
    v527[249] = CFSTR("类别");
    v265 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v527, v526, 250LL);
    v266 = objc_retainAutoreleasedReturnValue(v265);
    v272[0] = _NSConcreteStackBlock;
    v272[1] = 3254779904LL;
    v272[2] = sub_100B8E89D;
    v272[3] = &unk_1012DE980;
    v273 = objc_retain(v525);
    v274 = v323;
    v267(v323);
    _objc_msgSend(v266, "enumerateKeysAndObjectsUsingBlock:", v272);
    v269 = _objc_msgSend(v268, "copy");
    v5 = v525;
  }
  else
  {
    v269 = 0LL;
  }
  return objc_autoreleaseReturnValue(v269);
}

//----- (0000000100B8E89D) ----------------------------------------------------
void __fastcall sub_100B8E89D(__int64 a1, void *a2, void *a3)
{
  id (__cdecl *v5)(id); // r12
  id (*v8)(id, SEL, ...); // r12

  v4 = objc_retain(a2);
  v6 = v5(a3);
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)v8(v6, "isKindOfClass:", v7)
    && (unsigned __int8)_objc_msgSend(v6, "containsString:", *(_QWORD *)(a1 + 32)) )
  {
    _objc_msgSend(*(id *)(a1 + 40), "addObject:", v4);
  }
}

//----- (0000000100B8E944) ----------------------------------------------------
char __cdecl +[HXTools isPureInt:](id a1, SEL a2, id a3)
{
  NSScanner *v3; // rax
  NSScanner *v4; // rbx
  bool v5; // r14

  v3 = _objc_msgSend(&OBJC_CLASS___NSScanner, "scannerWithString:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( (unsigned __int8)_objc_msgSend(v4, "scanInt:", v7) )
    v5 = (unsigned __int8)_objc_msgSend(v4, "isAtEnd") != 0;
  else
    v5 = 0;
  return v5;
}

//----- (0000000100B8E9B8) ----------------------------------------------------
unsigned __int64 __cdecl +[HXTools getDispatchTimeByDate:](id a1, SEL a2, id a3)
{
  timespec when; // [rsp+0h] [rbp-20h] BYREF
  long double __iptr; // [rsp+10h] [rbp-10h] BYREF

  _objc_msgSend(a3, "timeIntervalSince1970");
  *(_QWORD *)&__iptr = 0LL;
  modf(*(long double *)&when, &__iptr);
  when.tv_sec = (unsigned int)(int)*(double *)&__iptr;
  when.tv_nsec = (unsigned int)(int)(v3 * 1000000000.0);
  return dispatch_walltime(&when, 0LL);
}

//----- (0000000100B8EA0F) ----------------------------------------------------
id __cdecl +[HXTools urlEncodeOtherCharacters:](id a1, SEL a2, id a3)
{
  NSMutableCharacterSet *v4; // rax
  NSMutableCharacterSet *v5; // rax
  NSMutableCharacterSet *v6; // rbx

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(
         &OBJC_CLASS___NSMutableCharacterSet,
         "characterSetWithCharactersInString:",
         CFSTR("!*'();:@&=+$,/?#[]"));
  objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(
         &OBJC_CLASS___NSMutableCharacterSet,
         "characterSetWithCharactersInString:",
         CFSTR("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~"));
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v7, "formUnionWithCharacterSet:", v6);
  v9 = _objc_msgSend(v3, "stringByAddingPercentEncodingWithAllowedCharacters:", v8);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  return objc_autoreleaseReturnValue(v10);
}

//----- (0000000100B8EAD2) ----------------------------------------------------
id __cdecl +[HXTools urlencode:](id a1, SEL a2, id a3)
{
  __CFString *v8; // r15
  int v10; // ecx
  __CFString *v12; // r14

  v14 = objc_retain(a3);
  v3 = _objc_msgSend(v14, "stringByAddingPercentEscapesUsingEncoding:", 4LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (char *)_objc_msgSend(v4, "length");
  v15 = objc_retainAutorelease(v4);
  v6 = _objc_msgSend(v15, "UTF8String");
  if ( (__int64)v5 <= 0 )
  {
    v8 = &charsToLeaveEscaped;
  }
  else
  {
    v7 = v6;
    v8 = &charsToLeaveEscaped;
    for ( i = 0LL; i != v5; ++i )
    {
      v10 = i[(_QWORD)v7];
      switch ( i[(_QWORD)v7] )
      {
        case '!':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%21"));
          break;
        case '"':
        case '%':
        case '-':
        case '.':
        case '0':
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
        case '7':
        case '8':
        case '9':
        case '<':
        case '>':
          goto LABEL_8;
        case '#':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%23"));
          break;
        case '$':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%24"));
          break;
        case '&':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%26"));
          break;
        case '\'':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%27"));
          break;
        case '(':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%28"));
          break;
        case ')':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%29"));
          break;
        case '*':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%2A"));
          break;
        case '+':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%2B"));
          break;
        case ',':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%2C"));
          break;
        case '/':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%2F"));
          break;
        case ':':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%3A"));
          break;
        case ';':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%3B"));
          break;
        case '=':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%3D"));
          break;
        case '?':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%3F"));
          break;
        case '@':
          v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%40"));
          break;
        default:
          if ( v10 == 91 )
          {
            v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%5B"));
          }
          else if ( v10 == 93 )
          {
            v11 = _objc_msgSend(v8, "stringByAppendingString:", CFSTR("%5D"));
          }
          else
          {
LABEL_8:
            v11 = _objc_msgSend(v8, "stringByAppendingFormat:", CFSTR("%c"));
          }
          break;
      }
      v12 = v8;
      v8 = (__CFString *)objc_retainAutoreleasedReturnValue(v11);
    }
  }
  return objc_autoreleaseReturnValue(v8);
}

//----- (0000000100B8EDC4) ----------------------------------------------------
char __cdecl +[HXTools isValidUrl:](id a1, SEL a2, id a3)
{
  NSURL *v4; // rax
  NSURL *v5; // r15

  v3 = objc_retain(a3);
  if ( !_objc_msgSend(v3, "length") )
    goto LABEL_6;
  v4 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "scheme");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( !v7 )
  {
    v12(v5);
LABEL_6:
    v10 = 0;
    goto LABEL_7;
  }
  v8 = _objc_msgSend(v5, "host");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = 1;
  if ( !v11 )
    goto LABEL_6;
LABEL_7:
  return v10;
}

//----- (0000000100B8EEA5) ----------------------------------------------------
id __cdecl +[HXTools sharedInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100B8EF08;
  block[3] = (__int64)&unk_1012DB3B8;
  block[4] = (__int64)a1;
  if ( qword_1016D3860 != -1 )
    dispatch_once(&qword_1016D3860, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3858);
}

//----- (0000000100B8EF08) ----------------------------------------------------
void __fastcall sub_100B8EF08(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3858;
  qword_1016D3858 = v2;
}

//----- (0000000100B8EF3A) ----------------------------------------------------
HXTools *__cdecl -[HXTools init](HXTools *self, SEL a2)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___HXTools;
  return (HXTools *)objc_msgSendSuper2(&v3, "init");
}

//----- (0000000100B8EF69) ----------------------------------------------------
id __cdecl +[HXTools marketConvertToTablePanKouType:stockCode:](id a1, SEL a2, id a3, id a4)
{

  v4 = _objc_msgSend(a1, "getStockType:stockCode:", a3, a4);
  return _objc_msgSend(a1, "stockTypeConvertToTablePanKouType:", v4);
}

//----- (0000000100B8EF9A) ----------------------------------------------------
id __cdecl +[HXTools marketConvertToTablePanKouType:](id a1, SEL a2, id a3)
{

  v3 = _objc_msgSend(a1, "getStockTypeWithMarket:", a3);
  return _objc_msgSend(a1, "stockTypeConvertToTablePanKouType:", v3);
}

//----- (0000000100B8EFCB) ----------------------------------------------------
id __cdecl +[HXTools stockTypeConvertToTablePanKouType:](id a1, SEL a2, unsigned __int64 a3)
{
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v6; // rax
  NSNumber *v8; // rax
  NSNumber *v10; // rax
  NSNumber *v12; // rax
  NSNumber *v14; // rax
  NSNumber *v16; // rax
  NSNumber *v18; // rax
  NSNumber *v20; // rax
  NSNumber *v22; // rax
  NSNumber *v24; // rax
  NSNumber *v26; // rax
  NSNumber *v28; // rax
  NSNumber *v30; // rax
  NSNumber *v32; // rax
  NSNumber *v34; // rax
  NSNumber *v36; // rax
  NSNumber *v38; // rax
  NSNumber *v40; // rax
  NSNumber *v42; // rax
  NSNumber *v44; // rax
  NSNumber *v46; // rax
  NSNumber *v49; // rax
  NSNumber *v50; // rbx
  NSNumber *v81; // rax
  NSNumber *v82; // r15
  __CFString *v84; // rbx
  NSNumber *v89; // [rsp+10h] [rbp-270h]
  NSNumber *v90; // [rsp+18h] [rbp-268h]
  NSNumber *v91; // [rsp+20h] [rbp-260h]
  NSNumber *v92; // [rsp+28h] [rbp-258h]
  NSNumber *v93; // [rsp+30h] [rbp-250h]
  NSNumber *v94; // [rsp+38h] [rbp-248h]
  NSNumber *v95; // [rsp+40h] [rbp-240h]
  NSNumber *v96; // [rsp+48h] [rbp-238h]
  NSNumber *v97; // [rsp+50h] [rbp-230h]
  NSNumber *v98; // [rsp+58h] [rbp-228h]
  NSNumber *v99; // [rsp+60h] [rbp-220h]
  NSNumber *v100; // [rsp+68h] [rbp-218h]
  NSNumber *v101; // [rsp+70h] [rbp-210h]
  NSNumber *v102; // [rsp+78h] [rbp-208h]
  NSNumber *v103; // [rsp+80h] [rbp-200h]
  NSNumber *v104; // [rsp+88h] [rbp-1F8h]
  NSNumber *v105; // [rsp+90h] [rbp-1F0h]
  NSNumber *v106; // [rsp+98h] [rbp-1E8h]
  NSNumber *v107; // [rsp+A0h] [rbp-1E0h]
  NSNumber *v108; // [rsp+A8h] [rbp-1D8h]
  NSNumber *v109; // [rsp+B0h] [rbp-1D0h]
  NSNumber *v110; // [rsp+B8h] [rbp-1C8h]
  NSNumber *v111; // [rsp+C0h] [rbp-1C0h]
  _QWORD v112[25]; // [rsp+188h] [rbp-F8h] BYREF

  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 11LL);
  v111 = objc_retainAutoreleasedReturnValue(v3);
  v112[0] = CFSTR("HuShenAGuStockTablePanKou");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
  v89 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v5 + 8) = v89;
  v112[1] = CFSTR("HuShenIndexTablePanKou");
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 3LL);
  v90 = objc_retainAutoreleasedReturnValue(v6);
  *(_QWORD *)(v7 + 16) = v90;
  v112[2] = CFSTR("HuShenBlockStockTablePanKou");
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
  v91 = objc_retainAutoreleasedReturnValue(v8);
  *(_QWORD *)(v9 + 24) = v91;
  v112[3] = CFSTR("GangGuIndexStockTablePanKou");
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
  v92 = objc_retainAutoreleasedReturnValue(v10);
  *(_QWORD *)(v11 + 32) = v92;
  v112[4] = CFSTR("GangGuStockStockTablePanKou");
  v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 22LL);
  v93 = objc_retainAutoreleasedReturnValue(v12);
  *(_QWORD *)(v13 + 40) = v93;
  v112[5] = CFSTR("WoLunNiuXiongTablePanKou");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 6LL);
  v94 = objc_retainAutoreleasedReturnValue(v14);
  *(_QWORD *)(v15 + 48) = v94;
  v112[6] = CFSTR("QiHuoStockTablePanKou");
  v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 24LL);
  v95 = objc_retainAutoreleasedReturnValue(v16);
  *(_QWORD *)(v17 + 56) = v95;
  v112[7] = CFSTR("QiHuoStockTablePanKou");
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 7LL);
  v96 = objc_retainAutoreleasedReturnValue(v18);
  *(_QWORD *)(v19 + 64) = v96;
  v112[8] = CFSTR("QuanQiuIndexStockTablePanKou");
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 8LL);
  v97 = objc_retainAutoreleasedReturnValue(v20);
  *(_QWORD *)(v21 + 72) = v97;
  v112[9] = CFSTR("MeiGuStockStockTablePanKou");
  v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 9LL);
  v98 = objc_retainAutoreleasedReturnValue(v22);
  *(_QWORD *)(v23 + 80) = v98;
  v112[10] = CFSTR("HuShenFundStockTablePanKou");
  v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 10LL);
  v99 = objc_retainAutoreleasedReturnValue(v24);
  *(_QWORD *)(v25 + 88) = v99;
  v112[11] = CFSTR("WaiHuiStockTablePanKou");
  v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 12LL);
  v100 = objc_retainAutoreleasedReturnValue(v26);
  *(_QWORD *)(v27 + 96) = v100;
  v112[12] = CFSTR("HuShenBGuStockTablePanKou");
  v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 13LL);
  v101 = objc_retainAutoreleasedReturnValue(v28);
  *(_QWORD *)(v29 + 104) = v101;
  v112[13] = CFSTR("QiHuoStockTablePanKou");
  v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 14LL);
  v102 = objc_retainAutoreleasedReturnValue(v30);
  *(_QWORD *)(v31 + 112) = v102;
  v112[14] = CFSTR("QiHuoStockTablePanKou");
  v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 15LL);
  v103 = objc_retainAutoreleasedReturnValue(v32);
  *(_QWORD *)(v33 + 120) = v103;
  v112[15] = CFSTR("XinSanBanStockTablePanKou");
  v34 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 17LL);
  v104 = objc_retainAutoreleasedReturnValue(v34);
  *(_QWORD *)(v35 + 128) = v104;
  v112[16] = CFSTR("XinSanBanStockTablePanKou");
  v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 16LL);
  v105 = objc_retainAutoreleasedReturnValue(v36);
  *(_QWORD *)(v37 + 136) = v105;
  v112[17] = CFSTR("XinSanBanIndexTablePanKou");
  v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 18LL);
  v106 = objc_retainAutoreleasedReturnValue(v38);
  *(_QWORD *)(v39 + 144) = v106;
  v112[18] = CFSTR("HuShenBondTablePanKou");
  v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 19LL);
  v107 = objc_retainAutoreleasedReturnValue(v40);
  *(_QWORD *)(v41 + 152) = v107;
  v112[19] = CFSTR("OptionStockTablePanKou");
  v42 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 20LL);
  v108 = objc_retainAutoreleasedReturnValue(v42);
  *(_QWORD *)(v43 + 160) = v108;
  v112[20] = CFSTR("OptionStockTablePanKou");
  v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 21LL);
  v109 = objc_retainAutoreleasedReturnValue(v44);
  *(_QWORD *)(v45 + 168) = v109;
  v112[21] = CFSTR("YingGuStockStockTablePanKou");
  v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 23LL);
  v110 = objc_retainAutoreleasedReturnValue(v46);
  *(_QWORD *)(v47 + 176) = v110;
  v48 = v47;
  v112[22] = CFSTR("CDRStockTablePanKou");
  v49 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v50 = objc_retainAutoreleasedReturnValue(v49);
  *(_QWORD *)(v48 + 184) = v50;
  v112[23] = CFSTR("DefaultStockTablePanKou");
  v52 = _objc_msgSend(&OBJC_CLASS___NSNumber, v51, 25LL);
  v53 = objc_retainAutoreleasedReturnValue(v52);
  *(_QWORD *)(v48 + 192) = v53;
  v112[24] = CFSTR("JingShiStockTablePanKou");
  v55 = (void *)v54(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v112, v48, 25LL);
  v56 = objc_retainAutoreleasedReturnValue(v55);
  v57(v50);
  v58(v110);
  v59(v109);
  v60(v108);
  v61(v107);
  v62(v106);
  v63(v105);
  v64(v104);
  v65(v103);
  v66(v102);
  v67(v101);
  v68(v100);
  v69(v99);
  v70(v98);
  v71(v97);
  v72(v96);
  v73(v95);
  v74(v94);
  v75(v93);
  v76(v92);
  v77(v91);
  v78(v90);
  v79(v89);
  v80(v111);
  v81 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", a3);
  v82 = objc_retainAutoreleasedReturnValue(v81);
  v83 = _objc_msgSend(v56, "objectForKey:", v82);
  v84 = (__CFString *)objc_retainAutoreleasedReturnValue(v83);
  v85(v82);
  if ( !v84 )
    v84 = CFSTR("DefaultStockTablePanKou");
  v86(v56);
  return objc_autoreleaseReturnValue(v84);
}

//----- (0000000100B8F688) ----------------------------------------------------
id __cdecl +[HXTools getPanKouCorrelatedCodeWithName:market:stockCode:](id a1, SEL a2, id a3, id a4, id a5)
{
  __CFString *v8; // rbx
  __CFString *v11; // r13
  __CFString *v12; // r15
  NSString *v13; // rax
  __CFString *v14; // r14
  NSArray *v15; // rax
  NSArray *v16; // rbx
  __CFString *v19; // r12
  __CFString *v21; // r12
  NSDictionary *v23; // rax
  NSString *v30; // rax
  __CFString *v33; // [rsp+8h] [rbp-88h]
  NSDictionary *v34; // [rsp+10h] [rbp-80h]
  _QWORD v35[3]; // [rsp+18h] [rbp-78h] BYREF
  _QWORD v36[3]; // [rsp+30h] [rbp-60h] BYREF
  _QWORD v37[3]; // [rsp+48h] [rbp-48h] BYREF

  v7 = objc_retain(a3);
  objc_retain(a4);
  v8 = (__CFString *)objc_retain(a5);
  if ( (unsigned __int8)_objc_msgSend(v9, "isEqualToString:", CFSTR("USOO")) )
  {
    v11 = CFSTR("USHJ");
    v12 = CFSTR("510050");
    v13 = _objc_msgSend(
            &OBJC_CLASS___NSString,
            "stringWithFormat:",
            CFSTR("%@ %@ %@"),
            CFSTR("50ETF"),
            CFSTR("USHJ"),
            CFSTR("510050"));
    v14 = objc_retainAutoreleasedReturnValue(v13);
  }
  else
  {
    v33 = v8;
    v37[0] = CFSTR("UCFT");
    v37[1] = CFSTR("UCFL");
    v37[2] = CFSTR("UCFX");
    v15 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v37, 3LL);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v18 = (unsigned __int8)_objc_msgSend(v16, "containsObject:", v17);
    v14 = &charsToLeaveEscaped;
    if ( v18 )
    {
      v20 = _objc_msgSend(v7, "rangeOfString:", CFSTR("购"));
      if ( v20 == (id)0x7FFFFFFFFFFFFFFFLL
        && (v20 = _objc_msgSend(v7, "rangeOfString:", CFSTR("沽")), v20 == (id)0x7FFFFFFFFFFFFFFFLL) )
      {
        v12 = v33;
        v11 = v21;
      }
      else
      {
        v22 = _objc_msgSend(v7, "substringToIndex:", v20);
        objc_retainAutoreleasedReturnValue(v22);
        v35[0] = CFSTR("UCFT");
        v36[0] = CFSTR("UCFD");
        v35[1] = CFSTR("UCFL");
        v36[1] = CFSTR("UCFZ");
        v35[2] = CFSTR("UCFX");
        v36[2] = CFSTR("UCFS");
        v23 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v36, v35, 3LL);
        v34 = objc_retainAutoreleasedReturnValue(v23);
        v25 = +[HXTools getDecodeCodeContactWithMarket:code:](
                &OBJC_CLASS___HXTools,
                "getDecodeCodeContactWithMarket:code:",
                v24,
                v33);
        v12 = (__CFString *)objc_retainAutoreleasedReturnValue(v25);
        v27 = _objc_msgSend(v34, "objectForKeyedSubscript:", v26);
        v11 = (__CFString *)objc_retainAutoreleasedReturnValue(v27);
        v30 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@ %@ %@"), v29, v11, v12);
        v14 = objc_retainAutoreleasedReturnValue(v30);
      }
    }
    else
    {
      v12 = v33;
      v11 = v19;
    }
  }
  return objc_autoreleaseReturnValue(v14);
}

//----- (0000000100B8F9A6) ----------------------------------------------------
id __cdecl +[HXTools mergeArray:into:](id a1, SEL a2, id a3, id a4)
{
  NSNumber *v7; // rax
  NSNumber *v13; // r12
  NSNumber *v15; // rax
  NSNumber *v16; // rbx
  SEL v18; // r12
  SEL v21; // r12
  unsigned __int64 v22; // r12
  unsigned __int64 v30; // r13
  NSNumber *v51; // [rsp+70h] [rbp-30h]

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  if ( _objc_msgSend(v5, "count") )
  {
    if ( _objc_msgSend(v6, "count") )
    {
      v49 = v6;
      v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      objc_retainAutoreleasedReturnValue(v7);
      v8 = _objc_msgSend(v5, "firstObject");
      v9 = v5;
      v10 = objc_retainAutoreleasedReturnValue(v8);
      v11 = _objc_msgSend(v10, "allKeys");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v51 = v13;
      _objc_msgSend(v12, "containsObject:", v13);
      if ( !v14 )
      {
        v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 1LL);
        v16 = objc_retainAutoreleasedReturnValue(v15);
        v51 = v16;
      }
      v45 = _objc_msgSend(__NSArray0__, "mutableCopy");
      v17 = _objc_msgSend(v9, "count");
      v19 = v49;
      if ( v17 > _objc_msgSend(v49, v18) )
        v19 = v9;
      v48 = objc_retain(v19);
      v46 = v9;
      if ( v48 == v9 )
        v9 = v49;
      v20 = objc_retain(v9);
      v47 = _objc_msgSend(__NSDictionary0__, "mutableCopy");
      v44 = v20;
      if ( _objc_msgSend(v20, v21) )
      {
        v22 = 0LL;
        do
        {
          v23 = _objc_msgSend(v20, "thsDictionaryAtIndex:", v22);
          v24 = objc_retainAutoreleasedReturnValue(v23);
          v25 = _objc_msgSend(v24, "objectForKey:", v51);
          v26 = objc_retainAutoreleasedReturnValue(v25);
          v27 = v26;
          if ( v26 )
            _objc_msgSend(v47, "setObject:forKey:", v24, v26);
          v20 = v44;
          v28 = _objc_msgSend(v44, "count");
        }
        while ( (unsigned __int64)v28 > v22 );
      }
      v29 = v48;
      if ( _objc_msgSend(v48, "count") )
      {
        v30 = 0LL;
        do
        {
          v31 = _objc_msgSend(v29, "thsDictionaryAtIndex:", v30);
          v32 = objc_retainAutoreleasedReturnValue(v31);
          _objc_msgSend(v32, "mutableCopy");
          v50 = v33;
          v34 = _objc_msgSend(v33, "objectForKeyedSubscript:", v51);
          v35 = objc_retainAutoreleasedReturnValue(v34);
          v36 = v35;
          if ( v35 )
          {
            v37 = _objc_msgSend(v47, "thsDictionaryForKey:", v35);
            v43 = objc_retainAutoreleasedReturnValue(v37);
            v38 = _objc_msgSend(v43, "objectForKeyedSubscript:", v51);
            v39 = objc_retainAutoreleasedReturnValue(v38);
            if ( (unsigned __int8)_objc_msgSend(v36, "isEqual:", v39) )
              _objc_msgSend(v50, "addEntriesFromDictionary:", v43);
            _objc_msgSend(v45, "addObject:", v50);
          }
          ++v30;
          v29 = v48;
        }
        while ( (__int64)_objc_msgSend(v48, "count") > v30 );
      }
      v5 = v46;
      v6 = v49;
    }
    else
    {
      _objc_msgSend(v5, "mutableCopy");
    }
  }
  else
  {
    _objc_msgSend(v6, "mutableCopy");
  }
  return objc_autoreleaseReturnValue(v41);
}

//----- (0000000100B8FDB5) ----------------------------------------------------
id __cdecl +[HXTools readDictFromLocalWithKey:andFileName:](id a1, SEL a2, id a3, id a4)
{

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = objc_alloc(&OBJC_CLASS___NSMutableData);
  v8 = _objc_msgSend(v7, "initWithContentsOfFile:", v6);
  v9 = 0LL;
  if ( v8 )
  {
    v10 = objc_alloc(&OBJC_CLASS___NSKeyedUnarchiver);
    v12 = _objc_msgSend(v10, "initForReadingWithData:", v11);
    v13 = _objc_msgSend(v12, "decodeObjectForKey:", v5);
    v9 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v12, "finishDecoding");
  }
  return objc_autoreleaseReturnValue(v9);
}

//----- (0000000100B8FED3) ----------------------------------------------------
void __cdecl +[HXTools writeDictToLocalWithData:andKey:andFilename:](id a1, SEL a2, id a3, id a4, id a5)
{

  if ( a3 )
  {
    v13 = objc_retain(a5);
    v7 = objc_retain(a4);
    objc_retain(a3);
    v8 = objc_alloc(&OBJC_CLASS___NSMutableData);
    v14 = _objc_msgSend(v8, "init");
    v9 = objc_alloc(&OBJC_CLASS___NSKeyedArchiver);
    v10 = _objc_msgSend(v9, "initForWritingWithMutableData:", v14);
    _objc_msgSend(v10, "encodeObject:forKey:", v11, v7);
    _objc_msgSend(v10, "finishEncoding");
    _objc_msgSend(v14, "writeToFile:atomically:", v13, 1LL);
  }
}

//----- (0000000100B8FFD0) ----------------------------------------------------
char __cdecl +[HXTools isIAPVersion](id a1, SEL a2)
{
  bool v4; // bl

  v2 = _objc_msgSend(a1, "getQsid");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = 1;
  if ( !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("7000")) )
    v4 = (unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("7004")) != 0;
  return v4;
}

//----- (0000000100B90040) ----------------------------------------------------
id __cdecl +[HXTools getQsid](id a1, SEL a2)
{
  NSDictionary *v8; // rax
  NSDictionary *v9; // rax
  __CFString *v11; // rbx

  v2 = _objc_msgSend(&OBJC_CLASS___NSBundle, "mainBundle");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "pathForResource:ofType:", CFSTR("dingzhi"), CFSTR("plist"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( (unsigned __int8)_objc_msgSend(v7, "fileExistsAtPath:", v5)
    && (v8 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithContentsOfFile:", v5),
        v9 = objc_retainAutoreleasedReturnValue(v8),
        v10 = _objc_msgSend(v9, "thsStringForKey:", CFSTR("QSID")),
        v11 = (__CFString *)objc_retainAutoreleasedReturnValue(v10),
        v11) )
  {
    if ( !(unsigned __int8)_objc_msgSend(v11, "isEqualToString:", &charsToLeaveEscaped) )
      goto LABEL_7;
  }
  else
  {
    v11 = 0LL;
  }
  v11 = CFSTR("7000");
LABEL_7:
  v13(v5);
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100B90174) ----------------------------------------------------
id __cdecl +[HXTools convertPythonHexColorStringToOCColor:](id a1, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  int v32; // [rsp+4h] [rbp-4Ch] BYREF
  int v36; // [rsp+20h] [rbp-30h] BYREF
  int v37[11]; // [rsp+24h] [rbp-2Ch] BYREF

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v7 = v6(v3, "stringByReplacingOccurrencesOfString:withString:", CFSTR("#"), &charsToLeaveEscaped);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( v9(v8, "length") == (id)6 )
    {
      v11 = v10(v8, "substringWithRange:", 0LL, 2LL);
      v33 = objc_retainAutoreleasedReturnValue(v11);
      v13 = v12(v8, "substringWithRange:", 2LL, 2LL);
      v34 = objc_retainAutoreleasedReturnValue(v13);
      v15 = v14(v8, "substringWithRange:", 4LL, 2LL);
      v35 = objc_retainAutoreleasedReturnValue(v15);
      v17 = v16(&OBJC_CLASS___NSScanner, "scannerWithString:", v33);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19(v18, "scanHexInt:", &v36);
      v21 = v20(&OBJC_CLASS___NSScanner, "scannerWithString:", v34);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23(v22, "scanHexInt:", v37);
      v25 = v24(&OBJC_CLASS___NSScanner, "scannerWithString:", v35);
      v26 = objc_retainAutoreleasedReturnValue(v25);
      v27(v26, "scanHexInt:", &v32);
      v29 = v28(
              &OBJC_CLASS___NSColor,
              "colorWithRed:green:blue:alpha:",
              (double)v36 / 255.0,
              (double)v37[0] / 255.0,
              (double)v32 / 255.0,
              1.0);
      v30 = objc_retainAutoreleasedReturnValue(v29);
    }
    else
    {
      v30 = 0LL;
    }
  }
  else
  {
    v30 = 0LL;
  }
  return objc_autoreleaseReturnValue(v30);
}

//----- (0000000100B903B5) ----------------------------------------------------
id __cdecl +[HXTools transCodingXMLData:](id a1, SEL a2, id a3)
{
  unsigned __int64 v6; // rbx
  NSString *v24; // rax
  NSString *v34; // [rsp+10h] [rbp-30h]

  if ( !a3 )
    return objc_autoreleaseReturnValue(0LL);
  v3 = objc_retain(a3);
  v4 = objc_alloc(&OBJC_CLASS___NSData);
  v5 = _objc_msgSend(v4, "initWithData:", v3);
  v6 = CFStringConvertEncodingToNSStringEncoding(0x631u);
  v7 = objc_alloc(&OBJC_CLASS___NSString);
  v8 = _objc_msgSend(v7, "initWithData:encoding:", v5, v6);
  if ( _objc_msgSend(v8, "rangeOfString:", CFSTR("encoding=\"UTF-8\"")) == (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v10 = (char *)_objc_msgSend(v9, "rangeOfString:", CFSTR("encoding=\""));
    v12 = &v10[v11];
    if ( &v10[v11] <= _objc_msgSend(v13, "length") )
    {
      v32 = v5;
      v18 = _objc_msgSend(v14, "substringFromIndex:", v12);
      v33 = objc_retainAutoreleasedReturnValue(v18);
      v19 = (char *)_objc_msgSend(v33, "rangeOfString:", CFSTR("\""));
      if ( &v12[(_QWORD)v19] <= _objc_msgSend(v20, "length") )
      {
        v22 = _objc_msgSend(v21, "substringWithRange:", v12, v19);
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v24 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@\""), CFSTR("encoding=\""), v23);
        v34 = objc_retainAutoreleasedReturnValue(v24);
        v26 = _objc_msgSend(v25, "stringByReplacingOccurrencesOfString:withString:", v34, CFSTR("encoding=\"UTF-8\""));
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v29 = _objc_msgSend(v27, "dataUsingEncoding:", 4LL);
        v30 = objc_retainAutoreleasedReturnValue(v29);
        v16 = _objc_msgSend(v30, "copy");
      }
      else
      {
        v16 = 0LL;
      }
      v15 = v32;
    }
    else
    {
      v15 = v5;
      v16 = 0LL;
    }
  }
  else
  {
    v15 = v5;
    v16 = _objc_msgSend(v5, "copy");
  }
  return objc_autoreleaseReturnValue(v16);
}

//----- (0000000100B90605) ----------------------------------------------------
void __cdecl +[HXTools writeFileLog:](id a1, SEL a2, id a3)
{
  +[HXLoggerManager trackLog:log:showStack:](&OBJC_CLASS___HXLoggerManager, "trackLog:log:showStack:", 100LL, a3, 0LL);
}

//----- (0000000100B90629) ----------------------------------------------------
void __cdecl -[HXTools setShanghaiDateFormatter:](HXTools *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ShanghaiDateFormatter, a3);
}

//----- (0000000100B9063A) ----------------------------------------------------
void __cdecl -[HXTools .cxx_destruct](HXTools *self, SEL a2)
{
  objc_storeStrong((id *)&self->_ShanghaiDateFormatter, 0LL);
}

