void __cdecl -[Zhang<PERSON>ieTingStocksPoolViewController viewDidLoad](ZhangDieTingStocksPoolViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhangDieTingStocksPoolViewController;
  objc_msgSendSuper2(&v2, "viewDidLoad");
  -[ZhangDieTingStocksPoolViewController initViewState](self, "initViewState");
  -[ZhangDieTingStocksPoolViewController addNotifications](self, "addNotifications");
}

//----- (00000001001EDB1B) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController initViewState](ZhangDieTingStocksPoolViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v37)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v45)(id, SEL, ...); // r12
  id (*v48)(id, SEL, ...); // r12
  id (*v52)(id, SEL, ...); // r12
  id (*v56)(id, SEL, ...); // r12
  id (*v60)(id, SEL, ...); // r12
  id (*v64)(id, SEL, ...); // r12
  id (*v68)(id, SEL, ...); // r12
  id (*v71)(id, SEL, ...); // r12
  id (*v75)(id, SEL, ...); // r12
  id (*v79)(id, SEL, ...); // r12
  id (*v83)(id, SEL, ...); // r12
  id (*v86)(id, SEL, ...); // r12
  id (*v90)(id, SEL, ...); // r12
  id (*v94)(id, SEL, ...); // r12
  id (*v98)(id, SEL, ...); // r12
  id (*v101)(id, SEL, ...); // r12
  id (*v105)(id, SEL, ...); // r12
  id (*v109)(id, SEL, ...); // r12
  id (*v113)(id, SEL, ...); // r12
  id (*v116)(id, SEL, ...); // r12
  id (*v120)(id, SEL, ...); // r12
  id (*v123)(id, SEL, ...); // r12
  id (*v127)(id, SEL, ...); // r12
  id (*v130)(id, SEL, ...); // r12
  id (*v134)(id, SEL, ...); // r12
  id (*v138)(id, SEL, ...); // r12
  id (*v142)(id, SEL, ...); // r12
  id (*v146)(id, SEL, ...); // r12
  id (*v149)(id, SEL, ...); // r12
  id (*v153)(id, SEL, ...); // r12
  id (*v156)(id, SEL, ...); // r12
  id (*v160)(id, SEL, ...); // r12
  id (*v163)(id, SEL, ...); // r12
  id (*v167)(id, SEL, ...); // r12
  id (*v171)(id, SEL, ...); // r12
  id (*v175)(id, SEL, ...); // r12
  id (*v178)(id, SEL, ...); // r12
  id (*v182)(id, SEL, ...); // r12
  id (*v185)(id, SEL, ...); // r12
  id (*v189)(id, SEL, ...); // r12
  id (*v193)(id, SEL, ...); // r12
  id (*v196)(id, SEL, ...); // r12
  id (*v200)(id, SEL, ...); // r12
  id (*v204)(id, SEL, ...); // r12
  id (*v208)(id, SEL, ...); // r12
  id (*v211)(id, SEL, ...); // r12
  id (*v215)(id, SEL, ...); // r12
  id (*v218)(id, SEL, ...); // r12
  id (*v222)(id, SEL, ...); // r12
  id (*v225)(id, SEL, ...); // r12

  v2 = -[ZhangDieTingStocksPoolViewController ztTableSwitchController](self, "ztTableSwitchController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "setSelectedIndex:", 0LL);
  v6 = v5(self, "dtTableSwitchController");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "setSelectedIndex:", 0LL);
  v10 = v9(self, "zdtFenXiView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(self, "zdtFenXiGVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = v15(v14, "view");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18(v11, "addSubview:", v17);
  v20 = v19(self, "ztJinBiaoXianView");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v23 = v22(self, "ztJinBiaoXianGVC");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v26 = v25(v24, "view");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28(v21, "addSubview:", v27);
  v30 = v29(self, "dieTingTabBarView");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  v32(v31, "setTopBorder:", 1LL);
  v34 = v33(self, "dieTingTabBarView");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  v36(v35, "setLeftBorder:", 1LL);
  v38 = v37(self, "dieTingTabBarView");
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v40(v39, "setRightBorder:", 1LL);
  v42 = v41(self, "dieTingTabBarView");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  v44(v43, "setBorderWidth:", 2.0);
  v46 = v45(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v49 = v48(self, "dieTingTabBarView");
  v50 = objc_retainAutoreleasedReturnValue(v49);
  v51(v50, "setBorderColor:", v47);
  v53 = v52(self, "zhangTingTabBarView");
  v54 = objc_retainAutoreleasedReturnValue(v53);
  v55(v54, "setTopBorder:", 1LL);
  v57 = v56(self, "zhangTingTabBarView");
  v58 = objc_retainAutoreleasedReturnValue(v57);
  v59(v58, "setLeftBorder:", 1LL);
  v61 = v60(self, "zhangTingTabBarView");
  v62 = objc_retainAutoreleasedReturnValue(v61);
  v63(v62, "setRightBorder:", 1LL);
  v65 = v64(self, "zhangTingTabBarView");
  v66 = objc_retainAutoreleasedReturnValue(v65);
  v67(v66, "setBorderWidth:", 2.0);
  v69 = v68(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v70 = objc_retainAutoreleasedReturnValue(v69);
  v72 = v71(self, "zhangTingTabBarView");
  v73 = objc_retainAutoreleasedReturnValue(v72);
  v74(v73, "setBorderColor:", v70);
  v76 = v75(self, "zdtFenXiView");
  v77 = objc_retainAutoreleasedReturnValue(v76);
  v78(v77, "setAllBorder:", 1LL);
  v80 = v79(self, "zdtFenXiView");
  v81 = objc_retainAutoreleasedReturnValue(v80);
  v82(v81, "setBorderWidth:", 2.0);
  v84 = v83(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v85 = objc_retainAutoreleasedReturnValue(v84);
  v87 = v86(self, "zdtFenXiView");
  v88 = objc_retainAutoreleasedReturnValue(v87);
  v89(v88, "setBorderColor:", v85);
  v91 = v90(self, "ztJinBiaoXianView");
  v92 = objc_retainAutoreleasedReturnValue(v91);
  v93(v92, "setAllBorder:", 1LL);
  v95 = v94(self, "ztJinBiaoXianView");
  v96 = objc_retainAutoreleasedReturnValue(v95);
  v97(v96, "setBorderWidth:", 2.0);
  v99 = v98(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v100 = objc_retainAutoreleasedReturnValue(v99);
  v102 = v101(self, "ztJinBiaoXianView");
  v103 = objc_retainAutoreleasedReturnValue(v102);
  v104(v103, "setBorderColor:", v100);
  v106 = v105(self, "ztLeftBtn");
  v107 = objc_retainAutoreleasedReturnValue(v106);
  v108(v107, "setTitle:", CFSTR("今日涨停股票"));
  v110 = v109(self, "ztLeftBtn");
  v111 = objc_retainAutoreleasedReturnValue(v110);
  v112(v111, "setCanBeSelected:", 1LL);
  v114 = v113(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v115 = objc_retainAutoreleasedReturnValue(v114);
  v117 = v116(self, "ztLeftBtn");
  v118 = objc_retainAutoreleasedReturnValue(v117);
  v119(v118, "setTextColorDefault:", v115);
  v121 = v120(&OBJC_CLASS___HXThemeManager, "lightBlueLineColor");
  v122 = objc_retainAutoreleasedReturnValue(v121);
  v124 = v123(self, "ztLeftBtn");
  v125 = objc_retainAutoreleasedReturnValue(v124);
  v126(v125, "setTextColorSelected:", v122);
  v128 = v127(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v129 = objc_retainAutoreleasedReturnValue(v128);
  v131 = v130(self, "ztLeftBtn");
  v132 = objc_retainAutoreleasedReturnValue(v131);
  v133(v132, "setBackgroundColor:", v129);
  v135 = v134(self, "ztLeftBtn");
  v136 = objc_retainAutoreleasedReturnValue(v135);
  v137(v136, "setIsSelected:", 1LL);
  v139 = v138(self, "ztRightBtn");
  v140 = objc_retainAutoreleasedReturnValue(v139);
  v141(v140, "setTitle:", CFSTR("炸板池"));
  v143 = v142(self, "ztRightBtn");
  v144 = objc_retainAutoreleasedReturnValue(v143);
  v145(v144, "setCanBeSelected:", 1LL);
  v147 = v146(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v148 = objc_retainAutoreleasedReturnValue(v147);
  v150 = v149(self, "ztRightBtn");
  v151 = objc_retainAutoreleasedReturnValue(v150);
  v152(v151, "setTextColorDefault:", v148);
  v154 = v153(&OBJC_CLASS___HXThemeManager, "lightBlueLineColor");
  v155 = objc_retainAutoreleasedReturnValue(v154);
  v157 = v156(self, "ztRightBtn");
  v158 = objc_retainAutoreleasedReturnValue(v157);
  v159(v158, "setTextColorSelected:", v155);
  v161 = v160(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v162 = objc_retainAutoreleasedReturnValue(v161);
  v164 = v163(self, "ztRightBtn");
  v165 = objc_retainAutoreleasedReturnValue(v164);
  v166(v165, "setBackgroundColor:", v162);
  v168 = v167(self, "dtLeftBtn");
  v169 = objc_retainAutoreleasedReturnValue(v168);
  v170(v169, "setTitle:", CFSTR("今日跌停股票"));
  v172 = v171(self, "dtLeftBtn");
  v173 = objc_retainAutoreleasedReturnValue(v172);
  v174(v173, "setCanBeSelected:", 1LL);
  v176 = v175(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v177 = objc_retainAutoreleasedReturnValue(v176);
  v179 = v178(self, "dtLeftBtn");
  v180 = objc_retainAutoreleasedReturnValue(v179);
  v181(v180, "setTextColorDefault:", v177);
  v183 = v182(&OBJC_CLASS___HXThemeManager, "lightBlueLineColor");
  v184 = objc_retainAutoreleasedReturnValue(v183);
  v186 = v185(self, "dtLeftBtn");
  v187 = objc_retainAutoreleasedReturnValue(v186);
  v188(v187, "setTextColorSelected:", v184);
  v190 = v189(self, "dtLeftBtn");
  v191 = objc_retainAutoreleasedReturnValue(v190);
  v192(v191, "setIsSelected:", 1LL);
  v194 = v193(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v195 = objc_retainAutoreleasedReturnValue(v194);
  v197 = v196(self, "dtLeftBtn");
  v198 = objc_retainAutoreleasedReturnValue(v197);
  v199(v198, "setBackgroundColor:", v195);
  v201 = v200(self, "dtRightBtn");
  v202 = objc_retainAutoreleasedReturnValue(v201);
  v203(v202, "setTitle:", CFSTR("翘板池"));
  v205 = v204(self, "dtRightBtn");
  v206 = objc_retainAutoreleasedReturnValue(v205);
  v207(v206, "setCanBeSelected:", 1LL);
  v209 = v208(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v210 = objc_retainAutoreleasedReturnValue(v209);
  v212 = v211(self, "dtRightBtn");
  v213 = objc_retainAutoreleasedReturnValue(v212);
  v214(v213, "setTextColorDefault:", v210);
  v216 = v215(&OBJC_CLASS___HXThemeManager, "lightBlueLineColor");
  v217 = objc_retainAutoreleasedReturnValue(v216);
  v219 = v218(self, "dtRightBtn");
  v220 = objc_retainAutoreleasedReturnValue(v219);
  v221(v220, "setTextColorSelected:", v217);
  v223 = v222(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v224 = objc_retainAutoreleasedReturnValue(v223);
  v226 = v225(self, "dtRightBtn");
  v227 = objc_retainAutoreleasedReturnValue(v226);
  v228(v227, "setBackgroundColor:", v224);
}

//----- (00000001001EE602) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController addNotifications](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v3, "addObserver:selector:name:object:", self, "frameDidChange:", v6, v5);
}

//----- (00000001001EE698) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController dealloc](ZhangDieTingStocksPoolViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZhangDieTingStocksPoolViewController;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (00000001001EE70D) ----------------------------------------------------
HXTabbarController *__cdecl -[ZhangDieTingStocksPoolViewController ztTableSwitchController](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *zhangTingTabBarView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi
  NSArray *v16; // rax
  NSArray *v17; // r15
  _QWORD v20[2]; // [rsp+0h] [rbp-40h] BYREF

  zhangTingTabBarView = self->_zhangTingTabBarView;
  if ( !zhangTingTabBarView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_zhangTingTabBarView;
    self->_zhangTingTabBarView = v5;
    v8 = (void *)v7(self, "zhangTingContainerView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_zhangTingTabBarView, "setView:", v9);
    v12 = (void *)v11(self, "zhangTingTVC");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v20[0] = v13;
    v15 = (void *)v14(self, "cengZhangTingTVC");
    v20[1] = objc_retainAutoreleasedReturnValue(v15);
    v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v20, 2LL);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(self->_zhangTingTabBarView, "setViewControllers:", v17);
    zhangTingTabBarView = self->_zhangTingTabBarView;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(zhangTingTabBarView);
}

//----- (00000001001EE865) ----------------------------------------------------
HXTabbarController *__cdecl -[ZhangDieTingStocksPoolViewController dtTableSwitchController](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *dieTingTabBarView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi
  NSArray *v16; // rax
  NSArray *v17; // r15
  _QWORD v20[2]; // [rsp+0h] [rbp-40h] BYREF

  dieTingTabBarView = self->_dieTingTabBarView;
  if ( !dieTingTabBarView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_dieTingTabBarView;
    self->_dieTingTabBarView = v5;
    v8 = (void *)v7(self, "dieTingContainerView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->_dieTingTabBarView, "setView:", v9);
    v12 = (void *)v11(self, "dieTingTVC");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v20[0] = v13;
    v15 = (void *)v14(self, "cengDieTingTVC");
    v20[1] = objc_retainAutoreleasedReturnValue(v15);
    v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v20, 2LL);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(self->_dieTingTabBarView, "setViewControllers:", v17);
    dieTingTabBarView = self->_dieTingTabBarView;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(dieTingTabBarView);
}

//----- (00000001001EE9BD) ----------------------------------------------------
ZhangTingStocksPoolTableViewController *__cdecl -[ZhangDieTingStocksPoolViewController zhangTingTVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  objc_class *v5; // rax
  HXBaseView *v14; // rax

  v3 = *(void **)&self->super._isContentViewController;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangTingStocksPoolTableViewController);
    v5 = (objc_class *)_objc_msgSend(
                         v4,
                         "initWithNibName:bundle:",
                         CFSTR("ZhangTingStocksPoolTableViewController"),
                         0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZhangDieTingStocksPoolViewController updateStocksNumberBlock](self, "updateStocksNumberBlock");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v13), "setUpdateStocksNumberBlock:", v12);
    v14 = -[ZhangDieTingStocksPoolViewController zhangTingContainerView](self, "zhangTingContainerView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = (char *)v15;
    if ( v15 )
      objc_msgSend_stret(v22, v15, "bounds");
    else
      memset(v22, 0, 32);
    v18 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v16), "view");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v19, "setFrame:");
    v3 = *(Class *)((char *)&self->super.super.super.isa + v20);
  }
  return (ZhangTingStocksPoolTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001001EEB1A) ----------------------------------------------------
DieTingStocksPoolTableViewController *__cdecl -[ZhangDieTingStocksPoolViewController dieTingTVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  objc_class *v5; // rax
  HXBaseView *v14; // rax

  v3 = *(void **)&self->super._reserved;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___DieTingStocksPoolTableViewController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("DieTingStocksPoolTableViewController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZhangDieTingStocksPoolViewController updateStocksNumberBlock](self, "updateStocksNumberBlock");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v13), "setUpdateStocksNumberBlock:", v12);
    v14 = -[ZhangDieTingStocksPoolViewController dieTingContainerView](self, "dieTingContainerView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = (char *)v15;
    if ( v15 )
      objc_msgSend_stret(v22, v15, "bounds");
    else
      memset(v22, 0, 32);
    v18 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v16), "view");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v19, "setFrame:");
    v3 = *(Class *)((char *)&self->super.super.super.isa + v20);
  }
  return (DieTingStocksPoolTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001001EEC77) ----------------------------------------------------
CengZhangTingStocksPoolTableViewController *__cdecl -[ZhangDieTingStocksPoolViewController cengZhangTingTVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *zdtFenXiView; // rdi
  objc_class *v5; // rax
  HXBaseView *v14; // rax

  zdtFenXiView = self->_zdtFenXiView;
  if ( !zdtFenXiView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___CengZhangTingStocksPoolTableViewController);
    v5 = (objc_class *)_objc_msgSend(
                         v4,
                         "initWithNibName:bundle:",
                         CFSTR("CengZhangTingStocksPoolTableViewController"),
                         0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZhangDieTingStocksPoolViewController updateStocksNumberBlock](self, "updateStocksNumberBlock");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v13), "setUpdateStocksNumberBlock:", v12);
    v14 = -[ZhangDieTingStocksPoolViewController zhangTingContainerView](self, "zhangTingContainerView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = (char *)v15;
    if ( v15 )
      objc_msgSend_stret(v22, v15, "bounds");
    else
      memset(v22, 0, 32);
    v18 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v16), "view");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v19, "setFrame:");
    zdtFenXiView = *(HXBaseView **)((char *)&self->super.super.super.isa + v20);
  }
  return (CengZhangTingStocksPoolTableViewController *)objc_retainAutoreleaseReturnValue(zdtFenXiView);
}

//----- (00000001001EEDD4) ----------------------------------------------------
CengDieTingStocksPoolTableViewController *__cdecl -[ZhangDieTingStocksPoolViewController cengDieTingTVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *ztJinBiaoXianView; // rdi
  objc_class *v5; // rax
  HXBaseView *v14; // rax

  ztJinBiaoXianView = self->_ztJinBiaoXianView;
  if ( !ztJinBiaoXianView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___CengDieTingStocksPoolTableViewController);
    v5 = (objc_class *)_objc_msgSend(
                         v4,
                         "initWithNibName:bundle:",
                         CFSTR("CengDieTingStocksPoolTableViewController"),
                         0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZhangDieTingStocksPoolViewController updateStocksNumberBlock](self, "updateStocksNumberBlock");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v13), "setUpdateStocksNumberBlock:", v12);
    v14 = -[ZhangDieTingStocksPoolViewController dieTingContainerView](self, "dieTingContainerView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = (char *)v15;
    if ( v15 )
      objc_msgSend_stret(v22, v15, "bounds");
    else
      memset(v22, 0, 32);
    v18 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v16), "view");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v19, "setFrame:");
    ztJinBiaoXianView = *(HXBaseView **)((char *)&self->super.super.super.isa + v20);
  }
  return (CengDieTingStocksPoolTableViewController *)objc_retainAutoreleaseReturnValue(ztJinBiaoXianView);
}

//----- (00000001001EEF31) ----------------------------------------------------
id __cdecl -[ZhangDieTingStocksPoolViewController updateStocksNumberBlock](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  objc_class *v4; // rax
  _QWORD v10[4]; // [rsp+0h] [rbp-50h] BYREF
  id to; // [rsp+20h] [rbp-30h] BYREF
  id location[5]; // [rsp+28h] [rbp-28h] BYREF

  v3 = *(void **)&self->super._viewIsAppearing;
  if ( !v3 )
  {
    objc_initWeak(location, self);
    v10[0] = _NSConcreteStackBlock;
    v10[1] = 3254779904LL;
    v10[2] = sub_1001EEFDF;
    v10[3] = &unk_1012DD620;
    objc_copyWeak(&to, location);
    v4 = objc_retainBlock(v10);
    v6 = *(Class *)((char *)&self->super.super.super.isa + v5);
    *(Class *)((char *)&self->super.super.super.isa + v5) = v4;
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    v3 = *(Class *)((char *)&self->super.super.super.isa + v7);
  }
  v8 = objc_retainBlock(v3);
  return objc_autoreleaseReturnValue(v8);
}

//----- (00000001001EEFDF) ----------------------------------------------------
void __fastcall sub_1001EEFDF(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "updateTabBarStocksNumber:", v2);
}

//----- (00000001001EF039) ----------------------------------------------------
ZhangDieTingFenXiGraphViewController *__cdecl -[ZhangDieTingStocksPoolViewController zdtFenXiGVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *zhangTingContainerView; // rdi
  objc_class *v5; // rax
  HXBaseView *v8; // rax

  zhangTingContainerView = self->_zhangTingContainerView;
  if ( !zhangTingContainerView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangDieTingFenXiGraphViewController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZhangDieTingFenXiGraphViewController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController zdtFenXiView](self, "zdtFenXiView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = (char *)v9;
    if ( v9 )
      objc_msgSend_stret(v16, v9, "bounds");
    else
      memset(v16, 0, 32);
    v12 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "view");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "setFrame:");
    zhangTingContainerView = *(HXBaseView **)((char *)&self->super.super.super.isa + v14);
  }
  return (ZhangDieTingFenXiGraphViewController *)objc_retainAutoreleaseReturnValue(zhangTingContainerView);
}

//----- (00000001001EF136) ----------------------------------------------------
ZuoRiZhangTingJinBiaoXianGraphViewController *__cdecl -[ZhangDieTingStocksPoolViewController ztJinBiaoXianGVC](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  HXBaseView *dieTingContainerView; // rdi
  objc_class *v5; // rax
  HXBaseView *v8; // rax

  dieTingContainerView = self->_dieTingContainerView;
  if ( !dieTingContainerView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZuoRiZhangTingJinBiaoXianGraphViewController);
    v5 = (objc_class *)_objc_msgSend(
                         v4,
                         "initWithNibName:bundle:",
                         CFSTR("ZuoRiZhangTingJinBiaoXianGraphViewController"),
                         0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZhangDieTingStocksPoolViewController ztJinBiaoXianView](self, "ztJinBiaoXianView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = (char *)v9;
    if ( v9 )
      objc_msgSend_stret(v16, v9, "bounds");
    else
      memset(v16, 0, 32);
    v12 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "view");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "setFrame:");
    dieTingContainerView = *(HXBaseView **)((char *)&self->super.super.super.isa + v14);
  }
  return (ZuoRiZhangTingJinBiaoXianGraphViewController *)objc_retainAutoreleaseReturnValue(dieTingContainerView);
}

//----- (00000001001EF233) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController frameDidChange:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(self, "view");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v4);
  if ( v4 == v6 )
    -[ZhangDieTingStocksPoolViewController layoutSubviews](self, "layoutSubviews");
}

//----- (00000001001EF2AF) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController layoutSubviews](ZhangDieTingStocksPoolViewController *self, SEL a2)
{
  HXBaseView *v6; // rax
  HXBaseView *v7; // rbx
  HXBaseView *v8; // rax
  HXBaseView *v9; // r15
  HXBaseView *v10; // rax
  HXBaseView *v11; // rbx
  SEL v12; // r12
  SEL v15; // r12
  HXBaseView *v18; // rax
  HXBaseView *v19; // rbx
  HXBaseView *v21; // rax
  HXBaseView *v22; // r15
  HXBaseView *v23; // rax
  HXBaseView *v24; // rbx
  HXBaseView *v29; // rax
  HXBaseView *v30; // rbx
  SEL v31; // r12
  HXBaseView *v32; // rax
  HXBaseView *v33; // rbx
  SEL v34; // r12
  HXBaseView *v35; // rax
  HXBaseView *v36; // rbx
  SEL v39; // r12
  HXBaseView *v42; // rax
  HXBaseView *v43; // r15
  SEL v44; // r12
  HXBaseView *v45; // rax
  HXBaseView *v46; // rbx
  long double __x; // [rsp+0h] [rbp-130h]
  long double v48; // [rsp+48h] [rbp-E8h]
  long double v49; // [rsp+88h] [rbp-A8h]

  v2 = _objc_msgSend(self, "view");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "height");
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "width");
  floor(__x);
  v6 = -[ZhangDieTingStocksPoolViewController dieTingContainerView](self, "dieTingContainerView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  _objc_msgSend(v7, "setFrame:");
  v8 = -[ZhangDieTingStocksPoolViewController dieTingContainerView](self, "dieTingContainerView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "tail");
  v10 = -[ZhangDieTingStocksPoolViewController dieTingTabBarView](self, "dieTingTabBarView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  *(_QWORD *)&v48 = 0x4000000000000000LL;
  *((_QWORD *)&v48 + 1) = 0x4000000000000000LL;
  _objc_msgSend(v11, "setFrame:");
  v13 = _objc_msgSend(self, v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "tail");
  floor(v48);
  v16 = _objc_msgSend(self, v15);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  _objc_msgSend(v17, "tail");
  v18 = -[ZhangDieTingStocksPoolViewController zhangTingContainerView](self, "zhangTingContainerView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "setFrame:");
  v21 = -[ZhangDieTingStocksPoolViewController zhangTingContainerView](self, "zhangTingContainerView");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v22, "tail");
  v23 = -[ZhangDieTingStocksPoolViewController zhangTingTabBarView](self, "zhangTingTabBarView");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  *(_QWORD *)&v49 = 0x4000000000000000LL;
  *((_QWORD *)&v49 + 1) = 0x4000000000000000LL;
  _objc_msgSend(v24, "setFrame:");
  v25 = _objc_msgSend(self, "view");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  _objc_msgSend(v26, "width");
  floor(v49);
  v27 = _objc_msgSend(self, "view");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  _objc_msgSend(v28, "height");
  v29 = -[ZhangDieTingStocksPoolViewController zhangTingTabBarView](self, "zhangTingTabBarView");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  _objc_msgSend(v30, v31);
  v32 = -[ZhangDieTingStocksPoolViewController zhangTingTabBarView](self, "zhangTingTabBarView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  _objc_msgSend(v33, v34);
  v35 = -[ZhangDieTingStocksPoolViewController zdtFenXiView](self, "zdtFenXiView");
  v36 = objc_retainAutoreleasedReturnValue(v35);
  _objc_msgSend(v36, "setFrame:");
  v37 = _objc_msgSend(self, "view");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  _objc_msgSend(v38, "width");
  v40 = _objc_msgSend(self, v39);
  v41 = objc_retainAutoreleasedReturnValue(v40);
  _objc_msgSend(v41, "right");
  v42 = -[ZhangDieTingStocksPoolViewController zdtFenXiView](self, "zdtFenXiView");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  _objc_msgSend(v43, v44);
  v45 = -[ZhangDieTingStocksPoolViewController ztJinBiaoXianView](self, "ztJinBiaoXianView");
  v46 = objc_retainAutoreleasedReturnValue(v45);
  _objc_msgSend(v46, "setFrame:");
}

//----- (00000001001EF8EF) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController ztLeftBtnClicked:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  HXButton *v3; // rax
  HXButton *v4; // rbx
  HXButton *v5; // rax
  HXButton *v6; // rbx
  HXTabbarController *v8; // rax
  HXTabbarController *v9; // rbx

  v3 = -[ZhangDieTingStocksPoolViewController ztLeftBtn](self, "ztLeftBtn", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXButton setIsSelected:](v4, "setIsSelected:", 1LL);
  v5 = -[ZhangDieTingStocksPoolViewController ztRightBtn](self, "ztRightBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXButton setIsSelected:](v6, "setIsSelected:", 0LL);
  v7(v6);
  v8 = -[ZhangDieTingStocksPoolViewController ztTableSwitchController](self, "ztTableSwitchController");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXTabbarController setSelectedIndex:](v9, "setSelectedIndex:", 0LL);
  v10(v9);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("今日涨停股票_今日涨跌停股票"),
    0LL,
    1LL);
}

//----- (00000001001EF9CA) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController ztRightBtnClicked:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  HXButton *v3; // rax
  HXButton *v4; // rbx
  HXButton *v5; // rax
  HXButton *v6; // rbx
  HXTabbarController *v8; // rax
  HXTabbarController *v9; // rbx

  v3 = -[ZhangDieTingStocksPoolViewController ztRightBtn](self, "ztRightBtn", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXButton setIsSelected:](v4, "setIsSelected:", 1LL);
  v5 = -[ZhangDieTingStocksPoolViewController ztLeftBtn](self, "ztLeftBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXButton setIsSelected:](v6, "setIsSelected:", 0LL);
  v7(v6);
  v8 = -[ZhangDieTingStocksPoolViewController ztTableSwitchController](self, "ztTableSwitchController");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXTabbarController setSelectedIndex:](v9, "setSelectedIndex:", 1LL);
  v10(v9);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("炸板池_今日涨跌停股票"),
    0LL,
    1LL);
}

//----- (00000001001EFAA8) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController dtLeftBtnClicked:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  HXButton *v3; // rax
  HXButton *v4; // rbx
  HXButton *v5; // rax
  HXButton *v6; // rbx
  HXTabbarController *v8; // rax
  HXTabbarController *v9; // rbx

  v3 = -[ZhangDieTingStocksPoolViewController dtLeftBtn](self, "dtLeftBtn", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXButton setIsSelected:](v4, "setIsSelected:", 1LL);
  v5 = -[ZhangDieTingStocksPoolViewController dtRightBtn](self, "dtRightBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXButton setIsSelected:](v6, "setIsSelected:", 0LL);
  v7(v6);
  v8 = -[ZhangDieTingStocksPoolViewController dtTableSwitchController](self, "dtTableSwitchController");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXTabbarController setSelectedIndex:](v9, "setSelectedIndex:", 0LL);
  v10(v9);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("今日跌停股票_今日涨跌停股票"),
    0LL,
    1LL);
}

//----- (00000001001EFB83) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController dtRightBtnClicked:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  HXButton *v3; // rax
  HXButton *v4; // rbx
  HXButton *v5; // rax
  HXButton *v6; // rbx
  HXTabbarController *v8; // rax
  HXTabbarController *v9; // rbx

  v3 = -[ZhangDieTingStocksPoolViewController dtRightBtn](self, "dtRightBtn", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXButton setIsSelected:](v4, "setIsSelected:", 1LL);
  v5 = -[ZhangDieTingStocksPoolViewController dtLeftBtn](self, "dtLeftBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXButton setIsSelected:](v6, "setIsSelected:", 0LL);
  v7(v6);
  v8 = -[ZhangDieTingStocksPoolViewController dtTableSwitchController](self, "dtTableSwitchController");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXTabbarController setSelectedIndex:](v9, "setSelectedIndex:", 1LL);
  v10(v9);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("翘板池_今日涨跌停股票"),
    0LL,
    1LL);
}

//----- (00000001001EFC61) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController keyDownFromSuper:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  ZhangTingStocksPoolTableViewController *v3; // rax
  ZhangTingStocksPoolTableViewController *v4; // rbx
  DieTingStocksPoolTableViewController *v7; // rax
  DieTingStocksPoolTableViewController *v8; // rbx
  CengZhangTingStocksPoolTableViewController *v10; // rax
  CengZhangTingStocksPoolTableViewController *v11; // rbx
  CengDieTingStocksPoolTableViewController *v16; // rax
  CengDieTingStocksPoolTableViewController *v17; // rbx

  v20 = objc_retain(a3);
  v3 = -[ZhangDieTingStocksPoolViewController zhangTingTVC](self, "zhangTingTVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)-[ZhangTingStocksPoolTableViewController isFocus](v4, "isFocus");
  if ( v5 )
    goto LABEL_4;
  v7 = -[ZhangDieTingStocksPoolViewController dieTingTVC](self, "dieTingTVC");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (unsigned __int8)-[DieTingStocksPoolTableViewController isFocus](v8, "isFocus");
  if ( v9
    || (v10 = -[ZhangDieTingStocksPoolViewController cengZhangTingTVC](self, "cengZhangTingTVC"),
        v11 = objc_retainAutoreleasedReturnValue(v10),
        v12 = (unsigned __int8)-[CengZhangTingStocksPoolTableViewController isFocus](v11, "isFocus"),
        v12) )
  {
LABEL_4:
    v13 = _objc_msgSend(self, v6);
    v14 = v20;
  }
  else
  {
    v16 = -[ZhangDieTingStocksPoolViewController cengDieTingTVC](self, "cengDieTingTVC");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18 = (unsigned __int8)-[CengDieTingStocksPoolTableViewController isFocus](v17, "isFocus");
    v14 = v20;
    if ( !v18 )
      goto LABEL_6;
    v13 = _objc_msgSend(self, v19);
  }
  v15 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v15, "keyDownFromSuper:", v14);
LABEL_6:
}

//----- (00000001001EFDD0) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController refreshAllModules](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  ZhangDieTingFenXiGraphViewController *v2; // rax
  ZhangDieTingFenXiGraphViewController *v3; // rbx
  id (*v5)(id, SEL, ...); // r12

  v2 = -[ZhangDieTingStocksPoolViewController zdtFenXiGVC](self, "zdtFenXiGVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "refreshAllModules");
  v6 = v5(self, "ztJinBiaoXianGVC");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "refreshAllModules");
}

//----- (00000001001EFE51) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController updateTabBarStocksNumber:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  unsigned __int64 v8; // r15
  SEL v14; // r12
  HXButton *v15; // rdi
  NSString *v17; // rax
  NSString *v18; // r15
  NSArray *v23; // [rsp+18h] [rbp-78h]
  _QWORD v26[4]; // [rsp+40h] [rbp-50h] BYREF

  v22 = objc_retain(a3);
  if ( v22 )
  {
    v26[0] = CFSTR("updateZhangTingNumber");
    v26[1] = CFSTR("updateCengZhangTingNumber");
    v26[2] = CFSTR("updateDieTingNumber");
    v26[3] = CFSTR("updateCengDieTingNumber");
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v26, 4LL);
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = _objc_msgSend(&OBJC_CLASS___NSString, "string");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    if ( !_objc_msgSend(v5, "count") )
    {
LABEL_10:
      goto LABEL_11;
    }
    v25 = v7;
    v8 = 0LL;
    v23 = v5;
    while ( 1 )
    {
      v9 = _objc_msgSend(v5, "thsStringAtIndex:", v8);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v22, "thsNumberForKey:", v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      if ( (unsigned __int8)_objc_msgSend(v12, v14, v13) )
      {
        _objc_msgSend(v12, "doubleValue");
        if ( v3 != 4294967295.0 )
        {
          _objc_msgSend(v12, "doubleValue");
          if ( v3 != 2147483648.0 && (__int64)_objc_msgSend(v12, "integerValue") >= 0 )
            break;
        }
      }
      ++v8;
      v5 = v23;
      if ( (unsigned __int64)_objc_msgSend(v23, "count") <= v8 )
      {
        v7 = v25;
        goto LABEL_10;
      }
    }
    if ( (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("updateZhangTingNumber")) )
    {
      v15 = -[ZhangDieTingStocksPoolViewController ztLeftBtn](self, "ztLeftBtn");
    }
    else if ( (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("updateCengZhangTingNumber")) )
    {
      v15 = -[ZhangDieTingStocksPoolViewController ztRightBtn](self, "ztRightBtn");
    }
    else if ( (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("updateDieTingNumber")) )
    {
      v15 = -[ZhangDieTingStocksPoolViewController dtLeftBtn](self, "dtLeftBtn");
    }
    else
    {
      if ( !(unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("updateCengDieTingNumber")) )
      {
        v24 = 0LL;
        v17 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@（共%@只）"), v25, v12);
        goto LABEL_21;
      }
      v15 = -[ZhangDieTingStocksPoolViewController dtRightBtn](self, "dtRightBtn");
    }
    v24 = objc_retainAutoreleasedReturnValue(v15);
    v17 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@（共%@只）"), v16, v12);
LABEL_21:
    v18 = objc_retainAutoreleasedReturnValue(v17);
    _objc_msgSend(v24, "setTitle:", v18);
    v20 = v10;
    v7 = v18;
    v5 = v23;
    goto LABEL_10;
  }
LABEL_11:
}

//----- (00000001001F01EA) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController zdtFenXiView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._nextResponder);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F0203) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZdtFenXiView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super.super._nextResponder, a3);
}

//----- (00000001001F0217) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController ztJinBiaoXianView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F0230) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZtJinBiaoXianView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._nibName, a3);
}

//----- (00000001001F0244) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController zhangTingContainerView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibBundle);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F025D) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZhangTingContainerView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._nibBundle, a3);
}

//----- (00000001001F0271) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController dieTingContainerView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._representedObject);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F028A) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDieTingContainerView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super._representedObject, a3);
}

//----- (00000001001F029E) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController zhangTingTabBarView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F02B7) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZhangTingTabBarView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._title, a3);
}

//----- (00000001001F02CB) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangDieTingStocksPoolViewController dieTingTabBarView](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.view);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F02E4) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDieTingTabBarView:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.view, a3);
}

//----- (00000001001F02F8) ----------------------------------------------------
HXButton *__cdecl -[ZhangDieTingStocksPoolViewController ztLeftBtn](ZhangDieTingStocksPoolViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._topLevelObjects);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F0311) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZtLeftBtn:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._topLevelObjects, a3);
}

//----- (00000001001F0325) ----------------------------------------------------
HXButton *__cdecl -[ZhangDieTingStocksPoolViewController ztRightBtn](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._editors);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F033E) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZtRightBtn:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._editors, a3);
}

//----- (00000001001F0352) ----------------------------------------------------
HXButton *__cdecl -[ZhangDieTingStocksPoolViewController dtLeftBtn](ZhangDieTingStocksPoolViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._autounbinder);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F036B) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDtLeftBtn:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super._autounbinder, a3);
}

//----- (00000001001F037F) ----------------------------------------------------
HXButton *__cdecl -[ZhangDieTingStocksPoolViewController dtRightBtn](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._designNibBundleIdentifier);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001F0398) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDtRightBtn:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._designNibBundleIdentifier, a3);
}

//----- (00000001001F03AC) ----------------------------------------------------
id __cdecl -[ZhangDieTingStocksPoolViewController refreshRightViewBlock](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2)
{
  return objc_getProperty(self, a2, 88LL, 0);
}

//----- (00000001001F03BF) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setRefreshRightViewBlock:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 88LL);
}

//----- (00000001001F03D0) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setUpdateStocksNumberBlock:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 96LL);
}

//----- (00000001001F03E1) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZhangTingTVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._isContentViewController, a3);
}

//----- (00000001001F03F5) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDieTingTVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._reserved, a3);
}

//----- (00000001001F0409) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setCengZhangTingTVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_zdtFenXiView, a3);
}

//----- (00000001001F041D) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setCengDieTingTVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_ztJinBiaoXianView, a3);
}

//----- (00000001001F0431) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZdtFenXiGVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_zhangTingContainerView, a3);
}

//----- (00000001001F0445) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZtJinBiaoXianGVC:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_dieTingContainerView, a3);
}

//----- (00000001001F0459) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setZtTableSwitchController:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_zhangTingTabBarView, a3);
}

//----- (00000001001F046D) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController setDtTableSwitchController:](
        ZhangDieTingStocksPoolViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->_dieTingTabBarView, a3);
}

//----- (00000001001F0481) ----------------------------------------------------
void __cdecl -[ZhangDieTingStocksPoolViewController .cxx_destruct](ZhangDieTingStocksPoolViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_dieTingTabBarView, 0LL);
  objc_storeStrong((id *)&self->_zhangTingTabBarView, 0LL);
  objc_storeStrong((id *)&self->_dieTingContainerView, 0LL);
  objc_storeStrong((id *)&self->_zhangTingContainerView, 0LL);
  objc_storeStrong((id *)&self->_ztJinBiaoXianView, 0LL);
  objc_storeStrong((id *)&self->_zdtFenXiView, 0LL);
  objc_storeStrong((id *)&self->super._reserved, 0LL);
  objc_storeStrong((id *)&self->super._isContentViewController, 0LL);
  objc_storeStrong((id *)&self->super._viewIsAppearing, 0LL);
  objc_storeStrong(&self->super.__privateData, 0LL);
  objc_destroyWeak((id *)&self->super._designNibBundleIdentifier);
  objc_destroyWeak(&self->super._autounbinder);
  objc_destroyWeak((id *)&self->super._editors);
  objc_destroyWeak((id *)&self->super._topLevelObjects);
  objc_destroyWeak((id *)&self->super.view);
  objc_destroyWeak((id *)&self->super._title);
  objc_destroyWeak(&self->super._representedObject);
  objc_destroyWeak((id *)&self->super._nibBundle);
  objc_destroyWeak((id *)&self->super._nibName);
  objc_destroyWeak(&self->super.super._nextResponder);
}

