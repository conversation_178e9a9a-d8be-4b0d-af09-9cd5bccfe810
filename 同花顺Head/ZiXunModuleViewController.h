//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

#import "JYInQuotaQuickTradeViewControllerDelegate-Protocol.h"
#import "ReceiveQuotaData-Protocol.h"

@class DaDanLengJingContainerController, GuaDanCheDanContainerController, Guan<PERSON>ianBaoJiaoViewController, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uo<PERSON>iewController, GuanLianQiZhiViewController, GuanLianShangPinViewController, HXBaseView, HXTabbarController, JYInQuotaQuickTradeViewController, NSArray, NSString, NSTimer, QiHuoGuanLianAGuViewController, QuanShangXiWeiViewController, QueryQuotaDataModule, RelatedZiXunTableViewController, StockNotesViewController, Trading<PERSON>ueueContainerController, XiangGuanWoLunNiuXiongContainerController, ZiXunMenuViewController, ZiXunTableViewController;
@protocol ZiXunModuleViewDidChangeDelegate;

@interface ZiXunModuleViewController : NSViewController <JYInQuotaQuickTradeViewControllerDelegate, ReceiveQuotaData>
{
    BOOL _hasSetDefaultTabbarCtrlIndex;
    BOOL _ziXunOpen;
    BOOL _needJumpToQuickTrade;
    BOOL _viewDidAppearFlag;
    HXBaseView *_ziXunMenuView;
    HXBaseView *_ziXunContentView;
    NSString *_stockCode;
    NSString *_market;
    NSArray *_menuTitles;
    unsigned long long _zixunType;
    CDUnknownBlockType _ziXunOpenURLBlock;
    id <ZiXunModuleViewDidChangeDelegate> _delegate;
    HXTabbarController *_tabbarController;
    ZiXunMenuViewController *_ziXunMenuVC;
    ZiXunTableViewController *_ziXunTableVC;
    GuanLianBaoJiaoViewController *_guanLianBaoJiaVC;
    JYInQuotaQuickTradeViewController *_quickTradeVC;
    TradingQueueContainerController *_tradingQueueVC;
    QuanShangXiWeiViewController *_quanShangXiWeiVC;
    GuaDanCheDanContainerController *_guaDanCheDanVC;
    DaDanLengJingContainerController *_daDanLengJingVC;
    GuanLianShangPinViewController *_guanLianShangPinVC;
    RelatedZiXunTableViewController *_relatedZiXunTableVC;
    XiangGuanWoLunNiuXiongContainerController *_woLunNiuXiongCC;
    GuanLianQiZhiViewController *_guanLianQiZhiVC;
    QiHuoGuanLianAGuViewController *_qhGuanLianAGuTVC;
    GuanLianQiHuoViewController *_guanLianQiHuoVC;
    StockNotesViewController *_stockNotesVC;
    QueryQuotaDataModule *_queryModule;
    NSArray *_reqParamArr;
    NSTimer *_frameChangedTimer;
}


@property(nonatomic) BOOL viewDidAppearFlag; // @synthesize viewDidAppearFlag=_viewDidAppearFlag;
@property(nonatomic) BOOL needJumpToQuickTrade; // @synthesize needJumpToQuickTrade=_needJumpToQuickTrade;
@property(nonatomic) BOOL ziXunOpen; // @synthesize ziXunOpen=_ziXunOpen;
@property(retain, nonatomic) NSTimer *frameChangedTimer; // @synthesize frameChangedTimer=_frameChangedTimer;
@property(nonatomic) BOOL hasSetDefaultTabbarCtrlIndex; // @synthesize hasSetDefaultTabbarCtrlIndex=_hasSetDefaultTabbarCtrlIndex;
@property(retain, nonatomic) NSArray *reqParamArr; // @synthesize reqParamArr=_reqParamArr;
@property(retain, nonatomic) QueryQuotaDataModule *queryModule; // @synthesize queryModule=_queryModule;
@property(retain, nonatomic) StockNotesViewController *stockNotesVC; // @synthesize stockNotesVC=_stockNotesVC;
@property(retain, nonatomic) GuanLianQiHuoViewController *guanLianQiHuoVC; // @synthesize guanLianQiHuoVC=_guanLianQiHuoVC;
@property(retain, nonatomic) QiHuoGuanLianAGuViewController *qhGuanLianAGuTVC; // @synthesize qhGuanLianAGuTVC=_qhGuanLianAGuTVC;
@property(retain, nonatomic) GuanLianQiZhiViewController *guanLianQiZhiVC; // @synthesize guanLianQiZhiVC=_guanLianQiZhiVC;
@property(retain, nonatomic) XiangGuanWoLunNiuXiongContainerController *woLunNiuXiongCC; // @synthesize woLunNiuXiongCC=_woLunNiuXiongCC;
@property(retain, nonatomic) RelatedZiXunTableViewController *relatedZiXunTableVC; // @synthesize relatedZiXunTableVC=_relatedZiXunTableVC;
@property(retain, nonatomic) GuanLianShangPinViewController *guanLianShangPinVC; // @synthesize guanLianShangPinVC=_guanLianShangPinVC;
@property(retain, nonatomic) DaDanLengJingContainerController *daDanLengJingVC; // @synthesize daDanLengJingVC=_daDanLengJingVC;
@property(retain, nonatomic) GuaDanCheDanContainerController *guaDanCheDanVC; // @synthesize guaDanCheDanVC=_guaDanCheDanVC;
@property(retain, nonatomic) QuanShangXiWeiViewController *quanShangXiWeiVC; // @synthesize quanShangXiWeiVC=_quanShangXiWeiVC;
@property(retain, nonatomic) TradingQueueContainerController *tradingQueueVC; // @synthesize tradingQueueVC=_tradingQueueVC;
@property(retain, nonatomic) JYInQuotaQuickTradeViewController *quickTradeVC; // @synthesize quickTradeVC=_quickTradeVC;
@property(retain, nonatomic) GuanLianBaoJiaoViewController *guanLianBaoJiaVC; // @synthesize guanLianBaoJiaVC=_guanLianBaoJiaVC;
@property(retain, nonatomic) ZiXunTableViewController *ziXunTableVC; // @synthesize ziXunTableVC=_ziXunTableVC;
@property(retain, nonatomic) ZiXunMenuViewController *ziXunMenuVC; // @synthesize ziXunMenuVC=_ziXunMenuVC;
@property(retain, nonatomic) HXTabbarController *tabbarController; // @synthesize tabbarController=_tabbarController;
@property(nonatomic) __weak id <ZiXunModuleViewDidChangeDelegate> delegate; // @synthesize delegate=_delegate;
@property(copy, nonatomic) CDUnknownBlockType ziXunOpenURLBlock; // @synthesize ziXunOpenURLBlock=_ziXunOpenURLBlock;
@property(nonatomic) unsigned long long zixunType; // @synthesize zixunType=_zixunType;
@property(retain, nonatomic) NSArray *menuTitles; // @synthesize menuTitles=_menuTitles;
@property(copy, nonatomic) NSString *market; // @synthesize market=_market;
@property(copy, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
@property __weak HXBaseView *ziXunContentView; // @synthesize ziXunContentView=_ziXunContentView;
@property __weak HXBaseView *ziXunMenuView; // @synthesize ziXunMenuView=_ziXunMenuView;
- (void)jumpGeGu:(id)arg1;
- (void)receiveQuotaData:(id)arg1;
- (void)requestTradeStockInfoSucceedWithStockCode:(id)arg1 strMarket:(id)arg2 reqMkt:(BOOL)arg3;
- (void)autoSwitchGeGuFenShi:(id)arg1;
- (void)reloadStockNotes;
- (void)reloadGuanLianQiZhi;
- (void)reloadQiHuoGuanLianAGu;
- (void)reloadGuanLianQiHuo;
- (void)reloadWoLunNiuXiongWithZhengGu;
- (void)reloadRelatedWoLunNiuXiong;
- (void)reloadGuanLianShangPin;
- (void)reloadGuaDanCheDan;
- (void)reloadTradingQueue;
- (void)reloadQuickTradeTable;
- (void)reloadDaDanLengJing;
- (void)reloadGuanLianBaoJiaTable;
- (void)reloadRelatedZiXunTable;
- (void)reloadZiXunTable;
- (void)reloadQuanShangXiWei;
- (void)refreshHuLunTongCDRMoudule;
- (void)refreshBondZiXunMoudule;
- (void)refreshXinSanBanIndexZiXunMoudule;
- (void)refreshXinSanBanStockZiXunMoudule;
- (void)refreshHuShenIndexZiXunMoudule;
- (void)refreshBlockZiXunMoudule;
- (void)refreshBlockDPFXZiXunMoudule;
- (void)refreshUSIndexZiXunMoudule;
- (void)refreshMeiGuZiXunMoudule;
- (void)refreshHKIndexZiXunMoudule;
- (void)refreshWoLunNiuXiongZiXunMoudule;
- (void)refreshGangGuZiXunMoudule;
- (void)refreshCommodityOptionZiXunModule;
- (void)refreshHuShenOptionZiXunModule;
- (void)refreshGuoNeiQiHuoZiXunModule;
- (void)refreshGuZhiQiHuoZiXunModule;
- (void)refreshHuShenJiJinZiXunMoudule;
- (void)refreshWaiHuiZiXunMoudule;
- (void)refreshHuShenBGuZiXunMoudule;
- (void)refreshHuShenAGuZiXunMoudule;
- (void)refreshDefaultZiXunModule;
- (void)constructIndexZiXunRequestParam:(unsigned long long)arg1 indexType:(unsigned long long)arg2;
- (void)constructWaiHuiZiXunRequestParam:(unsigned long long)arg1;
- (void)constructGuoNeiQiHuoZiXunRequestParam;
- (void)constructGuZhiQiHuoZiXunRequestParam;
- (void)constructHuShenIndexZiXunRequestParam;
- (void)constructBlockZiXunRequestParam;
- (void)constructHuShenJiJinZiXunRequestParam;
- (void)constructGeGuXinWenZiXunRequestParam;
- (void)constructMeiGuZiXunRequestParam;
- (void)constructWoLunNiuXiongZiXunRequestParam;
- (void)constructNormalZiXunRequestParam;
- (void)constructHuShenOptionZiXunRequestParam;
- (void)constructDefaultZiXunRequestParam;
- (void)notifyUpperLevelControllerToChangeFrame;
- (void)addActionEventForBtns;
- (void)setDefaultTabbarControllerIndex;
- (void)setTabbarViewControllers;
@property(readonly, nonatomic) BOOL isL2Account; // @dynamic isL2Account;
- (void)updateZiXunMenuBtnState;
- (void)ziXunMenuBtnClicked:(id)arg1;
- (void)hideZiXunContentView:(id)arg1;
- (void)displayDataAtIndex:(long long)arg1 withStamp:(long long)arg2 param:(id)arg3;
- (void)switchTradeQueueModuleMode:(BOOL)arg1;
- (void)ziXunAreaState:(BOOL)arg1;
- (void)openQuickJY:(id)arg1 market:(id)arg2 price:(id)arg3 forceReq:(id)arg4 selectIndex:(id)arg5;
- (void)openWidgetJY:(id)arg1 market:(id)arg2 price:(id)arg3 maimaiType:(int)arg4;
- (void)quickTradeDidUpdate:(id)arg1;
- (void)refreshContentViews;
- (void)openQuickTradeVC;
- (void)initDefaultProperties;
- (void)viewDidDisappear;
- (void)viewWillDisappear;
- (void)viewWillAppear;
- (void)viewDidAppear;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

