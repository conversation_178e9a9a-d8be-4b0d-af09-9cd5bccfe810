//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "PanKouModularBaseViewController.h"

@class HXBaseView, HXDragToResizeView, NSString, NSTextField, ZhongYaoZhiShuTableModularViewController;

@interface ZhongYaoZhiShuModularContainerController : PanKouModularBaseViewController
{
    BOOL _modularRequseted;
    HXBaseView *_tableContentView;
    NSTextField *_titleTF;
    HXDragToResizeView *_resizeView;
    ZhongYaoZhiShuTableModularViewController *_zhongYaoZhiShuTableVC;
    NSString *_singleStockCode;
    NSString *_singleMarket;
}


@property(retain, nonatomic) NSString *singleMarket; // @synthesize singleMarket=_singleMarket;
@property(retain, nonatomic) NSString *singleStockCode; // @synthesize singleStockCode=_singleStockCode;
@property(nonatomic) BOOL modularRequseted; // @synthesize modularRequseted=_modularRequseted;
@property(retain, nonatomic) ZhongYaoZhiShuTableModularViewController *zhongYaoZhiShuTableVC; // @synthesize zhongYaoZhiShuTableVC=_zhongYaoZhiShuTableVC;
@property __weak HXDragToResizeView *resizeView; // @synthesize resizeView=_resizeView;
@property __weak NSTextField *titleTF; // @synthesize titleTF=_titleTF;
@property __weak HXBaseView *tableContentView; // @synthesize tableContentView=_tableContentView;
- (void)jugeCanRequset:(id)arg1 market:(id)arg2;
- (void)disableScroll;
- (void)fireTableModularTimer;
- (void)requestForTableViewData;
- (void)deleteModularOrder;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
- (void)setViewState;
- (void)viewDidLoad;

@end

