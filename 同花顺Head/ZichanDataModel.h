//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

#import "NSCopying-Protocol.h"

@class NSString;

@interface ZichanDataModel : NSObject <NSCopying>
{
    BOOL _needAddJjsz;
    BOOL _needCalcZsz;
    BOOL _needCalcZzc;
    NSString *_strZjzh;
    NSString *_strHblx;
    NSString *_strKhqs;
    NSString *_strZzc;
    NSString *_strZsz;
    NSString *_strZyk;
    NSString *_strDryk;
    NSString *_strZjye;
    NSString *_strDjje;
    NSString *_strKqje;
    NSString *_strKyje;
    NSString *_strSjje;
    NSString *_strJjsz;
    NSString *_strMeiguZzc;
    NSString *_strMeiguGml;
    NSString *_strMeiguT0cs;
    NSString *_strMeiguRnfk;
    NSString *_strMeiguGyfk;
    NSString *_strMeiguZhjb;
    NSString *_strMeiguHl;
    NSString *_strMeiguZqzz;
    NSString *_strMeiguZhlx;
    NSString *_strMeiguYgzt;
    NSString *_strMeiguZyk;
}


@property(copy, nonatomic) NSString *strMeiguZyk; // @synthesize strMeiguZyk=_strMeiguZyk;
@property(copy, nonatomic) NSString *strMeiguYgzt; // @synthesize strMeiguYgzt=_strMeiguYgzt;
@property(copy, nonatomic) NSString *strMeiguZhlx; // @synthesize strMeiguZhlx=_strMeiguZhlx;
@property(copy, nonatomic) NSString *strMeiguZqzz; // @synthesize strMeiguZqzz=_strMeiguZqzz;
@property(copy, nonatomic) NSString *strMeiguHl; // @synthesize strMeiguHl=_strMeiguHl;
@property(copy, nonatomic) NSString *strMeiguZhjb; // @synthesize strMeiguZhjb=_strMeiguZhjb;
@property(copy, nonatomic) NSString *strMeiguGyfk; // @synthesize strMeiguGyfk=_strMeiguGyfk;
@property(copy, nonatomic) NSString *strMeiguRnfk; // @synthesize strMeiguRnfk=_strMeiguRnfk;
@property(copy, nonatomic) NSString *strMeiguT0cs; // @synthesize strMeiguT0cs=_strMeiguT0cs;
@property(copy, nonatomic) NSString *strMeiguGml; // @synthesize strMeiguGml=_strMeiguGml;
@property(copy, nonatomic) NSString *strMeiguZzc; // @synthesize strMeiguZzc=_strMeiguZzc;
@property(nonatomic) BOOL needCalcZzc; // @synthesize needCalcZzc=_needCalcZzc;
@property(nonatomic) BOOL needCalcZsz; // @synthesize needCalcZsz=_needCalcZsz;
@property(nonatomic) BOOL needAddJjsz; // @synthesize needAddJjsz=_needAddJjsz;
@property(copy, nonatomic) NSString *strJjsz; // @synthesize strJjsz=_strJjsz;
@property(copy, nonatomic) NSString *strSjje; // @synthesize strSjje=_strSjje;
@property(copy, nonatomic) NSString *strKyje; // @synthesize strKyje=_strKyje;
@property(copy, nonatomic) NSString *strKqje; // @synthesize strKqje=_strKqje;
@property(copy, nonatomic) NSString *strDjje; // @synthesize strDjje=_strDjje;
@property(copy, nonatomic) NSString *strZjye; // @synthesize strZjye=_strZjye;
@property(copy, nonatomic) NSString *strDryk; // @synthesize strDryk=_strDryk;
@property(copy, nonatomic) NSString *strZyk; // @synthesize strZyk=_strZyk;
@property(copy, nonatomic) NSString *strZsz; // @synthesize strZsz=_strZsz;
@property(copy, nonatomic) NSString *strZzc; // @synthesize strZzc=_strZzc;
@property(copy, nonatomic) NSString *strKhqs; // @synthesize strKhqs=_strKhqs;
@property(copy, nonatomic) NSString *strHblx; // @synthesize strHblx=_strHblx;
@property(copy, nonatomic) NSString *strZjzh; // @synthesize strZjzh=_strZjzh;
- (id)copyWithZone:(struct _NSZone *)arg1;
- (void)resetZichanDataModel;
- (id)init;

@end

