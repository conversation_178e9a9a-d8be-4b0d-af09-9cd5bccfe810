//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXBaseViewController.h"

#import "WebFrameLoadDelegate-Protocol.h"
#import "WebPolicyDecisionListener-Protocol.h"
#import "WebUIDelegate-Protocol.h"

@class HXBaseView, HXButton, NSArray, NSString, WebView;

@interface ZiXunViewController : HXBaseViewController <WebPolicyDecisionListener, WebUIDelegate, WebFrameLoadDelegate>
{
    WebView *_webView;
    HXBaseView *_topView;
    HXButton *_indexBtn;
    HXButton *_financeBtn;
    HXButton *_newsBtn;
    HXButton *__24hourScrollBtn;
    HXButton *_marketBtn;
    NSArray *_toolBarItems;
}


@property(copy, nonatomic) NSArray *toolBarItems; // @synthesize toolBarItems=_toolBarItems;
@property __weak HXButton *marketBtn; // @synthesize marketBtn=_marketBtn;
@property __weak HXButton *_24hourScrollBtn; // @synthesize _24hourScrollBtn=__24hourScrollBtn;
@property __weak HXButton *newsBtn; // @synthesize newsBtn=_newsBtn;
@property __weak HXButton *financeBtn; // @synthesize financeBtn=_financeBtn;
@property __weak HXButton *indexBtn; // @synthesize indexBtn=_indexBtn;
@property __weak HXBaseView *topView; // @synthesize topView=_topView;
@property __weak WebView *webView; // @synthesize webView=_webView;
- (void)webView:(id)arg1 didCreateJavaScriptContext:(id)arg2 forFrame:(id)arg3;
- (id)webView:(id)arg1 contextMenuItemsForElement:(id)arg2 defaultMenuItems:(id)arg3;
- (id)webView:(id)arg1 createWebViewWithRequest:(id)arg2;
- (void)webView:(id)arg1 decidePolicyForNewWindowAction:(id)arg2 request:(id)arg3 newFrameName:(id)arg4 decisionListener:(id)arg5;
- (void)ignore;
- (void)download;
- (void)use;
- (void)marketBtnClicked:(id)arg1;
- (void)_24hourScrollBtnClicked:(id)arg1;
- (void)newsBtnClicked:(id)arg1;
- (void)financeBtnClicked:(id)arg1;
- (void)indexBtnClicked:(id)arg1;
- (void)setSelectedItem:(id)arg1;
- (void)setUpBtns;
- (void)insertJSCModel;
- (void)loadWebViewWithURL:(id)arg1;
- (void)setWebViewDelegates;
- (void)initObjects;
- (void)viewDidAppear;
- (void)viewWillAppear;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

