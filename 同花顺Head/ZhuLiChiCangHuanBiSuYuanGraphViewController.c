void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController viewDidLoad](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2)
{
  NSView *v4; // rax
  NSView *drawContentView; // rdi

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___ZhuLiChiCangHuanBiSuYuanGraphViewController;
  -[GraphBaseViewController viewDidLoad](&v6, "viewDidLoad");
  -[GraphBaseViewController setCurrentPlotType:](self, "setCurrentPlotType:", 122LL);
  v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v4 = (NSView *)objc_retainAutoreleasedReturnValue(v3);
  drawContentView = self->super._drawContentView;
  self->super._drawContentView = v4;
}

//----- (00000001007DE672) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController requestForModularData:market:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        id a3,
        id a4)
{

  objc_retain(a3);
  v5 = objc_retain(a4);
  if ( _objc_msgSend(v6, "length") && _objc_msgSend(v5, "length") )
  {
    -[HXBaseViewController setStockCode:](self, "setStockCode:", v7);
    -[HXBaseViewController setMarket:](self, "setMarket:", v5);
    if ( (unsigned __int64)_objc_msgSend(self->super._drawContentView, "count") > 7 )
      -[GraphBaseViewController updatePlotWhenParseFinished](self, "updatePlotWhenParseFinished");
    else
      -[ZhuLiChiCangHuanBiSuYuanGraphViewController requestData](self, "requestData");
  }
}

//----- (00000001007DE739) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController requestData](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2)
{
  NSURL *v2; // rax
  NSURL *v3; // r15
  id *v11; // r12
  _QWORD v12[5]; // [rsp+28h] [rbp-68h] BYREF
  id to; // [rsp+50h] [rbp-40h] BYREF
  id location[6]; // [rsp+60h] [rbp-30h] BYREF

  if ( (__int64)self->super._plotItem <= 0 )
  {
    self->super._plotItem = (PlotBaseView *)8;
    _objc_msgSend(self->super._drawContentView, "removeAllObjects");
    v2 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", CFSTR("http://zx.10jqka.com.cn/indval/getallindustry"));
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v5 = (void *)v4(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v3);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = objc_alloc((Class)&OBJC_CLASS___AFHTTPRequestOperation);
    v14 = v6;
    v9 = (void *)v8(v7, "initWithRequest:", v6);
    objc_initWeak(location, self);
    v12[0] = _NSConcreteStackBlock;
    v12[1] = 3254779904LL;
    v12[2] = sub_1007DE8EC;
    v12[3] = &unk_1012DC830;
    objc_copyWeak(&to, location);
    *(_QWORD *)(v10 - 8) = self;
    _objc_msgSend(v9, "setCompletionBlockWithSuccess:failure:", v12);
    _objc_msgSend(v9, "start");
    objc_destroyWeak(v11);
    objc_destroyWeak(location);
  }
}

//----- (00000001007DE8EC) ----------------------------------------------------
__int64 __fastcall sub_1007DE8EC(__int64 a1, __int64 a2, void *a3)
{
  id WeakRetained; // rbx

  objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "dealWithFirstRequestData:", v4);
  result = *(_QWORD *)(a1 + 32);
  --*(_QWORD *)(result + 232);
  return result;
}

//----- (00000001007DE953) ----------------------------------------------------
__int64 __fastcall sub_1007DE953(__int64 a1)
{

  result = *(_QWORD *)(a1 + 32);
  --*(_QWORD *)(result + 232);
  return result;
}

//----- (00000001007DE968) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController dealWithFirstRequestData:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v18; // r15
  NSMutableArray *v25; // rax
  id to; // [rsp+68h] [rbp-158h] BYREF
  SEL v49; // [rsp+98h] [rbp-128h]
  SEL v50; // [rsp+A0h] [rbp-120h]
  SEL v51; // [rsp+A8h] [rbp-118h]
  SEL v52; // [rsp+B0h] [rbp-110h]
  SEL v53; // [rsp+B8h] [rbp-108h]
  id location; // [rsp+E8h] [rbp-D8h] BYREF
  SEL v61; // [rsp+F8h] [rbp-C8h]
  id obj; // [rsp+100h] [rbp-C0h]

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "JSONObjectWithData:options:error:", v3, 0LL, 0LL);
    objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
    {
      val = self;
      _objc_msgSend(self->super._drawContentView, "addObject:", v8);
      v10 = _objc_msgSend(v9, "thsDictionaryForKey:", CFSTR("result"));
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(v11, "thsStringForKey:", CFSTR("date"));
      v13 = v11;
      v14 = objc_retainAutoreleasedReturnValue(v12);
      if ( (unsigned __int64)_objc_msgSend(v14, "length") >= 6 )
      {
        v58 = v15;
        v56 = v4;
        v16 = _objc_msgSend(v14, "substringToIndex:", 4LL);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = (unsigned __int64)_objc_msgSend(v17, "integerValue");
        v20 = v19;
        v57 = v14;
        v21 = _objc_msgSend(v14, "substringWithRange:", 4LL, 2LL);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        v23 = _objc_msgSend(v22, "integerValue");
        v20(v24);
        v25 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithCapacity:", 8LL);
        v63 = objc_retainAutoreleasedReturnValue(v25);
        v61 = "stringWithFormat:";
        do
        {
          v18 = (__PAIR128__(v18, (unsigned __int64)v23 - 1) - 3) >> 64;
          v23 = _objc_msgSend(val, "perQuarterMonth:", v23);
          v26 = _objc_msgSend(&OBJC_CLASS___NSString, v61, CFSTR("%ld%02ld"), v18, v23);
          v27 = objc_retainAutoreleasedReturnValue(v26);
          _objc_msgSend(v63, "addObject:", v27);
        }
        while ( v28 != 1 );
        objc_initWeak(&location, val);
        v42 = 0LL;
        v43 = 0LL;
        v44 = 0LL;
        v45 = 0LL;
        obj = objc_retain(v63);
        v29 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v42, v64, 16LL);
        if ( v29 )
        {
          v55 = *(_QWORD *)v43;
          do
          {
            v61 = "stringWithFormat:";
            v49 = "URLWithString:";
            v50 = "requestWithURL:";
            v51 = "initWithRequest:";
            v52 = "setCompletionBlockWithSuccess:failure:";
            v53 = "start";
            v30 = 0LL;
            v54 = v29;
            do
            {
              if ( *(_QWORD *)v43 != v55 )
                objc_enumerationMutation(obj);
              v31 = _objc_msgSend(
                      &OBJC_CLASS___NSString,
                      v61,
                      CFSTR("http://zx.10jqka.com.cn/indval/getallindustry?date=%@"),
                      *(_QWORD *)(*((_QWORD *)&v42 + 1) + 8LL * (_QWORD)v30));
              v32 = objc_retainAutoreleasedReturnValue(v31);
              v33 = _objc_msgSend(&OBJC_CLASS___NSURL, v49, v32);
              v34 = objc_retainAutoreleasedReturnValue(v33);
              v35 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, v50, v34);
              v63 = v36;
              objc_retainAutoreleasedReturnValue(v35);
              v37 = objc_alloc((Class)&OBJC_CLASS___AFHTTPRequestOperation);
              v39 = _objc_msgSend(v37, v51, v38);
              v46[0] = (__int64)_NSConcreteStackBlock;
              v46[1] = 3254779904LL;
              v46[2] = (__int64)sub_1007DEF39;
              v46[3] = (__int64)&unk_1012DC830;
              objc_copyWeak(&to, &location);
              v46[4] = (__int64)val;
              v48[0] = (__int64)_NSConcreteStackBlock;
              v48[1] = 3254779904LL;
              v48[2] = (__int64)sub_1007DEFA0;
              v48[3] = (__int64)&unk_1012E5C50;
              v48[4] = (__int64)val;
              _objc_msgSend(v39, v52, v46, v48);
              _objc_msgSend(v39, v53);
              objc_destroyWeak(&to);
              v30 = (char *)v30 + 1;
            }
            while ( v54 != v30 );
            v29 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v42, v64, 16LL);
          }
          while ( v29 );
        }
        v41 = obj;
        objc_destroyWeak(&location);
        v4 = v56;
        v14 = v57;
      }
    }
  }
}

//----- (00000001007DEF39) ----------------------------------------------------
__int64 __fastcall sub_1007DEF39(__int64 a1, __int64 a2, void *a3)
{
  id WeakRetained; // rbx

  objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "dealWithRequestData:", v4);
  result = *(_QWORD *)(a1 + 32);
  --*(_QWORD *)(result + 232);
  return result;
}

//----- (00000001007DEFA0) ----------------------------------------------------
__int64 __fastcall sub_1007DEFA0(__int64 a1)
{

  result = *(_QWORD *)(a1 + 32);
  --*(_QWORD *)(result + 232);
  return result;
}

//----- (00000001007DEFB5) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController dealWithRequestData:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        id a3)
{
  NSView *drawContentView; // r15
  SimpleGraphDataParseModule *v7; // rax
  SimpleGraphDataParseModule *v8; // rax

  if ( a3 )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "JSONObjectWithData:options:error:", a3, 0LL, 0LL);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
    if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
    {
      _objc_msgSend(self->super._drawContentView, "addObject:", v4);
      if ( _objc_msgSend(self->super._drawContentView, "count") == (id)8 )
      {
        _objc_msgSend(self->super._drawContentView, "sortUsingComparator:", &stru_1012E5C80);
        drawContentView = self->super._drawContentView;
        v7 = -[GraphBaseViewController SGParser](self, "SGParser");
        v8 = objc_retainAutoreleasedReturnValue(v7);
        -[SimpleGraphDataParseModule setZhuLiChiCangHuanBiArray:](v8, "setZhuLiChiCangHuanBiArray:", drawContentView);
        -[GraphBaseViewController updatePlotWhenParseFinished](self, "updatePlotWhenParseFinished");
      }
    }
  }
}

//----- (00000001007DF0C9) ----------------------------------------------------
signed __int64 __cdecl sub_1007DF0C9(id a1, NSDictionary *a2, NSDictionary *a3)
{

  objc_retain(a3);
  v3 = _objc_msgSend(a2, "thsDictionaryForKey:", CFSTR("result"));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "thsStringForKey:", CFSTR("date"));
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(v7, "thsDictionaryForKey:", CFSTR("result"));
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v9, "thsStringForKey:", CFSTR("date"));
  objc_retainAutoreleasedReturnValue(v11);
  v13 = 0LL;
  if ( v6 && v12 )
    v13 = _objc_msgSend(v6, "compare:", v12);
  return (signed __int64)v13;
}

//----- (00000001007DF1C6) ----------------------------------------------------
signed __int64 __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController perQuarterMonth:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        signed __int64 a3)
{
  if ( (unsigned __int64)(a3 - 1) > 0xB )
    return 3LL;
  else
    return qword_1010DC1F0[a3 - 1];
}

//----- (00000001007DF1E8) ----------------------------------------------------
id __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController createDataForPlot](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2)
{
  SimpleGraphDataParseModule *v4; // rax
  SimpleGraphDataParseModule *v5; // r15
  NSString *v6; // rax
  NSString *v7; // rax

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[GraphBaseViewController SGParser](self, "SGParser");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXBaseViewController stockCode](self, "stockCode");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[SimpleGraphDataParseModule getZhuLiChiCangHuanBiData:](v5, "getZhuLiChiCangHuanBiData:", v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v3, "setObject:forKeyedSubscript:", v9, off_1012E3E08);
  return objc_autoreleaseReturnValue(v3);
}

//----- (00000001007DF2B0) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController infoBtnDidClick:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        id a3)
{

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___ZhuLiChiCangHuanBiSuYuanGraphViewController;
  v3 = objc_retain(a3);
  -[GraphBaseViewController infoBtnDidClick:](&v5, "infoBtnDidClick:", v3);
  v4 = _objc_msgSend(v3, "state");
  if ( v4 )
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
      &OBJC_CLASS___UserLogSendingQueueManager,
      "sendUserLog:action:params:needWait:",
      11LL,
      CFSTR("板块热点_行业板块_行业估值_释义"),
      0LL,
      1LL);
}

//----- (00000001007DF345) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController mouseEntered:](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2,
        id a3)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___ZhuLiChiCangHuanBiSuYuanGraphViewController;
  -[GraphBaseViewController mouseEntered:](&v3, "mouseEntered:", a3);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("板块热点_行业板块_行业估值"),
    0LL,
    1LL);
}

//----- (00000001007DF39D) ----------------------------------------------------
void __cdecl -[ZhuLiChiCangHuanBiSuYuanGraphViewController .cxx_destruct](
        ZhuLiChiCangHuanBiSuYuanGraphViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super._drawContentView, 0LL);
}

