//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXCustomWindowController.h"

@class NSButton, NSImageView, NSTextField;

@interface UserKickWindowController : HXCustomWindowController
{
    NSImageView *_aIconImageView;
    NSTextField *_aHeadTitleTF;
    NSTextField *_aHeadTextTF;
    NSButton *_aReLoginButton;
    NSButton *_aCancelButton;
}


@property __weak NSButton *aCancelButton; // @synthesize aCancelButton=_aCancelButton;
@property __weak NSButton *aReLoginButton; // @synthesize aReLoginButton=_aReLoginButton;
@property __weak NSTextField *aHeadTextTF; // @synthesize aHeadTextTF=_aHeadTextTF;
@property __weak NSTextField *aHeadTitleTF; // @synthesize aHeadTitleTF=_aHeadTitleTF;
@property __weak NSImageView *aIconImageView; // @synthesize aIconImageView=_aIconImageView;
- (void)cancelBtnClick:(id)arg1;
- (void)reLoginBtnClick:(id)arg1;
- (void)initViewStates;
- (void)windowDidLoad;

@end

