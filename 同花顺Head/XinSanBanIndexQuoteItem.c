XinSanBanIndexQuoteItem *__cdecl -[XinSanBanIndexQuoteItem init](XinSanBanIndexQuoteItem *self, SEL a2)
{
  XinSanBanIndexQuoteItem *v2; // rax
  XinSanBanIndexQuoteItem *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___XinSanBanIndexQuoteItem;
  v2 = -[QuoteBaseItem init](&v5, "init");
  v3 = v2;
  if ( v2 )
    -[XinSanBanIndexQuoteItem initializeExtraValues](v2, "initializeExtraValues");
  return v3;
}

//----- (000000010060D798) ----------------------------------------------------
void __cdecl -[XinSanBanIndexQuoteItem initializeExtraValues](XinSanBanIndexQuoteItem *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XinSanBanIndexQuoteItem;
  -[QuoteBaseItem initializeBaseValues](&v2, "initializeBaseValues");
  -[XinSanBanIndexQuoteItem setJunJia:](self, "setJunJia:", 4294967295.0);
}

//----- (000000010060D7E1) ----------------------------------------------------
void __cdecl -[XinSanBanIndexQuoteItem resetExtraValues:](XinSanBanIndexQuoteItem *self, SEL a2, id a3)
{

  v3.receiver = self;
  v3.super_class = (Class)&OBJC_CLASS___XinSanBanIndexQuoteItem;
  -[QuoteBaseItem resetBaseValues:](&v3, "resetBaseValues:", a3);
  -[XinSanBanIndexQuoteItem junJiaCalculate](self, "junJiaCalculate");
}

//----- (000000010060D822) ----------------------------------------------------
void __cdecl -[XinSanBanIndexQuoteItem junJiaCalculate](XinSanBanIndexQuoteItem *self, SEL a2)
{

  -[QuoteBaseItem jinE](self, "jinE");
  if ( v2 != 4294967295.0 )
  {
    -[QuoteBaseItem jinE](self, "jinE");
    if ( v2 != 2147483648.0 )
    {
      -[QuoteBaseItem zongLiang](self, "zongLiang");
      if ( v2 != 4294967295.0 )
      {
        -[QuoteBaseItem zongLiang](self, "zongLiang");
        if ( v2 != 2147483648.0 )
        {
          -[QuoteBaseItem zongLiang](self, "zongLiang");
          if ( v2 != 0.0 )
          {
            -[QuoteBaseItem jinE](self, "jinE");
            v3(self, "zongLiang");
            v4(self, "setJunJia:", v2 / v2);
          }
        }
      }
    }
  }
}

//----- (000000010060D90A) ----------------------------------------------------
double __cdecl -[XinSanBanIndexQuoteItem junJia](XinSanBanIndexQuoteItem *self, SEL a2)
{
  return self->_junJia;
}

//----- (000000010060D91C) ----------------------------------------------------
void __cdecl -[XinSanBanIndexQuoteItem setJunJia:](XinSanBanIndexQuoteItem *self, SEL a2, double a3)
{
  self->_junJia = a3;
}

