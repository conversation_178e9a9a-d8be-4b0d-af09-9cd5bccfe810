void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController viewDidLoad](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZuoRiZhangTingJinBiaoXianGraphViewController;
  -[GraphBaseViewController viewDidLoad](&v2, "viewDidLoad");
  -[ZuoRiZhangTingJinBiaoXianGraphViewController initObjects](self, "initObjects");
}

//----- (0000000100006C2D) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController viewDidAppear](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZuoRiZhangTingJinBiaoXianGraphViewController;
  -[GraphBaseViewController viewDidAppear](&v2, "viewDidAppear");
  -[ZuoRiZhangTingJinBiaoXianGraphViewController requestAllModularData](self, "requestAllModularData");
}

//----- (0000000100006C6E) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController initObjects](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  NSTextField *v2; // rax
  NSTextField *v3; // rbx
  NSTextField *v4; // rax
  NSTextField *v5; // rbx
  SEL v6; // r12

  v2 = -[GraphBaseViewController titleLabel](self, "titleLabel");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setStringValue:", CFSTR("昨日涨停今表现"));
  v4 = -[GraphBaseViewController infoMainString](self, "infoMainString");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(
    v5,
    v6,
    CFSTR("统计昨日涨停股（剔除新股）今日表现。指标反映短期资金追涨后次日收益，当活跃资金短期获得额较大收益时，能刺激市场吸引资金，反之则交投下降，市场趋于弱势。"));
  -[GraphBaseViewController setCurrentPlotType:](self, "setCurrentPlotType:", 124LL);
  v7 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(self, "view");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v8, "addObserver:selector:name:object:", self, "frameDidChange:", v11, v10);
}

//----- (0000000100006D7C) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController dealloc](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZuoRiZhangTingJinBiaoXianGraphViewController;
  -[GraphBaseViewController dealloc](&v4, "dealloc");
}

//----- (0000000100006DF1) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController frameDidChange:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // r14
  NSTextField *v5; // rax
  NSTextField *v6; // rbx

  v3 = -[GraphBaseViewController infoContainerView](self, "infoContainerView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "width");
  v5 = -[GraphBaseViewController infoMainString](self, "infoMainString");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setWidth:");
}

//----- (0000000100006E75) ----------------------------------------------------
SimpleGraphDataRequestModule *__cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController SGRequestShouYi](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  PlotBaseView *plotItem; // rdi
  PlotBaseView *v5; // rax
  PlotBaseView *v6; // rdi

  plotItem = self->super._plotItem;
  if ( !plotItem )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___SimpleGraphDataRequestModule);
    v5 = (PlotBaseView *)_objc_msgSend(v4, "init");
    v6 = self->super._plotItem;
    self->super._plotItem = v5;
    plotItem = self->super._plotItem;
  }
  return (SimpleGraphDataRequestModule *)objc_retainAutoreleaseReturnValue(plotItem);
}

//----- (0000000100006EC6) ----------------------------------------------------
SimpleGraphDataRequestModule *__cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController SGRequestShangZheng](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  NSView *drawContentView; // rdi
  NSView *v5; // rax
  NSView *v6; // rdi

  drawContentView = self->super._drawContentView;
  if ( !drawContentView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___SimpleGraphDataRequestModule);
    v5 = (NSView *)_objc_msgSend(v4, "init");
    v6 = self->super._drawContentView;
    self->super._drawContentView = v5;
    drawContentView = self->super._drawContentView;
  }
  return (SimpleGraphDataRequestModule *)objc_retainAutoreleaseReturnValue(drawContentView);
}

//----- (0000000100006F17) ----------------------------------------------------
SimpleGraphDataParseModule *__cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController SGParserForShangZheng](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  NSButton *infoBtn; // rdi
  NSButton *v5; // rax
  NSButton *v6; // rdi

  infoBtn = self->super._infoBtn;
  if ( !infoBtn )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___SimpleGraphDataParseModule);
    v5 = (NSButton *)_objc_msgSend(v4, "init");
    v6 = self->super._infoBtn;
    self->super._infoBtn = v5;
    infoBtn = self->super._infoBtn;
  }
  return (SimpleGraphDataParseModule *)objc_retainAutoreleaseReturnValue(infoBtn);
}

//----- (0000000100006F68) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController refreshAllModules](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  -[ZuoRiZhangTingJinBiaoXianGraphViewController requestAllModularData](self, "requestAllModularData");
}

//----- (0000000100006F7A) ----------------------------------------------------
id __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController createDataForPlot](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v14 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "SGParser");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "getZuoRiZhangTingJinBiaoXianData");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "SGParserForShangZheng");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "getZuoRiZhangTingJinBiaoXianData");
  objc_retainAutoreleasedReturnValue(v11);
  if ( v7 )
    _objc_msgSend(v14, "setObject:forKeyedSubscript:", v7, off_1012E3D60);
  if ( v12 )
    _objc_msgSend(v14, "setObject:forKeyedSubscript:", v12, off_1012E3D68);
  return objc_autoreleaseReturnValue(v14);
}

//----- (000000010000708C) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController requestAllModularData](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  NSNumber *v4; // rax
  NSNumber *v5; // rbx
  NSDictionary *v6; // rax
  NSDictionary *v7; // r14
  NSDictionary *v11; // r15
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  NSDictionary *v16; // rax
  NSDictionary *v17; // r13
  _QWORD v23[4]; // [rsp+0h] [rbp-110h] BYREF
  _QWORD v26[4]; // [rsp+30h] [rbp-E0h] BYREF
  id to; // [rsp+58h] [rbp-B8h] BYREF
  __CFString *v29; // [rsp+60h] [rbp-B0h]
  __CFString *v30; // [rsp+68h] [rbp-A8h]
  __CFString *v31; // [rsp+70h] [rbp-A0h]
  id location; // [rsp+78h] [rbp-98h] BYREF

  objc_initWeak(&location, self);
  v29 = off_1012E2B50;
  v35[0] = (__int64)off_1012E2B50;
  v36[0] = (__int64)CFSTR("883900");
  v30 = off_1012E2B58;
  v35[1] = (__int64)off_1012E2B58;
  v36[1] = (__int64)CFSTR("URFI");
  v31 = off_1012E2B60;
  v35[2] = (__int64)off_1012E2B60;
  v3 = _objc_msgSend(v2, "currentPlotType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v3);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v36[2] = (__int64)v5;
  v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v36, v35, 3LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "SGRequestShouYi");
  v10 = objc_retain(v9);
  v26[0] = _NSConcreteStackBlock;
  v26[1] = 3254779904LL;
  v26[2] = sub_1000073F3;
  v26[3] = &unk_1012DAB30;
  objc_copyWeak(&to, &location);
  v11 = objc_retain(v7);
  v27 = v11;
  _objc_msgSend(v10, "requestZuoRiZhangTingJinBiaoXian:callBack:", v11, v26);
  v33[0] = (__int64)v29;
  v34[0] = (__int64)CFSTR("1A0001");
  v33[1] = (__int64)v30;
  v34[1] = (__int64)CFSTR("USHI");
  v33[2] = (__int64)v31;
  v13 = _objc_msgSend(v12, "currentPlotType");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v13);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v34[2] = (__int64)v15;
  v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v34, v33, 3LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = _objc_msgSend(v18, "SGRequestShangZheng");
  ((void (__fastcall *)(id))objc_retain)(v19);
  v23[0] = _NSConcreteStackBlock;
  v23[1] = 3254779904LL;
  v23[2] = sub_1000074C2;
  v23[3] = &unk_1012DAB30;
  objc_copyWeak(&v25, &location);
  v20 = (void *)((__int64 (__fastcall *)(NSDictionary *))objc_retain)(v17);
  v24 = v20;
  _objc_msgSend(v21, "requestZuoRiZhangTingJinBiaoXian:callBack:", v20, v23);
  objc_destroyWeak(&v25);
  objc_destroyWeak(&to);
  objc_destroyWeak(&location);
}

//----- (00000001000073F3) ----------------------------------------------------
void __fastcall sub_1000073F3(__int64 a1, void *a2)
{
  id WeakRetained; // rax

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v4 = _objc_msgSend(WeakRetained, "SGParser");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "replaceAllWithZuoRiZhangTingJinBiaoXianDataModel:", v2);
  v7 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v7, "updatePlotWhenParseFinished");
  v8 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v8, "orderShouYi:", *(_QWORD *)(a1 + 32));
}

//----- (00000001000074C2) ----------------------------------------------------
void __fastcall sub_1000074C2(__int64 a1, void *a2)
{
  id WeakRetained; // rax

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v4 = _objc_msgSend(WeakRetained, "SGParserForShangZheng");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "replaceAllWithZuoRiZhangTingJinBiaoXianDataModel:", v2);
  v7 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v7, "updatePlotWhenParseFinished");
  v8 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v8, "orderShangZheng:", *(_QWORD *)(a1 + 32));
}

//----- (0000000100007591) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController mouseDown:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  CGFloat v3; // xmm0_8
  CGFloat v4; // xmm1_8
  CGFloat x; // r13
  SelfStock *v16; // rax
  SelfStock *v17; // rbx
  NSNumber *v20; // rax
  NSNumber *v21; // rax
  NSNumber *v22; // rax
  NSNumber *v23; // rax
  NSNumber *v24; // rax
  NSNumber *v25; // rax
  NSNumber *v26; // r13
  NSNumber *v27; // rax
  NSNumber *v28; // rbx
  NSDictionary *v30; // rax
  NSDictionary *v31; // r15
  NSRect aRect; // [rsp+30h] [rbp-100h] BYREF
  NSPoint aPoint; // [rsp+70h] [rbp-C0h]
  _QWORD v42[8]; // [rsp+80h] [rbp-B0h] BYREF
  _QWORD v43[8]; // [rsp+C0h] [rbp-70h] BYREF

  x = COERCE_DOUBLE(objc_retain(a3));
  v36.receiver = v6;
  v36.super_class = (Class)&OBJC_CLASS___ZuoRiZhangTingJinBiaoXianGraphViewController;
  -[GraphBaseViewController mouseDown:](&v36, "mouseDown:", *(_QWORD *)&x);
  v8 = _objc_msgSend(v7, "view");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(*(id *)&x, "locationInWindow");
  _objc_msgSend(v9, "convertPoint:fromView:", 0LL);
  aPoint.x = v3;
  aPoint.y = v4;
  v11 = _objc_msgSend(v10, "view");
  v12 = (const char *)objc_retainAutoreleasedReturnValue(v11);
  if ( v12 )
    objc_msgSend_stret(&aRect, v12, "frame");
  else
    memset(&aRect, 0, sizeof(aRect));
  if ( !NSPointInRect(aPoint, aRect) )
    goto LABEL_7;
  v14 = _objc_msgSend(*(id *)&x, "clickCount");
  if ( v14 == (id)2 )
  {
    v16 = +[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    -[SelfStock addRecentlyScanStock:market:toBegin:](
      v17,
      "addRecentlyScanStock:market:toBegin:",
      CFSTR("1A0001"),
      CFSTR("USHI"),
      1LL);
    v19 = _objc_msgSend(v18, "stringByAppendingString:", CFSTR("1A0001"));
    objc_retainAutoreleasedReturnValue(v19);
    v42[0] = CFSTR("requesttype");
    v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
    *(_QWORD *)&aPoint.y = objc_retainAutoreleasedReturnValue(v20);
    v43[0] = *(_QWORD *)&aPoint.y;
    v42[1] = CFSTR("TableID");
    v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 513LL);
    v38 = objc_retainAutoreleasedReturnValue(v21);
    v43[1] = v38;
    v42[2] = CFSTR("sortid");
    v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v39 = objc_retainAutoreleasedReturnValue(v22);
    v43[2] = v39;
    v42[3] = CFSTR("sortbegin");
    v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v40 = objc_retainAutoreleasedReturnValue(v23);
    v43[3] = v40;
    v42[4] = CFSTR("sortcount");
    v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v25 = objc_retainAutoreleasedReturnValue(v24);
    aPoint.x = x;
    v26 = v25;
    v43[4] = v25;
    v42[5] = CFSTR("sortorder");
    v43[5] = CFSTR("D");
    v42[6] = CFSTR("Index");
    v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v43[6] = v28;
    v42[7] = CFSTR("SelectedCode");
    v43[7] = v29;
    v30 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v43, v42, 8LL);
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v32 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    _objc_msgSend(v33, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
    x = aPoint.x;
    v34 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    _objc_msgSend(v35, "postNotificationName:object:", CFSTR("DeliverQuotationTableDataNotification"), v31);
LABEL_7:
  }
}

//----- (00000001000079CF) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController orderShouYi:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  SimpleGraphDataRequestModule *v4; // rax
  SimpleGraphDataRequestModule *v5; // r15
  _QWORD v6[4]; // [rsp+8h] [rbp-48h] BYREF
  id to; // [rsp+28h] [rbp-28h] BYREF
  id location[4]; // [rsp+30h] [rbp-20h] BYREF

  v3 = objc_retain(a3);
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    objc_initWeak(location, self);
    v4 = -[ZuoRiZhangTingJinBiaoXianGraphViewController SGRequestShouYi](self, "SGRequestShouYi");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6[0] = _NSConcreteStackBlock;
    v6[1] = 3254779904LL;
    v6[2] = sub_100007ACA;
    v6[3] = &unk_1012DAB60;
    objc_copyWeak(&to, location);
    -[SimpleGraphDataRequestModule order:callBack:](v5, "order:callBack:", v3, v6);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (0000000100007ACA) ----------------------------------------------------
void __fastcall sub_100007ACA(__int64 a1, void *a2)
{
  id WeakRetained; // rbx
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  id *v15; // r12
  id *v19; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(v3 + 32));
  v5 = _objc_msgSend(WeakRetained, "currentPlotType");
  if ( v5 == (id)124 )
  {
    v6 = objc_retain(v2);
    v7 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
    {
      v22 = v2;
      v8 = _objc_msgSend(v6, "dicExt");
      v21 = v6;
      v23 = objc_retainAutoreleasedReturnValue(v8);
      v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v23, "objectForKey:", v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v12);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v6 = v21;
      if ( (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", CFSTR("883900")) )
      {
        v16 = objc_loadWeakRetained(v15);
        v17 = _objc_msgSend(v16, "SGParser");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        _objc_msgSend(v18, "updateDataModel:withPlotType:", v21, 124LL);
        v20 = objc_loadWeakRetained(v19);
        _objc_msgSend(v20, "updatePlotWhenParseFinished");
      }
      v2 = v22;
    }
  }
}

//----- (0000000100007CBD) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController orderShangZheng:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  SimpleGraphDataRequestModule *v4; // rax
  SimpleGraphDataRequestModule *v5; // r15
  _QWORD v6[4]; // [rsp+8h] [rbp-48h] BYREF
  id to; // [rsp+28h] [rbp-28h] BYREF
  id location[4]; // [rsp+30h] [rbp-20h] BYREF

  v3 = objc_retain(a3);
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    objc_initWeak(location, self);
    v4 = -[ZuoRiZhangTingJinBiaoXianGraphViewController SGRequestShangZheng](self, "SGRequestShangZheng");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6[0] = _NSConcreteStackBlock;
    v6[1] = 3254779904LL;
    v6[2] = sub_100007DB8;
    v6[3] = &unk_1012DAB60;
    objc_copyWeak(&to, location);
    -[SimpleGraphDataRequestModule order:callBack:](v5, "order:callBack:", v3, v6);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (0000000100007DB8) ----------------------------------------------------
void __fastcall sub_100007DB8(__int64 a1, void *a2)
{
  id WeakRetained; // rbx
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  id *v15; // r12
  id *v19; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(v3 + 32));
  v5 = _objc_msgSend(WeakRetained, "currentPlotType");
  if ( v5 == (id)124 )
  {
    v6 = objc_retain(v2);
    v7 = _objc_msgSend(&OBJC_CLASS___PCDBFileDataModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
    {
      v22 = v2;
      v8 = _objc_msgSend(v6, "dicExt");
      v21 = v6;
      v23 = objc_retainAutoreleasedReturnValue(v8);
      v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(v23, "objectForKey:", v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v12);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v6 = v21;
      if ( (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", CFSTR("1A0001")) )
      {
        v16 = objc_loadWeakRetained(v15);
        v17 = _objc_msgSend(v16, "SGParserForShangZheng");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        _objc_msgSend(v18, "updateDataModel:withPlotType:", v21, 124LL);
        v20 = objc_loadWeakRetained(v19);
        _objc_msgSend(v20, "updatePlotWhenParseFinished");
      }
      v2 = v22;
    }
  }
}

//----- (0000000100007FAB) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController deleteOrder](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  __CFString *v2; // r15
  __CFString *v3; // r13
  NSDictionary *v4; // rax
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  NSDictionary *v16; // [rsp+0h] [rbp-80h]
  _QWORD v17[2]; // [rsp+10h] [rbp-70h] BYREF
  _QWORD v18[2]; // [rsp+20h] [rbp-60h] BYREF
  _QWORD v19[2]; // [rsp+30h] [rbp-50h] BYREF
  _QWORD v20[2]; // [rsp+40h] [rbp-40h] BYREF

  v2 = off_1012E2B50;
  v19[0] = off_1012E2B50;
  v20[0] = CFSTR("883900");
  v3 = off_1012E2B58;
  v19[1] = off_1012E2B58;
  v20[1] = CFSTR("URFI");
  v4 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v20, v19, 2LL);
  v16 = objc_retainAutoreleasedReturnValue(v4);
  v6 = v5(self, "SGRequestShouYi");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "deleteOrder:", v16);
  v17[0] = v2;
  v18[0] = CFSTR("1A0001");
  v17[1] = v3;
  v18[1] = CFSTR("USHI");
  v10 = v9(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v18, v17, 2LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(self, "SGRequestShangZheng");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15(v14, "deleteOrder:", v11);
}

//----- (0000000100008111) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController setSGRequestShouYi:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._plotItem, a3);
}

//----- (0000000100008125) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController setSGRequestShangZheng:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._drawContentView, a3);
}

//----- (0000000100008139) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController setSGParserForShangZheng:](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._infoBtn, a3);
}

//----- (000000010000814D) ----------------------------------------------------
void __cdecl -[ZuoRiZhangTingJinBiaoXianGraphViewController .cxx_destruct](
        ZuoRiZhangTingJinBiaoXianGraphViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super._infoBtn, 0LL);
  objc_storeStrong((id *)&self->super._drawContentView, 0LL);
  objc_storeStrong((id *)&self->super._plotItem, 0LL);
}

