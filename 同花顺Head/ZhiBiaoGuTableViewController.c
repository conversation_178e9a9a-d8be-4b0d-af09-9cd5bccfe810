HXTableRequestModule *__cdecl -[ZhiBiaoGuTableViewController codeListSortRequestModule](
        ZhiBiaoGuTableViewController *self,
        SEL a2)
{

  allCodesNum = (void *)self->super._allCodesNum;
  if ( !allCodesNum )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = (void *)self->super._allCodesNum;
    self->super._allCodesNum = (unsigned __int64)v5;
    allCodesNum = (void *)self->super._allCodesNum;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(allCodesNum);
}

//----- (00000001002DE874) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ZhiBiaoGuTableViewController backgroundCodeListSortRequestModule](
        ZhiBiaoGuTableViewController *self,
        SEL a2)
{

  frozenCount = (void *)self->super._frozenCount;
  if ( !frozenCount )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = (void *)self->super._frozenCount;
    self->super._frozenCount = (signed __int64)v5;
    frozenCount = (void *)self->super._frozenCount;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(frozenCount);
}

//----- (00000001002DE8C5) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ZhiBiaoGuTableViewController backgroundDetailDataRequestModule](
        ZhiBiaoGuTableViewController *self,
        SEL a2)
{
  NSArray *orderHQDataTypes; // rdi
  NSArray *v5; // rax
  NSArray *v6; // rdi

  orderHQDataTypes = self->super._orderHQDataTypes;
  if ( !orderHQDataTypes )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = (NSArray *)_objc_msgSend(v4, "init");
    v6 = self->super._orderHQDataTypes;
    self->super._orderHQDataTypes = v5;
    orderHQDataTypes = self->super._orderHQDataTypes;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(orderHQDataTypes);
}

//----- (00000001002DE916) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController initObjects](ZhiBiaoGuTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhiBiaoGuTableViewController;
  -[QuoteBaseTableViewController initObjects](&v2, "initObjects");
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 328451LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("ZhiBiaoStock"));
  -[QuoteBaseTableViewController setTextMarkState](self, "setTextMarkState");
}

//----- (00000001002DE985) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController requestForMyTable](ZhiBiaoGuTableViewController *self, SEL a2)
{
  -[QuoteBaseTableViewController deleteOrder](self, "deleteOrder");
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    -[QuoteBaseTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
    if ( (unsigned __int8)-[QuoteBaseTableViewController requestFromZero](self, "requestFromZero") )
      -[ZhiBiaoGuTableViewController requestBackgroundCodeListSort](self, "requestBackgroundCodeListSort");
    -[ZhiBiaoGuTableViewController requestCodeListSort](self, "requestCodeListSort");
  }
}

//----- (00000001002DE9F8) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController setOriginalSortInfoForMyFrozenTable](
        ZhiBiaoGuTableViewController *self,
        SEL a2)
{
  NSString *v5; // rax
  NSSortDescriptor *v6; // rax
  NSSortDescriptor *v7; // r15
  NSArray *v8; // rax
  NSArray *v9; // r13
  NSString *v15; // [rsp+0h] [rbp-40h]
  NSSortDescriptor *v16; // [rsp+8h] [rbp-38h] BYREF

  -[HXBaseTableViewController setSortID:](self, "setSortID:", 199112LL);
  _objc_msgSend(v2, "setIsSetSortInfoByCode:", 1LL);
  v4 = _objc_msgSend(v3, "sortID");
  v5 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v4);
  v15 = objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(&OBJC_CLASS___NSSortDescriptor, "sortDescriptorWithKey:ascending:", v15, 0LL);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v16 = v7;
  v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v16, 1LL);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v10, "myFrozenTable");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setSortDescriptors:", v9);
  _objc_msgSend(v13, "setSortOrder:", CFSTR("D"));
  _objc_msgSend(v14, "setWenCaiSortIdentifier:", 0LL);
}

//----- (00000001002DEB57) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController requestBackgroundCodeListSort](ZhiBiaoGuTableViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v9; // rax
  NSNumber *v11; // rax
  NSNumber *v13; // rax
  NSNumber *v15; // rax
  NSNumber *v17; // rax
  NSNumber *v19; // rax
  NSNumber *v21; // rax
  NSNumber *v23; // rax
  NSNumber *v25; // rax
  NSNumber *v27; // rax
  NSNumber *v29; // rax
  NSNumber *v31; // rax
  NSNumber *v33; // rax
  NSNumber *v35; // rax
  NSNumber *v37; // rax
  NSNumber *v39; // rax
  NSNumber *v41; // rax
  NSNumber *v42; // r14
  NSDictionary *v44; // rax
  NSDictionary *v45; // r15
  __CFString *v76; // rax
  __CFString *v77; // rbx
  __CFString *v78; // rdx
  NSNumber *v79; // rax
  NSNumber *v80; // rax
  NSNumber *v84; // rax
  NSNumber *v85; // rbx
  NSDictionary *v86; // rax
  NSDictionary *v87; // r13
  _QWORD v97[4]; // [rsp+0h] [rbp-2B0h] BYREF
  id to; // [rsp+20h] [rbp-290h] BYREF
  objc_class *v100; // [rsp+30h] [rbp-280h]
  id location; // [rsp+B8h] [rbp-1F8h] BYREF
  _QWORD v124[42]; // [rsp+130h] [rbp-180h] BYREF

  val = self;
  v100 = &OBJC_CLASS___NSMutableArray;
  v124[0] = CFSTR("USHI1B0300");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 61322LL);
  v118 = objc_retainAutoreleasedReturnValue(v2);
  v124[21] = v118;
  v124[1] = CFSTR("USZI399300");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 61322LL);
  v121 = objc_retainAutoreleasedReturnValue(v3);
  *(_QWORD *)(v4 + 8) = v121;
  v124[2] = CFSTR("USZI399330");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 205LL);
  v119 = objc_retainAutoreleasedReturnValue(v5);
  *(_QWORD *)(v6 + 16) = v119;
  v124[3] = CFSTR("USHI1B0007");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 204LL);
  v120 = objc_retainAutoreleasedReturnValue(v7);
  *(_QWORD *)(v8 + 24) = v120;
  v124[4] = CFSTR("USHI1B0016");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 203LL);
  v101 = objc_retainAutoreleasedReturnValue(v9);
  *(_QWORD *)(v10 + 32) = v101;
  v124[5] = CFSTR("USZI399005");
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52791LL);
  v102 = objc_retainAutoreleasedReturnValue(v11);
  *(_QWORD *)(v12 + 40) = v102;
  v124[6] = CFSTR("USHI1B0043");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52858LL);
  v103 = objc_retainAutoreleasedReturnValue(v13);
  *(_QWORD *)(v14 + 48) = v103;
  v124[7] = CFSTR("USHI1B0044");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52857LL);
  v104 = objc_retainAutoreleasedReturnValue(v15);
  *(_QWORD *)(v16 + 56) = v104;
  v124[8] = CFSTR("USHI1B0045");
  v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52854LL);
  v105 = objc_retainAutoreleasedReturnValue(v17);
  *(_QWORD *)(v18 + 64) = v105;
  v124[9] = CFSTR("USHI1B0046");
  v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52856LL);
  v106 = objc_retainAutoreleasedReturnValue(v19);
  *(_QWORD *)(v20 + 72) = v106;
  v124[10] = CFSTR("USHI1B0047");
  v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52853LL);
  v107 = objc_retainAutoreleasedReturnValue(v21);
  *(_QWORD *)(v22 + 80) = v107;
  v124[11] = CFSTR("USHI1B0061");
  v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52852LL);
  v108 = objc_retainAutoreleasedReturnValue(v23);
  *(_QWORD *)(v24 + 88) = v108;
  v124[12] = CFSTR("USHI1B0006");
  v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52851LL);
  v109 = objc_retainAutoreleasedReturnValue(v25);
  *(_QWORD *)(v26 + 96) = v109;
  v124[13] = CFSTR("USHI1B0001");
  v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52850LL);
  v110 = objc_retainAutoreleasedReturnValue(v27);
  *(_QWORD *)(v28 + 104) = v110;
  v124[14] = CFSTR("USHI1B0002");
  v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52849LL);
  v111 = objc_retainAutoreleasedReturnValue(v29);
  *(_QWORD *)(v30 + 112) = v111;
  v124[15] = CFSTR("USHI1B0004");
  v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52848LL);
  v112 = objc_retainAutoreleasedReturnValue(v31);
  *(_QWORD *)(v32 + 120) = v112;
  v124[16] = CFSTR("USHI1B0005");
  v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52847LL);
  v113 = objc_retainAutoreleasedReturnValue(v33);
  *(_QWORD *)(v34 + 128) = v113;
  v124[17] = CFSTR("USHI1B0020");
  v35 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52846LL);
  v114 = objc_retainAutoreleasedReturnValue(v35);
  *(_QWORD *)(v36 + 136) = v114;
  v124[18] = CFSTR("USHI1B0015");
  v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52845LL);
  v115 = objc_retainAutoreleasedReturnValue(v37);
  *(_QWORD *)(v38 + 144) = v115;
  v124[19] = CFSTR("USHI1B0905");
  v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55442LL);
  v116 = objc_retainAutoreleasedReturnValue(v39);
  *(_QWORD *)(v40 + 152) = v116;
  v124[20] = CFSTR("USHI1B0688");
  v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52134LL);
  v42 = objc_retainAutoreleasedReturnValue(v41);
  *(_QWORD *)(v43 + 160) = v42;
  v44 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v43, v124, 21LL);
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = _objc_msgSend(v45, "allKeys");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v48 = _objc_msgSend(v100, "arrayWithArray:", v47);
  v49 = objc_retainAutoreleasedReturnValue(v48);
  v50(v45);
  v51(v42);
  v52(v116);
  v53(v115);
  v54(v114);
  v55(v113);
  v56(v112);
  v57(v111);
  v58(v110);
  v59(v109);
  v60(v108);
  v61(v107);
  v62(v106);
  v63(v105);
  v64(v104);
  v65(v103);
  v66(v102);
  v67(v101);
  v68(v120);
  v69(v119);
  v70(v121);
  v71(v118);
  _objc_msgSend(v49, "removeObject:", CFSTR("USZI399300"));
  v118 = v49;
  v72 = _objc_msgSend(val, "getCodeListStrWithCodeAndMarketArr:", v49);
  v73 = objc_retainAutoreleasedReturnValue(v72);
  if ( _objc_msgSend(v73, "length") )
  {
    v121 = v73;
    objc_initWeak(&location, val);
    v75 = _objc_msgSend(val, "sortOrder");
    v76 = (__CFString *)objc_retainAutoreleasedReturnValue(v75);
    v77 = v76;
    v78 = CFSTR("D");
    if ( v76 )
      v78 = v76;
    _objc_msgSend(val, "setSortOrder:", v78);
    v122[0] = (__int64)CFSTR("sortbegin");
    v79 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v119 = objc_retainAutoreleasedReturnValue(v79);
    v123[0] = (__int64)v119;
    v122[1] = (__int64)CFSTR("sortcount");
    v80 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 0LL);
    v120 = objc_retain(v80);
    v123[1] = (__int64)v120;
    v122[2] = (__int64)CFSTR("sortorder");
    v81 = _objc_msgSend(val, "sortOrder");
    v82 = objc_retain(v81);
    v123[2] = (__int64)v82;
    v122[3] = (__int64)CFSTR("sortid");
    v83 = _objc_msgSend(val, "sortID");
    v84 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v83);
    v85 = objc_retainAutoreleasedReturnValue(v84);
    v123[3] = (__int64)v85;
    v122[4] = (__int64)CFSTR("blockcodelist");
    v123[4] = (__int64)v121;
    v86 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v123, v122, 5LL);
    v87 = objc_retainAutoreleasedReturnValue(v86);
    v88(v85);
    v89(v82);
    v90(v120);
    v91(v119);
    v92 = _objc_msgSend(val, "backgroundCodeListSortRequestModule");
    v93 = objc_retain(v92);
    v97[0] = _NSConcreteStackBlock;
    v97[1] = 3254779904LL;
    v97[2] = sub_1002DF3F5;
    v97[3] = &unk_1012DAED8;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v93, "request:params:callBack:", 9LL, v87, v97);
    v94(v93);
    objc_destroyWeak(&to);
    v95(v87);
    objc_destroyWeak(&location);
    v73 = v121;
  }
  v74(v73);
  v96(v118);
}

//----- (00000001002DF3F5) ----------------------------------------------------
void __fastcall sub_1002DF3F5(__int64 a1, void *a2)
{
  id WeakRetained; // r15

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "getCodeAndMarketArrWithResponse:", v2);
  objc_retainAutoreleasedReturnValue(v4);
  v5 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v5, "requestBackgroundDetailDataWithCodeList:", v6);
}

//----- (00000001002DF48C) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController requestBackgroundDetailDataWithCodeList:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  NSArray *v9; // rax
  NSDictionary *v10; // rax
  NSDictionary *v11; // r15
  HXTableRequestModule *v13; // rax
  _QWORD v16[4]; // [rsp+0h] [rbp-80h] BYREF
  id to; // [rsp+20h] [rbp-60h] BYREF
  id location; // [rsp+28h] [rbp-58h] BYREF
  _QWORD v20[2]; // [rsp+40h] [rbp-40h] BYREF

  v3 = objc_retain(a3);
  v4 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( _objc_msgSend(v5, "count") )
  {
    _objc_msgSend(v3, v6);
    v7 = v3;
    if ( v8 )
    {
      objc_initWeak(&location, self);
      v19[0] = (__int64)CFSTR("datatype");
      v9 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
      v20[0] = objc_retainAutoreleasedReturnValue(v9);
      v19[1] = (__int64)CFSTR("StockCodesAndMarket");
      v20[1] = v3;
      v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v20, v19, 2LL);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = -[ZhiBiaoGuTableViewController backgroundDetailDataRequestModule](self, "backgroundDetailDataRequestModule");
      objc_retain(v13);
      v16[0] = _NSConcreteStackBlock;
      v16[1] = 3254779904LL;
      v16[2] = sub_1002DF688;
      v16[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      _objc_msgSend(v14, "request:params:callBack:", 4LL, v11, v16);
      objc_destroyWeak(&to);
      objc_destroyWeak(&location);
    }
  }
  else
  {
    v7 = v3;
  }
}

//----- (00000001002DF688) ----------------------------------------------------
__int64 __fastcall sub_1002DF688(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  id (*v4)(id, SEL, ...); // r12

  v2 = _objc_msgSend(a2, "copy");
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v5 = v4(WeakRetained, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setBackgroundData:", v2);
  v8(WeakRetained);
  return v9(v2);
}

//----- (00000001002DF70A) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController requestCodeListSort](ZhiBiaoGuTableViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v10; // rax
  NSNumber *v11; // rax
  NSNumber *v12; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // rax
  NSNumber *v15; // rax
  NSNumber *v16; // rax
  NSNumber *v17; // rax
  NSNumber *v18; // rax
  NSNumber *v19; // rax
  NSNumber *v20; // rax
  NSNumber *v21; // rax
  NSNumber *v22; // rax
  NSNumber *v23; // r13
  NSDictionary *v24; // rax
  NSDictionary *v25; // r14
  __CFString *v37; // rax
  __CFString *v38; // rbx
  __CFString *v39; // rdx
  NSNumber *v41; // rax
  NSNumber *v43; // rax
  SEL v44; // r12
  NSNumber *v48; // rax
  NSNumber *v49; // rbx
  NSDictionary *v50; // rax
  _QWORD v55[4]; // [rsp+8h] [rbp-2A8h] BYREF
  id to; // [rsp+28h] [rbp-288h] BYREF
  id location; // [rsp+B8h] [rbp-1F8h] BYREF
  _QWORD v81[21]; // [rsp+130h] [rbp-180h] BYREF
  _QWORD v82[21]; // [rsp+1D8h] [rbp-D8h] BYREF

  val = self;
  v81[0] = CFSTR("USHI1B0300");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 61322LL);
  v75 = objc_retainAutoreleasedReturnValue(v2);
  v82[0] = v75;
  v81[1] = CFSTR("USZI399300");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 61322LL);
  v78 = objc_retainAutoreleasedReturnValue(v3);
  v82[1] = v78;
  v81[2] = CFSTR("USZI399330");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 205LL);
  v76 = objc_retainAutoreleasedReturnValue(v4);
  v82[2] = v76;
  v81[3] = CFSTR("USHI1B0007");
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 204LL);
  v77 = objc_retainAutoreleasedReturnValue(v5);
  v82[3] = v77;
  v81[4] = CFSTR("USHI1B0016");
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 203LL);
  v58 = objc_retainAutoreleasedReturnValue(v6);
  v82[4] = v58;
  v81[5] = CFSTR("USZI399005");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52791LL);
  v59 = objc_retainAutoreleasedReturnValue(v7);
  v82[5] = v59;
  v81[6] = CFSTR("USHI1B0043");
  v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52858LL);
  v60 = objc_retainAutoreleasedReturnValue(v8);
  v82[6] = v60;
  v81[7] = CFSTR("USHI1B0044");
  v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52857LL);
  v61 = objc_retainAutoreleasedReturnValue(v9);
  v82[7] = v61;
  v81[8] = CFSTR("USHI1B0045");
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52854LL);
  v62 = objc_retainAutoreleasedReturnValue(v10);
  v82[8] = v62;
  v81[9] = CFSTR("USHI1B0046");
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52856LL);
  v63 = objc_retainAutoreleasedReturnValue(v11);
  v82[9] = v63;
  v81[10] = CFSTR("USHI1B0047");
  v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52853LL);
  v64 = objc_retainAutoreleasedReturnValue(v12);
  v82[10] = v64;
  v81[11] = CFSTR("USHI1B0061");
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52852LL);
  v65 = objc_retainAutoreleasedReturnValue(v13);
  v82[11] = v65;
  v81[12] = CFSTR("USHI1B0006");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52851LL);
  v66 = objc_retainAutoreleasedReturnValue(v14);
  v82[12] = v66;
  v81[13] = CFSTR("USHI1B0001");
  v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52850LL);
  v67 = objc_retainAutoreleasedReturnValue(v15);
  v82[13] = v67;
  v81[14] = CFSTR("USHI1B0002");
  v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52849LL);
  v68 = objc_retainAutoreleasedReturnValue(v16);
  v82[14] = v68;
  v81[15] = CFSTR("USHI1B0004");
  v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52848LL);
  v69 = objc_retainAutoreleasedReturnValue(v17);
  v82[15] = v69;
  v81[16] = CFSTR("USHI1B0005");
  v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52847LL);
  v70 = objc_retainAutoreleasedReturnValue(v18);
  v82[16] = v70;
  v81[17] = CFSTR("USHI1B0020");
  v19 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52846LL);
  v71 = objc_retainAutoreleasedReturnValue(v19);
  v82[17] = v71;
  v81[18] = CFSTR("USHI1B0015");
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52845LL);
  v72 = objc_retainAutoreleasedReturnValue(v20);
  v82[18] = v72;
  v81[19] = CFSTR("USHI1B0905");
  v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55442LL);
  v73 = objc_retainAutoreleasedReturnValue(v21);
  v82[19] = v73;
  v81[20] = CFSTR("USHI1B0688");
  v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 52134LL);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v82[20] = v23;
  v24 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v82, v81, 21LL);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "allKeys");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v29 = _objc_msgSend(v28, "arrayWithArray:", v27);
  objc_retainAutoreleasedReturnValue(v29);
  _objc_msgSend(v30, "removeObject:", CFSTR("USZI399300"));
  v32 = _objc_msgSend(v31, "count");
  -[QuoteBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v32);
  v75 = v33;
  v34 = -[ZhiBiaoGuTableViewController getCodeListStrWithCodeAndMarketArr:](
          self,
          "getCodeListStrWithCodeAndMarketArr:",
          v33);
  v35 = objc_retainAutoreleasedReturnValue(v34);
  if ( _objc_msgSend(v35, "length") )
  {
    v78 = v35;
    objc_initWeak(&location, val);
    v36 = _objc_msgSend(val, "sortOrder");
    v37 = (__CFString *)objc_retainAutoreleasedReturnValue(v36);
    v38 = v37;
    v39 = CFSTR("D");
    if ( v37 )
      v39 = v37;
    _objc_msgSend(val, "setSortOrder:", v39);
    v79[0] = (__int64)CFSTR("sortbegin");
    v40 = _objc_msgSend(val, "begin");
    v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v40);
    v76 = objc_retainAutoreleasedReturnValue(v41);
    v80[0] = (__int64)v76;
    v79[1] = (__int64)CFSTR("sortcount");
    v42 = _objc_msgSend(val, "count");
    v43 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v42);
    v77 = objc_retainAutoreleasedReturnValue(v43);
    v80[1] = (__int64)v77;
    v79[2] = (__int64)CFSTR("sortorder");
    v45 = _objc_msgSend(val, v44);
    v46 = objc_retain(v45);
    v80[2] = (__int64)v46;
    v79[3] = (__int64)CFSTR("sortid");
    v47 = _objc_msgSend(val, "sortID");
    v48 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v47);
    v49 = objc_retainAutoreleasedReturnValue(v48);
    v80[3] = (__int64)v49;
    v79[4] = (__int64)CFSTR("blockcodelist");
    v80[4] = (__int64)v78;
    v50 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v80, v79, 5LL);
    objc_retainAutoreleasedReturnValue(v50);
    v51 = _objc_msgSend(val, "codeListSortRequestModule");
    v52 = objc_retain(v51);
    v55[0] = _NSConcreteStackBlock;
    v55[1] = 3254779904LL;
    v55[2] = sub_1002DFFCA;
    v55[3] = &unk_1012DAED8;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v52, "request:params:callBack:", 9LL, v53, v55);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
    v35 = v78;
  }
}

//----- (00000001002DFFCA) ----------------------------------------------------
void __fastcall sub_1002DFFCA(__int64 a1, void *a2)
{
  id WeakRetained; // [rsp+0h] [rbp-30h]

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "getCodeAndMarketArrWithResponse:", v2);
  objc_retainAutoreleasedReturnValue(v3);
  v4 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v4, "setOrderCodeMArray:", v5);
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v7, "requestDetailData");
}

//----- (00000001002E0085) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController getCodeListStrWithCodeAndMarketArr:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  id (**v3)(id, SEL, ...); // r14
  unsigned __int64 v8; // r13
  id (**v16)(id, SEL, ...); // rbx
  __CFString *v24; // rcx
  id (*v28)(id, SEL, ...); // r12
  SEL v31; // rbx
  id (*v32)(id, SEL, ...); // r12
  SEL v36; // rbx
  id (*v38)(id, SEL, ...); // r12
  __CFString *v40; // r12
  __CFString *v42; // r12
  SEL v54; // [rsp+68h] [rbp-F8h]
  SEL v55; // [rsp+70h] [rbp-F0h]
  SEL v57; // [rsp+80h] [rbp-E0h]
  SEL v58; // [rsp+88h] [rbp-D8h]
  SEL v59; // [rsp+90h] [rbp-D0h]
  id obj; // [rsp+A8h] [rbp-B8h]

  objc_retain(a3);
  v3 = &_objc_msgSend;
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v61 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  v52 = v7;
  if ( v6 )
  {
    v58 = "getCodeString:";
    v54 = "getMarketString:";
    v57 = "length";
    v55 = "thsArrayForKey:";
    v56 = "arrayWithArray:";
    obj = "containsObject:";
    v51 = "addObject:";
    v50 = "setObject:forKey:";
    v8 = 0LL;
    do
    {
      v9 = ((id (*)(id, SEL, ...))v3)(v7, "thsStringAtIndex:", v8);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = ((id (*)(id, SEL, ...))v3)(&OBJC_CLASS___HXTools, v58, v10);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v53 = v10;
      v13 = ((id (*)(id, SEL, ...))v3)(&OBJC_CLASS___HXTools, v54, v10);
      v14 = (char *)objc_retainAutoreleasedReturnValue(v13);
      v60 = v12;
      if ( ((id (*)(id, SEL, ...))v3)(v12, v57) && ((id (*)(id, SEL, ...))v3)(v14, v57) )
      {
        v15 = v14;
        v16 = v3;
        v59 = v15;
        v17 = ((id (*)(id, SEL, ...))v3)(v61, v55);
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v19 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, id))v16)(
                        &OBJC_CLASS___NSMutableArray,
                        v56,
                        v18);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        if ( !((unsigned __int8 (__fastcall *)(id, id, id))v16)(v20, obj, v60) )
          ((void (__fastcall *)(id, const char *, id))v16)(v20, v51, v60);
        if ( ((__int64 (__fastcall *)(id, const char *))v16)(v20, "count") )
          ((void (__fastcall *)(id, const char *, id, SEL))v16)(v61, v50, v20, v59);
        v3 = v16;
        v14 = (char *)v59;
      }
      ++v8;
    }
    while ( (unsigned __int64)((id (*)(id, SEL, ...))v3)(v21, "count") > v8 );
  }
  v49 = 0LL;
  v48 = 0LL;
  v47 = 0LL;
  v46 = 0LL;
  v22 = ((id (*)(id, SEL, ...))v3)(v61, "allKeys");
  obj = objc_retainAutoreleasedReturnValue(v22);
  v58 = (SEL)((id (*)(id, SEL, ...))v3)(obj, "countByEnumeratingWithState:objects:count:", &v46, v63, 16LL);
  if ( v58 )
  {
    v56 = *(const char **)v47;
    v24 = &charsToLeaveEscaped;
    do
    {
      v54 = "thsArrayForKey:";
      v59 = "componentsJoinedByString:";
      v55 = "stringWithFormat:";
      v25 = 0LL;
      do
      {
        v60 = v24;
        if ( *(const char **)v47 != v56 )
          objc_enumerationMutation(obj);
        v26 = *(_QWORD *)(*((_QWORD *)&v46 + 1) + 8LL * (_QWORD)v25);
        v27 = _objc_msgSend(v61, v54, v26);
        v53 = objc_retainAutoreleasedReturnValue(v27);
        v57 = v25;
        v29 = v28(v53, v59, CFSTR(","));
        v30 = objc_retainAutoreleasedReturnValue(v29);
        v31 = v55;
        v33 = v32(&OBJC_CLASS___NSString, v55, CFSTR("%@(%@,);"), v26, v30);
        v34 = objc_retainAutoreleasedReturnValue(v33);
        v35 = v31;
        v36 = v57;
        v37 = v60;
        v39 = v38(&OBJC_CLASS___NSString, v35, CFSTR("%@%@"), v60, v34);
        objc_retainAutoreleasedReturnValue(v39);
        v25 = v36 + 1;
        v24 = v40;
      }
      while ( v25 < v58 );
      v41 = (const char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v46, v63, 16LL);
      v24 = v42;
      v58 = v41;
    }
    while ( v41 );
    v43 = v52;
  }
  else
  {
    v43 = v23;
  }
  return objc_autoreleaseReturnValue(v44);
}

//----- (00000001002E0565) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController getCodeAndMarketArrWithResponse:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v4; // r14
  NSNumber *v6; // rax
  NSNumber *v7; // rbx
  NSDictionary *v16; // rax
  NSDictionary *v17; // rbx

  v21 = objc_retain(a3);
  if ( _objc_msgSend(v21, "count") )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v20 = objc_retainAutoreleasedReturnValue(v3);
    if ( _objc_msgSend(v21, "count") )
    {
      v4 = 0LL;
      do
      {
        v5 = _objc_msgSend(v21, "thsDictionaryAtIndex:", v4);
        objc_retainAutoreleasedReturnValue(v5);
        v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
        v7 = objc_retainAutoreleasedReturnValue(v6);
        v9 = _objc_msgSend(v8, "thsStringForKey:", v7);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        if ( _objc_msgSend(v10, "length") )
        {
          v12 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v10);
          objc_retainAutoreleasedReturnValue(v12);
          v13 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v10);
          objc_retainAutoreleasedReturnValue(v13);
          v19 = v14;
          if ( _objc_msgSend(v14, "length") && _objc_msgSend(v15, "length") )
          {
            v22[0] = (__int64)CFSTR("StockCode");
            v23[0] = (__int64)v19;
            v22[1] = (__int64)CFSTR("Market");
            v23[1] = (__int64)v15;
            v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v23, v22, 2LL);
            v17 = objc_retainAutoreleasedReturnValue(v16);
            _objc_msgSend(v20, "addObject:", v17);
          }
        }
        ++v4;
      }
      while ( (unsigned __int64)_objc_msgSend(v21, "count") > v4 );
    }
  }
  else
  {
    v20 = 0LL;
  }
  return objc_autoreleaseReturnValue(v20);
}

//----- (00000001002E07EB) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController requestDetailData](ZhiBiaoGuTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  HXStockModel *v5; // rax
  HXStockModel *v6; // rbx
  SEL v7; // r12
  NSMutableArray *v13; // rax
  NSMutableArray *v14; // r15
  NSDictionary *v18; // rax
  HXTableRequestModule *v19; // rax
  HXTableRequestModule *v20; // r13
  _QWORD v23[4]; // [rsp+8h] [rbp-88h] BYREF
  id to; // [rsp+28h] [rbp-68h] BYREF
  id location; // [rsp+38h] [rbp-58h] BYREF
  _QWORD v27[2]; // [rsp+40h] [rbp-50h] BYREF
  _QWORD v28[2]; // [rsp+50h] [rbp-40h] BYREF

  v2 = -[QuoteBaseTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
  {
    v5 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[HXStockModel setMainRequestDidBack:](v6, "setMainRequestDidBack:", 0LL);
    objc_initWeak(&location, self);
    v8 = _objc_msgSend(self, v7);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = _objc_msgSend(v10, "arrayWithArray:", v9);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v16 = _objc_msgSend(v15, "arrayWithArray:", v14);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v27[0] = CFSTR("datatype");
    v28[0] = v12;
    v27[1] = CFSTR("StockCodesAndMarket");
    v25 = v17;
    v28[1] = v17;
    v18 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v28, v27, 2LL);
    objc_retainAutoreleasedReturnValue(v18);
    v19 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
    v20 = objc_retain(v19);
    v23[0] = _NSConcreteStackBlock;
    v23[1] = 3254779904LL;
    v23[2] = sub_1002E0A54;
    v23[3] = &unk_1012DAED8;
    objc_copyWeak(&to, &location);
    -[HXTableRequestModule request:params:callBack:](v20, "request:params:callBack:", 4LL, v21, v23);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
  }
}

//----- (00000001002E0A54) ----------------------------------------------------
void __fastcall sub_1002E0A54(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // rax

  v9 = objc_retain(a3);
  v10 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setMainRequestDidBack:", 1LL);
  v7 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v7, "dealWithRequestData:extension:", v10, v9);
}

//----- (00000001002E0B1F) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController dealWithRequestData:extension:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // r13
  HXStockModel *v11; // rax
  HXStockModel *v12; // rbx

  objc_retain(a3);
  v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = +[HXTools getCompleteDatasWith:and:](&OBJC_CLASS___HXTools, "getCompleteDatasWith:and:", v5, v6);
  v23 = objc_retainAutoreleasedReturnValue(v7);
  v9 = -[ZhiBiaoGuTableViewController calculateItemsWithRequestData:](self, "calculateItemsWithRequestData:", v23);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = (HXStockModel *)-[HXBaseTableViewController stockModel](self, "stockModel");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  -[HXStockModel setMainStockTableViewDataArray:](v12, "setMainStockTableViewDataArray:", v10);
  v14 = v13;
  v15 = _objc_msgSend(self, v13);
  v24 = objc_retainAutoreleasedReturnValue(v15);
  v16 = _objc_msgSend(v24, "mainStockTableViewDataArray");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = -[QuoteBaseTableViewController sortVisiblePart:](self, "sortVisiblePart:", v17);
  objc_retainAutoreleasedReturnValue(v18);
  v19 = _objc_msgSend(self, v14);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  _objc_msgSend(v20, "setMainStockTableViewDataArray:", v21);
  -[QuoteBaseTableViewController reloadData:](self, "reloadData:", 0LL);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  -[QuoteBaseTableViewController setRequestFromZero:](self, "setRequestFromZero:", 0LL);
  -[QuoteBaseTableViewController setOrderCodeList](self, "setOrderCodeList");
  -[QuoteBaseTableViewController order](self, "order");
}

//----- (00000001002E0CF1) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController calculateItemsWithRequestData:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  id (**v4)(id, SEL, ...); // r14
  unsigned __int64 v5; // r15
  SEL v14; // r12
  id (**v18)(id, SEL, ...); // r13
  unsigned __int64 v43; // [rsp+8h] [rbp-A8h]

  v3 = objc_retain(a3);
  v4 = &_objc_msgSend;
  v47 = _objc_msgSend(v3, "mutableCopy");
  v48 = v3;
  if ( _objc_msgSend(v3, "count") )
  {
    v5 = 0LL;
    do
    {
      v6 = ((id (*)(id, SEL, ...))v4)(v48, "thsDictionaryAtIndex:", v5);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      if ( ((id (*)(id, SEL, ...))v4)(v7, "count") )
      {
        v43 = v5;
        v9 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v10 = objc_retainAutoreleasedReturnValue(v9);
        v11 = ((id (*)(id, SEL, ...))v4)(v7, "thsNumberForKey:", v10);
        v46 = objc_retainAutoreleasedReturnValue(v11);
        v12 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v15 = ((id (*)(id, SEL, ...))v4)(v7, v14, v13);
        objc_retainAutoreleasedReturnValue(v15);
        v16 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, "class");
        v17 = (unsigned __int8)((id (*)(id, SEL, ...))v4)(v46, "isKindOfClass:", v16);
        v18 = v4;
        v52 = v7;
        v44 = v19;
        if ( v17 )
        {
          if ( ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 4294967295.0
            || ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 2147483648.0
            || ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue") == 0.0
            || (v33 = ((__int64 (__fastcall *)(void *, const char *))v4)(&OBJC_CLASS___NSNumber, "class"),
                !((unsigned __int8 (__fastcall *)(__int64, const char *, __int64))v4)(v34, "isKindOfClass:", v33))
            || ((double (__fastcall *)(__int64, const char *))v4)(v35, "doubleValue") == 4294967295.0
            || ((double (__fastcall *)(__int64, const char *))v4)(v36, "doubleValue") == 2147483648.0
            || ((double (__fastcall *)(__int64, const char *))v4)(v37, "doubleValue") == 0.0 )
          {
            v20 = 0LL;
          }
          else
          {
            v50 = ((double (__fastcall *)(id, const char *))v4)(v46, "doubleValue");
            v51 = v50 - ((double (__fastcall *)(__int64, const char *))v4)(v38, "doubleValue");
            v45 = v51 / ((double (__fastcall *)(__int64, const char *))v4)(v39, "doubleValue");
            v40 = (void *)((__int64 (__fastcall *)(void *, const char *, double))v4)(
                            &OBJC_CLASS___NSNumber,
                            "numberWithDouble:",
                            v51);
            v20 = objc_retainAutoreleasedReturnValue(v40);
            v41 = (void *)((__int64 (__fastcall *)(void *, const char *, double))v18)(
                            &OBJC_CLASS___NSNumber,
                            "numberWithDouble:",
                            v45 * 100.0);
            objc_retainAutoreleasedReturnValue(v41);
          }
        }
        else
        {
          v20 = 0LL;
        }
        v21 = ((__int64 (__fastcall *)(id, const char *))v18)(v7, "mutableCopy");
        v22 = ((__int64 (__fastcall *)(void *, const char *))v18)(&OBJC_CLASS___NSNumber, "class");
        if ( ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v18)(v20, "isKindOfClass:", v22)
          && ((double (__fastcall *)(id, const char *))v18)(v20, "doubleValue") != 4294967295.0
          && ((double (__fastcall *)(id, const char *))v18)(v20, "doubleValue") != 2147483648.0 )
        {
          v23 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                          &OBJC_CLASS___NSNumber,
                          "numberWithInt:",
                          264648LL);
          v24 = objc_retainAutoreleasedReturnValue(v23);
          ((void (__fastcall *)(__int64, const char *, id, id))v18)(v21, "setObject:forKey:", v20, v24);
        }
        v25 = ((__int64 (__fastcall *)(void *, const char *))v18)(&OBJC_CLASS___NSNumber, "class");
        if ( ((unsigned __int8 (__fastcall *)(__int64, const char *, __int64))v18)(v26, "isKindOfClass:", v25)
          && ((double (__fastcall *)(__int64, const char *))v18)(v27, "doubleValue") != 4294967295.0
          && ((double (__fastcall *)(__int64, const char *))v18)(v28, "doubleValue") != 2147483648.0 )
        {
          v29 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v18)(
                          &OBJC_CLASS___NSNumber,
                          "numberWithInt:",
                          199112LL);
          v49 = objc_retainAutoreleasedReturnValue(v29);
          ((void (__fastcall *)(__int64, const char *, __int64, id))v18)(v21, "setObject:forKey:", v30, v49);
        }
        ((void (__fastcall *)(id, const char *, __int64, unsigned __int64))v18)(
          v47,
          "setObject:atIndexedSubscript:",
          v21,
          v43);
        v31 = (void *)v21;
        v5 = v43;
        v4 = v18;
        v7 = v52;
      }
      v8(v7);
      ++v5;
    }
    while ( (unsigned __int64)((id (*)(id, SEL, ...))v4)(v48, "count") > v5 );
  }
  return objc_autoreleaseReturnValue(v47);
}

//----- (00000001002E11AE) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController myTableIsDoubleClicked:](ZhiBiaoGuTableViewController *self, SEL a2, id a3)
{
  _BYTE *v7; // r15
  NSNumber *v19; // rax
  NSNumber *v20; // r15
  __CFString *v25; // rbx
  __CFString *v26; // r14
  __CFString *v27; // rdi
  __CFString *v29; // rax
  id (__cdecl *v30)(id); // r12
  __CFString *v31; // rbx
  __CFString *v34; // rax
  __CFString *v35; // rbx
  __CFString *v36; // rdx
  __CFString *v86; // [rsp+58h] [rbp-F8h]
  _QWORD v90[22]; // [rsp+70h] [rbp-E0h] BYREF

  v76 = objc_retain(a3);
  if ( v76 )
  {
    _objc_msgSend(v3, "setRequestRowRange");
    v5 = _objc_msgSend(v4, "myFrozenTable");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "selectedRow");
    v9 = _objc_msgSend(v8, "selectedRowIndexs");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( v10 )
    {
      if ( v7 == (_BYTE *)-1LL )
        goto LABEL_15;
      v88 = (char *)(v7 - (_BYTE *)_objc_msgSend(v11, "begin"));
    }
    else
    {
      v88 = (char *)_objc_msgSend(v11, "begin");
    }
    v13 = _objc_msgSend(v12, "stockModel");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "mainStockTableViewDataArray");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17 = _objc_msgSend(v16, "thsDictionaryAtIndex:", v88);
    objc_retainAutoreleasedReturnValue(v17);
    v78 = v18;
    v19 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v77 = v21;
    v22 = _objc_msgSend(v21, "thsStringForKey:", v20);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v85 = v23;
    if ( (__int64)_objc_msgSend(v23, "length") )
    {
      v24 = _objc_msgSend(v78, "wenCaiSortIdentifier");
      v25 = (__CFString *)objc_retainAutoreleasedReturnValue(v24);
      v26 = &charsToLeaveEscaped;
      v27 = v25;
      if ( !v25 )
        v27 = &charsToLeaveEscaped;
      v86 = objc_retain(v27);
      v28 = _objc_msgSend(v78, "tableKey");
      v29 = (__CFString *)objc_retainAutoreleasedReturnValue(v28);
      v31 = v29;
      if ( v29 )
        v26 = v29;
      v32 = v30(v26);
      v79 = v32;
      v33 = _objc_msgSend(v78, "sortOrder");
      v34 = (__CFString *)objc_retainAutoreleasedReturnValue(v33);
      v35 = v34;
      v36 = CFSTR("D");
      if ( v34 )
        v36 = v34;
      _objc_msgSend(v78, "setSortOrder:", v36);
      v90[0] = CFSTR("requesttype");
      v38 = (void *)v37(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
      v80 = objc_retainAutoreleasedReturnValue(v38);
      v90[11] = v80;
      v90[1] = CFSTR("tablekey");
      v90[12] = v32;
      v90[2] = CFSTR("TableID");
      v40 = v39(v78, "tableID");
      v42 = (void *)v41(&OBJC_CLASS___NSNumber, "numberWithLong:", v40);
      v81 = objc_retainAutoreleasedReturnValue(v42);
      v90[13] = v81;
      v90[3] = CFSTR("sortid");
      v44 = v43(v78, "sortID");
      v46 = (void *)v45(&OBJC_CLASS___NSNumber, "numberWithLong:", v44);
      v82 = objc_retainAutoreleasedReturnValue(v46);
      v90[14] = v82;
      v90[4] = CFSTR("IWenCaiSortIdentifier");
      v90[15] = v86;
      v90[5] = CFSTR("sortbegin");
      v48 = v47(v78, "begin");
      v50 = v49;
      v51 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v49)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithUnsignedInteger:",
                      v48);
      v83 = objc_retainAutoreleasedReturnValue(v51);
      v90[16] = v83;
      v90[6] = CFSTR("sortcount");
      v52 = v50(v78, "count");
      v53 = (void *)((__int64 (__fastcall *)(void *, const char *, __int64))v50)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithUnsignedInteger:",
                      v52);
      v84 = objc_retainAutoreleasedReturnValue(v53);
      *(_QWORD *)(v54 + 48) = v84;
      v90[7] = CFSTR("Index");
      v55 = v50(v78, "begin");
      v56 = (void *)((__int64 (__fastcall *)(void *, const char *, char *))v50)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithUnsignedLong:",
                      &v88[v55]);
      v87 = objc_retainAutoreleasedReturnValue(v56);
      v58 = v57;
      *(_QWORD *)(v57 + 56) = v87;
      v90[8] = CFSTR("SelectedCode");
      *(_QWORD *)(v57 + 64) = v85;
      v90[9] = CFSTR("sortorder");
      v59 = (void *)v50(v78, "sortOrder");
      v60 = objc_retainAutoreleasedReturnValue(v59);
      *(_QWORD *)(v58 + 72) = v60;
      v90[10] = CFSTR("totalnumber");
      v62 = v61(v78, "allCodesNum");
      v64 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v62);
      v65 = objc_retainAutoreleasedReturnValue(v64);
      *(_QWORD *)(v58 + 80) = v65;
      v67 = (void *)v66(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v58, v90, 11LL);
      v89 = objc_retainAutoreleasedReturnValue(v67);
      v69 = (void *)v68(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v70 = objc_retainAutoreleasedReturnValue(v69);
      v71(v70, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
      v73 = (void *)v72(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v74 = objc_retainAutoreleasedReturnValue(v73);
      v75(v74, "postNotificationName:object:", CFSTR("DeliverQuotationTableDataNotification"), v89);
    }
  }
LABEL_15:
}

//----- (00000001002E180A) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController actionForTableViewSelectionDidChange:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v3; // rax
  HXStockModel *v4; // rax
  NSArray *v5; // rax
  NSArray *v6; // r14
  NSArray *v7; // rax
  NSArray *v8; // rbx
  ZhiBiaoGuTableViewController *v11; // r13
  NSNumber *v15; // rax
  NSNumber *v16; // rbx
  NSMutableArray *v23; // rax
  NSMutableArray *v24; // rbx
  NSIndexSet *v25; // rax
  NSIndexSet *v26; // r14
  _BYTE *v27; // rbx
  _BYTE *v29; // rax
  NSNumber *v32; // rax
  NSNumber *v33; // r13
  NSMutableArray *v38; // rax
  NSMutableArray *v39; // rbx
  NSIndexSet *v41; // rax
  NSIndexSet *v42; // rbx
  _BYTE *v43; // r13
  NSMutableArray *v46; // rax
  NSMutableArray *v47; // rbx
  NSMutableArray *v52; // rax
  NSMutableArray *v53; // rbx
  HXFrozenTableView *v58; // rax
  HXFrozenTableView *v59; // rbx
  HXMarkTableView *v60; // rax
  HXMarkTableView *v61; // r14
  HXMarkTableView *v63; // r12
  NSMutableArray *v66; // rax
  NSDictionary *v68; // rax
  HXFrozenTableView *v69; // rax
  HXFrozenTableView *v70; // rbx
  NSArray *v73; // [rsp+18h] [rbp-88h]
  NSMutableArray *v75; // [rsp+20h] [rbp-80h]
  _BYTE *v78; // [rsp+38h] [rbp-68h]
  _QWORD v80[3]; // [rsp+58h] [rbp-48h] BYREF

  v3 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXStockModel mainStockTableViewDataArray](v4, "mainStockTableViewDataArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithArray:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v73 = v8;
  v10 = _objc_msgSend(v8, "count");
  if ( (unsigned __int64)a3 <= 0x7FFFFFFFFFFFFFFELL && v10 )
  {
    v11 = self;
    v12 = -[QuoteBaseTableViewController begin](self, "begin");
    v13 = _objc_msgSend(v8, "thsDictionaryAtIndex:", a3 - (_QWORD)v12);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17 = _objc_msgSend(v14, "thsStringForKey:", v16);
    objc_retainAutoreleasedReturnValue(v17);
    v18 = -[ZhiBiaoGuTableViewController postZhiBiaoGuDidChangeBlock](self, "postZhiBiaoGuDidChangeBlock");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v76 = v20;
    if ( v20 && v19 )
    {
      v21 = -[ZhiBiaoGuTableViewController postZhiBiaoGuDidChangeBlock](self, "postZhiBiaoGuDidChangeBlock");
      v22 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v21);
      v22[2](v22, v76);
    }
    v23 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    _objc_msgSend(v24, "removeAllObjects");
    v25 = (NSIndexSet *)-[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27 = (_BYTE *)_objc_msgSend(v26, "firstIndex");
    v28(v26);
    while ( v27 != (_BYTE *)0x7FFFFFFFFFFFFFFFLL )
    {
      v29 = (_BYTE *)-[QuoteBaseTableViewController begin](v11, "begin");
      v78 = v27;
      v30 = _objc_msgSend(v73, "thsDictionaryAtIndex:", v27 - v29);
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v32 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v34 = _objc_msgSend(v31, "thsStringForKey:", v33);
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v36(v33);
      v37(v31);
      if ( (__int64)_objc_msgSend(v35, "length") )
      {
        v38 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](
                                  self,
                                  "selfStockCodesToBeAddMArr");
        v39 = objc_retainAutoreleasedReturnValue(v38);
        _objc_msgSend(v39, "addObject:", v35);
        v40(v39);
      }
      v41 = (NSIndexSet *)-[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v43 = (_BYTE *)_objc_msgSend(v42, "indexGreaterThanIndex:", v78);
      v44(v42);
      v27 = v43;
      v11 = self;
      v45(v35);
    }
    v46 = (NSMutableArray *)-[HXBaseTableViewController selfStockCodesToBeAddMArr](v11, "selfStockCodesToBeAddMArr");
    v47 = objc_retainAutoreleasedReturnValue(v46);
    v48 = _objc_msgSend(v47, "count");
    -[HXBaseTableViewController setIsBatchAddOperation:](v11, "setIsBatchAddOperation:", (unsigned __int64)v48 > 1);
    v49 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v76);
    v50 = objc_retainAutoreleasedReturnValue(v49);
    v51 = v50;
    if ( v76 )
    {
      if ( v50 )
      {
        v52 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
        v53 = objc_retainAutoreleasedReturnValue(v52);
        if ( v53 )
        {
          v79 = v51;
          v54 = _objc_msgSend(self, "view");
          v74 = objc_retainAutoreleasedReturnValue(v54);
          v55 = _objc_msgSend(v74, "window");
          v56 = objc_retainAutoreleasedReturnValue(v55);
          v57 = _objc_msgSend(v56, "firstResponder");
          objc_retainAutoreleasedReturnValue(v57);
          v58 = (HXFrozenTableView *)-[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
          v59 = objc_retainAutoreleasedReturnValue(v58);
          v60 = (HXMarkTableView *)-[HXFrozenTableView rightTableView](v59, "rightTableView");
          v61 = objc_retainAutoreleasedReturnValue(v60);
          if ( v63 == v61 )
          {
            v64 = +[EnvironmentVariablesManager shareInstance](
                    &OBJC_CLASS___EnvironmentVariablesManager,
                    "shareInstance");
            v65 = objc_retainAutoreleasedReturnValue(v64);
            _objc_msgSend(v65, "setCurrentMarketStockCode:", v76);
          }
          -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v76);
          v80[0] = v79;
          v80[1] = v76;
          v66 = -[HXBaseTableViewController selfStockCodesToBeAddMArr](self, "selfStockCodesToBeAddMArr");
          v75 = objc_retainAutoreleasedReturnValue(v66);
          v80[2] = v75;
          v68 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v80, v67, 3LL);
          objc_retainAutoreleasedReturnValue(v68);
          v69 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
          v70 = objc_retainAutoreleasedReturnValue(v69);
          -[HXFrozenTableView setParamsDic:](v70, "setParamsDic:", v71);
          v51 = v79;
        }
      }
    }
  }
}

//----- (00000001002E1E11) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController frozenTableView:menuForHeaderOfTableColumn:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  return 0LL;
}

//----- (00000001002E1E19) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController frozenTableView:dataCellForTableColumn:row:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  HXStockModel *v5; // rax
  HXStockModel *v6; // r15
  NSArray *v7; // rax
  NSArray *v8; // rbx
  signed __int64 v44; // rbx
  signed __int64 v50; // rbx
  signed __int64 v105; // r8
  signed __int64 v109; // r15
  signed __int64 v115; // r9

  v119 = objc_retain(a4);
  v5 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXStockModel mainStockTableViewDataArray](v6, "mainStockTableViewDataArray");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "count");
  v10(v6);
  if ( v9 )
  {
    v11 = _objc_msgSend(&OBJC_CLASS___HXTableColumn, "class");
    v12 = v119;
    if ( (unsigned __int8)_objc_msgSend(v119, "isKindOfClass:", v11) )
    {
      v13 = objc_retain(v119);
      if ( _objc_msgSend(v13, "tableHeaderItemType") )
      {
        v15 = 0LL;
LABEL_25:
        goto LABEL_26;
      }
      v16 = (void *)v14(self, "basicHQDataTypes");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v19 = (void *)v18(v13, "identifier");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v22 = v21(v20, "integerValue");
      v24 = (void *)v23(&OBJC_CLASS___NSNumber, "numberWithInteger:", v22);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v122 = v26(v17, "containsObject:", v25);
      v27 = v25;
      v29 = v28;
      v30(v20);
      v31(v17);
      v121 = v13;
      v32 = (void *)v29(v13, "identifier");
      v33 = v29;
      v34 = objc_retainAutoreleasedReturnValue(v32);
      v35 = v33;
      v36 = v33(v34, "integerValue");
      v37(v34);
      if ( !v122 && v36 != 12345670 )
      {
        v15 = 0LL;
        v12 = v119;
        v13 = v121;
        goto LABEL_25;
      }
      v13 = v121;
      v38 = (void *)v35(v121, "dataCell");
      v39 = objc_retainAutoreleasedReturnValue(v38);
      v40 = (void *)v35(v121, "headerCell");
      v41 = objc_retainAutoreleasedReturnValue(v40);
      v42 = v35(v41, "alignment");
      v123 = v39;
      ((void (__fastcall *)(id, const char *, __int64))v35)(v39, "setTextAlignment:", v42);
      v44 = v43(self, "begin") - 50;
      v46 = v45(self, "begin");
      v48 = v47(self, "count");
      if ( v44 > a5 || (v50 = a5, v46 + v48 + 50 <= a5) )
      {
        v86 = (void *)v49(v121, "identifier");
        v87 = objc_retainAutoreleasedReturnValue(v86);
        v89 = v88(v87, "integerValue");
        v12 = v119;
        if ( v89 == 12345670 )
        {
          v91 = (void *)v90(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), a5 + 1);
          v92 = objc_retainAutoreleasedReturnValue(v91);
          v93(v123, "setText:", v92);
          v95 = (void *)v94(&OBJC_CLASS___HXThemeManager, "normalTextColor");
          v96 = objc_retainAutoreleasedReturnValue(v95);
          v97(v123, "setTextColor:", v96);
        }
        else
        {
          _objc_msgSend(v123, "setText:", &charsToLeaveEscaped);
        }
        v13 = v121;
      }
      else
      {
        v12 = v119;
        if ( (unsigned __int64)-[QuoteBaseTableViewController begin](self, "begin") > a5
          || (v52 = v51(self, "begin"), v50 = a5, v52 + v53(self, "count") <= (unsigned __int64)a5) )
        {
          if ( !_objc_msgSend(v121, "tableHeaderItemType") )
          {
            v100 = (void *)v54(self, "stockModel");
            v101 = objc_retainAutoreleasedReturnValue(v100);
            v103 = (void *)v102(v101, "backgroundData");
            v104 = objc_retainAutoreleasedReturnValue(v103);
            v105 = v50;
            v106 = v104;
            v107(
              self,
              "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
              v123,
              v121,
              v105,
              v105,
              v104);
            v108 = v101;
            v13 = v121;
            v98 = v123;
            goto LABEL_23;
          }
        }
        if ( !_objc_msgSend(v121, "tableHeaderItemType") )
        {
          v56 = (void *)v55(v121, "identifier");
          v57 = objc_retainAutoreleasedReturnValue(v56);
          v59 = v58(v57, "integerValue");
          if ( v59 == 20190901 )
          {
            v61 = (void *)v60(self, "stockModel");
            v62 = objc_retainAutoreleasedReturnValue(v61);
            v64 = (void *)v63(v62, "mainStockTableViewDataArray");
            v65 = objc_retainAutoreleasedReturnValue(v64);
            v67 = v66(self, "begin");
            v69 = (void *)v68(v65, "thsDictionaryAtIndex:", a5 - v67);
            v70 = objc_retainAutoreleasedReturnValue(v69);
            v72 = (void *)v71(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
            v73 = objc_retainAutoreleasedReturnValue(v72);
            v75 = (void *)v74(v70, "thsStringForKey:", v73);
            v76 = objc_retainAutoreleasedReturnValue(v75);
            v78 = (void *)v77(&OBJC_CLASS___TextMarkManager, "sharedInstance");
            v79 = objc_retainAutoreleasedReturnValue(v78);
            v81 = (void *)v80(v79, "getPersonalLabelArray:", v76);
            objc_retainAutoreleasedReturnValue(v81);
            v82 = objc_alloc((Class)&OBJC_CLASS___TextMarkTableCell);
            v15 = _objc_msgSend(v82, "initWithLabelData:", v83);
            v85 = v76;
            v12 = v119;
            v13 = v121;
LABEL_24:
            goto LABEL_25;
          }
          v109 = a5 - v60(self, "begin");
          v111 = (void *)v110(self, "stockModel");
          v117 = objc_retainAutoreleasedReturnValue(v111);
          v113 = (void *)v112(v117, "mainStockTableViewDataArray");
          v114 = objc_retainAutoreleasedReturnValue(v113);
          v115 = v109;
          v12 = v119;
          v116(
            self,
            "reloadCellViewForBasicColumnItem:column:rowInTable:indexInDataSource:dataSource:",
            v123,
            v121,
            a5,
            v115,
            v114);
          v13 = v121;
        }
      }
      v98 = v123;
LABEL_23:
      v15 = objc_retain(v98);
      goto LABEL_24;
    }
    v15 = 0LL;
  }
  else
  {
    v15 = 0LL;
    v12 = v119;
  }
LABEL_26:
  return objc_autoreleaseReturnValue(v15);
}

//----- (00000001002E24AF) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController tableKey](ZhiBiaoGuTableViewController *self, SEL a2)
{

  begin = (void *)self->super._begin;
  if ( !begin )
  {
    self->super._begin = (unsigned __int64)CFSTR("__zhiBiaoGuTableKey");
    begin = (void *)self->super._begin;
  }
  return objc_retainAutoreleaseReturnValue(begin);
}

//----- (00000001002E24E7) ----------------------------------------------------
signed __int64 __cdecl -[ZhiBiaoGuTableViewController frozenCount](ZhiBiaoGuTableViewController *self, SEL a2)
{
  return 3LL;
}

//----- (00000001002E24F2) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuTableViewController postZhiBiaoGuDidChangeBlock](ZhiBiaoGuTableViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 384LL, 0);
}

//----- (00000001002E2505) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController setPostZhiBiaoGuDidChangeBlock:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 384LL);
}

//----- (00000001002E2516) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController setCodeListSortRequestModule:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._allCodesNum, a3);
}

//----- (00000001002E252A) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController setBackgroundCodeListSortRequestModule:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._frozenCount, a3);
}

//----- (00000001002E253E) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController setBackgroundDetailDataRequestModule:](
        ZhiBiaoGuTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._orderHQDataTypes, a3);
}

//----- (00000001002E2552) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuTableViewController .cxx_destruct](ZhiBiaoGuTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._orderHQDataTypes, 0LL);
  objc_storeStrong((id *)&self->super._frozenCount, 0LL);
  objc_storeStrong((id *)&self->super._allCodesNum, 0LL);
  objc_storeStrong((id *)&self->super._count, 0LL);
  objc_storeStrong((id *)&self->super._begin, 0LL);
}

