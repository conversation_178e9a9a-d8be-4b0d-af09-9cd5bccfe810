//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

#import "NSTableViewDataSource-Protocol.h"
#import "NSTableViewDelegate-Protocol.h"

@class HXSearchTextField, NSMutableArray, NSString, NSTableView;

@interface ZiXunKeyboradSpiritViewController : NSViewController <NSTableViewDelegate, NSTableViewDataSource>
{
    BOOL _autoJump;
    HXSearchTextField *_keyboardTextField;
    CDUnknownBlockType _searchResultBlock;
    CDUnknownBlockType _showNoResultViewBlock;
    NSTableView *_keyboardTable;
    long long _longFromTime;
    NSMutableArray *_resultMArray;
}


@property(nonatomic) BOOL autoJump; // @synthesize autoJump=_autoJump;
@property(retain, nonatomic) NSMutableArray *resultMArray; // @synthesize resultMArray=_resultMArray;
@property(nonatomic) long long longFromTime; // @synthesize longFromTime=_longFromTime;
@property __weak NSTableView *keyboardTable; // @synthesize keyboardTable=_keyboardTable;
@property(copy, nonatomic) CDUnknownBlockType showNoResultViewBlock; // @synthesize showNoResultViewBlock=_showNoResultViewBlock;
@property(copy, nonatomic) CDUnknownBlockType searchResultBlock; // @synthesize searchResultBlock=_searchResultBlock;
@property(nonatomic) __weak HXSearchTextField *keyboardTextField; // @synthesize keyboardTextField=_keyboardTextField;
- (id)tableView:(id)arg1 rowViewForRow:(long long)arg2;
- (BOOL)tableView:(id)arg1 shouldSelectRow:(long long)arg2;
- (long long)numberOfRowsInTableView:(id)arg1;
- (id)tableView:(id)arg1 viewForTableColumn:(id)arg2 row:(long long)arg3;
- (void)selectNext;
- (void)selectPrevious;
- (void)keyboardSpiritRowDoubleClicked;
- (void)sendUserLog:(id)arg1;
- (void)reloadData;
- (void)clear;
- (void)voiceAutoJump;
- (void)parseSearchData:(id)arg1 pattern:(id)arg2;
- (void)requestWithPattern:(id)arg1;
- (void)keyDownThenRequest:(id)arg1;
- (void)textDidChange;
- (void)voiceRequest:(id)arg1;
- (void)viewWillAppear;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

