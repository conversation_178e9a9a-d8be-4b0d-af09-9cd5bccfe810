//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSArray, NSDictionary, NSFont, NSNumber, NSString;

@interface YiDongListInfoResult : NSObject
{
    BOOL _isSelectResult;
    BOOL _isFall;
    BOOL _offSetTime;
    int _showTimeFlag;
    id _category;
    NSNumber *_isdel;
    NSString *_isread;
    NSString *_maintitle;
    NSString *_newstitle;
    NSString *_newurl;
    NSNumber *_rtime;
    NSString *_stocklist;
    NSString *_time;
    NSString *_title;
    NSString *_type;
    NSArray *_stocklistArr;
    double _rowHeight;
    NSString *_flagTitleName;
    long long _showType;
    unsigned long long _curItemIndex;
    NSString *_codeFlag;
    NSDictionary *_stockInfo;
    NSArray *_stockCodelist;
    NSArray *_stockNamelist;
    double _normalRadius;
    NSFont *_titleFont;
    double _yiDongButtonWidth;
    NSArray *_yiDongDianArr;
    double _verticalLineHeight;
    struct CGSize _textSize;
    struct CGPoint _dotCenterPoint;
    struct CGPoint _yiDongDianLocation;
    struct CGRect _yiDongPointFrame;
    struct CGRect _buttonFrame;
    struct CGRect _lineFrame;
    struct CGRect _visibleRect;
    struct CGRect _topLeftRect;
    struct CGRect _bottomLeftRect;
    struct CGRect _topRightRect;
    struct CGRect _bottomRightRect;
}


@property(nonatomic) double verticalLineHeight; // @synthesize verticalLineHeight=_verticalLineHeight;
@property(nonatomic) struct CGPoint yiDongDianLocation; // @synthesize yiDongDianLocation=_yiDongDianLocation;
@property(nonatomic) struct CGPoint dotCenterPoint; // @synthesize dotCenterPoint=_dotCenterPoint;
@property(retain, nonatomic) NSArray *yiDongDianArr; // @synthesize yiDongDianArr=_yiDongDianArr;
@property(nonatomic) double yiDongButtonWidth; // @synthesize yiDongButtonWidth=_yiDongButtonWidth;
@property(retain, nonatomic) NSFont *titleFont; // @synthesize titleFont=_titleFont;
@property(nonatomic) double normalRadius; // @synthesize normalRadius=_normalRadius;
@property(nonatomic) struct CGRect bottomRightRect; // @synthesize bottomRightRect=_bottomRightRect;
@property(nonatomic) struct CGRect topRightRect; // @synthesize topRightRect=_topRightRect;
@property(nonatomic) struct CGRect bottomLeftRect; // @synthesize bottomLeftRect=_bottomLeftRect;
@property(nonatomic) struct CGRect topLeftRect; // @synthesize topLeftRect=_topLeftRect;
@property(nonatomic) struct CGRect visibleRect; // @synthesize visibleRect=_visibleRect;
@property(retain, nonatomic) NSArray *stockNamelist; // @synthesize stockNamelist=_stockNamelist;
@property(retain, nonatomic) NSArray *stockCodelist; // @synthesize stockCodelist=_stockCodelist;
@property(retain, nonatomic) NSDictionary *stockInfo; // @synthesize stockInfo=_stockInfo;
@property(nonatomic) BOOL offSetTime; // @synthesize offSetTime=_offSetTime;
@property(nonatomic) BOOL isFall; // @synthesize isFall=_isFall;
@property(retain, nonatomic) NSString *codeFlag; // @synthesize codeFlag=_codeFlag;
@property(nonatomic) unsigned long long curItemIndex; // @synthesize curItemIndex=_curItemIndex;
@property(nonatomic) long long showType; // @synthesize showType=_showType;
@property(nonatomic) struct CGSize textSize; // @synthesize textSize=_textSize;
@property(nonatomic) struct CGRect lineFrame; // @synthesize lineFrame=_lineFrame;
@property(nonatomic) struct CGRect buttonFrame; // @synthesize buttonFrame=_buttonFrame;
@property(nonatomic) struct CGRect yiDongPointFrame; // @synthesize yiDongPointFrame=_yiDongPointFrame;
@property(retain, nonatomic) NSString *flagTitleName; // @synthesize flagTitleName=_flagTitleName;
@property(nonatomic) BOOL isSelectResult; // @synthesize isSelectResult=_isSelectResult;
@property(nonatomic) double rowHeight; // @synthesize rowHeight=_rowHeight;
@property(nonatomic) int showTimeFlag; // @synthesize showTimeFlag=_showTimeFlag;
@property(retain, nonatomic) NSArray *stocklistArr; // @synthesize stocklistArr=_stocklistArr;
@property(retain, nonatomic) NSString *type; // @synthesize type=_type;
@property(retain, nonatomic) NSString *title; // @synthesize title=_title;
@property(retain, nonatomic) NSString *time; // @synthesize time=_time;
@property(retain, nonatomic) NSString *stocklist; // @synthesize stocklist=_stocklist;
@property(retain, nonatomic) NSNumber *rtime; // @synthesize rtime=_rtime;
@property(retain, nonatomic) NSString *newurl; // @synthesize newurl=_newurl;
@property(retain, nonatomic) NSString *newstitle; // @synthesize newstitle=_newstitle;
@property(retain, nonatomic) NSString *maintitle; // @synthesize maintitle=_maintitle;
@property(retain, nonatomic) NSString *isread; // @synthesize isread=_isread;
@property(retain, nonatomic) NSNumber *isdel; // @synthesize isdel=_isdel;
@property(retain, nonatomic) id category; // @synthesize category=_category;
- (void)updateSubViewFrame;
- (void)adjustButtonFrameWithPreYiDong:(id)arg1;
- (void)modifyYiDongDianType;
- (void)adjustYiDongFrameYiDongDians:(id)arg1 middleIndex:(long long)arg2;
- (void)convertYiDongFrameWithPreYiDongs:(id)arg1;
- (void)setupViewWithType:(long long)arg1;
- (struct CGSize)heightOfRepliesLabelWithContent:(id)arg1;
- (void)configYiDongDianWithLocaltion:(struct CGPoint)arg1 yiDongName:(id)arg2 preYiDongs:(id)arg3 inRect:(struct CGRect)arg4;
- (void)setShowTypeByCenterPoint:(struct CGPoint)arg1;

@end

