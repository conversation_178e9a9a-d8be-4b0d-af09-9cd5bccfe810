TransactionCell *__cdecl -[TransactionCell init](TransactionCell *self, SEL a2)
{
  TransactionCell *v2; // rax
  TransactionCell *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___TransactionCell;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
  {
    _objc_msgSend(v2, "setEditable:", 0LL);
    _objc_msgSend(v3, "setSelectable:", 0LL);
  }
  return v3;
}

//----- (00000001001E99A7) ----------------------------------------------------
void __cdecl -[TransactionCell drawWithFrame:inView:](TransactionCell *self, SEL a2, CGRect a3, id a4)
{
  NSString *v4; // rax
  NSString *v5; // r15

  v4 = -[TransactionCell text](self, "text", a4);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = _objc_msgSend(v6, "curTextColor");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v9, "drawDetailString:contextFrame:textAlignment:textColor:", v5, 0LL, v8);
  v11 = _objc_msgSend(v10, "leftText");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = _objc_msgSend(v13, "leftTextColor");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v16, "drawDetailString:contextFrame:textAlignment:textColor:", v12, 0LL, v15);
  v18 = _objc_msgSend(v17, "rightText");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = _objc_msgSend(v20, "rightTextColor");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v23, "drawDetailString:contextFrame:textAlignment:textColor:", v19, 1LL, v22);
}

//----- (00000001001E9AFF) ----------------------------------------------------
void __cdecl -[TransactionCell drawDetailString:contextFrame:textAlignment:textColor:](
        TransactionCell *self,
        SEL a2,
        id a3,
        CGRect a4,
        signed __int64 a5,
        id a6)
{
  NSCharacterSet *v10; // rax
  TransactionCell *v13; // rbx
  NSFont *v16; // rax
  NSFont *v17; // r15
  NSFont *v19; // rax
  NSFont *v20; // rbx
  NSDictionary *v29; // rax
  NSDictionary *v30; // r15
  NSCharacterSet *v41; // [rsp+30h] [rbp-D0h]
  CGFloat v45; // [rsp+50h] [rbp-B0h]
  CGPoint origin; // [rsp+60h] [rbp-A0h]
  CGSize size; // [rsp+70h] [rbp-90h]
  CGFloat v52; // [rsp+90h] [rbp-70h]
  _QWORD v53[3]; // [rsp+A0h] [rbp-60h] BYREF
  _QWORD v54[3]; // [rsp+B8h] [rbp-48h] BYREF

  v8 = objc_retain(a3);
  v9 = objc_retain(a6);
  if ( _objc_msgSend(v8, "length") )
  {
    v10 = _objc_msgSend(&OBJC_CLASS___NSCharacterSet, "characterSetWithCharactersInString:", CFSTR("\n\r\f"));
    v41 = objc_retainAutoreleasedReturnValue(v10);
    v11 = _objc_msgSend(v8, "componentsSeparatedByCharactersInSet:", v41);
    v43 = objc_retainAutoreleasedReturnValue(v11);
    v12 = _objc_msgSend(v43, "componentsJoinedByString:", &charsToLeaveEscaped);
    objc_retainAutoreleasedReturnValue(v12);
    v40 = v9;
    if ( v9 )
    {
      v13 = self;
      _objc_msgSend(self, "setTextColor:", v9);
    }
    else
    {
      v14 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v13 = self;
      _objc_msgSend(self, "setTextColor:", v15);
    }
    v16 = -[TransactionCell textFont](v13, "textFont");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v42 = v18;
    if ( v17 )
      v19 = -[TransactionCell textFont](v13, "textFont");
    else
      v19 = _objc_msgSend(&OBJC_CLASS___NSFont, "fontWithName:size:", CFSTR("Helvetica Neue"), 13.0);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v21, "setTextFont:", v20);
    v22 = _objc_msgSend(&OBJC_CLASS___NSParagraphStyle, "defaultParagraphStyle");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v24 = _objc_msgSend(v23, "mutableCopy");
    _objc_msgSend(v24, "setLineBreakMode:", 4LL);
    v53[0] = NSForegroundColorAttributeName;
    v26 = _objc_msgSend(v25, "textColor");
    v50 = objc_retainAutoreleasedReturnValue(v26);
    v54[0] = v50;
    v53[1] = NSFontAttributeName;
    v28 = _objc_msgSend(v27, "textFont");
    v54[1] = objc_retainAutoreleasedReturnValue(v28);
    v53[2] = NSParagraphStyleAttributeName;
    v44 = v24;
    v54[2] = v24;
    v29 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v54,
                            v53,
                            3LL);
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v32 = objc_alloc(&OBJC_CLASS___NSMutableAttributedString);
    v33 = _objc_msgSend(v32, "initWithString:attributes:", v42, v30);
    v51 = ((double (__fastcall *)(__int64, const char *, NSDictionary *))_objc_msgSend)(v34, "sizeWithAttributes:", v30);
    v46 = v6;
    v45 = a4.size.width - ((double (__fastcall *)(TransactionCell *, const char *))_objc_msgSend)(self, "leftInterval");
    v35 = v51;
    if ( v51 > v45 - ((double (__fastcall *)(TransactionCell *, const char *))_objc_msgSend)(self, "rightInterval") )
    {
      v52 = a4.size.width
          - ((double (__fastcall *)(TransactionCell *, const char *))_objc_msgSend)(self, "leftInterval");
      v35 = v52 - ((double (__fastcall *)(TransactionCell *, const char *))_objc_msgSend)(self, "rightInterval");
    }
    size = NSZeroRect.size;
    origin = NSZeroRect.origin;
    x = a4.origin.x;
    y = a4.origin.y;
    height = a4.size.height;
    if ( a5 == 2 )
    {
      origin.x = (a4.size.width - v35) * 0.5 + a4.origin.x;
    }
    else
    {
      if ( a5 == 1 )
      {
        -[TransactionCell rightInterval](self, "rightInterval");
        height = a4.size.height;
        y = a4.origin.y;
        x = a4.size.width + a4.origin.x - v35 - (a4.size.width + a4.origin.x - v35);
      }
      else
      {
        if ( a5 )
        {
LABEL_19:
          _objc_msgSend(v33, "drawInRect:", origin.x, size.width, v35, height, x, origin, size);
          v9 = v40;
          goto LABEL_20;
        }
        -[TransactionCell leftInterval](self, "leftInterval");
        height = a4.size.height;
        y = a4.origin.y;
        x = a4.origin.x + NSZeroRect.origin.x;
      }
      origin.x = x;
    }
    height = (height - v46) * 0.5;
    origin.y = y + height;
    size.width = v35;
    size.height = v46;
    goto LABEL_19;
  }
LABEL_20:
}

//----- (00000001001EA045) ----------------------------------------------------
NSString *__cdecl -[TransactionCell text](TransactionCell *self, SEL a2)
{
  return (NSString *)self->super.super.super._contents;
}

//----- (00000001001EA056) ----------------------------------------------------
void __cdecl -[TransactionCell setText:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super.super._contents, a3);
}

//----- (00000001001EA06A) ----------------------------------------------------
NSColor *__cdecl -[TransactionCell curTextColor](TransactionCell *self, SEL a2)
{
  return (NSColor *)self->super.super.super._cFlags;
}

//----- (00000001001EA07B) ----------------------------------------------------
void __cdecl -[TransactionCell setCurTextColor:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super.super._cFlags, a3);
}

//----- (00000001001EA08F) ----------------------------------------------------
NSString *__cdecl -[TransactionCell leftText](TransactionCell *self, SEL a2)
{
  return (NSString *)self->super.super.super._support;
}

//----- (00000001001EA0A0) ----------------------------------------------------
void __cdecl -[TransactionCell setLeftText:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super.super._support, a3);
}

//----- (00000001001EA0B4) ----------------------------------------------------
NSString *__cdecl -[TransactionCell rightText](TransactionCell *self, SEL a2)
{
  return (NSString *)self->super.super._tag;
}

//----- (00000001001EA0C5) ----------------------------------------------------
void __cdecl -[TransactionCell setRightText:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._tag, a3);
}

//----- (00000001001EA0D9) ----------------------------------------------------
NSColor *__cdecl -[TransactionCell leftTextColor](TransactionCell *self, SEL a2)
{
  return (NSColor *)self->super.super._target;
}

//----- (00000001001EA0EA) ----------------------------------------------------
void __cdecl -[TransactionCell setLeftTextColor:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.super._target, a3);
}

//----- (00000001001EA0FE) ----------------------------------------------------
NSColor *__cdecl -[TransactionCell rightTextColor](TransactionCell *self, SEL a2)
{
  return (NSColor *)self->super.super._action;
}

//----- (00000001001EA10F) ----------------------------------------------------
void __cdecl -[TransactionCell setRightTextColor:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._action, a3);
}

//----- (00000001001EA123) ----------------------------------------------------
double __cdecl -[TransactionCell leftInterval](TransactionCell *self, SEL a2)
{
  return *(double *)&self->super.super._controlView;
}

//----- (00000001001EA135) ----------------------------------------------------
void __cdecl -[TransactionCell setLeftInterval:](TransactionCell *self, SEL a2, double a3)
{
  *(double *)&self->super.super._controlView = a3;
}

//----- (00000001001EA147) ----------------------------------------------------
double __cdecl -[TransactionCell rightInterval](TransactionCell *self, SEL a2)
{
  return *(double *)&self->super._backgroundColor;
}

//----- (00000001001EA159) ----------------------------------------------------
void __cdecl -[TransactionCell setRightInterval:](TransactionCell *self, SEL a2, double a3)
{
  *(double *)&self->super._backgroundColor = a3;
}

//----- (00000001001EA16B) ----------------------------------------------------
NSFont *__cdecl -[TransactionCell textFont](TransactionCell *self, SEL a2)
{
  return (NSFont *)self->super._textColor;
}

//----- (00000001001EA17C) ----------------------------------------------------
void __cdecl -[TransactionCell setTextFont:](TransactionCell *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._textColor, a3);
}

//----- (00000001001EA190) ----------------------------------------------------
signed __int64 __cdecl -[TransactionCell textAlignment](TransactionCell *self, SEL a2)
{
  return *(_QWORD *)&self->super._tfFlags;
}

//----- (00000001001EA1A1) ----------------------------------------------------
void __cdecl -[TransactionCell setTextAlignment:](TransactionCell *self, SEL a2, signed __int64 a3)
{
  *(_QWORD *)&self->super._tfFlags = a3;
}

//----- (00000001001EA1B2) ----------------------------------------------------
void __cdecl -[TransactionCell .cxx_destruct](TransactionCell *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._textColor, 0LL);
  objc_storeStrong((id *)&self->super.super._action, 0LL);
  objc_storeStrong(&self->super.super._target, 0LL);
  objc_storeStrong((id *)&self->super.super._tag, 0LL);
  objc_storeStrong(&self->super.super.super._support, 0LL);
  objc_storeStrong((id *)&self->super.super.super._cFlags, 0LL);
  objc_storeStrong(&self->super.super.super._contents, 0LL);
}

