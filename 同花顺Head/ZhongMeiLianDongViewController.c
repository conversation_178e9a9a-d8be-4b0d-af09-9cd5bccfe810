void __cdecl -[<PERSON>hongMeiLianDongViewController viewDidLoad](ZhongMeiLianDongViewController *self, SEL a2)
{
  LianDongMeiGuTableController *v2; // rax
  LianDongMeiGuTableController *v3; // rbx
  id (*v5)(id, SEL, ...); // r12

  v9.receiver = self;
  v9.super_class = (Class)&OBJC_CLASS___ZhongMeiLianDongViewController;
  -[ZMLDBaseViewController viewDidLoad](&v9, "viewDidLoad");
  v2 = -[ZMLDBaseViewController meiGuTableVC](self, "meiGuTableVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "setBlockID:", 55912LL);
  v6 = v5(self, "meiGuTableVC");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "setTableID:", 17105667LL);
}

