//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSBundle;

@interface configmanager : NSObject
{
    NSBundle *_bundle;
}

+ (id)shareInstance;

@property(copy, nonatomic) NSBundle *bundle; // @synthesize bundle=_bundle;
- (id)GetBundle;
- (id)loadString:(id)arg1;
- (long long)SetConfigValue:(char *)arg1 Name:(char *)arg2 StringValue:(char *)arg3;
- (long long)SetConfigValue:(char *)arg1 Name:(char *)arg2 IntegerValue:(unsigned int)arg3;
- (id)GetConfigValue:(char *)arg1 Name:(char *)arg2 DefaultString:(id)arg3;
- (long long)GetConfigValue:(char *)arg1 Name:(char *)arg2 DefaultInteger:(unsigned int)arg3;
- (void)saveConfig2File;
- (void)initSysConfig;
- (id)init;

@end

