void __cdecl -[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>inContainerController viewDidLoad](Zhang<PERSON>ingJi<PERSON>inContainerController *self, SEL a2)
{

  v12.receiver = self;
  v12.super_class = (Class)&OBJC_CLASS___ZhangTingJiYinContainerController;
  -[PanKouModularBaseViewController viewDidLoad](&v12, "viewDidLoad");
  _objc_msgSend(v2, "initViewState");
  v4 = _objc_msgSend(v3, "suYuanContainerView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = _objc_msgSend(v6, "suYuanTVC");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "view");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v5, "addSubview:", v10);
  _objc_msgSend(v11, "setIsRequestDiffStock:", 1LL);
}

//----- (000000010070C1E2) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController initViewState](ZhangTingJiYinContainerController *self, SEL a2)
{
  HXDragAndDropBaseView *v13; // rax
  HXDragAndDropBaseView *v14; // rbx
  SEL v17; // r12
  SEL v20; // r12
  SEL v23; // r12
  SEL v26; // r12
  SEL v31; // r12
  NSTextField *v36; // rax
  NSTextField *v37; // rbx
  HXBaseView *v40; // rax
  HXBaseView *v41; // rbx
  HXBaseView *v42; // rax
  HXBaseView *v43; // rbx
  HXBaseView *v44; // rax
  HXBaseView *v45; // rbx
  HXBaseView *v46; // rax
  HXBaseView *v47; // rbx
  HXBaseView *v50; // rax
  HXBaseView *v51; // r13
  SEL v71; // r12
  SEL v76; // r12
  SEL v81; // r12
  SEL v86; // r12
  SEL v91; // r12
  SEL v96; // r12
  SEL v101; // r12
  SEL v106; // r12
  SEL v111; // r12

  v2 = _objc_msgSend(self, "view");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(&OBJC_CLASS___HXDragAndDropBaseView, "class");
  _objc_msgSend(v3, "isKindOfClass:", v4);
  if ( v5 )
  {
    v6 = _objc_msgSend(self, "view");
    objc_retainAutoreleasedReturnValue(v6);
    v7 = _objc_msgSend(self, "className");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v9, "setTargetViewControllerName:", v8);
  }
  v11 = _objc_msgSend(self, "view");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = -[PanKouModularBaseViewController titleContainerView](self, "titleContainerView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  -[HXDragAndDropBaseView setDragImageView:](v14, "setDragImageView:", v12);
  v15 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = _objc_msgSend(self, v17);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "setBackgroundColor:", v16);
  v21 = _objc_msgSend(self, v20);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v22, "setTopBorder:", 1LL);
  v24 = _objc_msgSend(self, v23);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  _objc_msgSend(v25, "setBottomBorder:", 1LL);
  v27 = _objc_msgSend(self, v26);
  v28 = objc_retainAutoreleasedReturnValue(v27);
  _objc_msgSend(v28, "setBorderWidth:", 1.0);
  v29 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v32 = _objc_msgSend(self, v31);
  v33 = objc_retainAutoreleasedReturnValue(v32);
  _objc_msgSend(v33, "setBorderColor:", v30);
  v34 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  v36 = -[ZhangTingJiYinContainerController titleLabel](self, "titleLabel");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  _objc_msgSend(v37, "setTextColor:", v35);
  v38 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v40 = -[ZhangTingJiYinContainerController firstInfoView](self, "firstInfoView");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  -[HXBaseView setBackgroundColor:](v41, "setBackgroundColor:", v39);
  v42 = -[ZhangTingJiYinContainerController secondInfoView](self, "secondInfoView");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  -[HXBaseView setTopBorder:](v43, "setTopBorder:", 1LL);
  v44 = -[ZhangTingJiYinContainerController secondInfoView](self, "secondInfoView");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  -[HXBaseView setBottomBorder:](v45, "setBottomBorder:", 1LL);
  v46 = -[ZhangTingJiYinContainerController secondInfoView](self, "secondInfoView");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  -[HXBaseView setBorderWidth:](v47, "setBorderWidth:", 1.0);
  v48 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v49 = objc_retainAutoreleasedReturnValue(v48);
  v50 = -[ZhangTingJiYinContainerController secondInfoView](self, "secondInfoView");
  v51 = objc_retainAutoreleasedReturnValue(v50);
  -[HXBaseView setBorderColor:](v51, "setBorderColor:", v49);
  v52 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v53 = objc_retainAutoreleasedReturnValue(v52);
  v55 = _objc_msgSend(v54, "secondInfoView");
  v56 = objc_retainAutoreleasedReturnValue(v55);
  _objc_msgSend(v56, "setBackgroundColor:", v53);
  v57 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v58 = objc_retainAutoreleasedReturnValue(v57);
  v60 = v59;
  v61 = _objc_msgSend(v59, "secondInfoTitleTF");
  v62 = objc_retainAutoreleasedReturnValue(v61);
  _objc_msgSend(v62, "setTextColor:", v58);
  v63 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v64 = objc_retainAutoreleasedReturnValue(v63);
  v65 = _objc_msgSend(v60, "thirdInfoView");
  v66 = objc_retainAutoreleasedReturnValue(v65);
  _objc_msgSend(v66, "setBackgroundColor:", v64);
  v67 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v68 = objc_retainAutoreleasedReturnValue(v67);
  v69 = _objc_msgSend(v60, "thirdInfoTitleTF");
  v70 = objc_retainAutoreleasedReturnValue(v69);
  _objc_msgSend(v70, v71, v68);
  v72 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v73 = objc_retainAutoreleasedReturnValue(v72);
  v74 = _objc_msgSend(v60, "chengGongCiShuTF");
  v75 = objc_retainAutoreleasedReturnValue(v74);
  _objc_msgSend(v75, v76, v73);
  v77 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v78 = objc_retainAutoreleasedReturnValue(v77);
  v79 = _objc_msgSend(v60, "feiYiZiCiShuTF");
  v80 = objc_retainAutoreleasedReturnValue(v79);
  _objc_msgSend(v80, v81, v78);
  v82 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v83 = objc_retainAutoreleasedReturnValue(v82);
  v84 = _objc_msgSend(v60, "beiZaCiShuTF");
  v85 = objc_retainAutoreleasedReturnValue(v84);
  _objc_msgSend(v85, v86, v83);
  v87 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v88 = objc_retainAutoreleasedReturnValue(v87);
  v89 = _objc_msgSend(v60, "chengGongLvTF");
  v90 = objc_retainAutoreleasedReturnValue(v89);
  _objc_msgSend(v90, v91, v88);
  v92 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v93 = objc_retainAutoreleasedReturnValue(v92);
  v94 = _objc_msgSend(v60, "gaoKaiLvTF");
  v95 = objc_retainAutoreleasedReturnValue(v94);
  _objc_msgSend(v95, v96, v93);
  v97 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v98 = objc_retainAutoreleasedReturnValue(v97);
  v99 = _objc_msgSend(v60, "pingJunGaoKaiTF");
  v100 = objc_retainAutoreleasedReturnValue(v99);
  _objc_msgSend(v100, v101, v98);
  v102 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v103 = objc_retainAutoreleasedReturnValue(v102);
  v104 = _objc_msgSend(v60, "shangZhangLvTF");
  v105 = objc_retainAutoreleasedReturnValue(v104);
  _objc_msgSend(v105, v106, v103);
  v107 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v108 = objc_retainAutoreleasedReturnValue(v107);
  v109 = _objc_msgSend(v60, "pingJunZhangFuTF");
  v110 = objc_retainAutoreleasedReturnValue(v109);
  _objc_msgSend(v110, v111, v108);
  v112 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  objc_retainAutoreleasedReturnValue(v112);
  v113 = _objc_msgSend(v60, "suYuanContainerView");
  v114 = objc_retainAutoreleasedReturnValue(v113);
  _objc_msgSend(v114, "setBackgroundColor:", v115);
  v117 = _objc_msgSend(v60, "suYuanContainerView");
  v118 = objc_retainAutoreleasedReturnValue(v117);
  _objc_msgSend(v118, "setAllBorder:", 1LL);
  v119 = _objc_msgSend(v60, "suYuanContainerView");
  v120 = objc_retainAutoreleasedReturnValue(v119);
  _objc_msgSend(v120, "setBorderWidth:", 1.0);
  v121 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  objc_retainAutoreleasedReturnValue(v121);
  v122 = _objc_msgSend(v60, "suYuanContainerView");
  v123 = objc_retainAutoreleasedReturnValue(v122);
  _objc_msgSend(v123, "setBorderColor:", v124);
  v126 = _objc_msgSend(v60, "showAllBtn");
  v127 = objc_retainAutoreleasedReturnValue(v126);
  _objc_msgSend(v127, "setCanBeSelected:", 1LL);
  v128 = +[HXThemeManager lightBlueLineColor](&OBJC_CLASS___HXThemeManager, "lightBlueLineColor");
  objc_retainAutoreleasedReturnValue(v128);
  v129 = _objc_msgSend(v60, "showAllBtn");
  v130 = objc_retainAutoreleasedReturnValue(v129);
  _objc_msgSend(v130, "setTextColorDefault:", v131);
  v133 = _objc_msgSend(v60, "showAllBtn");
  v134 = objc_retainAutoreleasedReturnValue(v133);
  _objc_msgSend(v134, "setHidden:", 1LL);
  v135 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  objc_retainAutoreleasedReturnValue(v135);
  v136 = _objc_msgSend(v60, "showAllBtn");
  v137 = objc_retainAutoreleasedReturnValue(v136);
  _objc_msgSend(v137, "setBackgroundColor:", v138);
}

//----- (000000010070CBC3) ----------------------------------------------------
ZhangTingSuYuanTableViewController *__cdecl -[ZhangTingJiYinContainerController suYuanTVC](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  NSTextField *chengGongCiShuTF; // rdi
  objc_class *v5; // rax
  HXBaseView *v8; // rax

  chengGongCiShuTF = self->_chengGongCiShuTF;
  if ( !chengGongCiShuTF )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangTingSuYuanTableViewController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZhangTingSuYuanTableViewController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.super.super.isa + v6) = v5;
    v8 = -[ZhangTingJiYinContainerController suYuanContainerView](self, "suYuanContainerView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = (char *)v9;
    if ( v9 )
      objc_msgSend_stret(v16, v9, "bounds");
    else
      memset(v16, 0, 32);
    v12 = _objc_msgSend(*(id *)((char *)&self->super.super.super.super.super.isa + v10), "view");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "setFrame:");
    chengGongCiShuTF = *(NSTextField **)((char *)&self->super.super.super.super.super.isa + v14);
  }
  return (ZhangTingSuYuanTableViewController *)objc_retainAutoreleaseReturnValue(chengGongCiShuTF);
}

//----- (000000010070CCC0) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController requestForModularData:market:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSURL *v13; // rax
  NSURL *v14; // rbx
  NSURLRequest *v15; // rax
  NSURLRequest *v16; // r13
  _QWORD v20[4]; // [rsp+0h] [rbp-90h] BYREF
  _QWORD v22[4]; // [rsp+28h] [rbp-68h] BYREF
  id to; // [rsp+48h] [rbp-48h] BYREF
  id location; // [rsp+58h] [rbp-38h] BYREF

  val = self;
  objc_retain(a3);
  v5 = objc_retain(a4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v6) )
  {
    v8 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v8) )
    {
      if ( _objc_msgSend(v9, "length")
        && _objc_msgSend(v5, "length")
        && (unsigned __int8)+[HXTools isHuShenMarket:](&OBJC_CLASS___HXTools, "isHuShenMarket:", v5) )
      {
        _objc_msgSend(val, "setStockCode:", v10);
        _objc_msgSend(val, "setMarket:", v5);
        v12 = _objc_msgSend(
                CFSTR("http://ai.10jqka.com.cn/transfer/index/index/?app=21&code="),
                "stringByAppendingString:",
                v11);
        v24 = objc_retainAutoreleasedReturnValue(v12);
        v13 = _objc_msgSend(&OBJC_CLASS___NSURL, "URLWithString:", v24);
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(&OBJC_CLASS___NSURLRequest, "requestWithURL:", v14);
        v16 = objc_retainAutoreleasedReturnValue(v15);
        objc_initWeak(&location, val);
        v17 = objc_alloc((Class)&OBJC_CLASS___AFHTTPRequestOperation);
        v18 = _objc_msgSend(v17, "initWithRequest:", v16);
        val = v16;
        v22[0] = _NSConcreteStackBlock;
        v22[1] = 3254779904LL;
        v22[2] = sub_10070CF75;
        v22[3] = &unk_1012DC180;
        objc_copyWeak(&to, &location);
        v20[0] = _NSConcreteStackBlock;
        v20[1] = 3254779904LL;
        v20[2] = sub_10070D060;
        v20[3] = &unk_1012DD6D0;
        objc_copyWeak(&v21, &location);
        _objc_msgSend(v18, "setCompletionBlockWithSuccess:failure:", v22, v20);
        _objc_msgSend(v18, "start");
        objc_destroyWeak(&v21);
        objc_destroyWeak(&to);
        objc_destroyWeak(&location);
      }
    }
  }
}

//----- (000000010070CF75) ----------------------------------------------------
void __fastcall sub_10070CF75(__int64 a1, __int64 a2, __int64 a3)
{
  id WeakRetained; // r14

  if ( a3 )
  {
    v3 = _objc_msgSend(&OBJC_CLASS___NSJSONSerialization, "JSONObjectWithData:options:error:", a3, 4LL, 0LL);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    if ( v4 )
    {
      v5 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
      if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
      {
        v6 = _objc_msgSend(v4, "thsDictionaryForKey:", CFSTR("data"));
        v7 = objc_retainAutoreleasedReturnValue(v6);
        if ( v7 )
        {
          WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
          _objc_msgSend(WeakRetained, "dealWithRequestData:", v7);
        }
      }
    }
  }
}

//----- (000000010070D060) ----------------------------------------------------
void __fastcall sub_10070D060(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "resetAllViewState");
}

//----- (000000010070D091) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController dealWithRequestData:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  NSString *v28; // rax
  NSString *v40; // rax
  NSString *v45; // rax
  NSString *v50; // rax
  NSString *v68; // rax
  __CFString *v69; // rax
  NSTextField *v73; // rax
  NSTextField *v74; // r13
  NSTextField *v78; // rax
  NSTextField *v79; // rbx
  NSTextField *v84; // rax
  NSTextField *v85; // rbx
  NSTextField *v90; // rax
  NSTextField *v91; // rbx
  NSTextField *v96; // rax
  NSTextField *v97; // rbx
  NSTextField *v102; // rax
  NSTextField *v103; // rbx
  NSTextField *v112; // rax
  NSTextField *v113; // r14
  NSTextField *v119; // rax
  NSTextField *v120; // r14
  id (*v122)(id, SEL, ...); // r12
  id (*v124)(id, SEL, ...); // r12
  id (*v128)(id, SEL, ...); // r12
  id (*v130)(id, SEL, ...); // r12
  SEL v133; // r12
  NSTextField *v157; // rax
  NSTextField *v158; // rax
  id (*v161)(id, SEL, ...); // r12
  id (*v164)(id, SEL, ...); // r12
  NSAttributedStringKey v166; // r15
  id (*v167)(id, SEL, ...); // r12
  id (*v170)(id, SEL, ...); // r12

  v4 = objc_retain(a3);
  v5 = v4;
  if ( !v4 )
    goto LABEL_83;
  v6 = _objc_msgSend(v4, "thsNumberForKey:", CFSTR("limitup_times"));
  objc_retainAutoreleasedReturnValue(v6);
  v7 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("natural_limitup_times"));
  v181 = objc_retainAutoreleasedReturnValue(v7);
  v8 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("hit_times"));
  v193 = objc_retainAutoreleasedReturnValue(v8);
  v9 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("high_probability"));
  v174 = objc_retainAutoreleasedReturnValue(v9);
  v10 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("average_openincome"));
  v176 = objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("nextday_rise_rate"));
  v175 = objc_retainAutoreleasedReturnValue(v11);
  v12 = _objc_msgSend(v5, "thsNumberForKey:", CFSTR("avg_rise_rate"));
  v182 = objc_retainAutoreleasedReturnValue(v12);
  v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v15 = (unsigned __int8)_objc_msgSend(v14, "isKindOfClass:", v13);
  v184 = obj;
  v177 = v16;
  if ( v15 )
  {
    _objc_msgSend(v16, "doubleValue");
    v190 = obj;
    v17 = v181;
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v177, "doubleValue");
      v190 = obj;
      if ( v3 != 2147483648.0 )
      {
        if ( (__int64)_objc_msgSend(v177, "integerValue") < 0 )
        {
          v19 = obj;
        }
        else
        {
          v18 = _objc_msgSend(v177, "stringValue");
          v19 = (char *)objc_retainAutoreleasedReturnValue(v18);
        }
        v190 = v19;
      }
    }
  }
  else
  {
    v190 = obj;
    v17 = v181;
  }
  v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( !(unsigned __int8)_objc_msgSend(v17, "isKindOfClass:", v20) )
    goto LABEL_13;
  _objc_msgSend(v17, "doubleValue");
  v191 = obj;
  if ( v3 != 4294967295.0 )
  {
    _objc_msgSend(v17, "doubleValue");
    v191 = obj;
    if ( v3 != 2147483648.0 )
    {
      if ( (__int64)_objc_msgSend(v17, "integerValue") >= 0 )
      {
        v21 = _objc_msgSend(v17, "stringValue");
        v22 = (char *)objc_retainAutoreleasedReturnValue(v21);
LABEL_14:
        v191 = v22;
        goto LABEL_15;
      }
LABEL_13:
      v22 = obj;
      goto LABEL_14;
    }
  }
LABEL_15:
  v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v24 = v193;
  if ( !(unsigned __int8)_objc_msgSend(v193, "isKindOfClass:", v23) )
    goto LABEL_20;
  _objc_msgSend(v193, "doubleValue");
  v192 = obj;
  if ( v3 != 4294967295.0 )
  {
    _objc_msgSend(v193, "doubleValue");
    v192 = obj;
    if ( v3 != 2147483648.0 )
    {
      if ( (__int64)_objc_msgSend(v193, "integerValue") >= 0 )
      {
        v25 = _objc_msgSend(v193, "stringValue");
        v26 = (char *)objc_retainAutoreleasedReturnValue(v25);
LABEL_21:
        v192 = v26;
        goto LABEL_22;
      }
LABEL_20:
      v26 = obj;
      goto LABEL_21;
    }
  }
LABEL_22:
  v183 = v5;
  v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v188 = obj;
  if ( (unsigned __int8)_objc_msgSend(v174, "isKindOfClass:", v27) )
  {
    _objc_msgSend(v174, "doubleValue");
    v188 = obj;
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v174, "doubleValue");
      v188 = obj;
      if ( v3 != 2147483648.0 )
      {
        _objc_msgSend(v174, "doubleValue");
        v188 = obj;
        if ( v3 >= 0.0 )
        {
          _objc_msgSend(v174, "doubleValue");
          v24 = v193;
          v28 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f%%"));
          v188 = objc_retainAutoreleasedReturnValue(v28);
        }
      }
    }
  }
  v29 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v30 = (unsigned __int8)_objc_msgSend(v176, "isKindOfClass:", v29);
  v33 = obj;
  if ( v30 )
  {
    _objc_msgSend(v32, "doubleValue", v31, obj);
    v33 = obj;
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v35, "doubleValue", v34, obj);
      v33 = obj;
      if ( v3 != 2147483648.0 )
      {
        _objc_msgSend(v37, "doubleValue", v36, obj);
        v33 = obj;
        if ( v3 != -1.0 )
        {
          _objc_msgSend(v39, "doubleValue", v38, obj);
          v24 = v193;
          v40 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f%%"));
          v33 = objc_retainAutoreleasedReturnValue(v40);
        }
      }
    }
  }
  v186 = v33;
  v41 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v189 = obj;
  if ( (unsigned __int8)_objc_msgSend(v175, "isKindOfClass:", v41) )
  {
    _objc_msgSend(v175, "doubleValue");
    v189 = obj;
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v175, v42);
      v189 = obj;
      if ( v3 != 2147483648.0 )
      {
        _objc_msgSend(v175, v43);
        v189 = obj;
        if ( v3 >= 0.0 )
        {
          _objc_msgSend(v175, v44);
          v24 = v193;
          v45 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f%%"));
          v189 = objc_retainAutoreleasedReturnValue(v45);
        }
      }
    }
  }
  v46 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v182, "isKindOfClass:", v46) )
  {
    _objc_msgSend(v182, "doubleValue");
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v182, v47);
      if ( v3 != 2147483648.0 )
      {
        _objc_msgSend(v182, v48);
        if ( v3 != -1.0 )
        {
          _objc_msgSend(v182, v49);
          v24 = v193;
          v50 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.2f%%"));
          v184 = objc_retainAutoreleasedReturnValue(v50);
        }
      }
    }
  }
  v51 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( !(unsigned __int8)_objc_msgSend(v181, "isKindOfClass:", v51)
    || (_objc_msgSend(v52, "doubleValue"), v3 == 4294967295.0)
    || (_objc_msgSend(v53, "doubleValue"), v3 == 2147483648.0) )
  {
    v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
    v194 = obj;
    if ( !(unsigned __int8)_objc_msgSend(v24, "isKindOfClass:", v54) )
      goto LABEL_70;
    _objc_msgSend(v24, "doubleValue");
    if ( v3 == 4294967295.0 )
      goto LABEL_70;
    _objc_msgSend(v24, "doubleValue");
    if ( v3 == 2147483648.0 )
      goto LABEL_70;
  }
  v55 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (unsigned __int8)_objc_msgSend(v56, "isKindOfClass:", v55) )
  {
    _objc_msgSend(v57, "doubleValue");
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v58, "doubleValue");
      if ( v3 != 2147483648.0 )
      {
        v70 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
        v194 = (char *)CFSTR("100%");
        if ( !(unsigned __int8)_objc_msgSend(v24, "isKindOfClass:", v70) )
          goto LABEL_70;
        _objc_msgSend(v24, "doubleValue");
        if ( v3 == 4294967295.0 )
          goto LABEL_70;
        _objc_msgSend(v24, "doubleValue");
        if ( v3 == 2147483648.0 )
          goto LABEL_70;
      }
    }
  }
  v59 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( (!(unsigned __int8)_objc_msgSend(v60, "isKindOfClass:", v59)
     || (_objc_msgSend(v61, "doubleValue"), v3 == 4294967295.0)
     || (_objc_msgSend(v62, "doubleValue"), v3 == 2147483648.0))
    && (v64 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class"), (unsigned __int8)_objc_msgSend(v24, "isKindOfClass:", v64))
    && (_objc_msgSend(v24, "doubleValue"), v3 != 4294967295.0)
    && (_objc_msgSend(v24, "doubleValue"), v3 != 2147483648.0) )
  {
    v69 = CFSTR("0%");
  }
  else
  {
    _objc_msgSend(v63, "doubleValue");
    _objc_msgSend(v24, "doubleValue");
    v3 = v3 + v3;
    v194 = obj;
    if ( v3 <= 0.0 )
      goto LABEL_70;
    _objc_msgSend(v65, "doubleValue");
    v178 = v3;
    _objc_msgSend(v66, "doubleValue");
    _objc_msgSend(v24, "doubleValue");
    v3 = v3 + v3;
    v67 = v178 / v3;
    if ( v178 / v3 == 2147483648.0 )
      goto LABEL_70;
    v3 = 0.0;
    if ( v67 < 0.0 || v67 == 4294967295.0 )
      goto LABEL_70;
    v3 = v67 * 100.0;
    v68 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%.0f%%"), v67 * 100.0);
    v69 = objc_retainAutoreleasedReturnValue(v68);
  }
  v194 = (char *)v69;
LABEL_70:
  v71 = _objc_msgSend(CFSTR("涨停成功次数:"), "stringByAppendingString:", v190);
  v72 = objc_retainAutoreleasedReturnValue(v71);
  v73 = -[ZhangTingJiYinContainerController chengGongCiShuTF](self, "chengGongCiShuTF");
  v74 = objc_retainAutoreleasedReturnValue(v73);
  _objc_msgSend(v74, "setStringValue:", v72);
  v75(v72);
  v76 = _objc_msgSend(CFSTR("非一字涨停成功次数:"), "stringByAppendingString:", v191);
  v77 = objc_retainAutoreleasedReturnValue(v76);
  v78 = -[ZhangTingJiYinContainerController feiYiZiCiShuTF](self, "feiYiZiCiShuTF");
  v79 = objc_retainAutoreleasedReturnValue(v78);
  _objc_msgSend(v79, "setStringValue:", v77);
  v80(v79);
  v81(v77);
  v82 = _objc_msgSend(CFSTR("涨停被砸次数:"), "stringByAppendingString:", v192);
  v83 = objc_retainAutoreleasedReturnValue(v82);
  v84 = -[ZhangTingJiYinContainerController beiZaCiShuTF](self, "beiZaCiShuTF");
  v85 = objc_retainAutoreleasedReturnValue(v84);
  _objc_msgSend(v85, "setStringValue:", v83);
  v86(v85);
  v87(v83);
  v88 = _objc_msgSend(CFSTR("非一字封板成功率:"), "stringByAppendingString:", v194);
  v89 = objc_retainAutoreleasedReturnValue(v88);
  v90 = -[ZhangTingJiYinContainerController chengGongLvTF](self, "chengGongLvTF");
  v91 = objc_retainAutoreleasedReturnValue(v90);
  _objc_msgSend(v91, "setStringValue:", v89);
  v92(v91);
  v93(v89);
  v94 = _objc_msgSend(CFSTR("高开概率(开盘):"), "stringByAppendingString:", v188);
  v95 = objc_retainAutoreleasedReturnValue(v94);
  v96 = -[ZhangTingJiYinContainerController gaoKaiLvTF](self, "gaoKaiLvTF");
  v97 = objc_retainAutoreleasedReturnValue(v96);
  _objc_msgSend(v97, "setStringValue:", v95);
  v98(v97);
  v99(v95);
  v100 = _objc_msgSend(CFSTR("上涨概率(收盘):"), "stringByAppendingString:", v189);
  v101 = objc_retainAutoreleasedReturnValue(v100);
  v102 = -[ZhangTingJiYinContainerController shangZhangLvTF](self, "shangZhangLvTF");
  v103 = objc_retainAutoreleasedReturnValue(v102);
  _objc_msgSend(v103, "setStringValue:", v101);
  v104(v103);
  v105(v101);
  v106 = _objc_msgSend(CFSTR("平均高开(开盘):"), "stringByAppendingString:", v186);
  v107 = objc_retainAutoreleasedReturnValue(v106);
  v108(v186);
  v109 = _objc_msgSend(CFSTR("平均涨幅(收盘):"), "stringByAppendingString:", v184);
  v187 = objc_retainAutoreleasedReturnValue(v109);
  v110(v184);
  v111 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v185 = v107;
  if ( !(unsigned __int8)_objc_msgSend(v176, "isKindOfClass:", v111)
    || (_objc_msgSend(v176, "doubleValue"), v3 == 4294967295.0)
    || (_objc_msgSend(v176, "doubleValue"), v3 == 2147483648.0)
    || (_objc_msgSend(v176, "doubleValue"), v3 == -1.0) )
  {
    v112 = -[ZhangTingJiYinContainerController pingJunGaoKaiTF](self, "pingJunGaoKaiTF");
    v113 = objc_retainAutoreleasedReturnValue(v112);
    _objc_msgSend(v113, "setStringValue:", v107);
  }
  else
  {
    _objc_msgSend(v176, "doubleValue");
    v152 = _objc_msgSend(v151, "getColorByJudgeNumberValue:");
    v113 = (NSTextField *)objc_retainAutoreleasedReturnValue(v152);
    v153 = objc_alloc(&OBJC_CLASS___NSMutableAttributedString);
    v154 = _objc_msgSend(v153, "initWithString:", v107);
    v155 = (char *)_objc_msgSend(v154, "length");
    _objc_msgSend(v154, "addAttribute:value:range:", v156, v113, 9LL, v155 - 9);
    v157 = -[ZhangTingJiYinContainerController pingJunGaoKaiTF](self, "pingJunGaoKaiTF");
    v158 = objc_retainAutoreleasedReturnValue(v157);
    _objc_msgSend(v158, "setAttributedStringValue:", v154);
  }
  v114 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  if ( !(unsigned __int8)_objc_msgSend(v115, "isKindOfClass:", v114)
    || (_objc_msgSend(v116, "doubleValue"), v3 == 4294967295.0)
    || (_objc_msgSend(v117, "doubleValue"), v3 == 2147483648.0)
    || (_objc_msgSend(v118, "doubleValue"), v3 == -1.0) )
  {
    v119 = -[ZhangTingJiYinContainerController pingJunZhangFuTF](self, "pingJunZhangFuTF");
    v120 = objc_retainAutoreleasedReturnValue(v119);
    v121(v120, "setStringValue:", v187);
  }
  else
  {
    _objc_msgSend(v160, "doubleValue");
    v162 = v161(&OBJC_CLASS___HXTools, "getColorByJudgeNumberValue:");
    v120 = (NSTextField *)objc_retainAutoreleasedReturnValue(v162);
    v163 = objc_alloc(&OBJC_CLASS___NSMutableAttributedString);
    v165 = v164(v163, "initWithString:", v187);
    v166 = NSForegroundColorAttributeName;
    v168 = (char *)v167(v165, "length");
    v169(v165, "addAttribute:value:range:", v166, v120, 9LL, v168 - 9);
    v171 = v170(self, "pingJunZhangFuTF");
    v172 = objc_retainAutoreleasedReturnValue(v171);
    v173(v172, "setAttributedStringValue:", v165);
  }
  v123 = v122(v183, "thsStringForKey:", CFSTR("limitup_data"));
  v179 = objc_retainAutoreleasedReturnValue(v123);
  v125 = v124(v179, "thsToJsonObj");
  v126 = objc_retainAutoreleasedReturnValue(v125);
  v127 = objc_retain(v126);
  v129 = v128(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)v130(v127, "isKindOfClass:", v129) && _objc_msgSend(v127, "count") )
  {
    v131 = -[ZhangTingJiYinContainerController sortSuYuanDataArr:](self, "sortSuYuanDataArr:", v127);
    v132 = objc_retainAutoreleasedReturnValue(v131);
    v134 = _objc_msgSend(v132, v133);
    -[ZhangTingJiYinContainerController setOriginTableCount:](self, "setOriginTableCount:", v134);
    v5 = v183;
    -[ZhangTingJiYinContainerController loadSuYuanTableData:](self, "loadSuYuanTableData:", v132);
    v135 = v127;
  }
  else
  {
    v180 = v127;
    -[ZhangTingJiYinContainerController setOriginTableCount:](self, "setOriginTableCount:", 0LL);
    v137 = _objc_msgSend(v136, "showAllBtn");
    v138 = objc_retainAutoreleasedReturnValue(v137);
    _objc_msgSend(v138, "setHidden:", 1LL);
    v139 = __NSArray0__;
    v141 = _objc_msgSend(v140, "suYuanTVC");
    v142 = objc_retainAutoreleasedReturnValue(v141);
    v143 = v139;
    v5 = v183;
    _objc_msgSend(v142, "setTableDataArr:", v143);
    _objc_msgSend(v144, "resizeTableViewHeight:", 0LL);
    v146 = _objc_msgSend(v145, "suYuanTVC");
    v147 = objc_retainAutoreleasedReturnValue(v146);
    v148 = _objc_msgSend(v147, "myTable");
    v149 = objc_retainAutoreleasedReturnValue(v148);
    _objc_msgSend(v149, "reloadData");
    v135 = v180;
  }
LABEL_83:
}

//----- (000000010070E21B) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController loadSuYuanTableData:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  ZhangTingJiYinContainerController *v3; // r14
  HXButton *v8; // rax
  HXButton *v9; // rbx
  ZhangTingSuYuanTableViewController *v15; // rax
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  ZhangTingSuYuanTableViewController *v22; // rax
  ZhangTingSuYuanTableViewController *v23; // rax
  NSArray *v24; // rax
  ZhangTingSuYuanTableViewController *v25; // rax
  NSArray *v26; // rax
  NSArray *v27; // rbx
  HXButton *v29; // rax
  HXButton *v30; // rbx
  id (*v32)(id, SEL, ...); // r12
  ZhangTingSuYuanTableViewController *v40; // rax
  NSArray *v41; // rax
  NSArray *v42; // r14
  NSArray *v44; // rdi
  ZhangTingJiYinContainerController *v45; // r12
  ZhangTingSuYuanTableViewController *v46; // rax
  ZhangTingSuYuanTableViewController *v47; // rax
  NSArray *v48; // rax
  NSArray *v49; // r13
  ZhangTingSuYuanTableViewController *v50; // rax
  NSArray *v51; // rax
  HXButton *v53; // rax
  HXButton *v54; // rbx
  HXButton *v56; // rax
  ZhangTingSuYuanTableViewController *v62; // rax
  ZhangTingSuYuanTableViewController *v63; // rbx
  ZhangTingSuYuanTableViewController *v65; // rax
  ZhangTingSuYuanTableViewController *v66; // r14
  HXBaseTableView *v67; // rax
  HXBaseTableView *v68; // rbx
  ZhangTingSuYuanTableViewController *v72; // [rsp+10h] [rbp-50h]
  ZhangTingSuYuanTableViewController *v73; // [rsp+18h] [rbp-48h]
  NSArray *v74; // [rsp+18h] [rbp-48h]
  NSArray *v75; // [rsp+20h] [rbp-40h]
  ZhangTingSuYuanTableViewController *v78; // [rsp+28h] [rbp-38h]
  ZhangTingSuYuanTableViewController *v79; // [rsp+30h] [rbp-30h]
  NSArray *v80; // [rsp+30h] [rbp-30h]
  HXButton *v81; // [rsp+30h] [rbp-30h]

  v3 = self;
  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  v6 = v4;
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) && _objc_msgSend(v4, "count") )
  {
    if ( (unsigned __int8)-[ZhangTingJiYinContainerController isRequestDiffStock](self, "isRequestDiffStock") == 1 )
    {
      -[ZhangTingJiYinContainerController setIsRequestDiffStock:](self, "setIsRequestDiffStock:", 0LL);
      v7 = _objc_msgSend(v4, "count");
      v8 = -[ZhangTingJiYinContainerController showAllBtn](self, "showAllBtn");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      if ( (unsigned __int64)v7 < 6 )
      {
        _objc_msgSend(v9, "setHidden:", 1LL);
      }
      else
      {
        v11 = v10;
        _objc_msgSend(v9, "setHidden:", 0LL);
        v13 = (void *)v12(self, v11);
        v9 = objc_retainAutoreleasedReturnValue(v13);
        v14(v9, "setTitle:", CFSTR("展开"));
      }
      v37 = _objc_msgSend(v6, "count");
      v38 = 5LL;
      if ( (unsigned __int64)v37 <= 5 )
        v38 = (__int64)_objc_msgSend(v6, "count", 5LL);
      -[ZhangTingJiYinContainerController resizeTableViewHeight:](self, "resizeTableViewHeight:", v38);
      goto LABEL_28;
    }
    v15 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
    v79 = objc_retainAutoreleasedReturnValue(v15);
    v17 = v16(v79, "tableDataArr");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v20 = v19(&OBJC_CLASS___NSArray, "class");
    v77 = v18;
    if ( (unsigned __int8)v21(v18, "isKindOfClass:", v20) )
    {
      v22 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
      v23 = objc_retainAutoreleasedReturnValue(v22);
      v24 = -[ZhangTingSuYuanTableViewController tableDataArr](v23, "tableDataArr");
      v75 = objc_retainAutoreleasedReturnValue(v24);
      if ( _objc_msgSend(v75, "count") && (unsigned __int64)_objc_msgSend(v6, "count") >= 6 )
      {
        v25 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
        v73 = objc_retainAutoreleasedReturnValue(v25);
        v26 = -[ZhangTingSuYuanTableViewController tableDataArr](v73, "tableDataArr");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v71 = _objc_msgSend(v27, "count");
        if ( (unsigned __int64)v71 <= 5 )
        {
          v29 = -[ZhangTingJiYinContainerController showAllBtn](self, "showAllBtn");
          v30 = objc_retainAutoreleasedReturnValue(v29);
          v31(v30, "setHidden:", 0LL);
          v33 = v32(self, "showAllBtn");
          v34 = objc_retainAutoreleasedReturnValue(v33);
          v35(v34, "setTitle:", CFSTR("展开"));
          v36(self, "resizeTableViewHeight:", 5LL);
LABEL_28:
          v62 = -[ZhangTingJiYinContainerController suYuanTVC](v3, "suYuanTVC");
          v63 = objc_retainAutoreleasedReturnValue(v62);
          v64(v63, "setTableDataArr:", v6);
          v65 = -[ZhangTingJiYinContainerController suYuanTVC](v3, "suYuanTVC");
          v66 = objc_retainAutoreleasedReturnValue(v65);
          v67 = -[HXBaseTableViewController myTable](v66, "myTable");
          v68 = objc_retainAutoreleasedReturnValue(v67);
          _objc_msgSend(v68, "reloadData");
          v69(v68);
          v70(v66);
          goto LABEL_29;
        }
LABEL_17:
        v40 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
        v78 = objc_retainAutoreleasedReturnValue(v40);
        v41 = -[ZhangTingSuYuanTableViewController tableDataArr](v78, "tableDataArr");
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v43 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
        v80 = v42;
        v44 = v42;
        v3 = v45;
        if ( (unsigned __int8)_objc_msgSend(v44, "isKindOfClass:", v43) )
        {
          v46 = -[ZhangTingJiYinContainerController suYuanTVC](v3, "suYuanTVC");
          v47 = objc_retainAutoreleasedReturnValue(v46);
          v48 = -[ZhangTingSuYuanTableViewController tableDataArr](v47, "tableDataArr");
          v49 = objc_retainAutoreleasedReturnValue(v48);
          if ( _objc_msgSend(v49, "count") && (unsigned __int64)_objc_msgSend(v6, "count") <= 5 )
          {
            v50 = -[ZhangTingJiYinContainerController suYuanTVC](v3, "suYuanTVC");
            v72 = objc_retainAutoreleasedReturnValue(v50);
            v51 = -[ZhangTingSuYuanTableViewController tableDataArr](v72, "tableDataArr");
            v74 = objc_retainAutoreleasedReturnValue(v51);
            v76 = _objc_msgSend(v74, "count");
            if ( (unsigned __int64)v76 >= 6 )
            {
              v53 = -[ZhangTingJiYinContainerController showAllBtn](v3, "showAllBtn");
              v54 = objc_retainAutoreleasedReturnValue(v53);
              _objc_msgSend(v54, "setHidden:", 1LL);
              goto LABEL_27;
            }
LABEL_24:
            if ( (unsigned __int64)_objc_msgSend(v6, "count") >= 6 )
            {
              v56 = -[ZhangTingJiYinContainerController showAllBtn](v3, "showAllBtn");
              v81 = objc_retainAutoreleasedReturnValue(v56);
              v57 = _objc_msgSend(v81, "title");
              v58 = objc_retainAutoreleasedReturnValue(v57);
              v59 = (unsigned __int8)_objc_msgSend(v58, "isEqualToString:", CFSTR("展开"));
              v60(v81);
              if ( v59 )
              {
                -[ZhangTingJiYinContainerController resizeTableViewHeight:](v3, "resizeTableViewHeight:", 5LL);
                goto LABEL_28;
              }
            }
LABEL_27:
            v61 = _objc_msgSend(v6, "count");
            -[ZhangTingJiYinContainerController resizeTableViewHeight:](v3, "resizeTableViewHeight:", v61);
            goto LABEL_28;
          }
        }
        goto LABEL_24;
      }
    }
    goto LABEL_17;
  }
LABEL_29:
}

//----- (000000010070E8A9) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController resizeTableViewHeight:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        unsigned __int64 a3)
{
  ZhangTingSuYuanTableViewController *v5; // rax
  ZhangTingSuYuanTableViewController *v6; // rbx
  ZhangTingSuYuanTableViewController *v8; // rax
  ZhangTingSuYuanTableViewController *v10; // rax
  unsigned __int64 v13; // r12
  ZhangTingSuYuanTableViewController *v14; // rax
  ZhangTingSuYuanTableViewController *v15; // rax
  HXBaseTableView *v16; // rax
  HXBaseTableView *v17; // rbx
  id (*v60)(id, SEL, ...); // r12

  v5 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[ZhangTingSuYuanTableViewController setTableRowCount:](v6, "setTableRowCount:", v7);
  v8 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
  *(double *)&v87 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v8));
  v9 = _objc_msgSend(v87, "myTable");
  *(_QWORD *)&v84 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend((id)v84, "rowHeight");
  v86 = v3;
  v10 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
  *((_QWORD *)&v84 + 1) = objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(*((id *)&v84 + 1), "myTable");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "intercellSpacing");
  v81 = v4;
  *(__m128d *)v83 = _mm_sub_pd(
                      (__m128d)_mm_unpacklo_epi32((__m128i)v13, (__m128i)xmmword_1010CE400),
                      (__m128d)xmmword_1010CE410);
  v85 = self;
  v14 = -[ZhangTingJiYinContainerController suYuanTVC](self, "suYuanTVC");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = -[HXBaseTableViewController myTable](v15, "myTable");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "headerView");
  v19 = (const char *)objc_retainAutoreleasedReturnValue(v18);
  v20 = (char *)v19;
  if ( v19 )
  {
    objc_msgSend_stret(&v65, v19, "frame");
    v21 = *((double *)&v66 + 1);
  }
  else
  {
    v66 = 0LL;
    v65 = 0LL;
    v21 = 0.0;
  }
  v82 = v21;
  v86 = v86 + v81;
  *(__m128d *)v83 = _mm_hadd_pd(*(__m128d *)v83, *(__m128d *)v83);
  v23 = _objc_msgSend(v85, "firstInfoView");
  v24 = (const char *)objc_retainAutoreleasedReturnValue(v23);
  v26 = (char *)v24;
  if ( v24 )
  {
    objc_msgSend_stret(&v67, v24, "frame");
    v27 = *((double *)&v68 + 1);
  }
  else
  {
    v68 = 0LL;
    v67 = 0LL;
    v27 = 0.0;
  }
  *(double *)&v87 = v27;
  v86 = v86 * *(double *)v83;
  v28 = _objc_msgSend(v25, "secondInfoView");
  v29 = (const char *)objc_retainAutoreleasedReturnValue(v28);
  v31 = (char *)v29;
  if ( v29 )
  {
    objc_msgSend_stret(&v69, v29, "frame");
    v32 = *((double *)&v70 + 1);
  }
  else
  {
    v70 = 0LL;
    v69 = 0LL;
    v32 = 0.0;
  }
  *(double *)&v87 = *(double *)&v87 + v32;
  v86 = v86 + v82;
  v33 = _objc_msgSend(v30, "thirdInfoTitleTF");
  v34 = (const char *)objc_retainAutoreleasedReturnValue(v33);
  v35 = (char *)v34;
  if ( v34 )
  {
    objc_msgSend_stret(v71, v34, "frame");
    v37 = *(double *)(v36 + 24);
  }
  else
  {
    memset(v71, 0, sizeof(v71));
    v37 = 0.0;
  }
  *(double *)&v87 = *(double *)&v87 + v37 + 8.0;
  v86 = v86 + 20.0 + *(double *)&v87;
  v39 = _objc_msgSend(v38, "view");
  v40 = (const char *)objc_retainAutoreleasedReturnValue(v39);
  v41 = (char *)v40;
  if ( v40 )
  {
    objc_msgSend_stret(&v72, v40, "frame");
    v42 = *((double *)&v73 + 1);
  }
  else
  {
    v73 = 0LL;
    v72 = 0LL;
    v42 = 0.0;
  }
  *(double *)&v87 = v42 - v86;
  if ( *(double *)&v87 != 0.0 )
  {
    v44 = _objc_msgSend(v43, "view");
    v45 = objc_retainAutoreleasedReturnValue(v44);
    v47 = _objc_msgSend(v46, "view");
    v48 = objc_retainAutoreleasedReturnValue(v47);
    v83[0] = v48;
    if ( v48 )
    {
      objc_msgSend_stret(v74, (SEL)v48, "frame");
      *(_QWORD *)&v84 = *(_QWORD *)&v74[0];
    }
    else
    {
      memset(v74, 0, sizeof(v74));
      *(_QWORD *)&v84 = 0LL;
    }
    v49 = _objc_msgSend(v85, "view");
    v50 = (const char *)objc_retainAutoreleasedReturnValue(v49);
    v52 = (char *)v50;
    if ( v50 )
    {
      objc_msgSend_stret(v75, v50, v51);
      v53 = *((_QWORD *)&v75[0] + 1);
    }
    else
    {
      memset(v75, 0, sizeof(v75));
      v53 = 0LL;
    }
    *((_QWORD *)&v84 + 1) = v53;
    v54 = _objc_msgSend(v85, "view");
    v55 = (const char *)objc_retainAutoreleasedReturnValue(v54);
    v57 = (char *)v55;
    if ( v55 )
    {
      objc_msgSend_stret(&v76, v55, v56);
      v58 = v77;
    }
    else
    {
      v77 = 0LL;
      v76 = 0LL;
      v58 = 0LL;
    }
    v78 = v84;
    v79 = v58;
    v80 = v86;
    _objc_msgSend(v45, "setFrame:");
    v59 = v85;
    v61 = v60(v85, "viewResizeCallBackBlock");
    v62 = objc_retainAutoreleasedReturnValue(v61);
    if ( v62 )
    {
      v63 = _objc_msgSend(v59, "viewResizeCallBackBlock");
      v64 = (void (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v63);
      v64[2](v64, *(double *)&v87);
    }
  }
}

//----- (000000010070EE8A) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController resetAllViewState](ZhangTingJiYinContainerController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  id (*v43)(id, SEL, ...); // r12
  id (*v46)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  id (*v53)(id, SEL, ...); // r12
  id (*v58)(id, SEL, ...); // r12
  id (*v63)(id, SEL, ...); // r12
  id (*v68)(id, SEL, ...); // r12
  id (*v71)(id, SEL, ...); // r12

  v2 = _objc_msgSend(CFSTR("涨停成功次数:"), "stringByAppendingString:", obj);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "chengGongCiShuTF");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setStringValue:", v3);
  v9 = v8(CFSTR("非一字涨停成功次数:"), "stringByAppendingString:", obj);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(self, "feiYiZiCiShuTF");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v13, "setStringValue:", v10);
  v16 = v15(CFSTR("涨停被砸次数:"), "stringByAppendingString:");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = v18(self, "beiZaCiShuTF");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21(v20, "setStringValue:", v17);
  v23 = v22(CFSTR("非一字封板成功率:"), "stringByAppendingString:", obj);
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v26 = v25(self, "chengGongLvTF");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28(v27, "setStringValue:", v24);
  v30 = v29(CFSTR("高开概率(开盘):"), "stringByAppendingString:", obj);
  v31 = objc_retainAutoreleasedReturnValue(v30);
  v33 = v32(self, "gaoKaiLvTF");
  v34 = objc_retainAutoreleasedReturnValue(v33);
  v35(v34, "setStringValue:", v31);
  v37 = v36(CFSTR("平均高开(开盘):"), "stringByAppendingString:", obj);
  v38 = objc_retainAutoreleasedReturnValue(v37);
  v40 = v39(self, "pingJunGaoKaiTF");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  v42(v41, "setStringValue:", v38);
  v44 = v43(CFSTR("上涨概率(收盘):"), "stringByAppendingString:", obj);
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v47 = v46(self, "shangZhangLvTF");
  v48 = objc_retainAutoreleasedReturnValue(v47);
  v49(v48, "setStringValue:", v45);
  v51 = v50(CFSTR("平均涨幅(收盘):"), "stringByAppendingString:", obj);
  v52 = objc_retainAutoreleasedReturnValue(v51);
  v54 = v53(self, "pingJunZhangFuTF");
  v55 = objc_retainAutoreleasedReturnValue(v54);
  v56(v55, "setStringValue:", v52);
  v57(self, "setOriginTableCount:", 0LL);
  v59 = v58(self, "showAllBtn");
  v60 = objc_retainAutoreleasedReturnValue(v59);
  v61(v60, "setHidden:", 1LL);
  v62 = __NSArray0__;
  v64 = v63(self, "suYuanTVC");
  v65 = objc_retainAutoreleasedReturnValue(v64);
  v66(v65, "setTableDataArr:", v62);
  v67(self, "resizeTableViewHeight:", 0LL);
  v69 = v68(self, "suYuanTVC");
  v70 = objc_retainAutoreleasedReturnValue(v69);
  v72 = v71(v70, "myTable");
  v73 = objc_retainAutoreleasedReturnValue(v72);
  v74(v73, "reloadData");
}

//----- (000000010070F25F) ----------------------------------------------------
id __cdecl -[ZhangTingJiYinContainerController sortSuYuanDataArr:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v3, "sortedArrayUsingComparator:", &stru_1012E4C48);
    v6 = objc_retainAutoreleasedReturnValue(v5);
  }
  else
  {
    v6 = objc_retain(__NSArray0__);
  }
  v7 = v6;
  return objc_autoreleaseReturnValue(v7);
}

//----- (000000010070F2FA) ----------------------------------------------------
signed __int64 __cdecl sub_10070F2FA(id a1, id a2, id a3)
{

  objc_retain(a3);
  v3 = _objc_msgSend(a2, "thsStringForKey:", CFSTR("date"));
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "thsStringForKey:", CFSTR("date"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = 0LL;
  if ( v4 && v7 )
    v9 = _objc_msgSend(v7, "compare:", v4);
  v10(v4);
  return (signed __int64)v9;
}

//----- (000000010070F3AE) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setStockCode:](ZhangTingJiYinContainerController *self, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", *(_QWORD *)&self->super.super._shouldRefresh) )
  {
    objc_storeStrong((id *)&self->super.super._shouldRefresh, a3);
    LOBYTE(self->super.super.super._reserved) = 1;
  }
}

//----- (000000010070F415) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setMarket:](ZhangTingJiYinContainerController *self, SEL a2, id a3)
{

  v4 = objc_retain(a3);
  if ( !(unsigned __int8)_objc_msgSend(v4, "isEqualToString:", self->super.super._stockCode) )
  {
    objc_storeStrong((id *)&self->super.super._stockCode, a3);
    LOBYTE(self->super.super.super._reserved) = 1;
  }
}

//----- (000000010070F47C) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController showAllBtnClicked:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  ZhangTingJiYinContainerController *v12; // rbx
  bool v17; // zf
  ZhangTingSuYuanTableViewController *v18; // rax
  ZhangTingSuYuanTableViewController *v19; // r15
  HXBaseTableView *v20; // rax
  HXBaseTableView *v21; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
  {
    v6 = objc_retain(v5);
    v7 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
    {
      v8 = v6;
      v9 = _objc_msgSend(v6, "title");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = (unsigned __int8)_objc_msgSend(v10, "isEqualToString:", CFSTR("展开"));
      if ( v11 )
      {
        v6 = v8;
        _objc_msgSend(v8, "setTitle:", CFSTR("收起"));
        v12 = self;
        v13 = -[ZhangTingJiYinContainerController originTableCount](self, "originTableCount");
        -[ZhangTingJiYinContainerController resizeTableViewHeight:](self, "resizeTableViewHeight:", v13);
LABEL_7:
        v18 = -[ZhangTingJiYinContainerController suYuanTVC](v12, "suYuanTVC");
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v20 = -[HXBaseTableViewController myTable](v19, "myTable");
        v21 = objc_retainAutoreleasedReturnValue(v20);
        _objc_msgSend(v21, "reloadData");
        goto LABEL_8;
      }
      v14 = _objc_msgSend(v8, "title");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = (unsigned __int8)_objc_msgSend(v15, "isEqualToString:", CFSTR("收起"));
      v17 = v16 == 0;
      v6 = v8;
      v12 = self;
      if ( !v17 )
      {
        _objc_msgSend(v8, "setTitle:", CFSTR("展开"));
        -[ZhangTingJiYinContainerController resizeTableViewHeight:](self, "resizeTableViewHeight:", 5LL);
        goto LABEL_7;
      }
    }
LABEL_8:
  }
}

//----- (000000010070F651) ----------------------------------------------------
id __cdecl -[ZhangTingJiYinContainerController stockCode](ZhangTingJiYinContainerController *self, SEL a2)
{
  return objc_getProperty(self, a2, 120LL, 0);
}

//----- (000000010070F664) ----------------------------------------------------
id __cdecl -[ZhangTingJiYinContainerController market](ZhangTingJiYinContainerController *self, SEL a2)
{
  return objc_getProperty(self, a2, 128LL, 0);
}

//----- (000000010070F677) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController titleLabel](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._market);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F690) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setTitleLabel:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._market, a3);
}

//----- (000000010070F6A4) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangTingJiYinContainerController firstInfoView](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._contentsObjMArr);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F6BD) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setFirstInfoView:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._contentsObjMArr, a3);
}

//----- (000000010070F6D1) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController chengGongCiShuTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._controllerID);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F6EA) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setChengGongCiShuTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._controllerID, a3);
}

//----- (000000010070F6FE) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController feiYiZiCiShuTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._paramsMDic);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F717) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setFeiYiZiCiShuTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._paramsMDic, a3);
}

//----- (000000010070F72B) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController beiZaCiShuTF](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._noRecordFrame);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F744) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setBeiZaCiShuTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._noRecordFrame, a3);
}

//----- (000000010070F758) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController chengGongLvTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._titleContainerView);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F771) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setChengGongLvTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._titleContainerView, a3);
}

//----- (000000010070F785) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangTingJiYinContainerController secondInfoView](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._correlatedCode);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F79E) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setSecondInfoView:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._correlatedCode, a3);
}

//----- (000000010070F7B2) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController secondInfoTitleTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._refeshDitailsPageBlock);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F7CB) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setSecondInfoTitleTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super._refeshDitailsPageBlock, a3);
}

//----- (000000010070F7DF) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController gaoKaiLvTF](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._changeFrameBtnClickedBlock);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F7F8) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setGaoKaiLvTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super._changeFrameBtnClickedBlock, a3);
}

//----- (000000010070F80C) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController pingJunGaoKaiTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._viewResizeCallBackBlock);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F825) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setPingJunGaoKaiTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super._viewResizeCallBackBlock, a3);
}

//----- (000000010070F839) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController shangZhangLvTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._modularVisibleType);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F852) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setShangZhangLvTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._modularVisibleType, a3);
}

//----- (000000010070F866) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController pingJunZhangFuTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_isRequestDiffStock);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F87F) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setPingJunZhangFuTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->_isRequestDiffStock, a3);
}

//----- (000000010070F893) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangTingJiYinContainerController thirdInfoView](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_stockCode);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F8AC) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setThirdInfoView:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->_stockCode, a3);
}

//----- (000000010070F8C0) ----------------------------------------------------
NSTextField *__cdecl -[ZhangTingJiYinContainerController thirdInfoTitleTF](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_market);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F8D9) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setThirdInfoTitleTF:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->_market, a3);
}

//----- (000000010070F8ED) ----------------------------------------------------
HXBaseView *__cdecl -[ZhangTingJiYinContainerController suYuanContainerView](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_titleLabel);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F906) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setSuYuanContainerView:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->_titleLabel, a3);
}

//----- (000000010070F91A) ----------------------------------------------------
HXButton *__cdecl -[ZhangTingJiYinContainerController showAllBtn](ZhangTingJiYinContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_firstInfoView);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010070F933) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setShowAllBtn:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->_firstInfoView, a3);
}

//----- (000000010070F947) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setSuYuanTVC:](ZhangTingJiYinContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_chengGongCiShuTF, a3);
}

//----- (000000010070F95B) ----------------------------------------------------
unsigned __int64 __cdecl -[ZhangTingJiYinContainerController originTableCount](
        ZhangTingJiYinContainerController *self,
        SEL a2)
{
  return (unsigned __int64)self->_feiYiZiCiShuTF;
}

//----- (000000010070F96C) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setOriginTableCount:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        unsigned __int64 a3)
{
  self->_feiYiZiCiShuTF = (NSTextField *)a3;
}

//----- (000000010070F97D) ----------------------------------------------------
char __cdecl -[ZhangTingJiYinContainerController isRequestDiffStock](ZhangTingJiYinContainerController *self, SEL a2)
{
  return self->super.super.super._reserved;
}

//----- (000000010070F98E) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController setIsRequestDiffStock:](
        ZhangTingJiYinContainerController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super.super.super._reserved) = a3;
}

//----- (000000010070F99E) ----------------------------------------------------
void __cdecl -[ZhangTingJiYinContainerController .cxx_destruct](ZhangTingJiYinContainerController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_chengGongCiShuTF, 0LL);
  objc_destroyWeak((id *)&self->_firstInfoView);
  objc_destroyWeak((id *)&self->_titleLabel);
  objc_destroyWeak((id *)&self->_market);
  objc_destroyWeak((id *)&self->_stockCode);
  objc_destroyWeak((id *)&self->_isRequestDiffStock);
  objc_destroyWeak((id *)&self->super._modularVisibleType);
  objc_destroyWeak(&self->super._viewResizeCallBackBlock);
  objc_destroyWeak(&self->super._changeFrameBtnClickedBlock);
  objc_destroyWeak(&self->super._refeshDitailsPageBlock);
  objc_destroyWeak((id *)&self->super._correlatedCode);
  objc_destroyWeak((id *)&self->super._titleContainerView);
  objc_destroyWeak((id *)&self->super._noRecordFrame);
  objc_destroyWeak((id *)&self->super.super._paramsMDic);
  objc_destroyWeak((id *)&self->super.super._controllerID);
  objc_destroyWeak((id *)&self->super.super._contentsObjMArr);
  objc_destroyWeak((id *)&self->super.super._market);
  objc_storeStrong((id *)&self->super.super._stockCode, 0LL);
  objc_storeStrong((id *)&self->super.super._shouldRefresh, 0LL);
}

