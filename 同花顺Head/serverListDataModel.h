//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSString;

@interface serverListDataModel : NSObject
{
    NSString *_strBrokerName;
    NSString *_strYybid;
    NSString *_strQsid;
    NSString *_strYybrid;
    NSString *_strVersion;
    NSString *_strAuthType;
    NSString *_strAccountType;
    NSString *_strConnectMode;
    NSString *_strMacVersion;
    NSString *_strMacLoginVersion;
    NSString *_stryybFunc;
    NSString *_strMacKernel;
}


@property(copy, nonatomic) NSString *strMacKernel; // @synthesize strMacKernel=_strMacKernel;
@property(copy, nonatomic) NSString *stryybFunc; // @synthesize stryybFunc=_stryybFunc;
@property(copy, nonatomic) NSString *strMacLoginVersion; // @synthesize strMacLoginVersion=_strMacLoginVersion;
@property(copy, nonatomic) NSString *strMacVersion; // @synthesize strMacVersion=_strMacVersion;
@property(copy, nonatomic) NSString *strConnectMode; // @synthesize strConnectMode=_strConnectMode;
@property(copy, nonatomic) NSString *strAccountType; // @synthesize strAccountType=_strAccountType;
@property(copy, nonatomic) NSString *strAuthType; // @synthesize strAuthType=_strAuthType;
@property(copy, nonatomic) NSString *strVersion; // @synthesize strVersion=_strVersion;
@property(copy, nonatomic) NSString *strYybrid; // @synthesize strYybrid=_strYybrid;
@property(copy, nonatomic) NSString *strQsid; // @synthesize strQsid=_strQsid;
@property(copy, nonatomic) NSString *strYybid; // @synthesize strYybid=_strYybid;
@property(copy, nonatomic) NSString *strBrokerName; // @synthesize strBrokerName=_strBrokerName;
- (id)initWithName:(id)arg1 TypeData:(id)arg2 TypeValue:(id)arg3;
- (void)resetData;

@end

