//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

@class HXBaseView, NSArray, NSString, ZiXunWindowContentVC;

@interface ZiXunWindowContianerVC : NSViewController
{
    HXBaseView *_titleView;
    HXBaseView *_contentView;
    NSString *_stockCode;
    NSString *_market;
    NSArray *_menuBtnArr;
    NSArray *_reqParamArr;
    ZiXunWindowContentVC *_contentVC;
}


@property(retain, nonatomic) ZiXunWindowContentVC *contentVC; // @synthesize contentVC=_contentVC;
@property(retain, nonatomic) NSArray *reqParamArr; // @synthesize reqParamArr=_reqParamArr;
@property(retain, nonatomic) NSArray *menuBtnArr; // @synthesize menuBtnArr=_menuBtnArr;
@property(retain, nonatomic) NSString *market; // @synthesize market=_market;
@property(retain, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
@property __weak HXBaseView *contentView; // @synthesize contentView=_contentView;
@property __weak HXBaseView *titleView; // @synthesize titleView=_titleView;
- (void)menuBtnClicked:(id)arg1;
- (void)setMenuBtnStateByTag:(long long)arg1;
- (void)setMenuBtnStateByTitle:(id)arg1;
- (id)getBtnByTitle:(id)arg1 index:(unsigned long long)arg2;
- (void)refreshMenuBtn:(id)arg1;
- (void)sendMaiDian:(id)arg1;
- (void)resetSelection;
- (void)setSelectedMenuTitle:(id)arg1;
- (void)setSelectedItem:(id)arg1;
- (void)reloadContentViewWithStockCode:(id)arg1 market:(id)arg2;
- (void)initViewState;
- (void)viewDidLoad;

@end

