void __cdecl -[ZiXunWindowContentVC viewDidLoad](ZiXunWindowContentVC *self, SEL a2)
{
  _QWORD v6[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[4]; // [rsp+40h] [rbp-20h] BYREF

  v8.receiver = self;
  v8.super_class = (Class)&OBJC_CLASS___ZiXunWindowContentVC;
  objc_msgSendSuper2(&v8, "viewDidLoad");
  -[ZiXunWindowContentVC initViewState](self, "initViewState");
  v2 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "requestZiXunTree");
  objc_initWeak(location, self);
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6[0] = _NSConcreteStackBlock;
  v6[1] = 3254779904LL;
  v6[2] = sub_100383226;
  v6[3] = &unk_1012DAA10;
  objc_copyWeak(&to, location);
  _objc_msgSend(v5, "setViewDidChangeEffectiveAppearanceBlock:", v6);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100383226) ----------------------------------------------------
void __fastcall sub_100383226(__int64 a1)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = objc_retain(WeakRetained);
  v3 = _objc_msgSend(v2, "selectedItemModel");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v2, "loadModelContent:", v4);
}

//----- (000000010038329C) ----------------------------------------------------
HXReuseScrollView *__cdecl -[ZiXunWindowContentVC scrollView](ZiXunWindowContentVC *self, SEL a2)
{
  NSString *designNibBundleIdentifier; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi
  SEL v8; // r12
  HXBaseView *v10; // rax
  HXBaseView *v11; // rax
  HXBaseView *v12; // rax
  HXBaseView *v13; // r13
  HXBaseView *v15; // rax
  HXBaseView *v16; // rbx

  designNibBundleIdentifier = self->super._designNibBundleIdentifier;
  if ( !designNibBundleIdentifier )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXReuseScrollView);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super._designNibBundleIdentifier;
    self->super._designNibBundleIdentifier = v5;
    _objc_msgSend(self->super._designNibBundleIdentifier, "setDelegate:", self);
    _objc_msgSend(self->super._designNibBundleIdentifier, "setDataSource:", self);
    v7 = objc_alloc((Class)&OBJC_CLASS___HXScroller);
    v9 = _objc_msgSend(v7, v8);
    _objc_msgSend(v9, "setControlSize:", 2LL);
    v28 = v9;
    _objc_msgSend(self->super._designNibBundleIdentifier, "setVerticalScroller:", v9);
    _objc_msgSend(self->super._designNibBundleIdentifier, "setHasVerticalScroller:", 1LL);
    _objc_msgSend(self->super._designNibBundleIdentifier, "setVerticalScrollElasticity:", 1LL);
    v10 = -[ZiXunWindowContentVC itemListView](self, "itemListView");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "width");
    v12 = -[ZiXunWindowContentVC itemListView](self, "itemListView");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "height");
    _objc_msgSend(self->super._designNibBundleIdentifier, "setFrame:");
    v15 = -[ZiXunWindowContentVC itemListView](self, "itemListView");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    _objc_msgSend(v16, "addSubview:", self->super._designNibBundleIdentifier);
    v17(v16);
    _objc_msgSend(self->super._designNibBundleIdentifier, "setAutoresizingMask:", 63LL);
    v18 = _objc_msgSend(self->super._designNibBundleIdentifier, "documentView");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = _objc_msgSend(&OBJC_CLASS___HXFlipView, "class");
    LOBYTE(v13) = (unsigned __int8)_objc_msgSend(v19, "isKindOfClass:", v20);
    if ( (_BYTE)v13 )
    {
      v22 = _objc_msgSend(self->super._designNibBundleIdentifier, v21);
      objc_retainAutoreleasedReturnValue(v22);
      v23 = +[HXThemeManager callAuctionBgColor](&OBJC_CLASS___HXThemeManager, "callAuctionBgColor");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      _objc_msgSend(v25, "setBackgroundColor:", v24);
    }
    designNibBundleIdentifier = self->super._designNibBundleIdentifier;
  }
  return (HXReuseScrollView *)objc_retainAutoreleaseReturnValue(designNibBundleIdentifier);
}

//----- (0000000100383541) ----------------------------------------------------
MajorEventRequestModule *__cdecl -[ZiXunWindowContentVC majorEventReqModule](ZiXunWindowContentVC *self, SEL a2)
{

  v3 = *(void **)&self->super._viewIsAppearing;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___MajorEventRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._viewIsAppearing;
    *(_QWORD *)&self->super._viewIsAppearing = v5;
    v3 = *(void **)&self->super._viewIsAppearing;
  }
  return (MajorEventRequestModule *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100383592) ----------------------------------------------------
ZiXunRequestModule *__cdecl -[ZiXunWindowContentVC ziXunReqModule](ZiXunWindowContentVC *self, SEL a2)
{

  v3 = *(void **)&self->super._isContentViewController;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._isContentViewController;
    *(_QWORD *)&self->super._isContentViewController = v5;
    _objc_msgSend(*(id *)&self->super._isContentViewController, "setDelegate:", self);
    _objc_msgSend(*(id *)&self->super._isContentViewController, "setNeedWaiting:", 0LL);
    v3 = *(void **)&self->super._isContentViewController;
  }
  return (ZiXunRequestModule *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100383611) ----------------------------------------------------
HXBaseWKWebViewController *__cdecl -[ZiXunWindowContentVC webVC](ZiXunWindowContentVC *self, SEL a2)
{

  v3 = *(void **)&self->_isRequestByMenuTitle;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunWKWebViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("HXBaseWKWebViewController"), 0LL);
    v6 = *(void **)&self->_isRequestByMenuTitle;
    *(_QWORD *)&self->_isRequestByMenuTitle = v5;
    v3 = *(void **)&self->_isRequestByMenuTitle;
  }
  return (HXBaseWKWebViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010038366B) ----------------------------------------------------
MajorEventContentVC *__cdecl -[ZiXunWindowContentVC majorEventVC](ZiXunWindowContentVC *self, SEL a2)
{
  NSString *stockCode; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  stockCode = self->_stockCode;
  if ( !stockCode )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___MajorEventContentVC);
    v5 = (NSString *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("MajorEventContentVC"), 0LL);
    v6 = self->_stockCode;
    self->_stockCode = v5;
    stockCode = self->_stockCode;
  }
  return (MajorEventContentVC *)objc_retainAutoreleaseReturnValue(stockCode);
}

//----- (00000001003836C5) ----------------------------------------------------
HXTabbarController *__cdecl -[ZiXunWindowContentVC tabBarController](ZiXunWindowContentVC *self, SEL a2)
{
  NSArray *v16; // rax
  NSArray *v17; // r15
  _QWORD v20[2]; // [rsp+0h] [rbp-40h] BYREF

  v3 = *(void **)&self->super._reserved;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._reserved;
    *(_QWORD *)&self->super._reserved = v5;
    v8 = (void *)v7(self, "contentView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(*(_QWORD *)&self->super._reserved, "setView:", v9);
    v12 = (void *)v11(self, "webVC");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v20[0] = v13;
    v15 = (void *)v14(self, "majorEventVC");
    v20[1] = objc_retainAutoreleasedReturnValue(v15);
    v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v20, 2LL);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(*(id *)&self->super._reserved, "setViewControllers:", v17);
    v3 = *(void **)&self->super._reserved;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (000000010038381D) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC requestDataByType:params:](
        ZiXunWindowContentVC *self,
        SEL a2,
        unsigned __int64 a3,
        id a4)
{
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12

  v5 = objc_retain(a4);
  -[ZiXunWindowContentVC clearCache](self, "clearCache");
  v6(self, "setModelNeedToScroll:", 0LL);
  v7(self, "setIsRequestByMenuTitle:", 1LL);
  v8(self, "setCurReqType:", a3);
  v9(self, "switchContentViewState:", a3 == 1);
  if ( a3 == 1 )
    -[ZiXunWindowContentVC requestMajorEvent](self, "requestMajorEvent");
  else
    -[ZiXunWindowContentVC requestNormalZiXun:](self, "requestNormalZiXun:", v5);
  v11 = v10(self, "scrollView");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = v13(v12, "documentView");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16(v15, "scrollPoint:", 0.0, 0.0);
  v17(v12);
  v18(v5);
}

//----- (0000000100383926) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC requestDataBySelectedItem:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  ZiXunReqParamsManager *v14; // rax
  NSNumber *v21; // rax
  NSNumber *v22; // rax
  NSNumber *v23; // r15
  NSDictionary *v24; // rax
  NSDictionary *v25; // rbx
  ZiXunReqParamsManager *v31; // [rsp+18h] [rbp-58h]
  NSNumber *v32; // [rsp+18h] [rbp-58h]
  _QWORD v33[2]; // [rsp+20h] [rbp-50h] BYREF
  _QWORD v34[2]; // [rsp+30h] [rbp-40h] BYREF

  v3 = objc_retain(a3);
  _objc_msgSend(v4, "clearCache");
  _objc_msgSend(v5, "setModelNeedToScroll:", v3);
  _objc_msgSend(v6, "setIsRequestByMenuTitle:", 0LL);
  v7 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v7) )
  {
    _objc_msgSend(v8, "setCurReqType:", 1LL);
    _objc_msgSend(v9, "switchContentViewState:", 1LL);
    _objc_msgSend(v10, "requestMajorEvent");
  }
  else
  {
    v11 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
    if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v11) )
    {
      _objc_msgSend(v12, "setCurReqType:", 0LL);
      _objc_msgSend(v13, "switchContentViewState:", 0LL);
      v27 = objc_retain(v3);
      v14 = +[ZiXunReqParamsManager sharedInstance](&OBJC_CLASS___ZiXunReqParamsManager, "sharedInstance");
      v31 = objc_retainAutoreleasedReturnValue(v14);
      v29 = _objc_msgSend(v27, "ziXunType");
      v16 = _objc_msgSend(v15, "stockCode");
      v28 = objc_retainAutoreleasedReturnValue(v16);
      v18 = _objc_msgSend(v17, "market");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v30 = -[ZiXunReqParamsManager getNewsRequestTypeByZiXunType:stockCode:market:](
              v31,
              "getNewsRequestTypeByZiXunType:stockCode:market:",
              v29,
              v28,
              v19);
      v33[0] = off_1012E2C20[0];
      v20 = _objc_msgSend(v27, "ziXunType");
      v21 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v20);
      v32 = objc_retainAutoreleasedReturnValue(v21);
      v34[0] = v32;
      v33[1] = off_1012E2C28;
      v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v30);
      v23 = objc_retainAutoreleasedReturnValue(v22);
      v34[1] = v23;
      v24 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v34, v33, 2LL);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      _objc_msgSend(v26, "requestNormalZiXun:", v25);
    }
  }
}

//----- (0000000100383BE2) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC clearCache](ZiXunWindowContentVC *self, SEL a2)
{

  v2 = __NSArray0__;
  -[ZiXunWindowContentVC setCurDataArr:](self, "setCurDataArr:", __NSArray0__);
  -[ZiXunWindowContentVC setMajorEventArr:](self, "setMajorEventArr:", v2);
}

//----- (0000000100383C29) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC initViewState](ZiXunWindowContentVC *self, SEL a2)
{

  v2 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "itemListView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setBackgroundColor:", v3);
  v8 = _objc_msgSend(v7, "itemListView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setRightBorder:", 1LL);
  v10 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = _objc_msgSend(v12, "itemListView");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setBorderColor:", v11);
  v16 = _objc_msgSend(v15, "itemListView");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  _objc_msgSend(v17, "setBorderWidth:", 1.0);
  v18 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = _objc_msgSend(v20, "contentView");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v22, "setBackgroundColor:", v19);
  v24 = _objc_msgSend(v23, "meTitleView");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  _objc_msgSend(v25, "setBottomBorder:", 1LL);
  v27 = _objc_msgSend(v26, "meTitleView");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  _objc_msgSend(v28, "setBorderWidth:", 1.0);
  v29 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v32 = _objc_msgSend(v31, "meTitleView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  _objc_msgSend(v33, "setBorderColor:", v30);
  v35 = v34;
  _objc_msgSend(v34, "initMenuBtns");
  v36 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  v39 = _objc_msgSend(v38, "noStocksTipView");
  v40 = objc_retainAutoreleasedReturnValue(v39);
  _objc_msgSend(v40, "setBackgroundColor:", v37);
  v42 = _objc_msgSend(v41, "noStocksTipBtn");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  v44 = _objc_msgSend(v43, "cell");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  _objc_msgSend(v45, "setHighlightsBy:", 0LL);
  v47 = _objc_msgSend(v35, "noStocksTipBtn");
  v48 = objc_retainAutoreleasedReturnValue(v47);
  v49 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v50 = objc_retainAutoreleasedReturnValue(v49);
  _objc_msgSend(v48, "setTextColor:", v50);
}

//----- (0000000100383F79) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC initMenuBtns](ZiXunWindowContentVC *self, SEL a2)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  NSArray *v6; // rax
  NSArray *v7; // rax
  HXBaseView *v29; // rax
  HXBaseView *v30; // rbx
  unsigned __int64 v35; // [rsp+138h] [rbp-98h]

  v2 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
  v33 = objc_retainAutoreleasedReturnValue(v2);
  v35 = 0LL;
  while ( 1 )
  {
    v37[0] = (__int64)CFSTR("全部");
    v37[1] = (__int64)CFSTR("重大事件");
    v37[2] = (__int64)CFSTR("发布公告");
    v37[3] = (__int64)CFSTR("业绩披露");
    v37[4] = (__int64)CFSTR("交易提示");
    v37[5] = (__int64)CFSTR("交易行为");
    v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v37, 6LL);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v34 = _objc_msgSend(v4, "count");
    if ( (unsigned __int64)v34 <= v35 )
      break;
    v36[0] = (__int64)CFSTR("全部");
    v36[1] = (__int64)CFSTR("重大事件");
    v36[2] = (__int64)CFSTR("发布公告");
    v36[3] = v5;
    v36[4] = (__int64)CFSTR("交易提示");
    v36[5] = (__int64)CFSTR("交易行为");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v36, 6LL);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "thsStringAtIndex:", v35);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( v9 )
    {
      v12 = objc_alloc((Class)&OBJC_CLASS___HXButton);
      v13 = _objc_msgSend(v12, "init");
      _objc_msgSend(v13, "setTitle:", v9);
      _objc_msgSend(v13, "setTag:", v35);
      _objc_msgSend(v13, "setBordered:", 0LL);
      v14 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      _objc_msgSend(v13, "setBorderColor:", v15);
      v16(v15);
      _objc_msgSend(v13, "setBorderWidth:", 1.0);
      _objc_msgSend(v13, "setAllBorder:", 1LL);
      _objc_msgSend(v13, "setCanBeSelected:", 1LL);
      v17 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      _objc_msgSend(v13, "setTextColorDefault:", v18);
      v19(v18);
      v20 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      _objc_msgSend(v13, "setBackgroundColor:", v21);
      v22(v21);
      v23 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      _objc_msgSend(v13, "setBackgroundColorSelected:", v24);
      v25(v24);
      _objc_msgSend(v13, "setWantsLayer:", 1LL);
      v26 = _objc_msgSend(v13, "layer");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      _objc_msgSend(v27, "setCornerRadius:", 3.0);
      v28(v27);
      _objc_msgSend(v13, "setTarget:", self);
      _objc_msgSend(v13, "setAction:", "meMenuBtnClicked:");
      ++v35;
      _objc_msgSend(v13, "setFrame:");
      v29 = -[ZiXunWindowContentVC meTitleView](self, "meTitleView");
      v30 = objc_retainAutoreleasedReturnValue(v29);
      _objc_msgSend(v30, "addSubview:", v13);
      v31(v30);
      _objc_msgSend(v33, "addObject:", v13);
      v32(v13);
    }
    else
    {
      ++v35;
    }
    ((void (__fastcall *)(id))v11)(v9);
  }
  if ( _objc_msgSend(v33, "count") )
    -[ZiXunWindowContentVC setMeMenuBtnArr:](self, "setMeMenuBtnArr:", v33);
}

//----- (000000010038458B) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC switchContentViewState:](ZiXunWindowContentVC *self, SEL a2, char a3)
{
  HXBaseView *v7; // rax
  HXBaseView *v8; // r14

  LODWORD(v53) = a3;
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "subviews");
  objc_retainAutoreleasedReturnValue(v6);
  v50 = self;
  v7 = -[ZiXunWindowContentVC meTitleView](self, "meTitleView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = (unsigned __int8)_objc_msgSend(v9, "containsObject:", v8);
  if ( (_BYTE)v53 )
  {
    if ( !v10 )
    {
      v12 = _objc_msgSend(v50, "view");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v15 = _objc_msgSend(v14, "meTitleView");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      _objc_msgSend(v13, "addSubview:", v16);
      v18 = _objc_msgSend(v17, "view");
      v51 = objc_retainAutoreleasedReturnValue(v18);
      _objc_msgSend(v51, "width");
      v52 = v3;
      v20 = _objc_msgSend(v19, "view");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      _objc_msgSend(v21, "height");
      v23 = _objc_msgSend(v22, "meTitleView");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      _objc_msgSend(v24, "height");
      v26 = _objc_msgSend(v25, "containerView");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      _objc_msgSend(v27, "setFrame:");
      v29 = _objc_msgSend(v28, "containerView");
      v53 = objc_retainAutoreleasedReturnValue(v29);
      _objc_msgSend(v53, "tail");
      v31 = _objc_msgSend(v30, "view");
      v32 = objc_retainAutoreleasedReturnValue(v31);
      _objc_msgSend(v32, "width");
      v34 = _objc_msgSend(v33, "meTitleView");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      _objc_msgSend(v35, "height");
      v37 = _objc_msgSend(v36, "meTitleView");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      _objc_msgSend(v38, "setFrame:");
    }
  }
  else
  {
    if ( v10 )
    {
      v40 = _objc_msgSend(v50, "meTitleView");
      v41 = objc_retainAutoreleasedReturnValue(v40);
      _objc_msgSend(v41, "removeFromSuperview");
      v42 = _objc_msgSend(v50, "view");
      v43 = (const char *)objc_retainAutoreleasedReturnValue(v42);
      v44 = v43;
      if ( v43 )
        objc_msgSend_stret(v49, v43, "bounds");
      else
        memset(v49, 0, sizeof(v49));
      v45 = _objc_msgSend(v50, "containerView");
      v46 = objc_retainAutoreleasedReturnValue(v45);
      _objc_msgSend(v46, "setFrame:");
      v47(v46);
      v48(v44);
    }
  }
}

//----- (00000001003849A3) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC scrollListViewIfNeeded](ZiXunWindowContentVC *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  NSArray *v10; // rax
  NSArray *v11; // r14
  SEL v28; // r12
  NSArray *v32; // rax
  NSArray *v33; // rbx
  unsigned __int64 v35; // r13
  NSArray *v36; // rax
  NSArray *v37; // rax
  NSArray *v48; // rax
  NSArray *v49; // rbx
  NSArray *v56; // rax
  NSArray *v57; // rbx
  SEL v70; // r12
  NSArray *v74; // rax
  NSArray *v75; // r15
  HXReuseScrollView *v78; // rax
  HXReuseScrollView *v79; // r14
  __m128d v82; // xmm1
  ZiXunWindowContentVC *v93; // [rsp+50h] [rbp-30h]

  v93 = self;
  v2 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( !v3 )
  {
    return;
  }
  v4 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "count");
  if ( v6 )
  {
    v7 = -[ZiXunWindowContentVC modelNeedToScroll](self, "modelNeedToScroll");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    if ( v8 )
    {
    }
    else if ( (unsigned __int8)-[ZiXunWindowContentVC isRequestByMenuTitle](self, "isRequestByMenuTitle") == 1 )
    {
      v10 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(v11, "objectAtIndex:", 0LL);
      v13 = objc_retainAutoreleasedReturnValue(v12);
      _objc_msgSend(v14, "setSelectedItemModel:", v13);
      v16 = _objc_msgSend(v15, "scrollView");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18 = _objc_msgSend(v17, "documentView");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      _objc_msgSend(v19, "scrollPoint:", 0.0, 0.0);
      v21 = _objc_msgSend(v20, "selectedItemModel");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      _objc_msgSend(v23, "loadModelContent:", v22);
      return;
    }
    v24 = _objc_msgSend(self, v9);
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
    v27 = (unsigned __int8)_objc_msgSend(v25, "isKindOfClass:", v26);
    v29 = _objc_msgSend(self, v28);
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31 = v30;
    if ( v27 )
    {
      v91 = v30;
      v32 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v34 = _objc_msgSend(v33, "count");
      if ( v34 )
      {
        v35 = 0LL;
        while ( 1 )
        {
          v36 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
          v37 = objc_retainAutoreleasedReturnValue(v36);
          v38 = _objc_msgSend(v37, "objectAtIndex:", v35);
          v39 = objc_retainAutoreleasedReturnValue(v38);
          v41 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
          if ( (unsigned __int8)_objc_msgSend(v39, "isKindOfClass:", v41) )
          {
            v42 = _objc_msgSend(v39, "uniquekey");
            objc_retainAutoreleasedReturnValue(v42);
            v43 = _objc_msgSend(v91, "uniquekey");
            v44 = objc_retainAutoreleasedReturnValue(v43);
            v46 = (unsigned __int8)_objc_msgSend(v45, "isEqualToString:", v44);
            if ( v46 )
              break;
          }
          ++v35;
          v48 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
          v49 = objc_retainAutoreleasedReturnValue(v48);
          v50 = _objc_msgSend(v49, "count");
          v51(v49);
          if ( (unsigned __int64)v50 <= v35 )
            goto LABEL_25;
        }
        -[ZiXunWindowContentVC setSelectedItemModel:](self, "setSelectedItemModel:", v39);
        v77 = v39;
LABEL_28:
        goto LABEL_29;
      }
    }
    else
    {
      v52 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
      v53 = (unsigned __int8)_objc_msgSend(v31, "isKindOfClass:", v52);
      if ( !v53 )
      {
        v35 = 0LL;
LABEL_30:
        v78 = -[ZiXunWindowContentVC scrollView](v93, "scrollView");
        v79 = objc_retainAutoreleasedReturnValue(v78);
        v80 = _objc_msgSend(v79, "documentView");
        v81 = objc_retainAutoreleasedReturnValue(v80);
        v82 = _mm_sub_pd(
                (__m128d)_mm_unpacklo_epi32((__m128i)(83 * v35), (__m128i)xmmword_1010CE400),
                (__m128d)xmmword_1010CE410);
        _objc_msgSend(v81, "scrollPoint:", 0.0, _mm_hadd_pd(v82, v82).f64[0]);
        v84 = _objc_msgSend(v83, "selectedItemModel");
        v85 = objc_retainAutoreleasedReturnValue(v84);
        _objc_msgSend(v86, "loadModelContent:", v85);
        _objc_msgSend(v87, "setModelNeedToScroll:", 0LL);
        return;
      }
      v55 = _objc_msgSend(self, v54);
      v91 = objc_retainAutoreleasedReturnValue(v55);
      v56 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
      v57 = objc_retainAutoreleasedReturnValue(v56);
      v58 = _objc_msgSend(v57, "count");
      if ( v58 )
      {
        v35 = 0LL;
        v59 = "curDataArr";
        do
        {
          v60 = _objc_msgSend(self, v59);
          v61 = objc_retainAutoreleasedReturnValue(v60);
          v62 = _objc_msgSend(v61, "objectAtIndex:", v35);
          v63 = objc_retainAutoreleasedReturnValue(v62);
          v64 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
          if ( (unsigned __int8)_objc_msgSend(v63, "isKindOfClass:", v64) )
          {
            v65 = _objc_msgSend(v63, "title");
            objc_retainAutoreleasedReturnValue(v65);
            v66 = _objc_msgSend(v91, "title");
            v67 = (char *)objc_retainAutoreleasedReturnValue(v66);
            v89 = v68;
            v90 = v67;
            if ( (unsigned __int8)_objc_msgSend(v68, "isEqualToString:", v67) )
            {
              v69 = _objc_msgSend(v63, "time");
              v88 = objc_retainAutoreleasedReturnValue(v69);
              v71 = _objc_msgSend(v91, v70);
              v72 = objc_retainAutoreleasedReturnValue(v71);
              v92 = (unsigned __int8)_objc_msgSend(v88, "isEqualToString:", v72);
              if ( v92 )
              {
                -[ZiXunWindowContentVC setSelectedItemModel:](v93, "setSelectedItemModel:", v63);
                v77 = v63;
                goto LABEL_28;
              }
            }
            else
            {
            }
          }
          ++v35;
          v74 = -[ZiXunWindowContentVC curDataArr](v93, "curDataArr");
          v75 = objc_retainAutoreleasedReturnValue(v74);
          v76 = _objc_msgSend(v75, "count");
          self = v93;
        }
        while ( (unsigned __int64)v76 > v35 );
      }
    }
LABEL_25:
    v35 = 0LL;
LABEL_29:
    goto LABEL_30;
  }
}

//----- (00000001003850B5) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC sendMaiDian:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  NSString *v6; // rax
  NSString *v7; // r14

  v8 = v3;
  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 && _objc_msgSend(v4, "length") )
  {
    v6 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("全局.全部（%@）_重大事件_资讯弹窗"),
           v5);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
      &OBJC_CLASS___UserLogSendingQueueManager,
      "sendUserLog:action:params:needWait:",
      11LL,
      v7,
      0LL,
      1LL,
      v8);
  }
}

//----- (0000000100385152) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC requestMajorEvent](ZiXunWindowContentVC *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx
  NSString *v4; // rax
  NSString *v5; // r15
  MajorEventRequestModule *v7; // rax
  MajorEventRequestModule *v8; // r15
  NSString *v9; // rax
  _QWORD v12[5]; // [rsp+0h] [rbp-60h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  v2 = -[ZiXunWindowContentVC stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( v3 )
  {
    v4 = -[ZiXunWindowContentVC market](self, "market");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6(v3);
    if ( v5 )
    {
      -[ZiXunWindowContentVC setMenuBtnState:](self, "setMenuBtnState:", 0LL);
      objc_initWeak(location, self);
      v7 = -[ZiXunWindowContentVC majorEventReqModule](self, "majorEventReqModule");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v9 = -[ZiXunWindowContentVC stockCode](self, "stockCode");
      objc_retainAutoreleasedReturnValue(v9);
      v12[0] = _NSConcreteStackBlock;
      v12[1] = 3254779904LL;
      v12[2] = sub_1003852CE;
      v12[3] = &unk_1012DDFE8;
      objc_copyWeak(&to, location);
      v12[4] = self;
      -[MajorEventRequestModule requestForMajorEvent:reqCallback:](v8, "requestForMajorEvent:reqCallback:", v10, v12);
      objc_destroyWeak(&to);
      objc_destroyWeak(location);
    }
  }
  else
  {
  }
}

//----- (00000001003852CE) ----------------------------------------------------
void __fastcall sub_1003852CE(__int64 a1, void *a2)
{
  id WeakRetained; // r14
  id *v11; // r12
  id *v13; // r12
  id *v14; // r13
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12

  v2 = objc_retain(a2);
  v28 = v2;
  if ( v2 && (v3 = v2, _objc_msgSend(v2, "count")) )
  {
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(WeakRetained, "setMajorEventArr:", v3);
    v5 = *(void **)(a1 + 32);
    v6 = _objc_msgSend(v5, "majorEventArr");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v5, "filterMajorEventData:selectedIdx:", v7, 0LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(v10, "setCurDataArr:", v9);
    v12 = objc_loadWeakRetained(v11);
    _objc_msgSend(v12, "scrollListViewIfNeeded");
    v14 = v13;
  }
  else
  {
    v15 = __NSArray0__;
    v14 = (id *)(a1 + 40);
    v12 = objc_loadWeakRetained((id *)(a1 + 40));
    _objc_msgSend(v12, "setCurDataArr:", v15);
  }
  v16 = objc_loadWeakRetained(v14);
  v17 = _objc_msgSend(v16, "scrollView");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19(v18, "reload");
  v20 = objc_loadWeakRetained(v14);
  v21 = objc_retain(v20);
  v23 = v22(v21, "curDataArr");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v26 = v25(v24, "count");
  v27(v21, "setNoDataViewState:", v26 == 0LL);
}

//----- (00000001003854CB) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC requestNormalZiXun:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  NSString *v4; // rax
  NSString *v5; // rax
  NSString *v6; // rbx
  NSString *v7; // rax
  NSString *v8; // r15
  NSString *v10; // rax
  NSString *v11; // r15
  ZiXunRequestModule *v12; // rax
  ZiXunRequestModule *v13; // rbx
  NSString *v16; // rax
  NSString *v17; // r15
  id (*v18)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v4 = -[ZiXunWindowContentVC stockCode](self, "stockCode");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    if ( v5 )
    {
      v6 = v5;
      v7 = -[ZiXunWindowContentVC market](self, "market");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v9(v6);
      if ( v8 )
      {
        v10 = -[ZiXunWindowContentVC stockCode](self, "stockCode");
        v11 = objc_retainAutoreleasedReturnValue(v10);
        v12 = -[ZiXunWindowContentVC ziXunReqModule](self, "ziXunReqModule");
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v14(v13, "setStockCode:", v11);
        v15(v11);
        v16 = -[ZiXunWindowContentVC market](self, "market");
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v19 = v18(self, "ziXunReqModule");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v21(v20, "setMarket:", v17);
        v23 = v22(self, "ziXunReqModule");
        v24 = objc_retainAutoreleasedReturnValue(v23);
        v29 = v3;
        v26 = v25(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v29, 1LL);
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28(v24, "requestForZiXun:", v27);
      }
    }
  }
}

//----- (000000010038569E) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC receiveZiXunData:tableFlag:](
        ZiXunWindowContentVC *self,
        SEL a2,
        id a3,
        unsigned __int64 a4)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)v6(v4, "isKindOfClass:", v5) )
  {
    v8 = v7(self, "filterInvalidZiXunData:", v4);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self, "setCurDataArr:", v9);
    v12 = v11(self, "curDataArr");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v15 = v14(self, "sortByTime:", v13);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v17(self, "setCurDataArr:", v16);
    v18(self, "scrollListViewIfNeeded");
    v20 = v19(self, "scrollView");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22(v21, "reload");
    v24 = v23(self, "curDataArr");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v27 = v26(v25, "count");
    v28(self, "setNoDataViewState:", v27 == 0LL);
  }
}

//----- (0000000100385804) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC failToReceiveZiXunData:](ZiXunWindowContentVC *self, SEL a2, unsigned __int64 a3)
{
  HXReuseScrollView *v3; // rax
  HXReuseScrollView *v4; // rbx

  -[ZiXunWindowContentVC setCurDataArr:](self, "setCurDataArr:", __NSArray0__);
  v3 = -[ZiXunWindowContentVC scrollView](self, "scrollView");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXReuseScrollView reload](v4, "reload");
  -[ZiXunWindowContentVC setNoDataViewState:](self, "setNoDataViewState:", 1LL);
}

//----- (0000000100385878) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMenuBtnState:](ZiXunWindowContentVC *self, SEL a2, signed __int64 a3)
{
  NSArray *v3; // rax
  unsigned __int64 v5; // rbx
  SEL v14; // [rsp+48h] [rbp-D8h]
  SEL v15; // [rsp+50h] [rbp-D0h]
  id obj; // [rsp+68h] [rbp-B8h]

  v16 = (id)a3;
  v9 = 0LL;
  v10 = 0LL;
  v11 = 0LL;
  v12 = 0LL;
  v3 = -[ZiXunWindowContentVC meMenuBtnArr](self, "meMenuBtnArr");
  obj = objc_retainAutoreleasedReturnValue(v3);
  v17 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v9, v19, 16LL);
  if ( v17 )
  {
    v13 = *(_QWORD *)v10;
    do
    {
      v4 = "class";
      v14 = "tag";
      v15 = "setIsSelected:";
      v5 = 0LL;
      do
      {
        if ( *(_QWORD *)v10 != v13 )
          objc_enumerationMutation(obj);
        v6 = *(void **)(*((_QWORD *)&v9 + 1) + 8 * v5);
        v7 = _objc_msgSend(&OBJC_CLASS___HXButton, v4);
        if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
        {
          v8 = _objc_msgSend(v6, v14);
          _objc_msgSend(v6, v15, v8 == v16);
        }
        ++v5;
      }
      while ( v5 < (unsigned __int64)v17 );
      v17 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v9, v19, 16LL);
    }
    while ( v17 );
  }
}

//----- (0000000100385A2A) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC meMenuBtnClicked:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // r15
  bool v10; // zf
  NSArray *v12; // rax
  unsigned __int64 i; // r15
  id (*v20)(id, SEL, ...); // r12
  SEL v51; // [rsp+40h] [rbp-D0h]
  id obj; // [rsp+58h] [rbp-B8h]

  objc_retain(a3);
  v3 = -[ZiXunWindowContentVC meMenuBtnArr](self, "meMenuBtnArr");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( _objc_msgSend(v4, "count") )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
    _objc_msgSend(v6, "isKindOfClass:", v5);
    v8 = v7;
    v10 = v9 == 0;
    v11 = v8;
    if ( !v10 )
    {
      v53 = v8;
      v50 = 0LL;
      v49 = 0LL;
      v48 = 0LL;
      v47 = 0LL;
      v52 = self;
      v12 = -[ZiXunWindowContentVC meMenuBtnArr](self, "meMenuBtnArr");
      obj = objc_retainAutoreleasedReturnValue(v12);
      v14 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", v13, v55, 16LL);
      if ( v14 )
      {
        v15 = v14;
        v16 = *(_QWORD *)v48;
        do
        {
          v51 = "setIsSelected:";
          for ( i = 0LL; i < (unsigned __int64)v15; ++i )
          {
            if ( *(_QWORD *)v48 != v16 )
              objc_enumerationMutation(obj);
            v18 = *(id *)(*((_QWORD *)&v47 + 1) + 8 * i);
            v19 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
            if ( (unsigned __int8)v20(v18, "isKindOfClass:", v19) )
              v21(v18, v51, v18 == v53);
          }
          v15 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v47, v55, 16LL);
        }
        while ( v15 );
      }
      v22 = v52;
      v23 = _objc_msgSend(v52, "majorEventArr");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      v25 = _objc_msgSend(v53, "tag");
      v26 = _objc_msgSend(v22, "filterMajorEventData:selectedIdx:", v24, v25);
      v27 = objc_retainAutoreleasedReturnValue(v26);
      _objc_msgSend(v22, "setCurDataArr:", v27);
      v28(v27);
      v29(v24);
      v31 = v30;
      v32 = _objc_msgSend(v22, "curDataArr");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v34 = _objc_msgSend(v33, "firstObject");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      _objc_msgSend(v22, "setModelNeedToScroll:", v35);
      v31(v36);
      v31(v33);
      _objc_msgSend(v22, "scrollListViewIfNeeded");
      v37 = _objc_msgSend(v22, "scrollView");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      _objc_msgSend(v38, "reload");
      v39 = v38;
      v40 = v31;
      v31(v39);
      v41 = _objc_msgSend(v22, "curDataArr");
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v43 = _objc_msgSend(v42, "count");
      _objc_msgSend(v22, "setNoDataViewState:", v43 == 0LL);
      v40(v42);
      v45 = _objc_msgSend(v44, "title");
      v46 = objc_retainAutoreleasedReturnValue(v45);
      _objc_msgSend(v22, "sendMaiDian:", v46);
      v40(v46);
    }
  }
  else
  {
  }
}

//----- (0000000100385DB0) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC listItemClickAction:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  HXReuseScrollView *v10; // rax
  HXReuseScrollView *v11; // rbx

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "model");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[ZiXunWindowContentVC setSelectedItemModel:](self, "setSelectedItemModel:", v5);
  v7 = _objc_msgSend(v6, "model");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[ZiXunWindowContentVC loadModelContent:](self, "loadModelContent:", v8);
  v10 = -[ZiXunWindowContentVC scrollView](self, "scrollView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[HXReuseScrollView reload](v11, "reload");
}

//----- (0000000100385E7B) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC loadModelContent:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  SEL v15; // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = objc_retain(v3);
    v7 = (void *)v6(&OBJC_CLASS___ZiXunReqParamsManager, "sharedInstance");
    v16 = objc_retainAutoreleasedReturnValue(v7);
    v9 = (void *)v8(self, "market");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = _objc_msgSend(v16, "getZiXunURL:market:", v5, v10);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    -[ZiXunWindowContentVC loadZiXunURL:](self, "loadZiXunURL:", v12);
  }
  else
  {
    v14 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v3, v15, v14) )
      -[ZiXunWindowContentVC loadMajorEventItem:](self, "loadMajorEventItem:", v3);
  }
}

//----- (0000000100385FCC) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC loadZiXunURL:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  HXTabbarController *v5; // rax
  HXTabbarController *v6; // r15
  id (*v8)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "length") )
  {
    v5 = -[ZiXunWindowContentVC tabBarController](self, "tabBarController");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7(v6, "setSelectedIndex:", 0LL);
    v9 = v8(self, "webVC");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11(v10, "loadUrl:", v4);
  }
}

//----- (0000000100386083) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC loadMajorEventItem:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v7 = v6(self, "tabBarController");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v8, "setSelectedIndex:", 1LL);
    v11 = v10(self, "majorEventVC");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13(v12, "loadMajorEventContent:", v3);
  }
}

//----- (0000000100386148) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setNoDataViewState:](ZiXunWindowContentVC *self, SEL a2, char a3)
{
  HXBaseView *v8; // rax
  HXBaseView *v9; // r13

  LODWORD(v44) = a3;
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "subviews");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v43 = self;
  v8 = -[ZiXunWindowContentVC noStocksTipView](self, "noStocksTipView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = (unsigned __int8)_objc_msgSend(v7, "containsObject:", v9);
  if ( (_BYTE)v44 )
  {
    if ( !v10 )
    {
      v12 = _objc_msgSend(v43, "view");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v15 = _objc_msgSend(v14, "noStocksTipView");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      _objc_msgSend(v13, "addSubview:", v16);
      v18 = _objc_msgSend(v17, "curReqType");
      v20 = _objc_msgSend(v19, "view");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      if ( v18 )
      {
        v42 = v21;
        _objc_msgSend(v21, "width");
        v24 = _objc_msgSend(v23, "view");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v27 = v26;
        _objc_msgSend(v25, "height");
        v28 = _objc_msgSend(v27, "meTitleView");
        v29 = objc_retainAutoreleasedReturnValue(v28);
        _objc_msgSend(v29, "height");
        v30 = _objc_msgSend(v27, "noStocksTipView");
        v31 = objc_retainAutoreleasedReturnValue(v30);
        v39[2] = 0LL;
        v40 = v3;
        v41 = v3 - v3;
        _objc_msgSend(v31, "setFrame:");
      }
      else
      {
        if ( v21 )
        {
          v36 = v21;
          objc_msgSend_stret(v39, (SEL)v21, "bounds");
        }
        else
        {
          memset(v39, 0, 32);
          v36 = 0LL;
        }
        v37 = _objc_msgSend(v22, "noStocksTipView");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        _objc_msgSend(v38, "setFrame:");
      }
    }
  }
  else
  {
    if ( v10 )
    {
      v34 = _objc_msgSend(v43, "noStocksTipView");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      _objc_msgSend(v35, "removeFromSuperview");
    }
  }
}

//----- (0000000100386466) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunWindowContentVC numberOfBaseView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx

  v3 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "count");
  return (signed __int64)v5;
}

//----- (00000001003864AA) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC baseViewAtIndex:scrollView:](
        ZiXunWindowContentVC *self,
        SEL a2,
        signed __int64 a3,
        id a4)
{
  NSArray *v6; // rax
  NSArray *v7; // rbx
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  HXReuseScrollView *v16; // rax
  HXReuseScrollView *v22; // rax
  id (*v27)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v37)(id, SEL, ...); // r12
  id (*v40)(id, SEL, ...); // r12
  id (*v46)(id, SEL, ...); // r12
  _QWORD v52[4]; // [rsp+20h] [rbp-F0h] BYREF
  id to; // [rsp+40h] [rbp-D0h] BYREF
  id location; // [rsp+C0h] [rbp-50h] BYREF
  id from; // [rsp+C8h] [rbp-48h] BYREF

  v5 = objc_retain(a4);
  if ( a3 < 0 )
  {
    v20 = 0LL;
  }
  else
  {
    v65 = v5;
    v6 = -[ZiXunWindowContentVC curDataArr](self, "curDataArr");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = v8(v7, "count");
    if ( (unsigned __int64)v9 <= a3 )
    {
      v20 = 0LL;
    }
    else
    {
      v11 = v10(&OBJC_CLASS___ZiXunListItemView, "className");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = v13(v65, "baseViewWithId:", v12);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v67 = v15;
      val = self;
      if ( v15 )
      {
        v16 = -[ZiXunWindowContentVC scrollView](self, "scrollView");
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = (char *)v17;
        if ( v17 )
        {
          objc_msgSend_stret(&v57, v17, "bounds");
          v19 = *(double *)&v58;
        }
        else
        {
          v58 = 0LL;
          v57 = 0LL;
          v19 = 0.0;
        }
        v26 = v67;
        _objc_msgSend(v67, "setWidth:", v19);
        v28 = v26;
      }
      else
      {
        v21 = objc_alloc((Class)&OBJC_CLASS___ZiXunListItemView);
        v22 = -[ZiXunWindowContentVC scrollView](self, "scrollView");
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v24 = (char *)v23;
        if ( v23 )
        {
          objc_msgSend_stret(&v55, v23, "bounds");
          v25 = v56;
        }
        else
        {
          v56 = 0LL;
          v55 = 0LL;
          v25 = 0LL;
        }
        v59 = 0LL;
        v60 = v25;
        v61 = 0x4059000000000000LL;
        v28 = _objc_msgSend(v21, "initWithFrame:");
        v26 = v28;
      }
      v29 = v27(&OBJC_CLASS___ZiXunListItemView, "class");
      v67 = v26;
      if ( (unsigned __int8)v30(v26, "isKindOfClass:", v29) )
      {
        v20 = objc_retain(v28);
        v32 = val;
        v34 = v33(val, "market");
        v35 = objc_retainAutoreleasedReturnValue(v34);
        v36(v20, "setMarket:", v35);
        v62 = a3;
        v38 = v37(v32, "curDataArr");
        v39 = objc_retainAutoreleasedReturnValue(v38);
        v41 = v40(v39, "objectAtIndex:", v62);
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v43(v20, "setModel:", v42);
      }
      else
      {
        v20 = 0LL;
      }
      v44 = v31(val, "selectedItemModel");
      v45 = objc_retainAutoreleasedReturnValue(v44);
      v47 = v46(v20, "model");
      v48 = objc_retainAutoreleasedReturnValue(v47);
      v49(v20, "setIsSelected:", v45 == v48);
      objc_initWeak(&location, val);
      objc_initWeak(&from, v20);
      v52[0] = _NSConcreteStackBlock;
      v52[1] = 3254779904LL;
      v52[2] = sub_1003868CE;
      v52[3] = &unk_1012E0278;
      objc_copyWeak(&to, &location);
      objc_copyWeak(&v54, &from);
      v50(v20, "setClickBlock:", v52);
      objc_destroyWeak(&v54);
      objc_destroyWeak(&to);
      objc_destroyWeak(&from);
      objc_destroyWeak(&location);
    }
    v5 = v65;
  }
  return objc_autoreleaseReturnValue(v20);
}

//----- (00000001003868CE) ----------------------------------------------------
void __fastcall sub_1003868CE(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "listItemClickAction:", v2);
}

//----- (0000000100386928) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunWindowContentVC reuseBaseViewCount:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  return 30LL;
}

//----- (0000000100386933) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunWindowContentVC preloadBaseViewCount:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  return 5LL;
}

//----- (000000010038693E) ----------------------------------------------------
double __cdecl -[ZiXunWindowContentVC heightOfInvisibleBaseView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  return 83.0;
}

//----- (000000010038694C) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC filterMajorEventData:selectedIdx:](
        ZiXunWindowContentVC *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{
  NSArray *v6; // rax
  NSArray *v7; // rax
  NSArray *v8; // rax
  NSArray *v9; // rax
  NSArray *v10; // r13
  NSArray *v11; // rax
  NSArray *v12; // r14
  NSArray *v13; // rax
  NSArray *v14; // rbx
  unsigned __int64 v26; // r12
  SEL v39; // [rsp+40h] [rbp-1E0h]
  id obj; // [rsp+70h] [rbp-1B0h]
  _QWORD v48[7]; // [rsp+100h] [rbp-120h] BYREF
  _QWORD v49[3]; // [rsp+138h] [rbp-E8h] BYREF
  _QWORD v50[3]; // [rsp+150h] [rbp-D0h] BYREF
  __CFString *v51; // [rsp+168h] [rbp-B8h] BYREF
  _QWORD v52[10]; // [rsp+170h] [rbp-B0h] BYREF
  _QWORD v53[6]; // [rsp+1C0h] [rbp-60h] BYREF

  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) && _objc_msgSend(v4, "count") )
  {
    v46 = v4;
    v53[0] = __NSArray0__;
    v52[0] = CFSTR("涨停");
    v52[1] = CFSTR("股东大会");
    v52[2] = CFSTR("机构调研");
    v52[3] = CFSTR("收购兼并");
    v52[4] = CFSTR("诉讼仲裁");
    v52[5] = CFSTR("违规");
    v52[6] = CFSTR("停复牌");
    v52[7] = CFSTR("股票更名");
    v52[8] = CFSTR("参控公司");
    v52[9] = CFSTR("中标");
    v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v52, 10LL);
    v42 = objc_retainAutoreleasedReturnValue(v6);
    v53[1] = v42;
    v51 = CFSTR("发布公告");
    v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v51, 1LL);
    v43 = objc_retainAutoreleasedReturnValue(v7);
    v53[2] = v43;
    v50[0] = CFSTR("业绩快报");
    v50[1] = CFSTR("业绩披露");
    v50[2] = CFSTR("业绩预告");
    v8 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v50, 3LL);
    v44 = objc_retainAutoreleasedReturnValue(v8);
    v53[3] = v44;
    v49[0] = CFSTR("限售解禁");
    v49[1] = CFSTR("分红");
    v49[2] = CFSTR("增发提示");
    v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v49, 3LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v53[4] = v10;
    v48[0] = CFSTR("龙虎榜");
    v48[1] = CFSTR("高管持股变化");
    v48[2] = CFSTR("股东持股变化");
    v48[3] = CFSTR("大宗交易");
    v48[4] = CFSTR("融资融券");
    v48[5] = CFSTR("股东人数变化");
    v48[6] = CFSTR("股权质押");
    v11 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v48, 7LL);
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v53[5] = v12;
    v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v53, 6LL);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v16 = _objc_msgSend(v14, "thsArrayAtIndex:", v15);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v18(v12);
    v19(v10);
    v20(v44);
    v21(v43);
    v22(v42);
    v40 = v17;
    if ( _objc_msgSend(v17, "count") )
    {
      v23 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
      v41 = objc_retainAutoreleasedReturnValue(v23);
      v35 = 0LL;
      v36 = 0LL;
      v37 = 0LL;
      v38 = 0LL;
      obj = objc_retain(v46);
      v24 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v47, 16LL);
      if ( v24 )
      {
        v25 = v24;
        v42 = *(id *)v36;
        do
        {
          v43 = "cateCN";
          v44 = "containsObject:";
          v39 = "addObject:";
          v26 = 0LL;
          do
          {
            if ( *(id *)v36 != v42 )
              objc_enumerationMutation(obj);
            v27 = *(void **)(*((_QWORD *)&v35 + 1) + 8 * v26);
            v28 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
            if ( (unsigned __int8)_objc_msgSend(v27, "isKindOfClass:", v28) )
            {
              v30 = _objc_msgSend(v27, (SEL)v43);
              v31 = objc_retainAutoreleasedReturnValue(v30);
              v32 = (unsigned __int8)_objc_msgSend(v40, (SEL)v44, v31);
              if ( v32 )
                _objc_msgSend(v41, v39, v27);
            }
            v26 = v29 + 1;
          }
          while ( v26 < (unsigned __int64)v25 );
          v25 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v35, v47, 16LL);
        }
        while ( v25 );
      }
      v4 = v46;
      v33 = v41;
    }
    else
    {
      v4 = v46;
      v33 = objc_retain(v46);
    }
  }
  else
  {
    v33 = objc_retain(v4);
  }
  objc_autorelease(v33);
  return v33;
}

//----- (0000000100386E7D) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC filterInvalidZiXunData:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  NSMutableArray *v4; // rax
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  unsigned __int64 i; // r14
  id (*v10)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  SEL v24; // [rsp+48h] [rbp-F8h]
  SEL v25; // [rsp+50h] [rbp-F0h]
  SEL v27; // [rsp+60h] [rbp-E0h]
  SEL v28; // [rsp+68h] [rbp-D8h]
  SEL v29; // [rsp+70h] [rbp-D0h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v3);
  v31 = objc_retainAutoreleasedReturnValue(v4);
  v20 = 0LL;
  v21 = 0LL;
  v22 = 0LL;
  v23 = 0LL;
  obj = objc_retain(v3);
  v30 = v5(obj, "countByEnumeratingWithState:objects:count:", &v20, v33, 16LL);
  if ( v30 )
  {
    v26 = *(_QWORD *)v21;
    do
    {
      v24 = "class";
      v25 = "isKindOfClass:";
      v27 = "time";
      v28 = "isEqualToString:";
      v29 = "removeObject:";
      for ( i = 0LL; i < (unsigned __int64)v30; ++i )
      {
        if ( *(_QWORD *)v21 != v26 )
          objc_enumerationMutation(obj);
        v8 = *(void **)(*((_QWORD *)&v20 + 1) + 8 * i);
        v9 = v6(&OBJC_CLASS___NewsItem, v24);
        if ( (unsigned __int8)v10(v8, v25, v9) )
        {
          v11 = objc_retain(v8);
          v13 = v12(v11, v27);
          v14 = objc_retainAutoreleasedReturnValue(v13);
          v16 = (unsigned __int8)v15(v14, v28, CFSTR("0"));
          if ( v16 )
            v17(v31, v29, v11);
        }
      }
      v30 = v6(obj, "countByEnumeratingWithState:objects:count:", &v20, v33, 16LL);
    }
    while ( v30 );
  }
  v18 = obj;
  return objc_autoreleaseReturnValue(v31);
}

//----- (00000001003870D0) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC sortByTime:](ZiXunWindowContentVC *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  if ( v3 )
  {
    v4 = v3;
    if ( _objc_msgSend(v3, "count") )
    {
      v5 = _objc_msgSend(v4, "sortedArrayUsingComparator:", &stru_1012E02A8);
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v7 = objc_retain(v6);
      v4 = v7;
    }
    else
    {
      v7 = 0LL;
    }
  }
  else
  {
    v7 = 0LL;
    v4 = 0LL;
  }
  return objc_autoreleaseReturnValue(v7);
}

//----- (0000000100387158) ----------------------------------------------------
signed __int64 __cdecl sub_100387158(id a1, NewsItem *a2, NewsItem *a3)
{
  NewsItem *v4; // r15
  NewsItem *v5; // r14
  NSString *v6; // rax
  NSString *v7; // r13
  NSString *v8; // rax
  NSString *v9; // rbx
  NSString *v11; // rax
  id (*v12)(id, SEL, ...); // r12
  signed __int64 v16; // r12

  v4 = objc_retain(a2);
  v5 = objc_retain(a3);
  v6 = -[NewsItem time](v4, "time");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( !v7 )
    goto LABEL_4;
  v8 = -[NewsItem time](v5, "time");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v7);
  if ( v9 )
  {
    v11 = -[NewsItem time](v5, "time");
    v7 = objc_retainAutoreleasedReturnValue(v11);
    v13 = v12(v4, "time");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15(v7, "compare:", v14);
LABEL_4:
  }
  return v16;
}

//----- (0000000100387260) ----------------------------------------------------
NSString *__cdecl -[ZiXunWindowContentVC stockCode](ZiXunWindowContentVC *self, SEL a2)
{
  return self->super._nibName;
}

//----- (0000000100387271) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setStockCode:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._nibName, a3);
}

//----- (0000000100387285) ----------------------------------------------------
NSString *__cdecl -[ZiXunWindowContentVC market](ZiXunWindowContentVC *self, SEL a2)
{
  return (NSString *)&self->super._nibBundle->super.isa;
}

//----- (0000000100387296) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMarket:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._nibBundle, a3);
}

//----- (00000001003872AA) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContentVC meTitleView](ZiXunWindowContentVC *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 32LL, 1);
}

//----- (00000001003872C0) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMeTitleView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 32LL);
}

//----- (00000001003872D1) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContentVC containerView](ZiXunWindowContentVC *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001003872EA) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setContainerView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._title, a3);
}

//----- (00000001003872FE) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContentVC itemListView](ZiXunWindowContentVC *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.view);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100387317) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setItemListView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.view, a3);
}

//----- (000000010038732B) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContentVC contentView](ZiXunWindowContentVC *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._topLevelObjects);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100387344) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setContentView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._topLevelObjects, a3);
}

//----- (0000000100387358) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContentVC noStocksTipView](ZiXunWindowContentVC *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 64LL, 1);
}

//----- (000000010038736E) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setNoStocksTipView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 64LL);
}

//----- (000000010038737F) ----------------------------------------------------
NSButton *__cdecl -[ZiXunWindowContentVC noStocksTipBtn](ZiXunWindowContentVC *self, SEL a2)
{
  return (NSButton *)objc_getProperty(self, a2, 72LL, 1);
}

//----- (0000000100387395) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setNoStocksTipBtn:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 72LL);
}

//----- (00000001003873A6) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setScrollView:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._designNibBundleIdentifier, a3);
}

//----- (00000001003873BA) ----------------------------------------------------
NSArray *__cdecl -[ZiXunWindowContentVC meMenuBtnArr](ZiXunWindowContentVC *self, SEL a2)
{
  return (NSArray *)self->super.__privateData;
}

//----- (00000001003873CB) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMeMenuBtnArr:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.__privateData, a3);
}

//----- (00000001003873DF) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMajorEventReqModule:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._viewIsAppearing, a3);
}

//----- (00000001003873F3) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setZiXunReqModule:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._isContentViewController, a3);
}

//----- (0000000100387407) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setTabBarController:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._reserved, a3);
}

//----- (000000010038741B) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setWebVC:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_isRequestByMenuTitle, a3);
}

//----- (000000010038742F) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMajorEventVC:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_stockCode, a3);
}

//----- (0000000100387443) ----------------------------------------------------
NSArray *__cdecl -[ZiXunWindowContentVC majorEventArr](ZiXunWindowContentVC *self, SEL a2)
{
  return (NSArray *)self->_market;
}

//----- (0000000100387454) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setMajorEventArr:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_market, a3);
}

//----- (0000000100387468) ----------------------------------------------------
NSArray *__cdecl -[ZiXunWindowContentVC curDataArr](ZiXunWindowContentVC *self, SEL a2)
{
  return (NSArray *)self->_meTitleView;
}

//----- (0000000100387479) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setCurDataArr:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_meTitleView, a3);
}

//----- (000000010038748D) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunWindowContentVC curReqType](ZiXunWindowContentVC *self, SEL a2)
{
  return (unsigned __int64)self->_containerView;
}

//----- (000000010038749E) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setCurReqType:](ZiXunWindowContentVC *self, SEL a2, unsigned __int64 a3)
{
  self->_containerView = (HXBaseView *)a3;
}

//----- (00000001003874AF) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC selectedItemModel](ZiXunWindowContentVC *self, SEL a2)
{
  return self->_itemListView;
}

//----- (00000001003874C0) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setSelectedItemModel:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_itemListView, a3);
}

//----- (00000001003874D4) ----------------------------------------------------
id __cdecl -[ZiXunWindowContentVC modelNeedToScroll](ZiXunWindowContentVC *self, SEL a2)
{
  return self->_contentView;
}

//----- (00000001003874E5) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setModelNeedToScroll:](ZiXunWindowContentVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_contentView, a3);
}

//----- (00000001003874F9) ----------------------------------------------------
char __cdecl -[ZiXunWindowContentVC isRequestByMenuTitle](ZiXunWindowContentVC *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (000000010038750A) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC setIsRequestByMenuTitle:](ZiXunWindowContentVC *self, SEL a2, char a3)
{
  LOBYTE(self->super.super._nextResponder) = a3;
}

//----- (000000010038751A) ----------------------------------------------------
void __cdecl -[ZiXunWindowContentVC .cxx_destruct](ZiXunWindowContentVC *self, SEL a2)
{
  objc_storeStrong((id *)&self->_contentView, 0LL);
  objc_storeStrong((id *)&self->_itemListView, 0LL);
  objc_storeStrong((id *)&self->_meTitleView, 0LL);
  objc_storeStrong((id *)&self->_market, 0LL);
  objc_storeStrong((id *)&self->_stockCode, 0LL);
  objc_storeStrong((id *)&self->_isRequestByMenuTitle, 0LL);
  objc_storeStrong((id *)&self->super._reserved, 0LL);
  objc_storeStrong((id *)&self->super._isContentViewController, 0LL);
  objc_storeStrong((id *)&self->super._viewIsAppearing, 0LL);
  objc_storeStrong(&self->super.__privateData, 0LL);
  objc_storeStrong((id *)&self->super._designNibBundleIdentifier, 0LL);
  objc_storeStrong(&self->super._autounbinder, 0LL);
  objc_storeStrong((id *)&self->super._editors, 0LL);
  objc_destroyWeak((id *)&self->super._topLevelObjects);
  objc_destroyWeak((id *)&self->super.view);
  objc_destroyWeak((id *)&self->super._title);
  objc_storeStrong(&self->super._representedObject, 0LL);
  objc_storeStrong((id *)&self->super._nibBundle, 0LL);
  objc_storeStrong((id *)&self->super._nibName, 0LL);
}

