void __cdecl -[ZiXuanGuViewController viewDidLoad](ZiXuanGuViewController *self, SEL a2)
{
  id (*v7)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (**v41)(id, SEL, ...); // r12
  bool v48; // cc
  id (*v70)(id, SEL, ...); // r12
  id (*v73)(id, SEL, ...); // r12
  QiHuoZhuLianGuanLianAGuTableController *v84; // r12
  QiHuoZhuLianGuanLianAGuTableController *v85; // rbx
  __CFString *v94; // rdi
  __CFString *v95; // r14
  NSDictionary *v96; // rax
  NSDictionary *v97; // rax
  SEL v107; // [rsp+90h] [rbp-50h]
  SEL v108; // [rsp+98h] [rbp-48h]
  __CFString *v109; // [rsp+A0h] [rbp-40h] BYREF
  __CFString *v110; // [rsp+A8h] [rbp-38h] BYREF

  v2 = self;
  v99.receiver = self;
  v99.super_class = (Class)&OBJC_CLASS___ZiXuanGuViewController;
  -[HXBaseViewController viewDidLoad](&v99, "viewDidLoad");
  +[UserLogSendingQueueManager updatePagePrefix:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "updatePagePrefix:",
    CFSTR("自选"));
  v3(self, "setRightAccessaryState:", 3LL);
  v4(self, "setViewOriginalState");
  v5(self, "setViewControllersOriginalState");
  v6(self, "setRightAccessaryViewOriginalState");
  v8 = v7(&OBJC_CLASS___HXTipManager, "sharedInstance");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v9, "addDataSource:", self);
  v12 = v11(&OBJC_CLASS___HXCPManager, "sharedInstance");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v13, "setControllerForCPInMainWindow:", self);
  v16 = v15(&OBJC_CLASS___HXCPManager, "sharedInstance");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = v18(v17, "getHXCPManagerObject");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v22 = v21(v20, "ziXuanPageObject");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v24 = v17;
  v102 = v23;
  v106 = v2;
  if ( !v23 )
  {
    _objc_msgSend(v2, "insertDefaultPages");
    goto LABEL_9;
  }
  v27 = v26(v23, "tabItemObjects");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v29(v28, "enumerateObjectsUsingBlock:");
  v103 = (char *)v30(v23, "selectedIndex");
  v32 = v31(v23, "selectedIndex");
  v34 = v33(v2, "tabContainer");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  v37 = v36(v35, "tabViewArray");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  if ( (unsigned __int64)v32 > v39(v38, "count") - 1 )
  {
    v108 = "ziXuanTabbarController";
    v107 = "viewControllers";
    goto LABEL_6;
  }
  v104 = v35;
  v105 = (char *)_objc_msgSend(v23, "selectedIndex");
  v108 = "ziXuanTabbarController";
  v42 = _objc_msgSend(v106, "ziXuanTabbarController");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  v107 = "viewControllers";
  v44 = _objc_msgSend(v43, "viewControllers");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = (char *)_objc_msgSend(v45, "count") - 1;
  v48 = v105 <= v46;
  v49 = v103;
  if ( !v48 )
  {
LABEL_6:
    v50 = v106;
    v51 = _objc_msgSend(v106, "tabContainer");
    v52 = objc_retainAutoreleasedReturnValue(v51);
    v53 = _objc_msgSend(v52, "tabViewArray");
    v54 = objc_retainAutoreleasedReturnValue(v53);
    _objc_msgSend(v54, "count");
    v55 = _objc_msgSend(v50, v108);
    v56 = objc_retainAutoreleasedReturnValue(v55);
    v57 = _objc_msgSend(v56, v107);
    v58 = objc_retainAutoreleasedReturnValue(v57);
    v49 = (char *)_objc_msgSend(v58, "count") - 1;
    if ( v59 < v49 )
      v49 = v59;
  }
  v2 = v106;
  v60 = _objc_msgSend(v106, "tabContainer");
  v61 = objc_retainAutoreleasedReturnValue(v60);
  _objc_msgSend(v61, "setSelectedIndex:", v49);
  v62(v61);
  v63 = _objc_msgSend(v2, v108);
  v64 = objc_retainAutoreleasedReturnValue(v63);
  _objc_msgSend(v64, "setSelectedIndex:", v49);
  v65(v64);
  v25 = v66;
  v41 = &_objc_msgSend;
LABEL_9:
  v67 = v25;
  v68 = ((id (*)(id, SEL, ...))v41)(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v69 = objc_retainAutoreleasedReturnValue(v68);
  v108 = (SEL)NSWindowDidBecomeKeyNotification;
  v71 = v70(v2, "view");
  v72 = v2;
  v107 = (SEL)objc_retainAutoreleasedReturnValue(v71);
  v74 = v73((id)v107, "window");
  v75 = objc_retainAutoreleasedReturnValue(v74);
  v77 = v76;
  ((void (__fastcall *)(id, const char *, void *, const char *, SEL, id))v76)(
    v69,
    "addObserver:selector:name:object:",
    v72,
    "becomeKeyWindow:",
    v108,
    v75);
  ((void (__fastcall *)(__int64))v67)(v78);
  ((void (__fastcall *)(SEL))v67)(v107);
  ((void (__fastcall *)(id))v67)(v69);
  v79 = (void *)v77(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v80 = objc_retainAutoreleasedReturnValue(v79);
  ((void (__fastcall *)(id, const char *, id, const char *, __CFString *, _QWORD))v77)(
    v80,
    "addObserver:selector:name:object:",
    v106,
    "deleteMyTemplateTab:",
    off_1012E4768[0],
    0LL);
  ((void (__fastcall *)(id))v67)(v80);
  v81 = (void *)v77(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v82 = objc_retainAutoreleasedReturnValue(v81);
  ((void (__fastcall *)(id, const char *, __int64, const char *, __CFString *, _QWORD))v77)(
    v82,
    "addObserver:selector:name:object:",
    v83,
    "renameMyTemplateTab:",
    off_1012E4770,
    0LL);
  ((void (__fastcall *)(id))v67)(v82);
  v85 = v84;
  v86 = (void *)v77(v84, "baseSplitView");
  v108 = objc_retainAutoreleasedReturnValue(v86);
  v87 = (void *)v77((QiHuoZhuLianGuanLianAGuTableController *)v108, "subviews");
  objc_retainAutoreleasedReturnValue(v87);
  v88 = (void *)v77(v85, "rightAccessaryView");
  v89 = objc_retainAutoreleasedReturnValue(v88);
  v107 = v90;
  if ( ((unsigned __int8 (__fastcall *)(const char *, const char *, id))v77)(v90, "containsObject:", v89) )
  {
    v91 = _objc_msgSend(v85, "rightAccessaryView");
    v92 = (const char *)objc_retainAutoreleasedReturnValue(v91);
    v93 = (char *)v92;
    if ( v92 )
    {
      objc_msgSend_stret(&v100, v92, "frame");
      v94 = CFSTR("_模块状态");
      if ( *(double *)&v101 > 0.0 )
        v94 = CFSTR("右_模块状态");
    }
    else
    {
      v101 = 0LL;
      v100 = 0LL;
      v94 = CFSTR("_模块状态");
    }
    v95 = objc_retain(v94);
  }
  else
  {
    v95 = CFSTR("_模块状态");
  }
  v109 = CFSTR("statusKey");
  v110 = CFSTR("rightViewIsExistence");
  v96 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v110, &v109, 1LL);
  v97 = objc_retainAutoreleasedReturnValue(v96);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    12LL,
    v95,
    v97,
    1LL);
}

//----- (0000000100919D1D) ----------------------------------------------------
id __fastcall sub_100919D1D(__int64 a1, __int64 a2)
{
  return _objc_msgSend(*(id *)(a1 + 32), "insertCustomizablePageWithObject:", a2);
}

//----- (0000000100919D36) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController becomeKeyWindow:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  HXCPToolBoxWC *v10; // rax

  v3 = _objc_msgSend(a3, "object");
  objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(self, "view");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "window");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( v9 == v7 )
  {
    if ( (unsigned __int8)-[ZiXuanGuViewController toolBoxOpened](self, "toolBoxOpened") )
    {
      v10 = +[HXCPToolBoxWC sharedInstance](&OBJC_CLASS___HXCPToolBoxWC, "sharedInstance");
      objc_retainAutoreleasedReturnValue(v10);
      v11 = _objc_msgSend(self, "view");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v13 = _objc_msgSend(v12, "window");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      _objc_msgSend(v15, "showAsChildWindowIn:delegate:", v14, self);
    }
  }
}

//----- (0000000100919E64) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController viewWillAppear](ZiXuanGuViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXuanGuViewController;
  objc_msgSendSuper2(&v2, "viewWillAppear");
  +[UserLogSendingQueueManager updatePagePrefix:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "updatePagePrefix:",
    CFSTR("自选"));
}

//----- (0000000100919EAE) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController viewDidAppear](ZiXuanGuViewController *self, SEL a2)
{
  HXTipManager *v2; // rax
  HXTipManager *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  __CFString *v8; // [rsp+10h] [rbp-30h] BYREF

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZiXuanGuViewController;
  -[HXBaseViewController viewDidAppear](&v7, "viewDidAppear");
  v2 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v8 = off_1016BAEB0;
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v8, 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXTipManager showTipsWithIdArray:](v3, "showTipsWithIdArray:", v5);
  v6(v3);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    10LL,
    CFSTR("pageshow"),
    0LL,
    1LL);
}

//----- (0000000100919FAC) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController viewDidDisappear](ZiXuanGuViewController *self, SEL a2)
{
  HXCPToolBoxWC *v2; // rax
  HXCPToolBoxWC *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  ZiXuanGuViewController *v6; // rax
  id (*v7)(id, SEL, ...); // r12

  v12.receiver = self;
  v12.super_class = (Class)&OBJC_CLASS___ZiXuanGuViewController;
  -[HXBaseViewController viewDidDisappear](&v12, "viewDidDisappear");
  v2 = +[HXCPToolBoxWC sharedInstance](&OBJC_CLASS___HXCPToolBoxWC, "sharedInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "delegate");
  v6 = (ZiXuanGuViewController *)objc_retainAutoreleasedReturnValue(v5);
  if ( v6 == self )
  {
    v8 = v7(v3, "window");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "isVisible");
    if ( v11 )
      -[HXCPToolBoxWC close](v3, "close");
  }
  else
  {
  }
}

//----- (000000010091A08E) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController dealloc](ZiXuanGuViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZiXuanGuViewController;
  -[HXBaseViewController dealloc](&v4, "dealloc");
}

//----- (000000010091A103) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController refreshAllModules](ZiXuanGuViewController *self, SEL a2)
{
  HXPanKouContainerController *v2; // rax
  HXPanKouContainerController *v3; // rbx

  -[HXBaseViewController setShouldRefresh:](self, "setShouldRefresh:", 0LL);
  _objc_msgSend(self->super._stockCode, "refreshViewControllers");
  if ( !(unsigned __int8)-[ZiXuanGuViewController isRightViewHide](self, "isRightViewHide") )
  {
    v2 = -[ZiXuanGuViewController tableRightPanKouVC](self, "tableRightPanKouVC");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    -[HXPanKouContainerController refreshAllModules](v3, "refreshAllModules");
  }
}

//----- (000000010091A17D) ----------------------------------------------------
HXTabbarController *__cdecl -[ZiXuanGuViewController ziXuanTabbarController](ZiXuanGuViewController *self, SEL a2)
{
  NSString *stockCode; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  stockCode = self->super._stockCode;
  if ( !stockCode )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super._stockCode;
    self->super._stockCode = v5;
    _objc_msgSend(self->super._stockCode, "setSwitchDelegate:", self);
    stockCode = self->super._stockCode;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(stockCode);
}

//----- (000000010091A1EC) ----------------------------------------------------
SelfStockContainerController *__cdecl -[ZiXuanGuViewController selfStockContainerController](
        ZiXuanGuViewController *self,
        SEL a2)
{
  NSString *market; // rdi
  objc_class *v5; // rax
  HXRightAccessaryViewController *v11; // rax
  HXRightAccessaryViewController *v12; // r14
  _QWORD v17[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  market = self->super._market;
  if ( !market )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___SelfStockContainerController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("SelfStockContainerController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.super.isa + v6) = v5;
    v8 = -[ZiXuanGuViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZiXuanGuViewController rightAccessaryVC](self, "rightAccessaryVC");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v13), "setTableRightAccessaryVC:", v12);
    objc_initWeak(location, self);
    v17[0] = _NSConcreteStackBlock;
    v17[1] = 3254779904LL;
    v17[2] = sub_10091A34B;
    v17[3] = &unk_1012DAA10;
    objc_copyWeak(&to, location);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v14), "setOpenRightAccessaryViewBlcok:", v17);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    market = *(NSString **)((char *)&self->super.super.super.super.isa + v15);
  }
  return (SelfStockContainerController *)objc_retainAutoreleaseReturnValue(market);
}

//----- (000000010091A34B) ----------------------------------------------------
void __fastcall sub_10091A34B(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = (unsigned __int8)_objc_msgSend(WeakRetained, "isRightViewHide");
  if ( v2 )
  {
    v3 = objc_loadWeakRetained((id *)(a1 + 32));
    v4 = objc_retain(v3);
    v5 = _objc_msgSend(v4, "showAndHideRightBtn");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v4, "showAndHideRightClick:", v6);
  }
}

//----- (000000010091A3FE) ----------------------------------------------------
AIGroupContainerController *__cdecl -[ZiXuanGuViewController aiGroupContainerController](
        ZiXuanGuViewController *self,
        SEL a2)
{
  NSMutableArray *contentsObjMArr; // rdi
  objc_class *v5; // rax
  HXRightAccessaryViewController *v11; // rax
  HXRightAccessaryViewController *v12; // r14
  _QWORD v17[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  contentsObjMArr = self->super._contentsObjMArr;
  if ( !contentsObjMArr )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___AIGroupContainerController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("AIGroupContainerController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.super.isa + v6) = v5;
    v8 = -[ZiXuanGuViewController refreshRightViewBlock](self, "refreshRightViewBlock");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v10), "setRefreshRightViewBlock:", v9);
    v11 = -[ZiXuanGuViewController rightAccessaryVC](self, "rightAccessaryVC");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v13), "setTableRightAccessaryVC:", v12);
    objc_initWeak(location, self);
    v17[0] = _NSConcreteStackBlock;
    v17[1] = 3254779904LL;
    v17[2] = sub_10091A55D;
    v17[3] = &unk_1012DAA10;
    objc_copyWeak(&to, location);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v14), "setOpenRightAccessaryViewBlcok:", v17);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    contentsObjMArr = *(NSMutableArray **)((char *)&self->super.super.super.super.isa + v15);
  }
  return (AIGroupContainerController *)objc_retainAutoreleaseReturnValue(contentsObjMArr);
}

//----- (000000010091A55D) ----------------------------------------------------
void __fastcall sub_10091A55D(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = (unsigned __int8)_objc_msgSend(WeakRetained, "isRightViewHide");
  if ( v2 )
  {
    v3 = objc_loadWeakRetained((id *)(a1 + 32));
    v4 = objc_retain(v3);
    v5 = _objc_msgSend(v4, "showAndHideRightBtn");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v4, "showAndHideRightClick:", v6);
  }
}

//----- (000000010091A610) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController refreshRightViewBlock](ZiXuanGuViewController *self, SEL a2)
{
  NSView *leftMainView; // rdi
  objc_class *v4; // rax
  NSView *v8; // rax
  _QWORD v10[4]; // [rsp+0h] [rbp-50h] BYREF
  id to; // [rsp+20h] [rbp-30h] BYREF
  id location[5]; // [rsp+28h] [rbp-28h] BYREF

  leftMainView = self->_leftMainView;
  if ( !leftMainView )
  {
    objc_initWeak(location, self);
    v10[0] = _NSConcreteStackBlock;
    v10[1] = 3254779904LL;
    v10[2] = sub_10091A6BE;
    v10[3] = &unk_1012DD620;
    objc_copyWeak(&to, location);
    v4 = objc_retainBlock(v10);
    v6 = *(Class *)((char *)&self->super.super.super.super.isa + v5);
    *(Class *)((char *)&self->super.super.super.super.isa + v5) = v4;
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    leftMainView = *(NSView **)((char *)&self->super.super.super.super.isa + v7);
  }
  v8 = objc_retainBlock(leftMainView);
  return objc_autoreleaseReturnValue(v8);
}

//----- (000000010091A6BE) ----------------------------------------------------
void __fastcall sub_10091A6BE(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "theSelectRowDidChanged:", v2);
}

//----- (000000010091A718) ----------------------------------------------------
HXRightAccessaryViewController *__cdecl -[ZiXuanGuViewController rightAccessaryVC](
        ZiXuanGuViewController *self,
        SEL a2)
{

  controllerID = (void *)self->super._controllerID;
  if ( !controllerID )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXRightAccessaryViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("HXRightAccessaryViewController"), 0LL);
    v6 = (void *)self->super._controllerID;
    self->super._controllerID = (signed __int64)v5;
    controllerID = (void *)self->super._controllerID;
  }
  return (HXRightAccessaryViewController *)objc_retainAutoreleaseReturnValue(controllerID);
}

//----- (000000010091A772) ----------------------------------------------------
HXPanKouContainerController *__cdecl -[ZiXuanGuViewController tableRightPanKouVC](ZiXuanGuViewController *self, SEL a2)
{
  NSMutableDictionary *paramsMDic; // r15
  objc_class *v5; // rax
  HXRightAccessaryViewController *v8; // rax
  HXRightAccessaryViewController *v9; // rbx

  paramsMDic = self->super._paramsMDic;
  if ( !paramsMDic )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXPanKouContainerController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("HXPanKouContainerController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.super.isa + v6) = v5;
    v8 = -[ZiXuanGuViewController rightAccessaryVC](self, "rightAccessaryVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v10), "setRightAccessaryVC:", v9);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.super.isa + v11), "setStrPankouMode:", CFSTR("table"));
    paramsMDic = *(NSMutableDictionary **)((char *)&self->super.super.super.super.isa + v12);
  }
  v13 = +[WidgetParamSettingManager shareInstance](&OBJC_CLASS___WidgetParamSettingManager, "shareInstance");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setPanKouContainer:", paramsMDic);
  return (HXPanKouContainerController *)objc_retainAutoreleaseReturnValue(*(id *)((char *)&self->super.super.super.super.isa
                                                                                + v15));
}

//----- (000000010091A86D) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController quanBuZiXuanBtnClicked:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12

  v3 = (unsigned __int8)-[ZiXuanGuViewController rightAccessaryState](self, "rightAccessaryState", a3);
  v4(self, "setRightAccessaryState:", (v3 & 1u) + 2);
  v6 = v5(self, "selfStockContainerController");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(v7, "controllerID");
  v11 = v10(self, "tabContainer");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = v13(v12, "getIndexWithId:", v9);
  v16 = v15(self, "getVcIndexWithTabId:", v9);
  v18 = v17(self, "tabContainer");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20(v19, "setSelectedIndex:", v14);
  v22 = v21(self, "ziXuanTabbarController");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v24(v23, "setSelectedIndex:", v16);
}

//----- (000000010091A98D) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController zuiJinLinLanBtnClicked:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12

  v3 = (unsigned __int8)-[ZiXuanGuViewController rightAccessaryState](self, "rightAccessaryState", a3);
  v4(self, "setRightAccessaryState:", (v3 & 1u) + 2);
  v6 = v5(self, "aiGroupContainerController");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = v8(v7, "controllerID");
  v11 = v10(self, "tabContainer");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = v13(v12, "getIndexWithId:", v9);
  v16 = v15(self, "getVcIndexWithTabId:", v9);
  v18 = v17(self, "tabContainer");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20(v19, "setSelectedIndex:", v14);
  v22 = v21(self, "ziXuanTabbarController");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v24(v23, "setSelectedIndex:", v16);
}

//----- (000000010091AAAD) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController showAndHideRightClick:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  SEL v5; // r12
  SEL v7; // r12
  __CFString *v9; // rdi
  NSDictionary *v10; // rax
  NSDictionary *v11; // rbx
  __CFString *v14; // [rsp+0h] [rbp-40h] BYREF
  __CFString *v15; // [rsp+8h] [rbp-38h] BYREF

  v3 = (unsigned __int8)-[ZiXuanGuViewController isRightViewHide](self, "isRightViewHide", a3);
  -[ZiXuanGuViewController setIsRightViewHide:](self, "setIsRightViewHide:", v3 == 0);
  v4 = (unsigned __int8)-[ZiXuanGuViewController rightAccessaryState](self, "rightAccessaryState");
  v6 = (unsigned __int8)_objc_msgSend(self, v5);
  -[ZiXuanGuViewController setRightAccessaryState:](self, "setRightAccessaryState:", ((v4 & 1) + (v6 & 2)) ^ 1u);
  v8 = (unsigned __int8)_objc_msgSend(self, v7);
  v9 = CFSTR("右_模块状态");
  if ( (v8 & 1) == 0 )
    v9 = CFSTR("_模块状态");
  v14 = CFSTR("statusKey");
  v15 = CFSTR("rightViewIsExistence");
  objc_retain(v9);
  v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v15, &v14, 1LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    12LL,
    v12,
    v11,
    1LL);
}

//----- (000000010091ABEE) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController showToolBox:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  HXCPToolBoxWC *v13; // rax
  HXCPToolBoxWC *v36; // [rsp+10h] [rbp-30h]

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "ziXuanTabbarController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "selectedViewController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(&OBJC_CLASS___QuoteForMultiStocksTemplateVC, "class");
  if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v9) )
  {
    _objc_msgSend(v10, "displayPickControl:multiQuote:", v3, v8);
  }
  else
  {
    v35 = v3;
    v11 = (unsigned __int8)_objc_msgSend(v10, "toolBoxOpened");
    _objc_msgSend(v12, "setToolBoxOpened:", v11 == 0);
    v13 = +[HXCPToolBoxWC sharedInstance](&OBJC_CLASS___HXCPToolBoxWC, "sharedInstance");
    v36 = objc_retainAutoreleasedReturnValue(v13);
    v15 = (unsigned __int8)_objc_msgSend(v14, "toolBoxOpened");
    v17 = _objc_msgSend(v16, "customizablePageToolBoxBtn");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    if ( v15 )
    {
      v19 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("toolbox1"));
      v20 = objc_retainAutoreleasedReturnValue(v19);
      _objc_msgSend(v18, "setImage:", v20);
      v22 = _objc_msgSend(v21, "view");
      v23 = objc_retainAutoreleasedReturnValue(v22);
      v24 = _objc_msgSend(v23, "window");
      v25 = objc_retainAutoreleasedReturnValue(v24);
      -[HXCPToolBoxWC showAsChildWindowIn:delegate:](v36, "showAsChildWindowIn:delegate:", v25, v26);
      v3 = v35;
    }
    else
    {
      v27 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("toolbox0"));
      v28 = objc_retainAutoreleasedReturnValue(v27);
      _objc_msgSend(v18, "setImage:", v28);
      v29(v18);
      v31 = v30;
      v32 = _objc_msgSend(v36, "window");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      _objc_msgSend(v33, "isVisible");
      v31(v33);
      v3 = v35;
      if ( v34 )
        -[HXCPToolBoxWC close](v36, "close");
    }
  }
}

//----- (000000010091AE5A) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController displayPickControl:multiQuote:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  NSNumber *v10; // rax
  NSNumber *v11; // r13
  NSNumber *v14; // rax
  NSNumber *v15; // r14
  NSArray *v16; // rax
  NSArray *v17; // rbx
  _QWORD v41[4]; // [rsp+48h] [rbp-D8h] BYREF
  id to; // [rsp+68h] [rbp-B8h] BYREF
  id location; // [rsp+A8h] [rbp-78h] BYREF
  id from; // [rsp+B0h] [rbp-70h] BYREF
  _QWORD v57[2]; // [rsp+E0h] [rbp-40h] BYREF

  v55 = objc_retain(a3);
  objc_retain(a4);
  v5 = objc_alloc(&OBJC_CLASS___NSMenu);
  v56 = _objc_msgSend(v5, "init");
  v6 = objc_alloc(&OBJC_CLASS___NSMenuItem);
  v54 = _objc_msgSend(v6, "init");
  v7 = objc_alloc((Class)&OBJC_CLASS___ItemCountsControlView);
  v53 = _objc_msgSend(v7, "initWithFrame:");
  val = v8;
  v9 = _objc_msgSend(v8, "rowCount");
  v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v57[0] = v11;
  v13 = _objc_msgSend(v12, "columnCount");
  v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v13);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v57[1] = v15;
  v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v57, 2LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = v53;
  v19(v53, "setDisplayControlArr:", v17);
  v20(v15);
  v21(v11);
  objc_initWeak(&location, val);
  objc_initWeak(&from, v56);
  v41[0] = _NSConcreteStackBlock;
  v41[1] = 3254779904LL;
  v41[2] = sub_10091B2CA;
  v41[3] = &unk_1012E3758;
  objc_copyWeak(&to, &location);
  objc_copyWeak(&v43, &from);
  _objc_msgSend(v18, "setItemCountsComfirmedCallBlock:", v41);
  v22 = v54;
  _objc_msgSend(v54, "setView:", v18);
  _objc_msgSend(v56, "addItem:", v22);
  v23 = _objc_msgSend(NSApp, "currentEvent");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  _objc_msgSend(v24, "locationInWindow");
  v45 = 0.0;
  v44 = 176.0;
  v46 = &OBJC_CLASS___NSEvent;
  v47 = _objc_msgSend(v25, "type");
  v48 = _objc_msgSend(v26, "modifierFlags");
  _objc_msgSend(v27, "timestamp");
  v49 = 0.0;
  v29 = _objc_msgSend(v28, "windowNumber");
  v31 = _objc_msgSend(v30, "context");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  v34 = _objc_msgSend(v33, "eventNumber");
  v36 = _objc_msgSend(v35, "clickCount");
  _objc_msgSend(v37, "pressure");
  v38 = _objc_msgSend(
          v46,
          "mouseEventWithType:location:modifierFlags:timestamp:windowNumber:context:eventNumber:clickCount:pressure:",
          v47,
          v48,
          v29,
          v32,
          v45,
          v44 + -20.0,
          v49,
          0.0,
          v34,
          v36);
  v39 = objc_retainAutoreleasedReturnValue(v38);
  _objc_msgSend(&OBJC_CLASS___NSMenu, "popUpContextMenu:withEvent:forView:", v56, v39, v55);
  objc_destroyWeak(&v43);
  objc_destroyWeak(&to);
  objc_destroyWeak(&from);
  objc_destroyWeak(&location);
}

//----- (000000010091B2CA) ----------------------------------------------------
void __fastcall sub_10091B2CA(__int64 a1, double a2, double a3)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "updateRowCount:columnCount:", (unsigned int)(int)a3, (unsigned int)(int)a2);
  v5 = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(v5, "cancelTrackingWithoutAnimation");
}

//----- (000000010091B34A) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController keyDownFromSuper:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  HXTabbarController *v3; // rax
  HXTabbarController *v4; // r15
  HXTabbarController *v10; // rax
  HXTabbarController *v11; // r15

  objc_retain(a3);
  v3 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXTabbarController selectedViewController](v4, "selectedViewController");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v6, "respondsToSelector:", "keyDownFromSuper:");
  if ( v7 )
  {
    v10 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = -[HXTabbarController selectedViewController](v11, "selectedViewController");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "keyDownFromSuper:", v14);
  }
}

//----- (000000010091B43A) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController selectSSGWithGroupIndex:](
        ZiXuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{
  SelfStockContainerController *v4; // rax
  SelfStockContainerController *v5; // rbx

  -[ZiXuanGuViewController quanBuZiXuanBtnClicked:](self, "quanBuZiXuanBtnClicked:", 0LL);
  v4 = -[ZiXuanGuViewController selfStockContainerController](self, "selfStockContainerController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[SelfStockContainerController selectSSGWithGroupIndex:](v5, "selectSSGWithGroupIndex:", a3);
}

//----- (000000010091B498) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController jumpToStockFormPick](ZiXuanGuViewController *self, SEL a2)
{
  AIGroupContainerController *v2; // rax
  AIGroupContainerController *v3; // rbx

  -[ZiXuanGuViewController zuiJinLinLanBtnClicked:](self, "zuiJinLinLanBtnClicked:", 0LL);
  v2 = -[ZiXuanGuViewController aiGroupContainerController](self, "aiGroupContainerController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[AIGroupContainerController jumpToStockFormPickViewController](v3, "jumpToStockFormPickViewController");
}

//----- (000000010091B4E7) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController jumpTozuiJinLinLan](ZiXuanGuViewController *self, SEL a2)
{
  AIGroupContainerController *v2; // rax
  AIGroupContainerController *v3; // rbx

  -[ZiXuanGuViewController zuiJinLinLanBtnClicked:](self, "zuiJinLinLanBtnClicked:", 0LL);
  v2 = -[ZiXuanGuViewController aiGroupContainerController](self, "aiGroupContainerController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[AIGroupContainerController jumpTorecentlyViewController](v3, "jumpTorecentlyViewController");
}

//----- (000000010091B536) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController jumpToLongHuBang](ZiXuanGuViewController *self, SEL a2)
{
  AIGroupContainerController *v2; // rax
  AIGroupContainerController *v3; // rbx

  -[ZiXuanGuViewController zuiJinLinLanBtnClicked:](self, "zuiJinLinLanBtnClicked:", 0LL);
  v2 = -[ZiXuanGuViewController aiGroupContainerController](self, "aiGroupContainerController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[AIGroupContainerController jumpToLongHuBnagViewController](v3, "jumpToLongHuBnagViewController");
}

//----- (000000010091B585) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController addNewViewController:withTabItem:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  HXPageTabContainer *v9; // rax
  HXPageTabContainer *v10; // r15
  HXPageTabContainer *v11; // rax
  HXPageTabContainer *v12; // r13
  NSArray *v13; // rax
  NSArray *v14; // r14
  HXPageTabContainer *v17; // rdi
  ZiXuanGuViewController *v18; // r13
  id (*v19)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  unsigned __int64 v36; // r14
  ZiXuanGuViewController *v38; // r12
  unsigned __int8 (__fastcall *v49)(id, const char *, __int64); // r12
  id (*v52)(id, SEL, ...); // r12
  id (*v55)(id, SEL, ...); // r12
  id (*v58)(id, SEL, ...); // r12
  id (*v61)(id, SEL, ...); // r12
  id (*v65)(id, SEL, ...); // r12
  id (*v67)(id, SEL, ...); // r12
  id (*v70)(id, SEL, ...); // r12
  ZiXuanGuViewController *v71; // rdi
  id (*v73)(id, SEL, ...); // r12
  id (*v77)(id, SEL, ...); // r12
  id (*v79)(id, SEL, ...); // r12
  id (*v82)(id, SEL, ...); // r12
  id (*v84)(id, SEL, ...); // r12

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v95 = v5;
  if ( v5 )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___HXBaseViewController, "class");
    v8 = (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v7);
    if ( v6 )
    {
      if ( v8 )
      {
        v88 = v6;
        _objc_msgSend(v6, "copy");
        v9 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
        v10 = objc_retainAutoreleasedReturnValue(v9);
        v11 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        v13 = -[HXBaseTabContainer tabViewArray](v12, "tabViewArray");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(v14, "count");
        v89 = v16;
        -[HXBaseTabContainer addTabWithItem:atIndex:](v10, "addTabWithItem:atIndex:", v16, v15);
        v17 = v12;
        v18 = self;
        v20 = v19(&OBJC_CLASS___HXCPViewController, "class");
        if ( (unsigned __int8)v21(v5, "isKindOfClass:", v20) )
        {
          if ( (unsigned __int8)_objc_msgSend(v5, "lockState") )
          {
            v23 = v22(self, "tabContainer");
            v24 = objc_retainAutoreleasedReturnValue(v23);
            v26 = v25(v89, "tabID");
            v91 = v27(v24, "getIndexWithId:", v26);
            v29 = v28(self, "tabContainer");
            v30 = objc_retainAutoreleasedReturnValue(v29);
            v32 = v31;
            v33 = v30;
            v34 = (void *)v31(v30, "tabViewArray");
            v35 = objc_retainAutoreleasedReturnValue(v34);
            v36 = v32(v35, "count");
            v37 = v33;
            v18 = v38;
            if ( (unsigned __int64)v91 < v36 )
            {
              v39 = v22(v18, "tabContainer");
              v40 = objc_retainAutoreleasedReturnValue(v39);
              v42 = (void *)v41(v40, "tabViewArray");
              v43 = objc_retainAutoreleasedReturnValue(v42);
              v45 = (void *)v44(v43, "objectAtIndexedSubscript:", v91);
              v46 = objc_retainAutoreleasedReturnValue(v45);
              v48 = v47(&OBJC_CLASS___HXPageTabView, "class");
              if ( v49(v46, "isKindOfClass:", v48) )
                _objc_msgSend(v46, "updateLockState:", 1LL);
              v18 = self;
            }
          }
        }
        v50 = v22(v89, "tabID");
        v51(v95, "setupControllerID:", v50);
        v53 = v52(v18, "ziXuanTabbarController");
        v54 = objc_retainAutoreleasedReturnValue(v53);
        v56 = v55(v54, "viewControllers");
        v57 = objc_retainAutoreleasedReturnValue(v56);
        v59 = v58(v57, "mutableCopy");
        v92 = v59;
        v60(v59, "addObject:", v95);
        v62 = v61(v18, "ziXuanTabbarController");
        v63 = objc_retainAutoreleasedReturnValue(v62);
        v64(v63, "setViewControllers:", v59);
        v66 = v65(v18, "tabContainer");
        v93 = objc_retainAutoreleasedReturnValue(v66);
        v68 = v67(v93, "tabViewArray");
        v69 = objc_retainAutoreleasedReturnValue(v68);
        v71 = v18;
        v72 = (char *)v70(v69, "count") - 1;
        v74 = v73(v71, "tabContainer");
        v75 = objc_retainAutoreleasedReturnValue(v74);
        v76(v75, "setSelectedIndex:", v72);
        v78 = v77(self, "ziXuanTabbarController");
        v94 = objc_retainAutoreleasedReturnValue(v78);
        v80 = v79(v94, "viewControllers");
        v81 = objc_retainAutoreleasedReturnValue(v80);
        v83 = (char *)v82(v81, "count") - 1;
        v85 = v84(self, "ziXuanTabbarController");
        v86 = objc_retainAutoreleasedReturnValue(v85);
        v87(v86, "setSelectedIndex:", v83);
        v6 = v88;
      }
    }
  }
}

//----- (000000010091BA2E) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController insertSelectedPageWithPageType:](
        ZiXuanGuViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  HXPageTabContainer *v3; // rax
  HXPageTabContainer *v4; // rax
  NSArray *v5; // rax
  NSArray *v6; // rbx
  HXPageTabContainer *v8; // rax
  HXPageTabContainer *v9; // r15
  HXTabbarController *v11; // rax
  HXTabbarController *v12; // rax
  NSArray *v13; // rax
  NSArray *v14; // r14
  HXTabbarController *v16; // rax
  HXTabbarController *v17; // rbx

  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", a3);
  v3 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXBaseTabContainer tabViewArray](v4, "tabViewArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (char *)_objc_msgSend(v6, "count") - 1;
  v8 = (HXPageTabContainer *)-[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXPageTabContainer setSelectedIndex:](v9, "setSelectedIndex:", v7);
  v11 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = -[HXTabbarController viewControllers](v12, "viewControllers");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = (char *)_objc_msgSend(v14, "count") - 1;
  v16 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  -[HXTabbarController setSelectedIndex:](v17, "setSelectedIndex:", v15);
}

//----- (000000010091BB73) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController jumpToCustomizeTemplatePage:](
        ZiXuanGuViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  HXTabbarController *v3; // rax
  HXTabbarController *v4; // r13
  NSArray *v5; // rax
  NSArray *v6; // rbx
  unsigned __int64 v14; // [rsp+60h] [rbp-30h]

  v14 = a3;
  v3 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXTabbarController viewControllers](v4, "viewControllers");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "copy");
  v10 = 0LL;
  v11 = &v10;
  v12 = 0x2020000000LL;
  v13 = 0;
  _objc_msgSend(v7, "enumerateObjectsUsingBlock:");
  LOBYTE(v6) = *((_BYTE *)v11 + 24);
  _Block_object_dispose(&v10, 8);
  return (char)v6;
}

//----- (000000010091BC9D) ----------------------------------------------------
void __fastcall sub_10091BC9D(__int64 a1, void *a2, __int64 a3)
{
  _BYTE *v6; // r12
  _BYTE *v22; // [rsp+0h] [rbp-40h]

  v4 = objc_retain(a2);
  if ( (unsigned __int8)_objc_msgSend(v4, "respondsToSelector:", "pageType")
    && _objc_msgSend(v4, "pageType") == *(id *)(a1 + 48) )
  {
    v5 = *(void **)(a1 + 32);
    objc_retain(v4);
    v22 = v6;
    v23 = a3;
    v7 = _objc_msgSend(v5, "tabContainer");
    v24 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v8, "controllerID");
    _objc_msgSend(v24, "getIndexWithId:", v9);
    v11 = _objc_msgSend(*(id *)(a1 + 32), "tabContainer");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v12, "setSelectedIndex:", v13);
    v14 = _objc_msgSend(*(id *)(a1 + 32), "ziXuanTabbarController");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(v15, "setSelectedIndex:", v23);
    v16(v15);
    *(_BYTE *)(*(_QWORD *)(*(_QWORD *)(a1 + 40) + 8LL) + 24LL) = 1;
    v17 = _objc_msgSend(*(id *)(a1 + 32), "view");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = _objc_msgSend(v18, "window");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v20, "makeKeyAndOrderFront:", 0LL);
    *v22 = v21;
  }
}

//----- (000000010091BE58) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController getPageTabContainer](ZiXuanGuViewController *self, SEL a2)
{
  return -[ZiXuanGuViewController tabContainer](self, "tabContainer");
}

//----- (000000010091BE6A) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController getSelectedViewController](ZiXuanGuViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx

  v2 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedViewController](v3, "selectedViewController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  return objc_autoreleaseReturnValue(v5);
}

//----- (000000010091BEBA) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController showCustomizablePageToolBoxBtn](ZiXuanGuViewController *self, SEL a2)
{
  NSButton *v2; // rax
  NSButton *v3; // rbx

  v2 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[ZiXuanGuViewController showToolBox:](self, "showToolBox:", v3);
  -[ZiXuanGuViewController openCustomizablePageToolBox](self, "openCustomizablePageToolBox");
}

//----- (000000010091BF15) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController createTabItemObjects](ZiXuanGuViewController *self, SEL a2)
{
  HXPageTabContainer *v2; // rax
  HXPageTabContainer *v3; // rbx
  NSArray *v4; // rax
  _QWORD v11[5]; // [rsp+0h] [rbp-60h] BYREF

  v13 = _objc_msgSend(__NSArray0__, "mutableCopy");
  v2 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXBaseTabContainer tabViewArray](v3, "tabViewArray");
  objc_retainAutoreleasedReturnValue(v4);
  v11[0] = _NSConcreteStackBlock;
  v11[1] = 3254779904LL;
  v11[2] = sub_10091C00A;
  v11[3] = &unk_1012E36F8;
  v11[4] = self;
  v5 = objc_retain(v13);
  v12 = v5;
  _objc_msgSend(v6, "enumerateObjectsUsingBlock:", v11);
  v7 = v12;
  v8 = objc_retainAutoreleaseReturnValue(v5);
  return v8;
}

//----- (000000010091C00A) ----------------------------------------------------
void __fastcall sub_10091C00A(__int64 a1, void *a2)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  unsigned __int64 v26; // r12
  id (*v29)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (*v40)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12

  v46 = objc_retain(a2);
  v2 = objc_alloc((Class)&OBJC_CLASS___HXCPTabItemObject);
  v45 = _objc_msgSend(v2, "init");
  v4 = v3(v46, "tabItem");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(v5, "title");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v45, "setPageTitle:", v8);
  v10 = *(void **)(a1 + 32);
  v12 = v11(v46, "tabItem");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v15 = v14(v13, "tabID");
  v17 = v16(v10, "getVcIndexWithTabId:", v15);
  v18 = a1;
  v20 = v19(*(id *)(a1 + 32), "ziXuanTabbarController");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v23 = v22(v21, "viewControllers");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v25(v24, "count");
  if ( (unsigned __int64)v17 < v26 )
  {
    v27 = _objc_msgSend(*(id *)(a1 + 32), "ziXuanTabbarController");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v30 = v29(v28, "viewControllers");
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v33 = v32(v31, "objectAtIndexedSubscript:", v17);
    v34 = objc_retainAutoreleasedReturnValue(v33);
    if ( (unsigned __int8)v35(v34, "respondsToSelector:", "pageType") )
    {
      v37 = v36(v34, "pageType");
      v38(v45, "setPageType:", v37);
    }
    v39 = v36(&OBJC_CLASS___HXCPViewController, "class");
    if ( (unsigned __int8)v40(v34, "isKindOfClass:", v39) )
    {
      v42 = v41(v34, "buildCurrentPageObject");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v44(v45, "setPageObject:", v43);
    }
    v18 = a1;
  }
  _objc_msgSend(*(id *)(v18 + 40), "addObject:", v45);
}

//----- (000000010091C28B) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setViewControllersOriginalState](ZiXuanGuViewController *self, SEL a2)
{
  HXBaseView *v2; // rax
  HXTabbarController *v3; // rax
  HXTabbarController *v4; // rbx
  HXTabbarController *v7; // rax
  HXTabbarController *v8; // rbx
  HXPageTabContainer *v9; // rax
  HXPageTabContainer *v10; // rbx
  SEL v11; // r12
  SEL v14; // r12

  v2 = -[ZiXuanGuViewController contentView](self, "contentView");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  _objc_msgSend(v4, "setView:", v5);
  v7 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  -[HXTabbarController setViewControllers:](v8, "setViewControllers:", __NSArray0__);
  v9 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[HXBaseTabContainer setHasAddBtn:](v10, "setHasAddBtn:", 1LL);
  v12 = _objc_msgSend(self, v11);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "setDelegate:", self);
  v15 = _objc_msgSend(self, v14);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v16, "setEnableDrag:", 1LL);
}

//----- (000000010091C3C1) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController insertDefaultPages](ZiXuanGuViewController *self, SEL a2)
{
  SelfStockContainerController *v2; // rax
  SelfStockContainerController *v3; // r15
  AIGroupContainerController *v4; // rax
  AIGroupContainerController *v5; // rbx
  HXTabbarController *v10; // rax
  HXTabbarController *v11; // rbx
  NSArray *v13; // rax
  unsigned __int64 v14; // r13
  SEL v19; // r12
  HXPageTabContainer *v20; // rax
  HXPageTabContainer *v21; // rbx
  HXPageTabContainer *v23; // rax
  HXPageTabContainer *v24; // rbx
  HXTabbarController *v25; // rax
  HXTabbarController *v26; // rbx
  NSArray *v28; // [rsp+58h] [rbp-68h]
  _QWORD v31[2]; // [rsp+70h] [rbp-50h] BYREF
  _QWORD v32[2]; // [rsp+80h] [rbp-40h] BYREF

  v2 = -[ZiXuanGuViewController selfStockContainerController](self, "selfStockContainerController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v32[0] = v3;
  v4 = -[ZiXuanGuViewController aiGroupContainerController](self, "aiGroupContainerController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v32[1] = v5;
  v7 = (void *)v6(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v32, 2LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v3);
  v10 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[HXTabbarController setViewControllers:](v11, "setViewControllers:", v8);
  v12(v11);
  v29 = _objc_msgSend(__NSArray0__, "mutableCopy");
  v31[0] = CFSTR("自选");
  v31[1] = CFSTR("智能分组");
  v13 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v31, 2LL);
  v28 = objc_retainAutoreleasedReturnValue(v13);
  if ( _objc_msgSend(v8, "count") )
  {
    v14 = 0LL;
    do
    {
      v15 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
      v16 = _objc_msgSend(v28, "objectAtIndexedSubscript:", v14);
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v18 = _objc_msgSend(v15, "initWithTitle:isDeletable:", v17, 0LL);
      _objc_msgSend(v18, "setAllowMenuType:", 0LL);
      _objc_msgSend(v29, "addObject:", v18);
      ++v14;
    }
    while ( (unsigned __int64)_objc_msgSend(v8, v19) > v14 );
  }
  v20 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  -[HXBaseTabContainer addTabsWithItems:](v21, "addTabsWithItems:", v29);
  v30 = objc_retain(v8);
  _objc_msgSend(v29, "enumerateObjectsUsingBlock:", v22);
  v23 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  -[HXPageTabContainer setSelectedIndex:](v24, "setSelectedIndex:", 0LL);
  v25 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  -[HXTabbarController setSelectedIndex:](v26, "setSelectedIndex:", 0LL);
  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", 3LL);
  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", 2LL);
  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", 5LL);
  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", 6LL);
  -[ZiXuanGuViewController insertCustomizablePageWithType:](self, "insertCustomizablePageWithType:", 1LL);
}

//----- (000000010091C748) ----------------------------------------------------
__int64 __fastcall sub_10091C748(__int64 a1, void *a2, __int64 a3)
{

  v4 = objc_retain(a2);
  v6 = _objc_msgSend(v5, "objectAtIndexedSubscript:", a3);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v4, "tabID");
  _objc_msgSend(v7, "setupControllerID:", v8);
  return v9(v7);
}

//----- (000000010091C7D1) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController updateTabAccessaryToolViewState](ZiXuanGuViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx

  v2 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedViewController](v3, "selectedViewController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = _objc_msgSend(v6, "selfStockContainerController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( v5 == v8 )
  {
  }
  else
  {
    v10 = v8;
    v11 = _objc_msgSend(v9, "aiGroupContainerController");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    if ( v5 != v12 )
    {
      _objc_msgSend(v13, "openCustomizablePageToolBox");
      _objc_msgSend(v14, "closeRightAccessarySwitch");
      v16 = (unsigned __int8)_objc_msgSend(v15, "rightAccessaryState");
      _objc_msgSend(v17, "setRightAccessaryState:", v16 & 1);
      goto LABEL_9;
    }
  }
  _objc_msgSend(v13, "openRightAccessarySwitch");
  _objc_msgSend(v18, "closeCustomizablePageToolBox");
  v20 = (unsigned __int8)_objc_msgSend(v19, "rightAccessaryState");
  _objc_msgSend(v21, "setRightAccessaryState:", (v20 & 1u) + 2);
  v23 = _objc_msgSend(v22, "selfStockContainerController");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  if ( v5 == v24 )
  {
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
      &OBJC_CLASS___UserLogSendingQueueManager,
      "sendUserLog:action:params:needWait:",
      11LL,
      CFSTR("自选"),
      0LL,
      1LL);
  }
  else
  {
    v26 = _objc_msgSend(v25, "aiGroupContainerController");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    if ( v5 == v27 )
      +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
        &OBJC_CLASS___UserLogSendingQueueManager,
        "sendUserLog:action:params:needWait:",
        11LL,
        CFSTR("智能分组"),
        0LL,
        1LL);
  }
LABEL_9:
}

//----- (000000010091C9A7) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController openRightAccessarySwitch](ZiXuanGuViewController *self, SEL a2)
{
  NSButton *v2; // rax
  NSButton *v3; // rbx

  v2 = -[ZiXuanGuViewController showAndHideRightBtn](self, "showAndHideRightBtn");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHidden:", 0LL);
}

//----- (000000010091C9E6) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController closeRightAccessarySwitch](ZiXuanGuViewController *self, SEL a2)
{
  NSButton *v2; // rax
  NSButton *v3; // rbx

  v2 = -[ZiXuanGuViewController showAndHideRightBtn](self, "showAndHideRightBtn");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHidden:", 1LL);
}

//----- (000000010091CA28) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController openCustomizablePageToolBox](ZiXuanGuViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  NSButton *v8; // rax
  NSButton *v13; // rax
  NSButton *v14; // rdi
  NSButton *v26; // rax
  NSButton *v27; // rdi
  NSButton *v30; // rax

  v2 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedViewController](v3, "selectedViewController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___QuoteForMultiStocksTemplateVC, "class");
  if ( !(unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6) )
  {
    v16 = _objc_msgSend(&OBJC_CLASS___MarketLandscapeTemplateVC, v7);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v16) )
      goto LABEL_9;
    v18 = _objc_msgSend(&OBJC_CLASS___HXHotGraphTemplateVC, v17);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v18) )
      goto LABEL_9;
    v20 = _objc_msgSend(&OBJC_CLASS___HXCPGuanLianBaoJiaTemplateVC, v19);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v20) )
      goto LABEL_9;
    v22 = _objc_msgSend(&OBJC_CLASS___HXCPDuanXianQinLongTemplateVC, v21);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v22) )
      goto LABEL_9;
    v24 = _objc_msgSend(&OBJC_CLASS___HXCPDaPingDingZhiTemplateVC, v23);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v24) )
      goto LABEL_9;
    v28 = _objc_msgSend(&OBJC_CLASS___HXCPViewController, v25);
    if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v28) )
    {
      if ( (unsigned __int8)_objc_msgSend(v5, "isHomePageState") )
      {
LABEL_9:
        v26 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        _objc_msgSend(v27, "setHidden:", 1LL);
        goto LABEL_10;
      }
      v29 = (unsigned __int8)-[ZiXuanGuViewController toolBoxOpened](self, "toolBoxOpened");
      v30 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
      objc_retainAutoreleasedReturnValue(v30);
      if ( v29 )
        v31 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("toolbox1"));
      else
        v31 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("toolbox0"));
      v32 = objc_retainAutoreleasedReturnValue(v31);
      _objc_msgSend(v33, "setImage:", v32);
    }
    v13 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
    goto LABEL_3;
  }
  v8 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
  objc_retainAutoreleasedReturnValue(v8);
  v9 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("icon_modularpick_normal"));
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v11, "setImage:", v10);
  v13 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
LABEL_3:
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v14, "setHidden:", 0LL);
LABEL_10:
}

//----- (000000010091CCD7) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController closeCustomizablePageToolBox](ZiXuanGuViewController *self, SEL a2)
{
  NSButton *v2; // rax
  NSButton *v3; // rbx

  v2 = -[ZiXuanGuViewController customizablePageToolBoxBtn](self, "customizablePageToolBoxBtn");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHidden:", 1LL);
}

//----- (000000010091CD19) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setViewOriginalState](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // r13
  id (*v7)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  id (*v42)(id, SEL, ...); // r12
  id (*v46)(id, SEL, ...); // r12

  WeakRetained = objc_loadWeakRetained((id *)&self->super._shouldRefresh);
  v3 = _objc_msgSend(WeakRetained, "cell");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5(v4, "setHighlightsBy:", 1LL);
  v6 = objc_loadWeakRetained((id *)&self->super.super._reserved);
  v8 = v7(v6, "cell");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v9, "setHighlightsBy:", 1LL);
  v11 = objc_loadWeakRetained((id *)&self->super.super._reserved);
  v12(v11, "setToolTip:", CFSTR("点击隐藏视图"));
  v14 = v13(&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = v16(self, "tabAccessaryToolView");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19(v18, "setBackgroundColor:", v15);
  v21 = v20(self, "tabAccessaryToolView");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23(v22, "setBottomBorder:", 1LL);
  v25 = v24(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v28 = v27(self, "tabAccessaryToolView");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  v30(v29, "setBorderColor:", v26);
  v32 = v31(self, "tabAccessaryToolView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v34(v33, "setBorderWidth:", 2.0);
  v36 = v35(self, "tabContainer");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  v38(v37, "setBottomBorder:", 1LL);
  v40 = v39(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  v43 = v42(self, "tabContainer");
  v44 = objc_retainAutoreleasedReturnValue(v43);
  v45(v44, "setBorderColor:", v41);
  v47 = v46(self, "tabContainer");
  v48 = objc_retainAutoreleasedReturnValue(v47);
  v49(v48, "setBorderWidth:", 2.0);
}

//----- (000000010091CFD7) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setRightAccessaryViewOriginalState](ZiXuanGuViewController *self, SEL a2)
{
  HXRightAccessaryViewController *v2; // rax
  id (*v33)(id, SEL, ...); // r12
  id (*v37)(id, SEL, ...); // r12
  id (*v40)(id, SEL, ...); // r12

  v2 = -[ZiXuanGuViewController rightAccessaryVC](self, "rightAccessaryVC");
  v45 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v45, "view");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "rightAccessaryView");
  v7 = (const char *)objc_retainAutoreleasedReturnValue(v6);
  v8 = (char *)v7;
  v46 = v9;
  if ( v7 )
    objc_msgSend_stret(v44, v7, "bounds");
  else
    memset(v44, 0, sizeof(v44));
  _objc_msgSend(v4, "setFrame:");
  v10 = _objc_msgSend(v46, "rightAccessaryView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = _objc_msgSend(v12, "rightAccessaryVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(v14, "view");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v11, "addSubview:", v16);
  v18 = _objc_msgSend(v17, "rightAccessaryVC");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = _objc_msgSend(v20, "tableRightPanKouVC");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v19, "pushAccessaryViewController:", v22);
  v24 = _objc_msgSend(v23, "rightAccessaryView");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "superview");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28 = _objc_msgSend(&OBJC_CLASS___HXSplitView, "class");
  LOBYTE(v14) = (unsigned __int8)_objc_msgSend(v27, "isKindOfClass:", v28);
  if ( (_BYTE)v14 )
  {
    v30 = v46;
    v31 = _objc_msgSend(v46, v29);
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v34 = v33(v32, "superview");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v36(v30, "setBaseSplitView:", v35);
    v38 = v37(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v41 = v40(v30, "baseSplitView");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    v43(v42, "setLineColor:", v39);
  }
}

//----- (000000010091D281) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController changeFrameForWiget:](ZiXuanGuViewController *self, SEL a2, double a3)
{
  HXBaseView *v3; // rax
  HXBaseView *v4; // rax
  HXBaseView *v7; // rax
  NSView *v13; // rax
  NSView *v14; // rax
  NSView *v18; // rax

  v33 = a3;
  v3 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
  objc_retainAutoreleasedReturnValue(v3);
  v4 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
  v5 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v4));
  *(double *)&v34 = v5;
  if ( v5 == 0.0 )
  {
    v25 = 0LL;
    v24 = 0LL;
    v6 = 0.0;
  }
  else
  {
    objc_msgSend_stret(&v24, *(SEL *)&v5, "frame");
    v6 = *(double *)&v25;
  }
  v32 = v6 - v33;
  v7 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = (char *)v8;
  if ( v8 )
  {
    objc_msgSend_stret(&v26, v8, "frame");
    v11 = *((double *)&v27 + 1);
  }
  else
  {
    v27 = 0LL;
    v26 = 0LL;
    v11 = 0.0;
  }
  _objc_msgSend(v9, "setFrameSize:", v32, v11);
  v13 = -[ZiXuanGuViewController leftMainView](self, "leftMainView");
  objc_retainAutoreleasedReturnValue(v13);
  v14 = -[ZiXuanGuViewController leftMainView](self, "leftMainView");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = (char *)v15;
  if ( v15 )
  {
    objc_msgSend_stret(&v28, v15, "frame");
    v17 = *(double *)&v29;
  }
  else
  {
    v29 = 0LL;
    v28 = 0LL;
    v17 = 0.0;
  }
  *(double *)&v34 = v17;
  v18 = -[ZiXuanGuViewController leftMainView](self, "leftMainView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = (char *)v19;
  if ( v19 )
  {
    objc_msgSend_stret(&v30, v19, "frame");
    v22 = *((double *)&v31 + 1);
  }
  else
  {
    v31 = 0LL;
    v30 = 0LL;
    v22 = 0.0;
  }
  _objc_msgSend(v20, "setFrameSize:", *(double *)&v34 + v33, v22);
}

//----- (000000010091D487) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController theSelectRowDidChanged:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  ZiXuanGuViewController *v8; // r14
  NSButton *v9; // rax
  NSButton *v10; // r15
  HXPanKouContainerController *v11; // rax
  HXPanKouContainerController *v14; // rax
  HXPanKouContainerController *v15; // r15
  HXPanKouContainerController *v32; // [rsp+20h] [rbp-50h]
  bool v35; // [rsp+47h] [rbp-29h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = _objc_msgSend(v3, "thsStringForKey:", CFSTR("PanKouLinkCode"));
    objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(v3, "thsStringForKey:", CFSTR("PanKouModularType"));
    v34 = objc_retainAutoreleasedReturnValue(v6);
    v7 = _objc_msgSend(v3, "objectForKeyedSubscript:", CFSTR("AutoOpenRightView"));
    v31 = objc_retainAutoreleasedReturnValue(v7);
    if ( (unsigned __int8)_objc_msgSend(v31, "boolValue") )
    {
      v8 = self;
      if ( (unsigned __int8)-[ZiXuanGuViewController isRightViewHide](self, "isRightViewHide") )
      {
        v9 = -[ZiXuanGuViewController showAndHideRightBtn](self, "showAndHideRightBtn");
        v10 = objc_retainAutoreleasedReturnValue(v9);
        -[ZiXuanGuViewController showAndHideRightClick:](self, "showAndHideRightClick:", v10);
        v8 = self;
      }
    }
    else
    {
      v8 = self;
    }
    v29 = v3;
    v11 = -[ZiXuanGuViewController tableRightPanKouVC](v8, "tableRightPanKouVC");
    v32 = objc_retainAutoreleasedReturnValue(v11);
    v12 = -[HXPanKouContainerController getLinkCode](v32, "getLinkCode");
    v33 = objc_retainAutoreleasedReturnValue(v12);
    v30 = v13;
    if ( (unsigned __int8)_objc_msgSend(v33, "isEqualToString:", v13) )
    {
      v14 = -[ZiXuanGuViewController tableRightPanKouVC](v8, "tableRightPanKouVC");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = -[HXPanKouContainerController getStrPankouType](v15, "getStrPankouType");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v35 = (unsigned __int8)_objc_msgSend(v17, "isEqualToString:", v34) == 0;
    }
    else
    {
      v35 = 1;
    }
    v19 = v35 & ((unsigned __int8)-[ZiXuanGuViewController isRightViewHide](v8, "isRightViewHide") == 0);
    v21 = _objc_msgSend(v20, "tableRightPanKouVC");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v23 = v19;
    v3 = v29;
    _objc_msgSend(v22, "updatePanKouSubViews:params:needRequset:", v34, v29, v23);
    if ( (__int64)_objc_msgSend(v30, "length") )
    {
      v25 = +[EnvironmentVariablesManager shareInstance](&OBJC_CLASS___EnvironmentVariablesManager, "shareInstance");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      _objc_msgSend(v26, "setCurrentMarketStockCode:", v30);
    }
    v24(v31);
    v27(v34);
    v28(v30);
  }
}

//----- (000000010091D736) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController renameMyTemplateTab:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  SEL v11; // r12
  SEL v15; // r12
  SEL v24; // r12

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNotification, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v5 = _objc_msgSend(v3, "userInfo");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v63 = v7;
    v8 = _objc_msgSend(v6, "thsStringForKey:", off_1012E4760[0]);
    v59 = objc_retainAutoreleasedReturnValue(v8);
    v58 = v3;
    v9 = _objc_msgSend(v3, "userInfo");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = _objc_msgSend(v10, v11, CFSTR("name"));
    objc_retainAutoreleasedReturnValue(v12);
    v60 = v13;
    if ( v13 )
    {
      if ( _objc_msgSend(v13, "length") )
      {
        v14 = _objc_msgSend(v63, "tabContainer");
        v61 = objc_retainAutoreleasedReturnValue(v14);
        v16 = _objc_msgSend(v61, v15);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = _objc_msgSend(v17, "count");
        v19 = v17;
        v20 = v63;
        if ( v18 )
        {
          v22 = 0LL;
          v57 = v21;
          do
          {
            v62 = v22;
            v23 = _objc_msgSend(v20, "tabContainer");
            v64 = objc_retainAutoreleasedReturnValue(v23);
            v25 = _objc_msgSend(v64, v24);
            v26 = objc_retainAutoreleasedReturnValue(v25);
            v27 = _objc_msgSend(v26, "objectAtIndexedSubscript:", v22);
            objc_retainAutoreleasedReturnValue(v27);
            v29 = v28;
            v30 = _objc_msgSend(&OBJC_CLASS___HXPageTabView, "class");
            if ( (unsigned __int8)_objc_msgSend(v29, "isKindOfClass:", v30) )
            {
              v65 = v29;
              v31 = _objc_msgSend(v29, "tabItem");
              v32 = objc_retainAutoreleasedReturnValue(v31);
              v33 = _objc_msgSend(v32, "tabID");
              v35 = _objc_msgSend(v34, "getVcIndexWithTabId:", v33);
              v37 = _objc_msgSend(v36, "ziXuanTabbarController");
              v38 = objc_retainAutoreleasedReturnValue(v37);
              v39 = _objc_msgSend(v38, "viewControllers");
              v40 = objc_retainAutoreleasedReturnValue(v39);
              v41 = _objc_msgSend(v40, "objectAtIndexedSubscript:", v35);
              v42 = objc_retainAutoreleasedReturnValue(v41);
              v44 = _objc_msgSend(&OBJC_CLASS___HXCPViewController, "class");
              if ( (unsigned __int8)_objc_msgSend(v42, "isKindOfClass:", v44) )
              {
                v45 = _objc_msgSend(v42, "object");
                v46 = objc_retainAutoreleasedReturnValue(v45);
                v47 = _objc_msgSend(v46, "pageUUID");
                v48 = objc_retainAutoreleasedReturnValue(v47);
                _objc_msgSend(v48, "isEqualToString:", v59);
                if ( v49 )
                {
                  _objc_msgSend(v65, "renameWithNewName:", v60);
                  break;
                }
              }
              v29 = v65;
            }
            v51 = _objc_msgSend(v50, "tabContainer");
            v52 = objc_retainAutoreleasedReturnValue(v51);
            v53 = _objc_msgSend(v52, v57);
            v54 = objc_retainAutoreleasedReturnValue(v53);
            v55 = (char *)_objc_msgSend(v54, "count");
            v56 = v52;
            v20 = v63;
            v22 = v62 + 1;
          }
          while ( v55 > v62 + 1 );
        }
      }
    }
    v3 = v58;
  }
}

//----- (000000010091DBC3) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController deleteMyTemplateTab:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  id (**v15)(id, SEL, ...); // rbx
  id (**v17)(id, SEL, ...); // r15
  id (**v34)(id, SEL, ...); // r13
  unsigned __int64 v53; // r15

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNotification, "class");
  if ( !(unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
    goto LABEL_12;
  v56 = v3;
  v5 = _objc_msgSend(v3, "userInfo");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "thsStringForKey:", off_1012E4760[0]);
  v58 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "tabContainer");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "tabViewArray");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v12, "count");
  if ( !v13 )
    goto LABEL_11;
  v15 = &_objc_msgSend;
  v59 = v14;
  while ( 1 )
  {
    v16 = (void *)((__int64 (__fastcall *)(id, const char *))v15)(v14, "tabContainer");
    v17 = v15;
    v18 = objc_retainAutoreleasedReturnValue(v16);
    v19 = (void *)((__int64 (__fastcall *)(id, const char *))v15)(v18, "tabViewArray");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v60 = v21;
    v22 = (void *)((__int64 (__fastcall *)(id, const char *, char *))v17)(v20, "objectAtIndexedSubscript:", v21);
    objc_retainAutoreleasedReturnValue(v22);
    v24 = v23;
    v25 = ((__int64 (__fastcall *)(__objc2_class *, const char *))v17)(&OBJC_CLASS___HXPageTabView, "class");
    if ( ((unsigned __int8 (__fastcall *)(void *, const char *, __int64))v17)(v24, "isKindOfClass:", v25) )
      break;
LABEL_8:
    v48 = v60 + 1;
    v50 = (void *)((__int64 (__fastcall *)(__int64, const char *))v17)(v49, "tabContainer");
    v61 = objc_retainAutoreleasedReturnValue(v50);
    v51 = (void *)((__int64 (__fastcall *)(id, const char *))v17)(v61, "tabViewArray");
    v52 = objc_retainAutoreleasedReturnValue(v51);
    v15 = v17;
    v53 = ((__int64 (__fastcall *)(id, const char *))v17)(v52, "count");
    v14 = v59;
    if ( v53 <= (unsigned __int64)v48 )
      goto LABEL_11;
  }
  v57 = v24;
  v26 = (void *)((__int64 (__fastcall *)(void *, const char *))v17)(v24, "tabItem");
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v28 = ((__int64 (__fastcall *)(id, const char *))v17)(v27, "tabID");
  v55 = v28;
  v30 = ((__int64 (__fastcall *)(__int64, const char *, __int64))v17)(v29, "getVcIndexWithTabId:", v28);
  v32 = (void *)((__int64 (__fastcall *)(__int64, const char *))v17)(v31, "ziXuanTabbarController");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v34 = v17;
  v35 = (void *)((__int64 (__fastcall *)(id, const char *))v17)(v33, "viewControllers");
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v37 = (void *)((__int64 (__fastcall *)(id, const char *, __int64))v34)(v36, "objectAtIndexedSubscript:", v30);
  v38 = objc_retainAutoreleasedReturnValue(v37);
  v40 = ((__int64 (__fastcall *)(__objc2_class *, const char *))v34)(&OBJC_CLASS___HXCPViewController, "class");
  if ( !((unsigned __int8 (__fastcall *)(id, const char *, __int64))v34)(v38, "isKindOfClass:", v40) )
    goto LABEL_7;
  v41 = (void *)((__int64 (__fastcall *)(id, const char *))v34)(v38, "object");
  v42 = objc_retainAutoreleasedReturnValue(v41);
  v43 = (void *)((__int64 (__fastcall *)(id, const char *))v34)(v42, "pageUUID");
  v44 = v38;
  v45 = objc_retainAutoreleasedReturnValue(v43);
  ((void (__fastcall *)(id, const char *, id))v34)(v45, "isEqualToString:", v58);
  v46 = v45;
  v38 = v44;
  if ( !v47 )
  {
LABEL_7:
    v17 = v34;
    v24 = v57;
    goto LABEL_8;
  }
  _objc_msgSend(v59, "deleteTabWithId:index:", v55, (unsigned int)v60);
LABEL_11:
  v3 = v56;
LABEL_12:
}

//----- (000000010091DFC7) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController loadMyTemplate:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  HXCPManager *v5; // rax
  HXCPManager *v6; // r14
  NSMutableArray *v7; // rax
  NSMutableArray *v8; // rax
  id (*v14)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  unsigned __int64 i; // r14
  id (*v21)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  HXCPManager *v48; // rax
  HXCPManager *v49; // rbx
  NSMutableArray *v50; // rax
  unsigned __int64 j; // r15
  SEL v55; // r12
  SEL v103; // [rsp+88h] [rbp-188h]
  SEL v104; // [rsp+90h] [rbp-180h]
  id obj; // [rsp+98h] [rbp-178h]
  SEL v107; // [rsp+A8h] [rbp-168h]
  SEL v108; // [rsp+B0h] [rbp-160h]
  SEL v110; // [rsp+C0h] [rbp-150h]

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "length") )
    {
      v5 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v7 = -[HXCPManager myTemplateArr](v6, "myTemplateArr");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      v109 = self;
      v9 = _objc_msgSend(v8, "count");
      if ( v9 )
      {
        v106 = v4;
        v98 = 0LL;
        v97 = 0LL;
        v96 = 0LL;
        v95 = 0LL;
        v12 = _objc_msgSend(v11, "ziXuanTabbarController");
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v15 = v14(v13, "viewControllers");
        v16 = objc_retainAutoreleasedReturnValue(v15);
        v113 = v17(v16, "countByEnumeratingWithState:objects:count:", &v95, v115, 16LL);
        if ( v113 )
        {
          obj = v16;
          v112 = *(id *)v96;
          v111 = 0LL;
          do
          {
            v110 = "class";
            v107 = "isKindOfClass:";
            v104 = "object";
            v108 = "pageUUID";
            v103 = "isEqualToString:";
            for ( i = 0LL; i < (unsigned __int64)v113; ++i )
            {
              if ( *(id *)v96 != v112 )
                objc_enumerationMutation(obj);
              v19 = *(void **)(*((_QWORD *)&v95 + 1) + 8 * i);
              v20 = _objc_msgSend(&OBJC_CLASS___HXCPViewController, v110);
              if ( (unsigned __int8)v21(v19, v107, v20) )
              {
                v22 = objc_retain(v19);
                v24 = v23(v22, v104);
                v25 = objc_retainAutoreleasedReturnValue(v24);
                v27 = v26(v25, v108);
                v28 = objc_retainAutoreleasedReturnValue(v27);
                v29(v28, v103, v106);
                if ( v30 )
                {
                  v31 = objc_retain(v22);
                  v111 = v31;
                }
              }
            }
            v113 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v95, v115, 16LL);
          }
          while ( v113 );
          if ( v111 )
          {
            v33 = v32;
            v34 = _objc_msgSend(v109, "ziXuanTabbarController");
            v35 = objc_retainAutoreleasedReturnValue(v34);
            v36 = _objc_msgSend(v35, v33);
            v37 = objc_retainAutoreleasedReturnValue(v36);
            v113 = _objc_msgSend(v37, "indexOfObject:", v38);
            v39 = v109;
            v40 = _objc_msgSend(v109, "tabContainer");
            v41 = objc_retainAutoreleasedReturnValue(v40);
            v42 = v113;
            v43(v41, "setSelectedIndex:", v113);
            v45 = (void *)v44(v39, "ziXuanTabbarController");
            v46 = objc_retainAutoreleasedReturnValue(v45);
            v47(v46, "setSelectedIndex:", v42);
LABEL_38:
            v4 = v106;
            goto LABEL_39;
          }
        }
        else
        {
        }
        v102 = 0LL;
        v101 = 0LL;
        v100 = 0LL;
        v99 = 0LL;
        v48 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
        v49 = objc_retainAutoreleasedReturnValue(v48);
        v50 = -[HXCPManager myTemplateArr](v49, "myTemplateArr");
        v51 = objc_retainAutoreleasedReturnValue(v50);
        v108 = v51;
        v113 = _objc_msgSend(v51, "countByEnumeratingWithState:objects:count:", &v99, v114, 16LL);
        if ( v113 )
        {
          v107 = *(SEL *)v100;
          v112 = 0LL;
          do
          {
            v110 = "class";
            v111 = "pageUUID";
            v104 = "isEqualToString:";
            for ( j = 0LL; j < (unsigned __int64)v113; ++j )
            {
              if ( *(SEL *)v100 != v107 )
                objc_enumerationMutation((id)v108);
              v53 = *(void **)(*((_QWORD *)&v99 + 1) + 8 * j);
              v54 = _objc_msgSend(&OBJC_CLASS___HXCPVCObject, v110);
              if ( (unsigned __int8)_objc_msgSend(v53, v55, v54) )
              {
                v56 = _objc_msgSend(v53, (SEL)v111);
                v57 = objc_retainAutoreleasedReturnValue(v56);
                v58 = (unsigned __int8)_objc_msgSend(v57, v104, v106);
                if ( v58 )
                {
                  v59 = objc_retain(v53);
                  v112 = v59;
                }
              }
            }
            v113 = _objc_msgSend((id)v108, "countByEnumeratingWithState:objects:count:", &v99, v114, 16LL);
          }
          while ( v113 );
        }
        else
        {
          v112 = 0LL;
        }
        v60 = objc_alloc((Class)&OBJC_CLASS___HXCPTabItemObject);
        v61 = _objc_msgSend(v60, "init");
        v62 = v112;
        v63 = _objc_msgSend(v112, "pageTitle");
        if ( objc_retainAutoreleasedReturnValue(v63) )
        {
          v64 = _objc_msgSend(v62, "pageTitle");
          v65 = objc_retainAutoreleasedReturnValue(v64);
          _objc_msgSend(v61, "setPageTitle:", v65);
          v66 = v65;
          v62 = v112;
        }
        else
        {
          _objc_msgSend(v61, "setPageTitle:", CFSTR("自定义"));
        }
        _objc_msgSend(v61, "setPageType:", 1LL);
        _objc_msgSend(v61, "setPageObject:", v62);
        v68 = v109;
        _objc_msgSend(v109, "insertCustomizablePageWithObject:", v61);
        v69 = _objc_msgSend(v68, "tabContainer");
        v113 = objc_retainAutoreleasedReturnValue(v69);
        v70 = _objc_msgSend(v113, "tabViewArray");
        v71 = objc_retainAutoreleasedReturnValue(v70);
        if ( (__int64)_objc_msgSend(v71, "count") )
        {
          v73 = _objc_msgSend(v68, "ziXuanTabbarController");
          v74 = objc_retainAutoreleasedReturnValue(v73);
          v75 = _objc_msgSend(v74, "viewControllers");
          v76 = objc_retainAutoreleasedReturnValue(v75);
          v110 = (SEL)v61;
          v77 = v76;
          v107 = (SEL)_objc_msgSend(v76, "count");
          v78 = v77;
          v61 = (void *)v110;
          if ( !v107 )
          {
LABEL_37:
            goto LABEL_38;
          }
          v80 = v109;
          v81 = _objc_msgSend(v109, "tabContainer");
          v113 = objc_retainAutoreleasedReturnValue(v81);
          v82 = _objc_msgSend(v113, "tabViewArray");
          v83 = objc_retainAutoreleasedReturnValue(v82);
          _objc_msgSend(v83, "count");
          v84 = _objc_msgSend(v80, "tabContainer");
          v85 = objc_retainAutoreleasedReturnValue(v84);
          _objc_msgSend(v85, "setSelectedIndex:", v86);
          v87 = v80;
          v88 = _objc_msgSend(v80, "ziXuanTabbarController");
          v113 = objc_retainAutoreleasedReturnValue(v88);
          v89 = _objc_msgSend(v113, "viewControllers");
          v90 = objc_retainAutoreleasedReturnValue(v89);
          v91 = (char *)_objc_msgSend(v90, "count") - 1;
          v92 = _objc_msgSend(v87, "ziXuanTabbarController");
          v93 = objc_retainAutoreleasedReturnValue(v92);
          _objc_msgSend(v93, "setSelectedIndex:", v91);
          v94 = v93;
          v61 = (void *)v110;
        }
        goto LABEL_37;
      }
    }
  }
LABEL_39:
}

//----- (000000010091E8E6) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController insertCustomizablePageWithType:](
        ZiXuanGuViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSDictionary *v10; // rax
  NSDictionary *v11; // r13
  NSNumber *v12; // rax
  NSNumber *v13; // rbx
  NSNumber *v15; // rdi
  NSNumber *v18; // rax
  SEL v19; // r12
  SEL v21; // r12
  SEL v23; // r12
  SEL v25; // r12
  SEL v27; // r12
  SEL v29; // r12
  NSDictionary *v31; // rax
  NSDictionary *v32; // r13
  NSNumber *v33; // rax
  NSNumber *v34; // r14
  objc_class *v39; // rax
  SEL v48; // r12
  HXCPManager *v66; // rax
  HXCPManager *v67; // r15
  HXPageTabContainer *v71; // rax
  HXPageTabContainer *v72; // rax
  HXPageTabContainer *v73; // r14
  HXTabbarController *v85; // rax
  HXTabbarController *v86; // r15
  NSArray *v87; // rax
  NSArray *v88; // r13
  HXTabbarController *v91; // rax
  HXTabbarController *v92; // rbx
  _QWORD v95[4]; // [rsp+8h] [rbp-188h] BYREF
  id to; // [rsp+28h] [rbp-168h] BYREF
  id location; // [rsp+38h] [rbp-158h] BYREF
  _QWORD v107[7]; // [rsp+80h] [rbp-110h] BYREF
  _QWORD v108[7]; // [rsp+B8h] [rbp-D8h] BYREF
  _QWORD v109[7]; // [rsp+F0h] [rbp-A0h] BYREF
  _QWORD v110[7]; // [rsp+128h] [rbp-68h] BYREF

  if ( a3 - 2 >= 6 )
  {
    if ( a3 != 1 )
      return;
    v66 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
    v67 = objc_retainAutoreleasedReturnValue(v66);
    -[HXCPManager existPageCountPlusPlus](v67, "existPageCountPlusPlus");
    v104 = v67;
    v68 = -[HXCPManager getNewCustomizablePageTitle](v67, "getNewCustomizablePageTitle");
    v105 = objc_retainAutoreleasedReturnValue(v68);
    v69 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
    v70 = _objc_msgSend(v69, "initWithTitle:isDeletable:", v105, 1LL);
    _objc_msgSend(v70, "setCanDragOut:", 1LL);
    _objc_msgSend(v70, "setAllowMenuType:", 2LL);
    _objc_msgSend(v70, "setDragOutWindowClass:", CFSTR("HXCPDragoutWC"));
    v71 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
    v106 = objc_retainAutoreleasedReturnValue(v71);
    v72 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
    v73 = objc_retainAutoreleasedReturnValue(v72);
    v75 = (void *)v74(v73, "tabViewArray");
    v77 = v76;
    v78 = objc_retainAutoreleasedReturnValue(v75);
    val = self;
    v79 = v77(v78, "count");
    ((void (__fastcall *)(id, const char *, id, __int64))v77)(v106, "addTabWithItem:atIndex:", v70, v79);
    v81 = objc_alloc((Class)&OBJC_CLASS___HXCPViewController);
    v82 = _objc_msgSend(v81, "init");
    _objc_msgSend(v82, "setPageTitle:", v105);
    v106 = v70;
    v83 = _objc_msgSend(v70, "tabID");
    _objc_msgSend(v84, "setupControllerID:", v83);
    v85 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
    v86 = objc_retainAutoreleasedReturnValue(v85);
    v87 = -[HXTabbarController viewControllers](v86, "viewControllers");
    v88 = objc_retainAutoreleasedReturnValue(v87);
    v89 = _objc_msgSend(v88, "mutableCopy");
    _objc_msgSend(v89, "addObject:", v90);
    v91 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
    v92 = objc_retainAutoreleasedReturnValue(v91);
    v102 = v89;
    -[HXTabbarController setViewControllers:](v92, "setViewControllers:", v89);
    objc_initWeak(&location, self);
    v95[0] = _NSConcreteStackBlock;
    v95[1] = 3254779904LL;
    v95[2] = sub_10091F2FC;
    v95[3] = &unk_1012DC1F0;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v93, "setMyTemplateSelectCallBack:", v95);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
    v16 = v104;
  }
  else
  {
    val = self;
    v105 = (id)a3;
    v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
    v104 = objc_retainAutoreleasedReturnValue(v3);
    v109[0] = v104;
    v110[0] = CFSTR("QuoteForMultiStocksTemplateVC");
    v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 3LL);
    v106 = objc_retainAutoreleasedReturnValue(v4);
    v109[1] = v106;
    v110[1] = CFSTR("MarketLandscapeTemplateVC");
    v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 4LL);
    v102 = objc_retainAutoreleasedReturnValue(v5);
    v109[2] = v102;
    v110[2] = CFSTR("HXHotGraphTemplateVC");
    v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
    v99 = objc_retainAutoreleasedReturnValue(v6);
    v109[3] = v99;
    v110[3] = CFSTR("HXCPGuanLianBaoJiaTemplateVC");
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 6LL);
    v100 = objc_retainAutoreleasedReturnValue(v7);
    v109[4] = v100;
    v110[4] = CFSTR("HXCPDuanXianQinLongTemplateVC");
    v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 7LL);
    v101 = objc_retainAutoreleasedReturnValue(v8);
    v109[5] = v101;
    v110[5] = CFSTR("HXCPDaPingDingZhiTemplateVC");
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 8LL);
    v109[6] = objc_retainAutoreleasedReturnValue(v9);
    v110[6] = &charsToLeaveEscaped;
    v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v110, v109, 7LL);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v105);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v11, "thsStringForKey:", v13);
    v15 = v13;
    v16 = objc_retainAutoreleasedReturnValue(v14);
    if ( v16 && _objc_msgSend(v16, "length") )
    {
      v18 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
      v106 = objc_retainAutoreleasedReturnValue(v18);
      v107[0] = v106;
      v104 = v16;
      v108[0] = CFSTR("多股报价");
      v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, v19, 3LL);
      v102 = objc_retainAutoreleasedReturnValue(v20);
      v107[1] = v102;
      v108[1] = CFSTR("全景看盘");
      v22 = _objc_msgSend(&OBJC_CLASS___NSNumber, v21, 4LL);
      v99 = objc_retainAutoreleasedReturnValue(v22);
      v107[2] = v99;
      v108[2] = CFSTR("热力图");
      v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, v23, 5LL);
      v100 = objc_retainAutoreleasedReturnValue(v24);
      v107[3] = v100;
      v108[3] = CFSTR("关联报价");
      v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, v25, 6LL);
      v101 = objc_retainAutoreleasedReturnValue(v26);
      v107[4] = v101;
      v108[4] = CFSTR("短线擒龙");
      v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, v27, 7LL);
      v97 = objc_retainAutoreleasedReturnValue(v28);
      v107[5] = v97;
      v108[5] = CFSTR("大屏定制");
      v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, v29, 8LL);
      v107[6] = objc_retainAutoreleasedReturnValue(v30);
      v108[6] = CFSTR("L2专属");
      v31 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v108, v107, 7LL);
      v32 = objc_retainAutoreleasedReturnValue(v31);
      v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v105);
      v34 = objc_retainAutoreleasedReturnValue(v33);
      v35 = _objc_msgSend(v32, "thsStringForKey:", v34);
      v36 = (char *)objc_retainAutoreleasedReturnValue(v35);
      v38 = obj;
      if ( v36 )
        v38 = v36;
      objc_retain(v38);
      v39 = NSClassFromString((NSString *)v104);
      v40 = objc_alloc(v39);
      v105 = _objc_msgSend(v40, "init");
      v41 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
      v43 = _objc_msgSend(v41, "initWithTitle:isDeletable:", v42, 1LL);
      _objc_msgSend(v43, "setCanDragOut:", 1LL);
      _objc_msgSend(v43, "setAllowMenuType:", 1LL);
      v45 = v43;
      _objc_msgSend(v43, "setDragOutWindowClass:", CFSTR("HXCPDragoutWC"));
      v46 = _objc_msgSend(val, "tabContainer");
      v47 = objc_retainAutoreleasedReturnValue(v46);
      v49 = _objc_msgSend(val, v48);
      v50 = objc_retainAutoreleasedReturnValue(v49);
      v51 = _objc_msgSend(v50, "tabViewArray");
      v52 = objc_retainAutoreleasedReturnValue(v51);
      v53 = _objc_msgSend(v52, "count");
      v106 = v45;
      _objc_msgSend(v47, "addTabWithItem:atIndex:", v45, v53);
      v55 = _objc_msgSend(v45, "tabID");
      _objc_msgSend(v105, "setupControllerID:", v55);
      v56 = _objc_msgSend(val, "ziXuanTabbarController");
      v57 = objc_retainAutoreleasedReturnValue(v56);
      v58 = _objc_msgSend(v57, "viewControllers");
      v59 = objc_retainAutoreleasedReturnValue(v58);
      _objc_msgSend(v59, "mutableCopy");
      _objc_msgSend(v60, "addObject:", v105);
      v61 = _objc_msgSend(val, "ziXuanTabbarController");
      v62 = objc_retainAutoreleasedReturnValue(v61);
      _objc_msgSend(v62, "setViewControllers:", v63);
      v64 = v62;
      v16 = v104;
    }
  }
}

//----- (000000010091F2FC) ----------------------------------------------------
void __fastcall sub_10091F2FC(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "loadMyTemplate:", v2);
}

//----- (000000010091F356) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController insertCustomizablePageWithObject:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  NSNumber *v5; // rax
  SEL v6; // r12
  SEL v8; // r12
  SEL v10; // r12
  SEL v12; // r12
  SEL v14; // r12
  SEL v16; // r12
  NSDictionary *v19; // rax
  NSDictionary *v20; // r14
  SEL v22; // r12
  NSNumber *v28; // rax
  SEL v29; // r12
  SEL v31; // r12
  SEL v33; // r12
  SEL v35; // r12
  SEL v37; // r12
  SEL v39; // r12
  NSDictionary *v41; // rax
  NSDictionary *v42; // r14
  NSNumber *v44; // rax
  NSNumber *v45; // r15
  objc_class *v50; // rax
  SEL v59; // r12
  HXCPManager *v76; // rax
  HXCPManager *v77; // r15
  SEL v85; // r12
  SEL v170; // r12
  unsigned __int64 v175; // r12
  HXCPManager *v215; // rax
  HXCPManager *v216; // r14
  _QWORD v237[4]; // [rsp+8h] [rbp-188h] BYREF
  id to; // [rsp+28h] [rbp-168h] BYREF
  id location; // [rsp+30h] [rbp-160h] BYREF
  _QWORD v249[7]; // [rsp+80h] [rbp-110h] BYREF
  _QWORD v250[7]; // [rsp+B8h] [rbp-D8h] BYREF
  _QWORD v251[7]; // [rsp+F0h] [rbp-A0h] BYREF
  _QWORD v252[7]; // [rsp+128h] [rbp-68h] BYREF

  val = self;
  v3 = objc_retain(a3);
  v4 = (char *)_objc_msgSend(v3, "pageType");
  if ( (unsigned __int64)(v4 - 2) < 6 )
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
    v248 = objc_retainAutoreleasedReturnValue(v5);
    v243 = v3;
    v251[0] = v248;
    v252[0] = CFSTR("QuoteForMultiStocksTemplateVC");
    v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, v6, 3LL);
    v247 = objc_retainAutoreleasedReturnValue(v7);
    v251[1] = v247;
    v252[1] = CFSTR("MarketLandscapeTemplateVC");
    v9 = _objc_msgSend(&OBJC_CLASS___NSNumber, v8, 4LL);
    v246 = objc_retainAutoreleasedReturnValue(v9);
    v251[2] = v246;
    v252[2] = CFSTR("HXHotGraphTemplateVC");
    v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, v10, 5LL);
    v244 = objc_retainAutoreleasedReturnValue(v11);
    v251[3] = v244;
    v252[3] = CFSTR("HXCPGuanLianBaoJiaTemplateVC");
    v13 = _objc_msgSend(&OBJC_CLASS___NSNumber, v12, 6LL);
    v241 = objc_retainAutoreleasedReturnValue(v13);
    v251[4] = v241;
    v252[4] = CFSTR("HXCPDuanXianQinLongTemplateVC");
    v15 = _objc_msgSend(&OBJC_CLASS___NSNumber, v14, 7LL);
    v242 = objc_retainAutoreleasedReturnValue(v15);
    v251[5] = v242;
    v252[5] = CFSTR("HXCPDaPingDingZhiTemplateVC");
    v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, v16, 8LL);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v251[6] = v18;
    v252[6] = &charsToLeaveEscaped;
    v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v252, v251, 7LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = _objc_msgSend(v243, "pageType");
    v23 = _objc_msgSend(&OBJC_CLASS___NSNumber, v22, v21);
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25 = _objc_msgSend(v20, "thsStringForKey:", v24);
    objc_retainAutoreleasedReturnValue(v25);
    v27 = v26;
    if ( v26 && _objc_msgSend(v26, "length") )
    {
      v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 2LL);
      v247 = objc_retainAutoreleasedReturnValue(v28);
      v249[0] = v247;
      v250[0] = CFSTR("多股报价");
      v30 = _objc_msgSend(&OBJC_CLASS___NSNumber, v29, 3LL);
      v246 = objc_retainAutoreleasedReturnValue(v30);
      v249[1] = v246;
      v250[1] = CFSTR("全景看盘");
      v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, v31, 4LL);
      v244 = objc_retainAutoreleasedReturnValue(v32);
      v249[2] = v244;
      v250[2] = CFSTR("热力图");
      v34 = _objc_msgSend(&OBJC_CLASS___NSNumber, v33, 5LL);
      v241 = objc_retainAutoreleasedReturnValue(v34);
      v249[3] = v241;
      v250[3] = CFSTR("关联报价");
      v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, v35, 6LL);
      v242 = objc_retainAutoreleasedReturnValue(v36);
      v249[4] = v242;
      v250[4] = CFSTR("短线擒龙");
      v38 = _objc_msgSend(&OBJC_CLASS___NSNumber, v37, 7LL);
      v240 = objc_retainAutoreleasedReturnValue(v38);
      v249[5] = v240;
      v250[5] = CFSTR("大屏定制");
      v40 = _objc_msgSend(&OBJC_CLASS___NSNumber, v39, 8LL);
      v249[6] = objc_retainAutoreleasedReturnValue(v40);
      v250[6] = CFSTR("L2专属");
      v41 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v250, v249, 7LL);
      v42 = objc_retainAutoreleasedReturnValue(v41);
      v43 = _objc_msgSend(v243, "pageType");
      v44 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v43);
      v248 = v27;
      v45 = objc_retainAutoreleasedReturnValue(v44);
      v46 = _objc_msgSend(v42, "thsStringForKey:", v45);
      v47 = (char *)objc_retainAutoreleasedReturnValue(v46);
      v49 = obj;
      if ( v47 )
        v49 = v47;
      objc_retain(v49);
      v50 = NSClassFromString((NSString *)v248);
      v51 = objc_alloc(v50);
      v247 = _objc_msgSend(v51, "init");
      v52 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
      v54 = _objc_msgSend(v52, "initWithTitle:isDeletable:", v53, 1LL);
      _objc_msgSend(v54, "setCanDragOut:", 1LL);
      _objc_msgSend(v54, "setAllowMenuType:", 1LL);
      v56 = v54;
      _objc_msgSend(v54, "setDragOutWindowClass:", CFSTR("HXCPDragoutWC"));
      v57 = _objc_msgSend(val, "tabContainer");
      v58 = objc_retainAutoreleasedReturnValue(v57);
      v60 = _objc_msgSend(val, v59);
      v61 = objc_retainAutoreleasedReturnValue(v60);
      v62 = _objc_msgSend(v61, "tabViewArray");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v64 = _objc_msgSend(v63, "count");
      v246 = v56;
      _objc_msgSend(v58, "addTabWithItem:atIndex:", v56, v64);
      v66 = _objc_msgSend(v56, "tabID");
      _objc_msgSend(v247, "setupControllerID:", v66);
      v67 = _objc_msgSend(val, "ziXuanTabbarController");
      v68 = objc_retainAutoreleasedReturnValue(v67);
      v69 = _objc_msgSend(v68, "viewControllers");
      v70 = objc_retainAutoreleasedReturnValue(v69);
      _objc_msgSend(v70, "mutableCopy");
      _objc_msgSend(v71, "addObject:", v247);
      v72 = _objc_msgSend(val, "ziXuanTabbarController");
      v73 = objc_retainAutoreleasedReturnValue(v72);
      _objc_msgSend(v73, "setViewControllers:", v74);
      v27 = v248;
    }
    goto LABEL_8;
  }
  if ( v4 )
  {
    if ( v4 != (char *)1 )
      goto LABEL_9;
    v76 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
    v77 = objc_retainAutoreleasedReturnValue(v76);
    -[HXCPManager existPageCountPlusPlus](v77, "existPageCountPlusPlus");
    v78 = _objc_msgSend(v3, "pageTitle");
    v80 = objc_retainAutoreleasedReturnValue(v78);
    v243 = v3;
    v241 = v77;
    if ( v80 )
      v81 = _objc_msgSend(v3, v79);
    else
      v81 = -[HXCPManager getNewCustomizablePageTitle](v77, "getNewCustomizablePageTitle");
    v151 = objc_retainAutoreleasedReturnValue(v81);
    v152 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
    v246 = v151;
    v153 = _objc_msgSend(v152, "initWithTitle:isDeletable:", v151, 1LL);
    _objc_msgSend(v153, "setCanDragOut:", 1LL);
    _objc_msgSend(v154, "setAllowMenuType:", 2LL);
    _objc_msgSend(v155, "setDragOutWindowClass:", CFSTR("HXCPDragoutWC"));
    v156 = _objc_msgSend(val, "tabContainer");
    v157 = objc_retainAutoreleasedReturnValue(v156);
    v158 = _objc_msgSend(val, "tabContainer");
    v159 = objc_retainAutoreleasedReturnValue(v158);
    v160 = _objc_msgSend(v159, "tabViewArray");
    v161 = objc_retainAutoreleasedReturnValue(v160);
    v162 = _objc_msgSend(v161, "count");
    v247 = v163;
    _objc_msgSend(v157, "addTabWithItem:atIndex:", v163, v162);
    v164 = _objc_msgSend(v243, "pageObject");
    v165 = objc_retainAutoreleasedReturnValue(v164);
    LOBYTE(v157) = (__int64)_objc_msgSend(v165, "pageLocked");
    if ( (_BYTE)v157 )
    {
      v166 = _objc_msgSend(val, "tabContainer");
      v167 = objc_retainAutoreleasedReturnValue(v166);
      v168 = _objc_msgSend(v247, "tabID");
      v169 = _objc_msgSend(v167, "getIndexWithId:", v168);
      v171 = _objc_msgSend(val, v170);
      v172 = objc_retainAutoreleasedReturnValue(v171);
      v173 = _objc_msgSend(v172, "tabViewArray");
      v174 = objc_retainAutoreleasedReturnValue(v173);
      _objc_msgSend(v174, "count");
      v248 = v169;
      if ( (unsigned __int64)v169 < v175 )
      {
        v176 = _objc_msgSend(val, "tabContainer");
        v177 = objc_retainAutoreleasedReturnValue(v176);
        v178 = _objc_msgSend(v177, "tabViewArray");
        v179 = objc_retainAutoreleasedReturnValue(v178);
        v180 = _objc_msgSend(v179, "objectAtIndexedSubscript:", v248);
        v181 = objc_retainAutoreleasedReturnValue(v180);
        v182 = _objc_msgSend(&OBJC_CLASS___HXPageTabView, "class");
        if ( (unsigned __int8)_objc_msgSend(v181, "isKindOfClass:", v182) )
          _objc_msgSend(v181, "updateLockState:", 1LL);
      }
    }
    v183 = objc_alloc((Class)&OBJC_CLASS___HXCPViewController);
    v184 = _objc_msgSend(v183, "init");
    _objc_msgSend(v184, "setPageTitle:", v246);
    v186 = _objc_msgSend(v243, v185);
    v187 = objc_retainAutoreleasedReturnValue(v186);
    _objc_msgSend(v184, "setObject:", v187);
    v188 = v187;
    v189 = v184;
    v190 = _objc_msgSend(v184, "object");
    v191 = "pageUUID";
    v244 = objc_retainAutoreleasedReturnValue(v190);
    v192 = _objc_msgSend(v244, "pageUUID");
    v248 = v184;
    if ( objc_retainAutoreleasedReturnValue(v192) )
    {
      v193 = _objc_msgSend(v184, "object");
      v194 = objc_retainAutoreleasedReturnValue(v193);
      v195 = _objc_msgSend(v194, "pageUUID");
      v191 = (char *)objc_retainAutoreleasedReturnValue(v195);
      if ( (__int64)_objc_msgSend(v191, "length") )
      {
        v197 = v244;
        goto LABEL_34;
      }
      v240 = v194;
    }
    v198 = _objc_msgSend(v189, "object");
    v199 = objc_retainAutoreleasedReturnValue(v198);
    LOBYTE(v242) = (__int64)_objc_msgSend(v199, "isPageHandled");
    if ( v200 )
    {
      v202 = v201;
    }
    else
    {
      v202 = 0LL;
    }
    if ( (_BYTE)v242 != 1 )
      goto LABEL_35;
    v203 = _objc_msgSend(&OBJC_CLASS___NSUUID, "UUID");
    v204 = objc_retainAutoreleasedReturnValue(v203);
    v205 = _objc_msgSend(v204, "UUIDString");
    objc_retainAutoreleasedReturnValue(v205);
    v206 = _objc_msgSend(v248, "object");
    v207 = objc_retainAutoreleasedReturnValue(v206);
    _objc_msgSend(v207, "setPageUUID:", v208);
    v210 = _objc_msgSend(v243, "pageTitle");
    v211 = objc_retainAutoreleasedReturnValue(v210);
    v212 = _objc_msgSend(v248, "object");
    v213 = objc_retainAutoreleasedReturnValue(v212);
    _objc_msgSend(v213, "setPageTitle:", v211);
    v215 = (HXCPManager *)+[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
    v216 = objc_retainAutoreleasedReturnValue(v215);
    v217 = _objc_msgSend(v248, "object");
    v218 = objc_retainAutoreleasedReturnValue(v217);
    -[HXCPManager createMyTemplate:](v216, "createMyTemplate:", v218);
    v197 = v216;
LABEL_34:
LABEL_35:
    v220 = _objc_msgSend(v247, "tabID");
    _objc_msgSend(v248, "setupControllerID:", v220);
    v221 = _objc_msgSend(val, "ziXuanTabbarController");
    v222 = objc_retainAutoreleasedReturnValue(v221);
    v223 = _objc_msgSend(v222, "viewControllers");
    v224 = objc_retainAutoreleasedReturnValue(v223);
    v225 = _objc_msgSend(v224, "mutableCopy");
    v226(v224);
    v227(v222);
    _objc_msgSend(v225, "addObject:", v248);
    v228 = _objc_msgSend(val, "ziXuanTabbarController");
    v229 = objc_retainAutoreleasedReturnValue(v228);
    v244 = v225;
    _objc_msgSend(v229, "setViewControllers:", v225);
    v230(v229);
    v231 = v248;
    objc_initWeak(&location, val);
    v237[0] = _NSConcreteStackBlock;
    v237[1] = 3254779904LL;
    v237[2] = sub_1009206D9;
    v237[3] = &unk_1012DC1F0;
    objc_copyWeak(&to, &location);
    _objc_msgSend(v231, "setMyTemplateSelectCallBack:", v237);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
    v232(v244);
    v233(v231);
    v234(v247);
    v235(v246);
    v236(v241);
LABEL_8:
    v3 = v243;
    goto LABEL_9;
  }
  v82 = _objc_msgSend(v3, "pageTitle");
  v83 = objc_retainAutoreleasedReturnValue(v82);
  v84 = (unsigned __int8)_objc_msgSend(v83, "isEqualToString:", CFSTR("自选"));
  v86 = _objc_msgSend(v3, v85);
  v87 = objc_retainAutoreleasedReturnValue(v86);
  v88 = v87;
  if ( v84 )
  {
    v89 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
    v248 = _objc_msgSend(v89, "initWithTitle:isDeletable:", v88, 0LL);
    _objc_msgSend(v248, "setCanDragOut:", 0LL);
    _objc_msgSend(v248, "setAllowMenuType:", 0LL);
    v247 = v88;
    v90 = _objc_msgSend(val, "tabContainer");
    v91 = objc_retainAutoreleasedReturnValue(v90);
    v93 = _objc_msgSend(val, v92);
    v246 = objc_retainAutoreleasedReturnValue(v93);
    v94 = _objc_msgSend(v246, "tabViewArray");
    v95 = objc_retainAutoreleasedReturnValue(v94);
    v96 = _objc_msgSend(v95, "count");
    _objc_msgSend(v91, "addTabWithItem:atIndex:", v248, v96);
    v97 = _objc_msgSend(val, "selfStockContainerController");
    v98 = objc_retainAutoreleasedReturnValue(v97);
    v100 = _objc_msgSend(v99, "tabID");
    _objc_msgSend(v98, "setupControllerID:", v100);
    v101 = _objc_msgSend(val, "ziXuanTabbarController");
    v102 = objc_retainAutoreleasedReturnValue(v101);
    v104 = (void *)v103(v102, "viewControllers");
    v106 = v105;
    v107 = objc_retainAutoreleasedReturnValue(v104);
    v243 = v3;
    v108 = (void *)v106(v107, "mutableCopy");
    v110(v102);
    v111 = _objc_msgSend(val, "selfStockContainerController");
    v112 = objc_retainAutoreleasedReturnValue(v111);
    _objc_msgSend(v108, "addObject:", v112);
    v113(v112);
    v114 = _objc_msgSend(val, "ziXuanTabbarController");
    v115 = objc_retainAutoreleasedReturnValue(v114);
    _objc_msgSend(v115, "setViewControllers:", v108);
    v116(v115);
    v117(v108);
    v118(v248);
    v119(v247);
  }
  else
  {
    v120 = (unsigned __int8)_objc_msgSend(v87, "isEqualToString:", CFSTR("智能分组"));
    if ( v120 )
    {
      v122 = _objc_msgSend(v3, v121);
      v247 = objc_retainAutoreleasedReturnValue(v122);
      v123 = objc_alloc((Class)&OBJC_CLASS___HXBaseTabItem);
      v248 = _objc_msgSend(v123, "initWithTitle:isDeletable:", v247, 0LL);
      v124(v248, "setCanDragOut:", 0LL);
      v125(v248, "setAllowMenuType:", 0LL);
      v127 = (void *)v126(val, "tabContainer");
      v246 = objc_retainAutoreleasedReturnValue(v127);
      v129 = (void *)v128(val, "tabContainer");
      v244 = objc_retainAutoreleasedReturnValue(v129);
      v131 = (void *)v130(v244, "tabViewArray");
      v132 = objc_retainAutoreleasedReturnValue(v131);
      v134 = v133(v132, "count");
      v135(v246, "addTabWithItem:atIndex:", v248, v134);
      v136 = _objc_msgSend(val, "aiGroupContainerController");
      v137 = objc_retainAutoreleasedReturnValue(v136);
      v139 = _objc_msgSend(v138, "tabID");
      _objc_msgSend(v137, "setupControllerID:", v139);
      v140 = _objc_msgSend(val, "ziXuanTabbarController");
      v141 = objc_retainAutoreleasedReturnValue(v140);
      v142 = _objc_msgSend(v141, "viewControllers");
      v143 = objc_retainAutoreleasedReturnValue(v142);
      _objc_msgSend(v143, "mutableCopy");
      v144 = _objc_msgSend(val, "aiGroupContainerController");
      v145 = objc_retainAutoreleasedReturnValue(v144);
      _objc_msgSend(v146, "addObject:", v145);
      v147 = _objc_msgSend(val, "ziXuanTabbarController");
      v148 = objc_retainAutoreleasedReturnValue(v147);
      _objc_msgSend(v148, "setViewControllers:", v149);
    }
  }
LABEL_9:
}

//----- (00000001009206D9) ----------------------------------------------------
void __fastcall sub_1009206D9(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "loadMyTemplate:", v2);
}

//----- (0000000100920733) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController hxTabDidSwitchOld:toNew:](
        ZiXuanGuViewController *self,
        SEL a2,
        unsigned __int64 a3,
        unsigned __int64 a4)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  ZiXuanGuViewController *v9; // rax
  id (*v10)(id, SEL, ...); // r12

  -[ZiXuanGuViewController updateTabAccessaryToolViewState](self, "updateTabAccessaryToolViewState", a3, a4);
  v5 = v4(&OBJC_CLASS___HXCPToolBoxWC, "sharedInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(v6, "delegate");
  v9 = (ZiXuanGuViewController *)objc_retainAutoreleasedReturnValue(v8);
  if ( v9 == self )
  {
    v11 = v10(v6, "window");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13(v12, "isVisible");
    if ( v14 )
      _objc_msgSend(v6, "close");
  }
  else
  {
  }
}

//----- (00000001009207FD) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController selectTabWithId:index:](
        ZiXuanGuViewController *self,
        SEL a2,
        signed __int64 a3,
        unsigned __int64 a4)
{
  HXPageTabContainer *v6; // rax
  HXPageTabContainer *v7; // rax
  HXTabbarController *v9; // rax
  HXTabbarController *v10; // rbx

  v5 = -[ZiXuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", a3);
  if ( v5 != (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v6 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    -[HXPageTabContainer setSelectedIndex:](v7, "setSelectedIndex:", a4);
    v9 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    -[HXTabbarController setSelectedIndex:](v10, "setSelectedIndex:", v5);
  }
}

//----- (00000001009208B9) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController deleteTabWithId:index:](
        ZiXuanGuViewController *self,
        SEL a2,
        signed __int64 a3,
        unsigned __int64 a4)
{
  HXPageTabContainer *v5; // rax
  HXPageTabContainer *v6; // rbx
  HXPageTabContainer *v7; // rax
  HXPageTabContainer *v8; // rax
  HXPageTabContainer *v9; // r14
  NSArray *v11; // rax
  NSArray *v12; // r15
  HXPageTabContainer *v19; // rax
  HXPageTabContainer *v20; // rbx
  HXTabbarController *v43; // rax
  HXTabbarController *v44; // r15
  NSArray *v45; // rax
  NSArray *v46; // rbx
  unsigned __int64 v49; // r13
  HXPageTabContainer *v50; // rax
  HXPageTabContainer *v51; // rax
  NSArray *v52; // rax
  NSArray *v53; // rbx
  HXPageTabContainer *v56; // rax
  NSArray *v57; // rax
  NSArray *v58; // r14
  HXPageTabContainer *v69; // rax
  HXPageTabContainer *v70; // rbx
  HXTabbarController *v72; // rax
  HXTabbarController *v73; // rbx
  HXTabbarController *v75; // rax
  HXTabbarController *v76; // r15
  HXTabbarController *v82; // rax
  HXTabbarController *v83; // rbx
  HXPageTabContainer *v85; // [rsp+8h] [rbp-48h]
  unsigned __int64 v89; // [rsp+20h] [rbp-30h]

  v5 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[HXPageTabContainer selectedIndex](v6, "selectedIndex");
  v7 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = v8;
  v89 = a4;
  if ( v10 == a4 )
  {
    -[HXBaseTabContainer deleteTabAtIndex:](v8, "deleteTabAtIndex:", a4);
    if ( -[ZiXuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", a3) != (id)0x7FFFFFFFFFFFFFFFLL )
    {
      v43 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
      v44 = objc_retainAutoreleasedReturnValue(v43);
      v45 = -[HXTabbarController viewControllers](v44, "viewControllers");
      v46 = objc_retainAutoreleasedReturnValue(v45);
      v47 = _objc_msgSend(v46, "mutableCopy");
      v88 = v47;
      _objc_msgSend(v47, "removeObjectAtIndex:", v48);
      v49 = 0LL;
      if ( v89 )
        v49 = v89 - 1;
      v50 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
      v51 = objc_retainAutoreleasedReturnValue(v50);
      v52 = -[HXBaseTabContainer tabViewArray](v51, "tabViewArray");
      v53 = objc_retainAutoreleasedReturnValue(v52);
      v54 = _objc_msgSend(v53, "count");
      if ( v54 )
      {
        v56 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
        v85 = objc_retainAutoreleasedReturnValue(v56);
        v57 = -[HXBaseTabContainer tabViewArray](v85, "tabViewArray");
        v58 = objc_retainAutoreleasedReturnValue(v57);
        v59 = _objc_msgSend(v58, "objectAtIndexedSubscript:", v49);
        v60 = objc_retainAutoreleasedReturnValue(v59);
        v61 = _objc_msgSend(v60, "tabItem");
        v62 = objc_retainAutoreleasedReturnValue(v61);
        v63 = _objc_msgSend(v62, "tabID");
        v65(v60);
        v66(v58);
        v67(v85);
        v68 = -[ZiXuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", v63);
        v69 = -[ZiXuanGuViewController tabContainer](self, "tabContainer");
        v70 = objc_retainAutoreleasedReturnValue(v69);
        -[HXPageTabContainer setSelectedIndex:](v70, "setSelectedIndex:", v49);
        v71(v70);
        v72 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
        v73 = objc_retainAutoreleasedReturnValue(v72);
        -[HXTabbarController setSelectedIndex:](v73, "setSelectedIndex:", v68);
        v74(v73);
      }
      v75 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
      v76 = objc_retainAutoreleasedReturnValue(v75);
      v77 = -[HXTabbarController selectedViewController](v76, "selectedViewController");
      v78 = objc_retainAutoreleasedReturnValue(v77);
      v91 = _objc_msgSend(v88, "indexOfObject:", v78);
      v80 = _objc_msgSend(v79, "ziXuanTabbarController");
      v81 = objc_retainAutoreleasedReturnValue(v80);
      _objc_msgSend(v81, "setViewControllers:", v88);
      if ( v91 != (id)0x7FFFFFFFFFFFFFFFLL )
      {
        v82 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
        v83 = objc_retainAutoreleasedReturnValue(v82);
        -[HXTabbarController setSelectedIndex:](v83, "setSelectedIndex:", v91);
      }
    }
  }
  else
  {
    v11 = -[HXBaseTabContainer tabViewArray](v8, "tabViewArray");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = _objc_msgSend(v12, "objectAtIndexedSubscript:", v13);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16 = _objc_msgSend(v15, "tabItem");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v84 = _objc_msgSend(v17, "tabID");
    v19 = (HXPageTabContainer *)-[ZiXuanGuViewController tabContainer](self, "tabContainer");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    -[HXBaseTabContainer deleteTabAtIndex:](v20, "deleteTabAtIndex:", v89);
    v87 = _objc_msgSend(v21, "getVcIndexWithTabId:", a3);
    v23 = _objc_msgSend(v22, "ziXuanTabbarController");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25 = _objc_msgSend(v24, "viewControllers");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v27 = _objc_msgSend(v26, "mutableCopy");
    v90 = v27;
    _objc_msgSend(v27, "removeObjectAtIndex:", v87);
    v29 = _objc_msgSend(v28, "ziXuanTabbarController");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    _objc_msgSend(v30, "setViewControllers:", v27);
    v32 = _objc_msgSend(v31, "tabContainer");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = _objc_msgSend(v33, "getIndexWithId:", v84);
    v36 = _objc_msgSend(v35, "getVcIndexWithTabId:", v84);
    v38 = _objc_msgSend(v37, "tabContainer");
    v39 = objc_retainAutoreleasedReturnValue(v38);
    _objc_msgSend(v39, "setSelectedIndex:", v34);
    v41 = _objc_msgSend(v40, "ziXuanTabbarController");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    _objc_msgSend(v42, "setSelectedIndex:", v36);
  }
}

//----- (0000000100920EBB) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController addBtnClicked:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  HXCPManager *v3; // rax
  HXCPManager *v4; // rbx
  HXCPBeyondPageCountTipWC *v6; // rax
  HXCPBeyondPageCountTipWC *v7; // r15
  HXCPBeyondPageCountTipWC *v14; // rbx
  HXTipManager *v16; // rax
  HXTipManager *v17; // rbx
  HXTipManager *v18; // rax
  HXTipManager *v19; // rbx
  _QWORD v36[4]; // [rsp+8h] [rbp-78h] BYREF
  id to; // [rsp+30h] [rbp-50h] BYREF
  id location[6]; // [rsp+50h] [rbp-30h] BYREF

  v39 = objc_retain(a3);
  v3 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[HXCPManager existCustomizablePageCount](v4, "existCustomizablePageCount");
  if ( v5 < 8 )
  {
    v16 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    -[HXTipManager hideTipWithId:](v17, "hideTipWithId:", off_1016BAEB0);
    v18 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    -[HXTipManager setTipShowedWithId:](v19, "setTipShowedWithId:", off_1016BAEB0);
    _objc_msgSend(v20, "insertCustomizablePageWithType:", 1LL);
    v22 = _objc_msgSend(v21, "tabContainer");
    v40 = objc_retainAutoreleasedReturnValue(v22);
    v23 = _objc_msgSend(v40, "tabViewArray");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    _objc_msgSend(v24, "count");
    v41 = v25;
    v26 = _objc_msgSend(v25, "tabContainer");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    _objc_msgSend(v27, "setSelectedIndex:", v28);
    v29 = _objc_msgSend(v41, "ziXuanTabbarController");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31 = _objc_msgSend(v30, "viewControllers");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    _objc_msgSend(v32, "count");
    v33 = _objc_msgSend(v41, "ziXuanTabbarController");
    v34 = objc_retainAutoreleasedReturnValue(v33);
    _objc_msgSend(v34, "setSelectedIndex:", v35);
  }
  else
  {
    v6 = +[HXCPBeyondPageCountTipWC sharedInstance](&OBJC_CLASS___HXCPBeyondPageCountTipWC, "sharedInstance");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    objc_initWeak(location, v8);
    v10 = _objc_msgSend(v9, "view");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v11, "window");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v36[0] = _NSConcreteStackBlock;
    v36[1] = 3254779904LL;
    v36[2] = sub_1009211D3;
    v36[3] = &unk_1012DAFF8;
    objc_copyWeak(&to, location);
    v14 = objc_retain(v7);
    v37 = v14;
    -[HXCPBeyondPageCountTipWC beginSheetBaseedOnWindow:withBlock:](
      v14,
      "beginSheetBaseedOnWindow:withBlock:",
      v13,
      v36);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (00000001009211D3) ----------------------------------------------------
__int64 __fastcall sub_1009211D3(__int64 a1)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id WeakRetained; // [rsp+0h] [rbp-30h]

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v1 = _objc_msgSend(WeakRetained, "view");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v4 = v3(v2, "window");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(*(id *)(a1 + 32), "window");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v5, "endSheet:", v8);
  v10(v5);
  v11(v2);
  return v12(WeakRetained);
}

//----- (0000000100921280) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController getViewControllerForTabID:](
        ZiXuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  unsigned __int64 v10; // r12
  HXTabbarController *v11; // rax
  HXTabbarController *v12; // r15
  NSArray *v13; // rax
  NSArray *v14; // rax

  v19 = -[ZiXuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", a3);
  v4 = v3(self, "ziXuanTabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(v5, "viewControllers");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "count");
  if ( (unsigned __int64)v19 >= v10 )
    return objc_autoreleaseReturnValue(0LL);
  v11 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = -[HXTabbarController viewControllers](v12, "viewControllers");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(v14, "objectAtIndexedSubscript:", v19);
  v16 = objc_retainAutoreleasedReturnValue(v15);
  return objc_autoreleaseReturnValue(v16);
}

//----- (0000000100921381) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController copyFromTabItem:withTabID:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{
  HXCPManager *v4; // rax
  HXCPManager *v5; // rbx
  HXCPBeyondPageCountTipWC *v8; // rax
  HXCPBeyondPageCountTipWC *v9; // rbx
  HXCPBeyondPageCountTipWC *v14; // rbx
  _QWORD v47[4]; // [rsp+0h] [rbp-90h] BYREF
  id to; // [rsp+28h] [rbp-68h] BYREF
  id location; // [rsp+50h] [rbp-40h] BYREF

  v52 = objc_retain(a3);
  v4 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXCPManager existCustomizablePageCount](v5, "existCustomizablePageCount");
  if ( v6 < 8 )
  {
    v16 = -[ZiXuanGuViewController getViewControllerForTabID:](self, "getViewControllerForTabID:", v7);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    if ( v17 )
    {
      v18 = _objc_msgSend(&OBJC_CLASS___HXCPViewController, "class");
      if ( (unsigned __int8)_objc_msgSend(v17, "isKindOfClass:", v18) )
      {
        v51 = self;
        v50 = v17;
        v19 = _objc_msgSend(v17, "buildCurrentPageObject");
        v53 = objc_retainAutoreleasedReturnValue(v19);
        v20 = objc_alloc((Class)&OBJC_CLASS___HXCPTabItemObject);
        v21 = _objc_msgSend(v20, "init");
        v22 = _objc_msgSend(v52, "title");
        v23 = objc_retainAutoreleasedReturnValue(v22);
        v24 = _objc_msgSend(v23, "stringByAppendingString:", CFSTR("-副本"));
        v25 = objc_retainAutoreleasedReturnValue(v24);
        _objc_msgSend(v21, "setPageTitle:", v25);
        _objc_msgSend(v21, "setPageType:", 1LL);
        _objc_msgSend(v21, "setPageObject:", v53);
        v27 = _objc_msgSend(v21, "pageObject");
        v28 = objc_retainAutoreleasedReturnValue(v27);
        _objc_msgSend(v28, "setPageUUID:", 0LL);
        if ( v21 )
        {
          v29(v51, "insertCustomizablePageWithObject:", v21);
          v31 = (void *)v30(v51, "tabContainer");
          v55 = objc_retainAutoreleasedReturnValue(v31);
          v33 = (void *)v32(v55, "tabViewArray");
          v34 = objc_retainAutoreleasedReturnValue(v33);
          v56 = v35(v34, "count") - 1;
          v37 = (void *)v36(v51, "tabContainer");
          v38 = objc_retainAutoreleasedReturnValue(v37);
          v39(v38, "setSelectedIndex:", v56);
          v41 = _objc_msgSend(v51, "ziXuanTabbarController");
          v55 = objc_retainAutoreleasedReturnValue(v41);
          v42 = _objc_msgSend(v55, "viewControllers");
          v43 = objc_retainAutoreleasedReturnValue(v42);
          v56 = (__int64)_objc_msgSend(v43, "count") - 1;
          v44 = _objc_msgSend(v51, "ziXuanTabbarController");
          v45 = objc_retainAutoreleasedReturnValue(v44);
          _objc_msgSend(v45, "setSelectedIndex:", v56);
        }
        v17 = v50;
      }
    }
  }
  else
  {
    v8 = +[HXCPBeyondPageCountTipWC sharedInstance](&OBJC_CLASS___HXCPBeyondPageCountTipWC, "sharedInstance");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    objc_initWeak(&location, self);
    v10 = _objc_msgSend(self, "view");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v11, "window");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v47[0] = _NSConcreteStackBlock;
    v47[1] = 3254779904LL;
    v47[2] = sub_1009217A3;
    v47[3] = &unk_1012DAFF8;
    objc_copyWeak(&to, &location);
    v14 = objc_retain(v9);
    v48 = v14;
    -[HXCPBeyondPageCountTipWC beginSheetBaseedOnWindow:withBlock:](
      v14,
      "beginSheetBaseedOnWindow:withBlock:",
      v13,
      v47);
    objc_destroyWeak(&to);
    objc_destroyWeak(&location);
  }
}

//----- (00000001009217A3) ----------------------------------------------------
__int64 __fastcall sub_1009217A3(__int64 a1)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id WeakRetained; // [rsp+0h] [rbp-30h]

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v1 = _objc_msgSend(WeakRetained, "view");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v4 = v3(v2, "window");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(*(id *)(a1 + 32), "window");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v5, "endSheet:", v8);
  v10(v5);
  v11(v2);
  return v12(WeakRetained);
}

//----- (0000000100921850) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController disableCloseAlert](ZiXuanGuViewController *self, SEL a2)
{
  return 1;
}

//----- (000000010092185B) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController renameFinishedTabItem:withTabID:newName:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4,
        id a5)
{
  HXTabbarController *v7; // rax
  HXTabbarController *v8; // rax
  NSArray *v9; // rax
  NSArray *v10; // rbx
  HXCPManager *v18; // rax
  HXCPManager *v19; // r15

  v25 = objc_retain(a5);
  v6 = -[ZiXuanGuViewController getVcIndexWithTabId:](self, "getVcIndexWithTabId:", a4);
  v7 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = -[HXTabbarController viewControllers](v8, "viewControllers");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "objectAtIndexedSubscript:", v6);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = _objc_msgSend(&OBJC_CLASS___HXCPViewController, "class");
  if ( (unsigned __int8)_objc_msgSend(v12, "isKindOfClass:", v14) )
  {
    _objc_msgSend(v12, "setPageTitle:", v25);
    v15 = _objc_msgSend(v12, "object");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    _objc_msgSend(v16, "setPageTitle:", v17);
    v18 = +[HXCPManager sharedInstance](&OBJC_CLASS___HXCPManager, "sharedInstance");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v20 = _objc_msgSend(v12, "object");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = _objc_msgSend(v21, "pageUUID");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    -[HXCPManager renameMyTemplateByUUID:pageTitle:](v19, "renameMyTemplateByUUID:pageTitle:", v23, v25);
  }
}

//----- (00000001009219FD) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController toolBoxOpenStateChanged:](ZiXuanGuViewController *self, SEL a2, char a3)
{
  id (*v3)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  __CFString *v9; // rdx

  -[ZiXuanGuViewController setToolBoxOpened:](self, "setToolBoxOpened:", a3);
  v4 = (unsigned __int8)v3(self, "toolBoxOpened");
  v6 = v5(self, "customizablePageToolBoxBtn");
  v8 = objc_retainAutoreleasedReturnValue(v6);
  v9 = CFSTR("toolbox1");
  if ( !v4 )
    v9 = CFSTR("toolbox0");
  v10 = v7(&OBJC_CLASS___NSImage, "imageNamed:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12(v8, "setImage:", v11);
}

//----- (0000000100921AA2) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController componentSelect:params:](ZiXuanGuViewController *self, SEL a2, id a3, id a4)
{

  v16 = objc_retain(a3);
  v5 = objc_retain(a4);
  v7 = _objc_msgSend(v6, "tabContainer");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "getSelectedTabID");
  v11 = _objc_msgSend(v10, "getViewControllerForTabID:", v9);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  if ( (unsigned __int8)_objc_msgSend(v12, "respondsToSelector:", "insertComponent:params:") )
    _objc_msgSend(v12, "performSelector:withObject:withObject:", v13, v16, v5);
  v14(v5);
  v15(v16);
}

//----- (0000000100921B84) ----------------------------------------------------
double __cdecl -[ZiXuanGuViewController splitView:constrainMinCoordinate:ofSubviewAt:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        double a4,
        signed __int64 a5)
{

  v6 = (const char *)objc_retain(a3);
  v8 = (char *)v6;
  if ( a5 == 1 )
  {
    if ( (unsigned __int8)_objc_msgSend(v7, "isExpand", a4) == 1 )
    {
      v29 = 0.0;
      if ( (unsigned __int8)_objc_msgSend(v9, "isRightViewHide") != 1 )
      {
        v11 = _objc_msgSend(v10, "rightAccessaryView");
        v12 = (const char *)objc_retainAutoreleasedReturnValue(v11);
        v13 = (char *)v12;
        if ( v12 )
        {
          objc_msgSend_stret(&v24, v12, "frame");
          v14 = *(double *)&v25;
        }
        else
        {
          v25 = 0LL;
          v24 = 0LL;
          v14 = 0.0;
        }
        v29 = v14;
LABEL_20:
      }
    }
    else
    {
      v29 = 312.0;
    }
  }
  else
  {
    if ( v6 )
    {
      objc_msgSend_stret(&v26, v6, "frame", a4);
      v15 = *(double *)&v27;
    }
    else
    {
      v27 = 0LL;
      v26 = 0LL;
      v15 = 0.0;
    }
    v29 = v15;
    if ( (unsigned __int8)_objc_msgSend(v7, "isExpand") == 1 )
    {
      v18 = 0.0;
      if ( (unsigned __int8)_objc_msgSend(v16, "isRightViewHide") != 1 )
      {
        v19 = _objc_msgSend(v17, "rightAccessaryView", 0.0);
        v20 = (const char *)objc_retainAutoreleasedReturnValue(v19);
        v13 = (char *)v20;
        if ( v20 )
        {
          objc_msgSend_stret(v28, v20, "frame");
          v22 = *(double *)(v21 + 16);
        }
        else
        {
          memset(v28, 0, sizeof(v28));
          v22 = 0.0;
        }
        v29 = v29 - v22;
        goto LABEL_20;
      }
    }
    else
    {
      v18 = 600.0;
    }
    v29 = v29 - v18;
  }
  return v29;
}

//----- (0000000100921D5D) ----------------------------------------------------
double __cdecl -[ZiXuanGuViewController splitView:constrainMaxCoordinate:ofSubviewAt:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        double a4,
        signed __int64 a5)
{

  v6 = (const char *)objc_retain(a3);
  v8 = (char *)v6;
  if ( a5 == 1 )
  {
    if ( (unsigned __int8)_objc_msgSend(v7, "isExpand", a4) == 1 )
    {
      v29 = 0.0;
      if ( (unsigned __int8)_objc_msgSend(v9, "isRightViewHide") != 1 )
      {
        v11 = _objc_msgSend(v10, "rightAccessaryView");
        v12 = (const char *)objc_retainAutoreleasedReturnValue(v11);
        v13 = (char *)v12;
        if ( v12 )
        {
          objc_msgSend_stret(&v24, v12, "frame");
          v14 = *(double *)&v25;
        }
        else
        {
          v25 = 0LL;
          v24 = 0LL;
          v14 = 0.0;
        }
        v29 = v14;
LABEL_20:
      }
    }
    else
    {
      v29 = 600.0;
    }
  }
  else
  {
    if ( v6 )
    {
      objc_msgSend_stret(&v26, v6, "frame", a4);
      v15 = *(double *)&v27;
    }
    else
    {
      v27 = 0LL;
      v26 = 0LL;
      v15 = 0.0;
    }
    v29 = v15;
    if ( (unsigned __int8)_objc_msgSend(v7, "isExpand") == 1 )
    {
      v18 = 0.0;
      if ( (unsigned __int8)_objc_msgSend(v16, "isRightViewHide") != 1 )
      {
        v19 = _objc_msgSend(v17, "rightAccessaryView", 0.0);
        v20 = (const char *)objc_retainAutoreleasedReturnValue(v19);
        v13 = (char *)v20;
        if ( v20 )
        {
          objc_msgSend_stret(v28, v20, "frame");
          v22 = *(double *)(v21 + 16);
        }
        else
        {
          memset(v28, 0, sizeof(v28));
          v22 = 0.0;
        }
        v29 = v29 - v22;
        goto LABEL_20;
      }
    }
    else
    {
      v18 = 312.0;
    }
    v29 = v29 - v18;
  }
  return v29;
}

//----- (0000000100921F36) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController splitView:shouldAdjustSizeOfSubview:](
        ZiXuanGuViewController *self,
        SEL a2,
        id a3,
        id a4)
{

  objc_retain(a4);
  v5 = _objc_msgSend(a3, "subviews");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = _objc_msgSend(v6, "indexOfObject:", v7);
  return v8 != (id)1;
}

//----- (0000000100921FB3) ----------------------------------------------------
id __cdecl -[ZiXuanGuViewController tipsToShow](ZiXuanGuViewController *self, SEL a2)
{
  NSArray *v5; // rax
  _QWORD v8[4]; // [rsp+0h] [rbp-90h] BYREF
  _QWORD v10[4]; // [rsp+28h] [rbp-68h] BYREF
  id to; // [rsp+48h] [rbp-48h] BYREF
  id location; // [rsp+50h] [rbp-40h] BYREF

  v2 = objc_alloc((Class)&OBJC_CLASS___HXTipModel);
  v3 = _objc_msgSend(v2, "initWithIdentifier:", off_1016BAEB0);
  objc_initWeak(&location, self);
  v10[0] = _NSConcreteStackBlock;
  v10[1] = 3254779904LL;
  v10[2] = sub_10092213A;
  v10[3] = &unk_1012DAA10;
  objc_copyWeak(&to, &location);
  _objc_msgSend(v3, "setShowTipBlock:", v10);
  v8[0] = v4;
  v8[1] = 3254779904LL;
  v8[2] = sub_1009221A0;
  v8[3] = &unk_1012DAA10;
  objc_copyWeak(&v9, &location);
  _objc_msgSend(v3, "setHideTipBlock:", v8);
  v13 = v3;
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v13, 1LL);
  objc_retainAutoreleasedReturnValue(v5);
  objc_destroyWeak(&v9);
  objc_destroyWeak(&to);
  objc_destroyWeak(&location);
  return objc_autoreleaseReturnValue(v6);
}

//----- (000000010092213A) ----------------------------------------------------
void __fastcall sub_10092213A(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = _objc_msgSend(WeakRetained, "tabContainer");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHasNewFunction:", 1LL);
}

//----- (00000001009221A0) ----------------------------------------------------
void __fastcall sub_1009221A0(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = _objc_msgSend(WeakRetained, "tabContainer");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHasNewFunction:", 0LL);
}

//----- (0000000100922203) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setRightAccessaryState:](ZiXuanGuViewController *self, SEL a2, int a3)
{
  id WeakRetained; // rbx
  HXBaseView *v4; // rax
  HXBaseView *v5; // rbx
  HXBaseView *v6; // rdi
  HXBaseView *v8; // rax
  HXBaseView *v9; // r14
  HXBaseView *v17; // rbx
  HXSplitView *v19; // rax
  HXSplitView *v20; // r14
  HXBaseView *v21; // rax
  HXBaseView *v22; // r15

  if ( HIDWORD(self->super.super._topLevelObjects) != a3 )
  {
    HIDWORD(self->super.super._topLevelObjects) = a3;
    if ( (a3 & 2) == 0 )
    {
      WeakRetained = objc_loadWeakRetained((id *)&self->super.super._reserved);
      _objc_msgSend(WeakRetained, "setHidden:", 1LL);
      v4 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
      v5 = objc_retainAutoreleasedReturnValue(v4);
      _objc_msgSend(v5, "removeFromSuperview");
      v6 = v5;
LABEL_6:
      ((void (__fastcall *)(HXBaseView *))v7)(v6);
      return;
    }
    if ( (a3 & 1) == 0 )
    {
      v8 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      _objc_msgSend(v9, "removeFromSuperview");
      v10 = objc_loadWeakRetained((id *)&self->super.super._reserved);
      _objc_msgSend(v10, "setHidden:", 0LL);
      v11(v10);
      v12 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("switchleft"));
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = objc_loadWeakRetained((id *)&self->super.super._reserved);
      _objc_msgSend(v14, "setImage:", v13);
      v15(v14);
      v16(v13);
      v17 = (HXBaseView *)objc_loadWeakRetained((id *)&self->super.super._reserved);
      _objc_msgSend(v17, "setToolTip:", CFSTR("点击显示视图"));
      v6 = v17;
      v7 = v18;
      goto LABEL_6;
    }
    v19 = -[ZiXuanGuViewController baseSplitView](self, "baseSplitView");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v21 = -[ZiXuanGuViewController rightAccessaryView](self, "rightAccessaryView");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    _objc_msgSend(v20, "addSubview:", v22);
    v23 = objc_loadWeakRetained((id *)&self->super.super._reserved);
    _objc_msgSend(v23, "setHidden:", 0LL);
    v24 = _objc_msgSend(&OBJC_CLASS___NSImage, "imageNamed:", CFSTR("switchright"));
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = objc_loadWeakRetained((id *)&self->super.super._reserved);
    _objc_msgSend(v26, "setImage:", v25);
    v28 = objc_loadWeakRetained((id *)&self->super.super._reserved);
    _objc_msgSend(v28, "setToolTip:", CFSTR("点击隐藏视图"));
    _objc_msgSend(self->super._paramsMDic, "reloadPanKou:", 1LL);
  }
}

//----- (00000001009224AF) ----------------------------------------------------
signed __int64 __cdecl -[ZiXuanGuViewController getVcIndexWithTabId:](
        ZiXuanGuViewController *self,
        SEL a2,
        signed __int64 a3)
{
  signed __int64 v3; // r12
  HXTabbarController *v4; // rax
  HXTabbarController *v5; // rbx
  NSArray *v6; // rax
  NSArray *v7; // r15
  id i; // r12
  id (*v18)(id, SEL, ...); // r12
  SEL v27; // [rsp+40h] [rbp-E0h]
  SEL v30; // [rsp+58h] [rbp-C8h]
  id obj; // [rsp+68h] [rbp-B8h]

  v3 = 0x7FFFFFFFFFFFFFFFLL;
  v29 = (id)a3;
  if ( a3 > 0 )
  {
    v26 = 0LL;
    v25 = 0LL;
    v24 = 0LL;
    v23 = 0LL;
    v31 = self;
    v4 = -[ZiXuanGuViewController ziXuanTabbarController](self, "ziXuanTabbarController");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[HXTabbarController viewControllers](v5, "viewControllers");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    obj = v7;
    v8 = _objc_msgSend(v7, "countByEnumeratingWithState:objects:count:", &v23, v33, 16LL);
    if ( v8 )
    {
      v9 = v8;
      v28 = *(_QWORD *)v24;
      while ( 2 )
      {
        v27 = "class";
        v30 = "controllerID";
        for ( i = 0LL; i != v9; i = (id)(v13 + 1) )
        {
          if ( *(_QWORD *)v24 != v28 )
            objc_enumerationMutation(obj);
          v11 = *(void **)(*((_QWORD *)&v23 + 1) + 8LL * (_QWORD)i);
          v12 = _objc_msgSend(&OBJC_CLASS___HXBaseViewController, v27);
          if ( (unsigned __int8)_objc_msgSend(v11, "isKindOfClass:", v12) )
          {
            v14 = objc_retain(v11);
            v15 = _objc_msgSend(v14, v30);
            if ( v15 == v29 )
            {
              v16 = _objc_msgSend(v31, "ziXuanTabbarController");
              v17 = objc_retainAutoreleasedReturnValue(v16);
              v19 = v18(v17, "viewControllers");
              v20 = objc_retainAutoreleasedReturnValue(v19);
              v21(v20, "indexOfObject:", v14);
              goto LABEL_16;
            }
          }
        }
        v9 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v23, v33, 16LL);
        if ( v9 )
          continue;
        break;
      }
    }
LABEL_16:
  }
  return v3;
}

//----- (0000000100922732) ----------------------------------------------------
NSView *__cdecl -[ZiXuanGuViewController leftMainView](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._editors);
  return (NSView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010092274B) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setLeftMainView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._editors, a3);
}

//----- (000000010092275F) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXuanGuViewController topView](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._autounbinder);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100922778) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setTopView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super._autounbinder, a3);
}

//----- (000000010092278C) ----------------------------------------------------
HXPageTabContainer *__cdecl -[ZiXuanGuViewController tabContainer](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._designNibBundleIdentifier);
  return (HXPageTabContainer *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001009227A5) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setTabContainer:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._designNibBundleIdentifier, a3);
}

//----- (00000001009227B9) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXuanGuViewController tabAccessaryToolView](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super.__privateData);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001009227D2) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setTabAccessaryToolView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super.__privateData, a3);
}

//----- (00000001009227E6) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXuanGuViewController rightAccessaryView](ZiXuanGuViewController *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 96LL, 1);
}

//----- (00000001009227FC) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setRightAccessaryView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 96LL);
}

//----- (000000010092280D) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXuanGuViewController contentView](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._isContentViewController);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100922826) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setContentView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._isContentViewController, a3);
}

//----- (000000010092283A) ----------------------------------------------------
NSButton *__cdecl -[ZiXuanGuViewController showAndHideRightBtn](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._reserved);
  return (NSButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100922853) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setShowAndHideRightBtn:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._reserved, a3);
}

//----- (0000000100922867) ----------------------------------------------------
NSButton *__cdecl -[ZiXuanGuViewController customizablePageToolBoxBtn](ZiXuanGuViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._shouldRefresh);
  return (NSButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100922880) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setCustomizablePageToolBoxBtn:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._shouldRefresh, a3);
}

//----- (0000000100922894) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setZiXuanTabbarController:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._stockCode, a3);
}

//----- (00000001009228A8) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setSelfStockContainerController:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._market, a3);
}

//----- (00000001009228BC) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setAiGroupContainerController:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._contentsObjMArr, a3);
}

//----- (00000001009228D0) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController isRightViewHide](ZiXuanGuViewController *self, SEL a2)
{
  return (char)self->super.super._topLevelObjects;
}

//----- (00000001009228E1) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setIsRightViewHide:](ZiXuanGuViewController *self, SEL a2, char a3)
{
  LOBYTE(self->super.super._topLevelObjects) = a3;
}

//----- (00000001009228F1) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setRightAccessaryVC:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._controllerID, a3);
}

//----- (0000000100922905) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setTableRightPanKouVC:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._paramsMDic, a3);
}

//----- (0000000100922919) ----------------------------------------------------
HXSplitView *__cdecl -[ZiXuanGuViewController baseSplitView](ZiXuanGuViewController *self, SEL a2)
{
  return *(HXSplitView **)&self->_isRightViewHide;
}

//----- (000000010092292A) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setBaseSplitView:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_isRightViewHide, a3);
}

//----- (000000010092293E) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController isExpand](ZiXuanGuViewController *self, SEL a2)
{
  return BYTE1(self->super.super._topLevelObjects);
}

//----- (000000010092294F) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setIsExpand:](ZiXuanGuViewController *self, SEL a2, char a3)
{
  BYTE1(self->super.super._topLevelObjects) = a3;
}

//----- (000000010092295F) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setRefreshRightViewBlock:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 176LL);
}

//----- (0000000100922970) ----------------------------------------------------
NSMutableArray *__cdecl -[ZiXuanGuViewController tipArray](ZiXuanGuViewController *self, SEL a2)
{
  return (NSMutableArray *)self->_topView;
}

//----- (0000000100922981) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setTipArray:](ZiXuanGuViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_topView, a3);
}

//----- (0000000100922995) ----------------------------------------------------
char __cdecl -[ZiXuanGuViewController toolBoxOpened](ZiXuanGuViewController *self, SEL a2)
{
  return BYTE2(self->super.super._topLevelObjects);
}

//----- (00000001009229A6) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController setToolBoxOpened:](ZiXuanGuViewController *self, SEL a2, char a3)
{
  BYTE2(self->super.super._topLevelObjects) = a3;
}

//----- (00000001009229B6) ----------------------------------------------------
int __cdecl -[ZiXuanGuViewController rightAccessaryState](ZiXuanGuViewController *self, SEL a2)
{
  return HIDWORD(self->super.super._topLevelObjects);
}

//----- (00000001009229C6) ----------------------------------------------------
void __cdecl -[ZiXuanGuViewController .cxx_destruct](ZiXuanGuViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_topView, 0LL);
  objc_storeStrong((id *)&self->_leftMainView, 0LL);
  objc_storeStrong((id *)&self->_isRightViewHide, 0LL);
  objc_storeStrong((id *)&self->super._paramsMDic, 0LL);
  objc_storeStrong((id *)&self->super._controllerID, 0LL);
  objc_storeStrong((id *)&self->super._contentsObjMArr, 0LL);
  objc_storeStrong((id *)&self->super._market, 0LL);
  objc_storeStrong((id *)&self->super._stockCode, 0LL);
  objc_destroyWeak((id *)&self->super._shouldRefresh);
  objc_destroyWeak((id *)&self->super.super._reserved);
  objc_destroyWeak((id *)&self->super.super._isContentViewController);
  objc_storeStrong((id *)&self->super.super._viewIsAppearing, 0LL);
  objc_destroyWeak(&self->super.super.__privateData);
  objc_destroyWeak((id *)&self->super.super._designNibBundleIdentifier);
  objc_destroyWeak(&self->super.super._autounbinder);
  objc_destroyWeak((id *)&self->super.super._editors);
}

