configmanager *__cdecl -[configmanager init](configmanager *self, SEL a2)
{
  configmanager *v2; // rax
  configmanager *v3; // rbx

  v5.receiver = self;
  v5.super_class = (Class)&OBJC_CLASS___configmanager;
  v2 = objc_msgSendSuper2(&v5, "init");
  v3 = v2;
  if ( v2 )
    -[configmanager initSysConfig](v2, "initSysConfig");
  return v3;
}

//----- (0000000100C93D9A) ----------------------------------------------------
id __cdecl +[configmanager shareInstance](id a1, SEL a2)
{

  block[0] = (__int64)_NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = (__int64)sub_100C93DFD;
  block[3] = (__int64)&unk_1012E3900;
  block[4] = (__int64)a1;
  if ( qword_1016D3A68 != -1 )
    dispatch_once(&qword_1016D3A68, block);
  return objc_retainAutoreleaseReturnValue(qword_1016D3A60);
}

//----- (0000000100C93DFD) ----------------------------------------------------
void __fastcall sub_100C93DFD(__int64 a1)
{

  v1 = objc_alloc(*(Class *)(a1 + 32));
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D3A60;
  qword_1016D3A60 = v2;
}

//----- (0000000100C93E3B) ----------------------------------------------------
void __cdecl -[configmanager initSysConfig](configmanager *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSString *v6; // rax
  NSString *v7; // rbx
  __CFString *v10; // rax
  __CFString *v27; // r14
  NSString *v28; // rax
  NSString *v29; // rax
  NSString *v37; // rax
  NSString *v38; // r13
  NSString *v42; // rax
  NSString *v60; // rax
  NSString *v61; // rbx
  NSString *v64; // rax
  NSString *v73; // [rsp+50h] [rbp-90h]
  NSString *v78; // [rsp+78h] [rbp-68h]
  __CFString *v82; // [rsp+98h] [rbp-48h]

  v2 = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, 1uLL, 1);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "firstObject");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@/"), v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v73 = v7;
  v8 = _objc_msgSend(v7, "rangeOfString:options:", CFSTR("/"), 4LL);
  if ( v8 == (id)0x7FFFFFFFFFFFFFFFLL )
  {
    v10 = &charsToLeaveEscaped;
  }
  else
  {
    v11 = _objc_msgSend(v7, "substringWithRange:", 0LL, v8);
    v10 = (__CFString *)objc_retainAutoreleasedReturnValue(v11);
  }
  v82 = v10;
  v12 = _objc_msgSend(v9, "GetBundle");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "pathForResource:ofType:", CFSTR("hexin"), CFSTR("ini"));
  v75 = objc_retainAutoreleasedReturnValue(v14);
  v16 = _objc_msgSend(v15, "GetBundle");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "pathForResource:ofType:", CFSTR("server"), CFSTR("ini"));
  v81 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "GetBundle");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v21, "pathForResource:ofType:", CFSTR("string"), CFSTR("ini"));
  v79 = objc_retainAutoreleasedReturnValue(v22);
  v24 = _objc_msgSend(v23, "GetBundle");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26 = _objc_msgSend(v25, "pathForResource:ofType:", CFSTR("commu"), CFSTR("ini"));
  v76 = objc_retainAutoreleasedReturnValue(v26);
  v27 = v82;
  v28 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@/%@"), v82, CFSTR("config/trade/communal/"));
  objc_retainAutoreleasedReturnValue(v28);
  v29 = _objc_msgSend(
          &OBJC_CLASS___NSString,
          "stringWithFormat:",
          CFSTR("%@/%@"),
          v82,
          CFSTR("bin/trade/resource/string/"));
  v78 = objc_retainAutoreleasedReturnValue(v29);
  v30 = _objc_msgSend(&OBJC_CLASS___NSFileManager, "defaultManager");
  v32 = objc_retainAutoreleasedReturnValue(v30);
  if ( v32 )
  {
    v84[0] = 0;
    v80 = v31;
    !(unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:isDirectory:", v31);
    _objc_msgSend(v32, "createDirectoryAtPath:withIntermediateDirectories:attributes:error:", v33, 1LL, 0LL);
    v34 = objc_retain(0LL);
    v83 = v34;
    if ( !(unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:isDirectory:", v78, v84) || !v84[0] )
    {
      _objc_msgSend(v32, "createDirectoryAtPath:withIntermediateDirectories:attributes:error:", v78, 1LL, 0LL);
      v36 = objc_retain(v83);
      v83 = v36;
    }
    v37 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v35, CFSTR("hexin.ini"));
    v38 = objc_retainAutoreleasedReturnValue(v37);
    v39 = (unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:", v38);
    if ( v75 && !v39 )
    {
      _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v75, v38);
      v41 = objc_retain(v83);
      v83 = v41;
    }
    v42 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v40, CFSTR("server.ini"));
    objc_retainAutoreleasedReturnValue(v42);
    v43 = +[JYPlistManage shareInstance](&OBJC_CLASS___JYPlistManage, "shareInstance");
    v44 = objc_retainAutoreleasedReturnValue(v43);
    v45 = _objc_msgSend(
            v44,
            "getConfigValueForSectionName:andKeyName:defaultStr:",
            CFSTR("JYConfigVersion"),
            CFSTR("JYServerConfig"),
            &charsToLeaveEscaped);
    v77 = objc_retainAutoreleasedReturnValue(v45);
    v46 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
    v47 = objc_retainAutoreleasedReturnValue(v46);
    v48 = _objc_msgSend(v47, "objectForKey:", CFSTR("JYServerConfig"));
    v74 = objc_retainAutoreleasedReturnValue(v48);
    v50 = (unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:", v49);
    if ( !v81 || v50 )
    {
      v52 = (unsigned __int8)_objc_msgSend(v77, "isEqualToString:", v74);
      if ( v81 && !v52 )
      {
        _objc_msgSend(v32, "removeItemAtPath:error:", v53);
        v71 = objc_retain(0LL);
        _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v81, v54);
        v55 = objc_retain(v71);
        v56 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
        v57 = objc_retainAutoreleasedReturnValue(v56);
        _objc_msgSend(v57, "setObject:forKey:", v77, CFSTR("JYServerConfig"));
        v58 = _objc_msgSend(&OBJC_CLASS___NSUserDefaults, "standardUserDefaults");
        v59 = objc_retainAutoreleasedReturnValue(v58);
        _objc_msgSend(v59, "synchronize");
        v27 = v82;
      }
    }
    else
    {
      _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v81, v51);
    }
    v60 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v80, CFSTR("commu.ini"));
    v61 = objc_retainAutoreleasedReturnValue(v60);
    v63 = (unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:", v61);
    if ( v76 && !v63 )
      _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v76, v61);
    v64 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v78, CFSTR("string.ini"));
    objc_retainAutoreleasedReturnValue(v64);
    if ( v79 )
    {
      if ( (unsigned __int8)_objc_msgSend(v32, "fileExistsAtPath:", v65) )
      {
        _objc_msgSend(v32, "removeItemAtPath:error:", v66);
        v72 = objc_retain(0LL);
        _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v79, v67);
        v68 = objc_retain(v72);
        v27 = v82;
      }
      else
      {
        _objc_msgSend(v32, "copyItemAtPath:toPath:error:", v79, v66);
      }
    }
  }
}

//----- (0000000100C94868) ----------------------------------------------------
void __cdecl -[configmanager saveConfig2File](configmanager *self, SEL a2)
{

  v2 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "getPublicModule");
  sub_100D37D40((__int64)v4, 0);
}

//----- (0000000100C948CA) ----------------------------------------------------
signed __int64 __cdecl -[configmanager GetConfigValue:Name:DefaultInteger:](
        configmanager *self,
        SEL a2,
        char *a3,
        char *a4,
        unsigned int a5)
{
  unsigned int v11; // ebx

  v7 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "getPublicModule");
  v11 = sub_100D37C50((__int64)v9, v10, (__int64)a4, a5);
  return v11;
}

//----- (0000000100C94950) ----------------------------------------------------
id __cdecl -[configmanager GetConfigValue:Name:DefaultString:](configmanager *self, SEL a2, char *a3, char *a4, id a5)
{
  volatile signed __int32 *v5; // rax
  volatile signed __int32 *v17[6]; // [rsp+0h] [rbp-30h] BYREF

  v17[0] = v5;
  v7 = objc_retain(a5);
  v8 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "getPublicModule");
  v11 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v7);
  sub_100D37C70(v17, (__int64)v10, v12, (__int64)a4, v11);
  v13 = sub_100F9F630((__int64)v17);
  v14 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v13);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  sub_100C56008(v17);
  return objc_autoreleaseReturnValue(v15);
}

//----- (0000000100C94A5D) ----------------------------------------------------
signed __int64 __cdecl -[configmanager SetConfigValue:Name:IntegerValue:](
        configmanager *self,
        SEL a2,
        char *a3,
        char *a4,
        unsigned int a5)
{
  signed __int64 v11; // rbx

  v7 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "getPublicModule");
  v11 = (int)sub_100D37CA0((__int64)v9, v10, (__int64)a4, a5, 1);
  return v11;
}

//----- (0000000100C94AEB) ----------------------------------------------------
signed __int64 __cdecl -[configmanager SetConfigValue:Name:StringValue:](
        configmanager *self,
        SEL a2,
        char *a3,
        char *a4,
        char *a5)
{
  signed __int64 v11; // rbx

  v7 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "getPublicModule");
  v11 = (int)sub_100D37CC0((__int64)v9, v10, (__int64)a4, (__int64)a5, 1);
  return v11;
}

//----- (0000000100C94B79) ----------------------------------------------------
id __cdecl -[configmanager loadString:](configmanager *self, SEL a2, id a3)
{
  volatile signed __int32 *v3; // rax
  volatile signed __int32 *v13[4]; // [rsp+0h] [rbp-20h] BYREF

  v13[0] = v3;
  v4 = objc_retain(a3);
  v5 = +[trademodulemanager shareInstance](&OBJC_CLASS___trademodulemanager, "shareInstance");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "getPublicModule");
  v8 = +[tools NSString2CString:](&OBJC_CLASS___tools, "NSString2CString:", v4);
  sub_100D37BF0(v13, (__int64)v7, (__int64)v8);
  v9 = sub_100F9F630((__int64)v13);
  v10 = +[tools CString2NSstring:](&OBJC_CLASS___tools, "CString2NSstring:", v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  sub_100C56008(v13);
  return objc_autoreleaseReturnValue(v11);
}

//----- (0000000100C94C72) ----------------------------------------------------
id __cdecl -[configmanager GetBundle](configmanager *self, SEL a2)
{
  NSBundle *bundle; // rdi
  NSBundle *v4; // rbx
  NSBundle *v12; // rax
  NSBundle *v13; // rax
  NSBundle *v14; // rdi

  bundle = self->_bundle;
  if ( bundle )
  {
    v4 = objc_retain(bundle);
  }
  else
  {
    v6 = _objc_msgSend(&OBJC_CLASS___NSBundle, "mainBundle");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "resourcePath");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "stringByAppendingString:", CFSTR("/bundle4Trade.bundle"));
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(&OBJC_CLASS___NSBundle, "bundleWithPath:", v11);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = self->_bundle;
    self->_bundle = v13;
    _objc_msgSend(self->_bundle, "load");
    v4 = objc_retain(self->_bundle);
  }
  return objc_autoreleaseReturnValue(v4);
}

//----- (0000000100C94D82) ----------------------------------------------------
NSBundle *__cdecl -[configmanager bundle](configmanager *self, SEL a2)
{
  return (NSBundle *)objc_getProperty(self, a2, 8LL, 0);
}

//----- (0000000100C94D93) ----------------------------------------------------
void __cdecl -[configmanager setBundle:](configmanager *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 8LL);
}

//----- (0000000100C94DA2) ----------------------------------------------------
void __cdecl -[configmanager .cxx_destruct](configmanager *self, SEL a2)
{
  objc_storeStrong((id *)&self->_bundle, 0LL);
}

