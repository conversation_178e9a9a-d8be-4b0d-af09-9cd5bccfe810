void __cdecl -[XinSanBanZhiShuTableViewController initObjects](XinSanBanZhiShuTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___XinSanBanZhiShuTableViewController;
  -[QuoteBaseTableViewController initObjects](&v2, "initObjects");
  -[QuoteBaseTableViewController setBlockID:](self, "setBlockID:", 55540LL);
  -[QuoteBaseTableViewController setTableID:](self, "setTableID:", 592131LL);
  -[QuoteBaseTableViewController setTableInfo:](self, "setTableInfo:", CFSTR("XSB_XinSanBanZhiShu"));
}

//----- (00000001007E555C) ----------------------------------------------------
void __cdecl -[XinSanBanZhiShuTableViewController requestForMyTable](XinSanBanZhiShuTableViewController *self, SEL a2)
{
  NSNumber *v14; // rax
  NSNumber *v17; // rax
  NSNumber *v20; // rax
  NSNumber *v21; // rbx
  NSNumber *v27; // rax
  NSNumber *v28; // r13
  NSDictionary *v29; // rax
  id *v34; // r12
  _QWORD v35[4]; // [rsp+0h] [rbp-E0h] BYREF
  id to; // [rsp+20h] [rbp-C0h] BYREF
  id location; // [rsp+48h] [rbp-98h] BYREF

  -[QuoteBaseTableViewController deleteOrder](self, "deleteOrder");
  if ( (unsigned __int8)_objc_msgSend(v2, "viewIsDisplaying") )
  {
    _objc_msgSend(v3, "setRequestAndOrderParams");
    if ( _objc_msgSend(v4, "blockID") != (id)-1LL )
    {
      v6 = _objc_msgSend(v5, "sortOrder");
      v7 = objc_retainAutoreleasedReturnValue(v6);
      v8 = _objc_msgSend(v7, "length");
      if ( v8 )
      {
        objc_initWeak(&location, v9);
        v42[0] = (__int64)CFSTR("datatype");
        v11 = _objc_msgSend(v10, "basicHQDataTypes");
        v37 = objc_retainAutoreleasedReturnValue(v11);
        v43[0] = (__int64)v37;
        v42[1] = (__int64)CFSTR("blockid");
        v13 = _objc_msgSend(v12, "blockID");
        v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v13);
        v38 = objc_retainAutoreleasedReturnValue(v14);
        v43[1] = (__int64)v38;
        v42[2] = (__int64)CFSTR("sortbegin");
        v16 = _objc_msgSend(v15, "begin");
        v17 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v16);
        v39 = objc_retainAutoreleasedReturnValue(v17);
        v43[2] = (__int64)v39;
        v42[3] = (__int64)CFSTR("sortcount");
        v19 = _objc_msgSend(v18, "count");
        v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v19);
        v21 = objc_retainAutoreleasedReturnValue(v20);
        v43[3] = (__int64)v21;
        v42[4] = (__int64)CFSTR("sortorder");
        v23 = _objc_msgSend(v22, "sortOrder");
        v24 = objc_retain(v23);
        v43[4] = (__int64)v24;
        v42[5] = (__int64)CFSTR("sortid");
        v26 = _objc_msgSend(v25, "sortID");
        v27 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v26);
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v43[5] = (__int64)v28;
        v29 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v43, v42, 6LL);
        v40 = objc_retainAutoreleasedReturnValue(v29);
        v31 = _objc_msgSend(v30, "tableRequestModule");
        v32 = objc_retain(v31);
        v35[0] = _NSConcreteStackBlock;
        v35[1] = 3254779904LL;
        v35[2] = sub_1007E58E4;
        v35[3] = &unk_1012DAED8;
        objc_copyWeak(&to, &location);
        v33 = v40;
        _objc_msgSend(v32, "request:params:callBack:", 5LL, v40, v35);
        objc_destroyWeak(v34);
        objc_destroyWeak(&location);
      }
    }
  }
}

//----- (00000001007E58E4) ----------------------------------------------------
__int64 __fastcall sub_1007E58E4(__int64 a1, void *a2, void *a3)
{
  id (__cdecl *v4)(id); // r12
  id WeakRetained; // rbx

  v3 = objc_retain(a3);
  v5 = v4(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithRequestData:extension:", v5, v3);
  v7(v5);
  return v8(WeakRetained);
}

//----- (00000001007E5956) ----------------------------------------------------
void __cdecl -[XinSanBanZhiShuTableViewController dealWithRequestData:extension:](
        XinSanBanZhiShuTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  SEL v5; // r12
  HXStockModel *v8; // rax
  HXStockModel *v9; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // r15

  v19 = objc_retain(a4);
  v6 = _objc_msgSend(self, v5, a3);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXStockModel setMainStockTableViewDataArray:](v9, "setMainStockTableViewDataArray:", v7);
  v10(v7);
  v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 34056LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v19, "thsNumberForKey:", v12);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = _objc_msgSend(v14, "unsignedLongValue");
  -[QuoteBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v16);
  v17(v14);
  v18(v12);
  -[QuoteBaseTableViewController reloadData:](self, "reloadData:", 0LL);
  -[HXBaseTableViewController setTableHasData:](self, "setTableHasData:", 1LL);
  -[QuoteBaseTableViewController setRequestFromZero:](self, "setRequestFromZero:", 0LL);
  -[QuoteBaseTableViewController setOrderCodeList](self, "setOrderCodeList");
  -[QuoteBaseTableViewController order](self, "order");
}

//----- (00000001007E5AA7) ----------------------------------------------------
id __cdecl -[XinSanBanZhiShuTableViewController calculateItemsWithRequestData:](
        XinSanBanZhiShuTableViewController *self,
        SEL a2,
        id a3)
{
  unsigned __int64 v5; // rbx
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  SEL v16; // r12
  NSNumber *v19; // r13
  NSNumber *v20; // r14
  NSNumber *v26; // rax
  NSNumber *v27; // r15
  NSNumber *v28; // rax
  NSNumber *v30; // r14
  NSNumber *v32; // rax
  NSNumber *v33; // r13
  NSNumber *v36; // rax
  NSNumber *v37; // r13
  unsigned __int64 v46; // [rsp+60h] [rbp-40h]

  v4 = objc_retain(a3);
  v44 = _objc_msgSend(v4, "mutableCopy");
  if ( _objc_msgSend(v4, "count") )
  {
    v5 = 0LL;
    v42 = v4;
    do
    {
      v6 = _objc_msgSend(v4, "thsDictionaryAtIndex:", v5);
      v7 = objc_retainAutoreleasedReturnValue(v6);
      if ( v7 )
      {
        v46 = v5;
        v9 = v7;
        v45 = _objc_msgSend(v7, "mutableCopy");
        v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
        v11 = objc_retainAutoreleasedReturnValue(v10);
        v12 = _objc_msgSend(v9, "thsNumberForKey:", v11);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v40 = v9;
        v17 = _objc_msgSend(v9, v16, v15);
        v43 = objc_retainAutoreleasedReturnValue(v17);
        v18(v15);
        _objc_msgSend(v13, "doubleValue");
        v41 = v13;
        if ( v3 == 4294967295.0 || (_objc_msgSend(v13, "doubleValue"), v3 == 2147483648.0) )
        {
          v19 = 0LL;
          v20 = 0LL;
          v5 = v46;
        }
        else
        {
          _objc_msgSend(v13, "doubleValue");
          v5 = v46;
          if ( v3 == 0.0
            || (_objc_msgSend(v43, "doubleValue"), v3 == 4294967295.0)
            || (_objc_msgSend(v43, "doubleValue"), v3 == 2147483648.0)
            || (_objc_msgSend(v43, "doubleValue"), v3 == 0.0) )
          {
            v19 = 0LL;
            v20 = 0LL;
          }
          else
          {
            _objc_msgSend(v13, "doubleValue");
            _objc_msgSend(v43, "doubleValue");
            _objc_msgSend(v43, "doubleValue");
            v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3 - v3);
            v27 = objc_retainAutoreleasedReturnValue(v26);
            v3 = (v3 - v3) / v3 * 100.0;
            v28 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", v3);
            v30 = objc_retainAutoreleasedReturnValue(v28);
            if ( v27 )
            {
              _objc_msgSend(v27, v29);
              if ( v3 != 4294967295.0 )
              {
                _objc_msgSend(v27, v31);
                if ( v3 != 2147483648.0 )
                {
                  v32 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 264648LL);
                  v33 = objc_retainAutoreleasedReturnValue(v32);
                  v34(v45, "setObject:forKeyedSubscript:", v27, v33);
                }
              }
            }
            if ( v30 )
            {
              _objc_msgSend(v30, "doubleValue");
              if ( v3 != 4294967295.0 )
              {
                _objc_msgSend(v30, v35);
                if ( v3 != 2147483648.0 )
                {
                  v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 199112LL);
                  v37 = objc_retainAutoreleasedReturnValue(v36);
                  v38(v45, "setObject:forKeyedSubscript:", v30, v37);
                }
              }
              v19 = v30;
            }
            else
            {
              v19 = 0LL;
            }
            v20 = v27;
          }
        }
        _objc_msgSend(v44, "setObject:atIndexedSubscript:", v45, v5);
        v21(v20);
        v22(v19);
        v23(v43);
        v24(v41);
        v25(v45);
        v4 = v42;
        v7 = v40;
      }
      v8(v7);
      ++v5;
    }
    while ( (unsigned __int64)_objc_msgSend(v4, "count") > v5 );
  }
  return objc_autoreleaseReturnValue(v44);
}

//----- (00000001007E5EFE) ----------------------------------------------------
void __cdecl -[XinSanBanZhiShuTableViewController myTableIsDoubleClicked:](
        XinSanBanZhiShuTableViewController *self,
        SEL a2,
        id a3)
{
  HXFrozenTableView *v3; // rax
  HXFrozenTableView *v4; // rbx
  NSIndexSet *v5; // rax
  NSIndexSet *v6; // rbx
  HXStockModel *v10; // rax
  HXStockModel *v11; // rbx
  NSArray *v12; // rax
  NSArray *v13; // r14
  NSArray *v15; // rdi
  NSNumber *v24; // rax
  NSNumber *v25; // rbx
  NSNumber *v31; // rax
  NSNumber *v33; // rax
  NSNumber *v36; // rax
  NSNumber *v39; // rax
  NSNumber *v44; // rax
  NSNumber *v47; // rax
  NSNumber *v50; // rax
  NSNumber *v51; // r15
  NSNumber *v54; // rax
  NSNumber *v55; // rbx
  NSDictionary *v56; // rax
  NSNumber *v57; // rdi
  NSNumber *v63; // [rsp+10h] [rbp-120h]
  NSNumber *v64; // [rsp+18h] [rbp-118h]
  NSNumber *v65; // [rsp+20h] [rbp-110h]
  NSNumber *v66; // [rsp+28h] [rbp-108h]
  NSNumber *v68; // [rsp+38h] [rbp-F8h]
  NSNumber *v69; // [rsp+40h] [rbp-F0h]
  _QWORD v73[10]; // [rsp+60h] [rbp-D0h] BYREF
  _QWORD v74[10]; // [rsp+B0h] [rbp-80h] BYREF

  -[QuoteBaseTableViewController setRequestRowRange](self, "setRequestRowRange", a3);
  v3 = -[HXBaseTableViewController myFrozenTable](self, "myFrozenTable");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXFrozenTableView selectedRow](v4, "selectedRow");
  v5 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( v6 )
  {
    if ( v7 == -1 )
      return;
    v8 = -[QuoteBaseTableViewController begin](self, "begin");
    v70 = (char *)(v9 - (_QWORD)v8);
  }
  else
  {
    v70 = -[QuoteBaseTableViewController begin](self, "begin");
  }
  v10 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = -[HXStockModel mainStockTableViewDataArray](v11, "mainStockTableViewDataArray");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = (char *)_objc_msgSend(v13, "count");
  v15 = v13;
  v17 = v16;
  if ( v70 < v14 )
  {
    v18 = _objc_msgSend(v17, "stockModel");
    v71 = objc_retainAutoreleasedReturnValue(v18);
    v19 = _objc_msgSend(v71, "mainStockTableViewDataArray");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v22 = _objc_msgSend(v20, "thsDictionaryAtIndex:", v21);
    v62 = v17;
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = _objc_msgSend(v23, "thsStringForKey:", v25);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    if ( v27 )
    {
      v29 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v30 = objc_retainAutoreleasedReturnValue(v29);
      _objc_msgSend(v30, "postNotificationName:object:", CFSTR("JumpToGeGuController"), 0LL);
      v73[0] = CFSTR("requesttype");
      v31 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 5LL);
      v63 = objc_retainAutoreleasedReturnValue(v31);
      v74[0] = v63;
      v73[1] = CFSTR("blockid");
      v32 = _objc_msgSend(v62, "blockID");
      v72 = v27;
      v33 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v32);
      v64 = objc_retainAutoreleasedReturnValue(v33);
      v74[1] = v64;
      v73[2] = CFSTR("TableID");
      v35 = _objc_msgSend(v34, "tableID");
      v36 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v35);
      v65 = objc_retainAutoreleasedReturnValue(v36);
      v74[2] = v65;
      v73[3] = CFSTR("sortid");
      v38 = _objc_msgSend(v37, "sortID");
      v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v38);
      v66 = objc_retainAutoreleasedReturnValue(v39);
      v74[3] = v66;
      v73[4] = CFSTR("sortorder");
      v41 = _objc_msgSend(v40, "sortOrder");
      v67 = objc_retainAutoreleasedReturnValue(v41);
      v74[4] = v67;
      v73[5] = CFSTR("sortbegin");
      v43 = _objc_msgSend(v42, "begin");
      v44 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v43);
      v68 = objc_retainAutoreleasedReturnValue(v44);
      v74[5] = v68;
      v73[6] = CFSTR("sortcount");
      v46 = _objc_msgSend(v45, "count");
      v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v46);
      v69 = objc_retainAutoreleasedReturnValue(v47);
      v74[6] = v69;
      v73[7] = CFSTR("Index");
      v49 = _objc_msgSend(v48, "begin");
      v50 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedLong:", &v70[(_QWORD)v49]);
      v51 = objc_retainAutoreleasedReturnValue(v50);
      v74[7] = v51;
      v73[8] = CFSTR("totalnumber");
      v53 = _objc_msgSend(v52, "allCodesNum");
      v54 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v53);
      v55 = objc_retainAutoreleasedReturnValue(v54);
      v74[8] = v55;
      v73[9] = CFSTR("SelectedCode");
      v74[9] = v72;
      v56 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v74, v73, 10LL);
      objc_retainAutoreleasedReturnValue(v56);
      v57 = v51;
      v27 = v72;
      v58 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
      v59 = objc_retainAutoreleasedReturnValue(v58);
      _objc_msgSend(v59, "postNotificationName:object:", CFSTR("DeliverQuotationTableDataNotification"), v60);
    }
  }
}

//----- (00000001007E64BD) ----------------------------------------------------
void __cdecl -[XinSanBanZhiShuTableViewController setOriginalSortInfoForMyFrozenTable](
        XinSanBanZhiShuTableViewController *self,
        SEL a2)
{
  HXTableManager *v2; // rax
  NSString *v12; // rax
  NSString *v13; // rax
  NSSortDescriptor *v14; // rax
  NSSortDescriptor *v15; // r13
  NSArray *v16; // rax
  NSArray *v17; // r15
  _QWORD v21[3]; // [rsp+28h] [rbp-68h] BYREF
  NSSortDescriptor *v25; // [rsp+58h] [rbp-38h] BYREF

  v24 = self;
  v21[0] = 0LL;
  v21[1] = v21;
  v21[2] = 0x2020000000LL;
  v22 = 0;
  v2 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
  objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v24, "tableKey");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = _objc_msgSend(v5, "getSelectedSchemesForKey:", v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v23 = v7;
  v9 = _objc_msgSend(v7, "dataItemEntities");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v10, "enumerateObjectsUsingBlock:");
  _objc_msgSend(v24, "setSortID:", 5LL);
  _objc_msgSend(v24, "setIsSetSortInfoByCode:", 1LL);
  v11 = _objc_msgSend(v24, "sortID");
  v12 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%ld"), v11);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(&OBJC_CLASS___NSSortDescriptor, "sortDescriptorWithKey:ascending:", v13, 1LL);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v25 = v15;
  v16 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v25, 1LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v24, "myFrozenTable");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v19, "setSortDescriptors:", v17);
  _objc_msgSend(v24, "setSortOrder:", CFSTR("A"));
  _objc_msgSend(v24, "setWenCaiSortIdentifier:", 0LL);
  _Block_object_dispose(v21, 8);
}

//----- (00000001007E6764) ----------------------------------------------------
void __fastcall sub_1007E6764(__int64 a1, void *a2, __int64 a3, _BYTE *a4)
{

  v5 = _objc_msgSend(a2, "identifier");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "longLongValue");
  if ( v8 == 199112 )
  {
    *(_BYTE *)(*(_QWORD *)(*(_QWORD *)(a1 + 32) + 8LL) + 24LL) = 1;
    *a4 = 1;
  }
}

//----- (00000001007E67D2) ----------------------------------------------------
signed __int64 __cdecl -[XinSanBanZhiShuTableViewController frozenCount](
        XinSanBanZhiShuTableViewController *self,
        SEL a2)
{
  return 3LL;
}

//----- (00000001007E67DD) ----------------------------------------------------
id __cdecl -[XinSanBanZhiShuTableViewController tableKey](XinSanBanZhiShuTableViewController *self, SEL a2)
{

  begin = (void *)self->super.super._begin;
  if ( !begin )
  {
    self->super.super._begin = (unsigned __int64)CFSTR("__xinSanBanZhiShuTableKey");
    begin = (void *)self->super.super._begin;
  }
  return objc_retainAutoreleaseReturnValue(begin);
}

//----- (00000001007E6815) ----------------------------------------------------
void __cdecl -[XinSanBanZhiShuTableViewController .cxx_destruct](XinSanBanZhiShuTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super.super._begin, 0LL);
}

