//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@interface tools : NSObject
{
}

+ (BOOL)isHtmlString:(id)arg1;
+ (id)getQsid;
+ (id)getZjzh;
+ (id)safeStringValue:(id)arg1;
+ (void)writeFileAtPath:(id)arg1 content:(id)arg2;
+ (id)dictOrArrToJsonString:(id)arg1 options:(unsigned long long)arg2;
+ (id)stackSymboles;
+ (void)addLocalLog:(id)arg1;
+ (void)writeFileLog:(id)arg1;
+ (id)getMarketShortChineseNameWithMarket:(id)arg1;
+ (id)getSetTypeWithQuotaMarket:(id)arg1;
+ (BOOL)getCodeTypeByMarket:(id)arg1;
+ (id)trans2JiaoyiMarketWih:(id)arg1;
+ (BOOL)isChuangYeBanCode:(id)arg1;
+ (BOOL)isKeChuangBanCode:(id)arg1;
+ (void)AddTradeLog:(long long)arg1 logText:(id)arg2;
+ (id)getBase64EncodeString:(id)arg1;
+ (id)getBase64DecodeString:(id)arg1;
+ (id)getBase64DecodeDataJHMode:(id)arg1;
+ (id)getBase64DecodeData:(id)arg1;
+ (id)splitString3:(char *)arg1 withSeparator:(char *)arg2 subSeparator:(char *)arg3;
+ (id)splitString2:(id)arg1 withSeparator:(id)arg2 subSeparator:(id)arg3;
+ (id)trimString:(id)arg1 left:(id)arg2;
+ (id)ProcssMeiguPrice:(id)arg1;
+ (BOOL)isMeiguMarket:(id)arg1;
+ (double)fixAmount:(double)arg1 isShowZhang:(BOOL)arg2 isKCBType:(BOOL)arg3 Mrdw:(long long)arg4;
+ (BOOL)String:(id)arg1 containKey:(id)arg2 withSep:(id)arg3;
+ (id)getTradeProductAndVersion;
+ (id)stringIsNilOrEmpty:(id)arg1 ifNilReturnDefString:(id)arg2;
+ (BOOL)isStockNeedCaclZichanWithQuotaMarket:(id)arg1;
+ (double)subForFrontNumber:(double)arg1 behindNumber:(double)arg2 afterPoint:(long long)arg3;
+ (double)addForFrontNumber:(double)arg1 behindNumber:(double)arg2 afterPoint:(long long)arg3;
+ (id)roundingForFloatNumber:(double)arg1 afterPoint:(long long)arg2;
+ (id)getQuotaMarketWithSetType:(id)arg1;
+ (id)showAlertToWindow:(id)arg1 title:(id)arg2 message:(id)arg3 iconImage:(id)arg4 isNeedCancenBtn:(BOOL)arg5 completionHandler:(CDUnknownBlockType)arg6;
+ (id)showAlertToWindowCustomBtnName:(id)arg1 title:(id)arg2 message:(id)arg3 iconImage:(id)arg4 isNeedCancenBtn:(BOOL)arg5 OkBtnName:(id)arg6 CancelBtnName:(id)arg7 completionHandler:(CDUnknownBlockType)arg8;
+ (void)openURLToSafariWithURLStr:(id)arg1;
+ (void)saveArrayWithName:(id)arg1 array:(id)arg2 saveLastPath:(id)arg3;
+ (void)saveDataWithName:(id)arg1 data:(id)arg2 saveLastPath:(id)arg3;
+ (id)getDataToDocumentsFilesWithName:(id)arg1 lastPath:(id)arg2;
+ (id)getSaveToDocumentsFileWithLastPath:(id)arg1;
+ (id)nsstring2AttributedString:(id)arg1 andStrColor:(id)arg2 andFont:(id)arg3;
+ (id)loadFormNibName:(id)arg1 bundle:(id)arg2;
+ (id)splitOppositeString:(id)arg1 withSeparator:(id)arg2 subSeparator:(id)arg3;
+ (id)splitString:(id)arg1 withSeparator:(id)arg2 subSeparator:(id)arg3;
+ (id)getNativeTimestamp;
+ (id)getDateStrWithTimestamp:(long long)arg1 formatter:(id)arg2;
+ (id)getDateWithFormatter:(id)arg1;
+ (id)getMd5WithFilePath:(id)arg1;
+ (id)getMd5String:(id)arg1;
+ (long long)getCodeDecimal:(id)arg1;
+ (id)getMacAddr;
+ (id)Base64Decode:(id)arg1;
+ (id)Base64Encode:(id)arg1;
+ (long long)getStringHex:(id)arg1;
+ (char *)NSString2CString:(id)arg1;
+ (id)_fixCString:(const char *)arg1;
+ (id)_CString2NSstring:(const char *)arg1;
+ (id)CString2NSstring:(const char *)arg1;
+ (id)getShowValueWithCount:(id)arg1 Hand:(id)arg2;
+ (double)countMoney:(id)arg1 Amount:(double)arg2 BMairu:(BOOL)arg3;
+ (double)countCanBuy:(id)arg1;
+ (double)countMeiguCanBuy:(id)arg1;
+ (BOOL)getCodeTypeByScmc:(id)arg1;
+ (int)_mwstrcmp_mask:(const char *)arg1 code:(const char *)arg2 token:(BOOL)arg3;
+ (BOOL)isHTTPURLWithStr:(id)arg1;
+ (BOOL)isShowZhang:(id)arg1 codeType:(long long)arg2;
+ (BOOL)isHuiGou:(id)arg1 codeType:(long long)arg2;
+ (BOOL)isDebt:(id)arg1 codeType:(long long)arg2;
+ (BOOL)isPureNumber:(id)arg1;
+ (BOOL)isPureFloat:(id)arg1;
+ (BOOL)isValidCodeLen:(id)arg1;
+ (BOOL)isValidMeiguCodeLen:(id)arg1;
+ (BOOL)Nsstring:(id)arg1 isEqual2CString:(char *)arg2;
+ (BOOL)isZero:(double)arg1;

@end

