//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSString;

@interface aNoeZQVOSoPboiub : NSObject
{
    BOOL _bHTMode;
    BOOL _bIsMeigu;
    BOOL _bIsAutoReg;
    NSString *_strUserid;
    NSString *_strBroker;
    NSString *_strUniqueQsid;
    NSString *_strServer;
    NSString *_strAccount;
    NSString *_strAccType;
    NSString *_strTradePwd;
    NSString *_strCommPwd;
    NSString *_strCrypt;
    NSString *_strYybid;
    NSString *_strDefCommPWD;
    NSString *_strMacLoginVersion;
    NSString *_strMacVersion;
    NSString *_strKernelver;
    NSString *_strThsName;
    NSString *_strThsPwd;
    long long _wtLinkMode;
}


@property(nonatomic) long long wtLinkMode; // @synthesize wtLinkMode=_wtLinkMode;
@property(copy, nonatomic) NSString *strThsPwd; // @synthesize strThsPwd=_strThsPwd;
@property(copy, nonatomic) NSString *strThsName; // @synthesize strThsName=_strThsName;
@property(nonatomic) BOOL bIsAutoReg; // @synthesize bIsAutoReg=_bIsAutoReg;
@property(nonatomic) BOOL bIsMeigu; // @synthesize bIsMeigu=_bIsMeigu;
@property(copy, nonatomic) NSString *strKernelver; // @synthesize strKernelver=_strKernelver;
@property(copy, nonatomic) NSString *strMacVersion; // @synthesize strMacVersion=_strMacVersion;
@property(copy, nonatomic) NSString *strMacLoginVersion; // @synthesize strMacLoginVersion=_strMacLoginVersion;
@property(nonatomic) BOOL bHTMode; // @synthesize bHTMode=_bHTMode;
@property(copy, nonatomic) NSString *strDefCommPWD; // @synthesize strDefCommPWD=_strDefCommPWD;
@property(copy, nonatomic) NSString *strYybid; // @synthesize strYybid=_strYybid;
@property(copy, nonatomic) NSString *strCrypt; // @synthesize strCrypt=_strCrypt;
@property(copy, nonatomic) NSString *strCommPwd; // @synthesize strCommPwd=_strCommPwd;
@property(copy, nonatomic) NSString *strTradePwd; // @synthesize strTradePwd=_strTradePwd;
@property(copy, nonatomic) NSString *strAccType; // @synthesize strAccType=_strAccType;
@property(copy, nonatomic) NSString *strAccount; // @synthesize strAccount=_strAccount;
@property(copy, nonatomic) NSString *strServer; // @synthesize strServer=_strServer;
@property(copy, nonatomic) NSString *strUniqueQsid; // @synthesize strUniqueQsid=_strUniqueQsid;
@property(copy, nonatomic) NSString *strBroker; // @synthesize strBroker=_strBroker;
@property(copy, nonatomic) NSString *strUserid; // @synthesize strUserid=_strUserid;
@property(readonly, nonatomic) NSString *strLinkMode;
- (void)reseTradeLoginModel;
- (id)init;

@end

