//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <AppKit/NSViewController.h>

#import "HXReuseScrollViewDataSource-Protocol.h"
#import "HXReuseScrollViewDelegate-Protocol.h"
#import "ZiXunDispatchDelegate-Protocol.h"

@class HXBaseView, HXBaseWKWebViewController, HXReuseScrollView, HXTabbarController, MajorEventContentVC, MajorEventRequestModule, NSArray, NSButton, NSString, ZiXunRequestModule;

@interface ZiXunWindowContentVC : NSViewController <HXReuseScrollViewDelegate, HXReuseScrollViewDataSource, ZiXunDispatchDelegate>
{
    BOOL _isRequestByMenuTitle;
    NSString *_stockCode;
    NSString *_market;
    HXBaseView *_meTitleView;
    HXBaseView *_containerView;
    HXBaseView *_itemListView;
    HXBaseView *_contentView;
    HXBaseView *_noStocksTipView;
    NSButton *_noStocksTipBtn;
    HXReuseScrollView *_scrollView;
    NSArray *_meMenuBtnArr;
    MajorEventRequestModule *_majorEventReqModule;
    ZiXunRequestModule *_ziXunReqModule;
    HXTabbarController *_tabBarController;
    HXBaseWKWebViewController *_webVC;
    MajorEventContentVC *_majorEventVC;
    NSArray *_majorEventArr;
    NSArray *_curDataArr;
    unsigned long long _curReqType;
    id _selectedItemModel;
    id _modelNeedToScroll;
}


@property(nonatomic) BOOL isRequestByMenuTitle; // @synthesize isRequestByMenuTitle=_isRequestByMenuTitle;
@property(retain, nonatomic) id modelNeedToScroll; // @synthesize modelNeedToScroll=_modelNeedToScroll;
@property(retain, nonatomic) id selectedItemModel; // @synthesize selectedItemModel=_selectedItemModel;
@property(nonatomic) unsigned long long curReqType; // @synthesize curReqType=_curReqType;
@property(retain, nonatomic) NSArray *curDataArr; // @synthesize curDataArr=_curDataArr;
@property(retain, nonatomic) NSArray *majorEventArr; // @synthesize majorEventArr=_majorEventArr;
@property(retain, nonatomic) MajorEventContentVC *majorEventVC; // @synthesize majorEventVC=_majorEventVC;
@property(retain, nonatomic) HXBaseWKWebViewController *webVC; // @synthesize webVC=_webVC;
@property(retain, nonatomic) HXTabbarController *tabBarController; // @synthesize tabBarController=_tabBarController;
@property(retain, nonatomic) ZiXunRequestModule *ziXunReqModule; // @synthesize ziXunReqModule=_ziXunReqModule;
@property(retain, nonatomic) MajorEventRequestModule *majorEventReqModule; // @synthesize majorEventReqModule=_majorEventReqModule;
@property(retain, nonatomic) NSArray *meMenuBtnArr; // @synthesize meMenuBtnArr=_meMenuBtnArr;
@property(retain, nonatomic) HXReuseScrollView *scrollView; // @synthesize scrollView=_scrollView;
@property(retain) NSButton *noStocksTipBtn; // @synthesize noStocksTipBtn=_noStocksTipBtn;
@property(retain) HXBaseView *noStocksTipView; // @synthesize noStocksTipView=_noStocksTipView;
@property __weak HXBaseView *contentView; // @synthesize contentView=_contentView;
@property __weak HXBaseView *itemListView; // @synthesize itemListView=_itemListView;
@property __weak HXBaseView *containerView; // @synthesize containerView=_containerView;
@property(retain) HXBaseView *meTitleView; // @synthesize meTitleView=_meTitleView;
@property(retain, nonatomic) NSString *market; // @synthesize market=_market;
@property(retain, nonatomic) NSString *stockCode; // @synthesize stockCode=_stockCode;
- (id)sortByTime:(id)arg1;
- (id)filterInvalidZiXunData:(id)arg1;
- (id)filterMajorEventData:(id)arg1 selectedIdx:(long long)arg2;
- (double)heightOfInvisibleBaseView:(id)arg1;
- (long long)preloadBaseViewCount:(id)arg1;
- (long long)reuseBaseViewCount:(id)arg1;
- (id)baseViewAtIndex:(long long)arg1 scrollView:(id)arg2;
- (long long)numberOfBaseView:(id)arg1;
- (void)setNoDataViewState:(BOOL)arg1;
- (void)loadMajorEventItem:(id)arg1;
- (void)loadZiXunURL:(id)arg1;
- (void)loadModelContent:(id)arg1;
- (void)listItemClickAction:(id)arg1;
- (void)meMenuBtnClicked:(id)arg1;
- (void)setMenuBtnState:(long long)arg1;
- (void)failToReceiveZiXunData:(unsigned long long)arg1;
- (void)receiveZiXunData:(id)arg1 tableFlag:(unsigned long long)arg2;
- (void)requestNormalZiXun:(id)arg1;
- (void)requestMajorEvent;
- (void)sendMaiDian:(id)arg1;
- (void)scrollListViewIfNeeded;
- (void)switchContentViewState:(BOOL)arg1;
- (void)initMenuBtns;
- (void)initViewState;
- (void)clearCache;
- (void)requestDataBySelectedItem:(id)arg1;
- (void)requestDataByType:(unsigned long long)arg1 params:(id)arg2;
- (void)viewDidLoad;

// Remaining properties
@property(readonly, copy) NSString *debugDescription;
@property(readonly, copy) NSString *description;
@property(readonly) unsigned long long hash;
@property(readonly) Class superclass;

@end

