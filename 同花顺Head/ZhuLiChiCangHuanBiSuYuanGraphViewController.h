//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "GraphBaseViewController.h"

@class NSMutableArray;

@interface ZhuLiChiCangHuanBiSuYuanGraphViewController : GraphBaseViewController
{
    long long _requestCount;
    NSMutableArray *_dataArray;
}


- (void)mouseEntered:(id)arg1;
- (void)infoBtnDidClick:(id)arg1;
- (id)createDataForPlot;
- (long long)perQuarterMonth:(long long)arg1;
- (void)dealWithRequestData:(id)arg1;
- (void)dealWithFirstRequestData:(id)arg1;
- (void)requestData;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
- (void)viewDidLoad;

@end

