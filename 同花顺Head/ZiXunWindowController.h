//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "HXCustomWindowController.h"

@class HXSearchTextFieldView, MajorEventRequestModule, NSToolbarItem, ZiXunWindowContianerVC;

@interface ZiXunWindowController : HXCustomWindowController
{
    NSToolbarItem *_searchToolBarItem;
    HXSearchTextFieldView *_searchTextFieldView;
    MajorEventRequestModule *_majorEventReqModule;
    ZiXunWindowContianerVC *_ziXunContianerVC;
}

+ (id)sharedInstance;

@property(retain, nonatomic) ZiXunWindowContianerVC *ziXunContianerVC; // @synthesize ziXunContianerVC=_ziXunContianerVC;
@property(retain, nonatomic) MajorEventRequestModule *majorEventReqModule; // @synthesize majorEventReqModule=_majorEventReqModule;
@property __weak HXSearchTextFieldView *searchTextFieldView; // @synthesize searchTextFieldView=_searchTextFieldView;
@property __weak NSToolbarItem *searchToolBarItem; // @synthesize searchToolBarItem=_searchToolBarItem;
- (BOOL)shouldShowKeyBoardSpiritWindow:(id)arg1;
- (void)queryStockNameForSearchTextFieldIfNeed:(id)arg1 market:(id)arg2;
- (void)keyDown:(id)arg1;
- (void)searchResultDidChange:(id)arg1 market:(id)arg2;
- (void)showKeyBoardSpiritWindow:(id)arg1;
- (void)showKeyBoardSpriritWindow:(BOOL)arg1;
- (void)showVoiceRecognitionWindow;
- (void)initSearchField;
- (void)showWindowWithModel:(id)arg1 stockCode:(id)arg2 market:(id)arg3;
- (void)showWindowWithMenuTitle:(id)arg1 stockCode:(id)arg2 market:(id)arg3;
- (void)initObjects;
- (void)windowDidLoad;

@end

