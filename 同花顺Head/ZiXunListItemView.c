void __cdecl -[ZiXunListItemView drawRect:](ZiXunListItemView *self, SEL a2, CGRect a3)
{
  SEL v5; // r12
  SEL v6; // r12
  __m128d v7; // xmm0
  NSBezierPath *v8; // rax
  NSBezierPath *v9; // rbx
  bool v11; // zf
  SEL *v12; // rcx
  NSArray *v14; // rax
  NSArray *v15; // r14
  __m128d v56; // xmm0
  __m128d v72; // [rsp+100h] [rbp-120h]
  _QWORD v73[4]; // [rsp+110h] [rbp-110h]
  _QWORD v83[12]; // [rsp+190h] [rbp-90h] BYREF

  v70.receiver = self;
  v70.super_class = (Class)&OBJC_CLASS___ZiXunListItemView;
  objc_msgSendSuper2(&v70, "drawRect:");
  v3 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  objc_msgSend_stret(v58, v5, "frame");
  *(_OWORD *)v81 = v59;
  objc_msgSend_stret(v60, v6, "frame");
  *(id *)&v7.f64[0] = v81[0];
  v7.f64[1] = v61;
  v71 = xmmword_1010CE820;
  v72 = _mm_add_pd(v7, (__m128d)xmmword_1010CF310);
  v8 = _objc_msgSend(&OBJC_CLASS___NSBezierPath, "bezierPathWithRoundedRect:xRadius:yRadius:", 2.0, 2.0);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setLineWidth:", 1.0);
  v81[0] = v4;
  _objc_msgSend(v4, "setStroke");
  _objc_msgSend(v9, "stroke");
  v80 = v10;
  v11 = (unsigned __int8)_objc_msgSend(v10, "isSelected") == 0;
  v12 = &selRef_rowMarkedBgColor;
  if ( v11 )
    v12 = &selRef_normalBgColor;
  v13 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v12);
  v78 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(v78, "setFill");
  v77 = v9;
  _objc_msgSend(v9, "fill");
  v83[0] = CFSTR("新闻");
  v83[1] = CFSTR("公告");
  v83[2] = CFSTR("研报");
  v83[3] = CFSTR("解盘");
  v83[4] = CFSTR("要闻");
  v83[5] = CFSTR("全球");
  v83[6] = CFSTR("财经");
  v83[7] = CFSTR("数据");
  v83[8] = CFSTR("分析");
  v83[9] = CFSTR("推广");
  v83[10] = CFSTR("行业");
  v83[11] = CFSTR("个基");
  v14 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v83, 12LL);
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = _objc_msgSend(v16, "categoryTF");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v18, "stringValue");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v79 = v15;
  LOBYTE(v15) = (unsigned __int8)_objc_msgSend(v15, "containsObject:", v20);
  v21(v18);
  v22 = _objc_msgSend(v80, "categoryTF");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  if ( (_BYTE)v15 )
  {
    _objc_msgSend(v23, "setHidden:", 1LL);
    v25 = v24;
    v26 = _objc_msgSend(v24, "dateTF");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    objc_msgSend_stret(v62, v25, "bounds");
    *(_QWORD *)&v82 = v63;
    v29 = (void *)v28(v25, "dateTF");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v32 = v31(v30, "width");
    v33(v27, "setOrigin:", *(double *)&v82 - v32 + -16.0, 54.0);
    v35 = (void *)v34(v25, "titleAttrStr");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    objc_msgSend_stret(v64, v25, "frame");
    v74 = xmmword_1010D6650;
    v75 = v65 + -32.0;
    v76 = 0x4041000000000000LL;
    v37(v36, "drawWithRect:options:", 33LL);
  }
  else
  {
    _objc_msgSend(v23, "setHidden:", 0LL);
    v39 = v38;
    v40 = _objc_msgSend(v38, "categoryTF");
    v41 = objc_retainAutoreleasedReturnValue(v40);
    _objc_msgSend(v41, "setOrigin:", 16.0, 12.0);
    v42 = _objc_msgSend(v39, "dateTF");
    v43 = objc_retainAutoreleasedReturnValue(v42);
    objc_msgSend_stret(v66, (SEL)v39, "bounds");
    *(_QWORD *)&v82 = v67;
    v45 = (void *)v44(v39, "dateTF");
    v46 = objc_retainAutoreleasedReturnValue(v45);
    v48 = v47(v46, "width");
    v49(v43, "setOrigin:", *(double *)&v82 - v48 + -16.0, 12.0);
    v51 = (void *)v50(v39, "titleAttrStr");
    v36 = objc_retainAutoreleasedReturnValue(v51);
    v53 = (void *)v52(v39, "categoryTF");
    v54 = objc_retainAutoreleasedReturnValue(v53);
    v82 = COERCE_UNSIGNED_INT64(v55(v54, "tail"));
    objc_msgSend_stret(v68, (SEL)v39, "frame");
    *(_QWORD *)&v56.f64[0] = v82;
    v56.f64[1] = v69;
    v73[0] = 0x4030000000000000LL;
    *(__m128d *)&v73[1] = _mm_add_pd(v56, (__m128d)xmmword_1010DC3E0);
    v73[3] = 0x4041000000000000LL;
    v57(v36, "drawWithRect:options:", 33LL);
  }
}

//----- (000000010085C71F) ----------------------------------------------------
char __cdecl -[ZiXunListItemView isFlipped](ZiXunListItemView *self, SEL a2)
{
  return 1;
}

//----- (000000010085C72A) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunListItemView categoryTF](ZiXunListItemView *self, SEL a2)
{
  CGFloat x; // rdi
  CGFloat v6; // rdi

  x = self->super._bounds.origin.x;
  if ( x == 0.0 )
  {
    v4 = -[ZiXunListItemView getTextField](self, "getTextField");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._bounds.origin.x;
    *(_QWORD *)&self->super._bounds.origin.x = v5;
    _objc_msgSend(self, "addSubview:", *(_QWORD *)&self->super._bounds.origin.x);
    x = self->super._bounds.origin.x;
  }
  return (NSTextField *)objc_retainAutoreleaseReturnValue(*(id *)&x);
}

//----- (000000010085C795) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunListItemView dateTF](ZiXunListItemView *self, SEL a2)
{
  CGFloat height; // rdi
  CGFloat v6; // rdi

  height = self->super._frame.size.height;
  if ( height == 0.0 )
  {
    v4 = -[ZiXunListItemView getTextField](self, "getTextField");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._frame.size.height;
    *(_QWORD *)&self->super._frame.size.height = v5;
    _objc_msgSend(self, "addSubview:", *(_QWORD *)&self->super._frame.size.height);
    height = self->super._frame.size.height;
  }
  return (NSTextField *)objc_retainAutoreleaseReturnValue(*(id *)&height);
}

//----- (000000010085C800) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setViewState](ZiXunListItemView *self, SEL a2)
{
  SEL v7; // r12
  NSString *v35; // rax
  NSString *v36; // rbx
  NSTextField *v38; // rax
  NSTextField *v39; // rbx
  NSTextField *v43; // rax
  NSTextField *v44; // r13

  v3 = -[ZiXunListItemView model](self, "model");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
  v6 = (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5);
  v8 = _objc_msgSend(self, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  if ( v6 )
  {
    v10 = _objc_msgSend(v9, "cateCN");
    v63 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v11, "title");
    v62 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "rtimeStr");
    v61 = objc_retainAutoreleasedReturnValue(v14);
  }
  else
  {
    v16 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
    v18 = (unsigned __int8)_objc_msgSend(v17, "isKindOfClass:", v16);
    if ( !v18 )
    {
      v62 = obj;
      v61 = obj;
      v63 = obj;
      goto LABEL_8;
    }
    v20 = -[ZiXunListItemView model](self, "model");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22 = _objc_msgSend(v21, "title");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    if ( (unsigned __int8)_objc_msgSend(v24, "isAd") )
    {
      v26 = _objc_msgSend(v25, "title");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      v28 = _objc_msgSend(
              v27,
              "stringByReplacingOccurrencesOfString:withString:",
              CFSTR("【推广】"),
              &charsToLeaveEscaped);
      v29 = v23;
      v23 = objc_retainAutoreleasedReturnValue(v28);
    }
    v62 = v23;
    v30 = _objc_msgSend(v25, "time");
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v32 = +[HXTools getDateFromDoubleNumber:](&OBJC_CLASS___HXTools, "getDateFromDoubleNumber:", v31);
    v61 = objc_retainAutoreleasedReturnValue(v32);
    v34 = _objc_msgSend(v33, "ziXunType");
    v35 = (NSString *)-[ZiXunListItemView market](self, "market");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    v37 = +[ZiXunReqParamsManager getZiXunTitle:market:](
            &OBJC_CLASS___ZiXunReqParamsManager,
            "getZiXunTitle:market:",
            v34,
            v36);
    v63 = objc_retainAutoreleasedReturnValue(v37);
  }
LABEL_8:
  v38 = (NSTextField *)-[ZiXunListItemView categoryTF](self, "categoryTF");
  v39 = objc_retainAutoreleasedReturnValue(v38);
  v40 = +[HXThemeManager blueLineColor](&OBJC_CLASS___HXThemeManager, "blueLineColor");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  -[ZiXunListItemView setTextField:string:color:](self, "setTextField:string:color:", v39, v63, v41);
  v42(v39);
  v43 = (NSTextField *)-[ZiXunListItemView dateTF](self, "dateTF");
  v44 = objc_retainAutoreleasedReturnValue(v43);
  v45 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v46 = objc_retainAutoreleasedReturnValue(v45);
  -[ZiXunListItemView setTextField:string:color:](self, "setTextField:string:color:", v44, v61, v46);
  v47(v46);
  v48(v44);
  v49 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v50 = objc_retainAutoreleasedReturnValue(v49);
  v51 = v62;
  -[ZiXunListItemView setTitleAttributedString:color:](self, "setTitleAttributedString:color:", v62, v50);
  v52(v50);
  objc_msgSend_stret(v56, (SEL)self, "frame");
  v58 = 0LL;
  v59 = v57;
  v60 = 0x4054C00000000000LL;
  _objc_msgSend(self, "setFrame:");
  v53(v61);
  v54(v51);
  v55(v63);
}

//----- (000000010085CBBC) ----------------------------------------------------
id __cdecl -[ZiXunListItemView getTextField](ZiXunListItemView *self, SEL a2)
{

  v2 = objc_alloc(&OBJC_CLASS___NSTextField);
  v3 = _objc_msgSend(v2, "init");
  _objc_msgSend(v3, "setEditable:", 0LL);
  _objc_msgSend(v3, "setSelectable:", 0LL);
  _objc_msgSend(v3, "setDrawsBackground:", 0LL);
  _objc_msgSend(v3, "setBordered:", 0LL);
  _objc_msgSend(v3, "setLineBreakMode:", 4LL);
  return objc_autoreleaseReturnValue(v3);
}

//----- (000000010085CC40) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setTextField:string:color:](ZiXunListItemView *self, SEL a2, id a3, id a4, id a5)
{
  id (__cdecl *v8)(id); // r12
  id (__cdecl *v10)(id); // r12

  v7 = objc_retain(a3);
  v9 = (char *)v8(a4);
  v11 = v10(a5);
  if ( v9 )
  {
    v12 = v9;
    if ( !_objc_msgSend(v9, "length") )
      v12 = obj;
  }
  else
  {
    v12 = obj;
  }
  objc_retain(v12);
  if ( !v11 )
  {
    v14 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v11 = v15;
  }
  _objc_msgSend(v7, "setStringValue:", v13);
  _objc_msgSend(v7, "setTextColor:", v11);
  v17 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 13.0);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v7, "setFont:", v18);
  _objc_msgSend(v7, "sizeToFit");
}

//----- (000000010085CD84) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setTitleAttributedString:color:](ZiXunListItemView *self, SEL a2, id a3, id a4)
{
  NSDictionary *v18; // rax
  NSDictionary *v19; // r14
  _QWORD v32[3]; // [rsp+30h] [rbp-60h] BYREF
  _QWORD v33[3]; // [rsp+48h] [rbp-48h] BYREF

  v5 = (char *)objc_retain(a3);
  v6 = objc_retain(a4);
  if ( v5 )
  {
    v7 = v5;
    if ( !_objc_msgSend(v5, "length") )
      v7 = obj;
  }
  else
  {
    v7 = obj;
  }
  v29 = objc_retain(v7);
  if ( v6 )
  {
    v31 = v6;
  }
  else
  {
    v8 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v31 = v9;
  }
  v10 = _objc_msgSend(&OBJC_CLASS___NSParagraphStyle, "defaultParagraphStyle");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "mutableCopy");
  v30 = v12;
  _objc_msgSend(v12, "setLineBreakMode:", 0LL);
  v32[0] = NSFontAttributeName;
  v13 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 13.0);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v33[0] = v14;
  v32[1] = NSForegroundColorAttributeName;
  v15 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v33[1] = v16;
  v32[2] = NSParagraphStyleAttributeName;
  v33[2] = v17;
  v18 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v33, v32, 3LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20(v14);
  v21 = objc_alloc(&OBJC_CLASS___NSAttributedString);
  v22 = _objc_msgSend(v21, "initWithString:attributes:", v29, v19);
  v23(v29);
  -[ZiXunListItemView setTitleAttrStr:](self, "setTitleAttrStr:", v22);
  v24(v22);
  v25(v19);
  v26(v30);
  v27(v31);
}

//----- (000000010085CFDD) ----------------------------------------------------
void __cdecl -[ZiXunListItemView mouseUp:](ZiXunListItemView *self, SEL a2, id a3)
{

  v7.receiver = self;
  v7.super_class = (Class)&OBJC_CLASS___ZiXunListItemView;
  objc_msgSendSuper2(&v7, "mouseUp:", a3);
  v3 = -[ZiXunListItemView clickBlock](self, "clickBlock");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( v4 )
  {
    v5 = -[ZiXunListItemView clickBlock](self, "clickBlock");
    v6 = (void (__fastcall **)(_QWORD))objc_retainAutoreleasedReturnValue(v5);
    v6[2](v6);
  }
}

//----- (000000010085D06A) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setModel:](ZiXunListItemView *self, SEL a2, id a3)
{
  CGFloat x; // rbx
  objc_class *v6; // rax

  v3 = objc_retain(a3);
  v4 = v3;
  x = self->super._frame.origin.x;
  if ( *(id *)&x != v3 )
  {
    v6 = (objc_class *)objc_retain(v3);
    *(Class *)((char *)&self->super.super.super.isa + v7) = v6;
    -[ZiXunListItemView setViewState](self, "setViewState");
    _objc_msgSend(self, "setNeedsDisplay:", 1LL);
  }
}

//----- (000000010085D0DF) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setIsSelected:](ZiXunListItemView *self, SEL a2, char a3)
{
  if ( LOBYTE(self->super.super._nextResponder) != a3 )
  {
    LOBYTE(self->super.super._nextResponder) = a3;
    _objc_msgSend(self, "setNeedsDisplay:", 1LL);
  }
}

//----- (000000010085D107) ----------------------------------------------------
id __cdecl -[ZiXunListItemView model](ZiXunListItemView *self, SEL a2)
{
  return *(id *)&self->super._frame.origin.x;
}

//----- (000000010085D118) ----------------------------------------------------
NSString *__cdecl -[ZiXunListItemView market](ZiXunListItemView *self, SEL a2)
{
  return *(NSString **)&self->super._frame.origin.y;
}

//----- (000000010085D129) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setMarket:](ZiXunListItemView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._frame.origin.y, a3);
}

//----- (000000010085D13D) ----------------------------------------------------
char __cdecl -[ZiXunListItemView isSelected](ZiXunListItemView *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (000000010085D14E) ----------------------------------------------------
id __cdecl -[ZiXunListItemView clickBlock](ZiXunListItemView *self, SEL a2)
{
  return objc_getProperty(self, a2, 32LL, 0);
}

//----- (000000010085D161) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setClickBlock:](ZiXunListItemView *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (000000010085D172) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setDateTF:](ZiXunListItemView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._frame.size.height, a3);
}

//----- (000000010085D186) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setCategoryTF:](ZiXunListItemView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._bounds, a3);
}

//----- (000000010085D19A) ----------------------------------------------------
NSAttributedString *__cdecl -[ZiXunListItemView titleAttrStr](ZiXunListItemView *self, SEL a2)
{
  return *(NSAttributedString **)&self->super._bounds.origin.y;
}

//----- (000000010085D1AB) ----------------------------------------------------
void __cdecl -[ZiXunListItemView setTitleAttrStr:](ZiXunListItemView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._bounds.origin.y, a3);
}

//----- (000000010085D1BF) ----------------------------------------------------
void __cdecl -[ZiXunListItemView .cxx_destruct](ZiXunListItemView *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._bounds.origin.y, 0LL);
  objc_storeStrong((id *)&self->super._bounds, 0LL);
  objc_storeStrong((id *)&self->super._frame.size.height, 0LL);
  objc_storeStrong((id *)&self->super._frame.size, 0LL);
  objc_storeStrong((id *)&self->super._frame.origin.y, 0LL);
  objc_storeStrong((id *)&self->super._frame, 0LL);
}

