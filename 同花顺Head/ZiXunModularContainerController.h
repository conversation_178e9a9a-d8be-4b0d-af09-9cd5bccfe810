//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "PanKouModularBaseViewController.h"

@class HXBaseView, HXDragToResizeView, NSTextField, WebViewWindowController, ZiXunTableViewController;

@interface ZiXunModularContainerController : PanKouModularBaseViewController
{
    NSTextField *_titleTextField;
    HXBaseView *_tableContainerView;
    HXDragToResizeView *_resizeView;
    ZiXunTableViewController *_newsTableVC;
    long long _textID;
    unsigned long long _newsRequestType;
    CDUnknownBlockType _myOpenUrlBlock;
    WebViewWindowController *_newsViewWC;
}


@property(retain, nonatomic) WebViewWindowController *newsViewWC; // @synthesize newsViewWC=_newsViewWC;
@property(copy, nonatomic) CDUnknownBlockType myOpenUrlBlock; // @synthesize myOpenUrlBlock=_myOpenUrlBlock;
@property(nonatomic) unsigned long long newsRequestType; // @synthesize newsRequestType=_newsRequestType;
@property(nonatomic) long long textID; // @synthesize textID=_textID;
@property(retain, nonatomic) ZiXunTableViewController *newsTableVC; // @synthesize newsTableVC=_newsTableVC;
@property __weak HXDragToResizeView *resizeView; // @synthesize resizeView=_resizeView;
@property __weak HXBaseView *tableContainerView; // @synthesize tableContainerView=_tableContainerView;
@property __weak NSTextField *titleTextField; // @synthesize titleTextField=_titleTextField;
- (void)refreshAllModules;
- (void)disableScroll;
- (void)openNewsWebWindowWithNewsUrl:(id)arg1;
- (void)setTableVCToContainerWithTextID:(long long)arg1;
- (void)setViewState;
- (void)requestForModularData:(id)arg1 market:(id)arg2;
- (void)viewDidAppear;
- (void)viewDidLoad;

@end

