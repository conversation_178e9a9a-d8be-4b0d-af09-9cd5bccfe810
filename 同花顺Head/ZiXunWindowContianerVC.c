void __cdecl -[Zi<PERSON><PERSON>WindowContianerVC viewDidLoad](ZiXunWindowContianerVC *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunWindowContianerVC;
  objc_msgSendSuper2(&v2, "viewDidLoad");
  -[ZiXunWindowContianerVC initViewState](self, "initViewState");
}

//----- (00000001009B8F02) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC initViewState](ZiXunWindowContianerVC *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12

  v2 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "contentView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setBackgroundColor:", v3);
  v9 = v8(&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(self, "titleView");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v13, "setBackgroundColor:", v10);
  v16 = v15(self, "titleView");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18(v17, "setBottomBorder:", 1LL);
  v20 = v19(&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v23 = v22(self, "titleView");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v25(v24, "setBorderColor:", v21);
  v27 = v26(self, "titleView");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  v29(v28, "setBorderWidth:", 1.0);
  v31 = v30(self, "contentView");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  v34 = v33(self, "contentVC");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  v37 = v36(v35, "view");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  v39(v32, "addSubview:", v38);
}

//----- (00000001009B9100) ----------------------------------------------------
ZiXunWindowContentVC *__cdecl -[ZiXunWindowContianerVC contentVC](ZiXunWindowContianerVC *self, SEL a2)
{
  NSArray *topLevelObjects; // rdi
  objc_class *v5; // rax
  HXBaseView *v8; // rax

  topLevelObjects = self->super._topLevelObjects;
  if ( !topLevelObjects )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunWindowContentVC);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZiXunWindowContentVC"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v8 = -[ZiXunWindowContianerVC contentView](self, "contentView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v11 = (char *)v9;
    if ( v9 )
      objc_msgSend_stret(v16, v9, "bounds");
    else
      memset(v16, 0, 32);
    v12 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v10), "view");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "setFrame:");
    topLevelObjects = *(NSArray **)((char *)&self->super.super.super.isa + v14);
  }
  return (ZiXunWindowContentVC *)objc_retainAutoreleaseReturnValue(topLevelObjects);
}

//----- (00000001009B91FD) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC reloadContentViewWithStockCode:market:](
        ZiXunWindowContianerVC *self,
        SEL a2,
        id a3,
        id a4)
{
  NSString *v10; // rax
  ZiXunWindowContentVC *v11; // rax
  ZiXunWindowContentVC *v12; // rax
  NSString *v14; // rax
  ZiXunWindowContentVC *v15; // rax
  ZiXunWindowContentVC *v16; // rbx
  ZiXunReqParamsManager *v19; // rax
  ZiXunReqParamsManager *v20; // rax
  NSString *v25; // [rsp+8h] [rbp-38h]

  v5 = objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v7) )
  {
    v9 = _objc_msgSend(&OBJC_CLASS___NSString, v8);
    if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v9) )
    {
      if ( _objc_msgSend(v5, "length") && _objc_msgSend(v6, "length") )
      {
        -[ZiXunWindowContianerVC setStockCode:](self, "setStockCode:", v5);
        -[ZiXunWindowContianerVC setMarket:](self, "setMarket:", v6);
        v10 = -[ZiXunWindowContianerVC stockCode](self, "stockCode");
        v25 = objc_retainAutoreleasedReturnValue(v10);
        v11 = -[ZiXunWindowContianerVC contentVC](self, "contentVC");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        -[ZiXunWindowContentVC setStockCode:](v12, "setStockCode:", v25);
        v14 = -[ZiXunWindowContianerVC market](self, "market");
        objc_retainAutoreleasedReturnValue(v14);
        v15 = -[ZiXunWindowContianerVC contentVC](self, "contentVC");
        v16 = objc_retainAutoreleasedReturnValue(v15);
        -[ZiXunWindowContentVC setMarket:](v16, "setMarket:", v17);
        v19 = +[ZiXunReqParamsManager sharedInstance](&OBJC_CLASS___ZiXunReqParamsManager, "sharedInstance");
        v20 = objc_retainAutoreleasedReturnValue(v19);
        v21 = -[ZiXunReqParamsManager getZiXunRequestParamsWithMarket:stockCode:](
                v20,
                "getZiXunRequestParamsWithMarket:stockCode:",
                v6,
                v5);
        v22 = objc_retainAutoreleasedReturnValue(v21);
        -[ZiXunWindowContianerVC refreshMenuBtn:](self, "refreshMenuBtn:", v22);
        v24(v22);
      }
    }
  }
}

//----- (00000001009B9409) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setSelectedItem:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  ZiXunWindowContianerVC *v3; // r14
  NSString *v6; // rax
  NSString *v8; // r14
  NSString *v12; // rdi
  SEL v15; // r12
  ZiXunWindowContentVC *v16; // rax
  ZiXunWindowContentVC *v17; // rbx

  v3 = self;
  v4 = objc_retain(a3);
  v5 = _objc_msgSend(&OBJC_CLASS___NewsItem, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v5) )
  {
    _objc_msgSend(v4, "ziXunType");
    v6 = -[ZiXunWindowContianerVC market](self, "market");
    v8 = objc_retainAutoreleasedReturnValue(v6);
    v10 = +[ZiXunReqParamsManager getZiXunTitle:market:](
            &OBJC_CLASS___ZiXunReqParamsManager,
            "getZiXunTitle:market:",
            v9,
            v8);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = v8;
    v3 = self;
    -[ZiXunWindowContianerVC setMenuBtnStateByTitle:](self, "setMenuBtnStateByTitle:", v11);
    v13(v11);
  }
  else
  {
    v14 = _objc_msgSend(&OBJC_CLASS___MajorEventModel, "class");
    if ( (unsigned __int8)_objc_msgSend(v4, v15, v14) )
      -[ZiXunWindowContianerVC setMenuBtnStateByTitle:](self, "setMenuBtnStateByTitle:", CFSTR("大事提醒"));
  }
  v16 = -[ZiXunWindowContianerVC contentVC](v3, "contentVC");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  -[ZiXunWindowContentVC requestDataBySelectedItem:](v17, "requestDataBySelectedItem:", v4);
}

//----- (00000001009B955D) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setSelectedMenuTitle:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  _QWORD v22[2]; // [rsp+10h] [rbp-50h] BYREF
  _QWORD v23[2]; // [rsp+20h] [rbp-40h] BYREF

  v3 = objc_retain(a3);
  -[ZiXunWindowContianerVC setMenuBtnStateByTitle:](self, "setMenuBtnStateByTitle:", v3);
  if ( (unsigned __int8)v4(v3, "isEqualToString:", CFSTR("大事提醒")) )
  {
    v6 = v5(self, "contentVC");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8(v7, "requestDataByType:params:", 1LL, 0LL);
  }
  else if ( (unsigned __int8)_objc_msgSend(v3, "isEqualToString:", CFSTR("公告")) )
  {
    v22[0] = off_1012E2C20[0];
    v10 = v9(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
    v20 = objc_retainAutoreleasedReturnValue(v10);
    v23[0] = v20;
    v22[1] = off_1012E2C28;
    v12 = v11(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v23[1] = v13;
    v15 = v14(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v23, v22, 2LL);
    v21 = objc_retainAutoreleasedReturnValue(v15);
    v17 = v16(self, "contentVC");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "requestDataByType:params:", 0LL, v21);
  }
}

//----- (00000001009B9727) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC resetSelection](ZiXunWindowContianerVC *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSArray *v5; // rax
  NSArray *v6; // rbx
  ZiXunWindowContentVC *v9; // rax
  ZiXunWindowContentVC *v10; // rbx

  -[ZiXunWindowContianerVC setMenuBtnStateByTag:](self, "setMenuBtnStateByTag:", 0LL);
  v2 = -[ZiXunWindowContianerVC reqParamArr](self, "reqParamArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "count");
  if ( v4 )
  {
    v5 = -[ZiXunWindowContianerVC reqParamArr](self, "reqParamArr");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "thsDictionaryAtIndex:", 0LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = -[ZiXunWindowContianerVC contentVC](self, "contentVC");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    -[ZiXunWindowContentVC requestDataByType:params:](v10, "requestDataByType:params:", 0LL, v8);
    v11(v10);
    v12(v8);
  }
}

//----- (00000001009B9818) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC sendMaiDian:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  NSString *v6; // rax
  NSString *v7; // r14

  v8 = v3;
  v4 = objc_retain(a3);
  v5 = v4;
  if ( v4 && _objc_msgSend(v4, "length") )
  {
    v6 = _objc_msgSend(
           &OBJC_CLASS___NSString,
           "stringWithFormat:",
           CFSTR("全局.重大事件（%@）_资讯弹窗"),
           v5);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
      &OBJC_CLASS___UserLogSendingQueueManager,
      "sendUserLog:action:params:needWait:",
      11LL,
      v7,
      0LL,
      1LL,
      v8);
  }
}

//----- (00000001009B98B5) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC refreshMenuBtn:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  HXBaseView *v4; // rax
  HXBaseView *v5; // r14
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (**v12)(id, SEL, ...); // r12
  id (**v13)(id, SEL, ...); // r15
  unsigned __int64 i; // r13
  SEL v17; // r12
  unsigned __int64 v23; // r14
  bool v48; // zf
  SEL v64; // [rsp+58h] [rbp-128h]
  SEL v65; // [rsp+60h] [rbp-120h]
  SEL v66; // [rsp+68h] [rbp-118h]
  SEL v70; // [rsp+88h] [rbp-F8h]
  SEL v72; // [rsp+98h] [rbp-E8h]
  __CFString *v75; // [rsp+B0h] [rbp-D0h]
  id obj; // [rsp+B8h] [rbp-C8h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "count") )
  {
    v73 = v3;
    v77 = self;
    v4 = -[ZiXunWindowContianerVC titleView](self, "titleView");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = v6(v5, "subviews");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(v8, "copy");
    v61 = 0LL;
    v60 = 0LL;
    v59 = 0LL;
    v58 = 0LL;
    v11 = v10;
    v13 = v12;
    obj = objc_retain(v11);
    v75 = (__CFString *)((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v13)(
                          obj,
                          "countByEnumeratingWithState:objects:count:",
                          &v58,
                          v79,
                          16LL);
    if ( v75 )
    {
      v71 = *(const char **)v59;
      do
      {
        v72 = "removeFromSuperview";
        for ( i = 0LL; i < (unsigned __int64)v75; ++i )
        {
          if ( *(const char **)v59 != v71 )
            objc_enumerationMutation(obj);
          v15 = *(void **)(*((_QWORD *)&v58 + 1) + 8 * i);
          v16 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
          if ( (unsigned __int8)_objc_msgSend(v15, v17, v16) )
            _objc_msgSend(v15, v72);
        }
        v13 = &_objc_msgSend;
        v75 = (__CFString *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v58, v79, 16LL);
      }
      while ( v75 );
    }
    v18 = (void *)((__int64 (__fastcall *)(objc_class *, const char *))v13)(&OBJC_CLASS___NSMutableArray, "array");
    objc_retainAutoreleasedReturnValue(v18);
    v19 = (void *)((__int64 (__fastcall *)(objc_class *, const char *))v13)(&OBJC_CLASS___NSMutableArray, "array");
    v68 = objc_retainAutoreleasedReturnValue(v19);
    v20 = (__int64)v73;
    v21 = ((__int64 (__fastcall *)(id, const char *))v13)(v73, "count");
    v78 = v22;
    if ( v21 )
    {
      v75 = off_1012E2C20[0];
      v71 = "thsNumberForKey:";
      v72 = "class";
      v62 = "isKindOfClass:";
      v67 = "doubleValue";
      v63 = "integerValue";
      v69 = "market";
      v64 = "getZiXunTitle:market:";
      v65 = "getBtnByTitle:index:";
      v70 = "addObject:";
      v66 = "addSubview:";
      v23 = 0LL;
      do
      {
        v24 = (void *)((__int64 (__fastcall *)(__int64, const char *, unsigned __int64))v13)(
                        v20,
                        "thsDictionaryAtIndex:",
                        v23);
        v25 = objc_retainAutoreleasedReturnValue(v24);
        v26 = (void *)((__int64 (__fastcall *)(id, const char *, __CFString *))v13)(v25, v71, v75);
        objc_retainAutoreleasedReturnValue(v26);
        v27 = ((__int64 (__fastcall *)(NSArray *, SEL))v13)(&OBJC_CLASS___NSNumber, v72);
        if ( ((unsigned __int8 (__fastcall *)(__int64, const char *, __int64))v13)(v28, v62, v27)
          && ((double (__fastcall *)(void *, const char *))v13)(v29, v67) != 4294967295.0
          && ((double (__fastcall *)(void *, const char *))v13)(v29, v67) != 2147483648.0 )
        {
          v74 = &OBJC_CLASS___ZiXunReqParamsManager;
          v30 = ((__int64 (__fastcall *)(void *, const char *))v13)(v29, v63);
          v31 = (void *)((__int64 (__fastcall *)(id, const char *))v13)(v77, v69);
          v32 = objc_retainAutoreleasedReturnValue(v31);
          v33 = _objc_msgSend(v74, v64, v30, v32);
          v34 = objc_retainAutoreleasedReturnValue(v33);
          v35 = v32;
          v13 = &_objc_msgSend;
          v74 = v34;
          v36 = _objc_msgSend(v77, v65, v34, v23);
          v37 = objc_retainAutoreleasedReturnValue(v36);
          v38 = v37;
          if ( v37 )
          {
            _objc_msgSend(v78, v70, v37);
            v39 = _objc_msgSend(v77, "titleView");
            v40 = objc_retainAutoreleasedReturnValue(v39);
            _objc_msgSend(v40, v66, v38);
            v41 = v40;
            v13 = &_objc_msgSend;
            _objc_msgSend(v68, v70, v25);
          }
        }
        ++v23;
        v20 = (__int64)v73;
      }
      while ( ((__int64 (__fastcall *)(id, const char *))v13)(v73, "count") > v23 );
      v42 = v69;
    }
    else
    {
      v42 = "market";
    }
    v43 = v77;
    v44 = (void *)((__int64 (__fastcall *)(id, const char *))v13)(v77, v42);
    v45 = objc_retainAutoreleasedReturnValue(v44);
    v46 = (unsigned __int8)+[HXTools isHuShenAMarket:](&OBJC_CLASS___HXTools, "isHuShenAMarket:", v45);
    v48 = v46 == 0;
    v49 = v68;
    if ( !v48 )
    {
      v50 = _objc_msgSend(v47, "count");
      v51 = _objc_msgSend(v43, "getBtnByTitle:index:", CFSTR("大事提醒"), v50);
      v52 = objc_retainAutoreleasedReturnValue(v51);
      _objc_msgSend(v78, "addObject:", v52);
      v53 = _objc_msgSend(v43, "titleView");
      v54 = objc_retainAutoreleasedReturnValue(v53);
      _objc_msgSend(v54, "addSubview:", v55);
      _objc_msgSend(v49, "addObject:", CFSTR("大事提醒"));
    }
    _objc_msgSend(v43, "setMenuBtnArr:", v47);
    _objc_msgSend(v43, "setReqParamArr:", v49);
    v3 = v73;
  }
}

//----- (00000001009B9F16) ----------------------------------------------------
id __cdecl -[ZiXunWindowContianerVC getBtnByTitle:index:](
        ZiXunWindowContianerVC *self,
        SEL a2,
        id a3,
        unsigned __int64 a4)
{
  HXBaseView *v7; // rax
  HXBaseView *v8; // rbx

  v5 = objc_retain(a3);
  v6 = v5;
  if ( v5 && _objc_msgSend(v5, "length") )
  {
    v7 = -[ZiXunWindowContianerVC titleView](self, "titleView");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    _objc_msgSend(v8, "height");
    v9 = objc_alloc((Class)&OBJC_CLASS___HXButton);
    v10 = _objc_msgSend(v9, "init");
    _objc_msgSend(v10, "setTitle:", v11);
    _objc_msgSend(v10, "setTag:", a4);
    _objc_msgSend(v10, "setBordered:", 0LL);
    v12 = _objc_msgSend(&OBJC_CLASS___NSFont, "systemFontOfSize:", 13.0);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v10, "setFont:", v13);
    v14 = +[HXThemeManager majorModuleLineColor](&OBJC_CLASS___HXThemeManager, "majorModuleLineColor");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(v10, "setBorderColor:", v15);
    _objc_msgSend(v10, "setBorderWidth:", 2.0);
    _objc_msgSend(v10, "setBottomBorder:", 1LL);
    _objc_msgSend(v10, "setRightBorder:", 1LL);
    _objc_msgSend(v10, "setCanBeSelected:", 1LL);
    v16 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    _objc_msgSend(v10, "setTextColorDefault:", v17);
    v18 = +[HXThemeManager secondNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "secondNavigationBarBgColor");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v10, "setBackgroundColor:", v19);
    v20 = +[HXThemeManager secondNavigationBarSelectedColor](
            &OBJC_CLASS___HXThemeManager,
            "secondNavigationBarSelectedColor");
    v21 = objc_retainAutoreleasedReturnValue(v20);
    _objc_msgSend(v10, "setBackgroundColorSelected:", v21);
    _objc_msgSend(v10, "setTarget:", self);
    _objc_msgSend(v10, "setAction:", "menuBtnClicked:");
    _objc_msgSend(v10, "setFrame:");
  }
  else
  {
    v10 = 0LL;
  }
  return objc_autoreleaseReturnValue(v10);
}

//----- (00000001009BA1E3) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setMenuBtnStateByTitle:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  unsigned __int64 i; // r13
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  signed __int8 v19; // al
  SEL v26; // [rsp+50h] [rbp-E0h]
  SEL v27; // [rsp+58h] [rbp-D8h]
  SEL v28; // [rsp+60h] [rbp-D0h]
  id obj; // [rsp+78h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)v5(v3, "isKindOfClass:", v4) )
  {
    v30 = v3;
    v24 = 0LL;
    v23 = 0LL;
    v22 = 0LL;
    v21 = 0LL;
    v29 = self;
    v7 = v6(self, "menuBtnArr");
    obj = objc_retainAutoreleasedReturnValue(v7);
    v9 = v8(obj, "countByEnumeratingWithState:objects:count:", &v21, v32, 16LL);
    if ( v9 )
    {
      v11 = v9;
      v25 = *(_QWORD *)v22;
      do
      {
        v26 = "title";
        v27 = "isEqualToString:";
        v28 = "setIsSelected:";
        for ( i = 0LL; i < (unsigned __int64)v11; ++i )
        {
          if ( *(_QWORD *)v22 != v25 )
            objc_enumerationMutation(obj);
          v13 = *(void **)(*((_QWORD *)&v21 + 1) + 8 * i);
          v14 = v10(&OBJC_CLASS___HXButton, "class");
          if ( (unsigned __int8)v15(v13, "isKindOfClass:", v14) )
          {
            v16 = v10(v13, v26);
            v17 = objc_retainAutoreleasedReturnValue(v16);
            v19 = (unsigned __int8)v18(v17, v27, v30);
            v20(v13, v28, (unsigned int)v19);
          }
        }
        v11 = v10(obj, "countByEnumeratingWithState:objects:count:", &v21, v32, 16LL);
      }
      while ( v11 );
    }
    v3 = v30;
    _objc_msgSend(v29, "sendMaiDian:", v30);
  }
}

//----- (00000001009BA41E) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setMenuBtnStateByTag:](ZiXunWindowContianerVC *self, SEL a2, signed __int64 a3)
{
  NSArray *v4; // rax
  NSArray *v5; // rbx
  unsigned __int64 v6; // r12
  NSArray *v7; // rax
  unsigned __int64 i; // r13
  SEL v18; // [rsp+48h] [rbp-D8h]
  SEL v19; // [rsp+50h] [rbp-D0h]
  id obj; // [rsp+68h] [rbp-B8h]

  if ( a3 >= 0 )
  {
    v4 = -[ZiXunWindowContianerVC menuBtnArr](self, "menuBtnArr");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "count");
    if ( v6 > a3 )
    {
      v20 = (id)a3;
      v16 = 0LL;
      v15 = 0LL;
      v14 = 0LL;
      v13 = 0LL;
      v7 = -[ZiXunWindowContianerVC menuBtnArr](self, "menuBtnArr");
      obj = objc_retainAutoreleasedReturnValue(v7);
      v21 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v13, v23, 16LL);
      if ( v21 )
      {
        v17 = *(_QWORD *)v14;
        do
        {
          v8 = "class";
          v18 = "tag";
          v19 = "setIsSelected:";
          for ( i = 0LL; i < (unsigned __int64)v21; ++i )
          {
            if ( *(_QWORD *)v14 != v17 )
              objc_enumerationMutation(obj);
            v10 = *(void **)(*((_QWORD *)&v13 + 1) + 8 * i);
            v11 = _objc_msgSend(&OBJC_CLASS___HXButton, v8);
            if ( (unsigned __int8)_objc_msgSend(v10, "isKindOfClass:", v11) )
            {
              v12 = _objc_msgSend(v10, v18);
              _objc_msgSend(v10, v19, v12 == v20);
            }
          }
          v21 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v13, v23, 16LL);
        }
        while ( v21 );
      }
    }
  }
}

//----- (00000001009BA623) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC menuBtnClicked:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  id (**v12)(id, SEL, ...); // r15
  unsigned __int64 v13; // r14
  id (**v14)(id, SEL, ...); // r13
  unsigned __int64 v15; // rbx
  id obj; // [rsp+60h] [rbp-C0h]

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "menuBtnArr");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( _objc_msgSend(v6, "count") )
  {
    v7 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
    v8 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v7);
    if ( v8 )
    {
      v41 = v3;
      v39 = 0LL;
      v38 = 0LL;
      v37 = 0LL;
      v36 = 0LL;
      v44 = v9;
      v10 = _objc_msgSend(v9, "menuBtnArr");
      obj = objc_retainAutoreleasedReturnValue(v10);
      v11 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v36, v45, 16LL);
      v12 = &_objc_msgSend;
      if ( v11 )
      {
        v13 = (unsigned __int64)v11;
        v42 = *(id *)v37;
        do
        {
          v14 = v12;
          v40 = "setIsSelected:";
          v15 = 0LL;
          do
          {
            if ( *(id *)v37 != v42 )
              objc_enumerationMutation(obj);
            v16 = *(id *)(*((_QWORD *)&v36 + 1) + 8 * v15);
            v17 = ((__int64 (__fastcall *)(__objc2_class *, const char *))v14)(&OBJC_CLASS___HXButton, "class");
            if ( ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v14)(v16, "isKindOfClass:", v17) )
              v18(v16, v40, v16 == v41);
            ++v15;
          }
          while ( v15 < v13 );
          v12 = v14;
          v13 = ((__int64 (__fastcall *)(id, const char *, __int128 *, char *, __int64))v14)(
                  obj,
                  "countByEnumeratingWithState:objects:count:",
                  &v36,
                  v45,
                  16LL);
        }
        while ( v13 );
      }
      v19 = (void *)((__int64 (__fastcall *)(void *, const char *))v12)(v44, "reqParamArr");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v3 = v41;
      v21 = ((__int64 (__fastcall *)(id, const char *))v12)(v41, "tag");
      v22 = (void *)((__int64 (__fastcall *)(id, const char *, __int64))v12)(v20, "thsStringAtIndex:", v21);
      objc_retainAutoreleasedReturnValue(v22);
      v42 = v23;
      if ( ((unsigned __int8 (__fastcall *)(void *, const char *, __CFString *))v12)(
             v23,
             "isEqualToString:",
             CFSTR("大事提醒")) )
      {
        v24 = (void *)((__int64 (__fastcall *)(void *, const char *))v12)(v44, "contentVC");
        v25 = objc_retainAutoreleasedReturnValue(v24);
        ((void (__fastcall *)(id, const char *, __int64, _QWORD))v12)(v25, "requestDataByType:params:", 1LL, 0LL);
      }
      else
      {
        v26 = (void *)((__int64 (__fastcall *)(void *, const char *))v12)(v44, "reqParamArr");
        v27 = objc_retainAutoreleasedReturnValue(v26);
        v28 = ((__int64 (__fastcall *)(id, const char *))v12)(v3, "tag");
        v29 = (void *)((__int64 (__fastcall *)(id, const char *, __int64))v12)(v27, "thsDictionaryAtIndex:", v28);
        v25 = objc_retainAutoreleasedReturnValue(v29);
        v31 = (void *)((__int64 (__fastcall *)(__int64, const char *))v12)(v30, "contentVC");
        v32 = objc_retainAutoreleasedReturnValue(v31);
        ((void (__fastcall *)(id, const char *, _QWORD, id))v12)(v32, "requestDataByType:params:", 0LL, v25);
      }
      v33 = (void *)((__int64 (__fastcall *)(id, const char *))v12)(v3, "title");
      v34 = objc_retainAutoreleasedReturnValue(v33);
      ((void (__fastcall *)(__int64, const char *, id))v12)(v35, "sendMaiDian:", v34);
    }
  }
  else
  {
  }
}

//----- (00000001009BA9CB) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContianerVC titleView](ZiXunWindowContianerVC *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._nextResponder);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001009BA9E4) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setTitleView:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.super._nextResponder, a3);
}

//----- (00000001009BA9F8) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunWindowContianerVC contentView](ZiXunWindowContianerVC *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001009BAA11) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setContentView:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibName, a3);
}

//----- (00000001009BAA25) ----------------------------------------------------
NSString *__cdecl -[ZiXunWindowContianerVC stockCode](ZiXunWindowContianerVC *self, SEL a2)
{
  return (NSString *)&self->super._nibBundle->super.isa;
}

//----- (00000001009BAA36) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setStockCode:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._nibBundle, a3);
}

//----- (00000001009BAA4A) ----------------------------------------------------
NSString *__cdecl -[ZiXunWindowContianerVC market](ZiXunWindowContianerVC *self, SEL a2)
{
  return (NSString *)self->super._representedObject;
}

//----- (00000001009BAA5B) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setMarket:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super._representedObject, a3);
}

//----- (00000001009BAA6F) ----------------------------------------------------
NSArray *__cdecl -[ZiXunWindowContianerVC menuBtnArr](ZiXunWindowContianerVC *self, SEL a2)
{
  return (NSArray *)self->super._title;
}

//----- (00000001009BAA80) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setMenuBtnArr:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._title, a3);
}

//----- (00000001009BAA94) ----------------------------------------------------
NSArray *__cdecl -[ZiXunWindowContianerVC reqParamArr](ZiXunWindowContianerVC *self, SEL a2)
{
  return (NSArray *)self->super.view;
}

//----- (00000001009BAAA5) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setReqParamArr:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.view, a3);
}

//----- (00000001009BAAB9) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC setContentVC:](ZiXunWindowContianerVC *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, a3);
}

//----- (00000001009BAACD) ----------------------------------------------------
void __cdecl -[ZiXunWindowContianerVC .cxx_destruct](ZiXunWindowContianerVC *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, 0LL);
  objc_storeStrong((id *)&self->super.view, 0LL);
  objc_storeStrong((id *)&self->super._title, 0LL);
  objc_storeStrong(&self->super._representedObject, 0LL);
  objc_storeStrong((id *)&self->super._nibBundle, 0LL);
  objc_destroyWeak((id *)&self->super._nibName);
  objc_destroyWeak(&self->super.super._nextResponder);
}

