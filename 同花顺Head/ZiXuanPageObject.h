//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSArray;

@interface ZiXuanPageObject : NSObject
{
    NSArray *_tabItemObjects;
    long long _selectedIndex;
}


@property(nonatomic) long long selectedIndex; // @synthesize selectedIndex=_selectedIndex;
@property(retain, nonatomic) NSArray *tabItemObjects; // @synthesize tabItemObjects=_tabItemObjects;
- (id)copy;
- (void)encodeWithCoder:(id)arg1;
- (id)initWithCoder:(id)arg1;
- (id)initWithZiXuanGuVC:(id)arg1;

@end

