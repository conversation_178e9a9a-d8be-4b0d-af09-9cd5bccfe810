void __cdecl -[ZiXunModuleViewController viewDidLoad](ZiXunModuleViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunModuleViewController;
  objc_msgSendSuper2(&v2, "viewDidLoad");
  -[ZiXunModuleViewController initDefaultProperties](self, "initDefaultProperties");
}

//----- (0000000100BB85A1) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController viewDidAppear](ZiXunModuleViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunModuleViewController;
  objc_msgSendSuper2(&v2, "viewDidAppear");
  -[ZiXunModuleViewController setViewDidAppearFlag:](self, "setViewDidAppearFlag:", 1LL);
  if ( (unsigned __int8)-[ZiXunModuleViewController needJumpToQuickTrade](self, "needJumpToQuickTrade") )
  {
    -[ZiXunModuleViewController setNeedJumpToQuickTrade:](self, "setNeedJumpToQuickTrade:", 0LL);
    -[ZiXunModuleViewController openQuickTradeVC](self, "openQuickTradeVC");
  }
}

//----- (0000000100BB861C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController viewWillAppear](ZiXunModuleViewController *self, SEL a2)
{

  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZiXunModuleViewController;
  objc_msgSendSuper2(&v4, "viewWillAppear");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(
    v3,
    "addObserver:selector:name:object:",
    self,
    "autoSwitchGeGuFenShi:",
    CFSTR("autoSwitchGeGuFenShiNotification"),
    0LL);
}

//----- (0000000100BB86A2) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController viewWillDisappear](ZiXunModuleViewController *self, SEL a2)
{
  StockNotesViewController *v2; // rax
  StockNotesViewController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  v10.receiver = self;
  v10.super_class = (Class)&OBJC_CLASS___ZiXunModuleViewController;
  objc_msgSendSuper2(&v10, "viewWillDisappear");
  v2 = -[ZiXunModuleViewController stockNotesVC](self, "stockNotesVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (unsigned __int8)v4(v3, "viewIsDisplaying");
  if ( v5 )
    -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
  v7 = v6(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "removeObserver:name:object:", self, CFSTR("autoSwitchGeGuFenShiNotification"), 0LL);
}

//----- (0000000100BB876B) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController viewDidDisappear](ZiXunModuleViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunModuleViewController;
  objc_msgSendSuper2(&v2, "viewDidDisappear");
  -[ZiXunModuleViewController setViewDidAppearFlag:](self, "setViewDidAppearFlag:", 0LL);
}

//----- (0000000100BB87AE) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController initDefaultProperties](ZiXunModuleViewController *self, SEL a2)
{
  -[ZiXunModuleViewController setReqParamArr:](self, "setReqParamArr:", __NSArray0__);
  -[ZiXunModuleViewController setHasSetDefaultTabbarCtrlIndex:](self, "setHasSetDefaultTabbarCtrlIndex:", 0LL);
}

//----- (0000000100BB87E8) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController openQuickTradeVC](ZiXunModuleViewController *self, SEL a2)
{
  id (**v2)(id, SEL, ...); // r13
  ZiXunMenuViewController *v3; // rax
  ZiXunMenuViewController *v4; // rbx
  NSArray *v5; // rax
  NSArray *v6; // r15
  id (**v15)(id, SEL, ...); // r15
  SEL v25; // [rsp+40h] [rbp-F0h]
  SEL v26; // [rsp+48h] [rbp-E8h]
  SEL v28; // [rsp+58h] [rbp-D8h]
  SEL v29; // [rsp+60h] [rbp-D0h]
  id obj; // [rsp+78h] [rbp-B8h]

  v21 = 0LL;
  v22 = 0LL;
  v23 = 0LL;
  v24 = 0LL;
  v2 = &_objc_msgSend;
  v31 = self;
  v3 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[ZiXunMenuViewController menuBtns](v4, "menuBtns");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  obj = v6;
  v7 = _objc_msgSend(v6, "countByEnumeratingWithState:objects:count:", &v21, v33, 16LL);
  if ( v7 )
  {
    v8 = v7;
    v27 = *(_QWORD *)v22;
    while ( 2 )
    {
      v25 = "class";
      v26 = "isKindOfClass:";
      v28 = "title";
      v29 = "isEqualToString:";
      v9 = 0LL;
      v30 = v8;
      do
      {
        if ( *(_QWORD *)v22 != v27 )
          objc_enumerationMutation(obj);
        v10 = *(id *)(*((_QWORD *)&v21 + 1) + 8LL * (_QWORD)v9);
        v11 = ((id (*)(id, SEL, ...))v2)(&OBJC_CLASS___HXButton, v25);
        if ( (unsigned __int8)((id (*)(id, SEL, ...))v2)(v10, v26, v11) )
        {
          v12 = objc_retain(v10);
          v13 = ((id (*)(id, SEL, ...))v2)(v12, v28);
          v14 = objc_retainAutoreleasedReturnValue(v13);
          v15 = v2;
          v16 = (unsigned __int8)((id (*)(id, SEL, ...))v2)(v14, v29, CFSTR("快捷交易"));
          if ( v16 )
          {
            v18 = v31;
            if ( (unsigned __int8)_objc_msgSend(v31, "viewDidAppearFlag") )
              _objc_msgSend(v18, "ziXunMenuBtnClicked:", v19);
            else
              _objc_msgSend(v18, "setNeedJumpToQuickTrade:", 1LL);
            goto LABEL_16;
          }
          v2 = v15;
          v8 = v30;
        }
        v9 = (char *)v9 + 1;
      }
      while ( v8 != v9 );
      v8 = ((id (*)(id, SEL, ...))v2)(obj, "countByEnumeratingWithState:objects:count:", &v21, v33, 16LL);
      if ( v8 )
        continue;
      break;
    }
  }
LABEL_16:
}

//----- (0000000100BB8A71) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshContentViews](ZiXunModuleViewController *self, SEL a2)
{
  NSArray *topLevelObjects; // rax

  topLevelObjects = self->super._topLevelObjects;
  if ( (unsigned __int64)&topLevelObjects[-1].super.isa + 6 > 0x16 )
    v3 = &selRef_refreshDefaultZiXunModule;
  else
    v3 = off_1012E9C40[(_QWORD)topLevelObjects - 2];
  _objc_msgSend(self, *v3);
}

//----- (0000000100BB8AA9) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController quickTradeDidUpdate:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  NSString *v15; // rax
  NSString *v16; // rbx
  NSString *v18; // rax
  NSString *v19; // r14
  __CFString *v23; // rax
  __CFString *v24; // rbx
  __CFString *v25; // rdi
  HXTabbarController *v29; // rax
  HXTabbarController *v30; // rbx
  ZiXunMenuViewController *v32; // rax
  ZiXunMenuViewController *v33; // rbx
  NSArray *v34; // rax
  NSArray *v35; // rax
  ZiXunMenuViewController *v39; // rax
  ZiXunMenuViewController *v40; // rax
  NSArray *v41; // rax
  NSArray *v42; // rbx
  NSString *v58; // r15
  __CFString *v63; // [rsp+10h] [rbp-70h]
  NSString *v66; // [rsp+30h] [rbp-50h]
  unsigned int v68; // [rsp+44h] [rbp-3Ch]
  bool v70; // [rsp+57h] [rbp-29h]

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
  {
    v6 = _objc_msgSend(v5, "objectForKeyedSubscript:", CFSTR("tradePrice"));
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = _objc_msgSend(v8, "objectForKeyedSubscript:", CFSTR("maiMaiType"));
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v68 = (unsigned int)_objc_msgSend(v10, "intValue");
    v12 = _objc_msgSend(v11, "objectForKeyedSubscript:", CFSTR("market"));
    v65 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "objectForKeyedSubscript:", CFSTR("stockCode"));
    v15 = (NSString *)objc_retainAutoreleasedReturnValue(v14);
    v16 = v15;
    if ( !v15 || !_objc_msgSend(v15, "length") )
    {
      v18 = -[ZiXunModuleViewController stockCode](self, "stockCode");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v16 = v19;
    }
    v66 = v16;
    v20 = _objc_msgSend(v17, "objectForKeyedSubscript:", CFSTR("selectedIndex"));
    v67 = objc_retainAutoreleasedReturnValue(v20);
    v22 = _objc_msgSend(v21, "objectForKeyedSubscript:", CFSTR("forceReq"));
    v23 = (__CFString *)objc_retainAutoreleasedReturnValue(v22);
    v24 = v23;
    v25 = &charsToLeaveEscaped;
    if ( v23 )
      v25 = v23;
    v63 = objc_retain(v25);
    v27 = _objc_msgSend(v26, "objectForKeyedSubscript:", CFSTR("wuDangDoubleClick"));
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v64 = v7;
    v62 = v28;
    if ( v28 )
      v70 = _objc_msgSend(v28, "length") == 0LL;
    else
      v70 = 1;
    v29 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31 = -[HXTabbarController selectedIndex](v30, "selectedIndex");
    v32 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
    v33 = objc_retainAutoreleasedReturnValue(v32);
    v34 = -[ZiXunMenuViewController menuBtns](v33, "menuBtns");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v36 = _objc_msgSend(v35, "count");
    v38(v33);
    if ( v36 < v31 )
      goto LABEL_12;
    v39 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v41 = -[ZiXunMenuViewController menuBtns](v40, "menuBtns");
    v42 = objc_retainAutoreleasedReturnValue(v41);
    v43 = _objc_msgSend(v42, "objectAtIndexedSubscript:", v31);
    v44 = objc_retainAutoreleasedReturnValue(v43);
    v46 = _objc_msgSend(v44, "title");
    v47 = objc_retainAutoreleasedReturnValue(v46);
    _objc_msgSend(v47, "isEqualToString:", CFSTR("快捷交易"));
    LOBYTE(v47) = v48 == 0;
    if ( (unsigned __int8)v47 | v70 )
    {
LABEL_12:
      v49 = +[WidgetParamSettingManager shareInstance](&OBJC_CLASS___WidgetParamSettingManager, "shareInstance");
      v50 = objc_retainAutoreleasedReturnValue(v49);
      v51 = _objc_msgSend(v50, "panKouContainer");
      v52 = objc_retainAutoreleasedReturnValue(v51);
      v53 = _objc_msgSend(v52, "getTargetWidgetTradeVC");
      v54 = objc_retainAutoreleasedReturnValue(v53);
      v56 = v54;
      v57 = _objc_msgSend(v67, "length");
      if ( v54 && !v57 && (unsigned __int8)_objc_msgSend(v54, "viewIsDisplaying") )
      {
        v58 = v66;
        v59 = v65;
        v60 = v64;
        -[ZiXunModuleViewController openWidgetJY:market:price:maimaiType:](
          self,
          "openWidgetJY:market:price:maimaiType:",
          v66,
          v65,
          v64,
          v68);
      }
      else
      {
        v58 = v66;
        v59 = v65;
        v60 = v64;
        -[ZiXunModuleViewController openQuickJY:market:price:forceReq:selectIndex:](
          self,
          "openQuickJY:market:price:forceReq:selectIndex:",
          v66,
          v65,
          v64,
          v63,
          v67,
          v62);
      }
    }
    else
    {
      v59 = v65;
      v60 = v64;
      -[ZiXunModuleViewController openWidgetJY:market:price:maimaiType:](
        self,
        "openWidgetJY:market:price:maimaiType:",
        v66,
        v65,
        v64,
        v68);
      _objc_msgSend(v61, "openQuickJY:market:price:forceReq:selectIndex:", v66, v65, v64, v63, v67, v62);
      v58 = v66;
    }
  }
}

//----- (0000000100BB8F5B) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController openWidgetJY:market:price:maimaiType:](
        ZiXunModuleViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        int a6)
{

  v18 = objc_retain(a3);
  v19 = objc_retain(a4);
  v8 = objc_retain(a5);
  v9 = +[WidgetParamSettingManager shareInstance](&OBJC_CLASS___WidgetParamSettingManager, "shareInstance");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "panKouContainer");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v12, "getTargetWidgetTradeVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v15(v10);
  if ( v14 && (unsigned __int8)_objc_msgSend(v14, "viewIsDisplaying") )
  {
    _objc_msgSend(v14, "expandWidgetJY");
    v16(v14, "setStockCode:market:buySell:", v18, v19, (unsigned int)a6);
    v17(v14, "setJiaGeTFValueWithStrPrice:", v8);
  }
}

//----- (0000000100BB9085) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController openQuickJY:market:price:forceReq:selectIndex:](
        ZiXunModuleViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        id a6,
        id a7)
{
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  signed __int8 v16; // bl
  JYInQuotaQuickTradeViewController *v20; // rax
  JYInQuotaQuickTradeViewController *v21; // r14
  dispatch_time_t v24; // r14
  _QWORD block[5]; // [rsp+0h] [rbp-80h] BYREF

  v33 = a3;
  v34 = self;
  v30 = objc_retain(a7);
  v10 = objc_retain(v9);
  v31 = objc_retain(a5);
  v32 = objc_retain(a4);
  v11 = objc_retain(v33);
  -[ZiXunModuleViewController openQuickTradeVC](self, "openQuickTradeVC");
  v13 = v12(self, "quickTradeVC");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = (unsigned __int8)v15(v10, "boolValue");
  _objc_msgSend(v14, "setStockCode:market:forceReq:", v11, v32, (unsigned int)v16);
  v17(v32);
  v18(v11);
  v19(v14);
  v20 = -[ZiXunModuleViewController quickTradeVC](self, "quickTradeVC");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  -[JYInQuotaQuickTradeViewController setJiaGeTFValueWithStrPrice:](v21, "setJiaGeTFValueWithStrPrice:", v31);
  v22(v31);
  v23(v21);
  v24 = dispatch_time(0LL, 500000000LL);
  block[0] = _NSConcreteStackBlock;
  block[1] = 3254779904LL;
  block[2] = sub_100BB9218;
  block[3] = &unk_1012DAAE0;
  block[4] = self;
  v29 = v30;
  v25 = objc_retain(v30);
  dispatch_after(v24, &_dispatch_main_q, block);
  v26(v29);
  v27(v25);
}

//----- (0000000100BB9218) ----------------------------------------------------
void __fastcall sub_100BB9218(__int64 a1)
{

  v1 = _objc_msgSend(*(id *)(a1 + 32), "quickTradeVC");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v3 = _objc_msgSend(*(id *)(a1 + 40), "integerValue");
  _objc_msgSend(v2, "setPageViewSelectedViewWithIndex:", v3);
}

//----- (0000000100BB9278) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController ziXunAreaState:](ZiXunModuleViewController *self, SEL a2, char a3)
{
  -[ZiXunModuleViewController setZiXunOpen:](self, "setZiXunOpen:", a3 == 0);
}

//----- (0000000100BB9293) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController switchTradeQueueModuleMode:](ZiXunModuleViewController *self, SEL a2, char a3)
{
  TradingQueueContainerController *v4; // rax
  TradingQueueContainerController *v5; // rbx

  if ( (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account") )
  {
    if ( *(_QWORD *)&self->_hasSetDefaultTabbarCtrlIndex )
    {
      v4 = -[ZiXunModuleViewController tradingQueueVC](self, "tradingQueueVC");
      v5 = objc_retainAutoreleasedReturnValue(v4);
      -[TradingQueueContainerController switchTradeQueueMode:](v5, "switchTradeQueueMode:", (unsigned int)a3);
    }
  }
}

//----- (0000000100BB9310) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController displayDataAtIndex:withStamp:param:](
        ZiXunModuleViewController *self,
        SEL a2,
        signed __int64 a3,
        signed __int64 a4,
        id a5)
{
  TradingQueueContainerController *v8; // rax
  TradingQueueContainerController *v9; // rbx

  objc_retain(a5);
  if ( (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account")
    && *(_QWORD *)&self->_hasSetDefaultTabbarCtrlIndex )
  {
    v8 = -[ZiXunModuleViewController tradingQueueVC](self, "tradingQueueVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    -[TradingQueueContainerController displayDataAtIndex:withStamp:param:](
      v9,
      "displayDataAtIndex:withStamp:param:",
      a3,
      a4,
      v10);
  }
}

//----- (0000000100BB93AA) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController hideZiXunContentView:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12

  v4 = (unsigned __int8)-[ZiXunModuleViewController ziXunOpen](self, "ziXunOpen");
  v5(self, "setZiXunOpen:", v4 == 0);
  v6(self, "notifyUpperLevelControllerToChangeFrame");
  v8 = (unsigned __int8)v7(self, "ziXunOpen");
  if ( a3 )
  {
    if ( v8 )
      -[ZiXunModuleViewController refreshContentViews](self, "refreshContentViews");
  }
}

//----- (0000000100BB9423) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController ziXunMenuBtnClicked:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  HXTipManager *v10; // rax
  HXTipManager *v11; // r14
  NSString *v18; // rax
  NSString *v19; // r14
  NSString *v21; // rax
  NSString *v22; // r14
  NSString *v23; // rax
  NSString *v24; // r15
  NSString *v28; // rax
  ZiXunMenuViewController *v30; // rax
  ZiXunMenuViewController *v31; // rbx
  ZiXunMenuViewController *v32; // rax
  ZiXunMenuViewController *v33; // r15
  NSArray *v34; // rax
  NSArray *v35; // rbx
  unsigned __int64 v36; // r12
  HXTabbarController *v37; // rax
  HXTabbarController *v38; // rbx
  NSArray *v39; // rax
  NSArray *v40; // r15
  unsigned __int64 v41; // r12
  HXTabbarController *v42; // rax
  HXTabbarController *v43; // rbx
  unsigned __int64 v45; // [rsp+10h] [rbp-50h]
  NSString *v47; // [rsp+28h] [rbp-38h]

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___HXButton, "class");
  if ( !(unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
    goto LABEL_18;
  v6 = objc_retain(v5);
  v7 = _objc_msgSend(&OBJC_CLASS___HXCornerButton, "class");
  if ( (unsigned __int8)_objc_msgSend(v6, "isKindOfClass:", v7) )
  {
    v8 = _objc_msgSend(v6, "cornerText");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( v9 )
    {
      _objc_msgSend(v6, "setCornerText:", 0LL);
      v10 = +[HXTipManager sharedInstance](&OBJC_CLASS___HXTipManager, "sharedInstance");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = _objc_msgSend(v6, "title");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      -[HXTipManager setTipShowedWithId:](v11, "setTipShowedWithId:", v13);
    }
  }
  v14 = _objc_msgSend(v6, "title");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = (unsigned __int8)_objc_msgSend(v15, "isEqualToString:", CFSTR("挂单撤单"));
  v44 = v17;
  if ( v16 )
  {
    v18 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@_level2"), v15);
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v47 = v19;
  }
  else
  {
    v47 = (NSString *)v15;
  }
  v20 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v46 = objc_retainAutoreleasedReturnValue(v20);
  v21 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  if ( !v22 )
    goto LABEL_11;
  v23 = -[ZiXunModuleViewController market](self, "market");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  if ( v24 )
  {
    v26 = _objc_msgSend(self, v25);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    _objc_msgSend(v46, "setObject:forKey:", v27, CFSTR("StockCodeKey"));
    v28 = -[ZiXunModuleViewController market](self, "market");
    v22 = objc_retainAutoreleasedReturnValue(v28);
    _objc_msgSend(v29, "setObject:forKey:", v22, CFSTR("MarketKey"));
LABEL_11:
  }
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    v47,
    v46,
    1LL);
  v30 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  -[ZiXunMenuViewController resetSelectedBtn:](v31, "resetSelectedBtn:", v6);
  if ( !(unsigned __int8)-[ZiXunModuleViewController ziXunOpen](self, "ziXunOpen") )
    -[ZiXunModuleViewController hideZiXunContentView:](self, "hideZiXunContentView:", 0LL);
  v32 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v34 = -[ZiXunMenuViewController menuBtns](v33, "menuBtns");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  _objc_msgSend(v35, "indexOfObject:", v6);
  v45 = v36;
  if ( v36 != 0x7FFFFFFFFFFFFFFFLL )
  {
    v37 = (HXTabbarController *)-[ZiXunModuleViewController tabbarController](self, "tabbarController");
    v38 = objc_retainAutoreleasedReturnValue(v37);
    v39 = (NSArray *)-[HXTabbarController viewControllers](v38, "viewControllers");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    _objc_msgSend(v40, "count");
    if ( v45 <= v41 )
    {
      v42 = (HXTabbarController *)-[ZiXunModuleViewController tabbarController](self, "tabbarController");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      -[HXTabbarController setSelectedIndex:](v43, "setSelectedIndex:", v45);
      -[ZiXunModuleViewController refreshContentViews](self, "refreshContentViews");
    }
  }
  v5 = v44;
LABEL_18:
}

//----- (0000000100BB985E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController updateZiXunMenuBtnState](ZiXunModuleViewController *self, SEL a2)
{
  ZiXunMenuViewController *v2; // rax
  ZiXunMenuViewController *v3; // r14
  signed __int8 v4; // al

  v2 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)-[ZiXunModuleViewController ziXunOpen](self, "ziXunOpen");
  -[ZiXunMenuViewController resetHideCtrlBtn:](v3, "resetHideCtrlBtn:", (unsigned int)v4);
}

//----- (0000000100BB98B7) ----------------------------------------------------
char __cdecl -[ZiXunModuleViewController isL2Account](ZiXunModuleViewController *self, SEL a2)
{

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)_objc_msgSend(v3, "isLevel2");
  return v4;
}

//----- (0000000100BB9903) ----------------------------------------------------
HXTabbarController *__cdecl -[ZiXunModuleViewController tabbarController](ZiXunModuleViewController *self, SEL a2)
{
  NSString *designNibBundleIdentifier; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi
  id (*v7)(id, SEL, ...); // r12

  designNibBundleIdentifier = self->super._designNibBundleIdentifier;
  if ( !designNibBundleIdentifier )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTabbarController);
    v5 = (NSString *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("HXTabbarController"), 0LL);
    v6 = self->super._designNibBundleIdentifier;
    self->super._designNibBundleIdentifier = v5;
    v8 = v7(self, "ziXunContentView");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(self->super._designNibBundleIdentifier, "setView:", v9);
    designNibBundleIdentifier = self->super._designNibBundleIdentifier;
  }
  return (HXTabbarController *)objc_retainAutoreleaseReturnValue(designNibBundleIdentifier);
}

//----- (0000000100BB99A5) ----------------------------------------------------
ZiXunMenuViewController *__cdecl -[ZiXunModuleViewController ziXunMenuVC](ZiXunModuleViewController *self, SEL a2)
{
  id privateData; // rdi
  const char *WeakRetained; // rax

  privateData = self->super.__privateData;
  if ( !privateData )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunMenuViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZiXunMenuViewController"), 0LL);
    v6 = self->super.__privateData;
    self->super.__privateData = v5;
    v7 = _objc_msgSend(self->super.__privateData, "view");
    objc_retainAutoreleasedReturnValue(v7);
    WeakRetained = (const char *)objc_loadWeakRetained((id *)&self->super._nibName);
    v10 = (char *)WeakRetained;
    if ( WeakRetained )
      objc_msgSend_stret(v17, WeakRetained, "bounds");
    else
      memset(v17, 0, 32);
    _objc_msgSend(v9, "setFrame:");
    v12 = objc_loadWeakRetained((id *)&self->super._nibName);
    v13 = _objc_msgSend(self->super.__privateData, "view");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v12, "addSubview:", v14);
    privateData = *(Class *)((char *)&self->super.super.super.isa + v15);
  }
  return (ZiXunMenuViewController *)objc_retainAutoreleaseReturnValue(privateData);
}

//----- (0000000100BB9AF3) ----------------------------------------------------
ZiXunTableViewController *__cdecl -[ZiXunModuleViewController ziXunTableVC](ZiXunModuleViewController *self, SEL a2)
{
  objc_class *v5; // rax
  const char *WeakRetained; // rax

  v3 = *(void **)&self->super._viewIsAppearing;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunTableViewController);
    v5 = (objc_class *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZiXunTableViewController"), 0LL);
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    v9 = _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v8), "view");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    WeakRetained = (const char *)objc_loadWeakRetained((id *)&self->super._nibBundle);
    v12 = (char *)WeakRetained;
    if ( WeakRetained )
      objc_msgSend_stret(v16, WeakRetained, "bounds");
    else
      memset(v16, 0, 32);
    _objc_msgSend(v10, "setFrame:");
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v13), "setZiXunOpenURLBlock:", self->super._editors);
    v3 = *(Class *)((char *)&self->super.super.super.isa + v14);
  }
  return (ZiXunTableViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100BB9C06) ----------------------------------------------------
GuanLianBaoJiaoViewController *__cdecl -[ZiXunModuleViewController guanLianBaoJiaVC](
        ZiXunModuleViewController *self,
        SEL a2)
{

  v3 = *(void **)&self->super._isContentViewController;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuanLianBaoJiaoViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("GuanLianBaoJiaoViewController"), 0LL);
    v6 = *(void **)&self->super._isContentViewController;
    *(_QWORD *)&self->super._isContentViewController = v5;
    v3 = *(void **)&self->super._isContentViewController;
  }
  return (GuanLianBaoJiaoViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100BB9C60) ----------------------------------------------------
JYInQuotaQuickTradeViewController *__cdecl -[ZiXunModuleViewController quickTradeVC](
        ZiXunModuleViewController *self,
        SEL a2)
{

  v3 = *(void **)&self->super._reserved;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___JYInQuotaQuickTradeViewController);
    v5 = _objc_msgSend(v4, "init");
    v6 = *(void **)&self->super._reserved;
    *(_QWORD *)&self->super._reserved = v5;
    _objc_msgSend(*(id *)&self->super._reserved, "setDelegate:", self);
    v3 = *(void **)&self->super._reserved;
  }
  return (JYInQuotaQuickTradeViewController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100BB9CCF) ----------------------------------------------------
TradingQueueContainerController *__cdecl -[ZiXunModuleViewController tradingQueueVC](
        ZiXunModuleViewController *self,
        SEL a2)
{

  v3 = *(void **)&self->_hasSetDefaultTabbarCtrlIndex;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___TradingQueueContainerController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("TradingQueueContainerController"), 0LL);
    v6 = *(void **)&self->_hasSetDefaultTabbarCtrlIndex;
    *(_QWORD *)&self->_hasSetDefaultTabbarCtrlIndex = v5;
    v3 = *(void **)&self->_hasSetDefaultTabbarCtrlIndex;
  }
  return (TradingQueueContainerController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (0000000100BB9D29) ----------------------------------------------------
QuanShangXiWeiViewController *__cdecl -[ZiXunModuleViewController quanShangXiWeiVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  HXBaseView *ziXunMenuView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi

  ziXunMenuView = self->_ziXunMenuView;
  if ( !ziXunMenuView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___QuanShangXiWeiViewController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_ziXunMenuView;
    self->_ziXunMenuView = v5;
    ziXunMenuView = self->_ziXunMenuView;
  }
  return (QuanShangXiWeiViewController *)objc_retainAutoreleaseReturnValue(ziXunMenuView);
}

//----- (0000000100BB9D7A) ----------------------------------------------------
GuaDanCheDanContainerController *__cdecl -[ZiXunModuleViewController guaDanCheDanVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  HXBaseView *ziXunContentView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi

  ziXunContentView = self->_ziXunContentView;
  if ( !ziXunContentView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuaDanCheDanContainerController);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_ziXunContentView;
    self->_ziXunContentView = v5;
    ziXunContentView = self->_ziXunContentView;
  }
  return (GuaDanCheDanContainerController *)objc_retainAutoreleaseReturnValue(ziXunContentView);
}

//----- (0000000100BB9DCB) ----------------------------------------------------
DaDanLengJingContainerController *__cdecl -[ZiXunModuleViewController daDanLengJingVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSString *stockCode; // rdi
  objc_class *v5; // rax
  _QWORD v11[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  stockCode = self->_stockCode;
  if ( !stockCode )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___DaDanLengJingContainerController);
    v5 = (objc_class *)_objc_msgSend(v4, "init");
    v7 = *(Class *)((char *)&self->super.super.super.isa + v6);
    *(Class *)((char *)&self->super.super.super.isa + v6) = v5;
    objc_initWeak(location, self);
    v11[0] = _NSConcreteStackBlock;
    v11[1] = 3254779904LL;
    v11[2] = sub_100BB9EBF;
    v11[3] = &unk_1012DD620;
    objc_copyWeak(&to, location);
    _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v8), "setSelectTableRowDidChangeBlock:", v11);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    stockCode = *(NSString **)((char *)&self->super.super.super.isa + v9);
  }
  return (DaDanLengJingContainerController *)objc_retainAutoreleaseReturnValue(stockCode);
}

//----- (0000000100BB9EBF) ----------------------------------------------------
void __fastcall sub_100BB9EBF(__int64 a1, void *a2)
{
  id *v2; // rbx
  id WeakRetained; // r15
  SEL v7; // r12

  v18 = objc_retain(a2);
  v2 = (id *)(a1 + 32);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v4 = _objc_msgSend(WeakRetained, "delegate");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( !v5 )
    goto LABEL_4;
  v6 = v5;
  v17 = objc_loadWeakRetained(v2);
  v8 = _objc_msgSend(v17, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v19 = v10(v9, "respondsToSelector:", "daDanTableViewSelectRowDidChange:");
  v11(v17);
  v12(v6);
  v13(WeakRetained);
  if ( v19 )
  {
    WeakRetained = objc_loadWeakRetained(v2);
    v14 = _objc_msgSend(WeakRetained, "delegate");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16(v15, "daDanTableViewSelectRowDidChange:", v18);
LABEL_4:
  }
}

//----- (0000000100BB9FE2) ----------------------------------------------------
GuanLianShangPinViewController *__cdecl -[ZiXunModuleViewController guanLianShangPinVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSString *market; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  market = self->_market;
  if ( !market )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuanLianShangPinViewController);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->_market;
    self->_market = v5;
    market = self->_market;
  }
  return (GuanLianShangPinViewController *)objc_retainAutoreleaseReturnValue(market);
}

//----- (0000000100BBA033) ----------------------------------------------------
RelatedZiXunTableViewController *__cdecl -[ZiXunModuleViewController relatedZiXunTableVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSArray *menuTitles; // rdi
  NSArray *v5; // rax
  NSArray *v6; // rdi

  menuTitles = self->_menuTitles;
  if ( !menuTitles )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___RelatedZiXunTableViewController);
    v5 = (NSArray *)_objc_msgSend(v4, "init");
    v6 = self->_menuTitles;
    self->_menuTitles = v5;
    _objc_msgSend(self->_menuTitles, "setZiXunOpenURLBlock:", self->super._editors);
    menuTitles = self->_menuTitles;
  }
  return (RelatedZiXunTableViewController *)objc_retainAutoreleaseReturnValue(menuTitles);
}

//----- (0000000100BBA0AA) ----------------------------------------------------
XiangGuanWoLunNiuXiongContainerController *__cdecl -[ZiXunModuleViewController woLunNiuXiongCC](
        ZiXunModuleViewController *self,
        SEL a2)
{

  zixunType = (void *)self->_zixunType;
  if ( !zixunType )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___XiangGuanWoLunNiuXiongContainerController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("XiangGuanWoLunNiuXiongContainerController"), 0LL);
    v6 = (void *)self->_zixunType;
    self->_zixunType = (unsigned __int64)v5;
    zixunType = (void *)self->_zixunType;
  }
  return (XiangGuanWoLunNiuXiongContainerController *)objc_retainAutoreleaseReturnValue(zixunType);
}

//----- (0000000100BBA104) ----------------------------------------------------
GuanLianQiZhiViewController *__cdecl -[ZiXunModuleViewController guanLianQiZhiVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  id ziXunOpenURLBlock; // rdi

  ziXunOpenURLBlock = self->_ziXunOpenURLBlock;
  if ( !ziXunOpenURLBlock )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuanLianQiZhiViewController);
    v5 = _objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("GuanLianQiZhiViewController"), 0LL);
    v6 = self->_ziXunOpenURLBlock;
    self->_ziXunOpenURLBlock = v5;
    ziXunOpenURLBlock = self->_ziXunOpenURLBlock;
  }
  return (GuanLianQiZhiViewController *)objc_retainAutoreleaseReturnValue(ziXunOpenURLBlock);
}

//----- (0000000100BBA15E) ----------------------------------------------------
QiHuoGuanLianAGuViewController *__cdecl -[ZiXunModuleViewController qhGuanLianAGuTVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  ZiXunModuleViewDidChangeDelegate *delegate; // rdi
  ZiXunModuleViewDidChangeDelegate *v5; // rax
  ZiXunModuleViewDidChangeDelegate *v6; // rdi

  delegate = self->_delegate;
  if ( !delegate )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___QiHuoGuanLianAGuViewController);
    v5 = (ZiXunModuleViewDidChangeDelegate *)_objc_msgSend(v4, "initWithStockCodeInTableOrNot:", 1LL);
    v6 = self->_delegate;
    self->_delegate = v5;
    delegate = self->_delegate;
  }
  return (QiHuoGuanLianAGuViewController *)objc_retainAutoreleaseReturnValue(delegate);
}

//----- (0000000100BBA1B4) ----------------------------------------------------
GuanLianQiHuoViewController *__cdecl -[ZiXunModuleViewController guanLianQiHuoVC](
        ZiXunModuleViewController *self,
        SEL a2)
{
  HXTabbarController *tabbarController; // rdi
  HXTabbarController *v5; // rax
  HXTabbarController *v6; // rdi

  tabbarController = self->_tabbarController;
  if ( !tabbarController )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___GuanLianQiHuoViewController);
    v5 = (HXTabbarController *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("GuanLianQiHuoViewController"), 0LL);
    v6 = self->_tabbarController;
    self->_tabbarController = v5;
    tabbarController = self->_tabbarController;
  }
  return (GuanLianQiHuoViewController *)objc_retainAutoreleaseReturnValue(tabbarController);
}

//----- (0000000100BBA20E) ----------------------------------------------------
StockNotesViewController *__cdecl -[ZiXunModuleViewController stockNotesVC](ZiXunModuleViewController *self, SEL a2)
{
  ZiXunMenuViewController *ziXunMenuVC; // rdi
  ZiXunMenuViewController *v5; // rax
  ZiXunMenuViewController *v6; // rdi

  ziXunMenuVC = self->_ziXunMenuVC;
  if ( !ziXunMenuVC )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___StockNotesViewController);
    v5 = (ZiXunMenuViewController *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("StockNotesViewController"), 0LL);
    v6 = self->_ziXunMenuVC;
    self->_ziXunMenuVC = v5;
    ziXunMenuVC = self->_ziXunMenuVC;
  }
  return (StockNotesViewController *)objc_retainAutoreleaseReturnValue(ziXunMenuVC);
}

//----- (0000000100BBA268) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunOpen:](ZiXunModuleViewController *self, SEL a2, char a3)
{
  id (*v4)(id, SEL, ...); // r12

  BYTE1(self->super.super._nextResponder) = a3;
  -[ZiXunModuleViewController updateZiXunMenuBtnState](self, "updateZiXunMenuBtnState");
  v5 = v4(self->super._designNibBundleIdentifier, "view");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setHidden:", a3 == 0);
}

//----- (0000000100BBA2E0) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setMenuTitles:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  NSView *v3; // rax
  NSView *v4; // rbx
  NSView *view; // r15
  ZiXunMenuViewController *v6; // rax
  ZiXunMenuViewController *v7; // r13
  ZiXunMenuViewController *v9; // rax
  ZiXunMenuViewController *v10; // r15

  v3 = (NSView *)objc_retain(a3);
  v4 = v3;
  if ( v3 )
  {
    if ( _objc_msgSend(v3, "count") )
    {
      view = self->super.view;
      if ( view != v4 )
      {
        self->super.view = objc_retain(v4);
        v6 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
        v7 = objc_retainAutoreleasedReturnValue(v6);
        -[ZiXunMenuViewController createMenuBtns:](v7, "createMenuBtns:", v8);
        v9 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
        v10 = objc_retainAutoreleasedReturnValue(v9);
        -[ZiXunMenuViewController setDefaultSelectedMenuBtn](v10, "setDefaultSelectedMenuBtn");
        v11(v10);
        -[ZiXunModuleViewController addActionEventForBtns](self, "addActionEventForBtns");
      }
    }
  }
}

//----- (0000000100BBA3DA) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZixunType:](ZiXunModuleViewController *self, SEL a2, unsigned __int64 a3)
{
  ZiXunMenuViewController *v4; // rax
  ZiXunMenuViewController *v5; // rbx

  if ( self->super._topLevelObjects != (NSArray *)a3 )
  {
    self->super._topLevelObjects = (NSArray *)a3;
    -[ZiXunModuleViewController setTabbarViewControllers](self, "setTabbarViewControllers");
    -[ZiXunModuleViewController setDefaultTabbarControllerIndex](self, "setDefaultTabbarControllerIndex");
    v4 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    -[ZiXunMenuViewController setZixunType:](v5, "setZixunType:", a3);
  }
}

//----- (0000000100BBA45F) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunOpenURLBlock:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  NSPointerArray *v5; // rax
  NSPointerArray *editors; // rdi

  v4 = objc_retain(a3);
  v5 = (NSPointerArray *)objc_retainBlock(v4);
  editors = self->super._editors;
  self->super._editors = v5;
  _objc_msgSend(*(id *)&self->super._viewIsAppearing, "setZiXunOpenURLBlock:", v4);
  v7(self->_menuTitles, "setZiXunOpenURLBlock:", v4);
}

//----- (0000000100BBA4E9) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setStockCode:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  id (*v7)(id, SEL, ...); // r12

  v4 = objc_retain(a3);
  if ( !(unsigned __int8)_objc_msgSend(self->super._representedObject, "isEqualToString:", v4) )
    objc_storeStrong((id *)((char *)&self->super.super.super.isa + v5), a3);
  _objc_msgSend(*(id *)&self->super._reserved, "setDelegate:", self);
  v6 = *(void **)&self->super._reserved;
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v6, "setStockCode:market:forceReq:", v4, v9, 0LL);
}

//----- (0000000100BBA5A1) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setTabbarViewControllers](ZiXunModuleViewController *self, SEL a2)
{
  _QWORD *v3; // r12
  NSArray *v10; // rax
  NSArray *v11; // r13
  NSArray *v22; // rax
  NSArray *v36; // rax
  NSArray *v49; // rax
  NSArray *v64; // rax
  NSArray *v90; // rax
  NSArray *v102; // rax
  NSArray *v111; // rax
  NSArray *v126; // rax
  NSArray *v135; // rax
  NSArray *v145; // rax
  NSArray *v152; // rax
  NSArray *v169; // rax
  NSArray *v173; // rax
  NSArray *v179; // rax
  NSArray *v191; // [rsp+28h] [rbp-258h]
  _QWORD v205[4]; // [rsp+48h] [rbp-238h] BYREF
  _QWORD v206[2]; // [rsp+68h] [rbp-218h] BYREF
  _QWORD v210[4]; // [rsp+C8h] [rbp-1B8h] BYREF
  _QWORD v212[2]; // [rsp+100h] [rbp-180h] BYREF
  _QWORD v215[2]; // [rsp+148h] [rbp-138h] BYREF
  _QWORD v216[4]; // [rsp+158h] [rbp-128h] BYREF
  _QWORD v217[5]; // [rsp+178h] [rbp-108h] BYREF
  _QWORD v218[3]; // [rsp+1A0h] [rbp-E0h] BYREF
  _QWORD v219[4]; // [rsp+1B8h] [rbp-C8h] BYREF
  _QWORD v220[6]; // [rsp+1D8h] [rbp-A8h] BYREF
  _QWORD v221[9]; // [rsp+208h] [rbp-78h] BYREF

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  switch ( v3[7] )
  {
    case 2LL:
    case 0x11LL:
      if ( v2 )
      {
        v13 = _objc_msgSend(v3, "tradingQueueVC");
        v192 = objc_retainAutoreleasedReturnValue(v13);
        v219[0] = v192;
        v15 = _objc_msgSend(v14, "ziXunTableVC");
        v197 = objc_retainAutoreleasedReturnValue(v15);
        v219[1] = v197;
        v17 = _objc_msgSend(v16, "quickTradeVC");
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v219[2] = v18;
        v20 = _objc_msgSend(v19, "stockNotesVC");
        v21 = objc_retainAutoreleasedReturnValue(v20);
        v219[3] = v21;
        v22 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v219, 4LL);
        v11 = objc_retainAutoreleasedReturnValue(v22);
        v23 = v21;
        v24 = v192;
      }
      else
      {
        v147 = _objc_msgSend(v3, "ziXunTableVC");
        v24 = objc_retainAutoreleasedReturnValue(v147);
        v218[0] = v24;
        v149 = _objc_msgSend(v148, "quickTradeVC");
        v197 = objc_retainAutoreleasedReturnValue(v149);
        v218[1] = v197;
        v151 = _objc_msgSend(v150, "stockNotesVC");
        v18 = objc_retainAutoreleasedReturnValue(v151);
        v218[2] = v18;
        v152 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v218, 3LL);
        v11 = objc_retainAutoreleasedReturnValue(v152);
      }
      v153 = v24;
      goto LABEL_32;
    case 3LL:
      v39 = _objc_msgSend(v3, "ziXunTableVC");
      v198 = objc_retainAutoreleasedReturnValue(v39);
      v210[0] = v198;
      v41 = _objc_msgSend(v40, "guanLianBaoJiaVC");
      v193 = objc_retainAutoreleasedReturnValue(v41);
      v210[1] = v193;
      v43 = _objc_msgSend(v42, "stockNotesVC");
      v44 = objc_retainAutoreleasedReturnValue(v43);
      v210[2] = v44;
      v46 = _objc_msgSend(v45, "guanLianQiZhiVC");
      v47 = objc_retainAutoreleasedReturnValue(v46);
      v7 = v48;
      v210[3] = v47;
      v49 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v210, 4LL);
      v11 = objc_retainAutoreleasedReturnValue(v49);
      v38 = v44;
      goto LABEL_7;
    case 4LL:
      v54 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
      v55 = objc_retainAutoreleasedReturnValue(v54);
      v56 = (unsigned __int8)_objc_msgSend(v55, "isHongKongLevel2");
      v187 = v57;
      if ( v56 )
      {
        v58 = _objc_msgSend(v57, "quanShangXiWeiVC");
        v194 = objc_retainAutoreleasedReturnValue(v58);
        v59 = (__int64 *)&v214;
        v214 = v194;
        v61 = _objc_msgSend(v60, "ziXunTableVC");
        goto LABEL_22;
      }
      v154 = _objc_msgSend(v57, "ziXunTableVC");
      v155 = objc_retainAutoreleasedReturnValue(v154);
      v128 = v155;
      v157 = &v213;
      goto LABEL_35;
    case 5LL:
    case 0xALL:
    case 0xBLL:
    case 0xCLL:
    case 0xFLL:
    case 0x10LL:
    case 0x13LL:
    case 0x14LL:
    case 0x15LL:
    case 0x18LL:
      v4 = _objc_msgSend(v3, "ziXunTableVC");
      v5 = objc_retainAutoreleasedReturnValue(v4);
      v7 = v6;
      v212[0] = v5;
      v8 = _objc_msgSend(v6, "stockNotesVC");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v212[1] = v9;
      v10 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v212, 2LL);
      v11 = objc_retainAutoreleasedReturnValue(v10);
      goto LABEL_8;
    case 6LL:
    case 7LL:
      v25 = _objc_msgSend(v3, "ziXunTableVC");
      v198 = objc_retainAutoreleasedReturnValue(v25);
      v217[0] = v198;
      v27 = _objc_msgSend(v26, "ziXunTableVC");
      v193 = objc_retainAutoreleasedReturnValue(v27);
      v217[1] = v193;
      v29 = _objc_msgSend(v28, "ziXunTableVC");
      v189 = objc_retainAutoreleasedReturnValue(v29);
      v217[2] = v189;
      v31 = _objc_msgSend(v30, "ziXunTableVC");
      v32 = objc_retainAutoreleasedReturnValue(v31);
      v7 = v33;
      v217[3] = v32;
      v34 = _objc_msgSend(v33, "stockNotesVC");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v217[4] = v35;
      v36 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v217, 5LL);
      v11 = objc_retainAutoreleasedReturnValue(v36);
      v38 = v189;
LABEL_7:
LABEL_8:
      v51 = v7;
      goto LABEL_9;
    case 9LL:
      v67 = _objc_msgSend(v3, "ziXunTableVC");
      v199 = objc_retainAutoreleasedReturnValue(v67);
      v68 = (__int64 *)&v211;
      v211 = v199;
      v70 = v69;
      v71 = "ziXunTableVC";
      goto LABEL_19;
    case 0xDLL:
      if ( v2 )
      {
        v72 = _objc_msgSend(v3, "tradingQueueVC");
        v183 = objc_retainAutoreleasedReturnValue(v72);
        v221[0] = v183;
        v74 = _objc_msgSend(v73, "guaDanCheDanVC");
        v200 = objc_retainAutoreleasedReturnValue(v74);
        v221[1] = v200;
        v76 = _objc_msgSend(v75, "daDanLengJingVC");
        v195 = objc_retainAutoreleasedReturnValue(v76);
        v221[2] = v195;
        v78 = _objc_msgSend(v77, "ziXunTableVC");
        v190 = objc_retainAutoreleasedReturnValue(v78);
        v221[3] = v190;
        v80 = _objc_msgSend(v79, "guanLianBaoJiaVC");
        v185 = objc_retainAutoreleasedReturnValue(v80);
        v221[4] = v185;
        v82 = _objc_msgSend(v81, "guanLianQiHuoVC");
        v186 = objc_retainAutoreleasedReturnValue(v82);
        v221[5] = v186;
        v84 = _objc_msgSend(v83, "quickTradeVC");
        v184 = objc_retainAutoreleasedReturnValue(v84);
        v221[6] = v184;
        v86 = _objc_msgSend(v85, "guanLianShangPinVC");
        v87 = objc_retainAutoreleasedReturnValue(v86);
        v221[7] = v87;
        v89 = _objc_msgSend(v88, "stockNotesVC");
        v221[8] = objc_retainAutoreleasedReturnValue(v89);
        v90 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v221, 9LL);
        v11 = objc_retainAutoreleasedReturnValue(v90);
        v91 = v183;
      }
      else
      {
        v158 = _objc_msgSend(v3, "ziXunTableVC");
        v91 = objc_retainAutoreleasedReturnValue(v158);
        v220[0] = v91;
        v160 = _objc_msgSend(v159, "guanLianBaoJiaVC");
        v200 = objc_retainAutoreleasedReturnValue(v160);
        v220[1] = v200;
        v162 = _objc_msgSend(v161, "guanLianQiHuoVC");
        v195 = objc_retainAutoreleasedReturnValue(v162);
        v220[2] = v195;
        v164 = _objc_msgSend(v163, "quickTradeVC");
        v190 = objc_retainAutoreleasedReturnValue(v164);
        v220[3] = v190;
        v166 = _objc_msgSend(v165, "guanLianShangPinVC");
        v185 = objc_retainAutoreleasedReturnValue(v166);
        v220[4] = v185;
        v168 = _objc_msgSend(v167, "stockNotesVC");
        v186 = objc_retainAutoreleasedReturnValue(v168);
        v220[5] = v186;
        v169 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v220, 6LL);
        v11 = objc_retainAutoreleasedReturnValue(v169);
      }
      v153 = v91;
      goto LABEL_32;
    case 0xELL:
      if ( v2 )
      {
        v93 = _objc_msgSend(v3, "tradingQueueVC");
        v201 = objc_retainAutoreleasedReturnValue(v93);
        v216[0] = v201;
        v95 = _objc_msgSend(v94, "guaDanCheDanVC");
        v96 = objc_retainAutoreleasedReturnValue(v95);
        v216[1] = v96;
        v98 = _objc_msgSend(v97, "ziXunTableVC");
        v99 = objc_retainAutoreleasedReturnValue(v98);
        v216[2] = v99;
        v101 = _objc_msgSend(v100, "stockNotesVC");
        v216[3] = objc_retainAutoreleasedReturnValue(v101);
        v102 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v216, 4LL);
        v11 = objc_retainAutoreleasedReturnValue(v102);
      }
      else
      {
        v170 = _objc_msgSend(v3, "ziXunTableVC");
        v201 = objc_retainAutoreleasedReturnValue(v170);
        v215[0] = v201;
        v172 = _objc_msgSend(v171, "stockNotesVC");
        v96 = objc_retainAutoreleasedReturnValue(v172);
        v215[1] = v96;
        v173 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v215, 2LL);
        v11 = objc_retainAutoreleasedReturnValue(v173);
      }
      v153 = v201;
LABEL_32:
      v66 = _objc_msgSend(v174, "tabbarController");
      goto LABEL_33;
    case 0x12LL:
      v104 = _objc_msgSend(v3, "ziXunTableVC");
      v199 = objc_retainAutoreleasedReturnValue(v104);
      v68 = (__int64 *)&v207;
      v207 = v199;
      v71 = "qhGuanLianAGuTVC";
      v70 = v105;
LABEL_19:
      v106 = _objc_msgSend(v70, v71);
      v107 = objc_retainAutoreleasedReturnValue(v106);
      v68[1] = (__int64)v107;
      v109 = _objc_msgSend(v108, "stockNotesVC");
      v110 = objc_retainAutoreleasedReturnValue(v109);
      v68[2] = (__int64)v110;
      v111 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v68, 3LL);
      v11 = objc_retainAutoreleasedReturnValue(v111);
      v113 = _objc_msgSend(v112, "tabbarController");
      v53 = objc_retainAutoreleasedReturnValue(v113);
      _objc_msgSend(v53, "setViewControllers:", v11);
      goto LABEL_37;
    case 0x16LL:
      v114 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
      v115 = objc_retainAutoreleasedReturnValue(v114);
      v116 = (unsigned __int8)_objc_msgSend(v115, "isHongKongLevel2");
      v187 = v117;
      if ( v116 )
      {
        v118 = _objc_msgSend(v117, "quanShangXiWeiVC");
        v194 = objc_retainAutoreleasedReturnValue(v118);
        v59 = (__int64 *)&v209;
        v209 = v194;
        v61 = _objc_msgSend(v119, "relatedZiXunTableVC");
LABEL_22:
        v202 = objc_retainAutoreleasedReturnValue(v61);
        v59[1] = (__int64)v202;
        v121 = _objc_msgSend(v120, "woLunNiuXiongCC");
        v122 = objc_retainAutoreleasedReturnValue(v121);
        v59[2] = (__int64)v122;
        v124 = _objc_msgSend(v123, "stockNotesVC");
        v125 = objc_retainAutoreleasedReturnValue(v124);
        v59[3] = (__int64)v125;
        v126 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v59, 4LL);
        v11 = objc_retainAutoreleasedReturnValue(v126);
        v127 = v125;
        v128 = v194;
      }
      else
      {
        v175 = _objc_msgSend(v117, "relatedZiXunTableVC");
        v155 = objc_retainAutoreleasedReturnValue(v175);
        v128 = v155;
        v157 = &v208;
LABEL_35:
        *(_QWORD *)v157 = v155;
        v176 = _objc_msgSend(v156, "woLunNiuXiongCC");
        v202 = objc_retainAutoreleasedReturnValue(v176);
        *((_QWORD *)v157 + 1) = v202;
        v178 = _objc_msgSend(v177, "stockNotesVC");
        v122 = objc_retainAutoreleasedReturnValue(v178);
        *((_QWORD *)v157 + 2) = v122;
        v179 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v157, 3LL);
        v11 = objc_retainAutoreleasedReturnValue(v179);
      }
      v181 = (void *)v180(v187, "tabbarController");
      v53 = objc_retainAutoreleasedReturnValue(v181);
      v182(v53, "setViewControllers:", v11);
LABEL_37:
      return;
    case 0x17LL:
      v129 = _objc_msgSend(v3, "ziXunTableVC");
      v130 = objc_retainAutoreleasedReturnValue(v129);
      v206[0] = v130;
      v132 = _objc_msgSend(v131, "stockNotesVC");
      v133 = objc_retainAutoreleasedReturnValue(v132);
      v188 = v134;
      v206[1] = v133;
      v135 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v206, 2LL);
      v11 = objc_retainAutoreleasedReturnValue(v135);
      if ( v2 )
      {
        v137 = _objc_msgSend(v188, "tradingQueueVC");
        v203 = objc_retainAutoreleasedReturnValue(v137);
        v205[0] = v203;
        v139 = (void *)v138(v188, "guaDanCheDanVC");
        v196 = objc_retainAutoreleasedReturnValue(v139);
        v205[1] = v196;
        v141 = (void *)v140(v188, "ziXunTableVC");
        v142 = objc_retainAutoreleasedReturnValue(v141);
        v205[2] = v142;
        v144 = (void *)v143(v188, "stockNotesVC");
        v205[3] = objc_retainAutoreleasedReturnValue(v144);
        v145 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v205, 4LL);
        v191 = objc_retainAutoreleasedReturnValue(v145);
        v11 = v191;
      }
      v51 = v188;
LABEL_9:
      v52 = _objc_msgSend(v51, "tabbarController");
      v53 = objc_retainAutoreleasedReturnValue(v52);
      _objc_msgSend(v53, "setViewControllers:", v11);
      goto LABEL_37;
    default:
      v62 = _objc_msgSend(v3, "ziXunTableVC");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v204 = v63;
      v64 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v204, 1LL);
      v11 = objc_retainAutoreleasedReturnValue(v64);
      v66 = _objc_msgSend(v65, "tabbarController");
LABEL_33:
      v53 = objc_retainAutoreleasedReturnValue(v66);
      _objc_msgSend(v53, "setViewControllers:", v11);
      goto LABEL_37;
  }
}

//----- (0000000100BBB408) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setDefaultTabbarControllerIndex](ZiXunModuleViewController *self, SEL a2)
{

  if ( !(unsigned __int8)-[ZiXunModuleViewController hasSetDefaultTabbarCtrlIndex](self, "hasSetDefaultTabbarCtrlIndex") )
  {
    v3 = _objc_msgSend(v2, "tabbarController");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = _objc_msgSend(v4, "viewControllers");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(v6, "count");
    if ( v7 )
    {
      v9 = _objc_msgSend(v8, "tabbarController");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      _objc_msgSend(v10, "setSelectedIndex:", 0LL);
      _objc_msgSend(v11, "setHasSetDefaultTabbarCtrlIndex:", 1LL);
    }
  }
}

//----- (0000000100BBB4F1) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController addActionEventForBtns](ZiXunModuleViewController *self, SEL a2)
{
  ZiXunMenuViewController *v2; // rax
  ZiXunMenuViewController *v3; // r15
  id (*v4)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  SEL v33; // [rsp+48h] [rbp-D8h]
  ZiXunModuleViewController *v36; // [rsp+60h] [rbp-C0h]
  id obj; // [rsp+68h] [rbp-B8h]

  v2 = -[ZiXunModuleViewController ziXunMenuVC](self, "ziXunMenuVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "hideCtrlBtn");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setTarget:", self);
  v9 = v8(self, "ziXunMenuVC");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(v10, "hideCtrlBtn");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(v13, "setAction:", "hideZiXunContentView:");
  v32 = 0LL;
  v31 = 0LL;
  v30 = 0LL;
  v29 = 0LL;
  v36 = self;
  v16 = v15(self, "ziXunMenuVC");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = v18(v17, "menuBtns");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  obj = v20;
  v22 = v21(v20, "countByEnumeratingWithState:objects:count:", &v29, v38, 16LL);
  if ( v22 )
  {
    v23 = (__int64)v22;
    v34 = *(_QWORD *)v30;
LABEL_3:
    v33 = "class";
    v35 = "ziXunMenuBtnClicked:";
    if ( !v23 )
      v23 = 1LL;
    v24 = 0LL;
    while ( 1 )
    {
      if ( *(_QWORD *)v30 != v34 )
        objc_enumerationMutation(obj);
      v25 = *(void **)(*((_QWORD *)&v29 + 1) + 8 * v24);
      v26 = _objc_msgSend(&OBJC_CLASS___HXButton, v33);
      if ( !(unsigned __int8)_objc_msgSend(v25, "isKindOfClass:", v26) )
        break;
      v27 = objc_retain(v25);
      _objc_msgSend(v27, "setTarget:", v36);
      _objc_msgSend(v27, "setAction:", v35);
      v24 = v28 + 1;
      if ( v23 == v24 )
      {
        v23 = (__int64)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v29, v38, 16LL);
        if ( v23 )
          goto LABEL_3;
        break;
      }
    }
  }
}

//----- (0000000100BBB79B) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController notifyUpperLevelControllerToChangeFrame](
        ZiXunModuleViewController *self,
        SEL a2)
{
  ZiXunModuleViewDidChangeDelegate *v2; // rax
  ZiXunModuleViewDidChangeDelegate *v3; // rbx

  v2 = -[ZiXunModuleViewController delegate](self, "delegate");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = (unsigned __int8)_objc_msgSend(v3, "respondsToSelector:", "changeZiXunModuleState");
  if ( v4 )
  {
    v6 = _objc_msgSend(v5, "delegate");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    _objc_msgSend(v7, "changeZiXunModuleState");
  }
}

//----- (0000000100BBB837) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructDefaultZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v4; // rax
  NSNumber *v5; // rbx
  NSDictionary *v7; // rax
  NSDictionary *v8; // r15
  NSArray *v9; // rax
  NSArray *v10; // r13
  SEL v11; // r12
  NSDictionary *v15; // [rsp+10h] [rbp-60h] BYREF
  NSArray *v16; // [rsp+18h] [rbp-58h] BYREF
  _QWORD v17[4]; // [rsp+20h] [rbp-50h] BYREF

  v17[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v17[2] = v3;
  v17[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v17, 2LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v15 = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v15, 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v16 = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSArray, v11, &v16, 1LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(self, "setReqParamArr:", v13);
}

//----- (0000000100BBB9A0) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructHuShenOptionZiXunRequestParam](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r15
  NSNumber *v4; // rax
  NSNumber *v5; // r14
  NSDictionary *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  NSDictionary *v11; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r13
  NSDictionary *v32; // [rsp+8h] [rbp-C8h]
  NSDictionary *v33; // [rsp+10h] [rbp-C0h]
  _QWORD v35[2]; // [rsp+20h] [rbp-B0h] BYREF
  _QWORD v36[2]; // [rsp+30h] [rbp-A0h] BYREF
  _QWORD v37[2]; // [rsp+40h] [rbp-90h] BYREF
  _QWORD v38[2]; // [rsp+50h] [rbp-80h] BYREF
  _QWORD v39[2]; // [rsp+60h] [rbp-70h] BYREF
  _QWORD v40[2]; // [rsp+70h] [rbp-60h] BYREF
  _QWORD v41[4]; // [rsp+80h] [rbp-50h] BYREF

  v41[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v41[2] = v3;
  v41[1] = CFSTR("RequestType");
  v4 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v41, 2LL);
  v32 = objc_retainAutoreleasedReturnValue(v7);
  v39[0] = CFSTR("ZiXunType");
  v8 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 949LL);
  v40[0] = objc_retainAutoreleasedReturnValue(v8);
  v39[1] = CFSTR("RequestType");
  v9 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v40[1] = v10;
  v11 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v39, 2LL);
  v33 = objc_retainAutoreleasedReturnValue(v11);
  v37[0] = CFSTR("ZiXunType");
  v13 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v38[0] = v14;
  v37[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v38[1] = v17;
  v19 = (void *)v18(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v38, v37, 2LL);
  v21 = v20;
  objc_retainAutoreleasedReturnValue(v19);
  v35[0] = v32;
  v35[1] = v33;
  v22 = v21;
  v23 = (void *)v21(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v35, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v36[0] = v24;
  v34 = v25;
  v26 = (void *)v22(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v34, 1LL);
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v36[1] = v27;
  v28 = (void *)v22(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v36, 2LL);
  v29 = (void (__fastcall *)(ZiXunModuleViewController *, const char *, id))v22;
  v30 = objc_retainAutoreleasedReturnValue(v28);
  v29(self, "setReqParamArr:", v30);
}

//----- (0000000100BBBCD6) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructNormalZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r15
  NSNumber *v4; // rax
  NSNumber *v5; // r14
  NSDictionary *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  NSDictionary *v11; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r13
  NSDictionary *v32; // [rsp+8h] [rbp-C8h]
  NSDictionary *v33; // [rsp+10h] [rbp-C0h]
  _QWORD v34[2]; // [rsp+18h] [rbp-B8h] BYREF
  NSDictionary *v35; // [rsp+28h] [rbp-A8h] BYREF
  _QWORD v36[2]; // [rsp+30h] [rbp-A0h] BYREF
  _QWORD v37[2]; // [rsp+40h] [rbp-90h] BYREF
  _QWORD v38[2]; // [rsp+50h] [rbp-80h] BYREF
  _QWORD v39[2]; // [rsp+60h] [rbp-70h] BYREF
  _QWORD v40[2]; // [rsp+70h] [rbp-60h] BYREF
  _QWORD v41[4]; // [rsp+80h] [rbp-50h] BYREF

  v41[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v41[2] = v3;
  v41[1] = CFSTR("RequestType");
  v4 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v41, 2LL);
  v32 = objc_retainAutoreleasedReturnValue(v7);
  v39[0] = CFSTR("ZiXunType");
  v8 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v40[0] = objc_retainAutoreleasedReturnValue(v8);
  v39[1] = CFSTR("RequestType");
  v9 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v40[1] = v10;
  v11 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v39, 2LL);
  v33 = objc_retainAutoreleasedReturnValue(v11);
  v37[0] = CFSTR("ZiXunType");
  v13 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14342LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v38[0] = v14;
  v37[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v38[1] = v17;
  v19 = (void *)v18(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v38, v37, 2LL);
  v21 = v20;
  objc_retainAutoreleasedReturnValue(v19);
  v35 = v32;
  v22 = v21;
  v23 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, NSDictionary **, __int64))v21)(
                  &OBJC_CLASS___NSArray,
                  "arrayWithObjects:count:",
                  &v35,
                  1LL);
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v36[0] = v24;
  v34[0] = v33;
  v34[1] = v25;
  v26 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, __int64))v22)(
                  &OBJC_CLASS___NSArray,
                  "arrayWithObjects:count:",
                  v34,
                  2LL);
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v36[1] = v27;
  v28 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, __int64))v22)(
                  &OBJC_CLASS___NSArray,
                  "arrayWithObjects:count:",
                  v36,
                  2LL);
  v29 = v22;
  v30 = objc_retainAutoreleasedReturnValue(v28);
  v29(self, "setReqParamArr:", v30);
}

//----- (0000000100BBC00C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructWoLunNiuXiongZiXunRequestParam](
        ZiXunModuleViewController *self,
        SEL a2)
{
  RelatedZiXunTableViewController *v2; // rax
  RelatedZiXunTableViewController *v3; // r14
  NSString *v4; // rax
  NSString *v5; // r15
  NSString *v6; // rax
  NSString *v7; // r13
  id *v9; // r12
  _QWORD v10[5]; // [rsp+0h] [rbp-60h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  objc_initWeak(location, self);
  v2 = -[ZiXunModuleViewController relatedZiXunTableVC](self, "relatedZiXunTableVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[ZiXunModuleViewController market](self, "market");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v10[0] = _NSConcreteStackBlock;
  v10[1] = 3254779904LL;
  v10[2] = sub_100BBC131;
  v10[3] = &unk_1012DBAD8;
  objc_copyWeak(&to, location);
  *(_QWORD *)(v8 - 8) = self;
  -[RelatedZiXunTableViewController getWoLunRelateHKMarket:stockCode:call:](
    v3,
    "getWoLunRelateHKMarket:stockCode:call:",
    v5,
    v7,
    v10);
  objc_destroyWeak(v9);
  objc_destroyWeak(location);
}

//----- (0000000100BBC131) ----------------------------------------------------
id __fastcall sub_100BBC131(__int64 a1, __int64 a2)
{
  id WeakRetained; // rax

  v2 = +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  v4 = WeakRetained;
  if ( v2 == (id)4 )
    _objc_msgSend(WeakRetained, "constructIndexZiXunRequestParam:indexType:", 0LL, 6LL);
  else
    _objc_msgSend(WeakRetained, "constructNormalZiXunRequestParam");
  return _objc_msgSend(*(id *)(a1 + 32), "reloadRelatedZiXunTable");
}

//----- (0000000100BBC1B7) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructMeiGuZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r14
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSDictionary *v23; // [rsp+10h] [rbp-A0h]
  NSDictionary *v26; // [rsp+28h] [rbp-88h] BYREF
  _QWORD v27[2]; // [rsp+30h] [rbp-80h] BYREF
  __CFString *v28; // [rsp+40h] [rbp-70h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-60h] BYREF
  _QWORD v30[2]; // [rsp+60h] [rbp-50h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-40h] BYREF

  v30[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v31[0] = objc_retainAutoreleasedReturnValue(v2);
  v30[1] = CFSTR("RequestType");
  v3 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v31[1] = v4;
  v5 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v31, v30, 2LL);
  v23 = objc_retainAutoreleasedReturnValue(v5);
  v28 = CFSTR("ZiXunType");
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v29[0] = v8;
  *(_QWORD *)(v9 + 8) = CFSTR("RequestType");
  v10 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v29[1] = v11;
  v13 = (void *)v12(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, &v28, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v13);
  v26 = v23;
  v15 = v14;
  v16 = (void *)v14(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)&v26, 1LL);
  v27[0] = objc_retainAutoreleasedReturnValue(v16);
  v25 = v24;
  v17 = (void *)v15(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v25, 1LL);
  v18 = v15;
  v19 = objc_retainAutoreleasedReturnValue(v17);
  v27[1] = v19;
  v20 = (void *)v18(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)v27, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  ((void (__fastcall *)(ZiXunModuleViewController *, const char *, id))v18)(self, "setReqParamArr:", v21);
}

//----- (0000000100BBC42C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructGeGuXinWenZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v4; // rax
  NSNumber *v5; // rbx
  NSDictionary *v7; // rax
  NSDictionary *v8; // r15
  NSArray *v9; // rax
  NSArray *v10; // r13
  SEL v11; // r12
  NSDictionary *v15; // [rsp+10h] [rbp-60h] BYREF
  NSArray *v16; // [rsp+18h] [rbp-58h] BYREF
  _QWORD v17[4]; // [rsp+20h] [rbp-50h] BYREF

  v17[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v17[2] = v3;
  v17[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v17, 2LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v15 = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v15, 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v16 = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSArray, v11, &v16, 1LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(self, "setReqParamArr:", v13);
}

//----- (0000000100BBC598) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructHuShenJiJinZiXunRequestParam](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r15
  NSNumber *v4; // rax
  NSNumber *v5; // r14
  NSDictionary *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rax
  NSNumber *v10; // r13
  NSDictionary *v11; // rax
  NSNumber *v13; // rax
  NSNumber *v14; // r13
  NSDictionary *v32; // [rsp+8h] [rbp-C8h]
  NSDictionary *v33; // [rsp+10h] [rbp-C0h]
  _QWORD v35[2]; // [rsp+20h] [rbp-B0h] BYREF
  _QWORD v36[2]; // [rsp+30h] [rbp-A0h] BYREF
  _QWORD v37[2]; // [rsp+40h] [rbp-90h] BYREF
  _QWORD v38[2]; // [rsp+50h] [rbp-80h] BYREF
  _QWORD v39[2]; // [rsp+60h] [rbp-70h] BYREF
  _QWORD v40[2]; // [rsp+70h] [rbp-60h] BYREF
  _QWORD v41[4]; // [rsp+80h] [rbp-50h] BYREF

  v41[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v41[2] = v3;
  v41[1] = CFSTR("RequestType");
  v4 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v41, 2LL);
  v32 = objc_retainAutoreleasedReturnValue(v7);
  v39[0] = CFSTR("ZiXunType");
  v8 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 949LL);
  v40[0] = objc_retainAutoreleasedReturnValue(v8);
  v39[1] = CFSTR("RequestType");
  v9 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v40[1] = v10;
  v11 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v40, v39, 2LL);
  v33 = objc_retainAutoreleasedReturnValue(v11);
  v37[0] = CFSTR("ZiXunType");
  v13 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14341LL);
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v38[0] = v14;
  v37[1] = CFSTR("RequestType");
  v16 = (void *)v15(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v38[1] = v17;
  v19 = (void *)v18(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v38, v37, 2LL);
  v21 = v20;
  objc_retainAutoreleasedReturnValue(v19);
  v35[0] = v32;
  v35[1] = v33;
  v22 = v21;
  v23 = (void *)v21(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v35, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v36[0] = v24;
  v34 = v25;
  v26 = (void *)v22(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v34, 1LL);
  v27 = objc_retainAutoreleasedReturnValue(v26);
  v36[1] = v27;
  v28 = (void *)v22(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v36, 2LL);
  v29 = (void (__fastcall *)(ZiXunModuleViewController *, const char *, id))v22;
  v30 = objc_retainAutoreleasedReturnValue(v28);
  v29(self, "setReqParamArr:", v30);
}

//----- (0000000100BBC8CE) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructBlockZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r14
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSDictionary *v23; // [rsp+10h] [rbp-A0h]
  NSDictionary *v26; // [rsp+28h] [rbp-88h] BYREF
  _QWORD v27[2]; // [rsp+30h] [rbp-80h] BYREF
  __CFString *v28; // [rsp+40h] [rbp-70h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-60h] BYREF
  _QWORD v30[2]; // [rsp+60h] [rbp-50h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-40h] BYREF

  v30[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14339LL);
  v31[0] = objc_retainAutoreleasedReturnValue(v2);
  v30[1] = CFSTR("RequestType");
  v3 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v31[1] = v4;
  v5 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v31, v30, 2LL);
  v23 = objc_retainAutoreleasedReturnValue(v5);
  v28 = CFSTR("ZiXunType");
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14343LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v29[0] = v8;
  *(_QWORD *)(v9 + 8) = CFSTR("RequestType");
  v10 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v29[1] = v11;
  v13 = (void *)v12(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, &v28, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v13);
  v26 = v23;
  v15 = v14;
  v16 = (void *)v14(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)&v26, 1LL);
  v27[0] = objc_retainAutoreleasedReturnValue(v16);
  v25 = v24;
  v17 = (void *)v15(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v25, 1LL);
  v18 = v15;
  v19 = objc_retainAutoreleasedReturnValue(v17);
  v27[1] = v19;
  v20 = (void *)v18(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)v27, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  ((void (__fastcall *)(ZiXunModuleViewController *, const char *, id))v18)(self, "setReqParamArr:", v21);
}

//----- (0000000100BBCB43) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructHuShenIndexZiXunRequestParam](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSDictionary *v5; // rax
  NSNumber *v7; // rax
  NSNumber *v8; // r14
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSDictionary *v23; // [rsp+10h] [rbp-A0h]
  NSDictionary *v26; // [rsp+28h] [rbp-88h] BYREF
  _QWORD v27[2]; // [rsp+30h] [rbp-80h] BYREF
  __CFString *v28; // [rsp+40h] [rbp-70h] BYREF
  _QWORD v29[2]; // [rsp+50h] [rbp-60h] BYREF
  _QWORD v30[2]; // [rsp+60h] [rbp-50h] BYREF
  _QWORD v31[2]; // [rsp+70h] [rbp-40h] BYREF

  v30[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14355LL);
  v31[0] = objc_retainAutoreleasedReturnValue(v2);
  v30[1] = CFSTR("RequestType");
  v3 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v31[1] = v4;
  v5 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v31, v30, 2LL);
  v23 = objc_retainAutoreleasedReturnValue(v5);
  v28 = CFSTR("ZiXunType");
  v7 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14356LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v29[0] = v8;
  *(_QWORD *)(v9 + 8) = CFSTR("RequestType");
  v10 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v29[1] = v11;
  v13 = (void *)v12(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v29, &v28, 2LL);
  v24 = objc_retainAutoreleasedReturnValue(v13);
  v26 = v23;
  v15 = v14;
  v16 = (void *)v14(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)&v26, 1LL);
  v27[0] = objc_retainAutoreleasedReturnValue(v16);
  v25 = v24;
  v17 = (void *)v15(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v25, 1LL);
  v18 = v15;
  v19 = objc_retainAutoreleasedReturnValue(v17);
  v27[1] = v19;
  v20 = (void *)v18(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", (id *)v27, 2LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  ((void (__fastcall *)(ZiXunModuleViewController *, const char *, id))v18)(self, "setReqParamArr:", v21);
}

//----- (0000000100BBCDB8) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructGuZhiQiHuoZiXunRequestParam](ZiXunModuleViewController *self, SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v4; // rax
  NSNumber *v5; // rbx
  NSDictionary *v7; // rax
  NSDictionary *v8; // r15
  NSArray *v9; // rax
  NSArray *v10; // r13
  SEL v11; // r12
  NSDictionary *v15; // [rsp+10h] [rbp-60h] BYREF
  NSArray *v16; // [rsp+18h] [rbp-58h] BYREF
  _QWORD v17[4]; // [rsp+20h] [rbp-50h] BYREF

  v17[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14349LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v17[2] = v3;
  v17[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v17, 2LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v15 = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v15, 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v16 = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSArray, v11, &v16, 1LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(self, "setReqParamArr:", v13);
}

//----- (0000000100BBCF24) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructGuoNeiQiHuoZiXunRequestParam](
        ZiXunModuleViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // r13
  NSNumber *v4; // rax
  NSNumber *v5; // rbx
  NSDictionary *v7; // rax
  NSDictionary *v8; // r15
  NSArray *v9; // rax
  NSArray *v10; // r13
  SEL v11; // r12
  NSDictionary *v15; // [rsp+10h] [rbp-60h] BYREF
  NSArray *v16; // [rsp+18h] [rbp-58h] BYREF
  _QWORD v17[4]; // [rsp+20h] [rbp-50h] BYREF

  v17[0] = CFSTR("ZiXunType");
  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14340LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v17[2] = v3;
  v17[1] = CFSTR("RequestType");
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  *(_QWORD *)(v6 + 8) = v5;
  v7 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v6, v17, 2LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v15 = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v15, 1LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v16 = v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSArray, v11, &v16, 1LL);
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14(self, "setReqParamArr:", v13);
}

//----- (0000000100BBD090) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructWaiHuiZiXunRequestParam:](
        ZiXunModuleViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSNumber *v5; // rax
  NSDictionary *v6; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // r13
  NSNumber *v10; // rax
  NSNumber *v11; // rbx
  NSDictionary *v13; // rax
  ZiXunTableViewController *v14; // r14
  NSArray *v15; // rax
  SEL v16; // r12
  SEL v19; // r12
  ZiXunTableViewController *v23; // rax
  ZiXunTableViewController *v24; // rbx
  NSNumber *v25; // rax
  NSNumber *v26; // rax
  NSNumber *v27; // rbx
  NSDictionary *v28; // rax
  NSDictionary *v29; // r14
  NSArray *v31; // rax
  NSArray *v32; // r15
  NSArray *v33; // rax
  NSArray *v34; // rbx
  ZiXunTableViewController *v35; // rax
  NSDictionary *v37; // [rsp+8h] [rbp-D8h]
  NSArray *v38; // [rsp+10h] [rbp-D0h]
  NSDictionary *v39; // [rsp+20h] [rbp-C0h] BYREF
  NSArray *v40; // [rsp+28h] [rbp-B8h] BYREF
  _QWORD v41[2]; // [rsp+30h] [rbp-B0h] BYREF
  _QWORD v42[2]; // [rsp+40h] [rbp-A0h] BYREF
  ZiXunTableViewController *v43; // [rsp+50h] [rbp-90h] BYREF
  NSDictionary *v44; // [rsp+58h] [rbp-88h] BYREF
  _QWORD v45[2]; // [rsp+60h] [rbp-80h] BYREF
  _QWORD v46[4]; // [rsp+70h] [rbp-70h] BYREF
  _QWORD v47[2]; // [rsp+90h] [rbp-50h] BYREF
  _QWORD v48[2]; // [rsp+A0h] [rbp-40h] BYREF

  if ( a3 == 1 )
  {
    v41[0] = CFSTR("ZiXunType");
    v25 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL);
    v42[0] = objc_retainAutoreleasedReturnValue(v25);
    v41[1] = CFSTR("RequestType");
    v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v42[1] = v27;
    v28 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v42,
                            v41,
                            2LL);
    v29 = objc_retainAutoreleasedReturnValue(v28);
    v39 = v29;
    v31 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v39, 1LL);
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v40 = v32;
    v33 = (NSArray *)_objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v40, 1LL);
    v34 = objc_retainAutoreleasedReturnValue(v33);
    -[ZiXunModuleViewController setReqParamArr:](self, "setReqParamArr:", v34);
    v35 = (ZiXunTableViewController *)-[ZiXunModuleViewController ziXunTableVC](self, "ziXunTableVC");
    v14 = objc_retainAutoreleasedReturnValue(v35);
    -[ZiXunTableViewController hideSecondView](v14, "hideSecondView");
  }
  else
  {
    if ( a3 )
      return;
    v47[0] = CFSTR("ZiXunType");
    v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2078LL);
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v48[0] = v4;
    v47[1] = CFSTR("RequestType");
    v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
    v48[1] = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v48, v47, 2LL);
    v37 = objc_retainAutoreleasedReturnValue(v6);
    v46[0] = CFSTR("ZiXunType");
    v8 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 2077LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v46[2] = v9;
    v46[1] = CFSTR("RequestType");
    v10 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
    v11 = objc_retainAutoreleasedReturnValue(v10);
    *(_QWORD *)(v12 + 8) = v11;
    v13 = (NSDictionary *)_objc_msgSend(
                            &OBJC_CLASS___NSDictionary,
                            "dictionaryWithObjects:forKeys:count:",
                            v12,
                            v46,
                            2LL);
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v44 = v37;
    v15 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v44, 1LL);
    v38 = objc_retainAutoreleasedReturnValue(v15);
    v45[0] = v38;
    v43 = v14;
    v17 = _objc_msgSend(&OBJC_CLASS___NSArray, v16, &v43, 1LL);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v45[1] = v18;
    v20 = _objc_msgSend(&OBJC_CLASS___NSArray, v19, v45, 2LL);
    v21 = objc_retainAutoreleasedReturnValue(v20);
    v22(self, "setReqParamArr:", v21);
    v23 = -[ZiXunModuleViewController ziXunTableVC](self, "ziXunTableVC");
    v24 = objc_retainAutoreleasedReturnValue(v23);
    -[ZiXunTableViewController showSecondView](v24, "showSecondView");
  }
}

//----- (0000000100BBD4AF) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController constructIndexZiXunRequestParam:indexType:](
        ZiXunModuleViewController *self,
        SEL a2,
        unsigned __int64 a3,
        unsigned __int64 a4)
{
  NSNumber *v4; // rax
  NSNumber *v5; // r13
  NSArray *v19; // rax
  NSArray *v21; // r13
  NSNumber *v24; // rax
  NSNumber *v25; // r13
  NSNumber *v26; // rax
  NSNumber *v27; // rbx
  NSDictionary *v29; // rax
  ZiXunTableViewController *v30; // r15
  NSArray *v32; // rax
  NSArray *v33; // rax
  NSArray *v35; // r13
  NSNumber *v37; // rax
  NSNumber *v38; // r13
  NSNumber *v39; // rax
  NSNumber *v40; // rbx
  NSDictionary *v42; // rax
  NSArray *v44; // rax
  NSNumber *v45; // rax
  NSNumber *v46; // r13
  NSNumber *v47; // rax
  NSNumber *v48; // rbx
  NSDictionary *v50; // rax
  NSArray *v52; // rax
  NSNumber *v58; // rax
  NSNumber *v59; // r13
  NSArray *v73; // rax
  NSArray *v74; // rax
  NSArray *v75; // rax
  NSArray *v76; // rbx
  ZiXunTableViewController *v78; // rax
  ZiXunModuleViewController *v81; // [rsp+8h] [rbp-188h]
  ZiXunTableViewController *v82; // [rsp+10h] [rbp-180h] BYREF
  _QWORD v84[4]; // [rsp+20h] [rbp-170h] BYREF
  ZiXunTableViewController *v85; // [rsp+40h] [rbp-150h] BYREF
  _QWORD v87[4]; // [rsp+50h] [rbp-140h] BYREF
  ZiXunTableViewController *v88; // [rsp+70h] [rbp-120h] BYREF
  _QWORD v90[4]; // [rsp+80h] [rbp-110h] BYREF
  NSArray *v93; // [rsp+B0h] [rbp-E0h] BYREF
  _QWORD v94[4]; // [rsp+C0h] [rbp-D0h] BYREF
  _QWORD v95[2]; // [rsp+E0h] [rbp-B0h] BYREF
  _QWORD v96[2]; // [rsp+F0h] [rbp-A0h] BYREF
  NSArray *v99; // [rsp+110h] [rbp-80h] BYREF
  _QWORD v100[4]; // [rsp+120h] [rbp-70h] BYREF
  _QWORD v101[2]; // [rsp+140h] [rbp-50h] BYREF
  _QWORD v102[2]; // [rsp+150h] [rbp-40h] BYREF

  switch ( a3 )
  {
    case 0uLL:
      if ( a4 == 6 )
      {
        v101[0] = CFSTR("ZiXunType");
        v58 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 7363LL);
        v59 = objc_retainAutoreleasedReturnValue(v58);
        v102[0] = v59;
        v101[1] = CFSTR("RequestType");
        v61 = (void *)v60(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
        v63 = v62;
        v102[1] = objc_retainAutoreleasedReturnValue(v61);
        v64 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v63)(
                        &OBJC_CLASS___NSDictionary,
                        "dictionaryWithObjects:forKeys:count:",
                        v102,
                        v101,
                        2LL);
        v80 = objc_retainAutoreleasedReturnValue(v64);
        v100[0] = CFSTR("ZiXunType");
        v66 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithInt:", 32770LL);
        v67 = objc_retainAutoreleasedReturnValue(v66);
        v100[2] = v67;
        v100[1] = CFSTR("RequestType");
        v68 = (void *)v63(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
        v69 = v63;
        v70 = objc_retainAutoreleasedReturnValue(v68);
        *(_QWORD *)(v71 + 8) = v70;
        v72 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64, _QWORD *, __int64))v69)(
                        &OBJC_CLASS___NSDictionary,
                        "dictionaryWithObjects:forKeys:count:",
                        v71,
                        v100,
                        2LL);
        v79 = objc_retainAutoreleasedReturnValue(v72);
        v98 = v80;
        v73 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v98, 1LL);
        v21 = objc_retainAutoreleasedReturnValue(v73);
        v22 = (__int64 *)&v99;
        v99 = v21;
        v23 = &v97;
      }
      else
      {
        if ( a4 != 7 )
        {
LABEL_11:
          v78 = -[ZiXunModuleViewController ziXunTableVC](self, "ziXunTableVC");
          v30 = objc_retainAutoreleasedReturnValue(v78);
          -[ZiXunTableViewController showSecondView](v30, "showSecondView");
          goto LABEL_12;
        }
        v95[0] = CFSTR("ZiXunType");
        v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 14369LL);
        v5 = objc_retainAutoreleasedReturnValue(v4);
        v96[0] = v5;
        v95[1] = CFSTR("RequestType");
        v7 = (void *)v6(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 1LL);
        v9 = v8;
        v96[1] = objc_retainAutoreleasedReturnValue(v7);
        v10 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v9)(
                        &OBJC_CLASS___NSDictionary,
                        "dictionaryWithObjects:forKeys:count:",
                        v96,
                        v95,
                        2LL);
        v80 = objc_retainAutoreleasedReturnValue(v10);
        v94[0] = CFSTR("ZiXunType");
        v12 = (void *)v9(&OBJC_CLASS___NSNumber, "numberWithInt:", 7364LL);
        v13 = objc_retainAutoreleasedReturnValue(v12);
        v94[2] = v13;
        v94[1] = CFSTR("RequestType");
        v14 = (void *)v9(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
        v15 = v9;
        v16 = objc_retainAutoreleasedReturnValue(v14);
        *(_QWORD *)(v17 + 8) = v16;
        v18 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64, _QWORD *, __int64))v15)(
                        &OBJC_CLASS___NSDictionary,
                        "dictionaryWithObjects:forKeys:count:",
                        v17,
                        v94,
                        2LL);
        v79 = objc_retainAutoreleasedReturnValue(v18);
        v92 = v80;
        v19 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v92, 1LL);
        v21 = objc_retainAutoreleasedReturnValue(v19);
        v22 = (__int64 *)&v93;
        v93 = v21;
        v23 = &v91;
      }
      *(_QWORD *)v23 = v20;
      v74 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v23, 1LL);
      v22[1] = (__int64)objc_retainAutoreleasedReturnValue(v74);
      v75 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v22, 2LL);
      v76 = objc_retainAutoreleasedReturnValue(v75);
      -[ZiXunModuleViewController setReqParamArr:](self, "setReqParamArr:", v76);
      goto LABEL_11;
    case 1uLL:
      v90[0] = CFSTR("ZiXunType");
      v81 = self;
      v24 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49155LL, a4);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v90[2] = v25;
      v90[1] = CFSTR("RequestType");
      v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
      v27 = objc_retainAutoreleasedReturnValue(v26);
      *(_QWORD *)(v28 + 8) = v27;
      v29 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v28, v90, 2LL);
      v30 = objc_retainAutoreleasedReturnValue(v29);
      v31(v25);
      v88 = v30;
      v32 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v88, 1LL);
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v35 = v33;
      v36 = &v89;
      goto LABEL_8;
    case 2uLL:
      v87[0] = CFSTR("ZiXunType");
      v81 = self;
      v37 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 49154LL, a4);
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v87[2] = v38;
      v87[1] = CFSTR("RequestType");
      v39 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
      v40 = objc_retainAutoreleasedReturnValue(v39);
      *(_QWORD *)(v41 + 8) = v40;
      v42 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v41, v87, 2LL);
      v30 = objc_retainAutoreleasedReturnValue(v42);
      v43(v38);
      v85 = v30;
      v44 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v85, 1LL);
      v33 = objc_retainAutoreleasedReturnValue(v44);
      v35 = v33;
      v36 = &v86;
      goto LABEL_8;
    case 3uLL:
      v84[0] = CFSTR("ZiXunType");
      v81 = self;
      v45 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6146LL, a4);
      v46 = objc_retainAutoreleasedReturnValue(v45);
      v84[2] = v46;
      v84[1] = CFSTR("RequestType");
      v47 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", 0LL);
      v48 = objc_retainAutoreleasedReturnValue(v47);
      *(_QWORD *)(v49 + 8) = v48;
      v50 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v49, v84, 2LL);
      v30 = objc_retainAutoreleasedReturnValue(v50);
      v51(v46);
      v82 = v30;
      v52 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v82, 1LL);
      v33 = objc_retainAutoreleasedReturnValue(v52);
      v35 = v33;
      v36 = &v83;
LABEL_8:
      *(_QWORD *)v36 = v33;
      v53 = _objc_msgSend(&OBJC_CLASS___NSArray, v34, v36, 1LL);
      v54 = objc_retainAutoreleasedReturnValue(v53);
      -[ZiXunModuleViewController setReqParamArr:](v81, "setReqParamArr:", v54);
      v56 = _objc_msgSend(v55, "ziXunTableVC");
      v57 = objc_retainAutoreleasedReturnValue(v56);
      _objc_msgSend(v57, "hideSecondView");
LABEL_12:
      return;
    default:
      return;
  }
}

//----- (0000000100BBDC90) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshDefaultZiXunModule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( !v4 )
  {
    v5(self, "constructDefaultZiXunRequestParam");
    v6(self, "reloadZiXunTable");
  }
}

//----- (0000000100BBDD08) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuShenAGuZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  v4 = v3(self, "tabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "selectedIndex");
  if ( v2 )
  {
    switch ( v8 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadTradingQueue](self, "reloadTradingQueue");
        break;
      case 1LL:
        -[ZiXunModuleViewController reloadGuaDanCheDan](self, "reloadGuaDanCheDan");
        break;
      case 2LL:
        -[ZiXunModuleViewController reloadDaDanLengJing](self, "reloadDaDanLengJing");
        break;
      case 3LL:
        goto LABEL_5;
      case 4LL:
        goto LABEL_6;
      case 5LL:
        goto LABEL_7;
      case 6LL:
        goto LABEL_8;
      case 7LL:
        goto LABEL_9;
      case 8LL:
        goto LABEL_10;
      default:
        return;
    }
  }
  else
  {
    switch ( v8 )
    {
      case 0LL:
LABEL_5:
        v7(self, "constructNormalZiXunRequestParam");
        v7(self, "reloadZiXunTable");
        break;
      case 1LL:
LABEL_6:
        -[ZiXunModuleViewController reloadGuanLianBaoJiaTable](self, "reloadGuanLianBaoJiaTable");
        break;
      case 2LL:
LABEL_7:
        -[ZiXunModuleViewController reloadGuanLianQiHuo](self, "reloadGuanLianQiHuo");
        break;
      case 3LL:
LABEL_8:
        -[ZiXunModuleViewController reloadQuickTradeTable](self, "reloadQuickTradeTable");
        break;
      case 4LL:
LABEL_9:
        -[ZiXunModuleViewController reloadGuanLianShangPin](self, "reloadGuanLianShangPin");
        break;
      case 5LL:
LABEL_10:
        -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
        break;
      default:
        return;
    }
  }
}

//----- (0000000100BBDE68) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuShenBGuZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  v4 = v3(self, "tabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "selectedIndex");
  if ( v2 )
  {
    switch ( v8 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadTradingQueue](self, "reloadTradingQueue");
        break;
      case 1LL:
        -[ZiXunModuleViewController reloadGuaDanCheDan](self, "reloadGuaDanCheDan");
        break;
      case 2LL:
        goto LABEL_6;
      case 3LL:
        goto LABEL_7;
      default:
        return;
    }
  }
  else if ( v8 == 1 )
  {
LABEL_7:
    -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
  }
  else if ( !v8 )
  {
LABEL_6:
    v7(self, "constructNormalZiXunRequestParam");
    v7(self, "reloadZiXunTable");
  }
}

//----- (0000000100BBDF60) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshWaiHuiZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  SEL *v10; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( (unsigned __int64)v4 >= 2 )
  {
    if ( v4 != (id)2 )
      return;
    v10 = &selRef_reloadStockNotes;
  }
  else
  {
    v6 = _objc_msgSend(v5, "tabbarController");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "selectedIndex");
    _objc_msgSend(v9, "constructWaiHuiZiXunRequestParam:", v8);
    v10 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(v5, *v10);
}

//----- (0000000100BBE024) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuShenJiJinZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  v4 = v3(self, "tabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "selectedIndex");
  if ( v2 )
  {
    switch ( v8 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadTradingQueue](self, "reloadTradingQueue");
        break;
      case 1LL:
        goto LABEL_7;
      case 2LL:
        goto LABEL_9;
      case 3LL:
        goto LABEL_8;
      default:
        return;
    }
  }
  else
  {
    switch ( v8 )
    {
      case 2LL:
LABEL_8:
        -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
        break;
      case 1LL:
LABEL_9:
        -[ZiXunModuleViewController reloadQuickTradeTable](self, "reloadQuickTradeTable");
        break;
      case 0LL:
LABEL_7:
        v7(self, "constructHuShenJiJinZiXunRequestParam");
        v7(self, "reloadZiXunTable");
        break;
    }
  }
}

//----- (0000000100BBE124) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshGuZhiQiHuoZiXunModule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  SEL *v11; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedIndex");
  if ( v5 == (id)1 )
  {
    v11 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v5 )
      return;
    v6(self, "constructGuZhiQiHuoZiXunRequestParam");
    v8 = v7(self, "ziXunTableVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "hideSecondView");
    v11 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v11);
}

//----- (0000000100BBE1DA) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshGuoNeiQiHuoZiXunModule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  SEL *v11; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedIndex");
  if ( v5 == (id)2 )
  {
    v11 = &selRef_reloadStockNotes;
  }
  else if ( v5 == (id)1 )
  {
    v11 = (SEL *)&selRef_reloadQiHuoGuanLianAGu;
  }
  else
  {
    if ( v5 )
      return;
    v6(self, "constructGuoNeiQiHuoZiXunRequestParam");
    v8 = v7(self, "ziXunTableVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "hideSecondView");
    v11 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v11);
}

//----- (0000000100BBE29F) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuShenOptionZiXunModule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( v4 == (id)1 )
  {
    -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
  }
  else if ( !v4 )
  {
    v6 = (void *)v5(self, "market");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = v8;
    v10 = ((unsigned __int8 (__fastcall *)(id, const char *, __CFString *))v8)(v7, "isEqualToString:", CFSTR("UIFB"));
    if ( (_BYTE)v10 )
    {
      v9(self, "constructHuShenIndexZiXunRequestParam");
      v9(self, "reloadZiXunTable");
    }
    else
    {
      v9(self, "constructHuShenOptionZiXunRequestParam");
      v11 = (void *)((__int64 (__fastcall *)(ZiXunModuleViewController *, const char *))v9)(self, "ziXunTableVC");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      ((void (__fastcall *)(id, const char *, __CFString *))v9)(v12, "setStockCode:", CFSTR("510050"));
      v14 = (void *)v13(self, "ziXunTableVC");
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16(v15, "setMarket:", CFSTR("USHJ"));
      v18 = (void *)v17(self, "ziXunTableVC");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v21 = (void *)v20(self, "reqParamArr");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23(v19, "requestForZiXun:", v22);
    }
  }
}

//----- (0000000100BBE460) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshCommodityOptionZiXunModule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  ZiXunTableViewController *v5; // rax
  ZiXunTableViewController *v6; // rbx
  NSString *v7; // rax
  NSString *v8; // r14
  NSString *v10; // rax
  NSString *v11; // rax
  NSString *v13; // rax
  NSString *v14; // rbx
  NSString *v16; // rax
  NSString *v17; // rbx
  NSDictionary *v19; // rax
  NSString *v20; // rax
  NSString *v21; // r14
  NSString *v22; // rax
  NSString *v23; // rax
  NSString *v26; // rax
  NSString *v28; // rax
  NSString *v29; // rbx
  ZiXunTableViewController *v33; // rax
  ZiXunTableViewController *v34; // rbx
  SEL v35; // r12
  SEL v38; // r12
  NSArray *v40; // rax
  NSArray *v41; // rbx
  NSString *v46; // [rsp+8h] [rbp-78h]
  NSString *v47; // [rsp+10h] [rbp-70h]
  _QWORD v49[3]; // [rsp+20h] [rbp-60h] BYREF
  _QWORD v50[3]; // [rsp+38h] [rbp-48h] BYREF

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( v4 == (id)1 )
  {
    -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
  }
  else if ( !v4 )
  {
    -[ZiXunModuleViewController constructGuoNeiQiHuoZiXunRequestParam](self, "constructGuoNeiQiHuoZiXunRequestParam");
    v5 = -[ZiXunModuleViewController ziXunTableVC](self, "ziXunTableVC");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[ZiXunTableViewController hideSecondView](v6, "hideSecondView");
    v7 = -[ZiXunModuleViewController stockCode](self, "stockCode");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
    if ( (unsigned __int8)_objc_msgSend(v8, "isKindOfClass:", v9) )
    {
      v10 = -[ZiXunModuleViewController stockCode](self, "stockCode");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      if ( _objc_msgSend(v11, "length") )
      {
        v13 = -[ZiXunModuleViewController market](self, "market");
        v14 = objc_retainAutoreleasedReturnValue(v13);
        v15 = _objc_msgSend(&OBJC_CLASS___NSString, "class");
        v47 = v14;
        if ( (unsigned __int8)_objc_msgSend(v14, "isKindOfClass:", v15) )
        {
          v16 = -[ZiXunModuleViewController market](self, "market");
          v17 = objc_retainAutoreleasedReturnValue(v16);
          v48 = _objc_msgSend(v17, "length");
          if ( v48 )
          {
            v49[0] = CFSTR("UCFT");
            v50[0] = CFSTR("UCFD");
            v49[1] = CFSTR("UCFL");
            v50[1] = CFSTR("UCFZ");
            v49[2] = CFSTR("UCFX");
            v50[2] = CFSTR("UCFS");
            v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v50, v49, 3LL);
            objc_retainAutoreleasedReturnValue(v19);
            v20 = -[ZiXunModuleViewController market](self, "market");
            v21 = objc_retainAutoreleasedReturnValue(v20);
            v22 = -[ZiXunModuleViewController stockCode](self, "stockCode");
            v23 = objc_retainAutoreleasedReturnValue(v22);
            v24 = +[HXTools getDecodeCodePrefixWithMarket:code:](
                    &OBJC_CLASS___HXTools,
                    "getDecodeCodePrefixWithMarket:code:",
                    v21,
                    v23);
            v25 = objc_retainAutoreleasedReturnValue(v24);
            v26 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@9999"), v25);
            v46 = objc_retainAutoreleasedReturnValue(v26);
            v28 = -[ZiXunModuleViewController market](self, "market");
            v29 = objc_retainAutoreleasedReturnValue(v28);
            v31 = _objc_msgSend(v30, "thsStringForKey:", v29);
            v32 = objc_retainAutoreleasedReturnValue(v31);
            if ( _objc_msgSend(v32, "length") )
            {
              if ( _objc_msgSend(v46, "length") )
              {
                v33 = -[ZiXunModuleViewController ziXunTableVC](self, "ziXunTableVC");
                v34 = objc_retainAutoreleasedReturnValue(v33);
                -[ZiXunTableViewController setStockCode:](v34, "setStockCode:", v46);
                v36 = _objc_msgSend(self, v35);
                v37 = objc_retainAutoreleasedReturnValue(v36);
                _objc_msgSend(v37, "setMarket:", v32);
                v39 = _objc_msgSend(self, v38);
                objc_retainAutoreleasedReturnValue(v39);
                v40 = -[ZiXunModuleViewController reqParamArr](self, "reqParamArr");
                v41 = objc_retainAutoreleasedReturnValue(v40);
                _objc_msgSend(v42, "requestForZiXun:", v41);
              }
            }
          }
        }
        else
        {
        }
      }
      else
      {
      }
    }
    else
    {
    }
  }
}

//----- (0000000100BBE936) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshGangGuZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (unsigned __int8)v4(v3, "isHongKongLevel2");
  v7 = v6(self, "tabbarController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "selectedIndex");
  if ( v5 )
  {
    switch ( v10 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadQuanShangXiWei](self, "reloadQuanShangXiWei");
        break;
      case 1LL:
        goto LABEL_7;
      case 2LL:
        goto LABEL_9;
      case 3LL:
        goto LABEL_8;
      default:
        return;
    }
  }
  else
  {
    switch ( v10 )
    {
      case 2LL:
LABEL_8:
        -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
        break;
      case 1LL:
LABEL_9:
        -[ZiXunModuleViewController reloadWoLunNiuXiongWithZhengGu](self, "reloadWoLunNiuXiongWithZhengGu");
        break;
      case 0LL:
LABEL_7:
        -[ZiXunModuleViewController constructNormalZiXunRequestParam](self, "constructNormalZiXunRequestParam");
        -[ZiXunModuleViewController reloadZiXunTable](self, "reloadZiXunTable");
        break;
    }
  }
}

//----- (0000000100BBEA64) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshWoLunNiuXiongZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  v2 = +[UserInfoManager shareInstance](&OBJC_CLASS___UserInfoManager, "shareInstance");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (unsigned __int8)v4(v3, "isHongKongLevel2");
  v7 = v6(self, "tabbarController");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9(v8, "selectedIndex");
  if ( v5 )
  {
    switch ( v10 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadQuanShangXiWei](self, "reloadQuanShangXiWei");
        break;
      case 1LL:
        goto LABEL_7;
      case 2LL:
        goto LABEL_9;
      case 3LL:
        goto LABEL_8;
      default:
        return;
    }
  }
  else
  {
    switch ( v10 )
    {
      case 2LL:
LABEL_8:
        -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
        break;
      case 1LL:
LABEL_9:
        -[ZiXunModuleViewController reloadRelatedWoLunNiuXiong](self, "reloadRelatedWoLunNiuXiong");
        break;
      case 0LL:
LABEL_7:
        -[ZiXunModuleViewController constructWoLunNiuXiongZiXunRequestParam](
          self,
          "constructWoLunNiuXiongZiXunRequestParam");
        break;
    }
  }
}

//----- (0000000100BBEB68) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHKIndexZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  SEL *v10; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( (unsigned __int64)v4 >= 4 )
  {
    if ( v4 != (id)4 )
      return;
    v10 = &selRef_reloadStockNotes;
  }
  else
  {
    v6 = _objc_msgSend(v5, "tabbarController");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "selectedIndex");
    _objc_msgSend(v9, "constructIndexZiXunRequestParam:indexType:", v8, 6LL);
    v10 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(v5, *v10);
}

//----- (0000000100BBEC31) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshMeiGuZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  SEL *v5; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( v4 == (id)1 )
  {
    v5 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v4 )
      return;
    -[ZiXunModuleViewController constructMeiGuZiXunRequestParam](self, "constructMeiGuZiXunRequestParam");
    v5 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v5);
}

//----- (0000000100BBECBF) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshUSIndexZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  SEL *v10; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( (unsigned __int64)v4 >= 4 )
  {
    if ( v4 != (id)4 )
      return;
    v10 = &selRef_reloadStockNotes;
  }
  else
  {
    v6 = _objc_msgSend(v5, "tabbarController");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "selectedIndex");
    _objc_msgSend(v9, "constructIndexZiXunRequestParam:indexType:", v8, 7LL);
    v10 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(v5, *v10);
}

//----- (0000000100BBED88) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshBlockDPFXZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  SEL *v11; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedIndex");
  if ( v5 == (id)1 )
  {
    v11 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v5 )
      return;
    v6(self, "constructGeGuXinWenZiXunRequestParam");
    v8 = v7(self, "ziXunTableVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "hideSecondView");
    v11 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v11);
}

//----- (0000000100BBEE3E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshBlockZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  SEL *v11; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedIndex");
  if ( v5 == (id)1 )
  {
    v11 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v5 )
      return;
    v6(self, "constructBlockZiXunRequestParam");
    v8 = v7(self, "ziXunTableVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "showSecondView");
    v11 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v11);
}

//----- (0000000100BBEEF4) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuShenIndexZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  v5 = &selRef_reloadGuanLianBaoJiaTable;
  switch ( (unsigned __int64)v4 )
  {
    case 0uLL:
      -[ZiXunModuleViewController constructHuShenIndexZiXunRequestParam](self, "constructHuShenIndexZiXunRequestParam");
      v5 = &selRef_reloadZiXunTable;
      goto LABEL_5;
    case 1uLL:
      goto LABEL_5;
    case 2uLL:
      v5 = (char **)&selRef_reloadStockNotes;
      goto LABEL_5;
    case 3uLL:
      v5 = &selRef_reloadGuanLianQiZhi;
LABEL_5:
      _objc_msgSend(self, *v5);
      break;
    default:
      return;
  }
}

//----- (0000000100BBEFB0) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshXinSanBanStockZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  SEL *v5; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[HXTabbarController selectedIndex](v3, "selectedIndex");
  if ( v4 == (id)1 )
  {
    v5 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v4 )
      return;
    -[ZiXunModuleViewController constructNormalZiXunRequestParam](self, "constructNormalZiXunRequestParam");
    v5 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v5);
}

//----- (0000000100BBF03E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshXinSanBanIndexZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  HXTabbarController *v2; // rax
  HXTabbarController *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  SEL *v11; // rax

  v2 = -[ZiXunModuleViewController tabbarController](self, "tabbarController");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "selectedIndex");
  if ( v5 == (id)1 )
  {
    v11 = &selRef_reloadStockNotes;
  }
  else
  {
    if ( v5 )
      return;
    v6(self, "constructGeGuXinWenZiXunRequestParam");
    v8 = v7(self, "ziXunTableVC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10(v9, "hideSecondView");
    v11 = (SEL *)&selRef_reloadZiXunTable;
  }
  _objc_msgSend(self, *v11);
}

//----- (0000000100BBF0F4) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshBondZiXunMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  v4 = v3(self, "tabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "selectedIndex");
  if ( v2 )
  {
    switch ( v8 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadTradingQueue](self, "reloadTradingQueue");
        break;
      case 1LL:
        goto LABEL_7;
      case 2LL:
        goto LABEL_9;
      case 3LL:
        goto LABEL_8;
      default:
        return;
    }
  }
  else
  {
    switch ( v8 )
    {
      case 2LL:
LABEL_8:
        -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
        break;
      case 1LL:
LABEL_9:
        -[ZiXunModuleViewController reloadQuickTradeTable](self, "reloadQuickTradeTable");
        break;
      case 0LL:
LABEL_7:
        v7(self, "constructMeiGuZiXunRequestParam");
        v7(self, "reloadZiXunTable");
        break;
    }
  }
}

//----- (0000000100BBF1F4) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController refreshHuLunTongCDRMoudule](ZiXunModuleViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12

  v2 = (unsigned __int8)-[ZiXunModuleViewController isL2Account](self, "isL2Account");
  v4 = v3(self, "tabbarController");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6;
  v6(v5, "selectedIndex");
  if ( v2 )
  {
    switch ( v8 )
    {
      case 0LL:
        -[ZiXunModuleViewController reloadTradingQueue](self, "reloadTradingQueue");
        break;
      case 1LL:
        -[ZiXunModuleViewController reloadGuaDanCheDan](self, "reloadGuaDanCheDan");
        break;
      case 2LL:
        goto LABEL_6;
      case 3LL:
        goto LABEL_7;
      default:
        return;
    }
  }
  else if ( v8 == 1 )
  {
LABEL_7:
    -[ZiXunModuleViewController reloadStockNotes](self, "reloadStockNotes");
  }
  else if ( !v8 )
  {
LABEL_6:
    v7(self, "constructNormalZiXunRequestParam");
    v7(self, "reloadZiXunTable");
  }
}

//----- (0000000100BBF2EC) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadQuanShangXiWei](ZiXunModuleViewController *self, SEL a2)
{
  QuanShangXiWeiViewController *v2; // rax
  QuanShangXiWeiViewController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController quanShangXiWeiVC](self, "quanShangXiWeiVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestForData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBF37C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadZiXunTable](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r15

  v2 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "ziXunTableVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setStockCode:", v3);
  v8 = _objc_msgSend(v7, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v10, "ziXunTableVC");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setMarket:", v9);
  v14 = _objc_msgSend(v13, "ziXunTableVC");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = _objc_msgSend(v16, "reqParamArr");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v15, "requestForZiXun:", v18);
}

//----- (0000000100BBF493) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadRelatedZiXunTable](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r15

  v2 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "relatedZiXunTableVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setStockCode:", v3);
  v8 = _objc_msgSend(v7, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v11 = _objc_msgSend(v10, "relatedZiXunTableVC");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setMarket:", v9);
  v14 = _objc_msgSend(v13, "relatedZiXunTableVC");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = _objc_msgSend(v16, "reqParamArr");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v15, "requestForZiXun:", v18);
}

//----- (0000000100BBF5AA) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadGuanLianBaoJiaTable](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx
  NSString *v4; // rax
  NSString *v5; // r14
  SEL v6; // r12
  NSString *v8; // rax
  NSString *v9; // rbx
  _BOOL8 v11; // rbx
  GuanLianBaoJiaoViewController *v12; // rax
  GuanLianBaoJiaoViewController *v13; // r14
  NSString *v15; // rax
  NSString *v16; // rax
  NSString *v17; // r14
  NSString *v19; // rax
  GuanLianBaoJiaoViewController *v21; // rax
  GuanLianBaoJiaoViewController *v22; // r14

  v2 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = -[ZiXunModuleViewController market](self, "market");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, v6);
    if ( !v7 )
      return;
    v8 = -[ZiXunModuleViewController market](self, "market");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", v9);
    v11 = v10 == (id)2;
    v12 = -[ZiXunModuleViewController guanLianBaoJiaVC](self, "guanLianBaoJiaVC");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    -[GuanLianBaoJiaoViewController setSuoShuBanKuaiMode:](v13, "setSuoShuBanKuaiMode:", 2 * v11);
    v14(v13);
    v15 = -[ZiXunModuleViewController market](self, "market");
    objc_retainAutoreleasedReturnValue(v15);
    v16 = -[ZiXunModuleViewController stockCode](self, "stockCode");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v18, v17);
    v3 = objc_retainAutoreleasedReturnValue(v19);
    v21 = -[ZiXunModuleViewController guanLianBaoJiaVC](self, "guanLianBaoJiaVC");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    -[GuanLianBaoJiaoViewController requestForSuoShuBanKuaiWithLinkCode:](
      v22,
      "requestForSuoShuBanKuaiWithLinkCode:",
      v3);
    v23(v22);
  }
}

//----- (0000000100BBF778) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadDaDanLengJing](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  DaDanLengJingContainerController *v3; // r13
  NSString *v4; // rax
  NSString *v5; // rax
  DaDanLengJingContainerController *v8; // rax
  NSString *v9; // rax
  NSString *v10; // rax
  NSString *v11; // r14

  v2 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  if ( _objc_msgSend(v3, "length") )
  {
    v4 = -[ZiXunModuleViewController market](self, "market");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = _objc_msgSend(v5, "length");
    if ( !v6 )
      return;
    v8 = -[ZiXunModuleViewController daDanLengJingVC](self, "daDanLengJingVC");
    v3 = objc_retainAutoreleasedReturnValue(v8);
    v9 = -[ZiXunModuleViewController stockCode](self, "stockCode");
    objc_retainAutoreleasedReturnValue(v9);
    v10 = -[ZiXunModuleViewController market](self, "market");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    -[DaDanLengJingContainerController requestForModularData:market:](v3, "requestForModularData:market:", v12, v11);
  }
}

//----- (0000000100BBF895) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadQuickTradeTable](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx

  v2 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "length");
  if ( v4 )
  {
    v6 = _objc_msgSend(v5, "quickTradeVC");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = _objc_msgSend(v8, "stockCode");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = _objc_msgSend(v11, "market");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v7, "setStockCode:market:forceReq:", v10, v13, 0LL);
    v14(v10);
    v15(v7);
  }
}

//----- (0000000100BBF979) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadTradingQueue](ZiXunModuleViewController *self, SEL a2)
{
  TradingQueueContainerController *v2; // rax
  TradingQueueContainerController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController tradingQueueVC](self, "tradingQueueVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestForData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFA09) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadGuaDanCheDan](ZiXunModuleViewController *self, SEL a2)
{
  GuaDanCheDanContainerController *v2; // rax
  GuaDanCheDanContainerController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController guaDanCheDanVC](self, "guaDanCheDanVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestForModularData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFA99) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadGuanLianShangPin](ZiXunModuleViewController *self, SEL a2)
{
  GuanLianShangPinViewController *v2; // rax
  GuanLianShangPinViewController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController guanLianShangPinVC](self, "guanLianShangPinVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestForData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFB29) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadRelatedWoLunNiuXiong](ZiXunModuleViewController *self, SEL a2)
{
  XiangGuanWoLunNiuXiongContainerController *v2; // rax
  XiangGuanWoLunNiuXiongContainerController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController woLunNiuXiongCC](self, "woLunNiuXiongCC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestForModularData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFBB9) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadWoLunNiuXiongWithZhengGu](ZiXunModuleViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // r15

  v2 = -[ZiXunModuleViewController market](self, "market");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = _objc_msgSend(v4, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = +[HXTools getMarketAndCodeByAppendingMarket:andStockCode:](
         &OBJC_CLASS___HXTools,
         "getMarketAndCodeByAppendingMarket:andStockCode:",
         v3,
         v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(v9, "woLunNiuXiongCC");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(v11, "sendZhengGuRequset:", v8);
}

//----- (0000000100BBFC81) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadGuanLianQiHuo](ZiXunModuleViewController *self, SEL a2)
{
  GuanLianQiHuoViewController *v2; // rax
  GuanLianQiHuoViewController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController guanLianQiHuoVC](self, "guanLianQiHuoVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestAllData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFD11) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadQiHuoGuanLianAGu](ZiXunModuleViewController *self, SEL a2)
{
  QiHuoGuanLianAGuViewController *v2; // rax
  QiHuoGuanLianAGuViewController *v3; // r14
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12

  v2 = -[ZiXunModuleViewController qhGuanLianAGuTVC](self, "qhGuanLianAGuTVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "stockCode");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(self, "market");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v3, "requestTabaleData:market:", v6, v9);
  v11(v6);
  v12(v3);
}

//----- (0000000100BBFDA1) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadGuanLianQiZhi](ZiXunModuleViewController *self, SEL a2)
{
  GuanLianQiZhiViewController *v2; // rax
  GuanLianQiZhiViewController *v3; // rbx

  v2 = -[ZiXunModuleViewController guanLianQiZhiVC](self, "guanLianQiZhiVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[GuanLianQiZhiViewController reloadGraphData](v3, "reloadGraphData");
}

//----- (0000000100BBFDDE) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController reloadStockNotes](ZiXunModuleViewController *self, SEL a2)
{
  StockNotesViewController *v2; // rax
  StockNotesViewController *v3; // r15
  NSString *v4; // rax
  NSString *v5; // rax
  NSString *v6; // rbx
  StockNotesViewController *v9; // rax
  StockNotesViewController *v10; // rbx

  v2 = -[ZiXunModuleViewController stockNotesVC](self, "stockNotesVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[ZiXunModuleViewController stockCode](self, "stockCode");
  objc_retainAutoreleasedReturnValue(v4);
  v5 = -[ZiXunModuleViewController market](self, "market");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  -[StockNotesViewController resetStockCode:market:](v3, "resetStockCode:market:", v7, v6);
  v9 = -[ZiXunModuleViewController stockNotesVC](self, "stockNotesVC");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[StockNotesViewController swtichSingleStockCode](v10, "swtichSingleStockCode");
}

//----- (0000000100BBFEA8) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController autoSwitchGeGuFenShi:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  signed __int8 v15; // al

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("StockCode"));
  objc_retainAutoreleasedReturnValue(v5);
  v6 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("Market"));
  v18 = objc_retainAutoreleasedReturnValue(v6);
  v19 = v4;
  v7 = _objc_msgSend(v4, "objectForKeyedSubscript:", CFSTR("ReqMkt"));
  v20 = objc_retainAutoreleasedReturnValue(v7);
  v8 = +[WidgetParamSettingManager shareInstance](&OBJC_CLASS___WidgetParamSettingManager, "shareInstance");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "panKouContainer");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(v11, "getTargetWidgetTradeVC");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  if ( v14 && v13 && (unsigned __int8)-[ZiXunModuleViewController viewDidAppearFlag](self, "viewDidAppearFlag") )
  {
    v15 = (unsigned __int8)_objc_msgSend(v20, "boolValue");
    -[ZiXunModuleViewController requestTradeStockInfoSucceedWithStockCode:strMarket:reqMkt:](
      self,
      "requestTradeStockInfoSucceedWithStockCode:strMarket:reqMkt:",
      v16,
      v18,
      (unsigned int)v15);
  }
}

//----- (0000000100BC0021) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController requestTradeStockInfoSucceedWithStockCode:strMarket:reqMkt:](
        ZiXunModuleViewController *self,
        SEL a2,
        id a3,
        id a4,
        char a5)
{
  NSString *v9; // rax
  NSString *v10; // rax
  NSString *v13; // rax
  NSString *v14; // r14
  NSString *v18; // rax
  NSString *v19; // r15
  NSString *v20; // rax
  NSString *v21; // rbx
  NSString *v22; // rdi
  NSString *v24; // rax
  NSString *v25; // rbx
  QueryQuotaDataModule *v26; // rax
  QueryQuotaDataModule *v27; // rbx
  _QWORD v60[2]; // [rsp+30h] [rbp-40h] BYREF

  objc_retain(a3);
  v8 = objc_retain(a4);
  if ( v7 && !(unsigned __int8)_objc_msgSend(v7, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v55 = v8;
    v9 = -[ZiXunModuleViewController stockCode](self, "stockCode");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v56 = v11;
    if ( (unsigned __int8)_objc_msgSend(v10, "isEqualToString:") )
    {
      v13 = -[ZiXunModuleViewController market](self, "market");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v58 = (unsigned __int8)_objc_msgSend(v14, "isEqualToString:", v8);
      if ( v58 )
        goto LABEL_14;
    }
    else
    {
    }
    -[ZiXunModuleViewController setMarket:](self, "setMarket:", v8);
    -[ZiXunModuleViewController setStockCode:](self, "setStockCode:", v16);
    if ( v8 && (v17 = _objc_msgSend(v8, "length"), a5) && v17 )
    {
      v18 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("不需要查询市场了，直接添加至最近浏览:%@-%@"),
              v56,
              v8);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v20 = (NSString *)_objc_msgSend(
                          &OBJC_CLASS___NSString,
                          "stringWithFormat:",
                          CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                          "-[ZiXunModuleViewController requestTradeStockInfoSucceedWithStockCode:strMarket:reqMkt:]",
                          1590LL,
                          v19);
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v22 = v19;
      v8 = v55;
      +[HXTools writeFileLog:](&OBJC_CLASS___HXTools, "writeFileLog:", v21);
      v24 = (NSString *)_objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@%@"), v55, v23);
      v25 = objc_retainAutoreleasedReturnValue(v24);
      -[ZiXunModuleViewController jumpGeGu:](self, "jumpGeGu:", v25);
    }
    else
    {
      v26 = -[ZiXunModuleViewController queryModule](self, "queryModule");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      if ( !v27 )
      {
        v29 = objc_alloc((Class)&OBJC_CLASS___QueryQuotaDataModule);
        v31 = (void *)v30(v29, "init");
        v32(self, "setQueryModule:", v31);
        v34 = (void *)v33(self, "queryModule");
        v35 = objc_retainAutoreleasedReturnValue(v34);
        v36(v35, "setDelegate:", self);
      }
      v37 = v28;
      v38 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __CFString *, __int64, id))v28)(
                      &OBJC_CLASS___NSString,
                      "stringWithFormat:",
                      CFSTR("根据股票代码发送查询市场请求:%@-%@"),
                      v56,
                      v55);
      v39 = objc_retainAutoreleasedReturnValue(v38);
      v40 = (void *)v37(
                      &OBJC_CLASS___NSString,
                      "stringWithFormat:",
                      CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                      "-[ZiXunModuleViewController requestTradeStockInfoSucceedWithStockCode:strMarket:reqMkt:]",
                      1597LL,
                      v39);
      v41 = objc_retainAutoreleasedReturnValue(v40);
      ((void (__fastcall *)(__objc2_class *, const char *, id))v37)(&OBJC_CLASS___HXTools, "writeFileLog:", v41);
      v43 = (void *)((__int64 (__fastcall *)(ZiXunModuleViewController *, const char *))v37)(self, "queryModule");
      v44 = objc_retainAutoreleasedReturnValue(v43);
      v45 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v37)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithInt:",
                      5LL);
      v54 = objc_retainAutoreleasedReturnValue(v45);
      v60[0] = v54;
      v46 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v37)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithInt:",
                      10LL);
      v57 = objc_retainAutoreleasedReturnValue(v46);
      v60[1] = v57;
      v47 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, __int64))v37)(
                      &OBJC_CLASS___NSArray,
                      "arrayWithObjects:count:",
                      v60,
                      2LL);
      v48 = objc_retainAutoreleasedReturnValue(v47);
      v59 = v56;
      v49 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, __int64 *, __int64))v37)(
                      &OBJC_CLASS___NSArray,
                      "arrayWithObjects:count:",
                      &v59,
                      1LL);
      v50 = objc_retainAutoreleasedReturnValue(v49);
      ((void (__fastcall *)(id, const char *, id, id, _QWORD))v37)(
        v44,
        "queryDataItems:forStockCodes:alsoSubscribe:",
        v48,
        v50,
        0LL);
      v51 = v48;
      v8 = v55;
    }
  }
LABEL_14:
}

//----- (0000000100BC049D) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController receiveQuotaData:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  NSString *v6; // rax
  NSString *v7; // r13
  NSString *v11; // rax
  NSString *v12; // rbx
  NSNumber *v16; // rax
  NSNumber *v17; // rbx
  id representedObject; // rdi
  NSString *v25; // rax
  NSString *v26; // r15
  NSString *v27; // rax
  NSString *v28; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
  {
    v6 = -[ZiXunModuleViewController stockCode](self, "stockCode");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = _objc_msgSend(v8, "thsDictionaryForKey:", v7);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    if ( v10 )
    {
      v11 = -[ZiXunModuleViewController stockCode](self, "stockCode");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = _objc_msgSend(v13, "objectForKey:", v12);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
      v17 = objc_retainAutoreleasedReturnValue(v16);
      v29 = v15;
      v18 = _objc_msgSend(v15, "thsStringForKey:", v17);
      v19 = objc_retainAutoreleasedReturnValue(v18);
      if ( v19 )
      {
        v20 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v19);
        v21 = objc_retainAutoreleasedReturnValue(v20);
        -[ZiXunModuleViewController setMarket:](self, "setMarket:", v21);
        v22 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v19);
        v23 = objc_retainAutoreleasedReturnValue(v22);
        representedObject = self->super._representedObject;
        self->super._representedObject = v23;
        v25 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("查询股票市场成功，添加至最近浏览:%@"),
                v19);
        v26 = objc_retainAutoreleasedReturnValue(v25);
        v27 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("【调用方法】:%s[Line %d] 【日志信息】:%@"),
                "-[ZiXunModuleViewController receiveQuotaData:]",
                1613LL,
                v26);
        v28 = objc_retainAutoreleasedReturnValue(v27);
        +[HXTools writeFileLog:](&OBJC_CLASS___HXTools, "writeFileLog:", v28);
        -[ZiXunModuleViewController jumpGeGu:](self, "jumpGeGu:", v19);
      }
      else
      {
      }
    }
  }
}

//----- (0000000100BC0707) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController jumpGeGu:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  SelfStock *v3; // rax
  SelfStock *v6; // r14
  id (*v7)(id, SEL, ...); // r12
  NSDictionary *v25; // rax
  NSDictionary *v36; // [rsp+40h] [rbp-30h]

  v29 = objc_retain(a3);
  v3 = +[SelfStock sharedInstance](&OBJC_CLASS___SelfStock, "sharedInstance");
  v5 = v4;
  v6 = objc_retainAutoreleasedReturnValue(v3);
  v8 = v7(self, "stockCode");
  objc_retainAutoreleasedReturnValue(v8);
  v9 = (void *)v5(self, "market");
  v10 = v5;
  v11 = objc_retainAutoreleasedReturnValue(v9);
  ((void (__fastcall *)(SelfStock *, const char *, __int64, id, __int64))v10)(
    v6,
    "addRecentlyScanStock:market:toBegin:",
    v12,
    v11,
    1LL);
  v14 = (void *)v10((ZiXunModuleViewController *)&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  ((void (__fastcall *)(id, const char *, __CFString *, _QWORD))v10)(
    v15,
    "postNotificationName:object:",
    CFSTR("JumpToGeGuController"),
    0LL);
  v16 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithUnsignedInteger:",
                  4LL);
  v32 = objc_retainAutoreleasedReturnValue(v16);
  v17 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  513LL);
  v35 = objc_retainAutoreleasedReturnValue(v17);
  v18 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  0LL);
  v30 = objc_retainAutoreleasedReturnValue(v18);
  v19 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  0LL);
  v31 = objc_retainAutoreleasedReturnValue(v19);
  v20 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  0LL);
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  0LL);
  v34 = objc_retainAutoreleasedReturnValue(v22);
  v23 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, _QWORD))v10)(
                  &OBJC_CLASS___NSNumber,
                  "numberWithInt:",
                  0LL);
  v33 = objc_retainAutoreleasedReturnValue(v23);
  v25 = _objc_msgSend(
          &OBJC_CLASS___NSDictionary,
          "dictionaryWithObjectsAndKeys:",
          v24,
          CFSTR("requesttype"),
          v35,
          CFSTR("TableID"),
          v30,
          CFSTR("sortid"),
          v31,
          CFSTR("sortbegin"),
          v21,
          CFSTR("sortcount"),
          v34,
          CFSTR("Index"),
          v29,
          CFSTR("SelectedCode"),
          CFSTR("D"),
          CFSTR("sortorder"),
          v33,
          CFSTR("isQuickTrade"),
          0LL);
  v36 = objc_retainAutoreleasedReturnValue(v25);
  v27 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v28 = objc_retainAutoreleasedReturnValue(v27);
  _objc_msgSend(v28, "postNotificationName:object:", CFSTR("DeliverQuotationTableDataNotification"), v36);
}

//----- (0000000100BC0A23) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunModuleViewController ziXunMenuView](ZiXunModuleViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100BC0A3C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunMenuView:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibName, a3);
}

//----- (0000000100BC0A50) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunModuleViewController ziXunContentView](ZiXunModuleViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibBundle);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100BC0A69) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunContentView:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibBundle, a3);
}

//----- (0000000100BC0A7D) ----------------------------------------------------
NSString *__cdecl -[ZiXunModuleViewController stockCode](ZiXunModuleViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100BC0A90) ----------------------------------------------------
NSString *__cdecl -[ZiXunModuleViewController market](ZiXunModuleViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 40LL, 0);
}

//----- (0000000100BC0AA3) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setMarket:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 40LL);
}

//----- (0000000100BC0AB4) ----------------------------------------------------
NSArray *__cdecl -[ZiXunModuleViewController menuTitles](ZiXunModuleViewController *self, SEL a2)
{
  return (NSArray *)self->super.view;
}

//----- (0000000100BC0AC5) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunModuleViewController zixunType](ZiXunModuleViewController *self, SEL a2)
{
  return (unsigned __int64)self->super._topLevelObjects;
}

//----- (0000000100BC0AD6) ----------------------------------------------------
id __cdecl -[ZiXunModuleViewController ziXunOpenURLBlock](ZiXunModuleViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 64LL, 0);
}

//----- (0000000100BC0AE9) ----------------------------------------------------
ZiXunModuleViewDidChangeDelegate *__cdecl -[ZiXunModuleViewController delegate](
        ZiXunModuleViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._autounbinder);
  return (ZiXunModuleViewDidChangeDelegate *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100BC0B02) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setDelegate:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super._autounbinder, a3);
}

//----- (0000000100BC0B16) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setTabbarController:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._designNibBundleIdentifier, a3);
}

//----- (0000000100BC0B2A) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunMenuVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->super.__privateData, a3);
}

//----- (0000000100BC0B3E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setZiXunTableVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._viewIsAppearing, a3);
}

//----- (0000000100BC0B52) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setGuanLianBaoJiaVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._isContentViewController, a3);
}

//----- (0000000100BC0B66) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setQuickTradeVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._reserved, a3);
}

//----- (0000000100BC0B7A) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setTradingQueueVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_hasSetDefaultTabbarCtrlIndex, a3);
}

//----- (0000000100BC0B8E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setQuanShangXiWeiVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ziXunMenuView, a3);
}

//----- (0000000100BC0BA2) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setGuaDanCheDanVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ziXunContentView, a3);
}

//----- (0000000100BC0BB6) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setDaDanLengJingVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_stockCode, a3);
}

//----- (0000000100BC0BCA) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setGuanLianShangPinVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_market, a3);
}

//----- (0000000100BC0BDE) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setRelatedZiXunTableVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_menuTitles, a3);
}

//----- (0000000100BC0BF2) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setWoLunNiuXiongCC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_zixunType, a3);
}

//----- (0000000100BC0C06) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setGuanLianQiZhiVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong(&self->_ziXunOpenURLBlock, a3);
}

//----- (0000000100BC0C1A) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setQhGuanLianAGuTVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_delegate, a3);
}

//----- (0000000100BC0C2E) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setGuanLianQiHuoVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_tabbarController, a3);
}

//----- (0000000100BC0C42) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setStockNotesVC:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ziXunMenuVC, a3);
}

//----- (0000000100BC0C56) ----------------------------------------------------
QueryQuotaDataModule *__cdecl -[ZiXunModuleViewController queryModule](ZiXunModuleViewController *self, SEL a2)
{
  return (QueryQuotaDataModule *)self->_ziXunTableVC;
}

//----- (0000000100BC0C67) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setQueryModule:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_ziXunTableVC, a3);
}

//----- (0000000100BC0C7B) ----------------------------------------------------
NSArray *__cdecl -[ZiXunModuleViewController reqParamArr](ZiXunModuleViewController *self, SEL a2)
{
  return (NSArray *)self->_guanLianBaoJiaVC;
}

//----- (0000000100BC0C8C) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setReqParamArr:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_guanLianBaoJiaVC, a3);
}

//----- (0000000100BC0CA0) ----------------------------------------------------
char __cdecl -[ZiXunModuleViewController hasSetDefaultTabbarCtrlIndex](ZiXunModuleViewController *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (0000000100BC0CB1) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setHasSetDefaultTabbarCtrlIndex:](
        ZiXunModuleViewController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super.super._nextResponder) = a3;
}

//----- (0000000100BC0CC1) ----------------------------------------------------
NSTimer *__cdecl -[ZiXunModuleViewController frameChangedTimer](ZiXunModuleViewController *self, SEL a2)
{
  return (NSTimer *)self->_quickTradeVC;
}

//----- (0000000100BC0CD2) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setFrameChangedTimer:](ZiXunModuleViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_quickTradeVC, a3);
}

//----- (0000000100BC0CE6) ----------------------------------------------------
char __cdecl -[ZiXunModuleViewController ziXunOpen](ZiXunModuleViewController *self, SEL a2)
{
  return BYTE1(self->super.super._nextResponder);
}

//----- (0000000100BC0CF7) ----------------------------------------------------
char __cdecl -[ZiXunModuleViewController needJumpToQuickTrade](ZiXunModuleViewController *self, SEL a2)
{
  return BYTE2(self->super.super._nextResponder);
}

//----- (0000000100BC0D08) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setNeedJumpToQuickTrade:](ZiXunModuleViewController *self, SEL a2, char a3)
{
  BYTE2(self->super.super._nextResponder) = a3;
}

//----- (0000000100BC0D18) ----------------------------------------------------
char __cdecl -[ZiXunModuleViewController viewDidAppearFlag](ZiXunModuleViewController *self, SEL a2)
{
  return BYTE3(self->super.super._nextResponder);
}

//----- (0000000100BC0D29) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController setViewDidAppearFlag:](ZiXunModuleViewController *self, SEL a2, char a3)
{
  BYTE3(self->super.super._nextResponder) = a3;
}

//----- (0000000100BC0D39) ----------------------------------------------------
void __cdecl -[ZiXunModuleViewController .cxx_destruct](ZiXunModuleViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_quickTradeVC, 0LL);
  objc_storeStrong((id *)&self->_guanLianBaoJiaVC, 0LL);
  objc_storeStrong((id *)&self->_ziXunTableVC, 0LL);
  objc_storeStrong((id *)&self->_ziXunMenuVC, 0LL);
  objc_storeStrong((id *)&self->_tabbarController, 0LL);
  objc_storeStrong((id *)&self->_delegate, 0LL);
  objc_storeStrong(&self->_ziXunOpenURLBlock, 0LL);
  objc_storeStrong((id *)&self->_zixunType, 0LL);
  objc_storeStrong((id *)&self->_menuTitles, 0LL);
  objc_storeStrong((id *)&self->_market, 0LL);
  objc_storeStrong((id *)&self->_stockCode, 0LL);
  objc_storeStrong((id *)&self->_ziXunContentView, 0LL);
  objc_storeStrong((id *)&self->_ziXunMenuView, 0LL);
  objc_storeStrong((id *)&self->_hasSetDefaultTabbarCtrlIndex, 0LL);
  objc_storeStrong((id *)&self->super._reserved, 0LL);
  objc_storeStrong((id *)&self->super._isContentViewController, 0LL);
  objc_storeStrong((id *)&self->super._viewIsAppearing, 0LL);
  objc_storeStrong(&self->super.__privateData, 0LL);
  objc_storeStrong((id *)&self->super._designNibBundleIdentifier, 0LL);
  objc_destroyWeak(&self->super._autounbinder);
  objc_storeStrong((id *)&self->super._editors, 0LL);
  objc_storeStrong((id *)&self->super.view, 0LL);
  objc_storeStrong((id *)&self->super._title, 0LL);
  objc_storeStrong(&self->super._representedObject, 0LL);
  objc_destroyWeak((id *)&self->super._nibBundle);
  objc_destroyWeak((id *)&self->super._nibName);
}

