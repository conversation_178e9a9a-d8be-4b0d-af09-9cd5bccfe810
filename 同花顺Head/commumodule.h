//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSMutableDictionary, NSString, UserModel, aNoeZQVOSoPboiub;
@protocol TradeResponceDispatch;

@interface commumodule : NSObject
{
    // Error parsing type: {CRequestBindBase="_vptr$CTLObject"^^?"m_hModule"^v"m_dwRegisterID"I"m_dwRegisterType"I"m_callbackResponse"{FastDelegate1<CQLBaseResponse &, int>="m_Closure"{ClosurePtr<int (fastdelegate::detail::GenericClass::*)(CQLBaseResponse &), int (*)(CQLBaseResponse &), int (*)(CQLBaseResponse &)>="m_pthis"^{GenericClass}"m_pFunction"}}}, name: reqBindEvent
    // Error parsing type: {CRequestBindBase="_vptr$CTLObject"^^?"m_hModule"^v"m_dwRegisterID"I"m_dwRegisterType"I"m_callbackResponse"{FastDelegate1<CQLBaseResponse &, int>="m_Closure"{ClosurePtr<int (fastdelegate::detail::GenericClass::*)(CQLBaseResponse &), int (*)(CQLBaseResponse &), int (*)(CQLBaseResponse &)>="m_pthis"^{GenericClass}"m_pFunction"}}}, name: reqBindBase
    struct CResponceHandle rspHandle;
    void *hModule;
    unsigned int nLoginCount;
    BOOL _isLostSocketConnection;
    BOOL _autoReg;
    unsigned int _dwSessionId;
    id <TradeResponceDispatch> _defRspHandleDelegate;
    CDUnknownBlockType _LoginStatusFun;
    UserModel *_aUserModel;
    NSString *_strDomain;
    aNoeZQVOSoPboiub *_tradeLoginParam;
    NSMutableDictionary *_dicInsID2ReqDelegate;
    NSMutableDictionary *_dicContext2Insid;
    NSMutableDictionary *_dicNeedPush;
    aNoeZQVOSoPboiub *_loginModel;
    double _lastReconnect;
}

- (id).cxx_construct;

@property(nonatomic) double lastReconnect; // @synthesize lastReconnect=_lastReconnect;
@property(copy, nonatomic) aNoeZQVOSoPboiub *loginModel; // @synthesize loginModel=_loginModel;
@property(nonatomic) unsigned int dwSessionId; // @synthesize dwSessionId=_dwSessionId;
@property(retain, nonatomic) NSMutableDictionary *dicNeedPush; // @synthesize dicNeedPush=_dicNeedPush;
@property(retain, nonatomic) NSMutableDictionary *dicContext2Insid; // @synthesize dicContext2Insid=_dicContext2Insid;
@property(retain, nonatomic) NSMutableDictionary *dicInsID2ReqDelegate; // @synthesize dicInsID2ReqDelegate=_dicInsID2ReqDelegate;
@property(retain, nonatomic) aNoeZQVOSoPboiub *tradeLoginParam; // @synthesize tradeLoginParam=_tradeLoginParam;
@property(copy, nonatomic) NSString *strDomain; // @synthesize strDomain=_strDomain;
@property(nonatomic) __weak UserModel *aUserModel; // @synthesize aUserModel=_aUserModel;
@property(nonatomic) BOOL autoReg; // @synthesize autoReg=_autoReg;
@property(copy, nonatomic) CDUnknownBlockType LoginStatusFun; // @synthesize LoginStatusFun=_LoginStatusFun;
@property(nonatomic) BOOL isLostSocketConnection; // @synthesize isLostSocketConnection=_isLostSocketConnection;
@property(nonatomic) __weak id <TradeResponceDispatch> defRspHandleDelegate; // @synthesize defRspHandleDelegate=_defRspHandleDelegate;
- (BOOL)isKilledByServer;
- (unsigned int)vuPEXDRmhuGmWuqM:(void *)arg1;
- (BOOL)LOWNqsWstlJNohYU;
- (id)getRspDelegateByContextId:(id)arg1;
- (id)GetReceiveDelegateArray:(long long)arg1;
- (void)registerPushType:(id)arg1 InstanceId:(long long)arg2;
- (void)kQvYehFExlWUHfgW:(id)arg1;
- (int)reconnectSession;
- (void)Disconnect;
- (BOOL)isDisconnected;
- (id)getTradePassword;
- (id)getUserid;
- (id)getUniqueQsid;
- (id)getLoginBroker;
- (id)getLoginAccount;
- (id)getSessionIpAndPort;
- (int)getSessionStatus;
- (id)getSessionDomainNs;
- (char *)getSessionDomain;
- (void *)getModule;
- (void)clearCommumodule;
- (void)reloadSetIsZyyUser;
- (void)reloadSetIsMeiguUser;
- (void)reloadSetIsMoniUser;
- (void)tradeLogOut:(id)arg1;
- (void)DJAwpxdGFxRzelWL:(id)arg1;
- (id)getHddinfo;
- (void)handleSIGPIPE;
- (void)networkReachableVia;
- (void)networkNotReachable;
- (void)initModule;
- (id)initWithUserModel:(id)arg1;
- (void)dealloc;

@end

