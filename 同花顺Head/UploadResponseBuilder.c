UploadResponseBuilder *__cdecl -[UploadResponseBuilder init](UploadResponseBuilder *self, SEL a2)
{
  UploadResponseBuilder *v2; // rbx

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___UploadResponseBuilder;
  v2 = objc_msgSendSuper2(&v6, "init");
  if ( v2 )
  {
    v3 = objc_alloc((Class)&OBJC_CLASS___UploadResponse);
    v4 = _objc_msgSend(v3, "init");
    -[UploadResponseBuilder setResultUploadResponse:](v2, "setResultUploadResponse:", v4);
  }
  return v2;
}

//----- (00000001004C801F) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder internalGetResult](UploadResponseBuilder *self, SEL a2)
{
  return objc_retainAutoreleaseReturnValue(self->resultUploadResponse);
}

//----- (00000001004C8034) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder clear](UploadResponseBuilder *self, SEL a2)
{

  v2 = objc_alloc((Class)&OBJC_CLASS___UploadResponse);
  v3 = _objc_msgSend(v2, "init");
  -[UploadResponseBuilder setResultUploadResponse:](self, "setResultUploadResponse:", v3);
  return self;
}

//----- (00000001004C808B) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder clone](UploadResponseBuilder *self, SEL a2)
{
  return +[UploadResponse builderWithPrototype:](
           &OBJC_CLASS___UploadResponse,
           "builderWithPrototype:",
           self->resultUploadResponse);
}

//----- (00000001004C80B2) ----------------------------------------------------
UploadResponseBuilder *__cdecl -[UploadResponseBuilder defaultInstance](UploadResponseBuilder *self, SEL a2)
{
  return (UploadResponseBuilder *)+[UploadResponse defaultInstance](&OBJC_CLASS___UploadResponse, "defaultInstance");
}

//----- (00000001004C80CB) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder build](UploadResponseBuilder *self, SEL a2)
{
  -[PBGeneratedMessageBuilder checkInitialized](self, "checkInitialized");
  return -[UploadResponseBuilder buildPartial](self, "buildPartial");
}

//----- (00000001004C80F9) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder buildPartial](UploadResponseBuilder *self, SEL a2)
{
  UploadResponse *v2; // r14

  v2 = objc_retain(self->resultUploadResponse);
  -[UploadResponseBuilder setResultUploadResponse:](self, "setResultUploadResponse:", 0LL);
  return objc_autoreleaseReturnValue(v2);
}

//----- (00000001004C8135) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder mergeFrom:](UploadResponseBuilder *self, SEL a2, id a3)
{
  UploadResponse *v3; // rax
  UploadResponse *v4; // rbx
  UploadResponse *v5; // r12
  unsigned int v7; // eax
  NSMutableArray *v16; // rax
  NSMutableArray *v17; // rbx
  NSMutableArray *v18; // rax
  NSMutableArray *v19; // r13

  objc_retain(a3);
  v3 = +[UploadResponse defaultInstance](&OBJC_CLASS___UploadResponse, "defaultInstance");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( v4 != v5 )
  {
    if ( (unsigned __int8)-[UploadResponse hasCode](v5, "hasCode") )
    {
      v7 = (unsigned int)_objc_msgSend(v6, "code");
      v8 = -[UploadResponseBuilder setCode:](self, "setCode:", v7);
      objc_unsafeClaimAutoreleasedReturnValue(v8);
    }
    if ( (unsigned __int8)_objc_msgSend(v6, "hasVersion") )
    {
      v10 = _objc_msgSend(v9, "version");
      v11 = -[UploadResponseBuilder setVersion:](self, "setVersion:", v10);
      objc_unsafeClaimAutoreleasedReturnValue(v11);
    }
    v12 = _objc_msgSend(v9, "fileArray");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "count");
    if ( v14 )
    {
      v16 = -[UploadResponse fileArray](self->resultUploadResponse, "fileArray");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      if ( v17 )
      {
        v18 = -[UploadResponse fileArray](self->resultUploadResponse, "fileArray");
        v19 = objc_retainAutoreleasedReturnValue(v18);
        v21 = _objc_msgSend(v20, "fileArray");
        v22 = objc_retainAutoreleasedReturnValue(v21);
        _objc_msgSend(v19, "addObjectsFromArray:", v22);
      }
      else
      {
        v23 = objc_alloc(&OBJC_CLASS___NSMutableArray);
        v25 = _objc_msgSend(v24, "fileArray");
        v19 = (NSMutableArray *)objc_retainAutoreleasedReturnValue(v25);
        v22 = _objc_msgSend(v23, "initWithArray:", v19);
        -[UploadResponse setFileArray:](self->resultUploadResponse, "setFileArray:", v22);
      }
    }
    v26 = _objc_msgSend(v15, "unknownFields");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v28 = -[PBGeneratedMessageBuilder mergeUnknownFields:](self, "mergeUnknownFields:", v27);
    objc_unsafeClaimAutoreleasedReturnValue(v28);
  }
  objc_retainAutorelease(self);
  return self;
}

//----- (00000001004C838B) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder mergeFromCodedInputStream:](UploadResponseBuilder *self, SEL a2, id a3)
{

  objc_retain(a3);
  v3 = +[PBExtensionRegistry emptyRegistry](&OBJC_CLASS___PBExtensionRegistry, "emptyRegistry");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = -[UploadResponseBuilder mergeFromCodedInputStream:extensionRegistry:](
         self,
         "mergeFromCodedInputStream:extensionRegistry:",
         v5,
         v4);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (00000001004C8418) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder mergeFromCodedInputStream:extensionRegistry:](
        UploadResponseBuilder *self,
        SEL a2,
        id a3,
        id a4)
{
  id (__cdecl *v6)(id); // r12
  int v13; // eax
  unsigned int v23; // eax
  UploadResponseBuilder *v32; // r15

  v5 = objc_retain(a3);
  v37 = v6(a4);
  v7 = -[PBGeneratedMessageBuilder unknownFields](self, "unknownFields");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = _objc_msgSend(v9, "builderWithUnknownFields:", v8);
  v36 = objc_retainAutoreleasedReturnValue(v10);
  v12 = "readTag";
  v35 = v5;
  do
  {
    while ( 1 )
    {
      v13 = v11(v5, v12);
      if ( v13 <= 15 )
        break;
      if ( v13 == 16 )
      {
        v26 = v11(v5, "readInt64");
        v25 = (void *)v27(self, "setVersion:", v26);
LABEL_9:
        objc_unsafeClaimAutoreleasedReturnValue(v25);
      }
      else
      {
        if ( v13 != 26 )
          goto LABEL_11;
        v14 = (void *)v11(&OBJC_CLASS___EntityFile, "builder");
        v15 = objc_retainAutoreleasedReturnValue(v14);
        v16(v5, "readMessage:extensionRegistry:", v15, v37);
        v18 = (void *)v17(v15, "buildPartial");
        v19 = v12;
        v20 = objc_retainAutoreleasedReturnValue(v18);
        v22 = (void *)v21(self, "addFile:", v20);
        objc_unsafeClaimAutoreleasedReturnValue(v22);
        v12 = v19;
        v5 = v35;
      }
    }
    if ( v13 == 8 )
    {
      v23 = v11(v5, "readInt32");
      v25 = (void *)v24(self, "setCode:", v23);
      goto LABEL_9;
    }
    if ( !v13 )
      break;
LABEL_11:
    ;
  }
  while ( ((unsigned __int8 (__fastcall *)(UploadResponseBuilder *, const char *, id, id, id, _QWORD))v11)(
            self,
            "parseUnknownField:unknownFields:extensionRegistry:tag:",
            v5,
            v36,
            v37,
            (unsigned int)v13) );
  v28 = (__int64 (__fastcall *)(UploadResponseBuilder *, const char *, id))v11;
  v29 = (void *)v11(v36, "build");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v31 = (void *)v28(self, "setUnknownFields:", v30);
  objc_unsafeClaimAutoreleasedReturnValue(v31);
  v32 = objc_retainAutoreleaseReturnValue(self);
  return v32;
}

//----- (00000001004C8683) ----------------------------------------------------
char __cdecl -[UploadResponseBuilder hasCode](UploadResponseBuilder *self, SEL a2)
{
  return (unsigned __int8)-[UploadResponse hasCode](self->resultUploadResponse, "hasCode");
}

//----- (00000001004C86A0) ----------------------------------------------------
int __cdecl -[UploadResponseBuilder code](UploadResponseBuilder *self, SEL a2)
{
  return (unsigned int)-[UploadResponse code](self->resultUploadResponse, "code");
}

//----- (00000001004C86BD) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder setCode:](UploadResponseBuilder *self, SEL a2, int a3)
{

  -[UploadResponse setHasCode:](self->resultUploadResponse, "setHasCode:", 1LL);
  v4(self->resultUploadResponse, "setCode:", (unsigned int)a3);
  return self;
}

//----- (00000001004C870C) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder clearCode](UploadResponseBuilder *self, SEL a2)
{
  -[UploadResponse setHasCode:](self->resultUploadResponse, "setHasCode:", 0LL);
  -[UploadResponse setCode:](self->resultUploadResponse, "setCode:", 0LL);
  return self;
}

//----- (00000001004C8755) ----------------------------------------------------
char __cdecl -[UploadResponseBuilder hasVersion](UploadResponseBuilder *self, SEL a2)
{
  return (unsigned __int8)-[UploadResponse hasVersion](self->resultUploadResponse, "hasVersion");
}

//----- (00000001004C8772) ----------------------------------------------------
signed __int64 __cdecl -[UploadResponseBuilder version](UploadResponseBuilder *self, SEL a2)
{
  return (signed __int64)-[UploadResponse version](self->resultUploadResponse, "version");
}

//----- (00000001004C878F) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder setVersion:](UploadResponseBuilder *self, SEL a2, signed __int64 a3)
{

  -[UploadResponse setHasVersion:](self->resultUploadResponse, "setHasVersion:", 1LL);
  v4(self->resultUploadResponse, "setVersion:", a3);
  return self;
}

//----- (00000001004C87DE) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder clearVersion](UploadResponseBuilder *self, SEL a2)
{
  -[UploadResponse setHasVersion:](self->resultUploadResponse, "setHasVersion:", 0LL);
  -[UploadResponse setVersion:](self->resultUploadResponse, "setVersion:", 0LL);
  return self;
}

//----- (00000001004C8827) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder file](UploadResponseBuilder *self, SEL a2)
{
  return -[UploadResponse fileArray](self->resultUploadResponse, "fileArray");
}

//----- (00000001004C8844) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder fileAtIndex:](UploadResponseBuilder *self, SEL a2, unsigned __int64 a3)
{
  return -[UploadResponse fileAtIndex:](self->resultUploadResponse, "fileAtIndex:", a3);
}

//----- (00000001004C8861) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder addFile:](UploadResponseBuilder *self, SEL a2, id a3)
{
  UploadResponse *resultUploadResponse; // rbx
  NSMutableArray *v5; // rax
  NSMutableArray *v6; // rbx

  resultUploadResponse = self->resultUploadResponse;
  v4 = objc_retain(a3);
  v5 = -[UploadResponse fileArray](resultUploadResponse, "fileArray");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  if ( !v6 )
  {
    v8 = objc_alloc(&OBJC_CLASS___NSMutableArray);
    v9 = _objc_msgSend(v8, "init");
    _objc_msgSend(*(id *)(v10 + 8), "setFileArray:", v9);
  }
  v11 = _objc_msgSend(*(id *)(v7 + 8), "fileArray");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "addObject:", v4);
  return v13;
}

//----- (00000001004C8944) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder setFileArray:](UploadResponseBuilder *self, SEL a2, id a3)
{

  objc_retain(a3);
  v3 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v5 = _objc_msgSend(v3, "initWithArray:", v4);
  -[UploadResponse setFileArray:](self->resultUploadResponse, "setFileArray:", v5);
  return self;
}

//----- (00000001004C89C7) ----------------------------------------------------
id __cdecl -[UploadResponseBuilder clearFile](UploadResponseBuilder *self, SEL a2)
{
  -[UploadResponse setFileArray:](self->resultUploadResponse, "setFileArray:", 0LL);
  return self;
}

//----- (00000001004C89F4) ----------------------------------------------------
UploadResponse *__cdecl -[UploadResponseBuilder resultUploadResponse](UploadResponseBuilder *self, SEL a2)
{
  return (UploadResponse *)objc_getProperty(self, a2, 8LL, 1);
}

//----- (00000001004C8A0A) ----------------------------------------------------
void __cdecl -[UploadResponseBuilder setResultUploadResponse:](UploadResponseBuilder *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 8LL);
}

//----- (00000001004C8A1B) ----------------------------------------------------
void __cdecl -[UploadResponseBuilder .cxx_destruct](UploadResponseBuilder *self, SEL a2)
{
  objc_storeStrong((id *)&self->resultUploadResponse, 0LL);
}

