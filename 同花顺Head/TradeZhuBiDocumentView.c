TradeZhuBiDocumentView *__cdecl -[TradeZhuBiDocumentView initWithCoder:](TradeZhuBiDocumentView *self, SEL a2, id a3)
{
  TradeZhuBiDocumentView *v3; // rax
  TradeZhuBiDocumentView *v4; // rbx

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___TradeZhuBiDocumentView;
  v3 = objc_msgSendSuper2(&v6, "initWithCoder:", a3);
  v4 = v3;
  if ( v3 )
  {
    -[TradeZhuBiDocumentView initOverallParams](v3, "initOverallParams");
    -[TradeZhuBiDocumentView addNotifications](v4, "addNotifications");
    -[TradeZhuBiDocumentView initQuoteItemObj](v4, "initQuoteItemObj");
  }
  return v4;
}

//----- (000000010016B876) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView dealloc](TradeZhuBiDocumentView *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___TradeZhuBiDocumentView;
  objc_msgSendSuper2(&v4, "dealloc");
}

//----- (000000010016B8EB) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView initOverallParams](TradeZhuBiDocumentView *self, SEL a2)
{
  NSFont *v3; // rax
  _NSViewAuxiliary *v4; // rax
  _NSViewAuxiliary *viewAuxiliary; // rdi
  NSFont *v6; // rax
  NSFont *v7; // rax
  NSFont *v9; // rax
  NSFont *v10; // rax
  SEL v18; // r12

  self->super._frame.origin.y = 20.0;
  self->super._frame.size.width = 18.0;
  self->super._frame.size.height = 348.0;
  self->super._bounds.origin.x = 0.0;
  self->super._bounds.origin.y = 68.0;
  self->super._bounds.size.width = 68.0;
  self->super._bounds.size.height = 58.0;
  self->super._superview = (NSView *)0x405F800000000000LL;
  self->super._subviews = (NSArray *)0x4052800000000000LL;
  self->super._window = (NSWindow *)0x4069000000000000LL;
  self->super._unused_was_gState = (id)0x4052800000000000LL;
  self->super._frameMatrix = (id)0x4071200000000000LL;
  self->super._layer = (CALayer *)0x4052800000000000LL;
  self->super._dragTypes = (id)0x4000000000000000LL;
  -[TradeZhuBiDocumentView resetDiamondParams](self, "resetDiamondParams");
  v3 = _objc_msgSend(&OBJC_CLASS___NSFont, "fontWithName:size:", CFSTR("Helvetica Neue"), 13.0);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  viewAuxiliary = self->super._viewAuxiliary;
  self->super._viewAuxiliary = v4;
  v6 = _objc_msgSend(&OBJC_CLASS___NSFont, "fontWithName:size:", CFSTR("Helvetica Neue"), 11.0);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = *(void **)&self->super._vFlags;
  *(_QWORD *)&self->super._vFlags = v7;
  v9 = _objc_msgSend(&OBJC_CLASS___NSFont, "fontWithName:size:", CFSTR("Helvetica Neue"), 9.0);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  rowCount = (void *)self->rowCount;
  self->rowCount = (unsigned __int64)v10;
  v12 = _objc_msgSend(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "mutableCopy");
  columnCount = (void *)self->columnCount;
  self->columnCount = (unsigned __int64)v14;
  _objc_msgSend((id)self->columnCount, "setAlignment:", 0LL);
  v16 = _objc_msgSend(&OBJC_CLASS___NSMutableParagraphStyle, "defaultParagraphStyle");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v19 = _objc_msgSend(v17, v18);
  headerH = self->headerH;
  *(_QWORD *)&self->headerH = v19;
  _objc_msgSend(*(id *)((char *)&self->super.super.super.isa + v21), "setAlignment:", 1LL);
  *(_OWORD *)&self->minFont = xmmword_1010CE1F0;
  v22 = +[HXThemeManager fallTextColor](&OBJC_CLASS___HXThemeManager, "fallTextColor");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  rowH = self->rowH;
  *(_QWORD *)&self->rowH = v23;
  v25 = +[HXThemeManager riseTextColor](&OBJC_CLASS___HXThemeManager, "riseTextColor");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  cloumnW = self->cloumnW;
  *(_QWORD *)&self->cloumnW = v26;
  v28 = +[HXThemeManager pureFallBgColor](&OBJC_CLASS___HXThemeManager, "pureFallBgColor");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  nameX = self->nameX;
  *(_QWORD *)&self->nameX = v29;
  v31 = +[HXThemeManager pureRiseBgColor](&OBJC_CLASS___HXThemeManager, "pureRiseBgColor");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  nameW = self->nameW;
  *(_QWORD *)&self->nameW = v32;
  v34 = _objc_msgSend(&OBJC_CLASS___NSColor, "whiteColor");
  v35 = objc_retainAutoreleasedReturnValue(v34);
  priceX = self->priceX;
  *(_QWORD *)&self->priceX = v35;
}

//----- (000000010016BC74) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView resetDiamondParams](TradeZhuBiDocumentView *self, SEL a2)
{

  self->priceW = 0.0;
  self->volX = 0.0;
  self->volW = 0.0;
  buyW = self->buyW;
  self->buyW = 0.0;
  self->buyX = 0.0;
  saleX = self->saleX;
  self->saleX = 0.0;
}

//----- (000000010016BCE7) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView initQuoteItemObj](TradeZhuBiDocumentView *self, SEL a2)
{
  id *v9; // r12
  _QWORD v10[5]; // [rsp+8h] [rbp-D8h] BYREF
  _QWORD v12[4]; // [rsp+38h] [rbp-A8h] BYREF
  _QWORD v14[4]; // [rsp+60h] [rbp-80h] BYREF
  id to; // [rsp+80h] [rbp-60h] BYREF
  _QWORD v16[4]; // [rsp+88h] [rbp-58h] BYREF
  id location[6]; // [rsp+B0h] [rbp-30h] BYREF

  v3 = objc_alloc((Class)&OBJC_CLASS___TradeZhuBiQuoteItem);
  v4 = _objc_msgSend(v3, "init");
  lineW = self->lineW;
  *(_QWORD *)&self->lineW = v4;
  v6(*(id *)&self->lineW, "setType:", 1LL);
  objc_initWeak(location, self);
  v14[0] = _NSConcreteStackBlock;
  v14[1] = 3254779904LL;
  v14[2] = sub_10016BF16;
  v14[3] = &unk_1012DAA10;
  objc_copyWeak(&to, location);
  v7(*(id *)&self->lineW, "setReloadBlock:", v14);
  v16[0] = _NSConcreteStackBlock;
  v16[1] = 3254779904LL;
  v16[2] = sub_10016BF83;
  v16[3] = &unk_1012DB1D0;
  objc_copyWeak(&v17, location);
  _objc_msgSend(*(id *)&self->lineW, "setScrollToBottomBlock:", v16);
  v10[0] = _NSConcreteStackBlock;
  v10[1] = 3254779904LL;
  v10[2] = sub_10016BFB4;
  v10[3] = &unk_1012DBDF0;
  objc_copyWeak(&v11, location);
  *(_QWORD *)(v8 - 8) = self;
  _objc_msgSend(*(id *)&self->lineW, "setSelectionIndexesBlock:", v10);
  v12[0] = _NSConcreteStackBlock;
  v12[1] = 3254779904LL;
  v12[2] = sub_10016C32F;
  v12[3] = &unk_1012DAA10;
  objc_copyWeak(&v13, location);
  _objc_msgSend(*(id *)&self->lineW, "setDidFinishFirstRequestBlock:", v12);
  objc_destroyWeak(&v13);
  objc_destroyWeak(v9);
  objc_destroyWeak(&v17);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (000000010016BF16) ----------------------------------------------------
__int64 __fastcall sub_10016BF16(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "frameDidChange:", 0LL);
  v2 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v2, "setNeedsDisplay:", 1LL);
  return v3(v2);
}

//----- (000000010016BF83) ----------------------------------------------------
void __fastcall sub_10016BF83(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "scrollToBottom");
}

//----- (000000010016BFB4) ----------------------------------------------------
void __fastcall sub_10016BFB4(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  _QWORD v13[5]; // [rsp+0h] [rbp-80h] BYREF
  id to[3]; // [rsp+28h] [rbp-58h] BYREF

  v15 = a3;
  v16 = a2;
  v17 = a1;
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "setNotScrollToBottom:", 1LL);
  v4 = objc_loadWeakRetained((id *)(a1 + 40));
  v6 = v5(v4, "quoteItem");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "resetCountDownTimer");
  v10 = v9(&OBJC_CLASS___NSOperationQueue, "mainQueue");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13[0] = _NSConcreteStackBlock;
  v13[1] = 3254779904LL;
  v13[2] = sub_10016C0F7;
  v13[3] = &unk_1012DBDC0;
  objc_copyWeak(to, (id *)(a1 + 40));
  to[1] = v16;
  to[2] = v15;
  v13[4] = *(_QWORD *)(v17 + 32);
  v12(v11, "addOperationWithBlock:", v13);
  objc_destroyWeak(to);
}

//----- (000000010016C0F7) ----------------------------------------------------
void __fastcall sub_10016C0F7(__int64 a1)
{
  id *v3; // r14
  id WeakRetained; // r13
  unsigned __int64 v7; // rdi
  unsigned __int64 v8; // rsi
  __m128i si128; // xmm0
  __m128d v10; // xmm3
  __m128d v12; // xmm3
  unsigned __int64 v26; // [rsp+40h] [rbp-90h]
  _QWORD v27[14]; // [rsp+60h] [rbp-70h] BYREF

  v2 = *(_QWORD *)(a1 + 48);
  v3 = (id *)(a1 + 40);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
  _objc_msgSend(WeakRetained, "setSelectRange:", v2, v5);
  v6 = *(_QWORD *)(a1 + 32);
  v7 = *(_QWORD *)(v6 + 8);
  v8 = *(_QWORD *)(a1 + 48) / v7;
  si128 = _mm_load_si128((const __m128i *)&xmmword_1010CE400);
  v10 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)v8, si128), (__m128d)xmmword_1010CE410);
  v11 = *(double *)(v6 + 40);
  *(double *)&v26 = _mm_hadd_pd(v10, v10).f64[0] * v11;
  v12 = _mm_sub_pd(
          (__m128d)_mm_unpacklo_epi32((__m128i)((*(_QWORD *)(a1 + 56) + *(_QWORD *)(a1 + 48)) / v7 - v8 + 1), si128),
          (__m128d)xmmword_1010CE410);
  v13 = objc_loadWeakRetained(v3);
  _objc_msgSend(v13, "height");
  *(_OWORD *)&v27[5] = v26;
  *(double *)&v27[7] = _mm_hadd_pd(v12, v12).f64[0] * v11;
  v27[8] = si128.i64[0];
  v14(v13);
  v15 = objc_loadWeakRetained(v3);
  _objc_msgSend(v15, "scrollRectToVisible:");
  v16(v15);
  v17 = objc_loadWeakRetained(v3);
  v18 = (const char *)objc_retainAutoreleasedReturnValue(v17);
  v19 = (char *)v18;
  if ( v18 )
    objc_msgSend_stret(v27, v18, "selectRect");
  else
    memset(v27, 0, 32);
  _objc_msgSend(v19, "setNeedsDisplayInRect:");
  v20(v19);
  v21(v19);
  v22 = objc_loadWeakRetained(v3);
  _objc_msgSend(v22, "setNeedsDisplayInRect:");
  v23(v22);
  v24 = objc_loadWeakRetained(v3);
  _objc_msgSend(v24, "setSelectRect:");
  v25(v24);
}

//----- (000000010016C32F) ----------------------------------------------------
void __fastcall sub_10016C32F(__int64 a1)
{
  id WeakRetained; // r14

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v2 = _objc_msgSend(WeakRetained, "indicator");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "stopAnimation:", 0LL);
}

//----- (000000010016C392) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView addNotifications](TradeZhuBiDocumentView *self, SEL a2)
{
  NSNotificationName v7; // rbx

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "addObserver:selector:name:object:", v4, "frameDidChange:", NSViewFrameDidChangeNotification, v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = NSViewBoundsDidChangeNotification;
  v9 = _objc_msgSend(v8, "enclosingScrollView");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "contentView");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v6, "addObserver:selector:name:object:", v13, "scrollViewIsScrolling:", v7, v12);
}

//----- (000000010016C496) ----------------------------------------------------
char __cdecl -[TradeZhuBiDocumentView isFlipped](TradeZhuBiDocumentView *self, SEL a2)
{
  return 1;
}

//----- (000000010016C4A1) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawRect:](TradeZhuBiDocumentView *self, SEL a2, CGRect a3)
{
  unsigned __int64 v16; // r12
  unsigned __int64 v17; // r13
  unsigned __int64 v28; // [rsp+28h] [rbp-78h]

  v3 = _objc_msgSend(self, "window");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (unsigned __int8)_objc_msgSend(v4, "isVisible");
  if ( v5 )
  {
    v6 = _objc_msgSend(*(id *)&self->lineW, "tradeZhuBiArrayController");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(v7, "arrangedObjects");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    if ( _objc_msgSend(v9, "count") && !LOBYTE(self->saleW) )
    {
      v31 = v9;
      LOBYTE(self->saleW) = 1;
      v10 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "currentContext");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v12 = objc_retainAutorelease(v11);
      v32 = _objc_msgSend(v12, "graphicsPort");
      -[TradeZhuBiDocumentView columnsInRect:](self, "columnsInRect:");
      v14 = v13;
      -[TradeZhuBiDocumentView resetDiamondParams](self, "resetDiamondParams");
      v15 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
      v33 = objc_retainAutoreleasedReturnValue(v15);
      v28 = v14 + v16;
      if ( !__CFADD__(v14, v16) )
      {
        do
        {
          v30 = v16;
          v29 = v14;
          v17 = v16 * (unsigned __int64)self->super.super._nextResponder;
          v18 = (char *)self->super.super._nextResponder + v17;
          if ( v18 >= _objc_msgSend(v31, "count") )
          {
            v20 = v29;
            if ( v17 >= (unsigned __int64)_objc_msgSend(v19, "count") )
              break;
            v24 = (char *)_objc_msgSend(v31, "count");
            v25 = _objc_msgSend(v31, "subarrayWithRange:", v17, &v24[-v17]);
            v22 = objc_retainAutoreleasedReturnValue(v25);
            -[TradeZhuBiDocumentView drawCloumn:array:diamond:withEnd:context:nextIndex:originData:](
              self,
              "drawCloumn:array:diamond:withEnd:context:nextIndex:originData:",
              v26,
              v22,
              v33,
              1LL,
              v32,
              0x7FFFFFFFFFFFFFFFLL,
              v31);
          }
          else
          {
            v20 = v29;
            v21 = _objc_msgSend(v19, "subarrayWithRange:", v17, self->super.super._nextResponder);
            v22 = objc_retainAutoreleasedReturnValue(v21);
            -[TradeZhuBiDocumentView drawCloumn:array:diamond:withEnd:context:nextIndex:originData:](
              self,
              "drawCloumn:array:diamond:withEnd:context:nextIndex:originData:",
              v30,
              v22,
              v33,
              v29 == 0,
              v32,
              (char *)self->super.super._nextResponder + v17,
              v23);
          }
          v16 = v27 + 1;
          v14 = v20 - 1;
        }
        while ( v16 <= v28 );
      }
      if ( !-[TradeZhuBiDocumentView displayMode](self, "displayMode") )
        -[TradeZhuBiDocumentView drawDiamonds:context:](self, "drawDiamonds:context:", v33, v32);
      LOBYTE(self->saleW) = 0;
      v9 = v31;
    }
  }
}

//----- (000000010016C7F5) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawCloumn:array:diamond:withEnd:context:nextIndex:originData:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        unsigned __int64 a3,
        id a4,
        id a5,
        char a6,
        CGContext *c,
        signed __int64 a8,
        id a9)
{
  TradeZhuBiQuoteItem *v12; // rax
  TradeZhuBiQuoteItem *v13; // rbx
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v19)(id, SEL, ...); // r12
  __m128d v21; // xmm0
  id (*v23)(id, SEL, ...); // r12
  id (*v27)(id, SEL, ...); // r12
  id (*v32)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  unsigned __int64 v34; // r13
  id (*v36)(id, SEL, ...); // r12
  unsigned __int64 v38; // rax
  NSFont *minFont; // rcx
  id (*v44)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  id (*v53)(id, SEL, ...); // r12
  CGContextRef v59; // r12
  CGContextRef v60; // r12
  CGContextRef v61; // r12
  CGContextRef v62; // r12
  CGContextRef v63; // r12
  id (*v64)(id, SEL, ...); // r12
  CGRect rect; // [rsp+0h] [rbp-1B0h]
  CGSize v67; // [rsp+40h] [rbp-170h]
  CGRect v68; // [rsp+50h] [rbp-160h]
  CGContext *xb; // [rsp+140h] [rbp-70h]
  id x; // [rsp+140h] [rbp-70h]

  v76 = objc_retain(a4);
  v70 = objc_retain(a5);
  v71 = objc_retain(v11);
  v12 = -[TradeZhuBiDocumentView quoteItem](self, "quoteItem");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v15 = v14(v13, "getCurItemStockcode");
  v72 = objc_retainAutoreleasedReturnValue(v15);
  v17 = v16(self, "quoteItem");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v20 = v19(v18, "getCurItemMarket");
  v73 = objc_retainAutoreleasedReturnValue(v20);
  v21 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)a3, (__m128i)xmmword_1010CE400), (__m128d)xmmword_1010CE410);
  v22 = _mm_hadd_pd(v21, v21).f64[0] * self->super._frame.size.height;
  y = self->super._frame.origin.y;
  width = *(double *)&self->super._dragTypes * 0.5;
  v24 = v23(&OBJC_CLASS___HXThemeManager, "tableHeaderBgColor");
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v26(v25, "setFill");
  v67.width = self->super._frame.size.height;
  v67.height = self->super._frame.origin.y;
  rect.size = v67;
  rect.origin = (CGPoint)*(unsigned __int64 *)&v22;
  CGContextFillRect(c, rect);
  CGContextSetLineWidth(c, *(CGFloat *)&self->super._dragTypes);
  v28 = v27(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  v30(v29, "setStroke");
  if ( a3 )
  {
    xa = *(double *)&self->super._dragTypes + v22 - width;
    CGContextMoveToPoint(c, xa, 0.0);
    _objc_msgSend(self, "height");
    CGContextAddLineToPoint(c, xa, xa);
    CGContextStrokePath(c);
  }
  CGContextMoveToPoint(c, v22, self->super._frame.origin.y - width);
  CGContextAddLineToPoint(c, self->super._frame.size.height + v22, self->super._frame.origin.y - width);
  CGContextStrokePath(c);
  -[TradeZhuBiDocumentView drawHeadViewTexts:](self, "drawHeadViewTexts:", v22);
  v31 = v76;
  if ( v32(v76, "count") )
  {
    v75 = v22 + -5.0;
    v34 = 0LL;
    do
    {
      v35 = v33(v31, "objectAtIndexedSubscript:", v34);
      v37 = objc_retainAutoreleasedReturnValue(v35);
      v38 = a3 * (unsigned __int64)self->super.super._nextResponder;
      minFont = self->minFont;
      if ( v38 + v34 >= (unsigned __int64)minFont
        && (NSMutableParagraphStyle *)(v34 + v38 - (_QWORD)minFont) < self->leftAlignmentStyle )
      {
        v40 = v36(&OBJC_CLASS___NSGraphicsContext, "currentContext");
        v41 = objc_retainAutoreleasedReturnValue(v40);
        v42 = objc_retainAutorelease(v41);
        xb = (CGContext *)_objc_msgSend(v42, "graphicsPort");
        v68.origin.x = v22;
        v68.origin.y = y;
        v68.size.width = self->super._frame.size.height;
        v68.size.height = self->super._frame.size.width;
        v45 = v44(&OBJC_CLASS___HXThemeManager, "rowMarkedBgColor");
        v46 = objc_retainAutoreleasedReturnValue(v45);
        v47(v46, "setFill");
        CGContextFillRect(xb, v68);
      }
      v82[0] = (__int64)NSFontAttributeName;
      v83[0] = (__int64)self->super._viewAuxiliary;
      v82[1] = (__int64)NSForegroundColorAttributeName;
      v48 = v36(v37, "volColor");
      v49 = objc_retainAutoreleasedReturnValue(v48);
      v83[1] = (__int64)v49;
      v82[2] = (__int64)NSParagraphStyleAttributeName;
      v83[2] = *(_QWORD *)&self->headerH;
      v51 = v50(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v83, v82, 3LL);
      x = objc_retainAutoreleasedReturnValue(v51);
      v52(self, "drawBaseTypeContent:x:y:", v37, v75, y);
      if ( !v53(self, "displayMode") )
      {
        v54(self, "drawHuanYuanTypeContent:x:y:attributes:", v37, x, v75, y);
        LODWORD(rect_16) = a6;
        v55(
          self,
          "calculateHuanYuanDiamondMArr:array:diamond:x:y:stockCode:market:index:withEnd:nextIndex:originData:",
          v37,
          v76,
          v70,
          v72,
          v75,
          y,
          v73,
          v34,
          rect_16,
          a8,
          v71);
      }
      v56 = (unsigned __int8)((id (*)(id, SEL, ...))v54)(v37, "lineHidden");
      if ( v34 && !v56 )
      {
        CGContextSetShouldAntialias(c, 0);
        v57 = +[HXThemeManager minorModuleLineColor](&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
        v58 = objc_retainAutoreleasedReturnValue(v57);
        _objc_msgSend(v58, "setStroke");
        CGContextSetLineWidth(v59, width);
        CGContextMoveToPoint(v60, v22, y);
        CGContextAddLineToPoint(v61, self->super._frame.size.height + v22, y);
        CGContextStrokePath(v62);
        CGContextSetShouldAntialias(v63, 1);
      }
      y = y + self->super._frame.size.width;
      ++v34;
      v31 = v76;
    }
    while ( v34 < (unsigned __int64)v64(v76, "count") );
  }
}

//----- (000000010016CFD3) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawDiamonds:context:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        CGContext *a4)
{
  CGFloat width; // xmm1_8
  unsigned __int64 v13; // r12
  NSString *v15; // rbx
  NSRect v18; // [rsp+20h] [rbp-200h] BYREF
  CGRect v23; // [rsp+80h] [rbp-1A0h] BYREF
  SEL v24; // [rsp+A0h] [rbp-180h]
  SEL v25; // [rsp+A8h] [rbp-178h]
  SEL v26; // [rsp+B0h] [rbp-170h]
  SEL v27; // [rsp+B8h] [rbp-168h]
  SEL v30; // [rsp+D0h] [rbp-150h]
  SEL v33; // [rsp+E8h] [rbp-138h]
  SEL v34; // [rsp+F0h] [rbp-130h]
  SEL v35; // [rsp+F8h] [rbp-128h]
  SEL v37; // [rsp+108h] [rbp-118h]
  SEL v38; // [rsp+110h] [rbp-110h]
  NSRect rect; // [rsp+120h] [rbp-100h] BYREF
  CGContext *v41; // [rsp+148h] [rbp-D8h]
  id obj; // [rsp+158h] [rbp-C8h]
  int v45; // [rsp+16Ch] [rbp-B4h] BYREF

  v41 = a4;
  v44 = self;
  v5 = objc_retain(a3);
  if ( _objc_msgSend(v5, "count") )
  {
    x = 0.0;
    v22 = 0LL;
    v21 = 0LL;
    v20 = 0LL;
    v19 = 0LL;
    v39 = v5;
    obj = objc_retain(v5);
    v42 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v46, 16LL);
    if ( v42 )
    {
      v36 = *(_QWORD *)v20;
      do
      {
        v33 = "beginPoint";
        v34 = "getRectsArray:maxIndex:";
        v35 = "objectAtIndex:";
        v37 = "isDoubleEqualToZero:";
        v38 = "drawTwoRectDiamond:rects:context:";
        v25 = "objectAtIndexedSubscript:";
        v7 = 0LL;
        do
        {
          if ( *(_QWORD *)v20 != v36 )
            objc_enumerationMutation(obj);
          v8 = *(void **)(*((_QWORD *)&v19 + 1) + 8 * v7);
          _objc_msgSend(v8, v33);
          v28 = x;
          v29 = width;
          v45 = 0;
          v9 = _objc_msgSend(v44, v34, v8, &v45);
          v10 = objc_retainAutoreleasedReturnValue(v9);
          v11 = _objc_msgSend(v10, v35, v45);
          v32 = objc_retainAutoreleasedReturnValue(v11);
          NSRectFromString(&v18, (NSString *)v32);
          v12 = _objc_msgSend(v10, "count");
          v31 = v7;
          if ( v12 == (id)2
            && (x = v18.size.height + -14.0,
                (unsigned __int8)_objc_msgSend(&OBJC_CLASS___HXTools, v37, v18.size.height + -14.0)) )
          {
            _objc_msgSend(v44, v38, v8, v10, v41);
          }
          else if ( _objc_msgSend(v10, "count") )
          {
            v24 = "drawDiamondRect:rect:context:";
            v26 = "drawTextsView:rect:";
            v27 = "daDanType";
            v30 = "drawTriangleView:";
            v13 = 0LL;
            do
            {
              v14 = _objc_msgSend(v10, v25, v13);
              v15 = (NSString *)objc_retainAutoreleasedReturnValue(v14);
              NSRectFromString(&rect, v15);
              CGRectInset(&v23, rect, 0.5, 0.5);
              rect = v23;
              x = v23.origin.x;
              width = v23.size.width;
              _objc_msgSend(v44, v24, v8, v41);
              if ( v16 == v45 )
              {
                x = rect.origin.x;
                width = rect.size.width;
                _objc_msgSend(v44, v26, v8);
              }
              if ( !v16 && _objc_msgSend(v8, v27) == (id)2 )
              {
                x = v28;
                width = v29;
                _objc_msgSend(v44, v30, v28, v29);
              }
              v17 = _objc_msgSend(v10, "count");
            }
            while ( (unsigned __int64)v17 > v13 );
          }
          v7 = v31 + 1;
        }
        while ( v31 + 1 < (unsigned __int64)v42 );
        v42 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v19, v46, 16LL);
      }
      while ( v42 );
    }
    v5 = v39;
  }
}

//----- (000000010016D43F) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView calculateHuanYuanDiamondMArr:array:diamond:x:y:stockCode:market:index:withEnd:nextIndex:originData:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        double a6,
        double a7,
        id a8,
        id a9,
        unsigned __int64 a10,
        char a11,
        signed __int64 a12,
        id a13)
{
  id (__cdecl *v16)(id); // r12
  id (__cdecl *v17)(id); // r12
  id (__cdecl *v18)(id); // r12
  id (__cdecl *v19)(id); // r12
  id (__cdecl *v20)(id); // r12
  id (*v25)(id, SEL, ...); // r12
  TradeZhuBiDocumentView *v28; // rbx
  id (*v32)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v41)(id, SEL, ...); // r12
  id (*v45)(id, SEL, ...); // r12
  id (*v49)(id, SEL, ...); // r12
  id (*v52)(id, SEL, ...); // r12
  id (*v57)(id, SEL, ...); // r12
  id (*v64)(id, SEL, ...); // r12
  unsigned __int64 v66; // r13
  id (*v69)(id, SEL, ...); // r12
  id (*v70)(id, SEL, ...); // r12
  id (*v73)(id, SEL, ...); // r12
  id (*v74)(id, SEL, ...); // r12
  id (*v77)(id, SEL, ...); // r12
  id (*v81)(id, SEL, ...); // r12
  TradeZhuBiDocumentView *v85; // r13
  id (*v88)(id, SEL, ...); // r12
  id (*v95)(id, SEL, ...); // r12
  id (*v98)(id, SEL, ...); // r12
  id (*v102)(id, SEL, ...); // r12
  id (*v106)(id, SEL, ...); // r12
  id (*v109)(id, SEL, ...); // r12
  id (*v114)(id, SEL, ...); // r12
  id (*v117)(id, SEL, ...); // r12
  id (*v119)(id, SEL, ...); // r12
  unsigned __int64 v120; // r14
  id (*v123)(id, SEL, ...); // r12
  id (*v124)(id, SEL, ...); // r12
  id (*v127)(id, SEL, ...); // r12
  id (*v128)(id, SEL, ...); // r12
  id (*v131)(id, SEL, ...); // r12

  v15 = objc_retain(a3);
  v133 = v16(a4);
  v144 = v17(a5);
  v140 = v18(a8);
  v135 = v19(a9);
  v142 = v20(a13);
  priceW = self->priceW;
  v141 = v15;
  v23 = _objc_msgSend(v15, "buyNumber");
  v24 = objc_retainAutoreleasedReturnValue(v23);
  v26 = v25(v24, "integerValue");
  v27 = v24;
  v28 = self;
  volW = self->volW;
  if ( *(id *)&priceW == v26 )
  {
    *(_QWORD *)&self->volW = *(_QWORD *)&volW + 1LL;
    v29(
      *(id *)&self->buyW,
      "setEndPoint:",
      *(double *)&self->super._window + a6 + *(double *)&self->super._unused_was_gState,
      self->super._frame.size.width + a7 + -2.0);
    v31 = v141;
    v33 = v32(v141, "totalBuyVolValue");
    v34(*(id *)&v28->buyW, "setZongShouInt:", v33);
    v36 = 0;
  }
  else
  {
    v31 = v141;
    if ( *(__int64 *)&volW <= 0 )
    {
      v136 = 0;
    }
    else
    {
      _objc_msgSend(*(id *)&v28->buyW, "configDiamondModelParams:market:", v140, v135);
      v136 = 1;
      if ( *(_QWORD *)&v28->buyW )
        _objc_msgSend(v144, "addObject:");
    }
    v37 = objc_alloc((Class)&OBJC_CLASS___TradeZhuBiDiamondModel);
    v39 = v38(v37, "init");
    v40(v39, "setBeginPoint:", *(double *)&v28->super._window + a6 + 6.0, a7 + 2.0);
    v42 = v41(v141, "price");
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v44(v39, "setPrice:", v43);
    v46 = v45(v141, "totalBuyVolValue");
    v47(v39, "setZongShouInt:", v46);
    v48(v39, "setBuySaleType:", 74LL);
    v50 = v49(v141, "buyNumber");
    v51 = objc_retainAutoreleasedReturnValue(v50);
    v53 = v52(v51, "integerValue");
    v54(v39, "setSerialInteger:", v53);
    buyW = self->buyW;
    *(_QWORD *)&self->buyW = v39;
    v56 = objc_retain(v39);
    self->volW = 0.0;
    v58 = v57(v141, "buyNumber");
    v59 = objc_retainAutoreleasedReturnValue(v58);
    v60(v59, "integerValue");
    self->priceW = v61;
    v62 = v59;
    v28 = self;
    v36 = v136;
  }
  if ( a11 && !v36 && *(__int64 *)&v28->volW > 0 )
  {
    if ( (id)(a10 + 1) != _objc_msgSend(v133, "count") )
    {
      v28 = self;
      goto LABEL_24;
    }
    if ( a12 == 0x7FFFFFFFFFFFFFFFLL )
    {
      v28 = self;
    }
    else
    {
      v63 = v142;
      v28 = self;
      if ( (unsigned __int64)_objc_msgSend(v142, "count") > a12 )
      {
        v65 = v144;
        if ( (unsigned __int64)_objc_msgSend(v142, "count") > a12 )
        {
          v66 = a12;
          while ( 1 )
          {
            v67 = v64(v63, "objectAtIndexedSubscript:", v66);
            v68 = objc_retainAutoreleasedReturnValue(v67);
            v137 = v69(*(id *)&v28->buyW, "serialInteger");
            v71 = v70(v68, "buyNumber");
            v72 = objc_retainAutoreleasedReturnValue(v71);
            v132 = v73(v72, "integerValue");
            if ( v137 != v132 )
              break;
            v75 = v74(v68, "totalBuyVolValue");
            v28 = self;
            v76(*(id *)&self->buyW, "setZongShouInt:", v75);
            ++v66;
            v63 = v142;
            if ( v66 >= (unsigned __int64)v77(v142, "count") )
            {
              v31 = v141;
              goto LABEL_21;
            }
          }
          v31 = v141;
          v65 = v144;
          v28 = self;
        }
        goto LABEL_22;
      }
    }
LABEL_21:
    v65 = v144;
LABEL_22:
    _objc_msgSend(*(id *)&v28->buyW, "configDiamondModelParams:market:", v140, v135);
    if ( *(_QWORD *)&v28->buyW )
      _objc_msgSend(v65, "addObject:");
  }
LABEL_24:
  volX = v28->volX;
  v79 = v35(v31, "saleNumber");
  v80 = objc_retainAutoreleasedReturnValue(v79);
  v82 = v81(v80, "integerValue");
  v83 = v80;
  v84 = v31;
  v85 = self;
  buyX = self->buyX;
  if ( *(id *)&volX == v82 )
  {
    *(_QWORD *)&self->buyX = *(_QWORD *)&buyX + 1LL;
    v86(
      *(id *)&self->saleX,
      "setEndPoint:",
      a6 + *(double *)&self->super._frameMatrix + *(double *)&self->super._layer,
      a7 + self->super._frame.size.width + -2.0);
    v89 = v88(v84, "totalSaleVolValue");
    v90(*(id *)&self->saleX, "setZongShouInt:", v89);
    v91 = 0;
    v92 = v142;
    v93 = v144;
  }
  else
  {
    if ( *(__int64 *)&buyX <= 0 )
    {
      v143 = 0;
    }
    else
    {
      _objc_msgSend(*(id *)&self->saleX, "configDiamondModelParams:market:", v140, v135);
      v143 = 1;
      if ( *(_QWORD *)&self->saleX )
        _objc_msgSend(v144, "addObject:");
    }
    v94 = objc_alloc((Class)&OBJC_CLASS___TradeZhuBiDiamondModel);
    v96 = v95(v94, "init");
    v97(v96, "setBeginPoint:", a6 + *(double *)&self->super._frameMatrix + 6.0, a7 + 2.0);
    v99 = v98(v84, "price");
    v100 = objc_retainAutoreleasedReturnValue(v99);
    v101(v96, "setPrice:", v100);
    v103 = v102(v141, "totalSaleVolValue");
    v104(v96, "setZongShouInt:", v103);
    v105(v96, "setBuySaleType:", 75LL);
    v107 = v106(v141, "saleNumber");
    v108 = objc_retainAutoreleasedReturnValue(v107);
    v110 = v109(v108, "integerValue");
    v111(v96, "setSerialInteger:", v110);
    saleX = self->saleX;
    *(_QWORD *)&self->saleX = v96;
    v113 = objc_retain(v96);
    self->buyX = 0.0;
    v115 = v114(v141, "saleNumber");
    v116 = objc_retainAutoreleasedReturnValue(v115);
    v118 = v117(v116, "integerValue");
    *(_QWORD *)&self->volX = v118;
    v85 = self;
    v93 = v144;
    v92 = v142;
    v91 = v143;
  }
  if ( a11 && !v91 && *(__int64 *)&v85->buyX > 0 && (id)(a10 + 1) == _objc_msgSend(v133, "count") )
  {
    if ( a12 != 0x7FFFFFFFFFFFFFFFLL
      && (unsigned __int64)_objc_msgSend(v92, "count") > a12
      && (unsigned __int64)_objc_msgSend(v92, "count") > a12 )
    {
      v120 = a12;
      while ( 1 )
      {
        v121 = v119(v92, "objectAtIndexedSubscript:", v120);
        v122 = objc_retainAutoreleasedReturnValue(v121);
        v134 = v123(*(id *)&v85->saleX, "serialInteger");
        v125 = v124(v122, "saleNumber");
        v126 = objc_retainAutoreleasedReturnValue(v125);
        v138 = v127(v126, "integerValue");
        if ( v134 != v138 )
          break;
        v129 = v128(v122, "totalSaleVolValue");
        v130(*(id *)&v85->saleX, "setZongShouInt:", v129);
        ++v120;
        v92 = v142;
        if ( v120 >= (unsigned __int64)v131(v142, "count") )
          goto LABEL_44;
      }
      v92 = v142;
    }
LABEL_44:
    _objc_msgSend(*(id *)&v85->saleX, "configDiamondModelParams:market:", v140, v135);
    v93 = v144;
    if ( *(_QWORD *)&v85->saleX )
      _objc_msgSend(v144, "addObject:");
  }
}

//----- (000000010016DE72) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawHuanYuanTypeContent:x:y:attributes:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        double a4,
        double a5,
        id a6)
{

  objc_retain(a3);
  v7 = objc_retain(a6);
  if ( (__int64)_objc_msgSend(v8, "buyIndex") <= 0 )
  {
    v10 = _objc_msgSend(v9, "vol");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "drawInRect:withAttributes:", v7);
  }
  if ( (__int64)_objc_msgSend(v9, "saleIndex") <= 0 )
  {
    v13 = _objc_msgSend(v12, "vol");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v14, "drawInRect:withAttributes:", v7);
  }
}

//----- (000000010016E032) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawBaseTypeContent:x:y:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        double a4,
        double a5)
{
  NSDictionary *v9; // rax
  NSDictionary *v10; // rbx
  NSDictionary *v16; // rax
  NSDictionary *v17; // rbx
  bool v19; // zf
  SEL *v20; // rcx
  NSDictionary *v25; // rax
  NSDictionary *v26; // rbx
  _QWORD v32[3]; // [rsp+B8h] [rbp-A8h] BYREF
  _QWORD v33[9]; // [rsp+D0h] [rbp-90h] BYREF
  _QWORD v34[3]; // [rsp+118h] [rbp-48h] BYREF

  v28 = objc_retain(a3);
  v5 = _objc_msgSend(v28, "name");
  v29 = objc_retainAutoreleasedReturnValue(v5);
  v33[6] = NSFontAttributeName;
  v34[0] = self->super._viewAuxiliary;
  v33[7] = NSForegroundColorAttributeName;
  v6 = _objc_msgSend(v28, "nameColor");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v34[1] = v7;
  *(_QWORD *)(v8 + 16) = NSParagraphStyleAttributeName;
  v34[2] = *(_QWORD *)&self->headerH;
  v9 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v34, v8, 3LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v29, "drawInRect:withAttributes:", v10);
  v12 = _objc_msgSend(v28, "price");
  v30 = objc_retainAutoreleasedReturnValue(v12);
  v33[0] = NSFontAttributeName;
  v33[3] = self->super._viewAuxiliary;
  v33[1] = NSForegroundColorAttributeName;
  v13 = _objc_msgSend(v28, "priceColor");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  *(_QWORD *)(v15 + 8) = v14;
  v33[2] = NSParagraphStyleAttributeName;
  *(double *)(v15 + 16) = self->headerH;
  v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v15, v33, 3LL);
  v17 = objc_retainAutoreleasedReturnValue(v16);
  _objc_msgSend(v30, "drawInRect:withAttributes:", v17);
  v19 = (unsigned __int8)_objc_msgSend(*(id *)&self->lineW, "disPlayMoney") == 0;
  v20 = &selRef_money;
  if ( v19 )
    v20 = &selRef_vol;
  v21 = _objc_msgSend(v28, *v20);
  v31 = objc_retainAutoreleasedReturnValue(v21);
  v32[0] = self->super._viewAuxiliary;
  v22 = _objc_msgSend(v28, "volColor");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v32[1] = v23;
  *(_QWORD *)(v24 + 16) = NSParagraphStyleAttributeName;
  v32[2] = *(_QWORD *)&self->headerH;
  v25 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v32, v24, 3LL);
  v26 = objc_retainAutoreleasedReturnValue(v25);
  _objc_msgSend(v31, "drawInRect:withAttributes:", v26);
}

//----- (000000010016E50F) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawHeadViewTexts:](TradeZhuBiDocumentView *self, SEL a2, double a3)
{
  __CFString *v13; // rdi
  SEL v14; // r12
  _QWORD v15[3]; // [rsp+D0h] [rbp-60h] BYREF
  _QWORD v16[3]; // [rsp+E8h] [rbp-48h] BYREF

  v15[0] = NSFontAttributeName;
  v16[0] = *(_QWORD *)&self->super._vFlags;
  v15[1] = NSForegroundColorAttributeName;
  v4 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v6 = v5;
  v16[1] = objc_retainAutoreleasedReturnValue(v4);
  v15[2] = NSParagraphStyleAttributeName;
  v16[2] = *(_QWORD *)&self->headerH;
  v7 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v6)(
                 &OBJC_CLASS___NSDictionary,
                 "dictionaryWithObjects:forKeys:count:",
                 v16,
                 v15,
                 3LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v6(CFSTR("时间"), "drawInRect:withAttributes:", v8);
  v6(CFSTR("价格"), v10, v8);
  v11 = ((__int64 (__fastcall *)(_QWORD, const char *))v6)(*(_QWORD *)&self->lineW, "disPlayMoney");
  v13 = CFSTR("金额");
  if ( !v11 )
    v13 = CFSTR("手数");
  v6(v13, v12, v8);
  if ( !((__int64 (__fastcall *)(TradeZhuBiDocumentView *, const char *))v6)(self, "displayMode") )
  {
    _objc_msgSend(CFSTR("买单"), "drawInRect:withAttributes:", v8);
    _objc_msgSend(CFSTR("卖单"), v14, v8);
  }
}

//----- (000000010016E8CA) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawTwoRectDiamond:rects:context:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        id a4,
        CGContext *a5)
{
  unsigned __int64 v10; // r13
  __m128d v14; // xmm0
  __m128d v24; // xmm0
  CGRect v33; // [rsp+20h] [rbp-1B0h] BYREF
  __m128d v34; // [rsp+40h] [rbp-190h]
  __m128d v37; // [rsp+60h] [rbp-170h]
  CGContext *v41; // [rsp+90h] [rbp-140h]
  SEL v43; // [rsp+A0h] [rbp-130h]
  SEL v44; // [rsp+A8h] [rbp-128h]
  SEL v45; // [rsp+B0h] [rbp-120h]
  SEL v46; // [rsp+B8h] [rbp-118h]
  SEL v49; // [rsp+D0h] [rbp-100h]
  SEL v55; // [rsp+100h] [rbp-D0h]
  SEL v57; // [rsp+110h] [rbp-C0h]
  SEL v58; // [rsp+118h] [rbp-B8h]
  NSRect rect; // [rsp+120h] [rbp-B0h] BYREF

  v41 = a5;
  v50 = self;
  v8 = objc_retain(a3);
  v9 = objc_retain(a4);
  _objc_msgSend(v8, "beginPoint");
  v47 = v5;
  v48 = v6;
  if ( _objc_msgSend(v9, "count") )
  {
    v43 = "drawDiamondRect:rect:context:";
    v44 = "zongJinEString";
    v52 = 88LL;
    v53 = 32LL;
    v54 = 136LL;
    v55 = "textColor";
    v56 = 168LL;
    v57 = "dictionaryWithObjects:forKeys:count:";
    v58 = "drawInRect:withAttributes:";
    v45 = "zongShouString";
    v46 = "daDanType";
    v49 = "drawTriangleView:";
    v10 = 0LL;
    v51 = v8;
    v42 = v9;
    do
    {
      v11 = _objc_msgSend(v9, "objectAtIndexedSubscript:", v10);
      v40 = objc_retainAutoreleasedReturnValue(v11);
      NSRectFromString(&rect, (NSString *)v40);
      CGRectInset(&v33, rect, 0.5, 0.5);
      rect = v33;
      v12 = (char *)v50;
      _objc_msgSend(v50, v43, v8, v41);
      if ( v10 )
      {
        v13 = _objc_msgSend(v8, v44);
        objc_retainAutoreleasedReturnValue(v13);
        v14.f64[0] = CGRectGetMinX(rect) + -6.0;
        v14.f64[1] = rect.origin.y;
        v15 = *(_QWORD *)&v12[v52];
        v16 = *(_QWORD *)&v12[v53];
        v37 = _mm_add_pd(v14, (__m128d)xmmword_1010CEF60);
        v38 = v15;
        v39 = v16;
        v60[0] = (__int64)NSFontAttributeName;
        v61[0] = *(_QWORD *)&v12[v54];
        v60[1] = (__int64)NSForegroundColorAttributeName;
        v17 = _objc_msgSend(v8, v55);
        v18 = objc_retainAutoreleasedReturnValue(v17);
        v61[1] = (__int64)v18;
        v60[2] = (__int64)NSParagraphStyleAttributeName;
        v61[2] = *(_QWORD *)&v12[v56];
        v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v57, v61, v60, 3LL);
        v20 = objc_retainAutoreleasedReturnValue(v19);
        _objc_msgSend(v21, v58, v20);
        v8 = v51;
      }
      else
      {
        v23 = _objc_msgSend(v8, v45);
        objc_retainAutoreleasedReturnValue(v23);
        v24.f64[0] = CGRectGetMinX(rect) + -6.0;
        v24.f64[1] = rect.origin.y;
        v25 = *(_QWORD *)&v12[v52];
        v26 = *(_QWORD *)&v12[v53];
        v34 = _mm_add_pd(v24, (__m128d)xmmword_1010CEF60);
        v35 = v25;
        v36 = v26;
        v62[0] = (__int64)NSFontAttributeName;
        v63[0] = *(_QWORD *)&v12[v54];
        v62[1] = (__int64)NSForegroundColorAttributeName;
        v27 = _objc_msgSend(v8, v55);
        v28 = objc_retainAutoreleasedReturnValue(v27);
        v63[1] = (__int64)v28;
        v62[2] = (__int64)NSParagraphStyleAttributeName;
        v63[2] = *(_QWORD *)&v12[v56];
        v29 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v57, v63, v62, 3LL);
        v30 = objc_retainAutoreleasedReturnValue(v29);
        _objc_msgSend(v31, v58, v30);
        v8 = v51;
        if ( _objc_msgSend(v8, v46) == (id)2 )
          _objc_msgSend(v50, v49, v47, v48);
      }
      ++v10;
      v9 = v42;
    }
    while ( (unsigned __int64)_objc_msgSend(v42, "count") > v10 );
  }
}

//----- (000000010016EE1E) ----------------------------------------------------
id __cdecl -[TradeZhuBiDocumentView getRectsArray:maxIndex:](TradeZhuBiDocumentView *self, SEL a2, id a3, int *a4)
{
  int v9; // r14d
  id (*v10)(id, SEL, ...); // r12
  int v12; // eax
  NSString *v21; // rax
  NSString *v22; // rax
  NSString *v23; // r15
  int v24; // r12d
  long double aRect; // [rsp+0h] [rbp-E0h]
  NSRect v29; // [rsp+48h] [rbp-98h]
  int *v32; // [rsp+78h] [rbp-68h]

  v32 = a4;
  v6 = objc_retain(a3);
  _objc_msgSend(v6, "beginPoint");
  v37 = v4;
  v31 = *(double *)&v5;
  v35 = v6;
  v7(v6, "endPoint");
  v33 = v4;
  v34 = *(double *)&v5;
  v8 = (v4 - v4 + -68.0) / self->super._frame.size.height;
  floor(aRect);
  v9 = (int)(v8 + 1.0);
  objc_msgSend_stret(v27, (SEL)self, "bounds");
  v39 = v28;
  v11 = v10(&OBJC_CLASS___NSMutableArray, "array");
  v38 = objc_retainAutoreleasedReturnValue(v11);
  if ( v9 > 0 )
  {
    height = CGRectZero.size.height;
    v39 = v39 + -2.0;
    v12 = 0;
    v13 = 0.0;
    do
    {
      v14 = v37;
      v15 = self->super._frame.size.height * v13 + v37;
      v16 = v31;
      if ( v12 )
      {
        v14 = self->super._frame.size.height * v13 + v37;
        v16 = self->super._frame.origin.y + 2.0;
      }
      v17 = v13 + 1.0;
      v18 = v15 + 68.0;
      v19 = v39;
      if ( v12 + 1 >= v9 )
      {
        v19 = v34;
        v18 = v33;
      }
      v30 = v17;
      v20 = v19 - v16;
      v29.origin.x = v14;
      v29.origin.y = v16;
      v29.size.width = v18 - v14;
      v29.size.height = v20;
      if ( v20 > height )
      {
        *v32 = v12;
        height = v20;
      }
      v21 = NSStringFromRect(v29);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23 = v22;
      if ( v22 )
        _objc_msgSend(v38, "addObject:", v22);
      v12 = v24;
      v13 = v30;
    }
    while ( v9 != v24 );
  }
  return objc_autoreleaseReturnValue(v38);
}

//----- (000000010016F041) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawDiamondRect:rect:context:](
        TradeZhuBiDocumentView *self,
        SEL a2,
        id a3,
        CGRect rect,
        CGContext *a5)
{

  objc_retain(a3);
  CGContextSetLineWidth(a5, 1.0);
  v7 = _objc_msgSend(v6, "bgColor");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( v8 )
  {
    v10 = _objc_msgSend(v9, "bgColor");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "setFill");
    CGContextFillRect(a5, rect);
  }
  v12 = _objc_msgSend(v9, "borderColor");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "setStroke");
  CGContextStrokeRect(a5, rect);
}

//----- (000000010016F15B) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawTextsView:rect:](TradeZhuBiDocumentView *self, SEL a2, id a3, CGRect rect)
{
  NSDictionary *v18; // rax
  NSDictionary *v19; // rbx
  _QWORD v23[3]; // [rsp+A8h] [rbp-78h] BYREF
  _QWORD v24[3]; // [rsp+C0h] [rbp-60h] BYREF
  _QWORD v25[3]; // [rsp+D8h] [rbp-48h] BYREF

  v4 = objc_retain(a3);
  CGRectGetMinY(rect);
  CGRectGetMaxY(rect);
  v20 = v4;
  v5 = _objc_msgSend(v4, "zongShouString");
  v21 = objc_retainAutoreleasedReturnValue(v5);
  CGRectGetMinX(rect);
  v24[0] = NSFontAttributeName;
  v25[0] = self->super._viewAuxiliary;
  v24[1] = NSForegroundColorAttributeName;
  v7 = (void *)v6(v4, "textColor");
  v9 = v8;
  v25[1] = objc_retainAutoreleasedReturnValue(v7);
  v24[2] = NSParagraphStyleAttributeName;
  v25[2] = *(_QWORD *)&self->headerH;
  v10 = (void *)((__int64 (__fastcall *)(objc_class *, const char *, _QWORD *, _QWORD *, __int64))v9)(
                  &OBJC_CLASS___NSDictionary,
                  "dictionaryWithObjects:forKeys:count:",
                  v25,
                  v24,
                  3LL);
  v11 = v9;
  v12 = objc_retainAutoreleasedReturnValue(v10);
  v11(v21, "drawInRect:withAttributes:", v12);
  v14 = _objc_msgSend(v20, "zongJinEString");
  v22 = objc_retainAutoreleasedReturnValue(v14);
  CGRectGetMinX(rect);
  v23[0] = self->super._viewAuxiliary;
  v15 = _objc_msgSend(v20, "textColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v23[1] = v16;
  *(_QWORD *)(v17 + 16) = NSParagraphStyleAttributeName;
  v23[2] = *(_QWORD *)&self->headerH;
  v18 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v23, v17, 3LL);
  v19 = objc_retainAutoreleasedReturnValue(v18);
  _objc_msgSend(v22, "drawInRect:withAttributes:", v19);
}

//----- (000000010016F4F9) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView drawTriangleView:](TradeZhuBiDocumentView *self, SEL a2, CGPoint a3)
{
  _QWORD *v9; // r12
  NSDictionary *v10; // rax
  NSDictionary *v11; // rbx
  _QWORD v12[3]; // [rsp+50h] [rbp-60h] BYREF
  _QWORD v13[3]; // [rsp+68h] [rbp-48h] BYREF

  v3 = objc_alloc(&OBJC_CLASS___NSBezierPath);
  v4 = _objc_msgSend(v3, "init");
  _objc_msgSend(v4, "moveToPoint:", a3.x, a3.y);
  _objc_msgSend(v4, "lineToPoint:", a3.x, *(double *)(v5 + 32) + a3.y);
  _objc_msgSend(v4, "lineToPoint:", *(double *)(v6 + 32) + a3.x, a3.y);
  _objc_msgSend(v4, "closePath");
  v7 = +[HXThemeManager orangeLineColor](&OBJC_CLASS___HXThemeManager, "orangeLineColor");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, "set");
  _objc_msgSend(v4, "fill");
  _objc_msgSend(v4, "stroke");
  v12[0] = NSFontAttributeName;
  v13[0] = v9[19];
  v12[1] = NSForegroundColorAttributeName;
  v13[1] = v9[26];
  v12[2] = NSParagraphStyleAttributeName;
  v13[2] = v9[20];
  v10 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v13, v12, 3LL);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(CFSTR("特"), "drawInRect:withAttributes:", v11);
}

//----- (000000010016F71A) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView caculateRowAndColumn](TradeZhuBiDocumentView *self, SEL a2)
{
  unsigned __int64 v8; // rcx
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  __m128i si128; // xmm1
  __m128d v18; // xmm0
  __m128d v19; // xmm3
  unsigned __int64 v21; // rcx
  long double v23; // [rsp-8h] [rbp-30h]

  *(_QWORD *)&v23 = v2;
  v4 = _objc_msgSend(self, "enclosingScrollView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6(v5, "height");
  v7 = (v3 - self->super._frame.origin.y + -10.0) / self->super._frame.size.width;
  v8 = (unsigned int)(int)(v7 - 9.223372036854776e18) ^ 0x8000000000000000LL;
  if ( v7 < 9.223372036854776e18 )
    v8 = (unsigned int)(int)v7;
  self->super.super._nextResponder = (id)v8;
  if ( !self->super.super._nextResponder )
    self->super.super._nextResponder = (id)1;
  v10 = v9(*(id *)&self->lineW, "tradeZhuBiArrayController");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v13 = v12(v11, "arrangedObjects");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  v16 = v15(v14, "count");
  si128 = _mm_load_si128((const __m128i *)&xmmword_1010CE400);
  v18 = _mm_sub_pd((__m128d)_mm_unpacklo_epi32((__m128i)(unsigned __int64)v16, si128), (__m128d)xmmword_1010CE410);
  v19 = _mm_sub_pd(
          (__m128d)_mm_unpacklo_epi32(_mm_loadl_epi64((const __m128i *)&self->super.super._nextResponder), si128),
          (__m128d)xmmword_1010CE410);
  v20 = _mm_hadd_pd(v18, v18).f64[0] / _mm_hadd_pd(v19, v19).f64[0];
  ceil(v23);
  v21 = (unsigned int)(int)(v20 - 9.223372036854776e18) ^ 0x8000000000000000LL;
  if ( v20 < 9.223372036854776e18 )
    v21 = (unsigned int)(int)v20;
  *(_QWORD *)&self->super._frame.origin.x = v21;
  if ( !*(Class *)((char *)&self->super.super.super.isa + v22) )
    *(Class *)((char *)&self->super.super.super.isa + v22) = (Class)1;
}

//----- (000000010016F8A2) ----------------------------------------------------
_NSRange __cdecl -[TradeZhuBiDocumentView columnsInRect:](TradeZhuBiDocumentView *self, SEL a2, CGRect a3)
{
  CGFloat v3; // xmm0_8
  _BOOL8 v5; // rax
  NSUInteger v6; // rbx
  _NSRange result; // rax
  long double v9; // [rsp+0h] [rbp-20h]
  long double v10; // [rsp+0h] [rbp-20h]

  *((_QWORD *)&v9 + 1) = *(_QWORD *)&self->super._frame.size.height;
  *(CGFloat *)&v9 = a3.size.width;
  v3 = a3.origin.x / *((double *)&v9 + 1);
  floor(v9);
  v4 = (unsigned int)(int)v3;
  v5 = v4 != 0;
  if ( !(int)v3 )
    v4 = 0LL;
  v6 = v4 - v5;
  v7 = *(double *)&v10 / *((double *)&v10 + 1);
  ceil(v10);
  result.length = (unsigned int)(int)v7 + 1LL;
  result.location = v6;
  return result;
}

//----- (000000010016F918) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setDisplayMode:](TradeZhuBiDocumentView *self, SEL a2, unsigned __int64 a3)
{
  if ( self->middleFont != (NSFont *)a3 )
  {
    self->middleFont = (NSFont *)a3;
    -[TradeZhuBiDocumentView setCurModeDrawParams:](self, "setCurModeDrawParams:");
  }
}

//----- (000000010016F93D) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setCurModeDrawParams:](TradeZhuBiDocumentView *self, SEL a2, unsigned __int64 a3)
{

  self->super._frame.origin.y = 20.0;
  self->super._frame.size.width = 18.0;
  if ( a3 == 1 )
  {
    self->super._frame.size.height = 200.0;
    self->super._bounds.origin.x = 0.0;
    self->super._bounds.origin.y = 68.0;
    self->super._bounds.size.width = 68.0;
    self->super._bounds.size.height = 58.0;
    self->super._superview = (NSView *)0x405F800000000000LL;
    self->super._subviews = (NSArray *)0x4052800000000000LL;
    self->super._dragTypes = (id)0x4000000000000000LL;
  }
  else
  {
    self->super._frame.size.height = 348.0;
    self->super._bounds.origin.x = 0.0;
    self->super._bounds.origin.y = 68.0;
    self->super._bounds.size.width = 68.0;
    self->super._bounds.size.height = 58.0;
    self->super._superview = (NSView *)0x405F800000000000LL;
    self->super._subviews = (NSArray *)0x4052800000000000LL;
    self->super._window = (NSWindow *)0x4069000000000000LL;
    self->super._unused_was_gState = (id)0x4052800000000000LL;
    self->super._frameMatrix = (id)0x4071200000000000LL;
    self->super._layer = (CALayer *)0x4052800000000000LL;
    self->super._dragTypes = (id)0x4000000000000000LL;
    self->priceW = 0.0;
    self->volX = 0.0;
    self->volW = 0.0;
    buyW = self->buyW;
    self->buyW = 0.0;
    self->buyX = 0.0;
    saleX = self->saleX;
    self->saleX = 0.0;
  }
  -[TradeZhuBiDocumentView frameDidChange:](self, "frameDidChange:", 0LL);
  BYTE1(self->saleW) = 0;
  -[TradeZhuBiDocumentView scrollToBottom](self, "scrollToBottom");
}

//----- (000000010016FB7C) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView frameDidChange:](TradeZhuBiDocumentView *self, SEL a2, id a3)
{
  TradeZhuBiDocumentView *v6; // rbx

  v3 = objc_retain(a3);
  v4 = v3;
  if ( !v3
    || (v5 = _objc_msgSend(v3, "object"),
        v6 = (TradeZhuBiDocumentView *)objc_retainAutoreleasedReturnValue(v5),
        v6 == self) )
  {
    -[TradeZhuBiDocumentView frameEndChange](self, "frameEndChange");
  }
}

//----- (000000010016FBE6) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView frameEndChange](TradeZhuBiDocumentView *self, SEL a2)
{
  __m128d v2; // xmm0
  __m128d v3; // xmm0
  __m128d v4; // xmm1
  CGRect rect1; // [rsp+40h] [rbp-70h] BYREF
  __m128d v6; // [rsp+60h] [rbp-50h]
  CGRect rect2; // [rsp+78h] [rbp-38h]

  -[TradeZhuBiDocumentView caculateRowAndColumn](self, "caculateRowAndColumn");
  v2 = _mm_sub_pd(
         (__m128d)_mm_unpacklo_ps((__m128)(unsigned __int64)self->super.super._nextResponder, (__m128)xmmword_1010CE400),
         (__m128d)xmmword_1010CE410);
  v3 = _mm_hadd_pd(v2, v2);
  v3.f64[0] = v3.f64[0] * self->super._frame.size.width + self->super._frame.origin.y;
  v6 = v3;
  _objc_msgSend(self, "x");
  v8 = v3.f64[0];
  _objc_msgSend(self, "y");
  v4 = _mm_sub_pd(
         (__m128d)_mm_unpacklo_ps((__m128)*(unsigned __int64 *)&self->super._frame.origin.x, (__m128)xmmword_1010CE400),
         (__m128d)xmmword_1010CE410);
  v4.f64[0] = _mm_hadd_pd(v4, v4).f64[0] * self->super._frame.size.height;
  rect2.origin.x = v3.f64[0];
  rect2.origin.y = v3.f64[0];
  rect2.size.width = v4.f64[0];
  rect2.size.height = v3.f64[0];
  objc_msgSend_stret(&rect1, (SEL)self, "frame");
  if ( !CGRectEqualToRect(rect1, rect2) )
    _objc_msgSend(self, "setFrame:");
}

//----- (000000010016FD1B) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView scrollViewIsScrolling:](TradeZhuBiDocumentView *self, SEL a2, id a3)
{
  CGFloat MaxX; // xmm0_8
  CGRect rect; // [rsp+20h] [rbp-50h] BYREF

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(self, "enclosingScrollView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(v6, "contentView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( v4 == v8 )
  {
    objc_msgSend_stret(&rect, (SEL)self, "visibleRect");
    MaxX = CGRectGetMaxX(rect);
    v12 = MaxX;
    _objc_msgSend(self, "width");
    BYTE1(self->saleW) = v12 < MaxX;
  }
}

//----- (000000010016FDFF) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView scrollToBottom](TradeZhuBiDocumentView *self, SEL a2)
{

  if ( !BYTE1(self->saleW) )
  {
    _objc_msgSend(self, "width");
    _objc_msgSend(self, "scrollPoint:", v2, 0.0);
  }
}

//----- (000000010016FE42) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView configScrollToBottomState:](TradeZhuBiDocumentView *self, SEL a2, char a3)
{
  BYTE1(self->saleW) = a3;
}

//----- (000000010016FE52) ----------------------------------------------------
TradeZhuBiQuoteItem *__cdecl -[TradeZhuBiDocumentView quoteItem](TradeZhuBiDocumentView *self, SEL a2)
{
  return *(TradeZhuBiQuoteItem **)&self->lineW;
}

//----- (000000010016FE63) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setQuoteItem:](TradeZhuBiDocumentView *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->lineW, a3);
}

//----- (000000010016FE77) ----------------------------------------------------
NSProgressIndicator *__cdecl -[TradeZhuBiDocumentView indicator](TradeZhuBiDocumentView *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->font);
  return (NSProgressIndicator *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010016FE90) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setIndicator:](TradeZhuBiDocumentView *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->font, a3);
}

//----- (000000010016FEA4) ----------------------------------------------------
_NSRange __cdecl -[TradeZhuBiDocumentView selectRange](TradeZhuBiDocumentView *self, SEL a2)
{
  return *(_NSRange *)&self->minFont;
}

//----- (000000010016FEBA) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setSelectRange:](TradeZhuBiDocumentView *self, SEL a2, _NSRange a3)
{
  *(_NSRange *)&self->minFont = a3;
}

//----- (000000010016FED0) ----------------------------------------------------
char __cdecl -[TradeZhuBiDocumentView notScrollToBottom](TradeZhuBiDocumentView *self, SEL a2)
{
  return BYTE1(self->saleW);
}

//----- (000000010016FEE1) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setNotScrollToBottom:](TradeZhuBiDocumentView *self, SEL a2, char a3)
{
  BYTE1(self->saleW) = a3;
}

//----- (000000010016FEF1) ----------------------------------------------------
CGRect *__cdecl -[TradeZhuBiDocumentView selectRect](CGRect *retstr, TradeZhuBiDocumentView *self, SEL a3)
{
  CGRect *result; // rax
  CGSize v4; // xmm1

  result = retstr;
  v4 = *(CGSize *)&self->themeRedColor;
  retstr->origin = *(CGPoint *)&self->rightAlignmentStyle;
  retstr->size = v4;
  return result;
}

//----- (000000010016FF11) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView setSelectRect:](TradeZhuBiDocumentView *self, SEL a2, CGRect a3)
{
  *(CGRect *)&self->rightAlignmentStyle = a3;
}

//----- (000000010016FF2F) ----------------------------------------------------
unsigned __int64 __cdecl -[TradeZhuBiDocumentView displayMode](TradeZhuBiDocumentView *self, SEL a2)
{
  return (unsigned __int64)self->middleFont;
}

//----- (000000010016FF40) ----------------------------------------------------
void __cdecl -[TradeZhuBiDocumentView .cxx_destruct](TradeZhuBiDocumentView *self, SEL a2)
{
  objc_destroyWeak((id *)&self->font);
  objc_storeStrong((id *)&self->lineW, 0LL);
  objc_storeStrong((id *)&self->saleX, 0LL);
  objc_storeStrong((id *)&self->buyW, 0LL);
  objc_storeStrong((id *)&self->priceX, 0LL);
  objc_storeStrong((id *)&self->nameW, 0LL);
  objc_storeStrong((id *)&self->nameX, 0LL);
  objc_storeStrong((id *)&self->cloumnW, 0LL);
  objc_storeStrong((id *)&self->rowH, 0LL);
  objc_storeStrong((id *)&self->headerH, 0LL);
  objc_storeStrong((id *)&self->columnCount, 0LL);
  objc_storeStrong((id *)&self->rowCount, 0LL);
  objc_storeStrong((id *)&self->super._vFlags, 0LL);
  objc_storeStrong((id *)&self->super._viewAuxiliary, 0LL);
}



import idc
import idautils
import idaapi

# 获取类的地址
# 假设我们知道该类名为 "MyClass" 并且已知它的地址
class_name = "TradeZhuBiDocumentView"
class_ea = idc.get_name_ea_simple(class_name)

if class_ea == idaapi.BADADDR:
    print(f"未找到类: {class_name}")
else:
    print(f"类 {class_name} 地址: {hex(class_ea)}")

    # 获取类的元数据（如方法列表）
    methods = []
    properties = []

    # 这里假设类方法和属性信息存储在 __objc_classlist 和 __objc_methname 段中
    # 解析这些段中的信息，获取函数和属性列表

    # 示例：获取该类的所有方法
    for func_ea in idautils.Functions(class_ea, class_ea + 0x1000):  # 地址区间请根据实际情况调整
        func_name = idc.get_func_name(func_ea)
        methods.append(func_name)

    # 示例：列出该类的属性（若有）
    # 这里你需要通过相关段（如 __objc_propertylist）分析获取属性
    for prop_ea in idautils.DataRefsTo(class_ea):
        prop_name = idc.get_name(prop_ea)
        properties.append(prop_name)

    # 打印结果
    print("方法:")
    for method in methods:
        print(f"  - {method}")

    print("\n属性:")
    for prop in properties:
        print(f"  - {prop}")