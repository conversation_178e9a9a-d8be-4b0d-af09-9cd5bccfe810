void __cdecl -[YiDongCPTPlotSymbol enableDynamicShadow:radius:withCenter:color:](
        YiDongCPTPlotSymbol *self,
        SEL a2,
        double a3,
        double a4,
        CGPoint a5,
        id a6)
{
  YiDongCPTScatterPlot *v7; // rax
  YiDongCPTScatterPlot *v8; // rax
  CALayer *v9; // rax
  CALayer *v10; // rbx
  CALayer *v12; // rax
  CALayer *v13; // rbx
  YiDongCPTScatterPlot *v14; // rax
  YiDongCPTScatterPlot *v15; // r14
  SEL v16; // r12
  SEL v20; // r12
  SEL v23; // r12
  NSValue *v26; // rax
  NSValue *v27; // rax
  NSArray *v31; // rax
  NSArray *v32; // r15
  CALayer *v33; // rax
  CALayer *v34; // rbx
  NSValue *v36; // [rsp+C0h] [rbp-60h]
  _QWORD v39[3]; // [rsp+D8h] [rbp-48h] BYREF

  v6 = _objc_msgSend(a6, "colorWithAlphaComponent:", 0.5);
  v37 = objc_retainAutoreleasedReturnValue(v6);
  v7 = -[YiDongCPTPlotSymbol curLayer](self, "curLayer");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = -[YiDongCPTScatterPlot animationLayer](v8, "animationLayer");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  -[YiDongCPTPlotSymbol setAnimationLayer:](self, "setAnimationLayer:", v10);
  v12 = -[YiDongCPTPlotSymbol animationLayer](self, "animationLayer");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "setCornerRadius:", a4);
  v14 = -[YiDongCPTPlotSymbol curLayer](self, "curLayer");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v17 = _objc_msgSend(self, v16);
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v15, "addSublayer:", v18);
  v38 = objc_retainAutorelease(v37);
  v19 = _objc_msgSend(v38, "CGColor");
  v21 = _objc_msgSend(self, v20);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  _objc_msgSend(v22, "setBackgroundColor:", v19);
  _objc_msgSend(&OBJC_CLASS___CATransaction, "begin");
  _objc_msgSend(&OBJC_CLASS___CATransaction, "setDisableActions:", 1LL);
  v24 = _objc_msgSend(self, v23);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  _objc_msgSend(v25, "setFrame:");
  _objc_msgSend(&OBJC_CLASS___CATransaction, "commit");
  v26 = _objc_msgSend(&OBJC_CLASS___NSValue, "valueWithRect:");
  v36 = objc_retainAutoreleasedReturnValue(v26);
  v27 = _objc_msgSend(&OBJC_CLASS___NSValue, "valueWithRect:");
  objc_retainAutoreleasedReturnValue(v27);
  v28 = _objc_msgSend(&OBJC_CLASS___CAKeyframeAnimation, "animation");
  v29 = objc_retainAutoreleasedReturnValue(v28);
  _objc_msgSend(v29, "setKeyPath:", CFSTR("bounds"));
  v39[0] = v30;
  v39[1] = v36;
  v39[2] = v30;
  v31 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v39, 3LL);
  v32 = objc_retainAutoreleasedReturnValue(v31);
  _objc_msgSend(v29, "setValues:", v32);
  _objc_msgSend(v29, "setRepeatCount:", COERCE_DOUBLE(2139095039LL));
  _objc_msgSend(v29, "setDuration:", 3.0);
  v33 = -[YiDongCPTPlotSymbol animationLayer](self, "animationLayer");
  v34 = objc_retainAutoreleasedReturnValue(v33);
  _objc_msgSend(v34, "addAnimation:forKey:", v29, 0LL);
}

//----- (000000010000A631) ----------------------------------------------------
YiDongListInfoResult *__cdecl -[YiDongCPTPlotSymbol dataInfo](YiDongCPTPlotSymbol *self, SEL a2)
{
  return self->_dataInfo;
}

//----- (000000010000A642) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setDataInfo:](YiDongCPTPlotSymbol *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_dataInfo, a3);
}

//----- (000000010000A656) ----------------------------------------------------
YiDongCPTScatterPlot *__cdecl -[YiDongCPTPlotSymbol curLayer](YiDongCPTPlotSymbol *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_curLayer);
  return (YiDongCPTScatterPlot *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010000A66F) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setCurLayer:](YiDongCPTPlotSymbol *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_curLayer, a3);
}

//----- (000000010000A683) ----------------------------------------------------
CALayer *__cdecl -[YiDongCPTPlotSymbol animationLayer](YiDongCPTPlotSymbol *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_animationLayer);
  return (CALayer *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (000000010000A69C) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setAnimationLayer:](YiDongCPTPlotSymbol *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_animationLayer, a3);
}

//----- (000000010000A6B0) ----------------------------------------------------
CGRect *__cdecl -[YiDongCPTPlotSymbol yiDongPointFrame](CGRect *retstr, YiDongCPTPlotSymbol *self, SEL a3)
{
  CGRect *result; // rax
  CGSize size; // xmm1

  result = retstr;
  size = self->_yiDongPointFrame.size;
  retstr->origin = self->_yiDongPointFrame.origin;
  retstr->size = size;
  return result;
}

//----- (000000010000A6D0) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setYiDongPointFrame:](YiDongCPTPlotSymbol *self, SEL a2, CGRect a3)
{
  self->_yiDongPointFrame = a3;
}

//----- (000000010000A6EE) ----------------------------------------------------
CGRect *__cdecl -[YiDongCPTPlotSymbol yiDongFlagFrame](CGRect *retstr, YiDongCPTPlotSymbol *self, SEL a3)
{
  CGRect *result; // rax
  CGSize size; // xmm1

  result = retstr;
  size = self->_yiDongFlagFrame.size;
  retstr->origin = self->_yiDongFlagFrame.origin;
  retstr->size = size;
  return result;
}

//----- (000000010000A70E) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setYiDongFlagFrame:](YiDongCPTPlotSymbol *self, SEL a2, CGRect a3)
{
  self->_yiDongFlagFrame = a3;
}

//----- (000000010000A72C) ----------------------------------------------------
CGPoint __cdecl -[YiDongCPTPlotSymbol center](YiDongCPTPlotSymbol *self, SEL a2)
{
  CGPoint result; // xmm1_8:xmm0_8

  x = self->_center.x;
  y = self->_center.y;
  result.y = y;
  result.x = x;
  return result;
}

//----- (000000010000A744) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol setCenter:](YiDongCPTPlotSymbol *self, SEL a2, CGPoint a3)
{
  self->_center.x = a3.x;
  self->_center.y = a3.y;
}

//----- (000000010000A75C) ----------------------------------------------------
void __cdecl -[YiDongCPTPlotSymbol .cxx_destruct](YiDongCPTPlotSymbol *self, SEL a2)
{
  objc_destroyWeak((id *)&self->_animationLayer);
  objc_destroyWeak((id *)&self->_curLayer);
  objc_storeStrong((id *)&self->_dataInfo, 0LL);
}

