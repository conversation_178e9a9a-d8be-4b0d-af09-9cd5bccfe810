//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSArray, NSNumber, NSString;

@interface UserNotifyEntity : NSObject
{
    NSString *_version;
    NSString *_mtime;
    NSString *_name;
    NSArray *_textArray;
    NSArray *_imageArray;
    NSString *_mtext;
    NSString *_mbtnTitle;
    NSString *_adurl;
    NSNumber *_isvalid;
}


@property(retain, nonatomic) NSNumber *isvalid; // @synthesize isvalid=_isvalid;
@property(retain, nonatomic) NSString *adurl; // @synthesize adurl=_adurl;
@property(retain, nonatomic) NSString *mbtnTitle; // @synthesize mbtnTitle=_mbtnTitle;
@property(retain, nonatomic) NSString *mtext; // @synthesize mtext=_mtext;
@property(copy, nonatomic) NSArray *imageArray; // @synthesize imageArray=_imageArray;
@property(copy, nonatomic) NSArray *textArray; // @synthesize textArray=_textArray;
@property(copy, nonatomic) NSString *name; // @synthesize name=_name;
@property(copy, nonatomic) NSString *mtime; // @synthesize mtime=_mtime;
@property(retain, nonatomic) NSString *version; // @synthesize version=_version;
- (void)analyseSurveyDataDic:(id)arg1;
- (void)analyseDataDic:(id)arg1;

@end

