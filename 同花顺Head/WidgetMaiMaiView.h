//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import "JYMaiMaiView.h"

@class HXBaseTradeView, HXBaseView, HXVerticallyCenteredTextField, QueryWTCheDanRequest, WTButton, YYTimer;

@interface WidgetMaiMaiView : JYMaiMaiView
{
    CDUnknownBlockType _jumpActionBlock;
    WTButton *_cheButton;
    WTButton *_chaButton;
    HXBaseView *_badgeView;
    HXVerticallyCenteredTextField *_badgeLabel;
    YYTimer *_timer;
    long long _kecheCount;
    QueryWTCheDanRequest *_WTCheDanRequest;
    HXBaseTradeView *_baseView;
}


@property __weak HXBaseTradeView *baseView; // @synthesize baseView=_baseView;
@property(retain, nonatomic) QueryWTCheDanRequest *WTCheDanRequest; // @synthesize WTCheDanRequest=_WTCheDanRequest;
@property(nonatomic) long long kecheCount; // @synthesize kecheCount=_kecheCount;
@property(retain, nonatomic) YYTimer *timer; // @synthesize timer=_timer;
@property __weak HXVerticallyCenteredTextField *badgeLabel; // @synthesize badgeLabel=_badgeLabel;
@property __weak HXBaseView *badgeView; // @synthesize badgeView=_badgeView;
@property __weak WTButton *chaButton; // @synthesize chaButton=_chaButton;
@property __weak WTButton *cheButton; // @synthesize cheButton=_cheButton;
@property(copy, nonatomic) CDUnknownBlockType jumpActionBlock; // @synthesize jumpActionBlock=_jumpActionBlock;
- (void)dealloc;
- (void)hideBadge;
- (void)showBadgeWith:(long long)arg1;
- (void)chaClickAction:(id)arg1;
- (void)cheClickAction:(id)arg1;
- (void)queryCheDanInfo;
- (void)timeOutHandle;
- (void)timerInvalidate;
- (void)startRequestTimer;
- (void)onChedanNogtify:(id)arg1;
- (void)onWeituoNotify:(id)arg1;
- (void)initButtonColor:(id)arg1;
- (void)awakeFromNib;
- (void)didDisappear;
- (void)didAppear;

@end

