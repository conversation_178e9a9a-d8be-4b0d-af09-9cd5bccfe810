void __cdecl -[ZhiBiaoGuPageContainerController viewDidLoad](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  HXPageMainGraphTabContainer *v2; // rax
  HXPageMainGraphTabContainer *v3; // r14
  NSArray *v4; // rax
  NSArray *v5; // rbx
  __CFString *v7; // [rsp+18h] [rbp-28h] BYREF

  v6.receiver = self;
  v6.super_class = (Class)&OBJC_CLASS___ZhiBiaoGuPageContainerController;
  -[HXPageBaseViewController viewDidLoad](&v6, "viewDidLoad");
  v2 = -[HXPageBaseViewController mainGraphNavigationVC](self, "mainGraphNavigationVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v7 = CFSTR("诊股");
  v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v7, 1LL);
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[HXPageMainGraphTabContainer disable:forTitles:](v3, "disable:forTitles:", 1LL, v5);
}

//----- (0000000100B4C032) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuPageContainerController nibName](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  return CFSTR("HXPageBaseViewController");
}

//----- (0000000100B4C03F) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController initCustomZiXunModuleVC](
        ZhiBiaoGuPageContainerController *self,
        SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // r15
  id (*v4)(id, SEL, ...); // r12
  _QWORD v8[3]; // [rsp+0h] [rbp-40h] BYREF

  v8[0] = CFSTR("新闻资讯");
  v8[1] = CFSTR("板块热点");
  v8[2] = CFSTR("股市便签");
  v2 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v8, 3LL);
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(self, "ziXunModuleVC");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7(v6, "setMenuTitles:", v3);
}

//----- (0000000100B4C108) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController updatePageControllerView](
        ZhiBiaoGuPageContainerController *self,
        SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx

  v2 = -[HXBaseViewController market](self, "market");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "length");
  if ( v4 )
  {
    v5.receiver = self;
    v5.super_class = (Class)&OBJC_CLASS___ZhiBiaoGuPageContainerController;
    -[HXPageBaseViewController updatePageControllerView](&v5, "updatePageControllerView");
  }
}

//----- (0000000100B4C17B) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController actionForSelfStockUpdate](
        ZhiBiaoGuPageContainerController *self,
        SEL a2)
{
  ;
}

//----- (0000000100B4C181) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuPageContainerController splitBottomViewHeightName](
        ZhiBiaoGuPageContainerController *self,
        SEL a2)
{
  return CFSTR("splitBottomViewHeightZhiBiaoGu");
}

//----- (0000000100B4C18E) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuPageContainerController splitRightViewWidthName](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  return CFSTR("splitRightViewWidthZhiBiaoGu");
}

//----- (0000000100B4C19B) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuPageContainerController strGeGuPankouType](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  return CFSTR("NormalHuShenIndexPanKou");
}

//----- (0000000100B4C1A8) ----------------------------------------------------
id __cdecl -[ZhiBiaoGuPageContainerController FSLineVC](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  HXRightAccessaryViewController *rightAccessaryVC; // rdi
  HXBaseView *v4; // rax
  HXBaseView *v5; // rbx
  HXRightAccessaryViewController *v7; // rax
  HXRightAccessaryViewController *v8; // rdi
  HXBaseView *v9; // rax
  HXRightAccessaryViewController *v16; // rbx
  id *v17; // r12
  HXRightAccessaryViewController *v18; // rdi
  _QWORD v19[4]; // [rsp+28h] [rbp-78h] BYREF
  id to; // [rsp+48h] [rbp-58h] BYREF
  id location[6]; // [rsp+70h] [rbp-30h] BYREF

  rightAccessaryVC = self->super._rightAccessaryVC;
  if ( !rightAccessaryVC )
  {
    v4 = -[HXPageBaseViewController viewForFK](self, "viewForFK");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    if ( !v5 )
      return objc_autoreleaseReturnValue(0LL);
    v6 = objc_alloc((Class)&OBJC_CLASS___FenShiCallAuctionGraphViewController);
    v7 = (HXRightAccessaryViewController *)_objc_msgSend(
                                             v6,
                                             "initWithNibName:bundle:",
                                             CFSTR("FenShiCallAuctionGraphViewController"),
                                             0LL);
    v8 = self->super._rightAccessaryVC;
    self->super._rightAccessaryVC = v7;
    v9 = -[HXPageBaseViewController viewForFK](self, "viewForFK");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = (char *)v10;
    if ( v10 )
      objc_msgSend_stret(v21, v10, "bounds");
    else
      memset(v21, 0, sizeof(v21));
    v13 = _objc_msgSend(self->super._rightAccessaryVC, "view");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v14, "setFrame:");
    v15(v11);
    objc_initWeak(location, self);
    v16 = self->super._rightAccessaryVC;
    v19[0] = _NSConcreteStackBlock;
    v19[1] = 3254779904LL;
    v19[2] = sub_100B4C369;
    v19[3] = &unk_1012DAF38;
    objc_copyWeak(&to, v17);
    _objc_msgSend(v16, "setSwitchFenShiCycleBlock:", v19);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    rightAccessaryVC = self->super._rightAccessaryVC;
  }
  v18 = objc_retain(rightAccessaryVC);
  return objc_autoreleaseReturnValue(v18);
}

//----- (0000000100B4C369) ----------------------------------------------------
void __fastcall sub_100B4C369(__int64 a1, __int64 a2)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "switchFenShiCycleFromFenShi:", a2);
}

//----- (0000000100B4C39F) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController deleteOrderForPanKou](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  ;
}

//----- (0000000100B4C3A5) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController setFSLineVC:](ZhiBiaoGuPageContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._rightAccessaryVC, a3);
}

//----- (0000000100B4C3B9) ----------------------------------------------------
void __cdecl -[ZhiBiaoGuPageContainerController .cxx_destruct](ZhiBiaoGuPageContainerController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._rightAccessaryVC, 0LL);
}

