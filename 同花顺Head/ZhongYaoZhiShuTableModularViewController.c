void __cdecl -[<PERSON><PERSON><PERSON><PERSON><PERSON>hiShuTableModularViewController viewDidLoad](
        Zhong<PERSON>aoZhiShuTableModularViewController *self,
        SEL a2)
{
  HXBaseScrollView *v2; // rax
  HXBaseScrollView *v3; // rbx

  v4.receiver = self;
  v4.super_class = (Class)&OBJC_CLASS___ZhongYaoZhiShuTableModularViewController;
  -[GangGuZhiShuRightBottomViewController viewDidLoad](&v4, "viewDidLoad");
  v2 = -[ZhongYaoZhiShuTableModularViewController zhongYaoZhiShuScrollView](self, "zhongYaoZhiShuScrollView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  -[HXBaseScrollView setDisEnableScroll:](v3, "setDisEnableScroll:", 1LL);
}

//----- (0000000100120D7A) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableModularViewController requestForTableViewData](
        ZhongYaoZhiShuTableModularViewController *self,
        SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZhongYaoZhiShuTableModularViewController;
  -[GangGuZhiShuRightBottomViewController requestForMyTable](&v2, "requestForMyTable");
}

//----- (0000000100120DA9) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableModularViewController tableViewSelectionIsChanging:](
        ZhongYaoZhiShuTableModularViewController *self,
        SEL a2,
        id a3)
{
  HXBaseScrollView *v3; // rax
  HXBaseScrollView *v4; // rbx

  v3 = -[ZhongYaoZhiShuTableModularViewController zhongYaoZhiShuScrollView](self, "zhongYaoZhiShuScrollView", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  -[HXBaseScrollView setDisEnableScroll:](v4, "setDisEnableScroll:", 0LL);
}

//----- (0000000100120DE8) ----------------------------------------------------
HXBaseScrollView *__cdecl -[ZhongYaoZhiShuTableModularViewController zhongYaoZhiShuScrollView](
        ZhongYaoZhiShuTableModularViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.super._currentTable);
  return (HXBaseScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100120E01) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableModularViewController setZhongYaoZhiShuScrollView:](
        ZhongYaoZhiShuTableModularViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak(&self->super.super._currentTable, a3);
}

//----- (0000000100120E15) ----------------------------------------------------
void __cdecl -[ZhongYaoZhiShuTableModularViewController .cxx_destruct](
        ZhongYaoZhiShuTableModularViewController *self,
        SEL a2)
{
  objc_destroyWeak(&self->super.super._currentTable);
}

