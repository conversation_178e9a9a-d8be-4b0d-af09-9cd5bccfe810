//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Sep 17 2017 16:24:48).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2015 by <PERSON>.
//

#import <objc/NSObject.h>

@class NSArray, NSMutableArray;

@interface XingTaiDongChaDataParseModule : NSObject
{
    NSMutableArray *_modelArray;
    NSArray *_ohlcArr;
    double _yLocation;
    double _yLength;
}


@property(nonatomic) double yLength; // @synthesize yLength=_yLength;
@property(nonatomic) double yLocation; // @synthesize yLocation=_yLocation;
@property(retain, nonatomic) NSArray *ohlcArr; // @synthesize ohlcArr=_ohlcArr;
@property(retain, nonatomic) NSMutableArray *modelArray; // @synthesize modelArray=_modelArray;
- (void)calculateMutipleDayRectangle:(id)arg1 model:(id)arg2 trend:(id)arg3;
- (id)getIntervalDataWithStart:(long long)arg1 end:(long long)arg2;
- (id)sortGraphModel:(id)arg1;
- (void)setDrawerModelWithRequestData:(id)arg1 ohlcArr:(id)arg2 yLocation:(id)arg3 yLength:(id)arg4 isRequestNew:(BOOL)arg5;
- (void)clearCacheData;
- (id)getModels;

@end

