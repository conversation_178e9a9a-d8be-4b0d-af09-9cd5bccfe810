void __cdecl -[Zi<PERSON>unKeyboradSpiritViewController viewDidLoad](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSTableView *v2; // rax
  NSTableView *v3; // rbx
  SEL v4; // r12
  NSTableView *v8; // rax
  NSTableView *v9; // rbx

  v21.receiver = self;
  v21.super_class = (Class)&OBJC_CLASS___ZiXunKeyboradSpiritViewController;
  objc_msgSendSuper2(&v21, "viewDidLoad");
  v2 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setTarget:", self);
  v5 = _objc_msgSend(self, v4);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setDoubleAction:", "keyboardSpiritRowDoubleClicked");
  v7 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  objc_retainAutoreleasedReturnValue(v7);
  v8 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setBackgroundColor:", v10);
  v12 = _objc_msgSend(self, "view");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(&OBJC_CLASS___HXBaseView, "class");
  v15 = (unsigned __int8)_objc_msgSend(v13, "isKindOfClass:", v14);
  if ( v15 )
  {
    v17 = _objc_msgSend(self, v16);
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
    v20 = objc_retainAutoreleasedReturnValue(v19);
    _objc_msgSend(v18, "setBackgroundColor:", v20);
  }
}

//----- (000000010062160E) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController viewWillAppear](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12

  v17.receiver = self;
  v17.super_class = (Class)&OBJC_CLASS___ZiXunKeyboradSpiritViewController;
  objc_msgSendSuper2(&v17, "viewWillAppear");
  v2 = -[ZiXunKeyboradSpiritViewController resultMArray](self, "resultMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "count");
  if ( v5 )
  {
    v7 = v6(self, "keyboardTable");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v8, "reloadData");
    v11 = v10(self, "keyboardTable");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = v13(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", 0LL);
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16(v12, "selectRowIndexes:byExtendingSelection:", v15, 0LL);
  }
}

//----- (0000000100621715) ----------------------------------------------------
NSMutableArray *__cdecl -[ZiXunKeyboradSpiritViewController resultMArray](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2)
{
  NSArray *topLevelObjects; // rdi
  NSArray *v5; // rax
  NSArray *v6; // rdi

  topLevelObjects = self->super._topLevelObjects;
  if ( !topLevelObjects )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "array");
    v5 = (NSArray *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->super._topLevelObjects;
    self->super._topLevelObjects = v5;
    topLevelObjects = self->super._topLevelObjects;
  }
  return (NSMutableArray *)objc_retainAutoreleaseReturnValue(topLevelObjects);
}

//----- (0000000100621766) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController voiceRequest:](ZiXunKeyboradSpiritViewController *self, SEL a2, id a3)
{
  id WeakRetained; // rax

  objc_retain(a3);
  -[ZiXunKeyboradSpiritViewController setAutoJump:](self, "setAutoJump:", 1LL);
  v4 = _objc_msgSend(v3, "uppercaseString");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  _objc_msgSend(WeakRetained, "setStringValue:", v5);
  v9 = objc_loadWeakRetained((id *)&self->super._nibName);
  _objc_msgSend(v9, "codeBecomeFirstResponder");
  v11 = objc_loadWeakRetained((id *)&self->super._nibName);
  v12 = _objc_msgSend(v11, "currentEditor");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "moveToEndOfLine:", 0LL);
  -[ZiXunKeyboradSpiritViewController requestWithPattern:](self, "requestWithPattern:", v5);
}

//----- (000000010062187D) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController textDidChange](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  id WeakRetained; // rbx
  id (*v3)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12

  -[ZiXunKeyboradSpiritViewController setAutoJump:](self, "setAutoJump:", 0LL);
  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  v4 = v3(WeakRetained, "stringValue");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( v6(v5, "length") )
    -[ZiXunKeyboradSpiritViewController requestWithPattern:](self, "requestWithPattern:", v5);
}

//----- (0000000100621907) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController keyDownThenRequest:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  id WeakRetained; // rax

  objc_retain(a3);
  -[ZiXunKeyboradSpiritViewController setAutoJump:](self, "setAutoJump:", 0LL);
  v4 = _objc_msgSend(v3, "uppercaseString");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  _objc_msgSend(WeakRetained, "setStringValue:", v5);
  v9 = objc_loadWeakRetained((id *)&self->super._nibName);
  _objc_msgSend(v9, "codeBecomeFirstResponder");
  v11 = objc_loadWeakRetained((id *)&self->super._nibName);
  v12 = _objc_msgSend(v11, "currentEditor");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  _objc_msgSend(v13, "moveToEndOfLine:", 0LL);
  -[ZiXunKeyboradSpiritViewController requestWithPattern:](self, "requestWithPattern:", v5);
}

//----- (0000000100621A1B) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController requestWithPattern:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  NSView *v5; // rax
  NSNumber *v6; // rax
  NSDictionary *v8; // rax
  DataRequestCenter *v9; // rax
  DataRequestCenter *v10; // r14
  id *v12; // r12
  _QWORD v13[4]; // [rsp+8h] [rbp-98h] BYREF
  _QWORD v15[4]; // [rsp+30h] [rbp-70h] BYREF
  id to; // [rsp+58h] [rbp-48h] BYREF
  id location[6]; // [rsp+70h] [rbp-30h] BYREF

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "length") )
  {
    -[ZiXunKeyboradSpiritViewController clear](self, "clear");
    v4 = _objc_msgSend(v3, "uppercaseString");
    objc_retainAutoreleasedReturnValue(v4);
    v5 = +[HXTools getTenBillionLongFromTimeStamp](&OBJC_CLASS___HXTools, "getTenBillionLongFromTimeStamp");
    self->super.view = v5;
    v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v5);
    v18 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(
           &OBJC_CLASS___NSDictionary,
           "dictionaryWithObjectsAndKeys:",
           v7,
           CFSTR("SearchPattern"),
           v18,
           CFSTR("InstanceKey"),
           0LL);
    v19 = objc_retainAutoreleasedReturnValue(v8);
    objc_initWeak(location, self);
    v9 = +[DataRequestCenter sharedInstance](&OBJC_CLASS___DataRequestCenter, "sharedInstance");
    v10 = objc_retain(v9);
    v15[0] = _NSConcreteStackBlock;
    v15[1] = 3254779904LL;
    v15[2] = sub_100621C5D;
    v15[3] = &unk_1012DC068;
    objc_copyWeak(&to, location);
    v3 = objc_retain(v11);
    v16 = v3;
    v13[0] = _NSConcreteStackBlock;
    v13[1] = 3254779904LL;
    v13[2] = sub_100621D76;
    v13[3] = &unk_1012DAF38;
    objc_copyWeak(&v14, location);
    -[DataRequestCenter request:params:encodingType:callBack:fail:](
      v10,
      "request:params:encodingType:callBack:fail:",
      8LL,
      v19,
      1LL,
      v15,
      v13);
    objc_destroyWeak(v12);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (0000000100621C5D) ----------------------------------------------------
void __fastcall sub_100621C5D(__int64 a1, void *a2)
{
  id WeakRetained; // r15
  unsigned __int64 v7; // rbx

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___PCTXTDataModel, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    v11 = objc_retain(v2);
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 40));
    v10 = _objc_msgSend(WeakRetained, "longFromTime");
    v5 = _objc_msgSend(v11, "head");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = (unsigned int)_objc_msgSend(v6, "instanceId");
    if ( v10 == (id)v7 )
    {
      v9 = objc_loadWeakRetained((id *)(a1 + 40));
      _objc_msgSend(v9, "parseSearchData:pattern:", v11, *(_QWORD *)(a1 + 32));
    }
  }
}

//----- (0000000100621D76) ----------------------------------------------------
void __fastcall sub_100621D76(__int64 a1)
{
  id WeakRetained; // rbx

  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "reloadData");
}

//----- (0000000100621DA7) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController parseSearchData:pattern:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ZiXunKeyboradSpiritViewController *v4; // rbx
  unsigned __int64 v8; // rax
  ZiXunKeyboradSpiritViewController *v22; // r15
  unsigned __int64 v36; // r13
  SEL *v41; // rax
  SEL v46; // [rsp+48h] [rbp-108h]
  SEL v50; // [rsp+68h] [rbp-E8h]
  id obj; // [rsp+90h] [rbp-C0h]
  SEL v56; // [rsp+98h] [rbp-B8h]

  v4 = self;
  v5 = objc_retain(a3);
  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    v6 = objc_alloc(&OBJC_CLASS___NSString);
    v7 = _objc_msgSend(v5, "txtData");
    objc_retainAutoreleasedReturnValue(v7);
    v8 = CFStringConvertEncodingToNSStringEncoding(0x632u);
    v10 = _objc_msgSend(v6, "initWithData:encoding:", v9, v8);
    v12 = _objc_msgSend(v10, "componentsSeparatedByString:", &stru_10131D2F8);
    v13 = objc_retainAutoreleasedReturnValue(v12);
    if ( !_objc_msgSend(v13, "count") )
    {
LABEL_23:
      goto LABEL_24;
    }
    v53 = v5;
    v54 = v14;
    v15 = _objc_msgSend(v14, "objectAtIndexedSubscript:", 0LL);
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v52 = v16;
    v17 = _objc_msgSend(v16, "componentsSeparatedByString:", CFSTR("\n"));
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v42 = 0LL;
    v43 = 0LL;
    v44 = 0LL;
    v45 = 0LL;
    obj = objc_retain(v18);
    v19 = (char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v42, v57, 16LL);
    if ( v19 )
    {
      v20 = v19;
      v48 = *(_QWORD *)v43;
      v51 = self;
      while ( 2 )
      {
        v56 = "resultMArray";
        v46 = "containsObject:";
        v50 = "addObject:";
        v21 = 0LL;
        v47 = v20;
        do
        {
          if ( *(_QWORD *)v43 != v48 )
            objc_enumerationMutation(obj);
          v22 = v4;
          v23 = _objc_msgSend(v4, v56);
          v24 = objc_retainAutoreleasedReturnValue(v23);
          v26 = (unsigned __int8)_objc_msgSend(v24, v46, v25);
          if ( !v26 )
          {
            v28 = _objc_msgSend(v27, "componentsSeparatedByString:", CFSTR("|"));
            v29 = objc_retainAutoreleasedReturnValue(v28);
            if ( (unsigned __int64)_objc_msgSend(v29, "count") >= 3 )
            {
              v49 = v29;
              v30 = _objc_msgSend(v22, v56);
              v31 = objc_retainAutoreleasedReturnValue(v30);
              _objc_msgSend(v31, v50, v32);
              v33 = _objc_msgSend(v51, v56);
              v34 = objc_retainAutoreleasedReturnValue(v33);
              v35 = _objc_msgSend(v34, "count");
              v29 = v49;
              v36 = (unsigned __int64)v35;
              if ( v36 > 0xA )
              {
                v4 = v22;
                goto LABEL_17;
              }
            }
          }
          ++v21;
          v4 = v22;
        }
        while ( v47 != v21 );
        v20 = (char *)_objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v42, v57, 16LL);
        if ( v20 )
          continue;
        break;
      }
    }
    else
    {
      v56 = "resultMArray";
    }
LABEL_17:
    v38 = _objc_msgSend(v4, v56);
    v39 = objc_retainAutoreleasedReturnValue(v38);
    if ( _objc_msgSend(v39, "count") )
    {
      v40 = (unsigned __int8)-[ZiXunKeyboradSpiritViewController autoJump](v4, "autoJump");
      v5 = v53;
      if ( v40 == 1 )
      {
        v41 = &selRef_voiceAutoJump;
LABEL_22:
        _objc_msgSend(v4, *v41);
        v10 = v52;
        goto LABEL_23;
      }
    }
    else
    {
      v5 = v53;
    }
    v41 = &selRef_reloadData;
    goto LABEL_22;
  }
LABEL_24:
}

//----- (000000010062221F) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController voiceAutoJump](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12

  v2 = -[ZiXunKeyboradSpiritViewController resultMArray](self, "resultMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "firstObject");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v8 = v7(&OBJC_CLASS___NSString, "class");
  if ( (unsigned __int8)v9(v6, "isKindOfClass:", v8) && _objc_msgSend(v6, "length") )
  {
    v11 = v10(v6, "componentsSeparatedByString:", CFSTR("|"));
    v12 = objc_retainAutoreleasedReturnValue(v11);
    if ( (unsigned __int64)v13(v12, "count") >= 3 )
    {
      v15 = v14(v12, "objectAtIndexedSubscript:", 0LL);
      v28 = objc_retainAutoreleasedReturnValue(v15);
      v17 = v16(v12, "objectAtIndexedSubscript:", 2LL);
      v29 = objc_retainAutoreleasedReturnValue(v17);
      v19 = v18(self, "searchResultBlock");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      if ( v20 )
      {
        v22 = -[ZiXunKeyboradSpiritViewController searchResultBlock](self, "searchResultBlock");
        v23 = (void (__fastcall **)(id, id, id))objc_retainAutoreleasedReturnValue(v22);
        v23[2](v23, v28, v29);
      }
      v21(self, "sendUserLog:", v6);
      v25 = v24(&OBJC_CLASS___ZiXunKeyboardSpiritWindowController, "sharedInstance");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      v27(v26, "close");
    }
  }
}

//----- (00000001006223EC) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController clear](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx

  v2 = -[ZiXunKeyboradSpiritViewController resultMArray](self, "resultMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeAllObjects");
}

//----- (0000000100622429) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController reloadData](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // rbx
  NSTableView *v7; // rax
  NSTableView *v8; // rbx
  NSTableView *v9; // rax
  NSTableView *v10; // r14
  NSIndexSet *v11; // rax
  NSIndexSet *v12; // rbx
  NSIndexSet *v13; // rdi
  NSTableView *v16; // rax
  NSTableView *v17; // rbx
  NSDictionary *v21; // rax
  NSDictionary *v22; // rbx
  SEL v23; // r12
  id WeakRetained; // [rsp+8h] [rbp-58h]
  __CFString *v28; // [rsp+10h] [rbp-50h] BYREF
  __CFString *v30; // [rsp+20h] [rbp-40h] BYREF
  NSDictionary *v31; // [rsp+28h] [rbp-38h] BYREF

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    v3 = -[ZiXunKeyboradSpiritViewController resultMArray](self, "resultMArray");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = _objc_msgSend(v4, "count");
    if ( v5 )
    {
      representedObject = (void (__fastcall **)(id, _QWORD))self->super._representedObject;
      if ( representedObject )
        representedObject[2](representedObject, 0LL);
      v7 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      _objc_msgSend(v8, "reloadData");
      v9 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      v11 = _objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", 0LL);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      _objc_msgSend(v10, "selectRowIndexes:byExtendingSelection:", v12, 0LL);
      v13 = v12;
      v15 = v14;
      v14(v13);
      ((void (__fastcall *)(NSTableView *))v15)(v10);
    }
    else
    {
      v16 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
      v17 = objc_retainAutoreleasedReturnValue(v16);
      _objc_msgSend(v17, "reloadData");
      v18 = (void (__fastcall **)(id, __int64))self->super._representedObject;
      if ( v18 )
        v18[2](v18, 1LL);
      v30 = CFSTR("keyValuePairKey");
      v28 = CFSTR("搜索内容");
      WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
      v19 = _objc_msgSend(WeakRetained, "stringValue");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v29 = v20;
      v21 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v29, &v28, 1LL);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v31 = v22;
      v24 = _objc_msgSend(&OBJC_CLASS___NSDictionary, v23, &v31, &v30, 1LL);
      objc_retainAutoreleasedReturnValue(v24);
      +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
        &OBJC_CLASS___UserLogSendingQueueManager,
        "sendUserLog:action:params:needWait:",
        11LL,
        CFSTR("全局.无结果页_无结果搜索内容_搜索框"),
        v25,
        1LL);
    }
  }
}

//----- (00000001006226B1) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController sendUserLog:](ZiXunKeyboradSpiritViewController *self, SEL a2, id a3)
{
  SEL v3; // r12
  id WeakRetained; // r13
  NSDictionary *v9; // rax
  NSDictionary *v10; // rbx
  NSDictionary *v11; // rax
  NSDictionary *v12; // r14
  _QWORD v20[2]; // [rsp+20h] [rbp-50h] BYREF
  __CFString *v21; // [rsp+30h] [rbp-40h] BYREF
  NSDictionary *v22; // [rsp+38h] [rbp-38h] BYREF

  v19 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSString, v3, CFSTR("全局.%@搜素_搜索内容_搜索框"), CFSTR("重大事件"));
  v18 = objc_retainAutoreleasedReturnValue(v4);
  v21 = CFSTR("keyValuePairKey");
  v20[0] = v19;
  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  v6 = _objc_msgSend(WeakRetained, "stringValue");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v20[1] = v7;
  v9 = (NSDictionary *)_objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v20, v8, 2LL);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v22 = v10;
  v11 = (NSDictionary *)_objc_msgSend(
                          &OBJC_CLASS___NSDictionary,
                          "dictionaryWithObjects:forKeys:count:",
                          &v22,
                          &v21,
                          1LL);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13(v7);
  v14(WeakRetained);
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    v18,
    v12,
    1LL);
  v15(v19);
  v16(v12);
  v17(v18);
}

//----- (0000000100622851) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController keyboardSpiritRowDoubleClicked](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2)
{
  NSString **p_title; // r15
  id WeakRetained; // rbx
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v14)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12

  p_title = &self->super._title;
  WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
  v4 = _objc_msgSend(WeakRetained, "selectedRow");
  if ( (__int64)v4 >= 0 )
  {
    v5 = objc_loadWeakRetained((id *)p_title);
    _objc_msgSend(v5, "numberOfRows");
    if ( (__int64)v4 < v6 )
    {
      v7 = objc_loadWeakRetained((id *)p_title);
      v8 = _objc_msgSend(v7, "viewAtColumn:row:makeIfNecessary:", 0LL, v4, 0LL);
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v11 = v10(v9, "stringValue");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      if ( v13(v12, "length") )
      {
        v38 = v9;
        v15 = v14(v12, "componentsSeparatedByString:", CFSTR("|"));
        v16 = objc_retainAutoreleasedReturnValue(v15);
        if ( (unsigned __int64)v17(v16, "count") >= 3 )
        {
          v19 = v18(v16, "objectAtIndexedSubscript:", 0LL);
          v39 = objc_retainAutoreleasedReturnValue(v19);
          v21 = v20(v16, "objectAtIndexedSubscript:", 2LL);
          v40 = objc_retainAutoreleasedReturnValue(v21);
          v23 = v22(self, "searchResultBlock");
          v24 = objc_retainAutoreleasedReturnValue(v23);
          if ( v24 )
          {
            v26 = -[ZiXunKeyboradSpiritViewController searchResultBlock](self, "searchResultBlock");
            v27 = (void (__fastcall **)(id, id, id))objc_retainAutoreleasedReturnValue(v26);
            v27[2](v27, v39, v40);
          }
          v25(self, "sendUserLog:", v12);
          v29 = v28(self, "keyboardTextField");
          v30 = objc_retainAutoreleasedReturnValue(v29);
          v32 = v31(v30, "window");
          v33 = objc_retainAutoreleasedReturnValue(v32);
          v34(v33, "makeFirstResponder:", 0LL);
          v35(v30);
          v36(v40);
          v37(v39);
        }
        v9 = v38;
      }
    }
  }
}

//----- (0000000100622AB1) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController selectPrevious](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSTableView *v2; // rax
  NSTableView *v3; // rbx
  id WeakRetained; // rbx
  NSTableView *v10; // rax
  NSTableView *v11; // r14
  NSIndexSet *v13; // rax
  NSIndexSet *v14; // rbx

  v2 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "selectedRow");
  if ( v4 > 0 )
  {
    v5 = v4 - 1;
    WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
    v7 = (unsigned __int8)-[ZiXunKeyboradSpiritViewController tableView:shouldSelectRow:](
                            self,
                            "tableView:shouldSelectRow:",
                            WeakRetained,
                            v5);
    v9 = v8 - 2;
    if ( v7 )
      v9 = v5;
    if ( v9 >= 0 )
    {
      v10 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      v13 = _objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", v12);
      v14 = objc_retainAutoreleasedReturnValue(v13);
      _objc_msgSend(v11, "selectRowIndexes:byExtendingSelection:", v14, 0LL);
    }
  }
}

//----- (0000000100622BCB) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController selectNext](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  NSTableView *v2; // rax
  NSTableView *v3; // r15
  id (*v4)(id, SEL, ...); // r12
  id (*v7)(id, SEL, ...); // r12
  id WeakRetained; // rbx
  id (*v14)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  NSTableView *v20; // rax
  NSTableView *v21; // r14
  NSIndexSet *v22; // rax
  NSIndexSet *v23; // rbx

  v2 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (char *)v4(v3, "selectedRow");
  v24 = v5;
  v6 = v5 + 1;
  v8 = v7(self, "keyboardTable");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v9, "numberOfRows");
  if ( (__int64)(v5 + 1) < v11 )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
    v13 = (unsigned __int8)-[ZiXunKeyboradSpiritViewController tableView:shouldSelectRow:](
                             self,
                             "tableView:shouldSelectRow:",
                             WeakRetained,
                             v6);
    v15 = (__int64)(v24 + 2);
    if ( v13 )
      v15 = (__int64)v6;
    v16 = v14(self, "keyboardTable");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = v18(v17, "numberOfRows");
    if ( v15 < (__int64)v19 )
    {
      v20 = -[ZiXunKeyboradSpiritViewController keyboardTable](self, "keyboardTable");
      v21 = objc_retainAutoreleasedReturnValue(v20);
      v22 = _objc_msgSend(&OBJC_CLASS___NSIndexSet, "indexSetWithIndex:", v15);
      v23 = objc_retainAutoreleasedReturnValue(v22);
      _objc_msgSend(v21, "selectRowIndexes:byExtendingSelection:", v23, 0LL);
    }
  }
}

//----- (0000000100622D61) ----------------------------------------------------
id __cdecl -[ZiXunKeyboradSpiritViewController tableView:viewForTableColumn:row:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{

  v7 = objc_retain(a3);
  v8 = objc_retain(a4);
  if ( a5 < 0 )
  {
    v15 = 0LL;
  }
  else
  {
    v23 = v8;
    v10 = _objc_msgSend(v9, "resultMArray");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12 = _objc_msgSend(v11, "count");
    if ( (unsigned __int64)v12 <= a5 )
    {
      v15 = 0LL;
    }
    else
    {
      v14 = +[HXKeyboardSpiritCellView makeViewWithType:tableView:owner:](
              &OBJC_CLASS___HXKeyboardSpiritCellView,
              "makeViewWithType:tableView:owner:",
              0LL,
              v7,
              v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v17 = _objc_msgSend(v16, "resultMArray");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(v18, "thsStringAtIndex:", a5);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      _objc_msgSend(v15, "setStringValue:", v20);
    }
    v8 = v23;
  }
  return objc_autoreleaseReturnValue(v15);
}

//----- (0000000100622E92) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunKeyboradSpiritViewController numberOfRowsInTableView:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  NSMutableArray *v3; // rax
  NSMutableArray *v4; // r14
  signed __int64 v5; // rbx

  v3 = -[ZiXunKeyboradSpiritViewController resultMArray](self, "resultMArray", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = (signed __int64)_objc_msgSend(v4, "count");
  if ( v5 >= 11 )
    return 11LL;
  return v5;
}

//----- (0000000100622EE1) ----------------------------------------------------
char __cdecl -[ZiXunKeyboradSpiritViewController tableView:shouldSelectRow:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{
  return a4 >= 0 && (__int64)_objc_msgSend(a3, "numberOfRows") > a4;
}

//----- (0000000100622F13) ----------------------------------------------------
id __cdecl -[ZiXunKeyboradSpiritViewController tableView:rowViewForRow:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{

  v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRowView_Highlight);
  v5 = _objc_msgSend(v4, "init");
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100622F3C) ----------------------------------------------------
HXSearchTextField *__cdecl -[ZiXunKeyboradSpiritViewController keyboardTextField](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  return (HXSearchTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100622F55) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setKeyboardTextField:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._nibName, a3);
}

//----- (0000000100622F69) ----------------------------------------------------
id __cdecl -[ZiXunKeyboradSpiritViewController searchResultBlock](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 24LL, 0);
}

//----- (0000000100622F7C) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setSearchResultBlock:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 24LL);
}

//----- (0000000100622F8D) ----------------------------------------------------
id __cdecl -[ZiXunKeyboradSpiritViewController showNoResultViewBlock](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 32LL, 0);
}

//----- (0000000100622FA0) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setShowNoResultViewBlock:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 32LL);
}

//----- (0000000100622FB1) ----------------------------------------------------
NSTableView *__cdecl -[ZiXunKeyboradSpiritViewController keyboardTable](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
  return (NSTableView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (0000000100622FCA) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setKeyboardTable:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super._title, a3);
}

//----- (0000000100622FDE) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunKeyboradSpiritViewController longFromTime](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2)
{
  return (signed __int64)self->super.view;
}

//----- (0000000100622FEF) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setLongFromTime:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        signed __int64 a3)
{
  self->super.view = (NSView *)a3;
}

//----- (0000000100623000) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setResultMArray:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, a3);
}

//----- (0000000100623014) ----------------------------------------------------
char __cdecl -[ZiXunKeyboradSpiritViewController autoJump](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (0000000100623025) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController setAutoJump:](
        ZiXunKeyboradSpiritViewController *self,
        SEL a2,
        char a3)
{
  LOBYTE(self->super.super._nextResponder) = a3;
}

//----- (0000000100623035) ----------------------------------------------------
void __cdecl -[ZiXunKeyboradSpiritViewController .cxx_destruct](ZiXunKeyboradSpiritViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._topLevelObjects, 0LL);
  objc_destroyWeak((id *)&self->super._title);
  objc_storeStrong(&self->super._representedObject, 0LL);
  objc_storeStrong((id *)&self->super._nibBundle, 0LL);
  objc_destroyWeak((id *)&self->super._nibName);
}

