void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController updateTableVCData:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  __CFString *v20; // rax
  __CFString *v21; // rdx
  __CFString *v44; // [rsp+0h] [rbp-30h]

  v3 = objc_retain(a3);
  if ( _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v4, "thsStringForKey:", CFSTR("tablekey"));
    v6 = objc_retainAutoreleasedReturnValue(v5);
    -[ThumbnailZhangTingStocksPoolTableViewController setTableKey:](self, "setTableKey:", v6);
    v8 = _objc_msgSend(v7, "thsNumberForKey:", CFSTR("TableID"));
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = _objc_msgSend(v9, "longValue");
    -[ThumbnailBaseTableViewController setTableID:](self, "setTableID:", v10);
    v12 = _objc_msgSend(v11, "thsNumberForKey:", CFSTR("sortid"));
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14 = _objc_msgSend(v13, "longValue");
    -[HXBaseTableViewController setSortID:](self, "setSortID:", v14);
    v16 = _objc_msgSend(v15, "thsStringForKey:", CFSTR("IWenCaiSortIdentifier"));
    v17 = objc_retainAutoreleasedReturnValue(v16);
    -[HXBaseTableViewController setWenCaiSortIdentifier:](self, "setWenCaiSortIdentifier:", v17);
    v19 = _objc_msgSend(v18, "thsStringForKey:", CFSTR("sortorder"));
    v20 = (__CFString *)objc_retainAutoreleasedReturnValue(v19);
    v44 = v20;
    v21 = CFSTR("D");
    if ( v20 )
      v21 = v20;
    -[HXBaseTableViewController setSortOrder:](self, "setSortOrder:", v21);
    v23 = _objc_msgSend(v22, "thsNumberForKey:", CFSTR("sortbegin"));
    v24 = objc_retainAutoreleasedReturnValue(v23);
    v25 = _objc_msgSend(v24, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaBegin:](self, "setQuotaBegin:", v25);
    v27 = _objc_msgSend(v26, "thsNumberForKey:", CFSTR("sortcount"));
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29 = _objc_msgSend(v28, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaCount:](self, "setQuotaCount:", v29);
    v31 = _objc_msgSend(v30, "thsNumberForKey:", CFSTR("Index"));
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33 = _objc_msgSend(v32, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setQuotaIndex:](self, "setQuotaIndex:", v33);
    v35 = _objc_msgSend(v34, "thsStringForKey:", CFSTR("SelectedCode"));
    v36 = objc_retainAutoreleasedReturnValue(v35);
    -[HXBaseTableViewController setSelectedCode:](self, "setSelectedCode:", v36);
    v38 = _objc_msgSend(v37, "thsNumberForKey:", CFSTR("totalnumber"));
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = _objc_msgSend(v39, "unsignedIntegerValue");
    -[ThumbnailBaseTableViewController setAllCodesNum:](self, "setAllCodesNum:", v40);
    v42 = _objc_msgSend(v41, "thsArrayForKey:", off_1012DBA30[0]);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    -[ThumbnailZhangTingStocksPoolTableViewController setCompleteCodeList:](self, "setCompleteCodeList:", v43);
    -[ThumbnailBaseTableViewController setIsTableSwitched:](self, "setIsTableSwitched:", 1LL);
    -[ThumbnailBaseTableViewController setIsFoucsOfSuperController:](self, "setIsFoucsOfSuperController:", 1LL);
  }
}

//----- (0000000100A171B5) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController basicHQCodeListRequestModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  id invokeRowSwitchCallBack; // rdi

  invokeRowSwitchCallBack = self->super.super._invokeRowSwitchCallBack;
  if ( !invokeRowSwitchCallBack )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super.super._invokeRowSwitchCallBack;
    self->super.super._invokeRowSwitchCallBack = v5;
    invokeRowSwitchCallBack = self->super.super._invokeRowSwitchCallBack;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(invokeRowSwitchCallBack);
}

//----- (0000000100A17206) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiCodeListRequestModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{

  thumbTableSrcollDirection = (void *)self->super.super._thumbTableSrcollDirection;
  if ( !thumbTableSrcollDirection )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = (void *)self->super.super._thumbTableSrcollDirection;
    self->super.super._thumbTableSrcollDirection = (signed __int64)v5;
    thumbTableSrcollDirection = (void *)self->super.super._thumbTableSrcollDirection;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(thumbTableSrcollDirection);
}

//----- (0000000100A17257) ----------------------------------------------------
HXTableRequestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiRequestModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSTimer *countDownTimerForScroll; // rdi
  NSTimer *v5; // rax
  NSTimer *v6; // rdi

  countDownTimerForScroll = self->super.super._countDownTimerForScroll;
  if ( !countDownTimerForScroll )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRequestModule);
    v5 = (NSTimer *)_objc_msgSend(v4, "init");
    v6 = self->super.super._countDownTimerForScroll;
    self->super.super._countDownTimerForScroll = v5;
    countDownTimerForScroll = self->super.super._countDownTimerForScroll;
  }
  return (HXTableRequestModule *)objc_retainAutoreleaseReturnValue(countDownTimerForScroll);
}

//----- (0000000100A172A8) ----------------------------------------------------
ZhangDieTingStockpoolReuestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController codeListRequestModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{

  visibleY = self->super.super._visibleY;
  if ( visibleY == 0.0 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangDieTingStockpoolReuestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = self->super.super._visibleY;
    *(_QWORD *)&self->super.super._visibleY = v5;
    visibleY = self->super.super._visibleY;
  }
  return (ZhangDieTingStockpoolReuestModule *)objc_retainAutoreleaseReturnValue(*(id *)&visibleY);
}

//----- (0000000100A172F9) ----------------------------------------------------
ZhangDieTingRefactorHQDataRequestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController refactorHQCodeListRequestModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSString *kycCompleteCodeListRequestQuery; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  kycCompleteCodeListRequestQuery = self->super.super._kycCompleteCodeListRequestQuery;
  if ( !kycCompleteCodeListRequestQuery )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangDieTingRefactorHQDataRequestModule);
    v5 = (NSString *)_objc_msgSend(v4, "init");
    v6 = self->super.super._kycCompleteCodeListRequestQuery;
    self->super.super._kycCompleteCodeListRequestQuery = v5;
    kycCompleteCodeListRequestQuery = self->super.super._kycCompleteCodeListRequestQuery;
  }
  return (ZhangDieTingRefactorHQDataRequestModule *)objc_retainAutoreleaseReturnValue(kycCompleteCodeListRequestQuery);
}

//----- (0000000100A1734A) ----------------------------------------------------
ZhangDieTingRefactorHQDataRequestModule *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController zhangTingLeiXingReqeustModule](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{

  location = (void *)self->super.super._visibleRangeCache.location;
  if ( !location )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZhangDieTingRefactorHQDataRequestModule);
    v5 = _objc_msgSend(v4, "init");
    v6 = (void *)self->super.super._visibleRangeCache.location;
    self->super.super._visibleRangeCache.location = (NSUInteger)v5;
    location = (void *)self->super.super._visibleRangeCache.location;
  }
  return (ZhangDieTingRefactorHQDataRequestModule *)objc_retainAutoreleaseReturnValue(location);
}

//----- (0000000100A1739B) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController requestForMyTable](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  if ( !(unsigned __int8)-[ThumbnailBaseTableViewController invalidRequestWhenViewWillBeRemoved](
                           self,
                           "invalidRequestWhenViewWillBeRemoved") )
  {
    -[ThumbnailBaseTableViewController deleteOrder](self, "deleteOrder");
    if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
    {
      -[ThumbnailZhangTingStocksPoolTableViewController setRequestAndOrderParams](self, "setRequestAndOrderParams");
      -[ThumbnailZhangTingStocksPoolTableViewController requestCompleteCodeList](self, "requestCompleteCodeList");
    }
  }
}

//----- (0000000100A17412) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController order](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  HXTableRequestModule *v2; // rax
  HXTableRequestModule *v3; // r14
  NSMutableArray *v4; // rax
  NSMutableArray *v5; // r15
  NSArray *v6; // rax
  NSArray *v7; // r13
  id *v8; // r12
  _QWORD v9[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  if ( (unsigned __int8)_objc_msgSend(self, "viewIsDisplaying") )
  {
    objc_initWeak(location, self);
    v2 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v6 = -[ThumbnailZhangTingStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9[0] = _NSConcreteStackBlock;
    v9[1] = 3254779904LL;
    v9[2] = sub_100A17547;
    v9[3] = &unk_1012DAF08;
    objc_copyWeak(&to, location);
    -[HXTableRequestModule subscribe:dataTypes:callBack:](v3, "subscribe:dataTypes:callBack:", v5, v7, v9);
    objc_destroyWeak(v8);
    objc_destroyWeak(location);
  }
}

//----- (0000000100A17547) ----------------------------------------------------
void __fastcall sub_100A17547(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithPushData:", v2);
}

//----- (0000000100A175A1) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setRequestAndOrderParams](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSArray *v8; // rax
  NSArray *v9; // rax

  -[ThumbnailBaseTableViewController setRequestRowRange](self, "setRequestRowRange");
  v2 = -[ThumbnailZhangTingStocksPoolTableViewController getRequestDataTypes](self, "getRequestDataTypes");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "thsArrayForKey:", CFSTR("basicDataTypes"));
  v5 = objc_retainAutoreleasedReturnValue(v4);
  -[ThumbnailZhangTingStocksPoolTableViewController setBasicHQDataTypes:](self, "setBasicHQDataTypes:", v5);
  v6 = _objc_msgSend(v3, "thsDictionaryForKey:", CFSTR("iWenCaiDataItemParams"));
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[ThumbnailZhangTingStocksPoolTableViewController setIWenCaiItemParams:](self, "setIWenCaiItemParams:", v7);
  v8 = -[ThumbnailZhangTingStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = -[ThumbnailBaseTableViewController getOrderDataTypesWithRequestDataTypes:](
          self,
          "getOrderDataTypesWithRequestDataTypes:",
          v9);
  v11 = objc_retainAutoreleasedReturnValue(v10);
  -[ThumbnailZhangTingStocksPoolTableViewController setOrderHQDataTypes:](self, "setOrderHQDataTypes:", v11);
}

//----- (0000000100A176BA) ----------------------------------------------------
id __cdecl -[ThumbnailZhangTingStocksPoolTableViewController getRequestDataTypes](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSNumber *v2; // rax
  NSNumber *v3; // rax
  NSNumber *v4; // rax
  NSNumber *v5; // rax
  NSNumber *v6; // r15
  NSArray *v7; // rax
  NSArray *v8; // r14
  NSMutableArray *v9; // rax
  NSNumber *v16; // rax
  NSNumber *v17; // rbx
  NSNumber *v20; // rax
  NSNumber *v21; // rbx
  HXTableManager *v30; // rax
  HXTableManager *v31; // r15
  bool v49; // zf
  NSArray *v52; // rax
  NSArray *v53; // rbx
  NSArray *v56; // rax
  NSArray *v57; // rbx
  NSNumber *v59; // rax
  NSNumber *v60; // r14
  NSArray *v61; // rax
  NSArray *v62; // rbx
  NSArray *v65; // rax
  NSArray *v66; // r15
  NSDictionary *v71; // r14
  NSDictionary *v72; // rax
  NSNumber *v75; // [rsp+0h] [rbp-B0h]
  NSNumber *v76; // [rsp+8h] [rbp-A8h]
  NSMutableArray *v78; // [rsp+10h] [rbp-A0h]
  _QWORD v81[2]; // [rsp+20h] [rbp-90h] BYREF
  _QWORD v82[2]; // [rsp+30h] [rbp-80h] BYREF
  NSNumber *v84; // [rsp+48h] [rbp-68h] BYREF
  _QWORD v87[4]; // [rsp+60h] [rbp-50h] BYREF

  v2 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
  v76 = objc_retainAutoreleasedReturnValue(v2);
  v87[0] = v76;
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
  v75 = objc_retainAutoreleasedReturnValue(v3);
  v87[1] = v75;
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
  v87[2] = objc_retainAutoreleasedReturnValue(v4);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v87[3] = v6;
  v7 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v87, 4LL);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v8);
  v78 = objc_retainAutoreleasedReturnValue(v9);
  if ( (__int64)-[HXBaseTableViewController sortID](self, "sortID") )
  {
    if ( _objc_msgSend(v11, "sortID") != (id)12345670 )
    {
      v12 = _objc_msgSend(v11, "wenCaiSortIdentifier");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      v14 = _objc_msgSend(v13, "length");
      if ( !v14 )
      {
        v15 = _objc_msgSend(v11, "sortID");
        v16 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v15);
        v17 = objc_retainAutoreleasedReturnValue(v16);
        v18 = (__int64)_objc_msgSend(v78, "containsObject:", v17);
        if ( !v18 )
        {
          v19 = _objc_msgSend(v11, "sortID");
          v20 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v19);
          v21 = objc_retainAutoreleasedReturnValue(v20);
          _objc_msgSend(v78, "addObject:", v21);
        }
      }
    }
  }
  v22 = _objc_msgSend(v11, "sortID");
  v23 = +[ThumbnailUtils handleSpectialDataItem:dataItemsArr:](
          &OBJC_CLASS___ThumbnailUtils,
          "handleSpectialDataItem:dataItemsArr:",
          v22,
          v78);
  v77 = objc_retainAutoreleasedReturnValue(v23);
  v24 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
  v79 = objc_retainAutoreleasedReturnValue(v24);
  if ( !(__int64)_objc_msgSend(v25, "sortID") || _objc_msgSend(v26, "sortID") == (id)12345670 )
  {
    v27 = _objc_msgSend(v26, "wenCaiSortIdentifier");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29 = _objc_msgSend(v28, "length");
    if ( v29 )
    {
      v30 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v33 = v32;
      v34 = _objc_msgSend(v32, "tableKey");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v36 = -[HXTableManager getSelectedSchemesForKey:](v31, "getSelectedSchemesForKey:", v35);
      objc_retainAutoreleasedReturnValue(v36);
      v37 = _objc_msgSend(v33, "wenCaiSortIdentifier");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v40 = _objc_msgSend(v39, "entityForIdentifier:", v38);
      v41 = objc_retainAutoreleasedReturnValue(v40);
      v42 = _objc_msgSend(v41, "identifier");
      v43 = objc_retainAutoreleasedReturnValue(v42);
      if ( v43 && (v44 = _objc_msgSend(v41, "querykey"), (v45 = objc_retainAutoreleasedReturnValue(v44)) != 0LL) )
      {
        v46 = v45;
        v47 = _objc_msgSend(v41, "timestamp");
        v80 = v41;
        v48 = objc_retainAutoreleasedReturnValue(v47);
        v49 = v48 == 0LL;
        v41 = v80;
        if ( !v49 )
        {
          v50 = _objc_msgSend(v80, "identifier");
          v51 = objc_retainAutoreleasedReturnValue(v50);
          v86 = v51;
          v52 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v86, 1LL);
          v53 = objc_retainAutoreleasedReturnValue(v52);
          _objc_msgSend(v79, "setObject:forKey:", v53, CFSTR("IWenCaiIdentifier"));
          v54 = _objc_msgSend(v80, "querykey");
          v55 = objc_retainAutoreleasedReturnValue(v54);
          v85 = v55;
          v56 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v85, 1LL);
          v57 = objc_retainAutoreleasedReturnValue(v56);
          _objc_msgSend(v79, "setObject:forKey:", v57, CFSTR("IWenCaiQueryKey"));
          v58 = _objc_msgSend(v80, "index");
          v59 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v58);
          v60 = objc_retainAutoreleasedReturnValue(v59);
          v84 = v60;
          v61 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v84, 1LL);
          v62 = objc_retainAutoreleasedReturnValue(v61);
          _objc_msgSend(v79, "setObject:forKey:", v62, CFSTR("IWenCaiKeyIndex"));
          v63 = _objc_msgSend(v80, "timestamp");
          v64 = objc_retainAutoreleasedReturnValue(v63);
          v83 = v64;
          v65 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v83, 1LL);
          v66 = objc_retainAutoreleasedReturnValue(v65);
          _objc_msgSend(v79, "setObject:forKey:", v66, CFSTR("IWenCaiTimestamp"));
          v67 = v64;
          v41 = v80;
        }
      }
      else
      {
      }
    }
  }
  v69 = _objc_msgSend(v77, "count");
  v71 = 0LL;
  if ( v79 && v69 )
  {
    v81[0] = CFSTR("basicDataTypes");
    v82[0] = v70;
    v81[1] = CFSTR("iWenCaiDataItemParams");
    v82[1] = v79;
    v72 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v82, v81, 2LL);
    v71 = objc_retainAutoreleasedReturnValue(v72);
  }
  return objc_autoreleaseReturnValue(v71);
}

//----- (0000000100A17DB7) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController dealWithPushData:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  ThumbnailZhangTingStocksPoolTableViewController *v3; // r13
  HXStockModel *v4; // rax
  HXStockModel *v5; // r15
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  HXStockModel *v11; // rdi
  id (**v12)(id, SEL, ...); // r12
  id (**v13)(id, SEL, ...); // r15
  unsigned __int64 v16; // rcx
  unsigned __int64 v22; // r14
  unsigned __int64 v52; // r15
  bool v53; // cc
  unsigned __int64 v55; // rax
  ThumbnailZhangTingStocksPoolTableViewController *v60; // [rsp+60h] [rbp-50h]
  unsigned __int64 v61; // [rsp+68h] [rbp-48h]

  v3 = self;
  v64 = objc_retain(a3);
  if ( _objc_msgSend(v64, "count") )
  {
    v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    v7 = v6(v5, "mainStockTableViewDataArray");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v8);
    v63 = objc_retainAutoreleasedReturnValue(v10);
    v11 = v5;
    v13 = v12;
    if ( !((__int64 (__fastcall *)(id, __int64))v13)(v64, v14) )
      goto LABEL_15;
    v61 = 0LL;
    v60 = v3;
    while ( 1 )
    {
      v17 = (void *)((__int64 (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *))v13)(
                      v3,
                      "orderCodeMArray");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v20 = ((__int64 (__fastcall *)(id, __int64))v13)(v18, v19);
      if ( !v20 )
        goto LABEL_14;
      v22 = 0LL;
      while ( ((__int64 (__fastcall *)(id, const char *))v13)(v63, v21) <= v22 )
      {
LABEL_11:
        ++v22;
        v49 = (void *)((__int64 (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *))v13)(
                        v3,
                        "orderCodeMArray");
        v50 = objc_retainAutoreleasedReturnValue(v49);
        v52 = ((__int64 (__fastcall *)(id, __int64))v13)(v50, v51);
        v53 = v52 <= v22;
        v13 = &_objc_msgSend;
        if ( v53 )
          goto LABEL_14;
      }
      v23 = (void *)((__int64 (__fastcall *)(id, const char *, unsigned __int64))v13)(v64, "thsDictionaryAtIndex:", v61);
      v62 = objc_retainAutoreleasedReturnValue(v23);
      v24 = (void *)((__int64 (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *))v13)(
                      v3,
                      "orderCodeMArray");
      v25 = objc_retainAutoreleasedReturnValue(v24);
      v27 = (void *)((__int64 (__fastcall *)(id, __int64, unsigned __int64))v13)(v25, v26, v22);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      v29 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, __int64))v13)(
                      &OBJC_CLASS___NSNumber,
                      "numberWithInt:",
                      5LL);
      v30 = objc_retainAutoreleasedReturnValue(v29);
      v31 = (void *)((__int64 (__fastcall *)(id, const char *, id))v13)(v62, "thsStringForKey:", v30);
      v59 = objc_retainAutoreleasedReturnValue(v31);
      v32 = v28;
      v34 = (void *)((__int64 (__fastcall *)(id, __int64, __CFString *))v13)(v28, v33, CFSTR("Market"));
      v35 = objc_retainAutoreleasedReturnValue(v34);
      v58 = v32;
      v37 = (void *)((__int64 (__fastcall *)(id, __int64, __CFString *))v13)(v32, v36, CFSTR("StockCode"));
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v39 = (void *)((__int64 (__fastcall *)(__objc2_class *, const char *, id, id))v13)(
                      &OBJC_CLASS___HXTools,
                      "getMarketAndCodeByAppendingMarket:andStockCode:",
                      v35,
                      v38);
      objc_retainAutoreleasedReturnValue(v39);
      if ( !((unsigned __int8 (__fastcall *)(id, const char *, __int64))v13)(v59, "isEqualToString:", v40) )
        goto LABEL_10;
      v42 = v41;
      v43 = _objc_msgSend(v63, "thsDictionaryAtIndex:", v22);
      v44 = objc_retainAutoreleasedReturnValue(v43);
      _objc_msgSend(v44, "mutableCopy");
      v46 = -[ThumbnailZhangTingStocksPoolTableViewController updateData:SourceData:](
              v60,
              "updateData:SourceData:",
              v62,
              v45);
      v47 = objc_retainAutoreleasedReturnValue(v46);
      if ( !v47 )
        break;
      _objc_msgSend(v63, "replaceObjectAtIndex:withObject:", v22, v47);
      v54 = v42;
      v13 = &_objc_msgSend;
      v3 = v60;
      v21 = "count";
LABEL_14:
      v55 = ((__int64 (__fastcall *)(id, const char *))v13)(v64, v21);
      v16 = v61 + 1;
      v61 = v16;
      if ( v55 <= v16 )
      {
LABEL_15:
        v56 = (void *)((__int64 (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *, __int64, unsigned __int64))v13)(
                        v3,
                        "stockModel",
                        v15,
                        v16);
        v57 = objc_retainAutoreleasedReturnValue(v56);
        ((void (__fastcall *)(id, const char *, id))v13)(v57, "setMainStockTableViewDataArray:", v63);
        ((void (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *, __int64))v13)(
          v3,
          "reloadData:",
          1LL);
        goto LABEL_16;
      }
    }
    v41 = v42;
LABEL_10:
    v3 = v60;
    v13 = &_objc_msgSend;
    goto LABEL_11;
  }
LABEL_16:
}

//----- (0000000100A18211) ----------------------------------------------------
id __cdecl -[ThumbnailZhangTingStocksPoolTableViewController updateData:SourceData:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{
  ThumbnailZhangTingStocksPoolTableViewController *v5; // rbx
  NSArray *v7; // rax
  NSArray *v8; // r13
  NSArray *v11; // rax
  NSArray *v12; // r14
  NSArray *v19; // rax
  NSArray *v20; // rbx
  unsigned __int64 v22; // r12

  v5 = self;
  objc_retain(a3);
  v6 = objc_retain(a4);
  v7 = -[ThumbnailZhangTingStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = _objc_msgSend(v8, "count");
  if ( v9 )
  {
    v25 = v10;
    do
    {
      v24 = v6;
      v11 = -[ThumbnailZhangTingStocksPoolTableViewController orderHQDataTypes](v5, "orderHQDataTypes");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      v14 = _objc_msgSend(v12, "thsNumberAtIndex:", v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v16 = _objc_msgSend(v15, "unsignedIntegerValue");
      v17 = +[ThumbnailUtils setSourceData:withPushData:dataItemID:](
              &OBJC_CLASS___ThumbnailUtils,
              "setSourceData:withPushData:dataItemID:",
              v24,
              v25,
              v16);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = -[ThumbnailZhangTingStocksPoolTableViewController orderHQDataTypes](self, "orderHQDataTypes");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v20, "count");
      v6 = v18;
      v5 = self;
    }
    while ( (unsigned __int64)v21 > v22 );
    v6 = v18;
    v10 = v25;
  }
  return objc_autorelease(v6);
}

//----- (0000000100A183A9) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController actionForTableViewSelectionDidChange:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        signed __int64 a3)
{
  HXStockModel *v4; // rax
  HXStockModel *v5; // r13
  NSArray *v6; // rax
  NSArray *v7; // r14
  __objc2_class *v27; // r15
  __objc2_class *v31; // rdi
  ThumbnailZhangTingStocksPoolTableViewController *v36; // r15
  __objc2_class *v57; // [rsp+28h] [rbp-78h]
  _QWORD v58[2]; // [rsp+30h] [rbp-70h] BYREF
  _QWORD v59[2]; // [rsp+40h] [rbp-60h] BYREF
  _QWORD v60[2]; // [rsp+50h] [rbp-50h] BYREF
  _QWORD v61[2]; // [rsp+60h] [rbp-40h] BYREF

  v4 = -[HXBaseTableViewController stockModel](self, "stockModel");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = -[HXStockModel mainStockTableViewDataArray](v5, "mainStockTableViewDataArray");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(v8, "arrayWithArray:", v7);
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v12 = v11(v10, "count");
  if ( (unsigned __int64)a3 <= 0x7FFFFFFFFFFFFFFELL && v12 )
  {
    v14 = v13(self, "begin");
    v53 = v10;
    v16 = (void *)v15(v10, "thsDictionaryAtIndex:", a3 - v14);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = (void *)v18(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v20 = objc_retainAutoreleasedReturnValue(v19);
    v22 = (void *)v21(v17, "thsStringForKey:", v20);
    v23 = objc_retainAutoreleasedReturnValue(v22);
    v24 = v23;
    v26 = (void *)v25(&OBJC_CLASS___HXTools, "getCodeString:", v23);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v55 = v24;
    v29 = (void *)v28(&OBJC_CLASS___HXTools, "getMarketString:", v24);
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v57 = v27;
    v31 = v27;
    v33 = v32;
    if ( v32(v31, "length") && _objc_msgSend(v30, "length") )
    {
      v34 = (void *)v33(&OBJC_CLASS___SelfStock, "sharedInstance");
      v35 = objc_retainAutoreleasedReturnValue(v34);
      ((void (__fastcall *)(id, const char *, __objc2_class *, id, __int64))v33)(
        v35,
        "addRecentlyScanStock:market:toBegin:",
        (__objc2_class *)v57,
        v30,
        1LL);
      v36 = self;
      v38 = (void *)v37(self, "invokeRowSwitchCallBack");
      v39 = objc_retainAutoreleasedReturnValue(v38);
      v54 = v30;
      if ( v39 )
      {
        v60[0] = CFSTR("StockCode");
        v61[0] = v57;
        v60[1] = CFSTR("Market");
        v61[1] = v30;
        v41 = (void *)v40(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v61, v60, 2LL);
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v44 = (void *)v43(self, "invokeRowSwitchCallBack");
        v45 = objc_retainAutoreleasedReturnValue(v44);
        (*((void (__fastcall **)(id, id))v45 + 2))(v45, v42);
        v36 = self;
      }
      ((void (__fastcall *)(ThumbnailZhangTingStocksPoolTableViewController *, const char *, id))v40)(
        v36,
        "setSelectedCode:",
        v55);
      v58[0] = CFSTR("StockCode");
      v59[0] = v57;
      v58[1] = off_1012E0FA8;
      v59[1] = v55;
      v47 = (void *)v46(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v59, v58, 2LL);
      v48 = objc_retainAutoreleasedReturnValue(v47);
      v50 = (void *)v49(v36, "myTable");
      v51 = objc_retainAutoreleasedReturnValue(v50);
      v52(v51, "setParamsDic:", v48);
      v30 = v54;
    }
    v10 = v53;
  }
}

//----- (0000000100A18740) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController requestCompleteCodeList](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  ZhangDieTingStockpoolReuestModule *v2; // rax
  ZhangDieTingStockpoolReuestModule *v3; // r14
  _QWORD v4[4]; // [rsp+0h] [rbp-40h] BYREF
  id to; // [rsp+20h] [rbp-20h] BYREF
  id location[3]; // [rsp+28h] [rbp-18h] BYREF

  objc_initWeak(location, self);
  v2 = -[ThumbnailZhangTingStocksPoolTableViewController codeListRequestModule](self, "codeListRequestModule");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4[0] = _NSConcreteStackBlock;
  v4[1] = 3254779904LL;
  v4[2] = sub_100A1880C;
  v4[3] = &unk_1012DAF08;
  objc_copyWeak(&to, location);
  -[ZhangDieTingStockpoolReuestModule requestData:callBack:](v3, "requestData:callBack:", 1073876040LL, v4);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (0000000100A1880C) ----------------------------------------------------
void __fastcall sub_100A1880C(__int64 a1, void *a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v5)(id, SEL, ...); // r12
  id WeakRetained; // rax

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)v4(v2, "isKindOfClass:", v3) && _objc_msgSend(v2, "count") )
  {
    v6 = v5(v2, "count");
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    v8(WeakRetained, "setAllCodesNum:", v6);
    v10 = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(v10, "setCompleteCodeList:", v2);
    v11 = objc_loadWeakRetained((id *)(a1 + 32));
    v12(v11, "requestCodeList");
  }
  else
  {
    v13 = objc_loadWeakRetained((id *)(a1 + 32));
    v14(v13, "setAllCodesNum:", 0LL);
    v15 = __NSArray0__;
    v16 = objc_loadWeakRetained((id *)(a1 + 32));
    v17(v16, "setCompleteCodeList:", v15);
    v11 = objc_loadWeakRetained((id *)(a1 + 32));
    v18(v11, "reloadData:", 0LL);
  }
}

//----- (0000000100A1897D) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController requestCodeList](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSArray *v3; // rax
  NSArray *v4; // rbx
  NSString *v5; // rax
  __CFString *v6; // rax
  __CFString *v7; // rbx
  __CFString *v8; // rdi
  NSNumber *v10; // rax
  NSNumber *v12; // rax
  NSNumber *v14; // rax
  NSNumber *v15; // rbx
  SEL v16; // r12
  NSDictionary *v19; // rax
  NSDictionary *v20; // r15
  ZhangDieTingRefactorHQDataRequestModule *v24; // rax
  ZhangDieTingRefactorHQDataRequestModule *v25; // r14
  NSString *v29; // rax
  NSString *v30; // rbx
  NSString *v32; // rax
  NSString *v33; // rbx
  HXTableManager *v35; // rax
  HXTableManager *v36; // r13
  NSString *v37; // rax
  NSString *v38; // rbx
  NSString *v41; // rax
  NSString *v42; // rbx
  __CFString *v50; // rbx
  __CFString *v51; // rdi
  NSArray *v52; // rax
  NSArray *v53; // r13
  NSString *v57; // rax
  __CFString *v58; // rax
  __CFString *v59; // rbx
  __CFString *v60; // rdi
  NSNumber *v63; // rax
  NSNumber *v65; // rax
  NSNumber *v66; // r13
  NSNumber *v68; // rax
  NSNumber *v69; // r15
  NSDictionary *v70; // rax
  HXTableRequestModule *v71; // rax
  HXTableRequestModule *v72; // r14
  NSArray *v76; // rax
  NSArray *v77; // rbx
  SEL v80; // r12
  _QWORD v81[4]; // [rsp+8h] [rbp-1A8h] BYREF
  id to; // [rsp+28h] [rbp-188h] BYREF
  _QWORD v83[4]; // [rsp+30h] [rbp-180h] BYREF
  _QWORD v85[4]; // [rsp+58h] [rbp-158h] BYREF
  id location; // [rsp+A8h] [rbp-108h] BYREF

  objc_initWeak(&location, self);
  if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)330325 )
  {
    v3 = -[ThumbnailZhangTingStocksPoolTableViewController completeCodeList](self, "completeCodeList");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    if ( v4 )
    {
      v5 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
      v6 = objc_retainAutoreleasedReturnValue(v5);
      v7 = v6;
      v8 = CFSTR("D");
      if ( v6 )
        v8 = v6;
      v94 = objc_retain(v8);
      v97[0] = (__int64)CFSTR("sortbegin");
      v9 = -[ThumbnailBaseTableViewController begin](self, "begin");
      v10 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v9);
      v93 = objc_retainAutoreleasedReturnValue(v10);
      v98[0] = (__int64)v93;
      v97[1] = (__int64)CFSTR("sortcount");
      v11 = -[ThumbnailBaseTableViewController count](self, "count");
      v12 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v11);
      v91 = objc_retainAutoreleasedReturnValue(v12);
      v98[1] = (__int64)v91;
      v97[2] = (__int64)CFSTR("sortorder");
      v98[2] = (__int64)v94;
      v97[3] = (__int64)CFSTR("sortid");
      v13 = -[HXBaseTableViewController sortID](self, "sortID");
      v14 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v13);
      v15 = objc_retainAutoreleasedReturnValue(v14);
      v98[3] = (__int64)v15;
      v97[4] = (__int64)CFSTR("StockCodesAndMarket");
      v17 = _objc_msgSend(self, v16);
      v18 = objc_retain(v17);
      v98[4] = (__int64)v18;
      v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v98, v97, 5LL);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21(v15);
      v22(v91);
      v23(v93);
      v24 = -[ThumbnailZhangTingStocksPoolTableViewController refactorHQCodeListRequestModule](
              self,
              "refactorHQCodeListRequestModule");
      v25 = objc_retain(v24);
      v81[0] = _NSConcreteStackBlock;
      v81[1] = 3254779904LL;
      v81[2] = sub_100A19296;
      v81[3] = &unk_1012DAED8;
      objc_copyWeak(&to, &location);
      -[ZhangDieTingRefactorHQDataRequestModule requestForCodeListSort:reqCallBack:](
        v25,
        "requestForCodeListSort:reqCallBack:",
        v20,
        v81);
      v26(v25);
      objc_destroyWeak(&to);
      v27(v20);
      v28(v94);
    }
  }
  else if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670
         || (v29 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier"),
             v30 = objc_retainAutoreleasedReturnValue(v29),
             v31 = _objc_msgSend(v30, "length"),
             v31) )
  {
    if ( -[HXBaseTableViewController sortID](self, "sortID") == (id)12345670 )
    {
      v32 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
      v33 = objc_retainAutoreleasedReturnValue(v32);
      v34 = _objc_msgSend(v33, "length");
      if ( v34 )
      {
        v35 = +[HXTableManager sharedInstance](&OBJC_CLASS___HXTableManager, "sharedInstance");
        v36 = objc_retainAutoreleasedReturnValue(v35);
        v37 = -[ThumbnailZhangTingStocksPoolTableViewController tableKey](self, "tableKey");
        v38 = objc_retainAutoreleasedReturnValue(v37);
        v39 = -[HXTableManager getSelectedSchemesForKey:](v36, "getSelectedSchemesForKey:", v38);
        v94 = objc_retainAutoreleasedReturnValue(v39);
        v40(v36);
        v41 = -[HXBaseTableViewController wenCaiSortIdentifier](self, "wenCaiSortIdentifier");
        v42 = objc_retainAutoreleasedReturnValue(v41);
        v43 = _objc_msgSend(v94, "entityForIdentifier:", v42);
        objc_retainAutoreleasedReturnValue(v43);
        v45 = _objc_msgSend(v44, "querykey");
        v46 = objc_retainAutoreleasedReturnValue(v45);
        v47 = _objc_msgSend(v46, "length");
        if ( v47 )
        {
          v93 = v48;
          v49 = _objc_msgSend(v48, "timestamp");
          v50 = (__CFString *)objc_retainAutoreleasedReturnValue(v49);
          v51 = v50;
          if ( !v50 )
            v51 = &charsToLeaveEscaped;
          v91 = objc_retain(v51);
          v52 = -[ThumbnailZhangTingStocksPoolTableViewController completeCodeList](self, "completeCodeList");
          v53 = objc_retainAutoreleasedReturnValue(v52);
          v54 = +[HXTools getCodeListWithoutMarketStrFromArr:](
                  &OBJC_CLASS___HXTools,
                  "getCodeListWithoutMarketStrFromArr:",
                  v53);
          v55 = objc_retainAutoreleasedReturnValue(v54);
          if ( v55 )
            v56 = v55;
          v89 = objc_retain(v56);
          v57 = -[HXBaseTableViewController sortOrder](self, "sortOrder");
          v58 = objc_retainAutoreleasedReturnValue(v57);
          v59 = v58;
          v60 = CFSTR("D");
          if ( v58 )
            v60 = v58;
          v90 = objc_retain(v60);
          v95[0] = (__int64)CFSTR("IWenCaiQueryKey");
          v61 = _objc_msgSend(v93, "querykey");
          v87 = objc_retainAutoreleasedReturnValue(v61);
          v96[0] = (__int64)v87;
          v95[1] = (__int64)CFSTR("IWenCaiKeyIndex");
          v62 = _objc_msgSend(v93, "index");
          v63 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v62);
          v88 = objc_retainAutoreleasedReturnValue(v63);
          v96[1] = (__int64)v88;
          v95[2] = (__int64)CFSTR("IWenCaiTimestamp");
          v96[2] = (__int64)v91;
          v95[3] = (__int64)CFSTR("codelist");
          v96[3] = (__int64)v89;
          v95[4] = (__int64)CFSTR("sortbegin");
          v64 = -[ThumbnailBaseTableViewController begin](self, "begin");
          v65 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInteger:", v64);
          v66 = objc_retainAutoreleasedReturnValue(v65);
          v96[4] = (__int64)v66;
          v95[5] = (__int64)CFSTR("sortcount");
          v67 = -[ThumbnailBaseTableViewController count](self, "count");
          v68 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v67);
          v69 = objc_retainAutoreleasedReturnValue(v68);
          v96[5] = (__int64)v69;
          v95[6] = (__int64)CFSTR("sortorder");
          v96[6] = (__int64)v90;
          v70 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v96, v95, 7LL);
          objc_retainAutoreleasedReturnValue(v70);
          v71 = -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiCodeListRequestModule](
                  self,
                  "iWenCaiCodeListRequestModule");
          v72 = objc_retain(v71);
          v85[0] = _NSConcreteStackBlock;
          v85[1] = 3254779904LL;
          v85[2] = sub_100A194CE;
          v85[3] = &unk_1012DAED8;
          objc_copyWeak(&v86, &location);
          -[HXTableRequestModule request:params:callBack:](v72, "request:params:callBack:", 15LL, v73, v85);
          objc_destroyWeak(&v86);
          v75 = v93;
        }
        else
        {
          v75 = v48;
        }
      }
    }
  }
  else
  {
    v76 = -[ThumbnailZhangTingStocksPoolTableViewController completeCodeList](self, "completeCodeList");
    v77 = objc_retainAutoreleasedReturnValue(v76);
    v78 = +[HXTools getCodeListStrFromArr:](&OBJC_CLASS___HXTools, "getCodeListStrFromArr:", v77);
    v79 = objc_retainAutoreleasedReturnValue(v78);
    if ( _objc_msgSend(v79, v80) )
    {
      v83[0] = _NSConcreteStackBlock;
      v83[1] = 3254779904LL;
      v83[2] = sub_100A1939C;
      v83[3] = &unk_1012DAED8;
      objc_copyWeak(&v84, &location);
      -[ThumbnailBaseTableWithHangYeViewController requestBasicHQCodeListWithCodeList:callBack:](
        self,
        "requestBasicHQCodeListWithCodeList:callBack:",
        v79,
        v83);
      objc_destroyWeak(&v84);
    }
  }
  objc_destroyWeak(&location);
}

//----- (0000000100A19296) ----------------------------------------------------
void __fastcall sub_100A19296(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) && _objc_msgSend(v2, "count") )
  {
    v4 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v2);
    v9 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v9, "mutableCopy");
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "setOrderCodeMArray:", v6);
    v8 = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(v8, "requestDetailData");
  }
}

//----- (0000000100A1939C) ----------------------------------------------------
void __fastcall sub_100A1939C(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // rbx
  id *v7; // r12
  id *v10; // r12
  id *v12; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "sortID");
  if ( v5 == (id)84 )
  {
    v6 = _objc_msgSend(v2, "mutableCopy");
    v8 = objc_loadWeakRetained(v7);
    _objc_msgSend(v8, "setOrderCodeMArray:", v6);
  }
  else
  {
    v9 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v2);
    v6 = objc_retainAutoreleasedReturnValue(v9);
    v8 = _objc_msgSend(v6, "mutableCopy");
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v8);
  }
  v13 = objc_loadWeakRetained(v12);
  _objc_msgSend(v13, "requestDetailData");
}

//----- (0000000100A194CE) ----------------------------------------------------
void __fastcall sub_100A194CE(__int64 a1, void *a2)
{
  id *v3; // r12
  id WeakRetained; // rbx
  id *v10; // r12
  id *v12; // r12

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained(v3);
  v5 = _objc_msgSend(WeakRetained, "convertIWenCaiCodeListToUsefulCodeListResponse:", v2);
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v14 = v6;
  v7 = +[HXTools getCodesAndMarketArray:](&OBJC_CLASS___HXTools, "getCodesAndMarketArray:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( _objc_msgSend(v8, "count") )
  {
    v9 = _objc_msgSend(v8, "mutableCopy");
    v11 = objc_loadWeakRetained(v10);
    _objc_msgSend(v11, "setOrderCodeMArray:", v9);
    v13 = objc_loadWeakRetained(v12);
    _objc_msgSend(v13, "requestDetailData");
  }
}

//----- (0000000100A195E9) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController requestDetailData](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  NSMutableArray *v2; // rax
  NSMutableArray *v3; // rbx
  NSArray *v5; // rax
  NSArray *v6; // rbx
  NSMutableArray *v7; // rax
  NSNumber *v8; // rax
  NSNumber *v9; // rbx
  NSNumber *v11; // rax
  NSNumber *v12; // rbx
  HXStockModel *v13; // rax
  HXStockModel *v14; // rbx
  int v15; // r12d
  NSArray *v16; // rax
  NSMutableArray *v17; // rax
  NSMutableArray *v18; // rbx
  NSDictionary *v19; // rax
  NSDictionary *v20; // r13
  HXTableRequestModule *v22; // rax
  NSArray *v25; // rax
  NSNumber *v26; // rax
  NSNumber *v27; // rbx
  NSDictionary *v31; // rax
  NSDictionary *v32; // rbx
  NSDictionary *v34; // rax
  NSMutableArray *v35; // rax
  NSMutableArray *v36; // rbx
  NSDictionary *v37; // rax
  NSDictionary *v38; // r15
  HXTableRequestModule *v40; // rax
  HXTableRequestModule *v41; // r14
  _QWORD v42[4]; // [rsp+8h] [rbp-108h] BYREF
  id to; // [rsp+28h] [rbp-E8h] BYREF
  _QWORD v45[4]; // [rsp+38h] [rbp-D8h] BYREF
  _QWORD v47[4]; // [rsp+60h] [rbp-B0h] BYREF
  int v49; // [rsp+8Ch] [rbp-84h]
  id location; // [rsp+98h] [rbp-78h] BYREF

  v2 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
  {
    objc_initWeak(&location, self);
    v5 = -[ThumbnailZhangTingStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v7 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v6);
    v50 = objc_retainAutoreleasedReturnValue(v7);
    v8 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330325LL);
    v9 = objc_retainAutoreleasedReturnValue(v8);
    _objc_msgSend(v50, "containsObject:", v9);
    if ( v10 )
    {
      v11 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 330325LL);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      _objc_msgSend(v50, "removeObject:", v12);
    }
    if ( _objc_msgSend(v50, "count") )
    {
      v13 = -[HXBaseTableViewController stockModel](self, "stockModel");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      -[HXStockModel setMainRequestDidBack:](v14, "setMainRequestDidBack:", 0LL);
      v49 = v15;
      v54[0] = (__int64)CFSTR("datatype");
      v16 = -[ThumbnailZhangTingStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
      v55[0] = (__int64)objc_retainAutoreleasedReturnValue(v16);
      v54[1] = (__int64)CFSTR("StockCodesAndMarket");
      v17 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
      v18 = objc_retain(v17);
      v55[1] = (__int64)v18;
      v19 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v55, v54, 2LL);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v22 = -[HXBaseTableViewController tableRequestModule](self, "tableRequestModule");
      objc_retain(v22);
      v42[0] = _NSConcreteStackBlock;
      v42[1] = 3254779904LL;
      v42[2] = sub_100A19B3E;
      v42[3] = &unk_1012E7E00;
      objc_copyWeak(&to, &location);
      v44 = v49;
      _objc_msgSend(v23, "request:params:callBack:", 4LL, v20, v42);
      objc_destroyWeak(&to);
    }
    v25 = -[ThumbnailZhangTingStocksPoolTableViewController basicHQDataTypes](self, "basicHQDataTypes");
    objc_retainAutoreleasedReturnValue(v25);
    v26 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 84LL);
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v29 = (unsigned __int8)_objc_msgSend(v28, "containsObject:", v27);
    if ( v29 )
    {
      v45[0] = _NSConcreteStackBlock;
      v45[1] = 3254779904LL;
      v45[2] = sub_100A19DEC;
      v45[3] = &unk_1012DAED8;
      objc_copyWeak(&v46, &location);
      -[ThumbnailBaseTableWithHangYeViewController requestSuoShuHangYeDetail:](self, "requestSuoShuHangYeDetail:", v45);
      objc_destroyWeak(&v46);
    }
    v31 = -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiItemParams](self, "iWenCaiItemParams");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    _objc_msgSend(v32, "count");
    if ( v33 )
    {
      v52[0] = (__int64)CFSTR("IWenCaiQueryParams");
      v34 = -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiItemParams](self, "iWenCaiItemParams");
      v53[0] = (__int64)objc_retainAutoreleasedReturnValue(v34);
      v52[1] = (__int64)CFSTR("StockCodesAndMarket");
      v35 = -[HXBaseTableViewController orderCodeMArray](self, "orderCodeMArray");
      v36 = objc_retain(v35);
      v53[1] = (__int64)v36;
      v37 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v53, v52, 2LL);
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v40 = -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiRequestModule](self, "iWenCaiRequestModule");
      v41 = objc_retain(v40);
      v47[0] = _NSConcreteStackBlock;
      v47[1] = 3254779904LL;
      v47[2] = sub_100A19F1F;
      v47[3] = &unk_1012DAED8;
      objc_copyWeak(&v48, &location);
      -[HXTableRequestModule request:params:callBack:](v41, "request:params:callBack:", 12LL, v38, v47);
      objc_destroyWeak(&v48);
    }
    objc_destroyWeak(&location);
  }
}

//----- (0000000100A19B3E) ----------------------------------------------------
__int64 __fastcall sub_100A19B3E(__int64 a1, void *a2, void *a3)
{
  id WeakRetained; // r15
  id *v10; // r15
  NSDictionary *v16; // rax
  NSDictionary *v17; // r13
  _QWORD v22[4]; // [rsp+0h] [rbp-90h] BYREF
  id to; // [rsp+20h] [rbp-70h] BYREF
  __CFString *v29; // [rsp+50h] [rbp-40h] BYREF

  v28 = (id)a1;
  v4 = objc_retain(a2);
  v27 = objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v6 = _objc_msgSend(WeakRetained, "stockModel");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v7, "setMainRequestDidBack:", 1LL);
  v9(WeakRetained);
  v10 = (id *)(a1 + 32);
  v11 = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(v11, "dealWithRequestData:extension:", v4, v27);
  v12(v11);
  if ( *(_BYTE *)(a1 + 40) )
  {
    v24 = v4;
    v25 = objc_loadWeakRetained(v10);
    v14 = _objc_msgSend(v25, "zhangTingLeiXingReqeustModule");
    v28 = objc_retainAutoreleasedReturnValue(v14);
    v29 = CFSTR("StockCodesAndMarket");
    v26 = objc_loadWeakRetained(v10);
    v15 = _objc_msgSend(v26, "orderCodeMArray");
    v30 = objc_retainAutoreleasedReturnValue(v15);
    v16 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", &v30, &v29, 1LL);
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v22[0] = _NSConcreteStackBlock;
    v22[1] = 3254779904LL;
    v22[2] = sub_100A19D6B;
    v22[3] = &unk_1012DAED8;
    objc_copyWeak(&to, (id *)(a1 + 32));
    v18 = v28;
    _objc_msgSend(v28, "requestForLeiXing:reqCallBack:", v17, v22);
    objc_destroyWeak(&to);
    v4 = v24;
  }
  v13(v27);
  v20(v4);
  return __stack_chk_guard;
}

//----- (0000000100A19D6B) ----------------------------------------------------
void __fastcall sub_100A19D6B(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) )
  {
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    _objc_msgSend(WeakRetained, "dealWithZhangTingLeiXingResponse:", v2);
  }
}

//----- (0000000100A19DEC) ----------------------------------------------------
void __fastcall sub_100A19DEC(__int64 a1, void *a2)
{
  id WeakRetained; // r14

  v2 = objc_retain(a2);
  if ( _objc_msgSend(v2, "count") )
  {
    _objc_msgSend(v2, "copy");
    WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
    v4 = _objc_msgSend(WeakRetained, "stockModel");
    v5 = objc_retainAutoreleasedReturnValue(v4);
    _objc_msgSend(v5, "setSuoShuHangYeData:", v6);
    v14 = v2;
    v8 = objc_loadWeakRetained((id *)(a1 + 32));
    v9 = _objc_msgSend(v8, "stockModel");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11(v10, "mainRequestDidBack");
    v2 = v14;
    if ( v12 )
    {
      v13 = objc_loadWeakRetained((id *)(a1 + 32));
      _objc_msgSend(v13, "reloadData:", 0LL);
    }
  }
}

//----- (0000000100A19F1F) ----------------------------------------------------
void __fastcall sub_100A19F1F(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "dealWithIWenCaiResponse:", v2);
}

//----- (0000000100A19F79) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController dealWithRequestData:extension:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4)
{

  v4 = objc_retain(a3);
  v6 = _objc_msgSend(v5, "stockModel");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  _objc_msgSend(v7, "setMainStockTableViewDataArray:", v4);
  _objc_msgSend(v8, "reloadData:", 0LL);
  _objc_msgSend(v9, "setTableHasData:", 1LL);
  _objc_msgSend(v10, "setIsRequestFromZero:", 0LL);
  if ( (unsigned __int8)_objc_msgSend(v11, "isTableSwitched") )
  {
    v13 = _objc_msgSend(v12, "myTable");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v16 = _objc_msgSend(v15, "getScorllPosition");
    _objc_msgSend(v14, "scrollRowToVisible:", v16);
    _objc_msgSend(v17, "setIsTableSwitched:", 0LL);
  }
  v18 = _objc_msgSend(v12, "stockModel");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "mainStockTableViewDataArray");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  _objc_msgSend(v22, "setOrderCodeList:", v21);
  _objc_msgSend(v23, "order");
}

//----- (0000000100A1A0D8) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController dealWithZhangTingLeiXingResponse:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  id (**v4)(id, SEL, ...); // r15
  unsigned __int64 i; // r13
  SEL v13; // r12
  id (**v16)(id, SEL, ...); // r12
  id (**v21)(id, SEL, ...); // r12
  SEL v33; // [rsp+50h] [rbp-100h]
  SEL v34; // [rsp+58h] [rbp-F8h]
  id obj; // [rsp+98h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = &_objc_msgSend;
  v5 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v5) && _objc_msgSend(v3, "count") )
  {
    v38 = self;
    v6 = _objc_msgSend(&OBJC_CLASS___NSMutableDictionary, "dictionary");
    v41 = objc_retainAutoreleasedReturnValue(v6);
    v27 = 0LL;
    v28 = 0LL;
    v29 = 0LL;
    v30 = 0LL;
    v37 = v3;
    obj = objc_retain(v3);
    v39 = _objc_msgSend(obj, "countByEnumeratingWithState:objects:count:", &v27, v43, 16LL);
    if ( v39 )
    {
      v32 = *(_QWORD *)v28;
      do
      {
        v33 = "numberWithInt:";
        v34 = "thsStringForKey:";
        v35 = "thsNumberForKey:";
        v40 = "doubleValue";
        v36 = "setObject:forKey:";
        for ( i = 0LL; i < (unsigned __int64)v39; ++i )
        {
          if ( *(_QWORD *)v28 != v32 )
            objc_enumerationMutation(obj);
          v8 = *(id *)(*((_QWORD *)&v27 + 1) + 8 * i);
          v9 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSDictionary, "class");
          if ( (unsigned __int8)((id (*)(id, SEL, ...))v4)(v8, "isKindOfClass:", v9) )
          {
            v10 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, v33, 5LL);
            v11 = objc_retainAutoreleasedReturnValue(v10);
            v12 = ((id (*)(id, SEL, ...))v4)(v8, v34, v11);
            v31 = objc_retainAutoreleasedReturnValue(v12);
            v14 = ((id (*)(id, SEL, ...))v4)(&OBJC_CLASS___NSNumber, v13, 330325LL);
            v15 = objc_retainAutoreleasedReturnValue(v14);
            v16 = v4;
            v17 = v15;
            v18 = (void *)((__int64 (__fastcall *)(id, const char *, id))v16)(v8, v35, v15);
            v19 = objc_retainAutoreleasedReturnValue(v18);
            v20 = v17;
            v4 = v21;
            if ( v31 )
            {
              v22 = ((__int64 (__fastcall *)(NSArray *, const char *))v4)(&OBJC_CLASS___NSNumber, "class");
              if ( ((unsigned __int8 (__fastcall *)(id, const char *, __int64))v4)(v19, "isKindOfClass:", v22) )
              {
                if ( ((double (__fastcall *)(id, const char *))v4)(v19, v40) != 4294967295.0
                  && ((double (__fastcall *)(id, const char *))v4)(v19, v40) != 2147483648.0 )
                {
                  ((void (__fastcall *)(id, const char *, id, __int64))v4)(v41, v36, v19, v23);
                }
              }
            }
          }
        }
        v39 = ((id (*)(id, SEL, ...))v4)(obj, "countByEnumeratingWithState:objects:count:", &v27, v43, 16LL);
      }
      while ( v39 );
    }
    v25 = v38;
    ((void (*)(id, SEL, ...))v4)(v38, "setLeiXingDataDic:", v41);
    ((void (*)(id, SEL, ...))v4)(v25, "reloadData:", 0LL);
    v3 = v37;
  }
}

//----- (0000000100A1A470) ----------------------------------------------------
id __cdecl -[ThumbnailZhangTingStocksPoolTableViewController tableView:rowViewForRow:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        signed __int64 a4)
{

  v4 = objc_alloc((Class)&OBJC_CLASS___HXTableRowView_Highlight);
  v5 = _objc_msgSend(v4, "init");
  return objc_autoreleaseReturnValue(v5);
}

//----- (0000000100A1A499) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController tableViewSelectionIsChanging:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v12 = v7;
  v8 = _objc_msgSend(v7, "selectedRowIndexs");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v6, "isEqualToIndexSet:", v9);
  if ( !v10 )
  {
    _objc_msgSend(v12, "setSelectedRowIndexs:", v6);
    v11 = _objc_msgSend(v6, "lastIndex");
    _objc_msgSend(v12, "actionForTableViewSelectionDidChange:", v11);
    _objc_msgSend(v12, "setTableViewSelectionIsChanging:", 1LL);
  }
}

//----- (0000000100A1A582) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController tableViewSelectionDidChange:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  NSIndexSet *v7; // rax
  NSIndexSet *v8; // rbx

  v3 = _objc_msgSend(a3, "object");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "selectedRowIndexes");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = -[HXBaseTableViewController selectedRowIndexs](self, "selectedRowIndexs");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  if ( (unsigned __int8)_objc_msgSend(v6, "isEqualToIndexSet:", v8) )
  {
    -[HXBaseTableViewController tableViewSelectionIsChanging](self, "tableViewSelectionIsChanging");
    if ( v9 )
      goto LABEL_8;
  }
  else
  {
  }
  -[HXBaseTableViewController setSelectedRowIndexs:](self, "setSelectedRowIndexs:", v6);
  if ( (unsigned __int8)-[HXBaseTableViewController postSelectedRowDidChangedNotify](
                          self,
                          "postSelectedRowDidChangedNotify") )
  {
    v10 = _objc_msgSend(v6, "lastIndex");
    -[ThumbnailZhangTingStocksPoolTableViewController actionForTableViewSelectionDidChange:](
      self,
      "actionForTableViewSelectionDidChange:",
      v10);
  }
  -[HXBaseTableViewController setPostSelectedRowDidChangedNotify:](self, "setPostSelectedRowDidChangedNotify:", 1LL);
LABEL_8:
  -[HXBaseTableViewController setTableViewSelectionIsChanging:](self, "setTableViewSelectionIsChanging:", 0LL);
}

//----- (0000000100A1A6A7) ----------------------------------------------------
signed __int64 __cdecl -[ThumbnailZhangTingStocksPoolTableViewController numberOfRowsInTableView:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  return (signed __int64)-[ThumbnailBaseTableViewController allCodesNum](self, "allCodesNum", a3);
}

//----- (0000000100A1A6B9) ----------------------------------------------------
id __cdecl -[ThumbnailZhangTingStocksPoolTableViewController tableView:viewForTableColumn:row:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  SEL v7; // r12
  id (*v14)(id, SEL, ...); // r12
  NSNumber *v27; // rax
  NSNumber *v28; // rbx
  NSNumber *v34; // rax
  NSNumber *v35; // rbx
  NSNumber *v38; // rax
  NSNumber *v39; // rbx
  NSNumber *v42; // rax
  NSNumber *v43; // rbx
  NSNumber *v48; // rax
  NSNumber *v49; // rbx
  NSNumber *v56; // rax
  NSNumber *v57; // rbx
  bool v68; // zf
  SEL *v69; // rcx
  TextMarkManager *v72; // rax
  TextMarkManager *v73; // rbx
  int v183; // [rsp+BCh] [rbp-44h]

  v182 = a5;
  v6 = objc_retain(a3);
  v8 = _objc_msgSend(a4, v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v185 = self;
  v10 = _objc_msgSend(v6, "makeViewWithIdentifier:owner:", v9, self);
  objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(&OBJC_CLASS___ThumbnailTableCellView, "class");
  v184 = v12;
  if ( !(unsigned __int8)_objc_msgSend(v12, "isKindOfClass:", v11) )
  {
    v65 = v184;
    goto LABEL_16;
  }
  if ( (__int64)_objc_msgSend(v185, "begin") > v182
    || (v13 = _objc_msgSend(v185, "begin"), (char *)v14(v185, "count") + (unsigned __int64)v13 <= (char *)v182) )
  {
    _objc_msgSend(v184, "clearAllDatas");
    v65 = v184;
LABEL_16:
    objc_retain(v65);
    goto LABEL_17;
  }
  v15 = v182 - (_QWORD)_objc_msgSend(v185, "begin");
  v16 = _objc_msgSend(v185, "stockModel");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "mainStockTableViewDataArray");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(v19, "mutableCopy");
  v170 = v20;
  v182 = v15;
  v22 = _objc_msgSend(v20, "thsDictionaryAtIndex:", v15);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  _objc_msgSend(v23, "mutableCopy");
  v25 = _objc_msgSend(v24, "count");
  v174 = v26;
  if ( v25 )
  {
    v27 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 5LL);
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v30 = _objc_msgSend(v29, "thsStringForKey:", v28);
    v31 = objc_retainAutoreleasedReturnValue(v30);
    v32 = +[HXTools getCodeString:](&OBJC_CLASS___HXTools, "getCodeString:", v31);
    v172 = objc_retainAutoreleasedReturnValue(v32);
    v175 = v31;
    v33 = +[HXTools getMarketString:](&OBJC_CLASS___HXTools, "getMarketString:", v31);
    v178 = objc_retainAutoreleasedReturnValue(v33);
    v34 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 55LL);
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v37 = _objc_msgSend(v36, "thsStringForKey:", v35);
    v171 = objc_retainAutoreleasedReturnValue(v37);
    v38 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 6LL);
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v41 = _objc_msgSend(v40, "thsNumberForKey:", v39);
    v176 = objc_retainAutoreleasedReturnValue(v41);
    v42 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 10LL);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v45 = _objc_msgSend(v44, "thsNumberForKey:", v43);
    v173 = objc_retainAutoreleasedReturnValue(v45);
    v186 = +[HXTools getPrecisionTypeWithMarket:Code:](
             &OBJC_CLASS___HXTools,
             "getPrecisionTypeWithMarket:Code:",
             v178,
             v172);
    *(double *)&v179 = ((double (__fastcall *)(__objc2_class *, const char *, id))_objc_msgSend)(
                         &OBJC_CLASS___HXTools,
                         "getVolumeUintWithMarket:",
                         v178);
    v46 = _objc_msgSend(v185, "sortID");
    v47 = +[ThumbnailUtils getNewSortIDForNormalTable:](
            &OBJC_CLASS___ThumbnailUtils,
            "getNewSortIDForNormalTable:",
            v46);
    v48 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v47);
    v49 = objc_retainAutoreleasedReturnValue(v48);
    v51 = _objc_msgSend(v50, "thsNumberForKey:", v49);
    v52 = objc_retainAutoreleasedReturnValue(v51);
    v181 = v47;
    if ( v47 == (id)199112 )
    {
      v53 = +[ThumbnailUtils getSortItemDataWithSortID:dataDic:](
              &OBJC_CLASS___ThumbnailUtils,
              "getSortItemDataWithSortID:dataDic:",
              199112LL,
              v174);
      v54 = objc_retainAutoreleasedReturnValue(v53);
      v55 = v54;
      if ( v54 && (!v52 || !(unsigned __int8)_objc_msgSend(v54, "isEqualToNumber:", v52)) )
      {
        v180 = objc_retain(v55);
        v56 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", 199112LL);
        v57 = objc_retainAutoreleasedReturnValue(v56);
        _objc_msgSend(v174, "setObject:forKey:", v58, v57);
        _objc_msgSend(v170, "setObject:atIndexedSubscript:", v174, v182);
        v59 = _objc_msgSend(v185, "stockModel");
        v60 = objc_retainAutoreleasedReturnValue(v59);
        _objc_msgSend(v60, "setMainStockTableViewDataArray:", v61);
        v52 = v180;
      }
    }
    v180 = v52;
    if ( _objc_msgSend(v185, "sortID") && _objc_msgSend(v185, "sortID") != (id)12345670 )
    {
      v183 = 0;
    }
    else
    {
      v62 = _objc_msgSend(v185, "wenCaiSortIdentifier");
      v63 = objc_retainAutoreleasedReturnValue(v62);
      v64 = _objc_msgSend(v63, "length");
      LOBYTE(v64) = v64 != 0LL;
      v183 = (int)v64;
    }
    v68 = (unsigned __int8)+[SelfStock isSelfStock:](&OBJC_CLASS___SelfStock, "isSelfStock:", v175) == 0;
    v69 = &selRef_markedTextColor;
    if ( v68 )
      v69 = &selRef_normalTextColor;
    v70 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, *v69);
    v71 = objc_retainAutoreleasedReturnValue(v70);
    v72 = +[TextMarkManager sharedInstance](&OBJC_CLASS___TextMarkManager, "sharedInstance");
    v73 = objc_retainAutoreleasedReturnValue(v72);
    v74 = -[TextMarkManager getStockCodeColorIdx:](v73, "getStockCodeColorIdx:", v175);
    if ( v74 )
    {
      v75 = +[TextMarkMenuItemView getColorForDotsAtIndex:](
              &OBJC_CLASS___TextMarkMenuItemView,
              "getColorForDotsAtIndex:",
              v74);
      v76 = objc_retainAutoreleasedReturnValue(v75);
    }
    else
    {
      v76 = v71;
    }
    v77 = _objc_msgSend(v184, "stockName");
    v78 = objc_retainAutoreleasedReturnValue(v77);
    v167 = v76;
    _objc_msgSend(v78, "setTextColor:", v76);
    v79 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v80 = objc_retainAutoreleasedReturnValue(v79);
    v82 = _objc_msgSend(v81, "stockCode");
    v83 = objc_retainAutoreleasedReturnValue(v82);
    _objc_msgSend(v83, "setTextColor:", v80);
    v84 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
            &OBJC_CLASS___ThumbnailUtils,
            "getSortItemColor:sortItem:zuoShou:",
            10LL,
            v173,
            v176);
    v85 = objc_retainAutoreleasedReturnValue(v84);
    v87 = _objc_msgSend(v86, "currentPrice");
    v88 = objc_retainAutoreleasedReturnValue(v87);
    _objc_msgSend(v88, "setTextColor:", v85);
    v89 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v171);
    v90 = objc_retainAutoreleasedReturnValue(v89);
    v92 = _objc_msgSend(v91, "stockName");
    v93 = objc_retainAutoreleasedReturnValue(v92);
    _objc_msgSend(v93, "setStringValue:", v90);
    v94 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v172);
    v95 = objc_retainAutoreleasedReturnValue(v94);
    v97 = _objc_msgSend(v96, "stockCode");
    v98 = objc_retainAutoreleasedReturnValue(v97);
    _objc_msgSend(v98, "setStringValue:", v95);
    v99 = v179;
    v100 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
             &OBJC_CLASS___ThumbnailUtils,
             "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
             v173,
             10LL,
             v186,
             v178,
             *(double *)&v179);
    v101 = objc_retainAutoreleasedReturnValue(v100);
    v103 = _objc_msgSend(v102, "currentPrice");
    v104 = objc_retainAutoreleasedReturnValue(v103);
    _objc_msgSend(v104, "setStringValue:", v101);
    v105 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
    v106 = objc_retainAutoreleasedReturnValue(v105);
    if ( v180 && (unsigned __int8)v183 != 1 )
    {
      v107 = +[ThumbnailUtils getSortItemColor:sortItem:zuoShou:](
               &OBJC_CLASS___ThumbnailUtils,
               "getSortItemColor:sortItem:zuoShou:",
               v181,
               v180,
               v176);
      v108 = objc_retainAutoreleasedReturnValue(v107);
      v106 = v108;
    }
    v109 = _objc_msgSend(v184, "priceChange");
    v110 = objc_retainAutoreleasedReturnValue(v109);
    v177 = v106;
    _objc_msgSend(v110, "setTextColor:", v106);
    if ( _objc_msgSend(v111, "sortID") == (id)330325 )
    {
      v113 = _objc_msgSend(v112, "leiXingDataDic");
      v114 = objc_retainAutoreleasedReturnValue(v113);
      v115 = _objc_msgSend(v114, "thsNumberForKey:", v175);
      v116 = objc_retainAutoreleasedReturnValue(v115);
      v117 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
      v186 = obj;
      if ( (unsigned __int8)_objc_msgSend(v116, "isKindOfClass:", v117)
        && (_objc_msgSend(v116, "doubleValue"), *(double *)&v179 != 4294967295.0) )
      {
        _objc_msgSend(v116, "doubleValue");
        v118 = (__int64)v181;
        if ( *(double *)&v179 != 2147483648.0 )
        {
          v133 = _objc_msgSend(v185, "formatZhangTingLeiXingData:", v116);
          v186 = objc_retainAutoreleasedReturnValue(v133);
        }
      }
      else
      {
        v118 = (__int64)v181;
      }
    }
    else if ( (_BYTE)v183 )
    {
      v186 = &OBJC_CLASS___ThumbnailUtils;
      v119 = _objc_msgSend(v112, "stockModel");
      *(double *)&v179 = COERCE_DOUBLE(objc_retainAutoreleasedReturnValue(v119));
      v120 = _objc_msgSend(v179, "iWenCaiData");
      v168 = objc_retainAutoreleasedReturnValue(v120);
      v122 = _objc_msgSend(v121, "tableKey");
      v169 = objc_retainAutoreleasedReturnValue(v122);
      v124 = _objc_msgSend(v123, "wenCaiSortIdentifier");
      v125 = objc_retainAutoreleasedReturnValue(v124);
      v126 = v177;
      v166 = v177;
      v127 = v168;
      v128 = +[ThumbnailUtils getIWenCaiStringValueWithDataSource:index:tableKey:identifier:textColor:](
               &OBJC_CLASS___ThumbnailUtils,
               "getIWenCaiStringValueWithDataSource:index:tableKey:identifier:textColor:",
               v168,
               v182,
               v169,
               v125,
               &v166);
      v186 = objc_retainAutoreleasedReturnValue(v128);
      v129 = objc_retain(v166);
      v177 = v129;
      v118 = (__int64)v181;
    }
    else
    {
      v118 = (__int64)v181;
      if ( v180 )
      {
        v99 = v179;
        v131 = +[ThumbnailUtils formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:](
                 &OBJC_CLASS___ThumbnailUtils,
                 "formatSortItem:sortId:pricePrecision:volumnUnit:andMarket:",
                 v180,
                 v181,
                 v186,
                 v178,
                 *(double *)&v179);
        v132 = (char *)objc_retainAutoreleasedReturnValue(v131);
      }
      else
      {
        v132 = obj;
      }
      v186 = v132;
    }
    if ( v118 == 84 )
    {
      v134 = _objc_msgSend(v185, "getHangYeSortItemDataWithSortID:index:", 84LL, v182);
      v135 = objc_retainAutoreleasedReturnValue(v134);
      v186 = v135;
    }
    v136 = _objc_msgSend(v184, "priceChange");
    v137 = objc_retainAutoreleasedReturnValue(v136);
    _objc_msgSend(v185, "resetHangYeItemDataFont:textField:", v118, v137);
    v138 = +[HXTools loadStringForCell:](&OBJC_CLASS___HXTools, "loadStringForCell:", v186);
    v139 = objc_retainAutoreleasedReturnValue(v138);
    v141 = _objc_msgSend(v140, "priceChange");
    v142 = objc_retainAutoreleasedReturnValue(v141);
    _objc_msgSend(v142, "setStringValue:", v139);
    v144 = _objc_msgSend(v143, "priceChange");
    v145 = objc_retainAutoreleasedReturnValue(v144);
    _objc_msgSend(v145, "setLineBreakMode:", 0LL);
    v147 = _objc_msgSend(v146, "priceChange");
    v148 = objc_retainAutoreleasedReturnValue(v147);
    _objc_msgSend(v148, "setToolTip:", 0LL);
    if ( (_BYTE)v183 )
    {
      v149 = _objc_msgSend(v184, "priceChange");
      v150 = objc_retainAutoreleasedReturnValue(v149);
      _objc_msgSend(v150, "setLineBreakMode:", 4LL);
      v152 = _objc_msgSend(v151, "priceChange");
      v153 = objc_retainAutoreleasedReturnValue(v152);
      v154 = _objc_msgSend(v153, "font");
      v155 = objc_retainAutoreleasedReturnValue(v154);
      v156 = v186;
      _objc_msgSend(v185, "widthOfString:withFont:", v186, v155);
      v185 = v99;
      v158 = _objc_msgSend(v157, "priceChange");
      v159 = (const char *)objc_retainAutoreleasedReturnValue(v158);
      v160 = (char *)v159;
      if ( v159 )
      {
        objc_msgSend_stret(&v164, v159, "bounds");
        v161 = *(double *)&v165 + -4.0;
      }
      else
      {
        v165 = 0LL;
        v164 = 0LL;
        v161 = -4.0;
      }
      *(double *)&v182 = v161;
      v186 = v156;
      if ( *(double *)&v185 >= *(double *)&v182 )
      {
        v162 = _objc_msgSend(v184, "priceChange");
        v163 = objc_retainAutoreleasedReturnValue(v162);
        _objc_msgSend(v163, "setToolTip:", v186);
      }
    }
    objc_retain(v184);
  }
  else
  {
    _objc_msgSend(v184, "clearAllDatas");
    objc_retain(v184);
  }
LABEL_17:
  v66 = v184;
  return objc_autoreleaseReturnValue(v66);
}

//----- (0000000100A1B574) ----------------------------------------------------
id __cdecl -[ThumbnailZhangTingStocksPoolTableViewController formatZhangTingLeiXingData:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  __CFString *v7; // r14

  objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSNumber, "class");
  v7 = (__CFString *)obj;
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v4) )
  {
    _objc_msgSend(v6, "doubleValue");
    if ( v3 != 4294967295.0 )
    {
      _objc_msgSend(v6, "doubleValue");
      if ( v3 != 2147483648.0 )
      {
        if ( _objc_msgSend(v6, "integerValue") == (id)30 )
        {
          v7 = CFSTR("一字板");
        }
        else if ( _objc_msgSend(v6, "integerValue") == (id)20 )
        {
          v7 = CFSTR("T字板");
        }
        else
        {
          v7 = (__CFString *)obj;
          if ( _objc_msgSend(v6, "integerValue") == (id)10 )
            v7 = CFSTR("换手板");
        }
      }
    }
  }
  return v7;
}

//----- (0000000100A1B66B) ----------------------------------------------------
NSString *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController tableKey](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSString *)self->super.super._quotaCount;
}

//----- (0000000100A1B67C) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setTableKey:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._quotaCount, a3);
}

//----- (0000000100A1B690) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController completeCodeList](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSArray *)self->super.super._quotaIndex;
}

//----- (0000000100A1B6A1) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setCompleteCodeList:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._quotaIndex, a3);
}

//----- (0000000100A1B6B5) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setCodeListRequestModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._visibleY, a3);
}

//----- (0000000100A1B6C9) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setBasicHQCodeListRequestModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong(&self->super.super._invokeRowSwitchCallBack, a3);
}

//----- (0000000100A1B6DD) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setIWenCaiCodeListRequestModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._thumbTableSrcollDirection, a3);
}

//----- (0000000100A1B6F1) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setIWenCaiRequestModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._countDownTimerForScroll, a3);
}

//----- (0000000100A1B705) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController basicHQDataTypes](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSArray *)objc_getProperty(self, a2, 376LL, 0);
}

//----- (0000000100A1B718) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setBasicHQDataTypes:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 376LL);
}

//----- (0000000100A1B729) ----------------------------------------------------
NSArray *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController orderHQDataTypes](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSArray *)objc_getProperty(self, a2, 384LL, 0);
}

//----- (0000000100A1B73C) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setOrderHQDataTypes:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 384LL);
}

//----- (0000000100A1B74D) ----------------------------------------------------
NSDictionary *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController iWenCaiItemParams](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSDictionary *)objc_getProperty(self, a2, 392LL, 0);
}

//----- (0000000100A1B760) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setIWenCaiItemParams:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 392LL);
}

//----- (0000000100A1B771) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setRefactorHQCodeListRequestModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._kycCompleteCodeListRequestQuery, a3);
}

//----- (0000000100A1B785) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setZhangTingLeiXingReqeustModule:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._visibleRangeCache, a3);
}

//----- (0000000100A1B799) ----------------------------------------------------
NSDictionary *__cdecl -[ThumbnailZhangTingStocksPoolTableViewController leiXingDataDic](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  return (NSDictionary *)self->super.super._visibleRangeCache.length;
}

//----- (0000000100A1B7AA) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController setLeiXingDataDic:](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2,
        id a3)
{
  objc_storeStrong((id *)&self->super.super._visibleRangeCache.length, a3);
}

//----- (0000000100A1B7BE) ----------------------------------------------------
void __cdecl -[ThumbnailZhangTingStocksPoolTableViewController .cxx_destruct](
        ThumbnailZhangTingStocksPoolTableViewController *self,
        SEL a2)
{
  objc_storeStrong((id *)&self->super.super._visibleRangeCache.length, 0LL);
  objc_storeStrong((id *)&self->super.super._visibleRangeCache, 0LL);
  objc_storeStrong((id *)&self->super.super._kycCompleteCodeListRequestQuery, 0LL);
  objc_storeStrong((id *)&self->super.super._kycCompleteCodeListRequestDate, 0LL);
  objc_storeStrong((id *)&self->super.super._kycLastCodeAndMarketArr, 0LL);
  objc_storeStrong((id *)&self->super.super._kycCompleteCodeListRequestModule, 0LL);
  objc_storeStrong((id *)&self->super.super._countDownTimerForScroll, 0LL);
  objc_storeStrong((id *)&self->super.super._thumbTableSrcollDirection, 0LL);
  objc_storeStrong(&self->super.super._invokeRowSwitchCallBack, 0LL);
  objc_storeStrong((id *)&self->super.super._visibleY, 0LL);
  objc_storeStrong((id *)&self->super.super._quotaIndex, 0LL);
  objc_storeStrong((id *)&self->super.super._quotaCount, 0LL);
}

