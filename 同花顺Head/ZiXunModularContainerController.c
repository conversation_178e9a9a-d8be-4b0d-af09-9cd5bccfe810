void __cdecl -[ZiXunModularContainerController viewDidLoad](ZiXunModularContainerController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunModularContainerController;
  -[PanKouModularBaseViewController viewDidLoad](&v2, "viewDidLoad");
  -[ZiXunModularContainerController setViewState](self, "setViewState");
}

//----- (00000001004F7C46) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController viewDidAppear](ZiXunModularContainerController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunModularContainerController;
  -[HXBaseViewController viewDidAppear](&v2, "viewDidAppear");
}

//----- (00000001004F7C75) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController requestForModularData:market:](
        ZiXunModularContainerController *self,
        SEL a2,
        id a3,
        id a4)
{

  if ( -[ZiXunModularContainerController textID](self, "textID", a3, a4) )
  {
    v4 = -[ZiXunModularContainerController textID](self, "textID");
    -[ZiXunModularContainerController setTableVCToContainerWithTextID:](self, "setTableVCToContainerWithTextID:", v4);
  }
}

//----- (00000001004F7CCE) ----------------------------------------------------
ZiXunTableViewController *__cdecl -[ZiXunModularContainerController newsTableVC](
        ZiXunModularContainerController *self,
        SEL a2)
{
  NSString *market; // rdi
  NSString *v5; // rax
  NSString *v6; // rdi

  market = self->super.super._market;
  if ( !market )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunModularTableViewController);
    v5 = (NSString *)_objc_msgSend(v4, "initWithNibName:bundle:", CFSTR("ZiXunModularTableViewController"), 0LL);
    v6 = self->super.super._market;
    self->super.super._market = v5;
    market = self->super.super._market;
  }
  return (ZiXunTableViewController *)objc_retainAutoreleaseReturnValue(market);
}

//----- (00000001004F7D28) ----------------------------------------------------
WebViewWindowController *__cdecl -[ZiXunModularContainerController newsViewWC](
        ZiXunModularContainerController *self,
        SEL a2)
{

  v3 = *(void **)&self->super._noRecordFrame;
  if ( !v3 )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___WebViewWindowController);
    v5 = _objc_msgSend(v4, "initWithWindowNibName:", CFSTR("WebViewWindowController"));
    v6 = *(void **)&self->super._noRecordFrame;
    *(_QWORD *)&self->super._noRecordFrame = v5;
    v3 = *(void **)&self->super._noRecordFrame;
  }
  return (WebViewWindowController *)objc_retainAutoreleaseReturnValue(v3);
}

//----- (00000001004F7D80) ----------------------------------------------------
id __cdecl -[ZiXunModularContainerController myOpenUrlBlock](ZiXunModularContainerController *self, SEL a2)
{
  NSMutableDictionary *paramsMDic; // rdi
  objc_class *v4; // rax
  NSMutableDictionary *v8; // rax
  _QWORD v10[4]; // [rsp+0h] [rbp-50h] BYREF
  id to; // [rsp+20h] [rbp-30h] BYREF
  id location[5]; // [rsp+28h] [rbp-28h] BYREF

  paramsMDic = self->super.super._paramsMDic;
  if ( !paramsMDic )
  {
    objc_initWeak(location, self);
    v10[0] = _NSConcreteStackBlock;
    v10[1] = 3254779904LL;
    v10[2] = sub_1004F7E2E;
    v10[3] = &unk_1012DC1F0;
    objc_copyWeak(&to, location);
    v4 = objc_retainBlock(v10);
    v6 = *(Class *)((char *)&self->super.super.super.super.super.isa + v5);
    *(Class *)((char *)&self->super.super.super.super.super.isa + v5) = v4;
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
    paramsMDic = *(NSMutableDictionary **)((char *)&self->super.super.super.super.super.isa + v7);
  }
  v8 = objc_retainBlock(paramsMDic);
  return objc_autoreleaseReturnValue(v8);
}

//----- (00000001004F7E2E) ----------------------------------------------------
void __fastcall sub_1004F7E2E(__int64 a1, void *a2)
{
  id WeakRetained; // rbx

  v2 = objc_retain(a2);
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  _objc_msgSend(WeakRetained, "openNewsWebWindowWithNewsUrl:", v2);
}

//----- (00000001004F7E88) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setViewState](ZiXunModularContainerController *self, SEL a2)
{
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v11)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v21)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v31)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v39)(id, SEL, ...); // r12
  id (*v43)(id, SEL, ...); // r12
  id (*v47)(id, SEL, ...); // r12
  id (*v50)(id, SEL, ...); // r12
  id (*v54)(id, SEL, ...); // r12
  id (*v57)(id, SEL, ...); // r12
  id (*v62)(id, SEL, ...); // r12
  _QWORD v65[4]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+28h] [rbp-38h] BYREF
  id location[6]; // [rsp+30h] [rbp-30h] BYREF

  v2 = _objc_msgSend(self, "view");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(&OBJC_CLASS___HXDragAndDropBaseView, "class");
  v7 = (unsigned __int8)v6(v3, "isKindOfClass:", v5);
  if ( v7 )
  {
    v9 = v8(self, "view");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = v11(self, "className");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14(v10, "setTargetViewControllerName:", v13);
  }
  v15 = v8(&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v18 = v17(self, "tableContainerView");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v20(v19, "setBackgroundColor:", v16);
  v22 = v21(self, "view");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v25 = v24(self, "titleContainerView");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v27(v26, "setDragImageView:", v23);
  v29 = v28(&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v32 = v31(self, "titleContainerView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v34(v33, "setBackgroundColor:", v30);
  v36 = v35(self, "titleContainerView");
  v37 = objc_retainAutoreleasedReturnValue(v36);
  v38(v37, "setTopBorder:", 1LL);
  v40 = v39(self, "titleContainerView");
  v41 = objc_retainAutoreleasedReturnValue(v40);
  v42(v41, "setBottomBorder:", 1LL);
  v44 = v43(self, "titleContainerView");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46(v45, "setBorderWidth:", 1.0);
  v48 = v47(&OBJC_CLASS___HXThemeManager, "minorModuleLineColor");
  v49 = objc_retainAutoreleasedReturnValue(v48);
  v51 = v50(self, "titleContainerView");
  v52 = objc_retainAutoreleasedReturnValue(v51);
  v53(v52, "setBorderColor:", v49);
  v55 = v54(&OBJC_CLASS___HXThemeManager, "normalTextColor");
  v56 = objc_retainAutoreleasedReturnValue(v55);
  v58 = v57(self, "titleTextField");
  v59 = objc_retainAutoreleasedReturnValue(v58);
  v60(v59, "setTextColor:", v56);
  v61(self, "disableScroll");
  objc_initWeak(location, self);
  v63 = v62(self, "resizeView");
  v64 = objc_retainAutoreleasedReturnValue(v63);
  v65[0] = _NSConcreteStackBlock;
  v65[1] = 3254779904LL;
  v65[2] = sub_1004F823C;
  v65[3] = &unk_1012DBC30;
  objc_copyWeak(&to, location);
  _objc_msgSend(v64, "setViewResizeCallBackBlock:", v65);
  objc_destroyWeak(&to);
  objc_destroyWeak(location);
}

//----- (00000001004F823C) ----------------------------------------------------
void __fastcall sub_1004F823C(__int64 a1, double a2)
{
  id WeakRetained; // r14
  id *v9; // r12
  id *v12; // r12
  id *v15; // r12
  id *v28; // r14
  SEL v32; // r12
  SEL v37; // r12
  id *v44; // r14
  SEL v47; // r12
  SEL v51; // r12
  id *v63; // r14
  SEL v67; // r12
  SEL v72; // r12
  id *v80; // r13
  id *v113; // r12
  id *location; // [rsp+1A8h] [rbp-68h]

  v148 = a2;
  WeakRetained = objc_loadWeakRetained((id *)(a1 + 32));
  v3 = _objc_msgSend(WeakRetained, "view");
  v4 = (const char *)objc_retainAutoreleasedReturnValue(v3);
  v5 = (char *)v4;
  if ( v4 )
  {
    objc_msgSend_stret(&v117, v4, "frame");
    v6 = *((double *)&v118 + 1);
  }
  else
  {
    v118 = 0LL;
    v117 = 0LL;
    v6 = 0.0;
  }
  v7 = v148;
  v8 = v6 - v148;
  v148 = v8;
  v142 = objc_loadWeakRetained(v9);
  v10 = _objc_msgSend(v142, "newsTableVC");
  v143 = objc_retainAutoreleasedReturnValue(v10);
  v11 = _objc_msgSend(v143, "firstTable");
  v144 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v144, "rowHeight");
  *(double *)&location = v8 + 22.0;
  v145 = objc_loadWeakRetained(v12);
  v13 = _objc_msgSend(v145, "newsTableVC");
  v146 = objc_retainAutoreleasedReturnValue(v13);
  v14 = _objc_msgSend(v146, "firstTable");
  v140 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v140, "intercellSpacing");
  *(double *)&v147 = v7 + *(double *)&location;
  location = v15;
  v16 = objc_loadWeakRetained(v15);
  v17 = _objc_msgSend(v16, "newsTableVC");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = _objc_msgSend(v18, "firstTable");
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21 = _objc_msgSend(v20, "headerView");
  v22 = (const char *)objc_retainAutoreleasedReturnValue(v21);
  v23 = (char *)v22;
  if ( v22 )
  {
    objc_msgSend_stret(&v119, v22, "frame");
    v24 = *((double *)&v120 + 1);
  }
  else
  {
    v120 = 0LL;
    v119 = 0LL;
    v24 = 0.0;
  }
  v25 = *(double *)&v147 + v24;
  *(double *)&v147 = *(double *)&v147 + v24;
  v27 = *(double *)&v147;
  if ( *(double *)&v147 > v148 )
  {
    v28 = location;
    *(double *)&v147 = COERCE_DOUBLE(objc_loadWeakRetained(location));
    v29 = _objc_msgSend(v147, "newsTableVC");
    v142 = objc_retainAutoreleasedReturnValue(v29);
    v30 = _objc_msgSend(v142, "firstTable");
    v143 = objc_retainAutoreleasedReturnValue(v30);
    _objc_msgSend(v143, "rowHeight");
    v148 = v27 + 22.0;
    v144 = objc_loadWeakRetained(v28);
    v31 = _objc_msgSend(v144, "newsTableVC");
    v145 = objc_retainAutoreleasedReturnValue(v31);
    v33 = _objc_msgSend(v145, v32);
    v146 = objc_retainAutoreleasedReturnValue(v33);
    _objc_msgSend(v146, "intercellSpacing");
    v148 = v25 + v148;
    v34 = objc_loadWeakRetained(v28);
    v35 = _objc_msgSend(v34, "newsTableVC");
    v36 = objc_retainAutoreleasedReturnValue(v35);
    v38 = _objc_msgSend(v36, v37);
    v39 = objc_retainAutoreleasedReturnValue(v38);
    v40 = _objc_msgSend(v39, "headerView");
    v41 = (const char *)objc_retainAutoreleasedReturnValue(v40);
    v42 = (char *)v41;
    if ( v41 )
    {
      objc_msgSend_stret(&v121, v41, "frame");
      v27 = *((double *)&v122 + 1);
    }
    else
    {
      v122 = 0LL;
      v121 = 0LL;
      v27 = 0.0;
    }
    v25 = v148 + v27;
    v148 = v148 + v27;
  }
  v44 = location;
  v142 = objc_loadWeakRetained(location);
  v45 = _objc_msgSend(v142, "newsTableVC");
  v143 = objc_retainAutoreleasedReturnValue(v45);
  v46 = _objc_msgSend(v143, "firstTable");
  v144 = objc_retainAutoreleasedReturnValue(v46);
  _objc_msgSend(v144, "rowHeight");
  *(double *)&v147 = v27;
  v145 = objc_loadWeakRetained(v44);
  v48 = _objc_msgSend(v145, v47);
  v146 = objc_retainAutoreleasedReturnValue(v48);
  v49 = _objc_msgSend(v146, "firstTable");
  v140 = objc_retainAutoreleasedReturnValue(v49);
  _objc_msgSend(v140, "intercellSpacing");
  *(double *)&v147 = (v25 + *(double *)&v147) * 50.0 + 22.0;
  v50 = objc_loadWeakRetained(v44);
  v52 = _objc_msgSend(v50, v51);
  v53 = objc_retainAutoreleasedReturnValue(v52);
  v54 = _objc_msgSend(v53, "firstTable");
  v55 = objc_retainAutoreleasedReturnValue(v54);
  v56 = _objc_msgSend(v55, "headerView");
  v57 = (const char *)objc_retainAutoreleasedReturnValue(v56);
  v58 = (char *)v57;
  if ( v57 )
  {
    objc_msgSend_stret(&v123, v57, "frame");
    v59 = *((double *)&v124 + 1);
  }
  else
  {
    v124 = 0LL;
    v123 = 0LL;
    v59 = 0.0;
  }
  v60 = *(double *)&v147 + v59;
  *(double *)&v147 = *(double *)&v147 + v59;
  v62 = v148;
  if ( v148 > *(double *)&v147 )
  {
    v63 = location;
    *(double *)&v147 = COERCE_DOUBLE(objc_loadWeakRetained(location));
    v64 = _objc_msgSend(v147, "newsTableVC");
    v142 = objc_retainAutoreleasedReturnValue(v64);
    v65 = _objc_msgSend(v142, "firstTable");
    v143 = objc_retainAutoreleasedReturnValue(v65);
    _objc_msgSend(v143, "rowHeight");
    v148 = v62;
    v144 = objc_loadWeakRetained(v63);
    v66 = _objc_msgSend(v144, "newsTableVC");
    v145 = objc_retainAutoreleasedReturnValue(v66);
    v68 = _objc_msgSend(v145, v67);
    v146 = objc_retainAutoreleasedReturnValue(v68);
    _objc_msgSend(v146, "intercellSpacing");
    v148 = (v60 + v148) * 50.0 + 22.0;
    v69 = objc_loadWeakRetained(v63);
    v70 = _objc_msgSend(v69, "newsTableVC");
    v71 = objc_retainAutoreleasedReturnValue(v70);
    v73 = _objc_msgSend(v71, v72);
    v74 = objc_retainAutoreleasedReturnValue(v73);
    v75 = _objc_msgSend(v74, "headerView");
    v76 = (const char *)objc_retainAutoreleasedReturnValue(v75);
    v77 = (char *)v76;
    if ( v76 )
    {
      objc_msgSend_stret(&v125, v76, "frame");
      v78 = *((double *)&v126 + 1);
    }
    else
    {
      v126 = 0LL;
      v125 = 0LL;
      v78 = 0.0;
    }
    v148 = v148 + v78;
  }
  v80 = location;
  v81 = objc_loadWeakRetained(location);
  v82 = _objc_msgSend(v81, "view");
  v83 = (const char *)objc_retainAutoreleasedReturnValue(v82);
  v84 = (char *)v83;
  if ( v83 )
  {
    objc_msgSend_stret(&v127, v83, "frame");
    v85 = *((double *)&v128 + 1);
  }
  else
  {
    v128 = 0LL;
    v127 = 0LL;
    v85 = 0.0;
  }
  v148 = v85 - v148;
  if ( v148 != 0.0 )
  {
    v86 = COERCE_DOUBLE(objc_loadWeakRetained(v80));
    v87 = _objc_msgSend(*(id *)&v86, "view");
    objc_retainAutoreleasedReturnValue(v87);
    v142 = objc_loadWeakRetained(v80);
    v88 = _objc_msgSend(v142, "view");
    v89 = objc_retainAutoreleasedReturnValue(v88);
    *(double *)&v147 = v86;
    v143 = v89;
    v91 = v90;
    if ( v89 )
    {
      objc_msgSend_stret(v129, (SEL)v89, "frame");
      *(_QWORD *)&v138 = *(_QWORD *)&v129[0];
    }
    else
    {
      memset(v129, 0, sizeof(v129));
      *(_QWORD *)&v138 = 0LL;
    }
    v92 = objc_loadWeakRetained(v80);
    v93 = _objc_msgSend(v92, "view");
    v94 = objc_retainAutoreleasedReturnValue(v93);
    v144 = v92;
    v145 = v94;
    v140 = v91;
    if ( v94 )
    {
      objc_msgSend_stret(v130, (SEL)v94, v95);
      v96 = *((_QWORD *)&v130[0] + 1);
    }
    else
    {
      memset(v130, 0, sizeof(v130));
      v96 = 0LL;
    }
    *((_QWORD *)&v138 + 1) = v96;
    v97 = objc_loadWeakRetained(v80);
    v98 = _objc_msgSend(v97, "view");
    v99 = (const char *)objc_retainAutoreleasedReturnValue(v98);
    v101 = (char *)v99;
    v146 = v97;
    if ( v99 )
    {
      objc_msgSend_stret(&v131, v99, v100);
      v102 = v132;
    }
    else
    {
      v132 = 0LL;
      v131 = 0LL;
      v102 = 0LL;
    }
    v139 = v102;
    v103 = objc_loadWeakRetained(v80);
    v104 = _objc_msgSend(v103, "view");
    v105 = (const char *)objc_retainAutoreleasedReturnValue(v104);
    v107 = (char *)v105;
    if ( v105 )
    {
      objc_msgSend_stret(&v133, v105, v106);
      v108 = *((double *)&v134 + 1);
    }
    else
    {
      v134 = 0LL;
      v133 = 0LL;
      v108 = 0.0;
    }
    v135 = v138;
    v136 = v139;
    v137 = v108 - v148;
    _objc_msgSend(v140, "setFrame:");
    v110 = objc_loadWeakRetained(location);
    v111 = _objc_msgSend(v110, "viewResizeCallBackBlock");
    v112 = objc_retainAutoreleasedReturnValue(v111);
    if ( v112 )
    {
      v114 = objc_loadWeakRetained(v113);
      v115 = _objc_msgSend(v114, "viewResizeCallBackBlock");
      v116 = (void (__fastcall **)(_QWORD, double))objc_retainAutoreleasedReturnValue(v115);
      v116[2](v116, v148);
    }
  }
}

//----- (00000001004F8CD2) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setTableVCToContainerWithTextID:](
        ZiXunModularContainerController *self,
        SEL a2,
        signed __int64 a3)
{
  NSNumber *v3; // rax
  NSNumber *v4; // r13
  NSNumber *v7; // rax
  NSNumber *v8; // rbx
  NSDictionary *v9; // rax
  NSArray *v24; // rax
  NSArray *v25; // r13
  NSArray *v26; // rax
  NSArray *v27; // rbx
  SEL v70; // r12
  NSArray *v104; // [rsp+118h] [rbp-58h] BYREF
  _QWORD v105[2]; // [rsp+120h] [rbp-50h] BYREF
  _QWORD v106[2]; // [rsp+130h] [rbp-40h] BYREF

  v105[0] = CFSTR("ZiXunType");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v106[0] = v4;
  v105[1] = CFSTR("RequestType");
  v6 = _objc_msgSend(v5, "newsRequestType");
  v7 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v6);
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v106[1] = v8;
  v9 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v106, v105, 2LL);
  v99 = objc_retainAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v10, "market");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v14 = _objc_msgSend(v13, "newsTableVC");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  _objc_msgSend(v15, "setMarket:", v12);
  v17 = _objc_msgSend(v16, "stockCode");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v101 = v19;
  v20 = _objc_msgSend(v19, "newsTableVC");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  _objc_msgSend(v21, "setStockCode:", v18);
  v23 = _objc_msgSend(v22, "newsTableVC");
  objc_retainAutoreleasedReturnValue(v23);
  v103 = v99;
  v24 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v103, 1LL);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  v104 = v25;
  v26 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v104, 1LL);
  v27 = objc_retainAutoreleasedReturnValue(v26);
  _objc_msgSend(v28, "requestForZiXun:", v27);
  v31 = _objc_msgSend(v30, "newsTableVC");
  v32 = objc_retainAutoreleasedReturnValue(v31);
  _objc_msgSend(v32, "hideSecondViewForGuoWaiZhiShu");
  v34 = _objc_msgSend(v33, "tableContainerView");
  v35 = (const char *)objc_retainAutoreleasedReturnValue(v34);
  v36 = (char *)v35;
  if ( v35 )
  {
    objc_msgSend_stret(&v86, v35, "frame");
    v37 = *(double *)&v87 + -4.0;
  }
  else
  {
    v87 = 0LL;
    v86 = 0LL;
    v37 = -4.0;
  }
  *(double *)&v102 = v37;
  v39 = _objc_msgSend(v38, "tableContainerView");
  v40 = (const char *)objc_retainAutoreleasedReturnValue(v39);
  v41 = v40;
  if ( v40 )
  {
    objc_msgSend_stret(&v88, v40, "frame");
    v42 = *((double *)&v89 + 1) + -2.0;
  }
  else
  {
    v89 = 0LL;
    v88 = 0LL;
    v42 = -2.0;
  }
  *((double *)&v102 + 1) = v42;
  v44 = _objc_msgSend(v43, "newsTableVC");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = _objc_msgSend(v45, "view");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v94 = xmmword_1010CED00;
  v95 = v102;
  _objc_msgSend(v47, "setFrame:");
  v49 = _objc_msgSend(v48, "myOpenUrlBlock");
  v50 = objc_retainAutoreleasedReturnValue(v49);
  if ( v50 )
  {
    v52 = _objc_msgSend(v51, "myOpenUrlBlock");
    v53 = objc_retainAutoreleasedReturnValue(v52);
    v55 = _objc_msgSend(v54, "newsTableVC");
    v56 = objc_retainAutoreleasedReturnValue(v55);
    _objc_msgSend(v56, "setZiXunOpenURLBlock:", v53);
  }
  v57 = _objc_msgSend(v51, "newsTableVC");
  *(_QWORD *)&v102 = objc_retainAutoreleasedReturnValue(v57);
  v58 = _objc_msgSend((id)v102, "view");
  *((_QWORD *)&v102 + 1) = objc_retainAutoreleasedReturnValue(v58);
  v60 = _objc_msgSend(v59, "tableContainerView");
  v61 = (const char *)objc_retainAutoreleasedReturnValue(v60);
  v62 = (char *)v61;
  if ( v61 )
  {
    objc_msgSend_stret(&v90, v61, "frame");
    v63 = v91;
  }
  else
  {
    v91 = 0LL;
    v90 = 0LL;
    v63 = 0LL;
  }
  v100 = v63;
  v64 = _objc_msgSend(v101, "tableContainerView");
  v65 = (const char *)objc_retainAutoreleasedReturnValue(v64);
  v66 = (char *)v65;
  if ( v65 )
  {
    objc_msgSend_stret(&v92, v65, "frame");
    v67 = *((_QWORD *)&v93 + 1);
  }
  else
  {
    v93 = 0LL;
    v92 = 0LL;
    v67 = 0LL;
  }
  v96 = 0LL;
  v97 = v100;
  v98 = v67;
  v68 = (void *)*((_QWORD *)&v102 + 1);
  _objc_msgSend(*((id *)&v102 + 1), "setFrame:");
  v69 = v101;
  v71 = _objc_msgSend(v101, v70);
  v72 = objc_retainAutoreleasedReturnValue(v71);
  v73 = _objc_msgSend(v72, "view");
  v74 = objc_retainAutoreleasedReturnValue(v73);
  v75(v74, "setAutoresizingMask:", 63LL);
  v77 = (void *)v76(v69, "tableContainerView");
  v78 = objc_retainAutoreleasedReturnValue(v77);
  v80 = (void *)v79(v69, "newsTableVC");
  v81 = objc_retainAutoreleasedReturnValue(v80);
  v83 = (void *)v82(v81, "view");
  v84 = objc_retainAutoreleasedReturnValue(v83);
  v85(v78, "addSubview:", v84);
}

//----- (00000001004F9331) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController openNewsWebWindowWithNewsUrl:](
        ZiXunModularContainerController *self,
        SEL a2,
        id a3)
{
  WebViewWindowController *v5; // rax
  WebViewWindowController *v6; // rax
  WebViewWindowController *v8; // rax
  WebViewWindowController *v9; // r13
  ZiXunWKWebViewController *v10; // rax
  ZiXunWKWebViewController *v11; // r15

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && !(unsigned __int8)_objc_msgSend(v3, "isEqualToString:", &charsToLeaveEscaped) )
  {
    v5 = -[ZiXunModularContainerController newsViewWC](self, "newsViewWC");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "showWindow:", 0LL);
    v8 = -[ZiXunModularContainerController newsViewWC](self, "newsViewWC");
    v9 = objc_retainAutoreleasedReturnValue(v8);
    v10 = -[WebViewWindowController webViewController](v9, "webViewController");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "loadUrl:", v4);
    v14 = (void *)v13(self, "newsViewWC");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = (void *)v16(v15, "webViewController");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "setNewWindowActionBlock:", &stru_1012E2128);
  }
}

//----- (00000001004F946D) ----------------------------------------------------
void __cdecl sub_1004F946D(id a1, NSURLRequest *a2)
{
  NSURLRequest *v2; // r15
  id (*v5)(id, SEL, ...); // r12

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSWorkspace, "sharedWorkspace");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v6 = v5(v2, "URL");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8(v4, "openURL:", v7);
}

//----- (00000001004F9501) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController disableScroll](ZiXunModularContainerController *self, SEL a2)
{
  ZiXunTableViewController *v2; // rax
  ZiXunTableViewController *v3; // r15
  NSScrollView *v4; // rax
  NSScrollView *v5; // rbx
  id (*v11)(id, SEL, ...); // r12

  v2 = -[ZiXunModularContainerController newsTableVC](self, "newsTableVC");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = -[ZiXunTableViewController firstScrollView](v3, "firstScrollView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(&OBJC_CLASS___HXBaseScrollView, "class");
  v7 = (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6);
  if ( v7 )
  {
    v9 = _objc_msgSend(v8, "newsTableVC");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v12 = v11(v10, "firstScrollView");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    v14(v13, "setDisEnableScroll:", 1LL);
  }
}

//----- (00000001004F95F9) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController refreshAllModules](ZiXunModularContainerController *self, SEL a2)
{
  NSNumber *v3; // rax
  NSNumber *v4; // r15
  NSNumber *v6; // rax
  NSNumber *v7; // rbx
  NSDictionary *v9; // rax
  NSString *v11; // rax
  ZiXunTableViewController *v12; // rax
  ZiXunTableViewController *v13; // rbx
  NSString *v16; // rax
  ZiXunTableViewController *v17; // rax
  ZiXunTableViewController *v18; // rbx
  ZiXunTableViewController *v21; // rax
  NSArray *v22; // rax
  NSArray *v23; // r13
  NSArray *v24; // rax
  NSArray *v25; // rbx
  NSDictionary *v28; // [rsp+8h] [rbp-68h]
  NSDictionary *v29; // [rsp+10h] [rbp-60h] BYREF
  NSArray *v30; // [rsp+18h] [rbp-58h] BYREF
  _QWORD v31[4]; // [rsp+20h] [rbp-50h] BYREF

  v31[0] = CFSTR("ZiXunType");
  v2 = -[ZiXunModularContainerController textID](self, "textID");
  v3 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithLong:", v2);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v31[2] = v4;
  v31[1] = CFSTR("RequestType");
  v5 = -[ZiXunModularContainerController newsRequestType](self, "newsRequestType");
  v6 = _objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithUnsignedInteger:", v5);
  v7 = objc_retainAutoreleasedReturnValue(v6);
  *(_QWORD *)(v8 + 8) = v7;
  v9 = _objc_msgSend(&OBJC_CLASS___NSDictionary, "dictionaryWithObjects:forKeys:count:", v8, v31, 2LL);
  v28 = objc_retainAutoreleasedReturnValue(v9);
  v10(v4);
  v11 = -[HXBaseViewController market](self, "market");
  objc_retainAutoreleasedReturnValue(v11);
  v12 = -[ZiXunModularContainerController newsTableVC](self, "newsTableVC");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  -[ZiXunTableViewController setMarket:](v13, "setMarket:", v14);
  v16 = -[HXBaseViewController stockCode](self, "stockCode");
  objc_retainAutoreleasedReturnValue(v16);
  v17 = -[ZiXunModularContainerController newsTableVC](self, "newsTableVC");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  -[ZiXunTableViewController setStockCode:](v18, "setStockCode:", v19);
  v21 = -[ZiXunModularContainerController newsTableVC](self, "newsTableVC");
  objc_retainAutoreleasedReturnValue(v21);
  v29 = v28;
  v22 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v29, 1LL);
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v30 = v23;
  v24 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", &v30, 1LL);
  v25 = objc_retainAutoreleasedReturnValue(v24);
  _objc_msgSend(v26, "requestForZiXun:", v25);
}

//----- (00000001004F983B) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunModularContainerController titleTextField](ZiXunModularContainerController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super.super._reserved);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001004F9854) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setTitleTextField:](
        ZiXunModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super.super._reserved, a3);
}

//----- (00000001004F9868) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunModularContainerController tableContainerView](
        ZiXunModularContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._shouldRefresh);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001004F9881) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setTableContainerView:](
        ZiXunModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_storeWeak((id *)&self->super.super._shouldRefresh, a3);
}

//----- (00000001004F9895) ----------------------------------------------------
HXDragToResizeView *__cdecl -[ZiXunModularContainerController resizeView](
        ZiXunModularContainerController *self,
        SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.super._stockCode);
  return (HXDragToResizeView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001004F98AE) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setResizeView:](ZiXunModularContainerController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.super._stockCode, a3);
}

//----- (00000001004F98C2) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setNewsTableVC:](ZiXunModularContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super.super._market, a3);
}

//----- (00000001004F98D6) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunModularContainerController textID](ZiXunModularContainerController *self, SEL a2)
{
  return (signed __int64)self->super.super._contentsObjMArr;
}

//----- (00000001004F98E7) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setTextID:](
        ZiXunModularContainerController *self,
        SEL a2,
        signed __int64 a3)
{
  self->super.super._contentsObjMArr = (NSMutableArray *)a3;
}

//----- (00000001004F98F8) ----------------------------------------------------
unsigned __int64 __cdecl -[ZiXunModularContainerController newsRequestType](
        ZiXunModularContainerController *self,
        SEL a2)
{
  return self->super.super._controllerID;
}

//----- (00000001004F9909) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setNewsRequestType:](
        ZiXunModularContainerController *self,
        SEL a2,
        unsigned __int64 a3)
{
  self->super.super._controllerID = a3;
}

//----- (00000001004F991A) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setMyOpenUrlBlock:](
        ZiXunModularContainerController *self,
        SEL a2,
        id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 160LL);
}

//----- (00000001004F992B) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController setNewsViewWC:](ZiXunModularContainerController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->super._noRecordFrame, a3);
}

//----- (00000001004F993F) ----------------------------------------------------
void __cdecl -[ZiXunModularContainerController .cxx_destruct](ZiXunModularContainerController *self, SEL a2)
{
  objc_storeStrong((id *)&self->super._noRecordFrame, 0LL);
  objc_storeStrong((id *)&self->super.super._paramsMDic, 0LL);
  objc_storeStrong((id *)&self->super.super._market, 0LL);
  objc_destroyWeak((id *)&self->super.super._stockCode);
  objc_destroyWeak((id *)&self->super.super._shouldRefresh);
  objc_destroyWeak((id *)&self->super.super.super._reserved);
}

//----- (00000001004F99AE) ----------------------------------------------------
void __fastcall sub_1004F99AE(__int64 a1)
{
  _QWORD block[6]; // [rsp+0h] [rbp-30h] BYREF

  if ( qword_1016D3000 != -1 )
    dispatch_once(&qword_1016D3000, &stru_1012E2148);
  if ( qword_1016D3008 != -1 )
    dispatch_once(&qword_1016D3008, &stru_1012E2168);
  block[0] = _NSConcreteStackBlock;
  block[1] = 3221225472LL;
  block[2] = sub_1004F9A98;
  block[3] = &unk_1012DEB90;
  block[4] = a1;
  dispatch_sync(qword_1016D2FF8, block);
}

//----- (00000001004F9A3C) ----------------------------------------------------
void __cdecl sub_1004F9A3C(id a1)
{

  v1 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D2FF0;
  qword_1016D2FF0 = v2;
}

//----- (00000001004F9A71) ----------------------------------------------------
void __cdecl sub_1004F9A71(id a1)
{
  dispatch_queue_s *v1; // rax
  dispatch_queue_t v2; // rdi

  v1 = dispatch_queue_create("HexinStock.contextQueue", 0LL);
  v2 = qword_1016D2FF8;
  qword_1016D2FF8 = v1;
}

//----- (00000001004F9A98) ----------------------------------------------------
void __fastcall sub_1004F9A98(__int64 a1)
{
  NSGraphicsContext *v9; // rax
  NSGraphicsContext *v10; // rbx

  v1 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "currentContext");
  v2 = objc_retainAutoreleasedReturnValue(v1);
  v3 = v2;
  v4 = qword_1016D2FF0;
  if ( v2 )
  {
    _objc_msgSend(qword_1016D2FF0, "addObject:", v2);
  }
  else
  {
    v5 = _objc_msgSend(&OBJC_CLASS___NSNull, "null");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v4, "addObject:", v6);
  }
  v8 = *(_QWORD *)(a1 + 32);
  if ( v8 )
  {
    v9 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "graphicsContextWithGraphicsPort:flipped:", v8, 0LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11(&OBJC_CLASS___NSGraphicsContext, "setCurrentContext:", v10);
  }
}

//----- (00000001004F9B86) ----------------------------------------------------
void sub_1004F9B86()
{
  if ( qword_1016D3000 != -1 )
    dispatch_once(&qword_1016D3000, &stru_1012E2188);
  if ( qword_1016D3008 != -1 )
    dispatch_once(&qword_1016D3008, &stru_1012E21A8);
  dispatch_sync(qword_1016D2FF8, &stru_1012E21C8);
}

//----- (00000001004F9BDC) ----------------------------------------------------
void __cdecl sub_1004F9BDC(id a1)
{

  v1 = objc_alloc(&OBJC_CLASS___NSMutableArray);
  v2 = _objc_msgSend(v1, "init");
  v3 = qword_1016D2FF0;
  qword_1016D2FF0 = v2;
}

//----- (00000001004F9C11) ----------------------------------------------------
void __cdecl sub_1004F9C11(id a1)
{
  dispatch_queue_s *v1; // rax
  dispatch_queue_t v2; // rdi

  v1 = dispatch_queue_create("HexinStock.contextQueue", 0LL);
  v2 = qword_1016D2FF8;
  qword_1016D2FF8 = v1;
}

//----- (00000001004F9C38) ----------------------------------------------------
void __cdecl sub_1004F9C38(id a1)
{

  if ( _objc_msgSend(qword_1016D2FF0, "count") )
  {
    v1 = _objc_msgSend(qword_1016D2FF0, "lastObject");
    v2 = objc_retainAutoreleasedReturnValue(v1);
    v3 = _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "class");
    v4 = (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3);
    v5 = 0LL;
    if ( v4 )
      v5 = v2;
    _objc_msgSend(&OBJC_CLASS___NSGraphicsContext, "setCurrentContext:", v5);
    _objc_msgSend(qword_1016D2FF0, "removeLastObject");
  }
}

