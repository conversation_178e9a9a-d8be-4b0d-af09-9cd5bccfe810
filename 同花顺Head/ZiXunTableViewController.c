void __cdecl -[ZiXunTableViewController viewDidLoad](ZiXunTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunTableViewController;
  objc_msgSendSuper2(&v2, "viewDidLoad");
  -[ZiXunTableViewController setDefaultProperties](self, "setDefaultProperties");
  -[ZiXunTableViewController setTableAction](self, "setTableAction");
  -[ZiXunTableViewController registerNotificationObserver](self, "registerNotificationObserver");
  -[ZiXunTableViewController setUpTheme](self, "setUpTheme");
  -[ZiXunTableViewController setIsNeedNewZiXunWindow:](self, "setIsNeedNewZiXunWindow:", 1LL);
}

//----- (00000001001AF87A) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController dealloc](ZiXunTableViewController *self, SEL a2)
{

  -[ZiXunTableViewController invalidateNotificationObserver](self, "invalidateNotificationObserver");
  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunTableViewController;
  objc_msgSendSuper2(&v2, "dealloc");
}

//----- (00000001001AF8B8) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController viewDidAppear](ZiXunTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunTableViewController;
  objc_msgSendSuper2(&v2, "viewDidAppear");
  -[ZiXunTableViewController viewDidAppearHandle](self, "viewDidAppearHandle");
}

//----- (00000001001AF8F9) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController viewDidDisappear](ZiXunTableViewController *self, SEL a2)
{

  v2.receiver = self;
  v2.super_class = (Class)&OBJC_CLASS___ZiXunTableViewController;
  objc_msgSendSuper2(&v2, "viewDidDisappear");
  -[ZiXunTableViewController viewDidDisappearHandle](self, "viewDidDisappearHandle");
}

//----- (00000001001AF93A) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController viewDidAppearHandle](ZiXunTableViewController *self, SEL a2)
{
  -[ZiXunTableViewController fireRequestTimer](self, "fireRequestTimer");
}

//----- (00000001001AF94C) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController viewDidDisappearHandle](ZiXunTableViewController *self, SEL a2)
{
  -[ZiXunTableViewController invalidateRequestTimer](self, "invalidateRequestTimer");
}

//----- (00000001001AF95E) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController fireRequestTimer](ZiXunTableViewController *self, SEL a2)
{
  NSTimer *v3; // rax
  NSTimer *v4; // rbx
  HXButton *v6; // rax
  HXButton *majorEventMoreBtn; // rdi

  -[ZiXunTableViewController invalidateRequestTimer](self, "invalidateRequestTimer");
  v3 = -[ZiXunTableViewController requestTimer](self, "requestTimer");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  if ( !v4 )
  {
    v5 = _objc_msgSend(
           &OBJC_CLASS___NSTimer,
           "scheduledTimerWithTimeInterval:target:selector:userInfo:repeats:",
           self,
           "timerToRequestZiXun",
           0LL,
           1LL,
           40.0);
    v6 = (HXButton *)objc_retainAutoreleasedReturnValue(v5);
    majorEventMoreBtn = self->_majorEventMoreBtn;
    self->_majorEventMoreBtn = v6;
  }
}

//----- (00000001001AF9F2) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController invalidateRequestTimer](ZiXunTableViewController *self, SEL a2)
{
  NSTimer *v2; // rax
  NSTimer *v3; // rax
  NSTimer *v4; // rbx
  NSTimer *v7; // rax
  NSTimer *v8; // rbx

  v2 = -[ZiXunTableViewController requestTimer](self, "requestTimer");
  if ( objc_retainAutoreleasedReturnValue(v2) )
  {
    v3 = -[ZiXunTableViewController requestTimer](self, "requestTimer");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    v5 = (unsigned __int8)_objc_msgSend(v4, "isValid");
    if ( v5 )
    {
      v7 = -[ZiXunTableViewController requestTimer](self, "requestTimer");
      v8 = objc_retainAutoreleasedReturnValue(v7);
      _objc_msgSend(v8, "invalidate");
      -[ZiXunTableViewController setRequestTimer:](self, "setRequestTimer:", 0LL);
    }
  }
  else
  {
  }
}

//----- (00000001001AFAE4) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setDefaultProperties](ZiXunTableViewController *self, SEL a2)
{
  NSScrollView *v2; // rax
  NSScrollView *v3; // rbx
  NSScrollView *v4; // rax
  NSScrollView *v5; // rbx
  HXBaseView *v6; // rax
  HXBaseView *v7; // rbx
  HXBaseView *v8; // rax
  HXBaseView *v9; // rbx
  HXBaseView *v12; // rax
  HXBaseView *v13; // rbx
  NSTextField *v16; // rax
  NSTextField *v17; // rbx
  SEL v18; // r12
  HXButton *v21; // rax
  HXButton *v22; // rax
  HXButton *v25; // rax
  HXButton *v26; // rbx
  HXButton *v29; // rax
  HXButton *v30; // rbx
  HXBaseView *v32; // rax
  HXBaseView *v33; // rbx
  NSTextField *v37; // rax
  NSTextField *v38; // rbx
  HXButton *v43; // rax
  HXButton *v44; // r15
  SEL v47; // r12
  SEL v50; // r12
  NSTextField *v55; // rax
  NSTextField *v56; // rbx
  NSScrollView *v57; // rax
  NSScrollView *v58; // rbx

  v2 = -[ZiXunTableViewController firstScrollView](self, "firstScrollView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setHasVerticalScroller:", 0LL);
  v4 = -[ZiXunTableViewController secondScrollView](self, "secondScrollView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, "setHasVerticalScroller:", 0LL);
  v6 = -[ZiXunTableViewController firstView](self, "firstView");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  -[HXBaseView setRightBorder:](v7, "setRightBorder:", 1LL);
  v8 = -[ZiXunTableViewController firstView](self, "firstView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  -[HXBaseView setBorderWidth:](v9, "setBorderWidth:", 0.2000000029802322);
  -[ZiXunTableViewController setIsSecondViewHidden:](self, "setIsSecondViewHidden:", 0LL);
  -[ZiXunTableViewController removeMajorEventRelativeView](self, "removeMajorEventRelativeView");
  v10 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = -[ZiXunTableViewController secondTableTitleView](self, "secondTableTitleView");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  -[HXBaseView setBackgroundColor:](v13, "setBackgroundColor:", v11);
  v14 = +[HXThemeManager blueLineColor](&OBJC_CLASS___HXThemeManager, "blueLineColor");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  v16 = -[ZiXunTableViewController secondTableTitleTF](self, "secondTableTitleTF");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  _objc_msgSend(v17, "setTextColor:", v15);
  v19 = _objc_msgSend(&OBJC_CLASS___HXThemeManager, v18);
  v20 = objc_retainAutoreleasedReturnValue(v19);
  v21 = -[ZiXunTableViewController secondTableMoreBtn](self, "secondTableMoreBtn");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  -[HXButton setBackgroundColor:](v22, "setBackgroundColor:", v20);
  v24 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  objc_retainAutoreleasedReturnValue(v24);
  v25 = -[ZiXunTableViewController secondTableMoreBtn](self, "secondTableMoreBtn");
  v26 = objc_retainAutoreleasedReturnValue(v25);
  -[HXButton setTextColorDefault:](v26, "setTextColorDefault:", v27);
  v29 = -[ZiXunTableViewController secondTableMoreBtn](self, "secondTableMoreBtn");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  -[HXButton setCanBeSelected:](v30, "setCanBeSelected:", 1LL);
  v31 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  objc_retainAutoreleasedReturnValue(v31);
  v32 = -[ZiXunTableViewController majorEventTitleView](self, "majorEventTitleView");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  -[HXBaseView setBackgroundColor:](v33, "setBackgroundColor:", v34);
  v36 = +[HXThemeManager blueLineColor](&OBJC_CLASS___HXThemeManager, "blueLineColor");
  objc_retainAutoreleasedReturnValue(v36);
  v37 = -[ZiXunTableViewController majorEventTitleTF](self, "majorEventTitleTF");
  v38 = objc_retainAutoreleasedReturnValue(v37);
  _objc_msgSend(v38, "setTextColor:", v39);
  v41 = +[HXThemeManager indexNavigationBarBgColor](&OBJC_CLASS___HXThemeManager, "indexNavigationBarBgColor");
  v42 = objc_retainAutoreleasedReturnValue(v41);
  v43 = -[ZiXunTableViewController majorEventMoreBtn](self, "majorEventMoreBtn");
  v44 = objc_retainAutoreleasedReturnValue(v43);
  -[HXButton setBackgroundColor:](v44, "setBackgroundColor:", v42);
  v45 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v46 = objc_retainAutoreleasedReturnValue(v45);
  v48 = _objc_msgSend(self, v47);
  v49 = objc_retainAutoreleasedReturnValue(v48);
  _objc_msgSend(v49, "setTextColorDefault:", v46);
  v51 = _objc_msgSend(self, v50);
  v52 = objc_retainAutoreleasedReturnValue(v51);
  _objc_msgSend(v52, "setCanBeSelected:", 1LL);
  v53 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v54 = objc_retainAutoreleasedReturnValue(v53);
  v55 = -[ZiXunTableViewController majorEventNoDataTF](self, "majorEventNoDataTF");
  v56 = objc_retainAutoreleasedReturnValue(v55);
  _objc_msgSend(v56, "setTextColor:", v54);
  v57 = -[ZiXunTableViewController majorEventScrollView](self, "majorEventScrollView");
  v58 = objc_retainAutoreleasedReturnValue(v57);
  _objc_msgSend(v58, "setHasVerticalScroller:", 0LL);
}

//----- (00000001001AFF7D) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setTableAction](ZiXunTableViewController *self, SEL a2)
{
  NSTableView *v2; // rax
  NSTableView *v3; // rbx

  v2 = -[ZiXunTableViewController firstTable](self, "firstTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "setAction:", "actionForTableClicked:");
  v5 = _objc_msgSend(v4, "secondTable");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "setAction:", "actionForTableClicked:");
  v8 = _objc_msgSend(v7, "majorEventTable");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "setAction:", "actionForTableClicked:");
}

//----- (00000001001B0038) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController requestForZiXun:](ZiXunTableViewController *self, SEL a2, id a3)
{
  HXBaseView *v14; // rax
  HXBaseView *v15; // r14

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    -[ZiXunTableViewController setReqParamArr:](self, "setReqParamArr:", v4);
    -[ZiXunTableViewController clearCacheData](self, "clearCacheData");
    -[ZiXunTableViewController setMajorEventViewState](self, "setMajorEventViewState");
    -[ZiXunTableViewController relayoutForMajorEventViewIfNeeded](self, "relayoutForMajorEventViewIfNeeded");
    -[ZiXunTableViewController requestMajorEventDataIfNeeded](self, "requestMajorEventDataIfNeeded");
    -[ZiXunTableViewController fireRequestTimer](self, "fireRequestTimer");
    v5 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "requestZiXunTree");
    v7 = _objc_msgSend(v4, "objectAtIndexedSubscript:", 0LL);
    v8 = objc_retainAutoreleasedReturnValue(v7);
    -[ZiXunTableViewController requestZiXunForFirstTable:](self, "requestZiXunForFirstTable:", v8);
    v9(v8);
    if ( (unsigned __int64)_objc_msgSend(v4, "count") >= 2 )
    {
      v11 = _objc_msgSend(v4, "objectAtIndexedSubscript:", 1LL);
      v12 = objc_retainAutoreleasedReturnValue(v11);
      -[ZiXunTableViewController requestZiXunForSecondTable:](self, "requestZiXunForSecondTable:", v12);
    }
    v13 = _objc_msgSend(v4, v10);
    v14 = -[ZiXunTableViewController secondView](self, "secondView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    if ( v13 == (id)1 )
      _objc_msgSend(v15, "setHidden:", 1LL);
    else
      _objc_msgSend(v15, "setHidden:", 0LL);
  }
}

//----- (00000001001B01E4) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController hideSecondView](ZiXunTableViewController *self, SEL a2)
{
  id (*v3)(id, SEL, ...); // r12
  id WeakRetained; // rbx
  __m128 v10; // xmm0
  __m128 v13; // [rsp+30h] [rbp-A0h]
  __m128 v15; // [rsp+50h] [rbp-80h]
  __m128 v20; // [rsp+90h] [rbp-40h]

  -[ZiXunTableViewController setIsSpecialViewStateSetted:](self, "setIsSpecialViewStateSetted:", 1LL);
  v2(self, "setIsSecondViewHidden:", 1LL);
  v4 = v3(self, "view");
  v5 = (const char *)objc_retainAutoreleasedReturnValue(v4);
  v6 = (char *)v5;
  if ( v5 )
  {
    objc_msgSend_stret(&v12, v5, "bounds");
    v20 = v13;
  }
  else
  {
    v13 = 0LL;
    v20 = 0LL;
    v12 = 0LL;
  }
  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  v14 = 0LL;
  v15 = v20;
  v8(WeakRetained, "setFrame:");
  v19 = 0.5 * *(double *)v20.i64;
  v9 = objc_loadWeakRetained((id *)&self->super._nibBundle);
  v10 = v20;
  _mm_storel_ps((double *)&v16, v20);
  *((_QWORD *)&v16 + 1) = 0LL;
  v17 = v19;
  _mm_storeh_ps(&v18, v10);
  v11(v9, "setFrame:");
}

//----- (00000001001B034B) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController showSecondView](ZiXunTableViewController *self, SEL a2)
{
  id *v9; // r13
  id WeakRetained; // rbx
  id *v17; // rbx
  id *location; // [rsp+C8h] [rbp-48h]

  -[ZiXunTableViewController setIsSpecialViewStateSetted:](self, "setIsSpecialViewStateSetted:", 0LL);
  if ( (unsigned __int8)_objc_msgSend(v2, "isSecondViewHidden") )
  {
    _objc_msgSend(v3, "setIsSecondViewHidden:", 0LL);
    v5 = _objc_msgSend(v4, "view");
    v6 = (const char *)objc_retainAutoreleasedReturnValue(v5);
    v7 = (char *)v6;
    if ( v6 )
    {
      objc_msgSend_stret(&v22, v6, "bounds");
      *(_QWORD *)&v37 = *((_QWORD *)&v23 + 1);
      *((double *)&v37 + 1) = *(double *)&v23 * 0.5;
    }
    else
    {
      v23 = 0LL;
      v22 = 0LL;
      v37 = 0uLL;
    }
    v9 = (id *)(v8 + 16);
    WeakRetained = objc_loadWeakRetained((id *)(v8 + 16));
    v24 = 0LL;
    v25 = *((_QWORD *)&v37 + 1);
    v26 = v37;
    _objc_msgSend(WeakRetained, "setFrame:");
    location = (id *)(v11 + 24);
    v12 = objc_loadWeakRetained((id *)(v11 + 24));
    v32 = *((unsigned __int64 *)&v37 + 1);
    v33 = *((_QWORD *)&v37 + 1);
    v34 = v37;
    v13 = *((double *)&v37 + 1);
    _objc_msgSend(v12, "setFrame:");
    *(_QWORD *)&v37 = objc_loadWeakRetained(v9);
    _objc_msgSend((id)v37, "width");
    *((double *)&v37 + 1) = v13 + -1.0;
    v14 = objc_loadWeakRetained(v9);
    v36 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v14, "height");
    v16 = objc_loadWeakRetained((id *)(v15 + 32));
    v27 = 0LL;
    v28 = *((_QWORD *)&v37 + 1);
    v29 = v36;
    _objc_msgSend(v16, "setFrame:");
    v17 = location;
    v18 = objc_loadWeakRetained(location);
    _objc_msgSend(v18, "width");
    *(_QWORD *)&v37 = 0LL;
    v19 = objc_loadWeakRetained(v17);
    _objc_msgSend(v19, "height");
    *((_QWORD *)&v37 + 1) = 0LL;
    v21 = objc_loadWeakRetained((id *)(v20 + 40));
    v30 = 0LL;
    v31 = v37;
    _objc_msgSend(v21, "setFrame:");
  }
}

//----- (00000001001B0657) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController hideSecondViewForGuoWaiZhiShu](ZiXunTableViewController *self, SEL a2)
{
  const char *WeakRetained; // rax
  __m128 *v4; // r12

  -[ZiXunTableViewController setIsSpecialViewStateSetted:](self, "setIsSpecialViewStateSetted:", 1LL);
  -[ZiXunTableViewController setIsSecondViewHidden:](self, "setIsSecondViewHidden:", 1LL);
  WeakRetained = (const char *)objc_loadWeakRetained((id *)&self->super._nibName);
  v3 = (char *)WeakRetained;
  if ( WeakRetained )
  {
    objc_msgSend_stret(v14, WeakRetained, "frame");
    v21 = (__int128)*v4;
  }
  else
  {
    v21 = 0LL;
    memset(v14, 0, sizeof(v14));
  }
  v5 = _objc_msgSend(self, "view");
  v6 = (const char *)objc_retainAutoreleasedReturnValue(v5);
  v7 = (char *)v6;
  if ( v6 )
  {
    objc_msgSend_stret(v15, v6, "bounds");
    v23 = *(double *)(v8 + 16);
    v22 = *(double *)(v8 + 24);
  }
  else
  {
    memset(v15, 0, 32);
    v22 = 0.0;
    v23 = 0.0;
  }
  *(double *)&v20 = v23 + -4.0;
  v9 = objc_loadWeakRetained((id *)&self->super._nibName);
  v15[2] = v21;
  v16 = v20;
  v17 = v22;
  _objc_msgSend(v9, "setFrame:");
  v10(v9);
  *((_QWORD *)&v11 + 1) = *((_QWORD *)&v21 + 1);
  *(double *)&v11 = *(double *)&v21 + v23;
  v20 = v11;
  v23 = v23 * 0.5;
  v12 = objc_loadWeakRetained((id *)&self->super._nibBundle);
  v18 = v20;
  _mm_storeh_ps(v19, (__m128)v21);
  v19[1] = v23;
  v19[2] = v22;
  _objc_msgSend(v12, "setFrame:");
  v13(v12);
}

//----- (00000001001B086E) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController timerToRequestZiXun](ZiXunTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  NSArray *v7; // rax
  NSArray *v8; // rbx
  NSArray *v13; // rax
  NSArray *v14; // rbx
  NSArray *v17; // rax
  NSArray *v18; // r15

  v2 = -[ZiXunTableViewController reqParamArr](self, "reqParamArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "count");
  if ( v4 )
  {
    v5 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "requestZiXunTree");
    v7 = -[ZiXunTableViewController reqParamArr](self, "reqParamArr");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9 = _objc_msgSend(v8, "objectAtIndexedSubscript:", 0LL);
    v10 = objc_retainAutoreleasedReturnValue(v9);
    -[ZiXunTableViewController requestZiXunForFirstTable:](self, "requestZiXunForFirstTable:", v10);
    v12(v8);
    v13 = -[ZiXunTableViewController reqParamArr](self, "reqParamArr");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15 = _objc_msgSend(v14, "count");
    v16(v14);
    if ( (unsigned __int64)v15 >= 2 )
    {
      v17 = -[ZiXunTableViewController reqParamArr](self, "reqParamArr");
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(v18, "objectAtIndexedSubscript:", 1LL);
      v20 = objc_retainAutoreleasedReturnValue(v19);
      -[ZiXunTableViewController requestZiXunForSecondTable:](self, "requestZiXunForSecondTable:", v20);
      v21(v20);
      v22(v18);
    }
  }
  -[ZiXunTableViewController requestMajorEventDataIfNeeded](self, "requestMajorEventDataIfNeeded");
}

//----- (00000001001B09E6) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController requestMajorEventDataIfNeeded](ZiXunTableViewController *self, SEL a2)
{
  MajorEventRequestModule *v2; // rax
  MajorEventRequestModule *v3; // r14
  NSString *v4; // rax
  _QWORD v7[5]; // [rsp+8h] [rbp-58h] BYREF
  id to; // [rsp+30h] [rbp-30h] BYREF
  id location[5]; // [rsp+38h] [rbp-28h] BYREF

  if ( (unsigned __int8)-[ZiXunTableViewController isMajorEventShow](self, "isMajorEventShow") )
  {
    objc_initWeak(location, self);
    v2 = -[ZiXunTableViewController majorEvnetReqModule](self, "majorEvnetReqModule");
    v3 = objc_retainAutoreleasedReturnValue(v2);
    v4 = -[ZiXunTableViewController stockCode](self, "stockCode");
    objc_retainAutoreleasedReturnValue(v4);
    v7[0] = _NSConcreteStackBlock;
    v7[1] = 3254779904LL;
    v7[2] = sub_1001B0AF4;
    v7[3] = &unk_1012DDFE8;
    objc_copyWeak(&to, location);
    v7[4] = self;
    -[MajorEventRequestModule requestForMajorEvent:reqCallback:](v3, "requestForMajorEvent:reqCallback:", v5, v7);
    objc_destroyWeak(&to);
    objc_destroyWeak(location);
  }
}

//----- (00000001001B0AF4) ----------------------------------------------------
void __fastcall sub_1001B0AF4(__int64 a1, void *a2)
{
  id *v8; // r12
  id *v10; // r12
  id *v15; // r12
  id WeakRetained; // [rsp+8h] [rbp-38h]

  v2 = objc_retain(a2);
  v3 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v2, "isKindOfClass:", v3) && _objc_msgSend(v2, "count") )
  {
    WeakRetained = objc_loadWeakRetained((id *)(v4 + 40));
    v5 = _objc_msgSend(WeakRetained, "majorEvnetReqModule");
    v21 = objc_retainAutoreleasedReturnValue(v5);
    v6 = _objc_msgSend(v21, "filterlingDataArrayForMiniView:", v2);
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v19 = v2;
    v9 = objc_loadWeakRetained(v8);
    _objc_msgSend(v9, "setMajorEventArr:", v7);
    v11 = objc_loadWeakRetained(v10);
    v12 = _objc_msgSend(v11, "majorEventTable");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "reloadData");
    v14 = v11;
    v2 = v19;
    v16 = objc_loadWeakRetained(v15);
    _objc_msgSend(v16, "relayoutForMajorEventViewIfNeeded");
  }
  else
  {
    _objc_msgSend(*(id *)(v4 + 32), "clearCacheDataForMajorEventTable");
    v18 = _objc_msgSend(*(id *)(v17 + 32), "majorEventTable");
    v16 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v16, "reloadData");
  }
}

//----- (00000001001B0C93) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController receiveZiXunData:tableFlag:](
        ZiXunTableViewController *self,
        SEL a2,
        id a3,
        unsigned __int64 a4)
{

  v5 = objc_retain(a3);
  v6 = _objc_msgSend(&OBJC_CLASS___NSArray, "class");
  if ( (unsigned __int8)_objc_msgSend(v5, "isKindOfClass:", v6) )
  {
    v7 = objc_retain(v5);
    if ( a4 == 1 )
    {
      v34 = v7;
      v21 = _objc_msgSend(v8, "filterInvalidZiXunData:", v7);
      v22 = objc_retainAutoreleasedReturnValue(v21);
      _objc_msgSend(v23, "setSecondTableZiXunDatas:", v22);
      v25 = _objc_msgSend(v24, "secondTableZiXunDatas");
      v36 = objc_retainAutoreleasedReturnValue(v25);
      v27 = _objc_msgSend(v26, "sortByTime:", v36);
      v28 = objc_retainAutoreleasedReturnValue(v27);
      _objc_msgSend(v29, "setSecondTableZiXunDatas:", v28);
      v31 = _objc_msgSend(v30, "secondTable");
      v32 = objc_retainAutoreleasedReturnValue(v31);
      _objc_msgSend(v32, "reloadData");
      _objc_msgSend(v33, "relayoutForMajorEventViewIfNeeded");
    }
    else
    {
      if ( a4 )
      {
LABEL_7:
        goto LABEL_8;
      }
      v34 = v7;
      v9 = _objc_msgSend(v8, "filterInvalidZiXunData:", v7);
      v10 = objc_retainAutoreleasedReturnValue(v9);
      _objc_msgSend(v11, "setFirstTableZiXunDatas:", v10);
      v13 = _objc_msgSend(v12, "firstTableZiXunDatas");
      v35 = objc_retainAutoreleasedReturnValue(v13);
      v15 = _objc_msgSend(v14, "sortByTime:", v35);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      _objc_msgSend(v17, "setFirstTableZiXunDatas:", v16);
      v19 = _objc_msgSend(v18, "firstTable");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      _objc_msgSend(v20, "reloadData");
    }
    v7 = v34;
    goto LABEL_7;
  }
LABEL_8:
}

//----- (00000001001B0EB0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController failToReceiveZiXunData:](
        ZiXunTableViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  NSTableView *v3; // rax
  NSTableView *v4; // rbx
  NSTableView *v5; // rax
  NSTableView *v6; // r14

  if ( a3 == 1 )
  {
    -[ZiXunTableViewController clearCacheDataForSecondTable](self, "clearCacheDataForSecondTable");
    v5 = -[ZiXunTableViewController secondTable](self, "secondTable");
    v6 = objc_retainAutoreleasedReturnValue(v5);
    _objc_msgSend(v6, "reloadData");
    -[ZiXunTableViewController relayoutForMajorEventViewIfNeeded](self, "relayoutForMajorEventViewIfNeeded");
  }
  else if ( !a3 )
  {
    -[ZiXunTableViewController clearCacheDataForFirstTable](self, "clearCacheDataForFirstTable");
    v3 = -[ZiXunTableViewController firstTable](self, "firstTable");
    v4 = objc_retainAutoreleasedReturnValue(v3);
    _objc_msgSend(v4, "reloadData");
  }
}

//----- (00000001001B0F7E) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController actionForTableClicked:](ZiXunTableViewController *self, SEL a2, id a3)
{
  NSScrollView *v6; // rax
  NSScrollView *v7; // r15
  NSScrollView *v11; // rax
  NSScrollView *v12; // rbx

  objc_retain(a3);
  v3 = _objc_msgSend(&OBJC_CLASS___NSTableView, "class");
  if ( (unsigned __int8)_objc_msgSend(v4, "isKindOfClass:", v3) )
  {
    v6 = -[ZiXunTableViewController firstScrollView](self, "firstScrollView");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v8 = _objc_msgSend(&OBJC_CLASS___HXBaseScrollView, "class");
    v9 = (unsigned __int8)_objc_msgSend(v7, "isKindOfClass:", v8);
    if ( v9 )
    {
      v11 = -[ZiXunTableViewController firstScrollView](self, "firstScrollView");
      v12 = objc_retainAutoreleasedReturnValue(v11);
      _objc_msgSend(v12, "setDisEnableScroll:", 0LL);
    }
    -[ZiXunTableViewController postNotificationForChangingTableFocus:](
      self,
      "postNotificationForChangingTableFocus:",
      v10);
    -[ZiXunTableViewController openZiXunDetailWindow:](self, "openZiXunDetailWindow:", v13);
  }
}

//----- (00000001001B1078) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController gongGaoMoreBtnClicked:](ZiXunTableViewController *self, SEL a2, id a3)
{
  ZiXunWindowController *v7; // rax
  __CFString *v8; // r15
  NSString *v9; // rax
  NSString *v10; // r13
  NSString *v11; // rax
  NSString *v12; // rbx

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "class");
  v6 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v5);
  if ( v6 )
  {
    v7 = +[ZiXunWindowController sharedInstance](&OBJC_CLASS___ZiXunWindowController, "sharedInstance");
    objc_retainAutoreleasedReturnValue(v7);
    v8 = off_1012E7888;
    v9 = -[ZiXunTableViewController stockCode](self, "stockCode");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = -[ZiXunTableViewController market](self, "market");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v13, "showWindowWithMenuTitle:stockCode:market:", v8, v10, v12);
  }
}

//----- (00000001001B117C) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController majorEventMoreBtnClicked:](ZiXunTableViewController *self, SEL a2, id a3)
{
  ZiXunWindowController *v7; // rax
  __CFString *v8; // r15
  NSString *v9; // rax
  NSString *v10; // r13
  NSString *v11; // rax
  NSString *v12; // rbx

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "class");
  v6 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v5);
  if ( v6 )
  {
    v7 = +[ZiXunWindowController sharedInstance](&OBJC_CLASS___ZiXunWindowController, "sharedInstance");
    objc_retainAutoreleasedReturnValue(v7);
    v8 = off_1012E7880[0];
    v9 = -[ZiXunTableViewController stockCode](self, "stockCode");
    v10 = objc_retainAutoreleasedReturnValue(v9);
    v11 = -[ZiXunTableViewController market](self, "market");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    _objc_msgSend(v13, "showWindowWithMenuTitle:stockCode:market:", v8, v10, v12);
    -[ZiXunTableViewController sendMajorEventMaiDian](self, "sendMajorEventMaiDian");
  }
}

//----- (00000001001B129D) ----------------------------------------------------
signed __int64 __cdecl -[ZiXunTableViewController numberOfRowsInTableView:](
        ZiXunTableViewController *self,
        SEL a2,
        id a3)
{
  id WeakRetained; // rbx
  SEL *v7; // rax
  signed __int64 v10; // r12

  v3 = objc_retain(a3);
  WeakRetained = objc_loadWeakRetained((id *)&self->super.view);
  if ( WeakRetained == v3 )
  {
    v12 = &selRef_setNoDataTFStateForFirstTable;
    v7 = &selRef_firstTableZiXunDatas;
  }
  else
  {
    v5 = objc_loadWeakRetained((id *)&self->super._topLevelObjects);
    if ( v5 == v3 )
    {
      v12 = &selRef_setNoDataTFStateForSecondTable;
      v7 = &selRef_secondTableZiXunDatas;
    }
    else
    {
      v6 = objc_loadWeakRetained((id *)&self->_firstScrollView);
      if ( v6 != v3 )
        goto LABEL_9;
      v12 = &selRef_setNoDataTFStateForMajroEventTable;
      v7 = &selRef_majorEventArr;
    }
  }
  v8 = _objc_msgSend(self, *v7);
  v9 = objc_retainAutoreleasedReturnValue(v8);
  _objc_msgSend(v9, "count");
  _objc_msgSend(self, *v12);
LABEL_9:
  return v10;
}

//----- (00000001001B13B5) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController tableView:objectValueForTableColumn:row:](
        ZiXunTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        signed __int64 a5)
{
  return 0LL;
}

//----- (00000001001B13BD) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController tableView:willDisplayCell:forTableColumn:row:](
        ZiXunTableViewController *self,
        SEL a2,
        id a3,
        id a4,
        id a5,
        signed __int64 a6)
{
  ZiXunTableViewController *v8; // r13
  NSTableView *v11; // rax
  NSTableView *v12; // rbx
  NSTableView *v13; // r12
  NSTableView *v14; // rax
  NSTableView *v15; // rbx
  NSTableView *v16; // r12
  NSTableView *v17; // rax
  NSTableView *v18; // rbx
  NSTableView *v19; // r12
  NSArray *v22; // rax
  NSArray *v23; // r15
  NSArray *v24; // rax
  NSArray *v25; // rax
  NSArray *v30; // rax
  NSArray *v31; // rax
  NSArray *v32; // rax
  NSArray *v33; // r12
  NSArray *v34; // rax
  NSArray *v35; // r14
  NSArray *v38; // r14
  NSArray *v42; // rax
  NSArray *v43; // rax
  NSArray *v44; // rax
  NSArray *v45; // r14
  NSArray *v48; // rax
  NSArray *v51; // rdi
  NSString *v70; // rax
  NSString *v71; // rax
  NSTableView *v76; // [rsp+0h] [rbp-50h]
  unsigned int v77; // [rsp+8h] [rbp-48h]

  v8 = self;
  objc_retain(a3);
  v78 = objc_retain(a4);
  v9 = _objc_msgSend(a5, "identifier");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v77 = (unsigned int)_objc_msgSend(v10, "intValue");
  v11 = -[ZiXunTableViewController firstTable](self, "firstTable");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v76 = v13;
  if ( v12 == v13 )
  {
    v22 = -[ZiXunTableViewController firstTableZiXunDatas](self, "firstTableZiXunDatas");
    v23 = objc_retainAutoreleasedReturnValue(v22);
    if ( !_objc_msgSend(v23, "count") )
    {
      v51 = v23;
      goto LABEL_24;
    }
    v24 = (NSArray *)-[ZiXunTableViewController firstTableZiXunDatas](self, "firstTableZiXunDatas");
    v25 = objc_retainAutoreleasedReturnValue(v24);
    v26 = (char *)_objc_msgSend(v25, "count") - 1;
    v29 = (void (__cdecl **)(id))v28;
    v28(v23);
    if ( (unsigned __int64)v26 < a6 )
      goto LABEL_45;
    v8 = self;
    v30 = (NSArray *)-[ZiXunTableViewController firstTableZiXunDatas](self, "firstTableZiXunDatas");
    goto LABEL_11;
  }
  v14 = -[ZiXunTableViewController secondTable](self, "secondTable");
  v15 = objc_retainAutoreleasedReturnValue(v14);
  if ( v15 == v16 )
  {
    v31 = -[ZiXunTableViewController secondTableZiXunDatas](self, "secondTableZiXunDatas");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    if ( !_objc_msgSend(v32, "count") )
    {
LABEL_22:
      v51 = v33;
LABEL_24:
      return;
    }
    v34 = -[ZiXunTableViewController secondTableZiXunDatas](self, "secondTableZiXunDatas");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v36 = (char *)_objc_msgSend(v35, "count") - 1;
    if ( (unsigned __int64)v36 < a6 )
      goto LABEL_45;
    v8 = self;
    v30 = (NSArray *)-[ZiXunTableViewController secondTableZiXunDatas](self, "secondTableZiXunDatas");
LABEL_11:
    v38 = objc_retainAutoreleasedReturnValue(v30);
    v40 = _objc_msgSend(v38, "objectAtIndexedSubscript:", v39);
    v80 = objc_retainAutoreleasedReturnValue(v40);
    v21 = 1;
LABEL_12:
LABEL_13:
    if ( v77 == 2LL )
    {
      if ( v21 )
      {
        v52 = _objc_msgSend(v80, "time");
        v41 = (char *)objc_retainAutoreleasedReturnValue(v52);
        v53 = +[HXTools getDateFromDoubleNumber:](&OBJC_CLASS___HXTools, "getDateFromDoubleNumber:", v41);
        v54 = objc_retainAutoreleasedReturnValue(v53);
        _objc_msgSend(v78, "setStringValue:", v54);
        goto LABEL_36;
      }
      v64 = "monthDayStr";
      v65 = _objc_msgSend(v20, "monthDayStr");
    }
    else
    {
      if ( v77 != 1LL )
      {
        if ( v77 )
        {
LABEL_44:
          v73 = +[HXThemeManager normalTextColor](&OBJC_CLASS___HXThemeManager, "normalTextColor");
          v74 = objc_retainAutoreleasedReturnValue(v73);
          _objc_msgSend(v78, "setTextColor:", v74);
          goto LABEL_45;
        }
        if ( !v21 )
        {
          v60 = _objc_msgSend(v20, "cateCN");
          v62 = objc_retainAutoreleasedReturnValue(v60);
          if ( v62 )
          {
            v63 = _objc_msgSend(v61, "cateCN");
            v41 = (char *)objc_retainAutoreleasedReturnValue(v63);
          }
          else
          {
            v41 = obj;
          }
          v70 = (NSString *)_objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("【%@】"), v41);
          v71 = objc_retainAutoreleasedReturnValue(v70);
          _objc_msgSend(v78, "setStringValue:", v71);
          goto LABEL_43;
        }
        if ( (unsigned __int8)_objc_msgSend(v80, "isAd") )
        {
          v41 = (char *)CFSTR("【推广】");
        }
        else
        {
          v68 = _objc_msgSend(v80, "ziXunType");
          v69 = -[ZiXunTableViewController getZiXunTitle:](v8, "getZiXunTitle:", v68);
          v41 = (char *)objc_retainAutoreleasedReturnValue(v69);
        }
        goto LABEL_40;
      }
      if ( v21 )
      {
        v55 = _objc_msgSend(v80, "title");
        v41 = (char *)objc_retainAutoreleasedReturnValue(v55);
        if ( (unsigned __int8)_objc_msgSend(v80, "isAd") )
        {
          v56 = _objc_msgSend(v80, "title");
          v57 = objc_retainAutoreleasedReturnValue(v56);
          v58 = _objc_msgSend(
                  v57,
                  "stringByReplacingOccurrencesOfString:withString:",
                  CFSTR("【推广】"),
                  &charsToLeaveEscaped);
          v59 = (char *)objc_retainAutoreleasedReturnValue(v58);
          v41 = v59;
        }
LABEL_40:
        _objc_msgSend(v78, "setStringValue:", v41);
        goto LABEL_43;
      }
      v64 = "title";
      v65 = _objc_msgSend(v20, "title");
    }
    v41 = (char *)objc_retainAutoreleasedReturnValue(v65);
    if ( !v41 )
    {
      _objc_msgSend(v78, "setStringValue:", obj);
      goto LABEL_43;
    }
    v67 = _objc_msgSend(v66, v64);
    v54 = objc_retainAutoreleasedReturnValue(v67);
    _objc_msgSend(v78, "setStringValue:", v54);
LABEL_36:
LABEL_43:
    goto LABEL_44;
  }
  v17 = -[ZiXunTableViewController majorEventTable](self, "majorEventTable");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  if ( v18 != v19 )
  {
    v20 = 0LL;
    v21 = 1;
    v80 = 0LL;
    goto LABEL_13;
  }
  v42 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
  v43 = objc_retainAutoreleasedReturnValue(v42);
  if ( !_objc_msgSend(v43, "count") )
    goto LABEL_22;
  v44 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = (char *)_objc_msgSend(v45, "count") - 1;
  if ( (unsigned __int64)v46 >= a6 )
  {
    v8 = self;
    v48 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
    v38 = objc_retainAutoreleasedReturnValue(v48);
    v50 = _objc_msgSend(v38, "objectAtIndex:", v49);
    objc_retainAutoreleasedReturnValue(v50);
    v21 = 0;
    v80 = 0LL;
    goto LABEL_12;
  }
LABEL_45:
  ((void (__fastcall *)(id))v29)(v78);
  ((void (__fastcall *)(NSTableView *))v29)(v76);
}

//----- (00000001001B1A17) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController secondDocumentView](ZiXunTableViewController *self, SEL a2)
{
  NSTableView *majorEventTable; // rdi
  NSTableView *v5; // rax
  NSTableView *v6; // rdi

  majorEventTable = self->_majorEventTable;
  if ( !majorEventTable )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___HXBaseView);
    v5 = (NSTableView *)_objc_msgSend(v4, "init");
    v6 = self->_majorEventTable;
    self->_majorEventTable = v5;
    majorEventTable = self->_majorEventTable;
  }
  return (HXBaseView *)objc_retainAutoreleaseReturnValue(majorEventTable);
}

//----- (00000001001B1A68) ----------------------------------------------------
ZiXunRequestModule *__cdecl -[ZiXunTableViewController firstReqModule](ZiXunTableViewController *self, SEL a2)
{
  NSTextField *secondTableTitleTF; // rdi
  NSTextField *v5; // rax
  NSTextField *v6; // rdi

  secondTableTitleTF = self->_secondTableTitleTF;
  if ( !secondTableTitleTF )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunRequestModule);
    v5 = (NSTextField *)_objc_msgSend(v4, "init");
    v6 = self->_secondTableTitleTF;
    self->_secondTableTitleTF = v5;
    _objc_msgSend(self->_secondTableTitleTF, "setDelegate:", self);
    secondTableTitleTF = self->_secondTableTitleTF;
  }
  return (ZiXunRequestModule *)objc_retainAutoreleaseReturnValue(secondTableTitleTF);
}

//----- (00000001001B1AD7) ----------------------------------------------------
ZiXunRequestModule *__cdecl -[ZiXunTableViewController secondReqModule](ZiXunTableViewController *self, SEL a2)
{
  HXButton *secondTableMoreBtn; // rdi
  HXButton *v5; // rax
  HXButton *v6; // rdi

  secondTableMoreBtn = self->_secondTableMoreBtn;
  if ( !secondTableMoreBtn )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___ZiXunRequestModule);
    v5 = (HXButton *)_objc_msgSend(v4, "init");
    v6 = self->_secondTableMoreBtn;
    self->_secondTableMoreBtn = v5;
    _objc_msgSend(self->_secondTableMoreBtn, "setDelegate:", self);
    secondTableMoreBtn = self->_secondTableMoreBtn;
  }
  return (ZiXunRequestModule *)objc_retainAutoreleaseReturnValue(secondTableMoreBtn);
}

//----- (00000001001B1B46) ----------------------------------------------------
NSArray *__cdecl -[ZiXunTableViewController firstTableZiXunDatas](ZiXunTableViewController *self, SEL a2)
{
  NSTextField *secondNoNewsTextField; // rdi
  NSTextField *v5; // rax
  NSTextField *v6; // rdi

  secondNoNewsTextField = self->_secondNoNewsTextField;
  if ( !secondNoNewsTextField )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = (NSTextField *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->_secondNoNewsTextField;
    self->_secondNoNewsTextField = v5;
    secondNoNewsTextField = self->_secondNoNewsTextField;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(secondNoNewsTextField);
}

//----- (00000001001B1B97) ----------------------------------------------------
NSArray *__cdecl -[ZiXunTableViewController secondTableZiXunDatas](ZiXunTableViewController *self, SEL a2)
{
  HXBaseView *secondTableTitleView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi

  secondTableTitleView = self->_secondTableTitleView;
  if ( !secondTableTitleView )
  {
    v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "array");
    v5 = (HXBaseView *)objc_retainAutoreleasedReturnValue(v4);
    v6 = self->_secondTableTitleView;
    self->_secondTableTitleView = v5;
    secondTableTitleView = self->_secondTableTitleView;
  }
  return (NSArray *)objc_retainAutoreleaseReturnValue(secondTableTitleView);
}

//----- (00000001001B1BE8) ----------------------------------------------------
MajorEventRequestModule *__cdecl -[ZiXunTableViewController majorEvnetReqModule](
        ZiXunTableViewController *self,
        SEL a2)
{
  HXBaseView *majorEventTitleView; // rdi
  HXBaseView *v5; // rax
  HXBaseView *v6; // rdi

  majorEventTitleView = self->_majorEventTitleView;
  if ( !majorEventTitleView )
  {
    v4 = objc_alloc((Class)&OBJC_CLASS___MajorEventRequestModule);
    v5 = (HXBaseView *)_objc_msgSend(v4, "init");
    v6 = self->_majorEventTitleView;
    self->_majorEventTitleView = v5;
    majorEventTitleView = self->_majorEventTitleView;
  }
  return (MajorEventRequestModule *)objc_retainAutoreleaseReturnValue(majorEventTitleView);
}

//----- (00000001001B1C39) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setUpTheme](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rbx

  v2 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  WeakRetained = objc_loadWeakRetained((id *)(v4 + 16));
  _objc_msgSend(WeakRetained, "setBackgroundColor:", v3);
  v6 = +[HXThemeManager normalBgColor](&OBJC_CLASS___HXThemeManager, "normalBgColor");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v9 = objc_loadWeakRetained((id *)(v8 + 24));
  _objc_msgSend(v9, "setBackgroundColor:", v7);
  v10 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  _objc_msgSend(*(id *)(v12 + 64), "setTextColor:", v11);
  v13 = +[HXThemeManager helpTextColor](&OBJC_CLASS___HXThemeManager, "helpTextColor");
  v14 = objc_retainAutoreleasedReturnValue(v13);
  _objc_msgSend(*(id *)(v15 + 72), "setTextColor:", v14);
}

//----- (00000001001B1D79) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController clearCacheData](ZiXunTableViewController *self, SEL a2)
{
  -[ZiXunTableViewController clearCacheDataForFirstTable](self, "clearCacheDataForFirstTable");
  -[ZiXunTableViewController clearCacheDataForSecondTable](self, "clearCacheDataForSecondTable");
  -[ZiXunTableViewController clearCacheDataForMajorEventTable](self, "clearCacheDataForMajorEventTable");
}

//----- (00000001001B1DB4) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController clearCacheDataForFirstTable](ZiXunTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx

  v2 = -[ZiXunTableViewController firstTableZiXunDatas](self, "firstTableZiXunDatas");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
    -[ZiXunTableViewController setFirstTableZiXunDatas:](self, "setFirstTableZiXunDatas:", __NSArray0__);
}

//----- (00000001001B1E2A) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController clearCacheDataForSecondTable](ZiXunTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx

  v2 = -[ZiXunTableViewController secondTableZiXunDatas](self, "secondTableZiXunDatas");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "count");
  if ( v4 )
    -[ZiXunTableViewController setSecondTableZiXunDatas:](self, "setSecondTableZiXunDatas:", __NSArray0__);
}

//----- (00000001001B1EA0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController clearCacheDataForMajorEventTable](ZiXunTableViewController *self, SEL a2)
{
  -[ZiXunTableViewController setMajorEventArr:](self, "setMajorEventArr:", __NSArray0__);
}

//----- (00000001001B1EBC) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController reloadDataForTable](ZiXunTableViewController *self, SEL a2)
{
  NSTableView *v2; // rax
  NSTableView *v3; // rbx
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12

  v2 = -[ZiXunTableViewController firstTable](self, "firstTable");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4(v3, "reloadData");
  if ( !(unsigned __int8)v5(self, "isSecondViewHidden") )
  {
    v7 = v6(self, "secondTable");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v8, "reloadData");
  }
  if ( (unsigned __int8)-[ZiXunTableViewController isMajorEventShow](self, "isMajorEventShow") )
  {
    v11 = v10(self, "majorEventTable");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v13(v12, "reloadData");
  }
  -[ZiXunTableViewController relayoutForMajorEventViewIfNeeded](self, "relayoutForMajorEventViewIfNeeded");
}

//----- (00000001001B1F90) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController requestZiXunForFirstTable:](ZiXunTableViewController *self, SEL a2, id a3)
{
  id WeakRetained; // rbx
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->super.view);
    _objc_msgSend(WeakRetained, "setHidden:", 0LL);
    v7 = v6(self, "stockCode");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(self, "firstReqModule");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "setStockCode:", v8);
    v14 = v13(self, "market");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = v16(self, "firstReqModule");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "setMarket:", v15);
    v21 = v20(self, "firstReqModule");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v23(v22, "setNeedWaiting:", 0LL);
    if ( (unsigned __int64)v24(v4, "count") >= 2 )
    {
      v26 = v25(self, "firstReqModule");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      v28(v27, "setNeedWaiting:", 1LL);
    }
    v29 = v25(self, "firstReqModule");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31(v30, "requestForZiXun:", v4);
  }
}

//----- (00000001001B2160) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController requestZiXunForSecondTable:](ZiXunTableViewController *self, SEL a2, id a3)
{
  id WeakRetained; // rbx
  id (*v6)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v16)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v24)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    WeakRetained = objc_loadWeakRetained((id *)&self->super._topLevelObjects);
    _objc_msgSend(WeakRetained, "setHidden:", 0LL);
    v7 = v6(self, "stockCode");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v10 = v9(self, "secondReqModule");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v12(v11, "setStockCode:", v8);
    v14 = v13(self, "market");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v17 = v16(self, "secondReqModule");
    v18 = objc_retainAutoreleasedReturnValue(v17);
    v19(v18, "setMarket:", v15);
    v21 = v20(self, "secondReqModule");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v23(v22, "setNeedWaiting:", 0LL);
    if ( (unsigned __int64)v24(v4, "count") >= 2 )
    {
      v26 = v25(self, "secondReqModule");
      v27 = objc_retainAutoreleasedReturnValue(v26);
      v28(v27, "setNeedWaiting:", 1LL);
    }
    v29 = v25(self, "secondReqModule");
    v30 = objc_retainAutoreleasedReturnValue(v29);
    v31(v30, "requestForZiXun:", v4);
  }
}

//----- (00000001001B2330) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController registerNotificationObserver](ZiXunTableViewController *self, SEL a2)
{
  NSNotificationName v7; // r13

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(
    v3,
    "addObserver:selector:name:object:",
    v4,
    "actionWhenTableFocusDidChange:",
    CFSTR("TableFocusDidChanged"),
    0LL);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = NSViewFrameDidChangeNotification;
  v9 = _objc_msgSend(v8, "view");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  _objc_msgSend(v6, "addObserver:selector:name:object:", v11, "frameDidChanged:", v7, v10);
}

//----- (00000001001B240B) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController invalidateNotificationObserver](ZiXunTableViewController *self, SEL a2)
{

  v2 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeObserver:", self);
}

//----- (00000001001B245E) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController postNotificationForChangingTableFocus:](
        ZiXunTableViewController *self,
        SEL a2,
        id a3)
{
  NSString *v3; // rax
  NSString *v4; // r14

  v3 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), a3);
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(&OBJC_CLASS___NSNotificationCenter, "defaultCenter");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  _objc_msgSend(v6, "postNotificationName:object:", CFSTR("TableFocusDidChanged"), v4);
}

//----- (00000001001B24E9) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController actionWhenTableFocusDidChange:](ZiXunTableViewController *self, SEL a2, id a3)
{
  id (*v6)(id, SEL, ...); // r12
  id (*v8)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v25)(id, SEL, ...); // r12
  id (*v28)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  id (*v38)(id, SEL, ...); // r12
  id (*v45)(id, SEL, ...); // r12
  NSTableView *v47; // rax
  NSTableView *v48; // rbx
  id (*v49)(id, SEL, ...); // r12
  bool v50; // zf

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "object");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = v6(&OBJC_CLASS___NSString, "class");
  v9 = (unsigned __int8)v8(v5, "isKindOfClass:", v7);
  if ( v9 )
  {
    v11 = v10(self, "firstTable");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = v13(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), v12);
    v55 = objc_retainAutoreleasedReturnValue(v14);
    v16 = v15(self, "secondTable");
    v17 = objc_retainAutoreleasedReturnValue(v16);
    v19 = v18(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), v17);
    v53 = objc_retainAutoreleasedReturnValue(v19);
    v21 = v20(self, "majorEventTable");
    v22 = objc_retainAutoreleasedReturnValue(v21);
    v24 = v23(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@"), v22);
    v54 = objc_retainAutoreleasedReturnValue(v24);
    v26 = v25(v3, "object");
    v27 = objc_retainAutoreleasedReturnValue(v26);
    v29 = (unsigned __int8)v28(v27, "isEqualToString:", v55);
    if ( !v29 )
    {
      v31 = v30(self, "firstTable");
      v32 = objc_retainAutoreleasedReturnValue(v31);
      v33(v32, "deselectAll:", 0LL);
    }
    v34 = v30(v3, "object");
    v35 = objc_retainAutoreleasedReturnValue(v34);
    v37 = (unsigned __int8)v36(v35, "isEqualToString:", v53);
    if ( !v37 )
    {
      v39 = v38(self, "secondTable");
      v40 = objc_retainAutoreleasedReturnValue(v39);
      v41(v40, "deselectAll:", 0LL);
    }
    v42 = v38(v3, "object");
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v44 = v54;
    if ( (unsigned __int8)v45(v43, "isEqualToString:", v54) )
    {
      v46 = v55;
    }
    else
    {
      v47 = -[ZiXunTableViewController majorEventTable](self, "majorEventTable");
      v48 = objc_retainAutoreleasedReturnValue(v47);
      v50 = v48 == 0LL;
      v44 = v54;
      v46 = v55;
      if ( v50 )
      {
LABEL_11:
        goto LABEL_12;
      }
      v51 = v49(self, "majorEventTable");
      v43 = objc_retainAutoreleasedReturnValue(v51);
      v52(v43, "deselectAll:", 0LL);
    }
    goto LABEL_11;
  }
LABEL_12:
}

//----- (00000001001B27FB) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController openZiXunDetailWindow:](ZiXunTableViewController *self, SEL a2, id a3)
{
  NSTableView *v5; // r13
  NSTableView *v6; // rax
  NSTableView *v7; // rbx
  NSTableView *v9; // rax
  NSTableView *v10; // rbx
  NSTableView *v11; // rax
  NSTableView *v12; // rbx
  bool v13; // zf
  id (**v14)(id, SEL, ...); // rbx
  id (*v17)(id, SEL, ...); // r12
  id (*v18)(id, SEL, ...); // r12
  NSArray *v20; // r15
  id (*v21)(id, SEL, ...); // r12
  id (*v22)(id, SEL, ...); // r12
  unsigned __int64 v26; // rbx
  NSTableView *v30; // rax
  NSTableView *v31; // rbx
  id (*v32)(id, SEL, ...); // r12
  id (*v33)(id, SEL, ...); // r12
  id (*v35)(id, SEL, ...); // r12
  id (*v36)(id, SEL, ...); // r12
  unsigned __int64 v40; // rbx
  id (**v46)(id, SEL, ...); // r12
  id (**v47)(id, SEL, ...); // r15
  NSTableView *v48; // rax
  NSTableView *v49; // r15
  NSArray *v50; // rax
  NSArray *v56; // rax
  NSArray *v57; // rbx
  id (**v58)(id, SEL, ...); // r12
  id (**v59)(id, SEL, ...); // r15
  NSTableView *v75; // r15
  NSTableView *v85; // [rsp+8h] [rbp-38h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSTableView, "class");
  if ( (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v4) )
  {
    v86 = v3;
    v5 = (NSTableView *)objc_retain(v3);
    v6 = -[ZiXunTableViewController firstTable](self, "firstTable");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v85 = v5;
    if ( v7 == v5 )
    {
      v15 = _objc_msgSend(self, v8);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v82 = v17(v16, "selectedRow");
      v19 = v18(self, "firstTableZiXunDatas");
      v20 = (NSArray *)objc_retainAutoreleasedReturnValue(v19);
      if ( !v21(v20, "count") )
        goto LABEL_19;
      v23 = v22(self, "firstTableZiXunDatas");
      v24 = objc_retainAutoreleasedReturnValue(v23);
      v26 = v25(v24, "count") - 1;
      v28 = v82;
      if ( (unsigned __int64)v82 > v26 )
        goto LABEL_20;
      v29 = "firstTableZiXunDatas";
    }
    else
    {
      v9 = -[ZiXunTableViewController secondTable](self, "secondTable");
      v10 = objc_retainAutoreleasedReturnValue(v9);
      if ( v10 != v5 )
      {
        v11 = -[ZiXunTableViewController majorEventTable](self, "majorEventTable");
        v12 = objc_retainAutoreleasedReturnValue(v11);
        v13 = v12 == v5;
        v14 = &_objc_msgSend;
        if ( !v13 )
          goto LABEL_16;
        v48 = -[ZiXunTableViewController majorEventTable](self, "majorEventTable");
        v49 = objc_retainAutoreleasedReturnValue(v48);
        v84 = (char *)_objc_msgSend(v49, "selectedRow");
        v50 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
        v20 = objc_retainAutoreleasedReturnValue(v50);
        if ( _objc_msgSend(v20, "count") )
        {
          v52 = _objc_msgSend(self, v51);
          v53 = objc_retainAutoreleasedReturnValue(v52);
          v54 = (char *)_objc_msgSend(v53, "count") - 1;
          if ( v84 <= v54 )
          {
            v56 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
            v57 = objc_retainAutoreleasedReturnValue(v56);
            v59 = v58;
            v60 = (void *)((__int64 (__fastcall *)(NSArray *, const char *, char *))v58)(
                            v57,
                            "objectAtIndexedSubscript:",
                            v84);
            objc_retainAutoreleasedReturnValue(v60);
            ((void (__fastcall *)(ZiXunTableViewController *, const char *))v59)(self, "sendMajorEventMaiDian");
            v14 = v59;
            goto LABEL_16;
          }
LABEL_20:
          v72 = 0LL;
          v3 = v86;
          goto LABEL_21;
        }
LABEL_19:
        goto LABEL_20;
      }
      v30 = -[ZiXunTableViewController secondTable](self, "secondTable");
      v31 = objc_retainAutoreleasedReturnValue(v30);
      v83 = v32(v31, "selectedRow");
      v34 = v33(self, "secondTableZiXunDatas");
      v20 = (NSArray *)objc_retainAutoreleasedReturnValue(v34);
      if ( !v35(v20, "count") )
        goto LABEL_19;
      v37 = v36(self, "secondTableZiXunDatas");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v40 = v39(v38, "count") - 1;
      v28 = v83;
      if ( (unsigned __int64)v83 > v40 )
        goto LABEL_20;
      v29 = "secondTableZiXunDatas";
    }
    v42 = _objc_msgSend(self, v29);
    v43 = objc_retainAutoreleasedReturnValue(v42);
    v45 = (void *)v44(v43, "objectAtIndexedSubscript:", v28);
    v47 = v46;
    objc_retainAutoreleasedReturnValue(v45);
    v14 = v47;
LABEL_16:
    if ( (unsigned __int8)-[ZiXunTableViewController isNeedNewZiXunWindow](self, "isNeedNewZiXunWindow") )
    {
      v61 = ((id (*)(id, SEL, ...))v14)(&OBJC_CLASS___ZiXunWindowController, "sharedInstance");
      v62 = objc_retainAutoreleasedReturnValue(v61);
      v63 = ((id (*)(id, SEL, ...))v14)(self, "stockCode");
      v64 = objc_retainAutoreleasedReturnValue(v63);
      v66 = v65;
      v67 = v64;
      v68 = ((id (*)(id, SEL, ...))v14)(self, "market");
      v69 = objc_retainAutoreleasedReturnValue(v68);
      v70(v62, "showWindowWithModel:stockCode:market:", v66, v67, v69);
      v71 = v67;
      v5 = v85;
    }
    else
    {
      v73 = ((id (*)(id, SEL, ...))v14)(&OBJC_CLASS___NewsItem, "class");
      if ( (unsigned __int8)((id (*)(id, SEL, ...))v14)(v74, "isKindOfClass:", v73) )
      {
        v75 = v5;
        v76 = ((id (*)(id, SEL, ...))v14)(self, "getZiXunURL:", v72);
        v77 = objc_retainAutoreleasedReturnValue(v76);
        if ( ((id (*)(id, SEL, ...))v14)(v77, "length") )
        {
          v78 = -[ZiXunTableViewController ziXunOpenURLBlock](self, "ziXunOpenURLBlock");
          v79 = objc_retainAutoreleasedReturnValue(v78);
          if ( v79 )
          {
            v80 = -[ZiXunTableViewController ziXunOpenURLBlock](self, "ziXunOpenURLBlock");
            v81 = (void (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v80);
            v81[2](v81, v77);
          }
        }
        v3 = v86;
        v5 = v75;
        goto LABEL_21;
      }
    }
    v3 = v86;
LABEL_21:
  }
}

//----- (00000001001B2D51) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController frameDidChanged:](ZiXunTableViewController *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v5 = _objc_msgSend(v4, "class");
  v6 = (unsigned __int8)_objc_msgSend(v3, "isKindOfClass:", v5);
  if ( v6 )
    -[ZiXunTableViewController relayoutForMajorEventViewIfNeeded](self, "relayoutForMajorEventViewIfNeeded");
}

//----- (00000001001B2DDA) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setNoDataTFStateForFirstTable](ZiXunTableViewController *self, SEL a2)
{
  NSArray *v6; // rax
  NSArray *v7; // rbx
  NSTextField *v10; // rax
  NSTextField *v11; // rbx
  HXBaseView *v12; // rax
  NSTextField *v13; // rax
  NSTextField *v14; // rbx
  NSTextField *v17; // rax
  NSTextField *v18; // rax
  NSTextField *v19; // r14

  v2 = _objc_msgSend(self, "view");
  v3 = (const char *)objc_retainAutoreleasedReturnValue(v2);
  v4 = (char *)v3;
  if ( v3 )
  {
    objc_msgSend_stret(&v21, v3, "bounds");
    v5 = *((double *)&v22 + 1) * 0.5;
  }
  else
  {
    v22 = 0LL;
    v21 = 0LL;
    v5 = 0.0;
  }
  v23 = v5;
  v6 = -[ZiXunTableViewController firstTableZiXunDatas](self, "firstTableZiXunDatas");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(v7, "count");
  v9(v7);
  if ( v8 )
  {
    v10 = -[ZiXunTableViewController firstNoNewsTextField](self, "firstNoNewsTextField");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    _objc_msgSend(v11, "removeFromSuperview");
  }
  else
  {
    v12 = -[ZiXunTableViewController firstView](self, "firstView");
    objc_retainAutoreleasedReturnValue(v12);
    v13 = -[ZiXunTableViewController firstNoNewsTextField](self, "firstNoNewsTextField");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    _objc_msgSend(v15, "addSubview:", v14);
    v17 = -[ZiXunTableViewController firstNoNewsTextField](self, "firstNoNewsTextField");
    v11 = objc_retainAutoreleasedReturnValue(v17);
    v18 = -[ZiXunTableViewController firstNoNewsTextField](self, "firstNoNewsTextField");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v19, "height");
    _objc_msgSend(v11, "setFrameOrigin:", 0.0, v23 + v5 * -0.5);
    v20(v19);
  }
}

//----- (00000001001B2F85) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setNoDataTFStateForSecondTable](ZiXunTableViewController *self, SEL a2)
{
  long double v28; // [rsp+0h] [rbp-60h] BYREF

  v2 = _objc_msgSend(self, "view");
  v3 = (const char *)objc_retainAutoreleasedReturnValue(v2);
  v4 = (char *)v3;
  if ( v3 )
  {
    objc_msgSend_stret(&v28, v3, "bounds");
    v5 = *((double *)&v29 + 1) * 0.5;
  }
  else
  {
    v29 = 0LL;
    *(_OWORD *)&v28 = 0LL;
    v5 = 0.0;
  }
  v30 = v5;
  v6 = v30 + -20.0;
  floor(v28);
  v31 = v6;
  v8 = _objc_msgSend(v7, "secondTableZiXunDatas");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = _objc_msgSend(v9, "count");
  if ( v10 )
  {
    v12 = _objc_msgSend(v11, "secondNoNewsTextField");
    v13 = objc_retainAutoreleasedReturnValue(v12);
    _objc_msgSend(v13, "removeFromSuperview");
LABEL_12:
    return;
  }
  if ( !(unsigned __int8)_objc_msgSend(v11, "isSecondViewHidden")
    || (unsigned __int8)_objc_msgSend(v14, "isMajorEventShow") == 1 )
  {
    v15 = _objc_msgSend(v14, "secondView");
    v16 = objc_retainAutoreleasedReturnValue(v15);
    v18 = _objc_msgSend(v17, "secondNoNewsTextField");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    _objc_msgSend(v16, "addSubview:", v19);
    LOBYTE(v19) = (unsigned __int8)_objc_msgSend(v20, "isMajorEventShow");
    v22 = _objc_msgSend(v21, "secondNoNewsTextField");
    v13 = objc_retainAutoreleasedReturnValue(v22);
    if ( (_BYTE)v19 == 1 )
    {
      v31 = v31 * 0.5;
      v24 = v31;
      v25 = _objc_msgSend(v23, "secondNoNewsTextField");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      _objc_msgSend(v26, "height");
      _objc_msgSend(v13, "setFrameOrigin:", 0.0, v31 - v24 * 0.5);
    }
    else
    {
      v27 = _objc_msgSend(v23, "secondNoNewsTextField");
      v26 = objc_retainAutoreleasedReturnValue(v27);
      _objc_msgSend(v26, "height");
      _objc_msgSend(v13, "setFrameOrigin:", 0.0, v30 + v6 * -0.5);
    }
    goto LABEL_12;
  }
}

//----- (00000001001B31D0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setNoDataTFStateForMajroEventTable](ZiXunTableViewController *self, SEL a2)
{
  NSArray *v2; // rax
  NSArray *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  id (*v10)(id, SEL, ...); // r12
  id (*v13)(id, SEL, ...); // r12
  id (*v17)(id, SEL, ...); // r12
  id (*v20)(id, SEL, ...); // r12
  id (*v23)(id, SEL, ...); // r12
  id (*v26)(id, SEL, ...); // r12
  id (*v30)(id, SEL, ...); // r12

  v2 = -[ZiXunTableViewController majorEventArr](self, "majorEventArr");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = v4(v3, "count");
  if ( v5 )
  {
    v7 = v6(self, "majorEventNoDataTF");
    v8 = objc_retainAutoreleasedReturnValue(v7);
    v9(v8, "removeFromSuperview");
  }
  else if ( (unsigned __int8)-[ZiXunTableViewController isMajorEventShow](self, "isMajorEventShow") == 1 )
  {
    v11 = v10(self, "majorEventContentView");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = v13(self, "majorEventNoDataTF");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    v16(v12, "addSubview:", v15);
    v18 = v17(self, "majorEventNoDataTF");
    v19 = objc_retainAutoreleasedReturnValue(v18);
    v21 = v20(self, "majorEventContentView");
    v35 = objc_retainAutoreleasedReturnValue(v21);
    v22(v35, "height");
    v24 = v23(self, "majorEventNoDataTF");
    v36 = objc_retainAutoreleasedReturnValue(v24);
    v25(v36, "height");
    v27 = v26(self, "secondNoNewsTextField");
    v28 = objc_retainAutoreleasedReturnValue(v27);
    v29(v28, "width");
    v31 = v30(self, "secondNoNewsTextField");
    v32 = objc_retainAutoreleasedReturnValue(v31);
    v33(v32, "height");
    v34(v19, "setFrame:");
  }
}

//----- (00000001001B3408) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventViewState](ZiXunTableViewController *self, SEL a2)
{
  NSString *v2; // rax
  NSString *v3; // rbx
  id (*v4)(id, SEL, ...); // r12
  id (*v9)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12

  v2 = -[ZiXunTableViewController market](self, "market");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v5 = (unsigned __int8)v4(&OBJC_CLASS___HXTools, "isHuShenAMarket:", v3);
  if ( v5 )
  {
    v6(self, "showMajorEventRelativeView");
    v7(self, "setIsSecondViewHidden:", 1LL);
  }
  else if ( !(unsigned __int8)-[ZiXunTableViewController isSpecialViewStateSetted](self, "isSpecialViewStateSetted") )
  {
    v8(self, "removeMajorEventRelativeView");
    v10 = v9(self, "reqParamArr");
    v11 = objc_retainAutoreleasedReturnValue(v10);
    v13 = v12(v11, "count");
    if ( v13 == (id)1 )
    {
      -[ZiXunTableViewController hideSecondView](self, "hideSecondView");
    }
    else
    {
      v14(self, "setIsSecondViewHidden:", 1LL);
      v15(self, "showSecondView");
    }
  }
  -[ZiXunTableViewController setIsSpecialViewStateSetted:](self, "setIsSpecialViewStateSetted:", 0LL);
}

//----- (00000001001B3521) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController removeMajorEventRelativeView](ZiXunTableViewController *self, SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v3; // rbx
  HXBaseView *v4; // rax
  HXBaseView *v5; // rbx
  SEL v6; // r12
  HXBaseView *v7; // rax
  HXBaseView *v8; // rbx
  SEL v9; // r12
  NSTableView *v10; // rax
  NSScrollView *v11; // rax
  NSScrollView *v12; // rbx
  HXBaseView *v15; // rax
  HXBaseView *v16; // rbx
  NSTextField *v17; // rax
  NSTextField *v18; // rbx
  SEL v19; // r12

  v2 = -[ZiXunTableViewController secondTableTitleView](self, "secondTableTitleView");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  _objc_msgSend(v3, "removeFromSuperview");
  v4 = -[ZiXunTableViewController majorEventTitleView](self, "majorEventTitleView");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  _objc_msgSend(v5, v6);
  v7 = -[ZiXunTableViewController majorEventContentView](self, "majorEventContentView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  _objc_msgSend(v8, v9);
  v10 = -[ZiXunTableViewController secondTable](self, "secondTable");
  objc_retainAutoreleasedReturnValue(v10);
  v11 = -[ZiXunTableViewController secondScrollView](self, "secondScrollView");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  _objc_msgSend(v12, "setDocumentView:", v13);
  v15 = -[ZiXunTableViewController secondDocumentView](self, "secondDocumentView");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  _objc_msgSend(v16, "removeFromSuperview");
  v17 = -[ZiXunTableViewController majorEventNoDataTF](self, "majorEventNoDataTF");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  _objc_msgSend(v18, v19);
  -[ZiXunTableViewController setIsMajorEventShow:](self, "setIsMajorEventShow:", 0LL);
}

//----- (00000001001B3679) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController showMajorEventRelativeView](ZiXunTableViewController *self, SEL a2)
{
  HXBaseView *v2; // rax
  HXBaseView *v5; // rax
  HXBaseView *v6; // r14
  ZiXunTableViewController *v10; // r12
  HXBaseView *v11; // rax
  HXBaseView *v12; // r14
  HXBaseView *v16; // rax
  HXBaseView *v17; // r14
  HXBaseView *v58; // [rsp+0h] [rbp-30h]

  -[ZiXunTableViewController setIsMajorEventShow:](self, "setIsMajorEventShow:", 1LL);
  v2 = -[ZiXunTableViewController secondDocumentView](self, "secondDocumentView");
  v58 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v58, "subviews");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = -[ZiXunTableViewController secondTableTitleView](self, "secondTableTitleView");
  v6 = objc_retainAutoreleasedReturnValue(v5);
  v7 = (unsigned __int8)_objc_msgSend(v4, "containsObject:", v6);
  v8(v4);
  v9(v58);
  v10 = self;
  if ( !v7 )
  {
    v11 = -[ZiXunTableViewController secondDocumentView](self, "secondDocumentView");
    v12 = objc_retainAutoreleasedReturnValue(v11);
    v14 = _objc_msgSend(v13, "secondTableTitleView");
    v15 = objc_retainAutoreleasedReturnValue(v14);
    _objc_msgSend(v12, "addSubview:", v15);
  }
  v16 = -[ZiXunTableViewController secondDocumentView](v10, "secondDocumentView");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "subviews");
  v19 = objc_retainAutoreleasedReturnValue(v18);
  v21 = _objc_msgSend(v20, "majorEventTitleView");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = (unsigned __int8)_objc_msgSend(v19, "containsObject:", v22);
  if ( !v23 )
  {
    v25 = _objc_msgSend(v24, "secondDocumentView");
    v26 = objc_retainAutoreleasedReturnValue(v25);
    v28 = _objc_msgSend(v27, "majorEventTitleView");
    v29 = objc_retainAutoreleasedReturnValue(v28);
    _objc_msgSend(v26, "addSubview:", v29);
  }
  v30 = _objc_msgSend(v24, "secondDocumentView");
  v31 = objc_retainAutoreleasedReturnValue(v30);
  v32 = _objc_msgSend(v31, "subviews");
  v33 = objc_retainAutoreleasedReturnValue(v32);
  v35 = _objc_msgSend(v34, "majorEventContentView");
  v36 = objc_retainAutoreleasedReturnValue(v35);
  v37 = (unsigned __int8)_objc_msgSend(v33, "containsObject:", v36);
  if ( !v37 )
  {
    v39 = _objc_msgSend(v38, "secondDocumentView");
    v40 = objc_retainAutoreleasedReturnValue(v39);
    v42 = _objc_msgSend(v41, "majorEventContentView");
    v43 = objc_retainAutoreleasedReturnValue(v42);
    _objc_msgSend(v40, "addSubview:", v43);
  }
  v44 = _objc_msgSend(v38, "secondDocumentView");
  v45 = objc_retainAutoreleasedReturnValue(v44);
  v46 = _objc_msgSend(v45, "subviews");
  v47 = objc_retainAutoreleasedReturnValue(v46);
  v49 = _objc_msgSend(v48, "secondTable");
  v50 = objc_retainAutoreleasedReturnValue(v49);
  v51 = (unsigned __int8)_objc_msgSend(v47, "containsObject:", v50);
  if ( !v51 )
  {
    v53 = _objc_msgSend(v52, "secondDocumentView");
    v54 = objc_retainAutoreleasedReturnValue(v53);
    v56 = _objc_msgSend(v55, "secondTable");
    v57 = objc_retainAutoreleasedReturnValue(v56);
    _objc_msgSend(v54, "addSubview:", v57);
  }
  _objc_msgSend(v52, "relayoutForMajorEventViewIfNeeded");
}

//----- (00000001001B3A13) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController relayoutForMajorEventViewIfNeeded](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rbx
  NSTableView *v10; // rax
  NSTableView *v11; // rax
  HXBaseView *v12; // rax
  HXBaseView *v13; // r14
  HXBaseView *v15; // rax
  HXBaseView *v16; // r13
  HXBaseView *v18; // rax
  HXBaseView *v19; // rbx
  HXBaseView *v21; // rax
  HXBaseView *v22; // rbx
  HXBaseView *v25; // rax
  HXBaseView *v26; // rbx
  HXBaseView *v29; // rax
  HXBaseView *v30; // rbx
  NSTableView *v33; // rax
  NSTableView *v34; // rbx
  HXBaseView *v37; // rax
  HXBaseView *v38; // r14
  NSScrollView *v39; // rax
  NSScrollView *v40; // rbx
  SEL v41; // r12
  NSScrollView *v46; // rax
  NSScrollView *v47; // r15
  long double v52; // [rsp+0h] [rbp-160h]
  long double v53; // [rsp+0h] [rbp-160h]

  if ( (unsigned __int8)-[ZiXunTableViewController isMajorEventShow](self, "isMajorEventShow") )
  {
    v2 = _objc_msgSend(self, "view");
    v3 = (const char *)objc_retainAutoreleasedReturnValue(v2);
    v4 = (char *)v3;
    if ( v3 )
    {
      objc_msgSend_stret(&v54, v3, "bounds");
      v59 = *(double *)&v55;
      v5 = *((_QWORD *)&v55 + 1);
      *((_QWORD *)&v58 + 1) = *((_QWORD *)&v55 + 1);
    }
    else
    {
      v55 = 0LL;
      v54 = 0LL;
      *((_QWORD *)&v58 + 1) = 0LL;
      v5 = 0LL;
      v59 = 0.0;
    }
    WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
    _objc_msgSend(WeakRetained, "right");
    *(_QWORD *)&v58 = v5;
    v7 = 0.5 * *((double *)&v58 + 1) + -20.0;
    floor(v52);
    v8 = v7;
    if ( v7 > 0.0 )
    {
      v9 = v59 - *(double *)&v58;
      v59 = v8;
      floor(v53);
      *(double *)&v58 = v9;
      v10 = -[ZiXunTableViewController secondTable](self, "secondTable");
      v11 = objc_retainAutoreleasedReturnValue(v10);
      _objc_msgSend(v11, "height");
      v60 = v9;
      v12 = -[ZiXunTableViewController secondTableTitleView](self, "secondTableTitleView");
      v13 = objc_retainAutoreleasedReturnValue(v12);
      _objc_msgSend(v13, "height");
      v14 = v9 + v60 + v59;
      v60 = v14;
      v15 = -[ZiXunTableViewController majorEventTitleView](self, "majorEventTitleView");
      v16 = objc_retainAutoreleasedReturnValue(v15);
      _objc_msgSend(v16, "height");
      v60 = v14 + v60;
      *((_QWORD *)&v58 + 1) = fmax(*((double *)&v58 + 1), v60);
      v18 = -[ZiXunTableViewController secondDocumentView](self, "secondDocumentView");
      v19 = objc_retainAutoreleasedReturnValue(v18);
      v56 = 0LL;
      v57 = v58;
      _objc_msgSend(v19, "setFrame:");
      v20(v19);
      v21 = -[ZiXunTableViewController majorEventTitleView](self, "majorEventTitleView");
      v22 = objc_retainAutoreleasedReturnValue(v21);
      v23 = _objc_msgSend(v22, "mas_remakeConstraints:");
      objc_unsafeClaimAutoreleasedReturnValue(v23);
      v24(v22);
      v25 = -[ZiXunTableViewController majorEventContentView](self, "majorEventContentView");
      v26 = objc_retainAutoreleasedReturnValue(v25);
      v27 = _objc_msgSend(v26, "mas_remakeConstraints:");
      objc_unsafeClaimAutoreleasedReturnValue(v27);
      v28(v26);
      v29 = -[ZiXunTableViewController secondTableTitleView](self, "secondTableTitleView");
      v30 = objc_retainAutoreleasedReturnValue(v29);
      v31 = _objc_msgSend(v30, "mas_remakeConstraints:");
      objc_unsafeClaimAutoreleasedReturnValue(v31);
      v32(v30);
      v33 = -[ZiXunTableViewController secondTable](self, "secondTable");
      v34 = objc_retainAutoreleasedReturnValue(v33);
      v35 = _objc_msgSend(v34, "mas_remakeConstraints:");
      objc_unsafeClaimAutoreleasedReturnValue(v35);
      v36(v34);
      v37 = -[ZiXunTableViewController secondDocumentView](self, "secondDocumentView");
      v38 = objc_retainAutoreleasedReturnValue(v37);
      v39 = -[ZiXunTableViewController secondScrollView](self, "secondScrollView");
      v40 = objc_retainAutoreleasedReturnValue(v39);
      _objc_msgSend(v40, "setDocumentView:", v38);
      v42 = _objc_msgSend(self, v41);
      v43 = objc_retainAutoreleasedReturnValue(v42);
      v44 = _objc_msgSend(v43, "documentView");
      v45 = objc_retainAutoreleasedReturnValue(v44);
      v46 = (NSScrollView *)-[ZiXunTableViewController secondScrollView](self, "secondScrollView");
      v47 = objc_retainAutoreleasedReturnValue(v46);
      v48 = _objc_msgSend(v47, "documentView");
      v49 = objc_retainAutoreleasedReturnValue(v48);
      v50 = ((double (__fastcall *)(id, const char *))_objc_msgSend)(v49, "height");
      _objc_msgSend(v45, "scrollPoint:", 0.0, v50);
    }
  }
}

//----- (00000001001B3ED4) ----------------------------------------------------
__int64 __fastcall sub_1001B3ED4(__int64 a1, void *a2)
{
  NSNumber *v21; // rax
  NSNumber *v22; // rbx

  v28 = objc_retain(a2);
  v2 = _objc_msgSend(v28, "left");
  v29 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v29, "right");
  v30 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v30, "top");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "equalTo");
  v7 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v6);
  v9 = _objc_msgSend(*(id *)(v8 + 32), "secondDocumentView");
  v10 = objc_retainAutoreleasedReturnValue(v9);
  v11 = (void *)v7[2](v7, v10);
  objc_unsafeClaimAutoreleasedReturnValue(v11);
  v12(v7);
  v13(v5);
  v14(v30);
  v15(v29);
  v16 = _objc_msgSend(v28, "height");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18(v28);
  v19 = _objc_msgSend(v17, "equalTo");
  v20 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v19);
  v21 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 20LL);
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = (void *)v20[2](v20, v22);
  objc_unsafeClaimAutoreleasedReturnValue(v23);
  v24(v22);
  v25(v20);
  return v26(v17);
}

//----- (00000001001B4049) ----------------------------------------------------
void __fastcall sub_1001B4049(__int64 a1, void *a2)
{
  NSNumber *v25; // rax
  NSNumber *v26; // rbx

  v29 = objc_retain(a2);
  v2 = _objc_msgSend(v29, "left");
  v28 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v28, "right");
  v4 = objc_retainAutoreleasedReturnValue(v3);
  v5 = _objc_msgSend(v4, "equalTo");
  v6 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v5);
  v7 = _objc_msgSend(*(id *)(a1 + 32), "secondDocumentView");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v9 = (void *)v6[2](v6, v8);
  objc_unsafeClaimAutoreleasedReturnValue(v9);
  v11 = _objc_msgSend(v29, "top");
  v12 = objc_retainAutoreleasedReturnValue(v11);
  v13 = _objc_msgSend(v12, "equalTo");
  v14 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v13);
  v15 = _objc_msgSend(*(id *)(a1 + 32), "majorEventTitleView");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17 = _objc_msgSend(v16, "mas_bottom");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v19 = (void *)v14[2](v14, v18);
  objc_unsafeClaimAutoreleasedReturnValue(v19);
  v21 = _objc_msgSend(v29, "height");
  v22 = objc_retainAutoreleasedReturnValue(v21);
  v23 = _objc_msgSend(v22, "equalTo");
  v24 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v23);
  v25 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithDouble:", *(double *)(a1 + 40));
  v26 = objc_retainAutoreleasedReturnValue(v25);
  v27 = (void *)v24[2](v24, v26);
  objc_unsafeClaimAutoreleasedReturnValue(v27);
}

//----- (00000001001B4230) ----------------------------------------------------
__int64 __fastcall sub_1001B4230(__int64 a1, void *a2)
{
  NSNumber *v34; // rax
  NSNumber *v35; // rbx

  v41 = objc_retain(a2);
  v2 = _objc_msgSend(v41, "left");
  v3 = objc_retainAutoreleasedReturnValue(v2);
  v4 = _objc_msgSend(v3, "right");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v7 = (void *)v6(v5, "equalTo");
  v8 = objc_retainAutoreleasedReturnValue(v7);
  v10 = (void *)v9(*(_QWORD *)(a1 + 32), "secondDocumentView");
  v11 = objc_retainAutoreleasedReturnValue(v10);
  v12 = (void *)(*((__int64 (__fastcall **)(id, id))v8 + 2))(v8, v11);
  objc_unsafeClaimAutoreleasedReturnValue(v12);
  v13(v8);
  v14(v5);
  v15(v3);
  v16 = _objc_msgSend(v41, "top");
  v17 = objc_retainAutoreleasedReturnValue(v16);
  v18 = _objc_msgSend(v17, "equalTo");
  v19 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v18);
  v20 = _objc_msgSend(*(id *)(a1 + 32), "majorEventContentView");
  v21 = objc_retainAutoreleasedReturnValue(v20);
  v22 = _objc_msgSend(v21, "mas_bottom");
  v23 = objc_retainAutoreleasedReturnValue(v22);
  v24 = (void *)v19[2](v19, v23);
  objc_unsafeClaimAutoreleasedReturnValue(v24);
  v25(v23);
  v26(v21);
  v27(v19);
  v28(v17);
  v29 = _objc_msgSend(v41, "height");
  v30 = objc_retainAutoreleasedReturnValue(v29);
  v31(v41);
  v32 = _objc_msgSend(v30, "equalTo");
  v33 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v32);
  v34 = (NSNumber *)_objc_msgSend(&OBJC_CLASS___NSNumber, "numberWithInt:", 20LL);
  v35 = objc_retainAutoreleasedReturnValue(v34);
  v36 = (void *)v33[2](v33, v35);
  objc_unsafeClaimAutoreleasedReturnValue(v36);
  v37(v35);
  v38(v33);
  return v39(v30);
}

//----- (00000001001B441D) ----------------------------------------------------
void __fastcall sub_1001B441D(__int64 a1, void *a2)
{

  v22 = objc_retain(a2);
  v2 = _objc_msgSend(v22, "left");
  v23 = objc_retainAutoreleasedReturnValue(v2);
  v3 = _objc_msgSend(v23, "right");
  v24 = objc_retainAutoreleasedReturnValue(v3);
  v4 = _objc_msgSend(v24, "bottom");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  v6 = _objc_msgSend(v5, "equalTo");
  v7 = (__int64 (__fastcall **)(id, id))objc_retainAutoreleasedReturnValue(v6);
  v8 = _objc_msgSend(*(id *)(a1 + 32), "secondDocumentView");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10 = (void *)v7[2](v7, v9);
  objc_unsafeClaimAutoreleasedReturnValue(v10);
  v12 = _objc_msgSend(v22, "top");
  v13 = objc_retainAutoreleasedReturnValue(v12);
  v14 = _objc_msgSend(v13, "equalTo");
  objc_retainAutoreleasedReturnValue(v14);
  v15 = _objc_msgSend(*(id *)(a1 + 32), "secondTableTitleView");
  v16 = objc_retainAutoreleasedReturnValue(v15);
  v17 = _objc_msgSend(v16, "mas_bottom");
  v18 = objc_retainAutoreleasedReturnValue(v17);
  v20 = (void *)(*(__int64 (__fastcall **)(__int64, id))(v19 + 16))(v19, v18);
  objc_unsafeClaimAutoreleasedReturnValue(v20);
}

//----- (00000001001B45A1) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController sendMajorEventMaiDian](ZiXunTableViewController *self, SEL a2)
{
  +[UserLogSendingQueueManager sendUserLog:action:params:needWait:](
    &OBJC_CLASS___UserLogSendingQueueManager,
    "sendUserLog:action:params:needWait:",
    11LL,
    CFSTR("重大事件（个股资讯模块入口）"),
    0LL,
    1LL);
}

//----- (00000001001B45CF) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController filterInvalidZiXunData:](ZiXunTableViewController *self, SEL a2, id a3)
{
  NSMutableArray *v4; // rax
  id (*v5)(id, SEL, ...); // r12
  id (*v6)(id, SEL, ...); // r12
  unsigned __int64 i; // r14
  id (*v10)(id, SEL, ...); // r12
  id (*v12)(id, SEL, ...); // r12
  id (*v15)(id, SEL, ...); // r12
  SEL v24; // [rsp+48h] [rbp-F8h]
  SEL v25; // [rsp+50h] [rbp-F0h]
  SEL v27; // [rsp+60h] [rbp-E0h]
  SEL v28; // [rsp+68h] [rbp-D8h]
  SEL v29; // [rsp+70h] [rbp-D0h]
  id obj; // [rsp+88h] [rbp-B8h]

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(&OBJC_CLASS___NSMutableArray, "arrayWithArray:", v3);
  v31 = objc_retainAutoreleasedReturnValue(v4);
  v20 = 0LL;
  v21 = 0LL;
  v22 = 0LL;
  v23 = 0LL;
  obj = objc_retain(v3);
  v30 = v5(obj, "countByEnumeratingWithState:objects:count:", &v20, v33, 16LL);
  if ( v30 )
  {
    v26 = *(_QWORD *)v21;
    do
    {
      v24 = "class";
      v25 = "isKindOfClass:";
      v27 = "time";
      v28 = "isEqualToString:";
      v29 = "removeObject:";
      for ( i = 0LL; i < (unsigned __int64)v30; ++i )
      {
        if ( *(_QWORD *)v21 != v26 )
          objc_enumerationMutation(obj);
        v8 = *(void **)(*((_QWORD *)&v20 + 1) + 8 * i);
        v9 = v6(&OBJC_CLASS___NewsItem, v24);
        if ( (unsigned __int8)v10(v8, v25, v9) )
        {
          v11 = objc_retain(v8);
          v13 = v12(v11, v27);
          v14 = objc_retainAutoreleasedReturnValue(v13);
          v16 = (unsigned __int8)v15(v14, v28, CFSTR("0"));
          if ( v16 )
            v17(v31, v29, v11);
        }
      }
      v30 = v6(obj, "countByEnumeratingWithState:objects:count:", &v20, v33, 16LL);
    }
    while ( v30 );
  }
  v18 = obj;
  return objc_autoreleaseReturnValue(v31);
}

//----- (00000001001B4822) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController getZiXunTitle:](ZiXunTableViewController *self, SEL a2, unsigned __int64 a3)
{
  id result; // rax
  NSArray *v4; // rax
  NSArray *v5; // r14
  NSString *v6; // rax
  NSString *v7; // rbx
  _QWORD v10[2]; // [rsp+8h] [rbp-38h] BYREF

  result = obj;
  if ( (__int64)a3 > 14338 )
  {
    if ( (__int64)a3 <= 14364 )
    {
      switch ( a3 )
      {
        case 0x3803uLL:
          v10[0] = CFSTR("USHJ");
          v10[1] = CFSTR("USZJ");
          v4 = _objc_msgSend(&OBJC_CLASS___NSArray, "arrayWithObjects:count:", v10, 2LL);
          v5 = objc_retainAutoreleasedReturnValue(v4);
          v6 = -[ZiXunTableViewController market](self, "market");
          v7 = objc_retainAutoreleasedReturnValue(v6);
          v8 = (unsigned __int8)_objc_msgSend(v5, "containsObject:", v7);
          v9(v5);
          result = CFSTR("【个基】");
          if ( !v8 )
            result = CFSTR("【新闻】");
          break;
        case 0x3804uLL:
        case 0x380DuLL:
          return CFSTR("【新闻】");
        case 0x3805uLL:
          result = CFSTR("【公告】");
          break;
        case 0x3806uLL:
        case 0x3807uLL:
          result = CFSTR("【研报】");
          break;
        case 0x3813uLL:
          return CFSTR("【解盘】");
        case 0x3814uLL:
          return CFSTR("【要闻】");
        default:
          return result;
      }
      return result;
    }
    if ( (__int64)a3 > 32769 )
    {
      if ( a3 == 49155 )
        return CFSTR("【全球】");
      if ( a3 == 49154 )
        return CFSTR("【财经】");
      if ( a3 != 32770 )
        return result;
      return CFSTR("【新闻】");
    }
    if ( a3 != 14365 )
    {
      if ( a3 != 14369 )
        return result;
      return CFSTR("【新闻】");
    }
    return CFSTR("【要闻】");
  }
  if ( (__int64)a3 <= 6145 )
  {
    if ( a3 == 949 )
      return CFSTR("【行业】");
    if ( a3 == 2077 )
      return CFSTR("【分析】");
    if ( a3 != 2078 )
      return result;
    return CFSTR("【新闻】");
  }
  switch ( a3 )
  {
    case 0x1802uLL:
      return CFSTR("【数据】");
    case 0x1CC3uLL:
      return CFSTR("【解盘】");
    case 0x1CC4uLL:
      return CFSTR("【要闻】");
  }
  return result;
}

//----- (00000001001B4A7C) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController getZiXunBaseURLFromZiXunTree:](
        ZiXunTableViewController *self,
        SEL a2,
        unsigned __int64 a3)
{
  NSString *v6; // rax
  NSString *v7; // rbx

  v4 = +[ZiXunTreeRequestModule shareInstance](&OBJC_CLASS___ZiXunTreeRequestModule, "shareInstance");
  v5 = objc_retainAutoreleasedReturnValue(v4);
  if ( a3 == 14342 )
  {
    v10 = &selRef_getGeGuYanBaoURL;
  }
  else if ( a3 == 14341 )
  {
    v6 = -[ZiXunTableViewController market](self, "market");
    v7 = objc_retainAutoreleasedReturnValue(v6);
    v9 = _objc_msgSend(v8, "getStockTypeWithMarket:", v7);
    v10 = &selRef_getNormalGongGaoURL;
    if ( v9 == (id)8 )
      v10 = &selRef_getMeiGuGongGaoURL;
  }
  else
  {
    v10 = &selRef_getGeGuZiXunURL;
  }
  v11 = _objc_msgSend(v5, *v10);
  v12 = objc_retainAutoreleasedReturnValue(v11);
  return objc_autoreleaseReturnValue(v12);
}

//----- (00000001001B4B61) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController getZiXunURL:](ZiXunTableViewController *self, SEL a2, id a3)
{
  NSString *v10; // rax
  __CFString *v11; // rbx
  __CFString *v12; // rbx
  NSString *v27; // rax
  NSString *v30; // rax

  v3 = objc_retain(a3);
  v4 = _objc_msgSend(v3, "ziXunType");
  v6 = _objc_msgSend(v5, "getZiXunBaseURLFromZiXunTree:", v4);
  v33 = objc_retainAutoreleasedReturnValue(v6);
  if ( v3 )
  {
    v7 = _objc_msgSend(v3, "newsRequestType");
    if ( v7 == (id)1 )
    {
      v13 = _objc_msgSend(v3, "time");
      v14 = objc_retainAutoreleasedReturnValue(v13);
      v15 = +[HXTools getYYYYMMDDDate:](&OBJC_CLASS___HXTools, "getYYYYMMDDDate:", v14);
      v16 = objc_retainAutoreleasedReturnValue(v15);
      v34 = v16;
      v17 = _objc_msgSend(v33, "stringByReplacingOccurrencesOfString:withString:", CFSTR("%3"), v16);
      v18 = objc_retainAutoreleasedReturnValue(v17);
      v19 = _objc_msgSend(v3, "newsItemId");
      v20 = objc_retainAutoreleasedReturnValue(v19);
      v21 = _objc_msgSend(v18, "stringByReplacingOccurrencesOfString:withString:", CFSTR("{guid}"), v20);
      v35 = objc_retainAutoreleasedReturnValue(v21);
      if ( _objc_msgSend(v3, "ziXunType") != (id)14341
        || (v23 = _objc_msgSend(v22, "market"),
            v24 = objc_retainAutoreleasedReturnValue(v23),
            v25 = +[HXTools getStockTypeWithMarket:](&OBJC_CLASS___HXTools, "getStockTypeWithMarket:", v24),
            v25 == (id)8) )
      {
        v28 = _objc_msgSend(v22, "stockCode");
        v29 = objc_retainAutoreleasedReturnValue(v28);
        v30 = _objc_msgSend(
                &OBJC_CLASS___NSString,
                "stringWithFormat:",
                CFSTR("%@#code=%@&view_type=desktop_client"),
                v35,
                v29);
        v11 = objc_retainAutoreleasedReturnValue(v30);
        v26 = v29;
      }
      else
      {
        v26 = v35;
        v27 = _objc_msgSend(&OBJC_CLASS___NSString, "stringWithFormat:", CFSTR("%@#fromapp=IHexin"), v35);
        v11 = objc_retainAutoreleasedReturnValue(v27);
      }
      v9 = v34;
    }
    else
    {
      if ( v7 )
      {
        v11 = &charsToLeaveEscaped;
LABEL_13:
        v12 = objc_retain(v11);
        goto LABEL_14;
      }
      v8 = _objc_msgSend(v3, "newsURL");
      v9 = objc_retainAutoreleasedReturnValue(v8);
      v10 = _objc_msgSend(
              &OBJC_CLASS___NSString,
              "stringWithFormat:",
              CFSTR("%@#view_type=desktop_client&skin=black"),
              v9);
      v11 = objc_retainAutoreleasedReturnValue(v10);
    }
    goto LABEL_13;
  }
  v12 = &charsToLeaveEscaped;
LABEL_14:
  return objc_autoreleaseReturnValue(v12);
}

//----- (00000001001B4E1D) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController sortByTime:](ZiXunTableViewController *self, SEL a2, id a3)
{

  v3 = objc_retain(a3);
  v4 = v3;
  if ( v3 && _objc_msgSend(v3, "count") )
  {
    v5 = _objc_msgSend(v4, "sortedArrayUsingComparator:", &stru_1012DE038);
    v6 = objc_retainAutoreleasedReturnValue(v5);
    v4 = v6;
  }
  else
  {
    v6 = __NSArray0__;
  }
  v7 = objc_retain(v6);
  return objc_autoreleaseReturnValue(v7);
}

//----- (00000001001B4EAE) ----------------------------------------------------
signed __int64 __cdecl sub_1001B4EAE(id a1, NewsItem *a2, NewsItem *a3)
{
  NewsItem *v4; // r15
  NewsItem *v5; // r14
  NSString *v6; // rax
  NSString *v7; // r13
  NSString *v8; // rax
  NSString *v9; // rbx
  NSString *v11; // rax
  id (*v12)(id, SEL, ...); // r12
  signed __int64 v16; // r12

  v4 = objc_retain(a2);
  v5 = objc_retain(a3);
  v6 = -[NewsItem time](v4, "time");
  v7 = objc_retainAutoreleasedReturnValue(v6);
  if ( !v7 )
    goto LABEL_4;
  v8 = -[NewsItem time](v5, "time");
  v9 = objc_retainAutoreleasedReturnValue(v8);
  v10(v7);
  if ( v9 )
  {
    v11 = -[NewsItem time](v5, "time");
    v7 = objc_retainAutoreleasedReturnValue(v11);
    v13 = v12(v4, "time");
    v14 = objc_retainAutoreleasedReturnValue(v13);
    v15(v7, "compare:", v14);
LABEL_4:
  }
  return v16;
}

//----- (00000001001B4FB6) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController firstView](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibName);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B4FCF) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibName, a3);
}

//----- (00000001001B4FE3) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController secondView](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._nibBundle);
  return (HXBaseView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B4FFC) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._nibBundle, a3);
}

//----- (00000001001B5010) ----------------------------------------------------
NSScrollView *__cdecl -[ZiXunTableViewController firstScrollView](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super._representedObject);
  return (NSScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5029) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstScrollView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super._representedObject, a3);
}

//----- (00000001001B503D) ----------------------------------------------------
NSScrollView *__cdecl -[ZiXunTableViewController secondScrollView](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._title);
  return (NSScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5056) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondScrollView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._title, a3);
}

//----- (00000001001B506A) ----------------------------------------------------
NSTableView *__cdecl -[ZiXunTableViewController firstTable](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super.view);
  return (NSTableView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5083) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstTable:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super.view, a3);
}

//----- (00000001001B5097) ----------------------------------------------------
NSTableView *__cdecl -[ZiXunTableViewController secondTable](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._topLevelObjects);
  return (NSTableView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B50B0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondTable:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._topLevelObjects, a3);
}

//----- (00000001001B50C4) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunTableViewController firstNoNewsTextField](ZiXunTableViewController *self, SEL a2)
{
  return (NSTextField *)objc_getProperty(self, a2, 64LL, 1);
}

//----- (00000001001B50DA) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstNoNewsTextField:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 64LL);
}

//----- (00000001001B50EB) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunTableViewController secondNoNewsTextField](ZiXunTableViewController *self, SEL a2)
{
  return (NSTextField *)objc_getProperty(self, a2, 72LL, 1);
}

//----- (00000001001B5101) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondNoNewsTextField:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 72LL);
}

//----- (00000001001B5112) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController secondTableTitleView](ZiXunTableViewController *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 80LL, 1);
}

//----- (00000001001B5128) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondTableTitleView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 80LL);
}

//----- (00000001001B5139) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunTableViewController secondTableTitleTF](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained(&self->super.__privateData);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5152) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondTableTitleTF:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak(&self->super.__privateData, a3);
}

//----- (00000001001B5166) ----------------------------------------------------
HXButton *__cdecl -[ZiXunTableViewController secondTableMoreBtn](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._viewIsAppearing);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B517F) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondTableMoreBtn:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._viewIsAppearing, a3);
}

//----- (00000001001B5193) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController majorEventTitleView](ZiXunTableViewController *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 104LL, 1);
}

//----- (00000001001B51A9) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventTitleView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 104LL);
}

//----- (00000001001B51BA) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunTableViewController majorEventTitleTF](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->super._reserved);
  return (NSTextField *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B51D3) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventTitleTF:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->super._reserved, a3);
}

//----- (00000001001B51E7) ----------------------------------------------------
HXButton *__cdecl -[ZiXunTableViewController majorEventMoreBtn](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_isNeedNewZiXunWindow);
  return (HXButton *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5200) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventMoreBtn:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_isNeedNewZiXunWindow, a3);
}

//----- (00000001001B5214) ----------------------------------------------------
HXBaseView *__cdecl -[ZiXunTableViewController majorEventContentView](ZiXunTableViewController *self, SEL a2)
{
  return (HXBaseView *)objc_getProperty(self, a2, 128LL, 1);
}

//----- (00000001001B522A) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventContentView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 128LL);
}

//----- (00000001001B523B) ----------------------------------------------------
NSScrollView *__cdecl -[ZiXunTableViewController majorEventScrollView](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_secondView);
  return (NSScrollView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5254) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventScrollView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_secondView, a3);
}

//----- (00000001001B5268) ----------------------------------------------------
NSTableView *__cdecl -[ZiXunTableViewController majorEventTable](ZiXunTableViewController *self, SEL a2)
{
  id WeakRetained; // rax

  WeakRetained = objc_loadWeakRetained((id *)&self->_firstScrollView);
  return (NSTableView *)objc_autoreleaseReturnValue(WeakRetained);
}

//----- (00000001001B5281) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventTable:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeWeak((id *)&self->_firstScrollView, a3);
}

//----- (00000001001B5295) ----------------------------------------------------
NSTextField *__cdecl -[ZiXunTableViewController majorEventNoDataTF](ZiXunTableViewController *self, SEL a2)
{
  return (NSTextField *)objc_getProperty(self, a2, 152LL, 1);
}

//----- (00000001001B52AB) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventNoDataTF:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_atomic(self, a2, a3, 152LL);
}

//----- (00000001001B52BC) ----------------------------------------------------
char __cdecl -[ZiXunTableViewController isNeedNewZiXunWindow](ZiXunTableViewController *self, SEL a2)
{
  return (char)self->super.super._nextResponder;
}

//----- (00000001001B52CD) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setIsNeedNewZiXunWindow:](ZiXunTableViewController *self, SEL a2, char a3)
{
  LOBYTE(self->super.super._nextResponder) = a3;
}

//----- (00000001001B52DD) ----------------------------------------------------
NSString *__cdecl -[ZiXunTableViewController stockCode](ZiXunTableViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 160LL, 0);
}

//----- (00000001001B52F0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setStockCode:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 160LL);
}

//----- (00000001001B5301) ----------------------------------------------------
NSString *__cdecl -[ZiXunTableViewController market](ZiXunTableViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 168LL, 0);
}

//----- (00000001001B5314) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMarket:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 168LL);
}

//----- (00000001001B5325) ----------------------------------------------------
id __cdecl -[ZiXunTableViewController ziXunOpenURLBlock](ZiXunTableViewController *self, SEL a2)
{
  return objc_getProperty(self, a2, 176LL, 0);
}

//----- (00000001001B5338) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setZiXunOpenURLBlock:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 176LL);
}

//----- (00000001001B5349) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstTableZiXunDatas:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_secondNoNewsTextField, a3);
}

//----- (00000001001B535D) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondTableZiXunDatas:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_secondTableTitleView, a3);
}

//----- (00000001001B5371) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setFirstReqModule:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_secondTableTitleTF, a3);
}

//----- (00000001001B5385) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondReqModule:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_secondTableMoreBtn, a3);
}

//----- (00000001001B5399) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEvnetReqModule:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_majorEventTitleView, a3);
}

//----- (00000001001B53AD) ----------------------------------------------------
NSString *__cdecl -[ZiXunTableViewController ziXunUrl](ZiXunTableViewController *self, SEL a2)
{
  return (NSString *)objc_getProperty(self, a2, 224LL, 0);
}

//----- (00000001001B53C0) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setZiXunUrl:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_setProperty_nonatomic_copy(self, a2, a3, 224LL);
}

//----- (00000001001B53D1) ----------------------------------------------------
char __cdecl -[ZiXunTableViewController isSecondViewHidden](ZiXunTableViewController *self, SEL a2)
{
  return BYTE1(self->super.super._nextResponder);
}

//----- (00000001001B53E2) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setIsSecondViewHidden:](ZiXunTableViewController *self, SEL a2, char a3)
{
  BYTE1(self->super.super._nextResponder) = a3;
}

//----- (00000001001B53F2) ----------------------------------------------------
NSTimer *__cdecl -[ZiXunTableViewController requestTimer](ZiXunTableViewController *self, SEL a2)
{
  return (NSTimer *)self->_majorEventMoreBtn;
}

//----- (00000001001B5403) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setRequestTimer:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_majorEventMoreBtn, a3);
}

//----- (00000001001B5417) ----------------------------------------------------
NSArray *__cdecl -[ZiXunTableViewController reqParamArr](ZiXunTableViewController *self, SEL a2)
{
  return (NSArray *)self->_majorEventContentView;
}

//----- (00000001001B5428) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setReqParamArr:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_majorEventContentView, a3);
}

//----- (00000001001B543C) ----------------------------------------------------
NSArray *__cdecl -[ZiXunTableViewController majorEventArr](ZiXunTableViewController *self, SEL a2)
{
  return (NSArray *)self->_majorEventScrollView;
}

//----- (00000001001B544D) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setMajorEventArr:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_majorEventScrollView, a3);
}

//----- (00000001001B5461) ----------------------------------------------------
char __cdecl -[ZiXunTableViewController isMajorEventShow](ZiXunTableViewController *self, SEL a2)
{
  return BYTE2(self->super.super._nextResponder);
}

//----- (00000001001B5472) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setIsMajorEventShow:](ZiXunTableViewController *self, SEL a2, char a3)
{
  BYTE2(self->super.super._nextResponder) = a3;
}

//----- (00000001001B5482) ----------------------------------------------------
char __cdecl -[ZiXunTableViewController isSpecialViewStateSetted](ZiXunTableViewController *self, SEL a2)
{
  return BYTE3(self->super.super._nextResponder);
}

//----- (00000001001B5493) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setIsSpecialViewStateSetted:](ZiXunTableViewController *self, SEL a2, char a3)
{
  BYTE3(self->super.super._nextResponder) = a3;
}

//----- (00000001001B54A3) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController setSecondDocumentView:](ZiXunTableViewController *self, SEL a2, id a3)
{
  objc_storeStrong((id *)&self->_majorEventTable, a3);
}

//----- (00000001001B54B7) ----------------------------------------------------
void __cdecl -[ZiXunTableViewController .cxx_destruct](ZiXunTableViewController *self, SEL a2)
{
  objc_storeStrong((id *)&self->_majorEventTable, 0LL);
  objc_storeStrong((id *)&self->_majorEventScrollView, 0LL);
  objc_storeStrong((id *)&self->_majorEventContentView, 0LL);
  objc_storeStrong((id *)&self->_majorEventMoreBtn, 0LL);
  objc_storeStrong((id *)&self->_majorEventTitleTF, 0LL);
  objc_storeStrong((id *)&self->_majorEventTitleView, 0LL);
  objc_storeStrong((id *)&self->_secondTableMoreBtn, 0LL);
  objc_storeStrong((id *)&self->_secondTableTitleTF, 0LL);
  objc_storeStrong((id *)&self->_secondTableTitleView, 0LL);
  objc_storeStrong((id *)&self->_secondNoNewsTextField, 0LL);
  objc_storeStrong((id *)&self->_firstNoNewsTextField, 0LL);
  objc_storeStrong((id *)&self->_secondTable, 0LL);
  objc_storeStrong((id *)&self->_firstTable, 0LL);
  objc_storeStrong((id *)&self->_secondScrollView, 0LL);
  objc_destroyWeak((id *)&self->_firstScrollView);
  objc_destroyWeak((id *)&self->_secondView);
  objc_storeStrong((id *)&self->_firstView, 0LL);
  objc_destroyWeak((id *)&self->_isNeedNewZiXunWindow);
  objc_destroyWeak((id *)&self->super._reserved);
  objc_storeStrong((id *)&self->super._isContentViewController, 0LL);
  objc_destroyWeak((id *)&self->super._viewIsAppearing);
  objc_destroyWeak(&self->super.__privateData);
  objc_storeStrong((id *)&self->super._designNibBundleIdentifier, 0LL);
  objc_storeStrong(&self->super._autounbinder, 0LL);
  objc_storeStrong((id *)&self->super._editors, 0LL);
  objc_destroyWeak((id *)&self->super._topLevelObjects);
  objc_destroyWeak((id *)&self->super.view);
  objc_destroyWeak((id *)&self->super._title);
  objc_destroyWeak(&self->super._representedObject);
  objc_destroyWeak((id *)&self->super._nibBundle);
  objc_destroyWeak((id *)&self->super._nibName);
}

